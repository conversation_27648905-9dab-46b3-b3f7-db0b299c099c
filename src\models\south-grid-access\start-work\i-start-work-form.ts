import { ProcessEnum } from "@/enums/south-grid-access";

export interface IStartWorkForm {
  id?: string;

  /**
   * 监造计划Id
   */
  supervisionPlanId?: string;

  /**
   * 开工编码
   */
  productionStartNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;

  /**
   * 工序编码
   */
  processCode?: ProcessEnum;
  /**
   * 生产设备编码
   */
  deviceCode?: string;
  /**
   * 车间编码
   */
  workshopCode?: string;
  /**
   * 实际开始时间
   */
  actualStartTime?: string;
  /**
   * 实际结束时间
   */
  actualEndTime?: string;
}
