<template>
  <div class="raw-material-list h-full flex flex-col overflow-hidden">
    <div class="search flex-c mb-3 pr-4">
      <el-input
        class="mr-2 input-group"
        v-model="keywords"
        clearable
        placeholder="请输入原材料编号/名称"
        @keydown.enter="search"
      >
        <template #append>
          <el-button :icon="Search" @click="search" />
        </template>
      </el-input>
      <el-button
        v-auth="PermissionKey.meta.metaRawMaterialCreate"
        class="square-btn"
        @click="addRawMaterial"
        :icon="Plus"
      />
    </div>
    <el-scrollbar class="pr-4">
      <div class="raw-material-type-list flex-1">
        <div class="list" v-if="rawMaterialList?.length">
          <template v-for="(item, index) in rawMaterialList" :key="item.id">
            <CardList
              class="card mb-2 border p-3"
              :class="{ active: index === cardIndex }"
              :rawMaterialInfo="item"
              :processInfo="processInfo"
              @click="rawMaterialDetail(item, index)"
              @delRawMaterialEvent="delRawMaterial($event)"
            />
          </template>
        </div>
        <div class="list" v-else>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </div>
      </div>
    </el-scrollbar>
    <div class="list-footer pt-2">
      <el-pagination
        class="pagination"
        small
        layout="total, prev, pager, next"
        :total="paginationTotal"
        :pager-count="pagerCount"
        v-model:current-page="currentPage"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import CardList from "./card-list.vue";
import { Search, Plus } from "@element-plus/icons-vue";
import { ref, watchEffect, computed, watch } from "vue";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { IProductionStageProcessCode, IRawMaterialList } from "@/models/raw-material/i-raw-material-res-v2";
import { ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { PermissionKey } from "@/consts";

const props = withDefaults(
  defineProps<{
    processInfo: IProductionStageProcessCode;
    activeRawMaterial: IRawMaterialList | undefined;
  }>(),
  {}
);

const emits = defineEmits<{
  (event: "addRawMaterial"): void;
}>();

const keywords = ref("");
const cardIndex = ref(0);
const paginationTotal = ref(0);
const pagerCount = 5;
const rawMaterialStore = useRawMaterialV2Store();
const rawMaterialList = computed(() => rawMaterialStore.rawMaterialList);
const currentPage = ref(1);

watchEffect(() => {
  paginationTotal.value = rawMaterialStore.rawMaterialTotal;
});

watch(
  () => props.processInfo,
  () => {
    currentPage.value = 1;
  }
);

watch(
  rawMaterialList,
  newVal => {
    if (newVal.length) {
      // 监听列表是否有数据展示详情
      if (props.activeRawMaterial) {
        const { id } = props.activeRawMaterial;
        const oldIndex = rawMaterialStore.rawMaterialList?.findIndex(item => item.id === id);
        rawMaterialDetail(props.activeRawMaterial, oldIndex);
      } else {
        rawMaterialDetail(rawMaterialList.value[0], 0);
      }
    } else {
      rawMaterialStore.initRawMaterialBaseInfo();
      rawMaterialStore.initRawMaterialInspecList();
    }
  },
  {
    immediate: true
  }
);

/**
 * 搜索原材料
 */
const search = () => {
  rawMaterialStore.queryRawMaterialList({ keyWords: keywords.value });
};

/**
 * 新增原材料
 */
const addRawMaterial = () => {
  emits("addRawMaterial");
};

const delRawMaterial = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await rawMaterialStore.delRawMaterialInfo(id);
  ElMessage.success("删除成功");
  rawMaterialStore.initCurrentRawMaterialData();
  refreshRawMaterialList();
  scrollToTop();
};

/**
 * 刷新原材料列表
 */
const refreshRawMaterialList = () => {
  const { processCode } = props.processInfo;
  rawMaterialStore.queryRawMaterialList({ processCode });
};

/**
 * 页码数量改变
 */
const handleSizeChange = (pageSize: number) => {
  rawMaterialStore.queryRawMaterialList({ pageSize });
};

/**
 * 页码改变
 */
const handleCurrentChange = async (pageNo: number) => {
  await rawMaterialStore.queryRawMaterialList({ pageNo });
  // 当页码改变后，应选中第1个原材料
  rawMaterialDetail(rawMaterialList.value[0], 0);
};

/**
 * 原材料详情
 */
function rawMaterialDetail(item: IRawMaterialList, index: number) {
  cardIndex.value = index;
  // 根据id获取单个原材料的详情信息
  rawMaterialStore.getDetailBaseInfoOfRawMaterial(item.id);
  rawMaterialStore.setCurrentClickRawMaterial(item);
}

/**
 * 刷新单个原材料基础信息
 */
const refreshRawMaterialDetail = () => {
  const { id } = rawMaterialStore.currentClickRawMaterial;
  rawMaterialStore.getDetailBaseInfoOfRawMaterial(id);
};

/** 删除数据后 自动滚动到顶部 */
const scrollToTop = () => {
  const scrollEl = document.querySelector(".raw-material-type-list");
  scrollEl?.scrollIntoView({ block: "start" });
};

defineExpose({
  refreshRawMaterialDetail
});
</script>

<style scoped lang="scss">
.search {
  .input-group {
    :deep(.el-input-group__append) {
      width: 40px;
    }

    :deep(.el-button) {
      padding: 8px;
    }
  }

  .square-btn {
    padding: 5px 8px;
  }
}

.raw-material-type-list {
  .list {
    .card {
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      border-color: rgba(0, 0, 0, 0.1);

      &.active {
        color: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      &:hover {
        cursor: pointer;
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
}

.raw-material-list {
  .list-footer {
    :deep(.el-pagination .btn-prev) {
      margin-left: 0;
    }
  }
}
</style>
