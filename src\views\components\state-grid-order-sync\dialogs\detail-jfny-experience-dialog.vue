<template>
  <el-dialog v-model="visible" class="large" title="局放耐压试验" align-center destroy-on-close>
    <WithStandPressDetail :experimentId="experimentId" />
  </el-dialog>
</template>

<script setup lang="ts">
import WithStandPressDetail from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/predelivery-test/normal/jfny-detail/index.vue";
import { ref } from "vue";

const experimentId = ref<string>();
const visible = ref(false);

function openDetailDialog(dataId: string) {
  experimentId.value = dataId;
  visible.value = true;
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
