import { TableWidth } from "@/enums";
import { fullDateFormat } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "设备端采集点编号",
      prop: "no",
      width: TableWidth.largeOrder
    },
    {
      label: "设备端采集点名称",
      prop: "name",
      minWidth: TableWidth.name
    },
    {
      label: "设备端计量单位",
      prop: "unit",
      width: TableWidth.largeNumber
    },
    {
      label: "码点采集频率(S)",
      prop: "frequency",
      align: "center",
      minWidth: TableWidth.largeNumber
    },
    {
      label: "更新时间",
      prop: "updateTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "更新人",
      prop: "updaterName"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  return { columns };
}
