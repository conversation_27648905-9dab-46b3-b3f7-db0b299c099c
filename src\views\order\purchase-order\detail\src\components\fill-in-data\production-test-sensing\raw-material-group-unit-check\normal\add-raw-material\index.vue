<template>
  <div class="add-raw-material">
    <div class="form">
      <el-steps :active="state.step" class="mb-5">
        <el-step title="填写基础信息" />
        <el-step title="填写检测信息" />
        <el-step title="完成" />
      </el-steps>
      <div class="raw-content">
        <div :class="{ hidden: state.step !== StepEnum.materialBaseInfo }">
          <RawMaterialInfo ref="rawMaterialRef" :subclassCode="subclassCode" />
        </div>

        <div
          v-if="state.step !== StepEnum.materialBaseInfo"
          :class="['hidden', { block: state.step === StepEnum.materialTestInfo || state.step === StepEnum.complete }]"
        >
          <el-scrollbar :max-height="560">
            <MaterialForm ref="materialTestFormRef" />
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check/raw-material-group-unit-check";
import RawMaterialInfo from "./raw-material-info/index.vue";
import MaterialForm from "./material-form/index.vue";
import { ref, reactive } from "vue";
import { useFillInDataStore } from "@/store/modules";
import { IProductOrder } from "@/models";

/** PS：新增原材料检测 优化版本为 v2 版本，本版本新增现阶段只在编辑的时候使用 */

// 是否显示选择原材料弹框
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const fillInDataStore = useFillInDataStore();

enum StepEnum {
  materialBaseInfo = 0,
  materialTestInfo = 1,
  complete = 2
}

interface IProp {
  step: StepEnum;
}

// 传入的属性值
const props = withDefaults(
  defineProps<{
    notRefreshList?: boolean;
    subclassCode?: string;
  }>(),
  {}
);
// 定义抛出的事件
const emits = defineEmits<{
  (event: "stateChange", step): void;
}>();

// 步骤条
const state = reactive<IProp>({
  step: StepEnum.materialBaseInfo
});

const rawMaterialRef = ref();
const materialTestFormRef = ref<InstanceType<typeof MaterialForm>>();
const rawMaterialStore = useRawMaterialGroupUnitStore();

// 上一步
const onPreStep = () => {
  state.step = StepEnum.materialBaseInfo;
  emits("stateChange", state.step);
};

// 下一步
const onNextStep = async () => {
  await rawMaterialRef.value.submitForm();
  const valid = rawMaterialGroupUnitStore.selectBaseInfoValid;
  if (valid) {
    await rawMaterialGroupUnitStore.getRawMaterialCheckInfoByProductStePro(
      undefined,
      (fillInDataStore.data as IProductOrder).specificationModel || fillInDataStore.data.specificationType
    );
    state.step = StepEnum.materialTestInfo;
    emits("stateChange", StepEnum.materialTestInfo);
  }
};

// 重置表单数据
const resetFormFields = () => {
  rawMaterialRef.value.setShowSerialNumber(false);
  rawMaterialRef.value?.resetFormFiles();
};

// 保存数据
const saveFormData = async (saveAdd?: boolean) => {
  await materialTestFormRef.value.validRawMaterialTest();
  if (rawMaterialStore.materialTestFromValid) {
    await rawMaterialStore.addRawMaterialCheck();
    state.step = StepEnum.complete;
    // 保存并继续新增
    if (saveAdd) {
      state.step = StepEnum.materialBaseInfo;
      emits("stateChange", StepEnum.materialBaseInfo);
      resetFormFields();
    }
    if (props.notRefreshList) return;
    const { processCode, processUnionCode } = rawMaterialGroupUnitStore.currentTagInfo;
    if (rawMaterialGroupUnitStore.isAdd) {
      await rawMaterialGroupUnitStore.getRawMaterialOfSelectAction({ processUnionCode, pageNo: 1, pageSize: 10 });
    } else {
      await rawMaterialGroupUnitStore.getRawMaterialGroupUnitTable({ processCode });
    }
  } else {
    return Promise.reject();
  }
};

defineExpose({
  resetFormFields: resetFormFields,
  onPreStep,
  onNextStep,
  saveFormData
});
</script>

<style scoped lang="scss">
.add-raw-material {
  .hidden {
    display: none;
  }

  .block {
    display: block;
  }

  .raw-content {
    height: 560px;
  }
}
</style>
