<template>
  <pure-table
    show-overflow-tooltip
    size="large"
    class="flex-1 overflow-hidden"
    :class="{ pagination: addClassPagination }"
    border
    row-key="id"
    :data="data"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :span-method="spanMethod"
    v-bind="$attrs"
    :max-height="600"
    @page-current-change="pageChange"
    @page-size-change="pageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
  <DetailJfnyExperienceDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { PureTable } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { onMounted, ref, watchEffect, computed, h } from "vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IExperimentJfnySync, IPagingReq } from "@/models";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import {
  ExperimentResultEnumMapAialsDesc,
  KeywordAliasEnum,
  StateGridOrderSyncResult,
  TableWidth,
  VoltageClassesEnumMapDesc,
  VoltageLevelTypeEnumMapDescAlias
} from "@/enums";
import { useSpan } from "../hooks/useSpan";
import DetailJfnyExperienceDialog from "@/views/components/state-grid-order-sync/dialogs/detail-jfny-experience-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import { useTableConfig } from "@/utils/useTableConfig";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const store = useStateGridSyncAuditStore();
const { enumFormatter, dateFormatter, mapFormatter } = useTableCellFormatter();
const { setSpan } = useSpan();

const loading = ref(false);
const allData = ref<Array<IExperimentJfnySync>>();
const data = ref<Array<IExperimentJfnySync>>();
const handleRefresh = useLoadingFn(store.getJfnySyncList, loading);

const { detailDialog, openDetailDialog } = useEditDetailDialog<
  undefined,
  InstanceType<typeof DetailJfnyExperienceDialog>,
  undefined
>();

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: store.isCable ? "生产订单号" : "生产工单号",
    prop: "orderNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: store.isCable ? KeywordAliasEnum.IPO_NO : "product_work_order_no",
        defaultText: store.isCable ? "生产订单号" : "生产工单号"
      }),
    width: TableWidth.largeXgOperation
  },
  {
    label: "试验编号",
    prop: "experimentNo",
    minWidth: TableWidth.largeXgOperation,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "成品编号",
    prop: "finProNo",
    minWidth: TableWidth.largeXgOperation
  },
  {
    label: "电压等级",
    prop: "finishedVoltageLevel",
    minWidth: TableWidth.number,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "电压类型",
    prop: "finishedVoltageLevelType",
    minWidth: TableWidth.type,
    formatter: mapFormatter(VoltageLevelTypeEnumMapDescAlias)
  },
  {
    label: "试验结果",
    prop: "experimentResult",
    minWidth: TableWidth.type,
    formatter: mapFormatter(ExperimentResultEnumMapAialsDesc)
  },
  {
    label: "开始时间",
    prop: "startTime",
    minWidth: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "结束时间",
    prop: "endTime",
    minWidth: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  }
];

// 分页信息
const { pagination } = useTableConfig();
// 分页信息
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};
pagination.hideOnSinglePage = true;
const addClassPagination = computed(() => {
  return pagination?.total > 20;
});

watchEffect(() => {
  const activeNo = store.activeNo;
  data.value = activeNo ? allData.value?.filter(datum => datum.orderNo === activeNo) : allData.value;
  data.value = data.value || [];
  setSpan(data.value, "processName", "_processNameSpan");
  setSpan(data.value, "processName_orderNo", "_orderNoSpan");
});

onMounted(async () => {
  await refresh(pageInfo);
});

async function refresh(pageInfo?: IPagingReq) {
  const res = await handleRefresh(pageInfo);
  allData.value = res.list || [];
  pagination.total = res.total || 0;
}

function spanMethod({ row, column }) {
  if (column.property === "processName") {
    return row._processNameSpan;
  }
  if (column.property === "orderNo") {
    return row._orderNoSpan;
  }
}

/**
 * 切换页码
 */
function pageChange(pageNo: number) {
  refresh({ pageSize: pagination.pageSize, pageNo });
}
/**
 * 切换页码数量
 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  refresh({ pageNo: 1, pageSize });
}
</script>

<style scoped></style>
