import { IPagingReq } from "../i-paging-req";
import { IUpLoadFile } from "../production-test-sensing/i-common";
import { ProductOrWorkOrderEnum } from "@/enums";

export enum EDiagType {
  Add = "add",
  Edit = "edit",
  Detail = "detail"
}

/**
 * 原材料检测
 */
export enum ERawMaterialCheckType {
  All = -1, // 全部
  Has = 1, // 有
  Not = 0 // 无
}

/**
 * 获取工序接口
 */
export interface IProductionStageProcessCode {
  processCode: string;
  processName: string;
  /** 物资种类 */
  subclassCode?: string;
}

export interface IResponseRawMaterial {
  list?: IRawMaterialList[];
  total: number;
}
export interface IRawMaterialList {
  /** 唯一标识 */
  id?: number;
  /** 原材料编码 */
  code: string;
  /** 原材料名称 */
  name: string;
  /** 原材料类型编码 */
  processCode: string;
  /** 原材料类型名称 */
  processName: string;
  /** 创建时间 */
  createTime?: string;
  /** 计量单位 */
  partUnit?: string;
  /** 文件数据 */
  inspectionReport?: IUpLoadFile[];
  spotCheckReport?: IUpLoadFile[];
}

/**
 * 原材料检验数据接口
 */
export interface IRawMaterialReq {
  /**
   * 唯一标识
   */
  id?: string;
  /**
   * 原材料编码
   */
  code: string;
  /**
   * 原材料名称
   */
  name: string;
  /**
   * 原材料类型编码
   */
  processCode: string;
  /**
   * 原材料类型名称
   */
  processName: string;
  /**
   * 物料品类
   */
  categoryCode?: string;
  /**
   * 工序唯一标识
   */
  processId?: number;
  /**
   * 电压等级
   */
  voltageGrade: string;
  /**
   * 规格型号
   */
  modelCode: string;
  /**
   * 原材料规格型号
   */
  mrmSpecification: string;
  /**
   * 品牌
   */
  borMaterials: string;
  /**
   * 原材料制造商
   */
  rawmManufacturer: string;
  /**
   * 产地
   */
  oorMaterials: string;
  /**
   * 原材料批次号
   */
  materialBatchNo: string;
  /**
   * 检验批次号
   */
  inspectBatchNo?: string;
  /**
   * 检验日期
   */
  inspectDate?: string;
  /**
   * 生产日期
   */
  productionDate: string;
  /**
   * 生产订单唯一标识
   */
  productionId?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 计量单位
   */
  partUnit?: string;
  /** 用料量 */
  quantity?: number;
  /**
   * 工单唯一标识
   */
  workOrderId?: number;
  /** 租户信息 */
  tenantId?: number;
  /** 检验项的值 */
  rawMetadataValue?: [];
  purchaseOrderLineId?: number;
  purchaseOrderId?: number;
  /** 材料标准 */
  materialStandard?: string;
  /** 检验标准 */
  inspectStandard?: string;
  /** 炉/批号 */
  furnaceNo?: string;
  /** 检测人员 */
  inspectOperate?: string;
  /** 检测单位 */
  inspectEnterprise?: string;
  /** 审核人员 */
  auditor?: string;
  remark?: string;
}

/**
 * 查询原材料数据
 */
export interface ISearchRawList extends IPagingReq {
  keyWords?: string;
  creatTime?: string;
  isExist?: number;
  processCode?: string;
  processUnionCode?: string;
  hasProcess?: boolean;
  orderId?: string;
  orderType?: ProductOrWorkOrderEnum;
}

/** 控件值信息 */
export interface IIdentityDetailList {
  id?: number;
  metadataModelDetailId: number;
  identityId: number;
  identityValue: string;
  maxLength?: number;
  minLength?: number;
  step?: number;
}
/** 控件信息 */
export interface IDataTypeIdentityDetail {
  id?: number;
  identityCode: string;
  identityName: string;
  identityDetailList?: IIdentityDetailList;
}

/**
 * 原材料检测采集项
 */
export interface IRawMaterialCheckCollectionItem {
  collectionType?: number;
  collectionTypeName?: string;
  groupCode?: string;
  groupName?: string;
  id?: string;
  processId?: string;
  identityId?: string;
  isShow?: boolean;
  itemCode?: string;
  remarks?: string;
  required?: boolean;
  requiredPre?: string;
  sort?: number;
  status?: boolean;
  targetCode?: string;
  targetCodeAlias?: string;
  targetName?: string;
  targetValue?: string;
  targetNameAlias?: string;
  targetValueType?: string;
  unit?: string;
  unitLength?: string;
  initValue?: string;
  initType?: string;

  /** 日期时间格式 */
  dateTypeCode?: string;
  dateTypeName?: string;
  /** 数据格式要求 */
  datumOrganization?: string;
  /** 校验信息 */

  // 文本
  /** 文本最大长度 */
  maxStrLength?: number;
  /** 是否使用正则 */
  useRegular?: boolean;
  /** 正则表达式 */
  regular?: string;

  // 数字输入框
  /** 是否限制最大值 */
  validMaxValue?: boolean;
  /** 最大值 */
  maxValue?: number;
  /** 是否包含最大值 */
  includeMaxValue?: boolean;
  /** 是否限制最小值 */
  validMinValue?: boolean;
  /** 最小值 */
  minValue?: number;
  /** 是否包含最小值 */
  includeMinValue?: boolean;
  /** 小数精度 */
  decimalDigits?: number;

  // 日期格式化
  format?: string;

  // 文件大小
  /** 文件类型，多选 */
  fileType?: string[];
  /** 文件大小 */
  fileSize?: number;

  dataTypeIdentityDetail?: IDataTypeIdentityDetail;

  identityCode?: string;
}

export interface SubClassItemByRawMaterial {
  subClassCode: string;
  subClassName: string;
  processId: string;
  processCode: string;
}

export interface IRawMaterialExperimentValue {
  dataCode: string;
  identityCode: string;
  //{[key:string]:any} => 针对文件 后面需要调整
  dataValue: string | number | Array<string> | Array<number> | Array<{ key: string }> | { [key: string]: any };
}

/** 从生成订单中复制 */
export interface ICopyFromOrder {
  orderId: string;
  iOtDataIds: string[];
  orderType?: number;
}
