import BaseInfo from "./base-info.vue";
import { computed, Ref } from "vue";
import { QualitySpecificationMatchTypeEnum } from "@/enums/quality-specification";

/**
 * @description: 质量规范详情 tab 配置
 */
export function genTabConfig(baseInfoRef: Ref<InstanceType<typeof BaseInfo>>) {
  return computed(() => {
    if (!baseInfoRef.value) {
      return [
        {
          moduleCode: "particular-detail",
          moduleName: "质量规范明细"
        }
      ];
    }
    if (baseInfoRef.value.specificationDetail.matchType === QualitySpecificationMatchTypeEnum.Material) {
      return [
        {
          moduleCode: "particular-detail",
          moduleName: "质量规范明细"
        },
        {
          moduleCode: "application-materials",
          moduleName: "适用物料"
        }
      ];
    }
    return [
      {
        moduleCode: "particular-detail",
        moduleName: "质量规范明细"
      },
      {
        moduleCode: "application-seles-order-line",
        moduleName: "适用销售订单行"
      }
    ];
  });
}
