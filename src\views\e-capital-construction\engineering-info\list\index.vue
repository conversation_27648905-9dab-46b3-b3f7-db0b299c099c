<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 搜索条 -->
    <div class="bg-bg_color pt-[8px] pr-6 flex justify-between">
      <ElForm :inline="true" :model="state.searchForm" size="large" class="flex-1">
        <ElFormItem label="物资种类：">
          <el-select v-model="state.searchForm.ejjCategoryType" placeholder="请选择物资种类" clearable filterable>
            <el-option v-for="(val, key) in state.equipmentList" :key="key" :label="val.name" :value="key" />
          </el-select>
        </ElFormItem>
        <ElFormItem label="工程ID：">
          <ElInput class="!w-[200px]" clearable v-model="state.searchForm.projectId" placeholder="请输入工程ID" />
        </ElFormItem>
        <ElFormItem label="生产工号：">
          <ElInput class="!w-[200px]" clearable v-model="state.searchForm.productionNo" placeholder="请输入生产工号" />
        </ElFormItem>
        <ElFormItem>
          <ElButton size="large" type="primary" @click="requestList()">搜索</ElButton>
          <ElButton size="large" @click="onResetQuery()">重置</ElButton>
        </ElFormItem>
      </ElForm>
      <el-button type="primary" @click="handleAdd" :loading="loading">新增</el-button>
    </div>
    <!-- 表格 -->
    <div class="bg-bg_color p-5 pt-3 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="data">
          <ElButton type="primary" link @click="handleDetail(data.row)"> 详情 </ElButton>
          <ElButton type="primary" link @click="handleEdit(data.row)"> 编辑 </ElButton>
          <ElButton type="danger" link @click="handleDelete(data.row.id)"> 删除 </ElButton>
        </template>
      </PureTable>
    </div>
    <el-dialog
      v-model="engineerDialogVisibleRef"
      :title="isEditDialog ? '编辑' : '新增'"
      width="800px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCloseDialog()"
    >
      <AddEditForm
        ref="formRef"
        v-loading="loading"
        :detail="state.selectRow"
        :equipmentList="state.equipmentList"
        :isEdit="isEditDialog"
      />
      <template #footer>
        <span>
          <el-button :loading="loading" @click="handleCloseDialog()">取消</el-button>
          <el-button :loading="loading" type="primary" @click="handleSave()">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue";
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import { useColumns } from "./column-config";
import AddEditForm from "./components/add-edit-form.vue";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useTableConfig } from "@/utils/useTableConfig";
import { getEquipmentApi } from "@/api/e-capital-construction/test-parameter/index";
import {
  ejjProjectPageApi,
  createEngineeringApi,
  updateEngineeringApi,
  deleteEngineeringApi
} from "@/api/e-capital-construction/engineering-info/index";
import { EProjectModelReport, EquipmentJSONModel, EProjectModel, EProjectSearchModel } from "@/models";
/**
 * E基建-工程信息
 */

// 设置标题
const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);

const router = useRouter();

const { pagination } = useTableConfig();

const loading = ref(false);
const list = ref<EProjectModel[]>([]);
const engineerDialogVisibleRef = ref<boolean>();

const state = reactive({
  searchForm: {} as EProjectSearchModel,
  selectRow: {} as EProjectModel,
  equipmentList: {} as EquipmentJSONModel
});

const { columnsConfig } = useColumns(state.equipmentList);
const isEditDialog = ref(false);
const formRef = ref();
/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: EProjectModelReport = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    ...state.searchForm
  };
  const { data } = await ejjProjectPageApi(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 请求物资分类列表
 */
const getEquipmentList = useLoadingFn(async () => {
  const { data } = await getEquipmentApi();
  data.forEach(item => {
    state.equipmentList[item.code] = item;
  });
}, loading);
/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

/**
 * @description: 新增
 */
const handleAdd = async () => {
  engineerDialogVisibleRef.value = true;
  isEditDialog.value = false;
};

/**
 * @description: 取消
 */
const handleCloseDialog = async () => {
  engineerDialogVisibleRef.value = false;
  state.selectRow = {} as EProjectModel;
};

/**
 * @description: 重置
 */
const onResetQuery = () => {
  state.searchForm = {} as EProjectSearchModel;
  requestList();
};

/**
 * @description: 新增编辑工程信息
 */
const handleSave = async () => {
  const formData = await formRef.value.validateForm();
  if (!formData) {
    return;
  }
  onSave();
};

/**
 * @description: 保存工程信息
 */
const onSave = useLoadingFn(async () => {
  const params = formRef.value.getFormValue();
  const { data } = isEditDialog.value ? await updateEngineeringApi(params) : await createEngineeringApi(params);
  if (data) {
    engineerDialogVisibleRef.value = false;
    requestList();
    ElMessage({ type: "success", message: "保存成功" });
  }
}, loading);

/**
 * @description: 删除工程信息
 */
const handleDelete = async (id: string) => {
  if (!(await useConfirm("是否确认删除", "删除确认"))) {
    return;
  }
  onDelete(id);
};

/**
 * @description: 删除工程信息
 */

const onDelete = useLoadingFn(async (id: string) => {
  const { data } = await deleteEngineeringApi(id);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
}, loading);

/**
 * @description: 编辑
 */
const handleEdit = async (row: EProjectModel) => {
  engineerDialogVisibleRef.value = true;
  state.selectRow = row;
  isEditDialog.value = true;
};

/**
 * @description: 详情
 */
const handleDetail = async (row: EProjectModel) => {
  router.push({
    name: "engineeringInfoDetail",
    query: { id: row.id, type: row.equipmentCode, equipmentName: row.equipmentName, equipmentCode: row.equipmentCode }
  });
};

onMounted(() => {
  requestList();
  getEquipmentList();
});
</script>

<style scoped></style>
