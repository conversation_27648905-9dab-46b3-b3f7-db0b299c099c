import { StatusEnum } from "@/enums";

export interface ITenant {
  id?: string;
  /** 简称 */
  name: string;
  /** 企业名称 */
  comName: string;

  /** 租户编号 */
  tenantKey?: string;

  /** 联系人 */
  contactName: string;

  /** 联系手机 */
  contactMobile: string;

  /** 联系邮箱 */
  contactEmail: string;

  /** 状态 */
  status: StatusEnum;

  createTime: Date;

  /** 供应商编 */
  supplierCode?: string;

  /** 地址 */
  address: string;

  /** 供应商名称 */
  supplierName?: string;

  /** 密钥 */
  publicKey?: string;

  /** 物资种类 */
  subclassCode?: Array<string>;

  subclassName?: Array<string>;

  /** EIP授权配置开关 */
  enableEip?: boolean;

  /** IOT授权配置开关 */
  enableIot?: boolean;

  /** IOT授权配置-物资种类 */
  iotSubclassCode?: Array<string>;

  /** 客户端ID */
  iotClientId?: string;

  /** 客户端密码 */
  iotClientSecret?: string;

  /** 授权范围 */
  iotScopes?: string;

  /** IOT 选择的物资种类 */
  iotSubclassName?: Array<string>;

  /** 广州供电局授权配置开关 */
  enableGz?: boolean;
  /** 广州供电局授权配置-物资种类code */
  gzSubclassCode?: Array<string>;
  /** 广州供电局授权配置-物资种类名称 */
  gzSubclassName?: Array<string>;
  /** 广州供电局客户端账号名 */
  gzAccountName?: string;
  /** 广州供电局客户端密码 */
  gzAccountKey?: string;

  /** 报工默认地址(应用于页面新增报工默认值) */
  defaultReportAddress?: string;
  /** 统一社会信用代码（E基建用） */
  manufacturerId?: string;
}
