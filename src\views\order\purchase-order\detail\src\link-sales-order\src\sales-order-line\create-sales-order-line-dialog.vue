<template>
  <el-dialog
    v-model="visible"
    title="新增销售订单行"
    :close-on-click-modal="false"
    destroy-on-close
    class="middle"
    :close-on-press-escape="false"
    @open="dialogOpen"
    @close="dialogClose"
  >
    <el-steps :active="active" class="mb-5">
      <el-step title="选择采购订单行" />
      <el-step title="填写销售订单行" />
      <el-step title="完成" />
    </el-steps>

    <PurchaseOrderLineSelectTable
      ref="purchaseOrderLineSelectTableRef"
      :multiple="true"
      v-show="isFirstStep"
      v-model="purchaseLineIds"
      :height="400"
    />
    <SalesOrderLineForm
      v-if="isSecondStep"
      ref="form"
      type="create"
      :category-code="salesOrderStore.activeOrder.categoryCode"
      :parent-no="salesOrderStore.activeOrder.soNo"
    />
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button @click="prev" v-show="isSecondStep">上一步</el-button>
      <el-button
        type="primary"
        @click="handleNext"
        :loading="nextLoading"
        v-show="isFirstStep"
        :disabled="selectedPurchaseLine"
        >下一步</el-button
      >
      <el-button type="warning" @click="handleSaveAndContinue" :loading="continueLoading" v-show="isSecondStep"
        >保存，并继续新增</el-button
      >
      <el-button type="primary" @click="handleSaveAndClose" :loading="saveLoading" v-show="isSecondStep"
        >保存</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  usePurchaseOrderDetailSalesOrderLineStore,
  usePurchaseOrderDetailSalesOrderStore
} from "@/store/modules/purchase-order-detail";
import { computed, nextTick, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import PurchaseOrderLineSelectTable from "../purchase-order-line/purchase-order-line-select-table.vue";
import SalesOrderLineForm from "@/views/order/purchase-order/detail/src/link-sales-order/src/sales-order-line/sales-order-line-form.vue";
import { ElMessage } from "element-plus";
import { ISalesOrderLineForm } from "@/models";
import { usePurchaseOrderLink } from "@/views/order/purchase-order/detail/src/hooks/usePurchaseOrderLink";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { useMaterialStore } from "@/store/modules/material";

const store = usePurchaseOrderDetailSalesOrderLineStore();
const salesOrderStore = usePurchaseOrderDetailSalesOrderStore();
const purchaseOrderStore = usePurchaseOrderDetailStore();
const materialStore = useMaterialStore();
const { refreshLink } = usePurchaseOrderLink();

const active = ref(0);
const purchaseOrderLineSelectTableRef = ref<InstanceType<typeof PurchaseOrderLineSelectTable>>();
const purchaseLineIds = ref<Array<string>>([]);
const form = ref<InstanceType<typeof SalesOrderLineForm>>();
const saveLoading = ref(false);
const nextLoading = ref(false);
const continueLoading = ref(false);
const handleSaveAndClose = useLoadingFn(saveAndClose, saveLoading);
const handleSaveAndContinue = useLoadingFn(saveAndContinue, continueLoading);
const handleNext = useLoadingFn(next, nextLoading);

const visible = computed({
  get() {
    return store.createVisible;
  },
  set(value) {
    store.$patch({ createVisible: value });
  }
});
const isFirstStep = computed(() => active.value === 0);
const isSecondStep = computed(() => active.value === 1);
const selectedPurchaseLine = computed(() => !purchaseLineIds.value.length);

let shouldRefresh = false;

function dialogOpen() {
  shouldRefresh = false;
}

function dialogClose() {
  reset();
  if (shouldRefresh) {
    refreshLink();
  }
}

function cancel() {
  visible.value = false;
}

function prev() {
  active.value = 0;
}

async function next() {
  active.value = 1;
  await setFormValue();
}

async function saveAndContinue() {
  if (await save()) {
    await purchaseOrderStore.refreshPurchaseOrder();
    reset();
  }
}

async function saveAndClose() {
  if (!visible.value) {
    return;
  }
  if (await save()) {
    ElMessage.success("新增成功");
    cancel();
  }
}

async function save() {
  const { invalid, data } = await getValue();
  if (invalid) {
    return false;
  }
  await store.createSalesOrderLine(purchaseLineIds.value, data);
  shouldRefresh = true;
  return true;
}

function reset() {
  active.value = 0;
  purchaseLineIds.value = [];
}

async function getValue(): Promise<{ data: ISalesOrderLineForm | null; invalid: boolean }> {
  let invalid = false;
  const data = await form.value.getValue().catch(() => {
    invalid = true;
    return null;
  });
  return { data, invalid };
}

async function setFormValue() {
  const list = purchaseOrderLineSelectTableRef.value.getSelectedPurchaseLine();
  nextTick(() => {
    const materialCodes = [...new Set(list.map(m => m.materialCode))];
    if (materialCodes.length === 1) {
      materialStore.getMaterialDetailByCode(list[0].subClassCode, list[0].materialCode).then(material => {
        form.value.initializeForm({
          subClassCode: list[0].subClassCode,
          materialNumber: list[0].amount,
          materialCode: material.materialCode,
          materialName: material.materialName,
          materialUnit: material.materialUnit,
          materialDesc: material.materialDescribe,
          materialId: material.id
        });
      });
    } else {
      form.value.initializeForm({ subClassCode: list[0].subClassCode, materialNumber: list[0].amount });
    }
  });
}
</script>

<style scoped></style>
