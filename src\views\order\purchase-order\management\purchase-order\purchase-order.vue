<template>
  <div class="purchase-order flex flex-col flex-1 overflow-hidden">
    <PurchaseOrderFilter :link-steps="linkSteps" v-model="state" @clear-selected="postClear">
      <template #right>
        <BatchArchiveBar v-model="selectedList" @post-clear="postClear" />
      </template>
    </PurchaseOrderFilter>
    <PureTable
      ref="pureTableRef"
      class="flex-1 pagination"
      row-key="id"
      :data="purchaseOrderStore.purchaseOrders"
      :columns="columns"
      size="large"
      :row-class-name="getRowReaded"
      showOverflowTooltip
      :loading="purchaseOrderStore.queryLoading"
      :pagination="pagination"
      :class="{ 'is-batching': selectedList.length }"
      @page-size-change="onPageSizeChange($event)"
      @page-current-change="onCurrentPageChange($event)"
      @sort-change="sortChange"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import PurchaseOrderFilter from "./purchase-order-filter.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePurchaseOrderStore, usePurchaseOrderSyncStore, useUserTenantStore } from "@/store/modules";
import { useI18n } from "vue-i18n";
import { LinkStepEnum, LinkStepEnumAlias } from "@/enums";
import { useColumns } from "./columns";
import { onMounted, onUnmounted, ref } from "vue";
import { handleOrderType } from "@/utils/sortByOrderType";
import BatchArchiveBar from "./batch-archive-bar.vue";
import { emitter } from "@/utils/mitt";
import { useRoute } from "vue-router";
import { IPurchaseOrderReq } from "@/models";

const purchaseOrderStore = usePurchaseOrderStore();
const syncStore = usePurchaseOrderSyncStore();
const userTenantStore = useUserTenantStore();
syncStore.refreshSyncResult();
const route = useRoute();

const pureTableRef = ref<PureTableInstance>();

// 已选中列表
const selectedList = ref<Array<string>>([]);
const { pagination, columns } = useColumns(useI18n().t);
const state = ref<{
  linkStepKey?: LinkStepEnum;
  onlyLatestPull: boolean;
  onlyFollow: boolean;
}>({
  linkStepKey: undefined,
  onlyLatestPull: false,
  onlyFollow: false
});
const linkStepMap = {
  [LinkStepEnum.linkSales]: { label: "purchaseOrder.buttons.linkSales", linkStepName: LinkStepEnumAlias.linkSales },
  [LinkStepEnum.productionPlan]: {
    label: "purchaseOrder.buttons.productionPlan",
    linkStepName: LinkStepEnumAlias.productionPlan
  },
  [LinkStepEnum.productionOrder]: {
    label: "purchaseOrder.buttons.productionOrder",
    linkStepName: LinkStepEnumAlias.productionOrder
  },
  [LinkStepEnum.productionData]: {
    label: "purchaseOrder.buttons.productionData",
    linkStepName: LinkStepEnumAlias.productionData
  },
  [LinkStepEnum.syncOrder]: { label: "purchaseOrder.buttons.syncOrder", linkStepName: LinkStepEnumAlias.syncOrder },
  [LinkStepEnum.qualityEval]: {
    label: "purchaseOrder.buttons.qualityEval",
    linkStepName: LinkStepEnumAlias.qualityEval
  }
};

const linkSteps = [
  { label: "purchaseOrder.buttons.all", value: undefined, linkStepName: undefined },
  ...userTenantStore.supportLinkSteps.map(step => ({ value: step, ...linkStepMap[step] }))
];

function handleSelect(selection) {
  selectedList.value = selection.map(item => item.id);
}

function handleSelectAll(selection) {
  selectedList.value = selection.map(item => item.id);
}

function postClear() {
  pureTableRef.value?.getTableRef().clearSelection();
  selectedList.value = [];
}

onMounted(() => {
  pagination.currentPage = 1;
  const { linkStep, readed, follow } = purchaseOrderStore.initQueryParams || {};
  state.value.onlyFollow = follow;
  state.value.linkStepKey = linkStep;
  state.value.onlyLatestPull = readed;
  const queryReaded = state.value.onlyLatestPull ? false : undefined;
  const queryFollow = state.value.onlyFollow ? state.value.onlyFollow : undefined;

  const { start, end } = route.query;

  const queryParams: IPurchaseOrderReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    linkStep,
    readed: queryReaded,
    follow: queryFollow
  };

  if (start && end) {
    queryParams.createTime = [start as string, end as string];
  }

  purchaseOrderStore.queryPurchaseOrders(queryParams);
  purchaseOrderStore.queryPurchaseLinkStep({
    readed: queryReaded,
    follow: queryFollow
  });
  purchaseOrderStore.getStatisticsPurchaseOrders();
  purchaseOrderStore.setInitQueryParams({});
});

function getRowReaded({ row }): string {
  return row.readed ? "" : "font-medium";
}

const onPageSizeChange = (pageSize: number) => {
  purchaseOrderStore.queryPurchaseOrdersByPageSize(pageSize);
};

const onCurrentPageChange = (pagIndex: number) => {
  purchaseOrderStore.queryPurchaseOrdersByPageIndex(pagIndex);
};

async function sortChange({ prop, order }) {
  let sortParams = {
    orderByField: "",
    orderByType: ""
  };
  if (order) {
    sortParams = {
      orderByField: prop,
      orderByType: handleOrderType(order)
    };
  }
  pagination.currentPage = 1;
  const params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    readed: state.value.onlyLatestPull ? false : undefined,
    follow: state.value.onlyFollow ? state.value.onlyFollow : undefined,
    linkStep: state.value.linkStepKey,
    ...sortParams
  };
  purchaseOrderStore.queryPurchaseOrdersByFilter(params);
}

emitter.on("refreshPurchaseOrderList", async () => {
  postClear();
  purchaseOrderStore.queryPurchaseOrdersByFilter({
    pageNo: pagination.currentPage
  });
});

onUnmounted(() => {
  emitter.off("refreshPurchaseOrderList");
});
</script>

<style scoped lang="scss">
.is-batching {
  :deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }
}
</style>
