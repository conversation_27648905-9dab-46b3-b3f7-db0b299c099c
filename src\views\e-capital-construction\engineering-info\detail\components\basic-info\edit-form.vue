<template>
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="基本信息" />
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="原材料组部件类型"
          prop="gisComponentType"
          :rules="{ required: true, message: '请选择原材料组部件类型', trigger: 'change' }"
        >
          <el-select
            v-model="formData.gisComponentType"
            class="w-full"
            placeholder="请选择原材料组部件类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in GisComponentBasicOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-else :span="12">
        <el-form-item
          label="原材料组部件类型"
          prop="componentType"
          :rules="{ required: true, message: '请选择原材料组部件类型', trigger: 'change' }"
        >
          <el-select
            v-model="formData.componentType"
            class="w-full"
            placeholder="请选择原材料组部件类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in ComponentBasicOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="冷却方式"
          prop="coolingMethod"
          :rules="{ required: false, message: '请选择冷却方式', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.coolingMethod">
            <el-radio v-for="item in CoolingMethodEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="供应商统一社会信用代码"
          prop="supplierUscc"
          :rules="{ required: true, message: '请输入供应商统一社会信用代码', trigger: 'change' }"
        >
          <el-input
            v-model="formData.supplierUscc"
            maxlength="20"
            placeholder="请输入供应商统一社会信用代码"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="供应商"
          prop="supplier"
          :rules="{ required: true, message: '请输入供应商', trigger: 'change' }"
        >
          <el-input v-model="formData.supplier" maxlength="480" placeholder="请输入供应商" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="实物ID(原材料组部件)"
          prop="utcNum"
          :rules="{ required: formData.mainEquipmentutcNum, message: '请输入实物ID', trigger: 'change' }"
        >
          <el-input v-model="formData.utcNum" placeholder="请输入实物ID" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: true, message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" maxlength="50" placeholder="请输入子实物编码" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物ID(单元)"
          prop="subPhysicalItemId"
          :rules="{ required: false, message: '请输入子实物ID(单元)', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemId" placeholder="请输入子实物ID(单元)" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="主体变/调补变类型"
          prop="mainRegulatingTransformer"
          :rules="{
            required: $route.query.equipmentName == EquipmentNameEnum.Transformer.toString(),
            message: '请选择主体变/调补变类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mainRegulatingTransformer">
            <el-radio v-for="item in MainRegulatEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="批次号"
          prop="batchNumber"
          :rules="{
            required: isCombiner || formData.componentType == GisComponentEnumExt.InsulatedRod.toString(),
            message: '请输入批次号',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.batchNumber" placeholder="请输入批次号" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="规格型号"
          prop="specificationModel"
          :rules="{
            required: false,
            message: '请输入规格型号',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.specificationModel" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="产地类型"
          prop="domesticOrImported"
          :rules="{ required: true, message: '请选择产地类型', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.domesticOrImported">
            <el-radio v-for="item in DomesticOrImportedEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="钢印号/字头号"
          prop="stampingNumber"
          :rules="{
            required: false,
            message: '请输入钢印号/字头号',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.stampingNumber" placeholder="请输入钢印号/字头号" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="监测类型"
          prop="monitoringTypesSelect"
          :rules="{ required: false, message: '请选择监测类型', trigger: 'change' }"
        >
          <el-select
            v-model="formData.monitoringTypesSelect"
            class="w-full"
            placeholder="请选择监测类型"
            multiple
            clearable
            filterable
          >
            <el-option
              v-for="item in MonitoringTypesEnumOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="24">
        <el-row>
          <el-col :span="24">
            <TitleBar class="mb-2" title="附件" />
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="送检报告"
              prop="testFileId"
              :rules="{ required: false, message: '请选择附件', trigger: 'change' }"
            >
              <!-- 附件上传 -->
              <UploadFileCom ref="uploadRefTest" v-model="formData.testFileInfo" @change="uploadChange" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="出厂报告"
              prop="factoryFileId"
              :rules="{ required: false, message: '请选择附件', trigger: 'change' }"
            >
              <UploadFileCom ref="uploadRefFac" v-model="formData.factoryFileInfo" @change="uploadChangeFac" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import TitleBar from "@/components/TitleBar/index";
import {
  ComponentBasicOptions,
  GisComponentBasicOptions,
  EquipmentTypeEnumExt,
  GisComponentEnumExt,
  EquipmentNameEnum,
  MainRegulatEnumOptions,
  CoolingMethodEnumOptions,
  DomesticOrImportedEnumOptions,
  MonitoringTypesEnumOptions
} from "@/enums";
import { BasicInfoModel, FileInfoModel } from "@/models";
import { useRoute } from "vue-router";
import { FormInstance } from "element-plus";
import UploadFileCom from "../upload-file/index.vue";

const props = withDefaults(
  defineProps<{
    detail: BasicInfoModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as BasicInfoModel;
    },
    isEdit: false
  }
);
const formData = reactive({} as BasicInfoModel);
const testFileList = ref([]);
const factoryFileList = ref([]);
const uploadRefFac = ref();
const uploadRefTest = ref();

const route = useRoute();

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

watch(
  () => props.detail,
  value => {
    Object.assign(formData, value);
    if (props.isEdit) {
      formData.monitoringTypesSelect = formData.monitoringTypes ? formData.monitoringTypes.split(",") : [];
      testFileList.value = formData.testFileId ? [formData.testFileInfo] : [];
      factoryFileList.value = formData.factoryFileId ? [formData.factoryFileInfo] : [];
    }
  },
  { immediate: true }
);

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

const uploadChange = (info: FileInfoModel) => {
  formData.testFileInfo = info;
  formData.testFileId = info?.id || "";
  testFileList.value = info?.id ? [info] : [];
};

const uploadChangeFac = (info: FileInfoModel) => {
  formData.factoryFileInfo = info;
  formData.factoryFileId = info?.id || "";
  factoryFileList.value = info?.id ? [info] : [];
};

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const params = {
    ...formData,
    monitoringTypes: formData.monitoringTypesSelect.join(",")
  };
  delete params.monitoringTypesSelect;
  return Object.assign({}, params as BasicInfoModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  uploadRefTest.value?.resetFormValue();
  uploadRefFac.value?.resetFormValue();
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss"></style>
