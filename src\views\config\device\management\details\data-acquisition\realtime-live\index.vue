<!-- 监控播放 -->
<template>
  <div>
    <div class="flex items-center justify-start mb-2">
      <div>摄像头：</div>
      <el-select v-model="activeItem" value-key="id" placeholder="请选择要查看的监控">
        <template #loading>
          <svg class="circular" viewBox="0 0 50 50">
            <circle class="path" cx="25" cy="25" r="20" fill="none" />
          </svg>
        </template>
        <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item" />
      </el-select>
    </div>
    <div v-if="!url" class="h-[300px] flex items-center justify-center bg-black text-white">
      {{ loading ? "正在获取播放地址..." : "暂无摄像头信息" }}
    </div>
    <div id="cx-player" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch } from "vue";
import Xgplayer, { Events } from "xgplayer";
import { HlsPlugin } from "xgplayer-hls";
import "xgplayer/dist/index.min.css";
import type { IAssociatedCamera } from "@/models";
import { getMonitorDeviceWatchUrlById } from "@/api/monitor-device/monitor-device";
import { queryAssociatedCamera } from "@/api/device/associated-camera";
import { getToken } from "@/utils/auth";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCancelHttp } from "@/utils/http/cancel-http";

const props = defineProps<{
  deviceId: string;
}>();

let player: Xgplayer | null = null;
const list = ref<Array<IAssociatedCamera>>([]);
const activeItem = ref<IAssociatedCamera | null>(null);
const url = ref("");
const loading = ref(false);
const cancelHttp = useCancelHttp();
const errorHttpCode = [404, 401];

/**
 * @description: 生成直播url
 */
const genLiveUrl = useLoadingFn(async () => {
  const { data: streamUrl } = await getMonitorDeviceWatchUrlById(activeItem.value.id, cancelHttp.signal.value);
  const token = getToken();
  return `${streamUrl}?token=${token.accessToken}`;
}, loading);

/**
 * @description: 获取监控列表
 */
const requestMonitorList = async () => {
  if (!props.deviceId) {
    return;
  }
  const { data } = await queryAssociatedCamera({
    deviceId: props.deviceId,
    relationFlag: true,
    pageSize: 100
  });
  if (!data || !data.list.length) {
    list.value = [];
    return;
  }
  list.value = data.list;
  activeItem.value = list.value[0];
};

/**
 * @description: 视频错误处理
 */
const onError = async (error: any) => {
  // 如果是404,401，网络错误之外的错误，则不进行重试；
  if (!errorHttpCode.includes(error.httpCode) || error.errorType !== "network") {
    return;
  }
  url.value = await genLiveUrl();
  if (url.value && player) {
    player?.playNext({
      url: url.value,
      autoplay: true,
      isLive: true
    });
  }
};

/**
 * @description: 初始化视频播放器
 */
const initPlayer = () => {
  if (url.value) {
    player = new Xgplayer({
      id: "cx-player",
      plugins: [HlsPlugin],
      lang: "zh-cn",
      fluid: true,
      marginControls: false,
      volume: 0,
      isLive: true,
      autoplay: true,
      url: url.value,
      height: 500
    });
    // 播放器错误监听
    player.on(Events.ERROR, onError);
  }
};

watch(
  () => props.deviceId,
  async () => {
    activeItem.value = null;
    await requestMonitorList();
  }
);

watch(activeItem, async item => {
  url.value = "";
  if (player) {
    player.destroy();
    player = null;
  }
  if (!item) {
    return;
  }
  cancelHttp.abortAndCreateNewController();
  url.value = await genLiveUrl();
  initPlayer();
});

onMounted(requestMonitorList);

onBeforeUnmount(() => {
  player?.destroy();
  player = null;
});
</script>

<style scoped lang="scss"></style>
