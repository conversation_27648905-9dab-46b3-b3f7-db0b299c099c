<template>
  <div class="flex items-center" v-auth="PermissionKey.form.formPurchaseWorkReportDelete">
    <transition name="fade">
      <div class="flex justify-between items-center mr-6 text-base" v-if="selectedCount">
        已选中:
        <span class="text-primary mx-4">
          {{ selectedCount }}
        </span>
        项
        <el-icon class="close ml-4" @click="clearSelection"><CircleCloseFilled /></el-icon>
      </div>
    </transition>
    <el-button @click="handleDelete" :disabled="!selectedCount" :loading="loading"> 批量删除报工 </el-button>
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { CircleCloseFilled } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { batchDeleteReportWork } from "@/api/report-work";
import { useLoadingFn } from "@/utils/useLoadingFn";

/**
 * 批量删除检测标准
 */

const props = defineProps<{
  /** 已选中报工id */
  selectedIdList: Array<string>;
  /** */
}>();

const emits = defineEmits<{
  /** 删除后置操作 */
  (event: "postDeleteSuccess"): void;
  /** 清空选中项 */
  (event: "clearSelection"): void;
}>();

const selectedCount = computed(() => props.selectedIdList.length);

const loading = ref(false);

const requestBatchDelete = useLoadingFn(batchDeleteReportWork, loading);

function clearSelection() {
  emits("clearSelection");
}

async function handleDelete() {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  const res = await requestBatchDelete(props.selectedIdList);
  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  emits("postDeleteSuccess");
}
</script>
<style lang="scss" scoped>
.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}
</style>
