<template>
  <div class="flex mb-4">
    <p class="text-xl text-primaryText font-semibold">采购订单</p>
    <div class="flex ml-4 text-base items-center" v-if="syncResult?.modifyTime">
      <div class="flex items-center mr-10">
        <div class="label mr-2">{{ $t("purchaseOrder.status.lastPullTime") }}</div>
        <CxTag type="info">{{ syncResult.modifyTime }}</CxTag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePurchaseOrderSyncStore } from "@/store/modules";
import { computed } from "vue";
import CxTag from "@/components/CxTag/index.vue";

const syncStore = usePurchaseOrderSyncStore();
const syncResult = computed(() => syncStore.result);
</script>

<style scoped lang="scss"></style>
