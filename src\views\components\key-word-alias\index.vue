<template>
  <div>{{ keyWordAlias }}</div>
</template>

<script setup lang="ts">
import { useKeyWordAliasStore } from "@/store/modules";
import { computedAsync } from "@vueuse/core";

const keyWordAliasStore = useKeyWordAliasStore();

const props = withDefaults(
  defineProps<{
    keyWordCode?: string;
    keyWordName?: string;
  }>(),
  {}
);

const keyWordAlias = computedAsync(() => getKeyWordAlias);

async function getKeyWordAlias() {
  const keyWordAlias = await keyWordAliasStore.getKeyWordAlias;
  return keyWordAlias.find(x => x.keyWordCode == props.keyWordCode)?.keyWordAlias || props.keyWordName;
}
</script>

<style scoped lang="scss"></style>
