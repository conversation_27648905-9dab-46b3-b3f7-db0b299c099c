import { defineStore } from "pinia";
import { AcquisitionMaintain, IDeviceAcquisition, IAcquisitionMaintainForm, IPointReq } from "@/models/device";
import * as api from "@/api/device/acquisition-maintain";

/** 标准采集点维护 */
export const useAcquisitionMaintainStore = defineStore({
  id: "cx-maintain-store",
  state: () => ({
    processList: [] as Array<AcquisitionMaintain>,
    acquisitionlist: [] as Array<IDeviceAcquisition>,
    loading: false
  }),
  actions: {
    /**
     * 获取采集点工序信息列表
     * @param deviceId 设备ID
     * @param processId 工序ID
     * @returns
     */
    async queryAutomaticCollection(deviceId: string, processId: string) {
      this.loading = true;
      const res = await api.queryAutomaticCollection(deviceId, processId);
      this.loading = false;
      this.processList = res.data;
    },
    /** 根据设备id查询该设备下未绑定过的采集点信息 */
    async deviceIdAcquisitionlist(deviceId: string, processId: string) {
      const res = await api.deviceIdAcquisitionlist(deviceId, processId);
      this.acquisitionlist = res.data;
    },
    /** 维护标准采集点绑定 */
    async bindAcquisitionPoint(data: IAcquisitionMaintainForm) {
      return api.bindAcquisitionPoint(data);
    },
    /** 维护标准采集点解绑 */
    async unbindAcquisitionPoint(data: IAcquisitionMaintainForm) {
      return api.unbindAcquisitionPoint(data);
    },

    /** 根据工序和设备id获取采集项信息 */
    async pointByProcessIdDeviceId(params: IPointReq) {
      const { deviceId, processId } = params;
      return (await api.getPointByDeviceIdProcessId({ deviceId, processId })).data;
    },

    resetProcessList() {
      this.processList = [];
    }
  }
});
