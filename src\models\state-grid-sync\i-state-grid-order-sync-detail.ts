import { StateGridOrderSyncStep } from "@/enums";

export interface IStateGridOrderSyncDetail {
  salesLine: boolean;
  salesLineErrCnt: number;
  salesLineSyncingCnt: number;
  salesLineSuccessCnt: number;
  salesLineTotalCnt: number;
  productionPlan: boolean;
  productionPlanErrCnt: number;
  productionPlanSyncingCnt: number;
  productionPlanSuccessCnt: number;
  productionPlanTotalCnt: number;
  production: boolean;
  productionErrCnt: number;
  productionSyncingCnt: number;
  productionSuccessCnt: number;
  productionTotalCnt: number;
  workOrder: boolean;
  workOrderErrCnt: number;
  workOrderSyncingCnt: number;
  workOrderSuccessCnt: number;
  workOrderTotalCnt: number;
  workReport: boolean;
  workReportErrCnt: number;
  workReportSyncingCnt: number;
  workReportSuccessCnt: number;
  workReportTotalCnt: number;
  materialCompQuality: boolean;
  materialCompQualityErrCnt: number;
  materialCompQualitySyncingCnt: number;
  materialCompQualitySuccessCnt: number;
  materialCompQualityTotalCnt: number;
  techProcessQuality: boolean;
  techProcessQualityErrCnt: number;
  techProcessQualitySyncingCnt: number;
  techProcessQualitySuccessCnt: number;
  techProcessQualityTotalCnt: number;
  outgoingQuality: boolean;
  outgoingQualityErrCnt: number;
  outgoingQualitySyncingCnt: number;
  outgoingQualitySuccessCnt: number;
  outgoingQualityTotalCnt: number;
  productInfo: boolean;
  productInfoErrCnt: number;
  productInfoSyncingCnt: number;
  productInfoSuccessCnt: number;
  productInfoTotalCnt: number;
  /** 自动采集项 */
  processData: boolean;
  processDataErrCnt: number;
  processDataSuccessCnt: number;
  processDataTotalCnt: number;
  processDataSyncingCnt: number;
  /** 技术标准 */
  technicalStandard: boolean;
  technicalStandardErrCnt: number;
  technicalStandardSuccessCnt: number;
  technicalStandardTotalCnt: number;
  technicalStandardSyncingCnt: number;
  /** 附件报告 */
  attachedReport: boolean;
  attachedReportErrCnt: number;
  attachedReportSuccessCnt: number;
  attachedReportTotalCnt: number;
  attachedReportSyncingCnt: number;
}

export interface IStateGridOrderSyncDetailList {
  errCnt: number;
  key: StateGridOrderSyncStep;
  successCnt: number;
  syncResult: boolean;
  syncingCnt: number;
  totalCnt: number;
}
