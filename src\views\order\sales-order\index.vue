<template>
  <div class="bg-bg_color pt-[8px] pb-4 px-6">
    <SalesOrderSearchForm @onCreateSalesOrder="handleCreateSalesOrder()" @onSearch="onSearch($event)" />
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <div class="flex flex-col mb-5">
      <div class="flex items-center text-base mb-3">
        <div>订单共 {{ state.count }} 条</div>
        <!--订单号缺少-->
        <div class="ml-5">
          <ElCheckbox
            :label="getFaultPoNoCount()"
            class="font-medium"
            @change="onQueryFaultPoNoSalesOrder()"
            v-model="state.isFaultPoNo"
          />
        </div>
        <!--关注-->
        <div class="ml-5">
          <ElCheckbox
            :label="getFollowCount()"
            class="font-medium"
            @change="onQueryFollowSalesOrder()"
            v-model="state.follow"
          />
        </div>
        <!--归档-->
        <div class="ml-5">
          <ElCheckbox
            :label="getdocumentationCount()"
            class="font-medium"
            @change="onQueryDocumentationSalesOrder()"
            v-model="state.documentation"
          />
        </div>
      </div>
      <div class="flex justify-between">
        <ElRadioGroup v-model="state.linkStepKey" @change="onChangeLinkStepSearch()">
          <ElRadioButton :label="item.linkStepKey" v-for="item of state.linkSteps" :key="item.value">
            {{ $t(item.label) }}
            <span class="text-sm leading-none">{{ getStepValue(item.value) }}</span>
          </ElRadioButton>
        </ElRadioGroup>
        <BatchArchiveBar v-model="selectedList" @post-clear="postClear" />
      </div>
    </div>
    <PureTable
      ref="pureTableRef"
      class="flex-1 pagination"
      row-key="id"
      :data="state.salesOrderTableData"
      :columns="columns"
      size="large"
      showOverflowTooltip
      :loading="loading"
      :pagination="pagination"
      :class="{ 'is-batching': selectedList.length }"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
      @sort-change="sortChange"
      @select="handleSelect"
      @select-all="handleSelectAll"
    >
      <template #follow="data">
        <div @click="onToggleFollow(data.row)">
          <StarFilled class="w-[20px] cursor-pointer" :style="getStarStyle(data.row)" />
        </div>
      </template>
      <template #poNo="{ row }">
        <nav-dialog
          v-if="row.purchaseIds && row.purchaseIds.length > 1"
          title="选择采购订单"
          pre-path="/purchase-order"
          :list="calcNavDialogList(row.poNo, row.purchaseIds)"
        >
          <template #default="{ open }">
            <div
              v-if="row.poNo"
              class="text-primary w-full overflow-hidden text-ellipsis mt-1 cursor-pointer"
              @click="open"
            >
              {{ row.poNo }}
            </div>
          </template>
        </nav-dialog>
        <router-link
          v-else-if="row.purchaseIds.length === 1"
          class="text-primary"
          :to="`/purchase-order/${row.purchaseIds[0]}`"
        >
          {{ row.poNo }}
        </router-link>
      </template>
      <template #categoryName="data">
        {{ getCategoryNameByCode(data.row.categoryCode) }}
      </template>
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onViewSalesOrderDetail(data.row.id)">详情</ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseSalesEdit"
            type="primary"
            link
            @click="onEditSalesOrder(data.row)"
            >编辑</ElButton
          >
          <ElButton
            v-auth="PermissionKey.form.formPurchaseSalesDelete"
            type="danger"
            link
            @click="onDeleteSalesOrder(data.row.id)"
            >删除</ElButton
          >
        </div>
      </template>
      <template #matSyncFlagId="data">
        <span>{{ getFlagNameById(data.row.matSyncFlagId) }} </span>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      v-model="state.salesOrderDialogVisible"
      :title="getOrderFormModalTitle()"
      class="middle"
      align-center
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseSalesOrderModal"
    >
      <SalesOrderForm ref="orderFormRef" />
      <template #footer>
        <el-button @click="handleCloseSalesOrderModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveSalesOrderModal()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import SalesOrderSearchForm from "./sales-order-search-form.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { reactive, onMounted, ref, watch, onUnmounted } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { useConfirm } from "@/utils/useConfirm";
import { IOption, ISalesOrder, ISalesOrderDetail, ISalesOrderParams } from "@/models";
import { useSalesOrderHook } from "./hooks/sales-order-hook";
import { useRouter } from "vue-router";
import { usePageStoreHook } from "@/store/modules/page";
import { SalesLinkStepEnum } from "@/enums";
import { pickBy, assign, mapValues, isArray } from "lodash-unified";
import { useI18n } from "vue-i18n";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCategoryStore, useSalesOrderManagementStore, useSynchronousFlagStore } from "@/store/modules";
import SalesOrderForm from "./sales-order-form/index.vue";
import { ElMessage } from "element-plus";
import { StarFilled } from "@element-plus/icons-vue";
import { useSalesOrderLineHook } from "./hooks/sales-order-line-hook";
import { emptyDefaultValue, PermissionKey } from "@/consts";
import { handleOrderType } from "@/utils/sortByOrderType";
import NavDialog from "@/components/nav-dialog/index.vue";
import BatchArchiveBar from "./batch-archive-bar.vue";
import { emitter } from "@/utils/mitt";
import { useRoute } from "vue-router";

usePageStoreHook().setTitle("销售订单");

const { pagination } = useTableConfig();
const { columns } = useColumns(useI18n().t);
const salesOrderManagementStore = useSalesOrderManagementStore();
const synchronousFlagStore = useSynchronousFlagStore();
const categoryStore = useCategoryStore();
const loading = ref<boolean>(false);
const saveLoading = ref<boolean>(false);
const orderFormRef = ref<InstanceType<typeof SalesOrderForm>>();
const route = useRoute();

const {
  getFaultPoNoCount,
  getFollowCount,
  querySalesOrders,
  getSupportSalesLinkSteps,
  getStepValue,
  deleteSalesOrdersById,
  ceateSalesOrder,
  updateSalesOrdersById,
  setSalesOrderDetail,
  clearSalesOrderDetail,
  salesOrderFollow,
  getdocumentationCount
} = useSalesOrderHook();
const { getSalesOrderDetailById } = useSalesOrderLineHook();
const router = useRouter();

const pureTableRef = ref<PureTableInstance>();

// 已选中列表
const selectedList = ref<Array<string>>([]);

const state = reactive<{
  salesOrderDialogVisible: boolean;
  loading: boolean;
  salesOrderTableData: Array<ISalesOrder>;
  params: ISalesOrderParams;
  linkStepKey?: SalesLinkStepEnum;
  linkSteps: Array<IOption & { linkStepKey?: string }>;
  count: number;
  isFaultPoNo: boolean;
  follow: boolean;
  selectId: string;
  documentation: boolean;
}>({
  salesOrderDialogVisible: false,
  loading: false,
  salesOrderTableData: [],
  params: {} as ISalesOrderParams,
  linkSteps: [],
  count: 0,
  isFaultPoNo: false,
  follow: false,
  selectId: "",
  documentation: false
});

onMounted(async () => {
  state.salesOrderTableData = await getTableData(queryParams());
  state.count = salesOrderManagementStore.total;
  state.linkSteps = getSupportSalesLinkSteps();
  await synchronousFlagStore.querySynchronousFlag();
  await categoryStore.getCategories();
});
const getOrderFormModalTitle = () => (!state.selectId ? "新增销售订单" : "编辑销售订单");

const onPageSizeChange = async () => {
  state.salesOrderTableData = await getTableData(queryParams());
};
const onCurrentPageChange = async () => {
  state.salesOrderTableData = await getTableData(queryParams());
};

const onViewSalesOrderDetail = (id: string) => {
  router.push(`/sales-order/${id}`);
};

const onDeleteSalesOrder = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteSalesOrdersById(id);
  state.count -= 1;
  ElMessage.success("删除成功");
  pagination.currentPage = 1;
  state.salesOrderTableData = await getTableData(queryParams());
};

const onSave = async () => {
  const formValue: ISalesOrderDetail | false = await orderFormRef.value.getValidValue().catch(() => false);
  if (!formValue) {
    return;
  }
  if (!formValue.id) {
    await ceateSalesOrder(formValue);
    state.count += 1;
  } else {
    await updateSalesOrdersById(formValue);
  }
  clearForm();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  state.params = {};
  pagination.currentPage = 1;
  state.salesOrderTableData = await getTableData(queryParams());
};

const onSaveSalesOrderModal = useLoadingFn(onSave, saveLoading);

const getTableData = useLoadingFn(querySalesOrders, loading);
const onSearch = async (params: ISalesOrderParams) => {
  pagination.currentPage = 1;
  assign(
    state.params,
    {
      orFilters: []
    },
    mapValues(params, (value, key) => {
      if (isArray(value) && value.length && key == "matSyncFlagId") {
        return value.join(",");
      }
      return value;
    })
  );
  state.salesOrderTableData = await getTableData(queryParams());
};

const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;

  const params: ISalesOrderParams = pickBy(state.params, value => {
    if (Array.isArray(value)) {
      return !!value.length;
    }
    return !!value;
  });

  const { start, end } = route.query;
  if (start && end && (!Array.isArray(state.params.createTime) || state.params.createTime.length === 0)) {
    params.createTime = [start as string, end as string];
  }

  return params;
};

function getFlagNameById(id: string) {
  if (!id) {
    return "--";
  }
  const synchronousFlag: Array<string> = id.split(",").map(id => {
    const synchronousFlag = synchronousFlagStore.synchronousFlag.find(synchronousFlag => synchronousFlag.id == id);
    if (synchronousFlag) {
      return synchronousFlag.flagName;
    }
  });
  return synchronousFlag ? synchronousFlag.join(",") : "--";
}

/** 排序支持 */
const sortChange = async ({ prop, order }) => {
  pagination.currentPage = 1;

  if (order) {
    state.params.orderByField = prop;
    state.params.orderByType = handleOrderType(order);
  } else {
    state.params.orderByField = "";
    state.params.orderByType = "";
  }
  state.salesOrderTableData = await getTableData(queryParams());
};

watch(
  () => salesOrderManagementStore.total,
  () => {
    pagination.total = salesOrderManagementStore.total;
  },
  {
    immediate: true
  }
);

/** 创建销售订单 */
const handleCreateSalesOrder = () => {
  state.selectId = "";
  state.salesOrderDialogVisible = true;
};

/** 编辑销售订单 */
const onEditSalesOrder = async (row: ISalesOrderDetail) => {
  state.selectId = row.id;
  const salesOrderDetail: ISalesOrder = await getSalesOrderDetailById(state.selectId);
  setSalesOrderDetail(salesOrderDetail);
  state.salesOrderDialogVisible = true;
};

const handleCloseSalesOrderModal = () => {
  clearForm();
};
function clearForm() {
  clearSalesOrderDetail();
  state.salesOrderDialogVisible = false;
}

/**采购订单是否缺失 */
async function onQueryFaultPoNoSalesOrder() {
  state.params.isFaultPoNo = state.isFaultPoNo;
  state.salesOrderTableData = await getTableData(queryParams());
}

/**是否关注 */
async function onQueryFollowSalesOrder() {
  state.params.follow = state.follow;
  state.salesOrderTableData = await getTableData(queryParams());
}

/** 查询已归档数据 */
async function onQueryDocumentationSalesOrder() {
  state.params.documentation = state.documentation;
  state.salesOrderTableData = await getTableData(queryParams());
}

/**销售订单填报进度 */
async function onChangeLinkStepSearch() {
  state.params.linkStep = state.linkStepKey;
  pagination.currentPage = 1;
  selectedList.value = [];
  state.salesOrderTableData = await getTableData(queryParams());
}

const getStarStyle = (row: ISalesOrder) => {
  return row.follow ? "color: #E6A23C" : "color: #DEDFE0";
};

/** 关注 */
async function onToggleFollow(row: ISalesOrder) {
  if (row.follow) {
    if (!(await useConfirm("是否确认取消关注该销售订单", "取消关注"))) {
      return;
    }
  }
  await salesOrderFollow(row.id);
  await salesOrderManagementStore.querySalesOrderStatistic(queryParams());
  ElMessage.success(row.follow ? "取消关注订单成功" : "关注订单成功");
  row.follow = !row.follow;
}

const getCategoryNameByCode = (categoryCode: string) => {
  if (!categoryCode) {
    return emptyDefaultValue;
  }
  const categories = categoryStore.categories.find(categories => categories.categoryCode == categoryCode);
  return categories ? categories.categoryName : emptyDefaultValue;
};

/**
 * @description: 计算导航弹窗列表所需数据
 */
function calcNavDialogList(noListStr = "", idList: Array<string> = []) {
  const arr = noListStr.split(",");
  return arr.map((no, index) => {
    return {
      no,
      id: idList[index]
    };
  });
}

function handleSelect(selection) {
  selectedList.value = selection.map(item => item.id);
}

function handleSelectAll(selection) {
  selectedList.value = selection.map(item => item.id);
}

function postClear() {
  pureTableRef.value?.getTableRef().clearSelection();
  selectedList.value = [];
}

emitter.on("refreshSalesOrderList", async () => {
  postClear();
  state.salesOrderTableData = await getTableData(queryParams());
});

onUnmounted(() => {
  emitter.off("refreshSalesOrderList");
});
</script>
<style lang="scss" scoped>
.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}

.is-batching {
  :deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }
}
</style>
