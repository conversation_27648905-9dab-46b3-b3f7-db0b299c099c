import { defineStore } from "pinia";
import { IDeviceDataAcquisitionReq } from "@/models/device";
import * as api from "@/api";
import { queryDeviceAcquisitionAll } from "@/api/device/device-acquistion";
import { useDeviceStore } from "./device";

/** 数据采集 */
export const useDataAcauisitionStore = defineStore({
  id: "cx-datacauisition-store",
  state: () => ({
    dataAcauisitionList: []
    // dataAcauisitionPointDetailList: [] as IDeviceAcquisition[]
  }),
  actions: {
    /**
     * @description: 历史数据采集
     */

    async deviceHistoryDataAcquisition(params: IDeviceDataAcquisitionReq, signal: AbortSignal) {
      const res = await api.deviceHistoryDataAcquisition(params, signal);
      return res.data.items;
    },

    /**
     * @description: 查询设备码点
     */
    async queryDeviceCodePoints() {
      const deviceStore = useDeviceStore();
      return (await queryDeviceAcquisitionAll(deviceStore.deviceDetail.id)).data;
    },

    /**
     * @description: 实时数据采集
     */

    async deviceRealTimeDataAcquisition(params: IDeviceDataAcquisitionReq, signal: AbortSignal) {
      const res = await api.deviceRealTimeDataAcquisition(params, signal);
      return res.data.items;
    },

    /**
     * @description: 获取监控直播url
     */
    async getMonitorStreamUrlById(id: string) {
      return await api.getMonitorDeviceWatchUrlById(id);
    }
  }
});
