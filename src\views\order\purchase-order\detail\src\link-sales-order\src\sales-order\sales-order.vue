<template>
  <SalesOrderSwitch class="mt-[22px]" />
  <Card margin="12px 0" :fontWeight="700" :title="title" class="h-full" v-if="salesOrder">
    <template #action>
      <div class="flex-bc flex-1 ml-2">
        <el-button
          v-auth="PermissionKey.form.formPurchaseSalesEdit"
          v-track="TrackPointKey.FORM_PURCHASE_SALES_EDIT"
          size="small"
          @click="openEditDialog"
        >
          <template #icon>
            <EditPen />
          </template>
          编辑
        </el-button>
        <EnumTag :value="salesOrder.orderProgress" :enum="SalesOrderStatus" enumName="SalesOrderStatus" />
      </div>
    </template>

    <el-row>
      <el-col :span="12">
        <label class="label">物资品类</label>
        <span class="value">{{ salesOrder?.categoryName }}</span>
      </el-col>
      <el-col :span="12">
        <label class="label">项目名称</label>
        <ShowTooltip className="max-w-[20em] text-base" :content="salesOrder?.prjName" />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <label class="label">采购方公司名称</label>
        <span class="value">
          <CxTag type="custom" icon="icon-company-fill">{{ salesOrder?.buyerName || "--" }}</CxTag>
        </span>
      </el-col>
      <el-col :span="12">
        <label class="label">合同编号</label>
        <span class="value">{{ salesOrder?.conCode }}</span>
      </el-col>
    </el-row>
    <ExtraInfo :data="salesOrder" :fields="extraFields" :collapse="collapsed" />
  </Card>
</template>

<script setup lang="ts">
import Card from "../card.vue";
import ShowTooltip from "@/components/ShowTooltip";
import { computed, inject, onMounted } from "vue";
import SalesOrderSwitch from "./sales-order-switch.vue";
import { salesOrderDialogKey } from "@/views/order/purchase-order/detail/src/link-sales-order/token";
import EnumTag from "@/components/EnumTag/EnumTag.vue";
import CxTag from "@/components/CxTag/index.vue";
import { SalesOrderStatus } from "@/enums";
import { EditPen } from "@element-plus/icons-vue";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail";
import { formatDate } from "@/utils/format";
import { PermissionKey, TrackPointKey, emptyDefaultValue } from "@/consts";
import ExtraInfo from "@/views/order/purchase-order/detail/src/link-sales-order/src/extra-info.vue";
import { useSynchronousFlagStore } from "@/store/modules";

const props = defineProps<{
  collapsed: boolean;
}>();

const extraFields = [
  { title: "标识", key: "matSyncFlagId" },
  { title: "合同签订日期", key: "sellerSignTime" },
  { title: "交货日期", key: "deliveryDate" },
  { title: "备注", key: "remark", showAll: true }
];

const store = usePurchaseOrderDetailSalesOrderStore();
const title = computed(() => `销售订单号：${store.activeOrder?.soNo ?? ""}`);
const synchronousFlagStore = useSynchronousFlagStore();

const salesOrder = computed(() => {
  const order = store.activeOrder;
  if (!order) {
    return null;
  }
  const matSyncFlagNameList = getMatSyncFlagNameList(order.matSyncFlagId);
  return {
    ...order,
    sellerSignTime: formatDate(order.sellerSignTime),
    deliveryDate: formatDate(order.deliveryDate),
    matSyncFlagId: matSyncFlagNameList.length ? matSyncFlagNameList.join(",") : emptyDefaultValue
  };
});
const collapsed = computed(() => props.collapsed);

const dialog = inject(salesOrderDialogKey);

function openEditDialog() {
  dialog.title = "编辑销售订单";
  dialog.visible = true;
  dialog.id = salesOrder.value.id;
  dialog.type = "update";
}

onMounted(() => initializeFlag());

async function initializeFlag() {
  await synchronousFlagStore.querySynchronousFlag();
}

function getMatSyncFlagNameList(matSyncFlagId) {
  const synchronousFlag: Array<string> = matSyncFlagId.split(",").map(id => {
    const synchronousFlag = synchronousFlagStore.synchronousFlag.find(synchronousFlag => synchronousFlag.id == id);
    if (synchronousFlag) {
      return synchronousFlag.flagName;
    }
  });
  return synchronousFlag;
}

onMounted(store.refreshSalesOrders);
</script>

<style lang="scss" scoped>
@import "../../../../styles/mixin";
@import "@/style/mixin";
@include card-label-value;

:deep(.show-all) {
  @include scrollBar;
  text-align: justify;
  height: 180px;
  overflow: auto;
  overflow: overlay;
}
</style>
