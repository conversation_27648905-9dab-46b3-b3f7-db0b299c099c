import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TableColumnCtx } from "element-plus";
import { h } from "vue";
import { IFile } from "@/models";
import { downLoadFile } from "@/api/upload-file";
import { previewUploadFile } from "@/utils/uploadFiles";
import { downloadByData } from "@pureadmin/utils";

export function useFileFormatter() {
  const previewFile = fileData => {
    const { id, name, url } = fileData;
    if (id) {
      previewUploadFile({ id, name, url });
    }
  };

  async function downloadFile(id: string, name: string) {
    const blob = await downLoadFile(id);
    downloadByData(blob, name, blob.type);
  }
  return (row: any, column: TableColumnCtx<any>, files: Array<IFile>) => {
    if (!Array.isArray(files) || !files.length) {
      return null;
    }
    const children = files.map(file => {
      return (
        <ElPopover width={60} placement="left" trigger="hover">
          {{
            default: () => (
              <div class="flex flex-col items-center">
                <ElButton type="primary" link onClick={() => previewFile(file)}>
                  预览
                </ElButton>
                <ElDivider class="!my-3" />
                <ElButton type="primary" link onClick={() => downloadFile(file.id, file.name)}>
                  下载
                </ElButton>
              </div>
            ),
            reference: () => (
              <ElButton type="primary" link>
                <span class="name">{file.name}</span>
              </ElButton>
            )
          }}
        </ElPopover>
      );
    });
    return h("div", { class: "flex flex-col items-center" }, children);
  };
}
