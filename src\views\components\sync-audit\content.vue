<template>
  <el-scrollbar @scroll="handleScroll" ref="scrollBar">
    <div class="px-1">
      <BaseInfo class="mb-5" />
      <BusinessLists v-if="hasBusinessItem" />
      <IotLists v-if="hasIotItem" />
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { computed, inject, ref } from "vue";
import { ElScrollbar, ScrollbarInstance } from "element-plus";
import BaseInfo from "./base-info.vue";
import BusinessLists from "./business-lists/business-lists.vue";
import IotLists from "./iot-lists/iot-lists.vue";
import { useScroll } from "./hooks/useScroll";
import { syncAuditStateKey } from "./tokens";

const ctx = inject(syncAuditStateKey);

const scrollBar = ref<ScrollbarInstance>();
const { handleScroll } = useScroll(scrollBar);

const hasBusinessItem = computed(() => ctx.hasBusinessItem);
const hasIotItem = computed(() => ctx.hasIotItem);
</script>

<style scoped>
:deep(.active-card) {
  animation-name: shakeX;
  animation-duration: 0.6s;
  color: var(--el-color-primary);
}
@keyframes shakeX {
  0%,
  100% {
    transform: translate3d(0, 0, 0);
  }

  20%,
  60% {
    transform: translate3d(-0.625rem, 0, 0);
  }

  40%,
  80% {
    transform: translate3d(0.625rem, 0, 0);
  }
}
</style>
