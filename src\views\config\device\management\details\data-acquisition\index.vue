<!-- 生产设备—数据采集组件 -->
<template>
  <div class="h-full flex flex-col min-h-0">
    <div class="flex my-4">
      <el-radio-group v-model="dataType">
        <el-radio border :label="DataTypeEnum.REALTIME">实时数据</el-radio>
        <el-radio border :label="DataTypeEnum.HISTORY">历史数据</el-radio>
      </el-radio-group>
      <el-date-picker
        class="!flex-grow-0"
        v-if="dataType === DataTypeEnum.HISTORY"
        v-model="timeRange"
        type="datetimerange"
        range-separator="～"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :disabled-date="calcDisabledDate"
      />
      <el-button
        v-show="dataType === DataTypeEnum.HISTORY"
        type="primary"
        class="ml-2"
        @click="requestHistoryAcquisition"
        :disabled="disabledQuery"
        >查询</el-button
      >
    </div>
    <div class="flex flex-1 min-h-0">
      <histroy-acquisition
        class="flex-1"
        v-show="dataType === DataTypeEnum.HISTORY"
        ref="historyAcquisitionRef"
        :device-id="deviceStore.deviceDetail.id"
      />
      <realtime-acquisition class="flex-1" v-show="dataType === DataTypeEnum.REALTIME" />
      <realtime-live :device-id="deviceStore.deviceDetail.id" class="w-[400px] pl-3" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useDeviceStore } from "@/store/modules";
import { IDeviceDataAcquisitionReq } from "@/models/device";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import RealtimeAcquisition from "./realtime-acquisition/index.vue";
import HistroyAcquisition from "./history-acquisiton/index.vue";
import RealtimeLive from "./realtime-live/index.vue";

/** 数据类型 */
enum DataTypeEnum {
  HISTORY = "HISTORY",
  REALTIME = "REALTIME"
}
type HistroyAcquisitionType = InstanceType<typeof HistroyAcquisition>;

const route = useRoute();

const historyAcquisitionRef = ref<HistroyAcquisitionType | null>(null);

const dataType = ref<DataTypeEnum>(initDataType());
const timeRange = ref<[Date, Date]>(initTimeRange());
const disabledQuery = computed(() => {
  if (dataType.value === DataTypeEnum.HISTORY) {
    return !timeRange.value;
  }
  return false;
});
const deviceStore = useDeviceStore();

function initDataType() {
  if (isDataTypeEnum(route.query.subTab)) {
    return route.query.subTab as DataTypeEnum;
  } else {
    return DataTypeEnum.REALTIME;
  }
}

function initTimeRange(): [Date, Date] {
  const { workStartTime, workEndTime } = route.query;
  if (workStartTime && workEndTime) {
    return [dayjs(workStartTime as string).toDate(), dayjs(workEndTime as string).toDate()];
  } else {
    return [dayjs().subtract(2, "day").toDate(), dayjs().toDate()];
  }
}

function isDataTypeEnum(value: any): boolean {
  for (const key in DataTypeEnum) {
    if (DataTypeEnum[key] === value) {
      return true;
    }
  }
  return false;
}

/**
 * @description: 获取历史数据
 */
const requestHistoryAcquisition = async () => {
  const params: IDeviceDataAcquisitionReq = {
    deviceCode: deviceStore.deviceDetail.deviceCode
  };
  if (!validTimeRange()) {
    ElMessage({
      message: "仅支持查询时间跨度为48小时以内的数据",
      type: "warning"
    });
    return;
  }
  params.timeFrom = timeRange.value[0].toISOString();
  params.timeEnd = timeRange.value[1].toISOString();
  if (historyAcquisitionRef.value) {
    historyAcquisitionRef.value.combinationHistoryList(params);
  }
};

/**
 * @description: 计算el-date-picker禁用的日期(禁止选择未来的时间)
 */
const calcDisabledDate = (date: Date) => {
  return date.valueOf() > Date.now();
};

/**
 * @description: 验证请求的时间范围是否正确
 */
const validTimeRange = () => {
  const startTime = dayjs(timeRange.value[1], "YYYY-MM-DD HH:mm:ss");
  const endTime = dayjs(timeRange.value[0], "YYYY-MM-DD HH:mm:ss");
  return !(dayjs(startTime).diff(endTime, "hour", true) > 48);
};

onMounted(() => {
  if (dataType.value === DataTypeEnum.HISTORY) {
    requestHistoryAcquisition();
  }
});
</script>

<style scoped lang="scss"></style>
