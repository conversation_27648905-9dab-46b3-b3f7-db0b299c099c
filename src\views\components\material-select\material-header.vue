<template>
  <div class="flex-bc mb-4">
    <div>
      <el-input
        class="!w-96"
        placeholder="请输入物料编号/名称/规格型号"
        v-model="ctx.keyword"
        clearable
        :validate-event="false"
        @clear="ctx.refresh"
        @keydown.enter="ctx.refresh"
      />
      <el-button class="ml-2" type="primary" @click="ctx.refresh">查询</el-button>
    </div>

    <el-button @click="visible = true" :icon="Plus">新增物料</el-button>
    <el-dialog
      title="新增物料"
      v-model="visible"
      align-center
      class="middle"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open="dialogOpen"
    >
      <MaterialForm ref="form" />
      <template #footer>
        <el-button class="ml-2" @click="visible = false">取消</el-button>
        <el-button class="ml-2" type="primary" @click="handleCreateAndSelect" :loading="loading">确认新增</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import { materialSelectKey } from "./token";
import MaterialForm from "@/views/components/material-form";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IMaterialDto } from "@/models";
import { createMaterial } from "./tools";
import { Plus } from "@element-plus/icons-vue";

const props = defineProps<{
  subclassCode: string;
}>();

const emit = defineEmits<{
  (e: "afterCreate"): void;
}>();

const ctx = inject(materialSelectKey);
const visible = ref(false);
const form = ref<InstanceType<typeof MaterialForm>>();
const loading = ref(false);
const handleCreateAndSelect = useLoadingFn(createAndSelect, loading);

function dialogOpen() {
  form.value.initializeForm({ subClassCode: props.subclassCode }, ["subClassCode"]);
}

async function createAndSelect() {
  const materialDto: IMaterialDto | false = await form.value.getValidValue().catch(() => false);
  if (!materialDto) {
    return;
  }
  ctx.selectedMaterial = await createMaterial(materialDto);
  ctx.selectedId = ctx.selectedMaterial.id;
  visible.value = false;
  emit("afterCreate");
}
</script>

<style scoped></style>
