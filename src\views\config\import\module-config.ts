import { PermissionKey } from "@/consts/permission-key";
import { hasAuth } from "@/router/utils";
import { computed } from "vue";

export enum TabEnum {
  /** 业务数据 */
  BusinessData = "business-data",
  /** 试验数据 */
  ExperimentData = "experiment-data"
}

export const tabConfig = computed(() => {
  const tabList = [
    {
      moduleCode: TabEnum.BusinessData,
      moduleName: "业务数据",
      auth: ""
    },
    {
      moduleCode: TabEnum.ExperimentData,
      moduleName: "试验数据",
      auth: PermissionKey.meta.metaImportExperiment
    }
  ];
  return tabList.filter(({ auth }) => {
    if (!auth) {
      return true;
    }
    return hasAuth(auth);
  });
});
