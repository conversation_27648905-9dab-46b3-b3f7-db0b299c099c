import { InjectionKey } from "vue";
import { IPagingReq } from "../i-paging-req";

export interface IEventsTokenInject {
  delExperiment: Function;
  detailOfExperiment: Function;
  uploadReport: Function;
  deleteReport: Function;
  linkPurchaseOrder: Function;
  reloadList: () => void;
}

export const OutGoingExperimentEvent: InjectionKey<IEventsTokenInject> = Symbol("OutGoingExperimentEventsToken");
/** 搜索带关联的试验列表 */
export interface ISearchJfnyListReq extends IPagingReq {
  keyWords?: string;
  /** 试验开始时间 */
  experimentDataFrom?: string;
  /** 试验结束时间 */
  experimentDataTo?: string;
  /** 是否关联 */
  linked?: boolean;
  /** 车间 */
  workshopId?: string;
}

export enum ELinkStatusType {
  All = 1, // 全部
  Linked = 2, // 已关联
  UnLink = 3 // 未关联
}
// 关联状态
export interface ILinkStatus {
  title: string;
  key: string;
  value: number;
  count: number;
}

export interface ILinkStatusCount {
  linked: number;
  unLinkd: number;
  total: number;
}

export interface IProductionNo {
  id: string;
  ipoNo?: string;
  materialsName?: string;
  startTime?: string;
  endTime?: string;
  createStatus?: boolean;
}
/** 局放耐压试验列表 */
export interface IJfnyExperimentList {
  id?: string;
  /** 数量 */
  account: number;
  /** 实验编号 */
  experimentNo: string;
  /** 成品编号 */
  finProNo: string;
  /** 电压等级 */
  finishedVoltageLevel: string;
  /** 电压等级类型 */
  finishedVoltageLevelType: string;
  /** 试验状态 */
  experimentStatus: string;
  /** 实验结果 */
  experimentResult: string;
  startTime: string;
  endTime: string;
  /** 上传文件信息 */
  reportFileId: string;
  reportFileUrl: string;
  reportFileName: string;
  /** 产品型号 */
  productModel: string;
  /** 产品规格 */
  specificationModel: string;
  /** 完整的生产订单信息 */
  mappedProductionInfo: IProductionNo[];
  /** 数据未完整传如SDCC的订单 */
  noMappedProductionNo: IProductionNo[];
  /** 实验组ID */
  groupId: string;
  /** 线芯名称 */
  wireName: string;
  /** 试验长度单位 */
  measUnit: string;
  /** 试验盘号 */
  reelNo: string;
  /** 局放设置值 */
  pdSetting: string;
  /** 局放标定值 */
  pdScaling: string;
  /** 背景干扰值 */
  backGroundInterferenceValue: string;
  /** 局部放电量 */
  partialDischargeAmount: string;
  /** 局部放大倍数 */
  pdMagnification: string;
  /** 车间名称 */
  workshopName: string;

  /** 生产订单号  */
  ipoNos?: string;
}

/** 生产订单试列表 */
export interface ISearchProductionOrderReq extends IPagingReq {
  voltageType?: string;
  voltageClasses?: string;
  keyword?: string;
  experimentId?: string;
  groupId?: string;
}

/** 生产订单试列表 */
export interface IProductionOrderList {
  id?: string;
  /** 销售订单号  */
  soNo: string;
  /** 销售订单行项目号  */
  soItemNo: string;
  /** 生产订单号  */
  ipoNo: string;
  /** 厂家物料名称  */
  materialsName: string;
  /** 生产数量  */
  amount: number;
  /** 计量单位  */
  unit: string;
  /** 规格型号  */
  specificationModel: string;
  /** 应用类型  */
  applicationType: string;
  /** 接头类型  */
  jointCategories: string;
  /** 电压类型  */
  voltageType: string;
  /** 电压等级  */
  voltageClasses: string;
  /** 合并单元格 */
  rowSpan?: number;
  colSpan?: number;
}

export interface DuplicationJfnyExperimentParams {
  /** 试验组id */
  groupId: string;
  /** 生产订单编号 */
  productionOrderNo: string;
  /** 试验盘号 */
  reelNo: string;
  /** 电缆长度 */
  account: number;
  /** 产品型号 */
  productModel: string;
  /** 规格型号 */
  specificationModel: string;
}

export interface IEditJfnyExperimentForm {
  ipoNos?: Array<string>;
  reelNos?: Array<string>;
}
