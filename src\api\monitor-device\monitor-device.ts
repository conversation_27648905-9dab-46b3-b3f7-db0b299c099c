import {
  IListResponse,
  IMonitorDevice,
  IMonitorDeviceForm,
  IMonitorDeviceRefreshTime,
  IMonitorDeviceReq,
  IResponse
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 查询监控设备列表数据 */
export const queryMonitorDevices = (data?: IMonitorDeviceReq) => {
  const url: string = withApiGateway("admin-api/business/monitorDevice/getList");
  return http.post<IMonitorDeviceReq, IListResponse<IMonitorDevice>>(url, { data });
};

/** 查询监控设备明细 */
export const getMonitorDeviceDetailById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/getDetail/${id}`);
  return http.get<string, IResponse<IMonitorDevice>>(url);
};

/** 创建监控设备 */
export const createMonitorDevice = (data: IMonitorDeviceForm) => {
  const url: string = withApiGateway("admin-api/business/monitorDevice/create");
  return http.post<IMonitorDeviceForm, IResponse<boolean>>(url, { data });
};

/** 编辑监控设备 */
export const editMonitorDevice = (data: IMonitorDeviceForm) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/edit/${data.id}`);
  return http.post<IMonitorDeviceForm, IResponse<boolean>>(url, { data });
};

/** 删除监控设备数据 */
export const deleteMonitorDevice = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/delete/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/** 查看直播 */
export const getMonitorDeviceWatchUrlById = (id: string, signal?: AbortSignal) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/getWatchUrl/${id}`);
  return http.get<string, IResponse<string>>(url, {
    signal,
    timeout: 0
  });
};

/**
 * @description: 同步监控设备到IOT, 返回结果为true仅表示同步完成，不表示同步是否成功
 */
export const syncMonitorDeviceByID = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/syncMonitor/${id}`);
  return http.post<string, IResponse<string>>(url);
};

export const checkAllMonitorDeviceRunStatus = () => {
  const url: string = withApiGateway("admin-api/business/monitorDevice/refreshRunningStatus");
  return http.post<void, IResponse<boolean>>(url);
};

export const getMonitorDeviceLastRefreshTime = () => {
  return http.get<void, IResponse<IMonitorDeviceRefreshTime>>(
    withApiGateway("admin-api/business/monitorDevice/getLastRefreshTime")
  );
};
