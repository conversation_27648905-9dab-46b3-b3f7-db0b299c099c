<template>
  <CXBatchAction :count="count" @cancel="batchCancel">
    <div v-for="(item, index) in batchButton" :key="index" class="flex flex-wrap ml-4">
      <CXBatchButton :button="item" />
    </div>
  </CXBatchAction>
</template>

<script setup lang="ts">
import { StateGridOrderSyncType } from "@/enums/state-grid-order";
import { IPurchaseOrderLineRes } from "@/models/purchase-order";
import { usePurchaseOrderLineStore } from "@/store/modules/purchase-order";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { computed, ref } from "vue";
import { PurchaseOrderLineDetailEnum } from "@/enums/purchase-order";
import { ElNotification } from "element-plus";
import CXBatchAction from "@/components/cx-batch-action";
import CXBatchButton from "@/components/cx-batch-button";
import { PermissionKey } from "@/consts";

const props = withDefaults(
  defineProps<{
    selectedData: Array<IPurchaseOrderLineRes>;
  }>(),
  {
    selectedData: () => []
  }
);

const emit = defineEmits<{
  (e: "batchSyncSuccess"): void;
  (e: "batchTriggerSuccess"): void;
  (e: "batchSyncCancel"): void;
}>();

const count = computed(() => {
  return props.selectedData.length;
});
const lineStore = usePurchaseOrderLineStore();
// 批量同步
const syncLoading = ref<boolean>(false);
const batchSync = useLoadingFn(postBatchSync, syncLoading);
// 批量触发评分
const triggerLoading = ref<boolean>(false);
const batchTrigger = useLoadingFn(postBatchTrigger, triggerLoading);

const errorType = ref();

/** 批量触发同步 */
async function postBatchSync() {
  if (!props.selectedData?.length) {
    return;
  }
  errorType.value = PurchaseOrderLineDetailEnum.DATA_SYNC;
  if (!(await useConfirm(`已选中 ${props.selectedData?.length} 条数据，是否批量执行同步?`, "批量同步"))) {
    return;
  }
  const purchaseList = props.selectedData.map(item => {
    const { purchaseId, id } = item;
    return { purchaseOrderId: purchaseId, purchaseOrderItemId: id };
  });
  const params = {
    dataType: StateGridOrderSyncType.ALL,
    purchaseList
  };
  emit("batchSyncCancel");
  showNotificationOfSync();
  await lineStore.batchSyncOrderLine(params);
  emit("batchSyncSuccess");
}

/** 批量触发评分 */
async function postBatchTrigger() {
  if (!props.selectedData?.length) {
    return;
  }
  errorType.value = PurchaseOrderLineDetailEnum.TRIGGER_SCORE;
  if (!(await useConfirm(`已选中 ${props.selectedData?.length} 条数据，是否批量执行触发评分?`, "批量触发"))) {
    return;
  }
  const lineList = props.selectedData.map(item => {
    const { purchaseId, id } = item;
    return {
      purchaseId,
      purchaseLineId: id
    };
  });
  emit("batchSyncCancel");
  showNotificationOfTrigger();
  await lineStore.batchTriggerOrderLine(lineList);
  emit("batchTriggerSuccess");
}

function showNotificationOfSync() {
  ElNotification.info({
    title: "批量同步",
    message: `正在批量同步采购订单行下的数据`,
    duration: 3000
  });
}

function showNotificationOfTrigger() {
  ElNotification.info({
    title: "批量触发评分",
    message: `正在批量触发采购订单行下生产数据的质量评分`,
    duration: 3000
  });
}

function batchCancel() {
  emit("batchSyncCancel");
}
const batchButton = computed(() => [
  {
    name: "批量数据同步",
    action: batchSync,
    disabled: !props.selectedData.length,
    permission: PermissionKey.form.formPurchaseLineSync
  },
  {
    name: "批量触发评分",
    action: batchTrigger,
    disabled: !props.selectedData.length,
    permission: PermissionKey.form.formPurchaseLineTriggerScore
  }
]);
</script>

<style scoped lang="scss"></style>
