import { TableWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "销售订单号",
      prop: "soNo",
      width: TableWidth.order
    },
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      width: TableWidth.order
    },
    {
      label: "物料编码",
      prop: "materialCode",
      width: TableWidth.order
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料描述",
      prop: "materialDesc",
      minWidth: TableWidth.largeName
    },
    {
      label: "物料单位",
      prop: "unitDictionary.name",
      width: TableWidth.unit
    }
  ];
  return { columns };
}
