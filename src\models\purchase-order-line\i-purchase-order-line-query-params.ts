import { LinkStepEnum, OrderSyncPriorityEnum } from "@/enums";
import { IPagingReq, ISortReq } from "../i-paging-req";

export interface IPurchaseOrderLineQueryParams extends IPagingReq, ISortReq {
  /** 采购订单号 */
  poNo?: string;

  /** 采购方公司名称  */
  buyerName?: string;

  /** 合同签订日期 */
  sellerSignTime?: Array<Date | string>;

  /** 物资种类 */
  subclassCode?: string;

  /** 同步状态  */
  syncType?: boolean;

  /** 触发评分结果 */

  triggerType?: boolean;

  /** 	项目名称  */
  prjName?: string;

  /** 	合同名称,示例值(XXX采购合同)  */
  conName?: string;

  /** 当前填报环节,示例值(1)  */
  linkStep?: LinkStepEnum;

  /** 组合查询参数 */

  orFilters?: Array<Record<string, string>>;

  /** 优先级 */
  priority?: OrderSyncPriorityEnum;
}
