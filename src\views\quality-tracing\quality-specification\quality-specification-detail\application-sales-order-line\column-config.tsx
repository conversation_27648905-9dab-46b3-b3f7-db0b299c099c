import { ColumnWidth } from "@/enums";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig: TableColumnList = [
    {
      label: "销售订单行项目号",
      prop: "salesLineNo",
      fixed: "left",
      minWidth: ColumnWidth.Char12
    },
    {
      label: "销售订单号",
      prop: "soNo",
      minWidth: ColumnWidth.Char17
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "物料单位",
      prop: "materialUnitName",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "采购订单行项目号",
      prop: "poItemNos",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char4,
      fixed: "right",
      slot: "opertion"
    }
  ];

  return { columnsConfig };
}
