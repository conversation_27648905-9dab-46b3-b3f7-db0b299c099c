import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models";
import {
  addRawMaterialCheck,
  getRawMaterialCheckInfoByProductStePro,
  getRawMaterilKinds
} from "../base-config/raw-material";
import {
  ICopyFromOrder,
  IRawMaterialCheckCollectionItem,
  IRawMaterialReq,
  ISearchRawList
} from "@/models/raw-material/i-raw-material-res";
import {
  IChooseSaveRawMaterialInspect,
  IProductionMaterialProcess,
  IRawMaterialGroupUnitInspect,
  IRawMaterialInspectList,
  IResMaterialProcessList,
  ISaveRawMaterialInspectReq,
  ISearchMaterialList
} from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import { ISearchRawMaterialInspecReq } from "@/models/raw-material/i-raw-material-res-v2";

/********* 原材料，组部件的接口 *************/
/**
 * 获取原材料，组部件检测的工序 状态
 */
export function getRawMaterialGroupUnitProcess(productionId: string) {
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/process/status/${productionId}`);
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(url, {}, { duplicateKey: "processStatusKey" });
}

/**
 * 获取非线缆原材料，组部件检测的工序 状态
 */
export function getNonCableRawMaterialGroupUnitProcess(workOrderId: string) {
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/process/status/non-cable/${workOrderId}`);
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(url, {}, { duplicateKey: "processStatusKey" });
}

/**
 * 获取原材料，组部件检测的列表
 */
export function getRawMaterialGroupUnitList(queryParams?: ISearchMaterialList) {
  // v2
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/page`);
  return http.post<ISearchMaterialList, IListResponse<IResMaterialProcessList>>(
    url,
    {
      data: queryParams
    },
    {
      duplicateKey: "productionMaterialProcessList"
    }
  );
}

/**
 * 获取非线缆原材料，组部件检测的列表
 */
export function getNonCableRawMaterialGroupUnitList(queryParams?: ISearchMaterialList) {
  // v2
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/page/non-cable`);
  return http.post<ISearchMaterialList, IListResponse<IResMaterialProcessList>>(
    url,
    { data: queryParams },
    { duplicateKey: "productionMaterialProcessList" }
  );
}

/**
 * 获取原材料，组部件检测的列表页码改变
 */
export function queryRawMaterialGroupUnitByPageSize(queryParams: ISearchMaterialList) {
  this.getRawMaterialGroupUnitList(queryParams);
}
/**
 * 获取原材料，组部件检测的列表页码改变
 */
export function queryRawMaterialGroupUnitByPage(queryParams: ISearchMaterialList) {
  this.getRawMaterialGroupUnitList(queryParams);
}

/**
 * 获取原材料检测的详情数据
 */
export function getDetailRawMatrialByMaterialId(materialId: string) {
  // const url = withApiGateway(`admin-api/business/productionMaterialProcess/detail/${materialId}`);
  // v2
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/detail/${materialId}`);
  return http.get<string, IResponse<IRawMaterialGroupUnitInspect>>(url);
}

/**
 * 删除原材料检测的数据
 */
export function delRawMatrialGroupUnitList(materialId: string) {
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/delete/${materialId}`);
  return http.delete<string, IResponse<number>>(url);
}

/********* 原材料检测的接口 *************/
/**
 * 选择原材料（获取原材料检列表）
 */
export function getSelectRawMaterialList(queryParams?: ISearchRawMaterialInspecReq) {
  // return getRawMaterialList(queryParams);
  // v2
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/raw-material-inspect/page`);
  return http.post<ISearchRawList, IListResponse<IRawMaterialInspectList>>(url, { data: queryParams });
}

/**
 * 保存选择的原材料检信息(批量保存原材料，组部件检测)
 */
export function saveSelectRawMaterial(paramData: IChooseSaveRawMaterialInspect) {
  // const url = withApiGateway(`admin-api/business/productionMaterialProcess/save`);
  // v2
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/choose-save`);
  return http.post<IChooseSaveRawMaterialInspect, IResponse<boolean>>(
    url,
    {
      data: paramData
    },
    {}
  );
}

/** ============== 原材料优化V2 新增 ============== */
export function saveAddRawMaterialInspectInfo(params: ISaveRawMaterialInspectReq) {
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/create-save`);
  return http.post<ISaveRawMaterialInspectReq, IResponse<boolean>>(url, { data: params });
}

/********* 新增原材料的接口 *************/
/**
 * 获取原材料类型
 */
export function getAddRawMaterilKinds() {
  return getRawMaterilKinds();
}

/**
 * 根据原材料类型获取采集项信息
 */
export function getAddRawMaterialCheckInfoByProcessCode(processCode: string, specificationModel: string) {
  return getRawMaterialCheckInfoByProductStePro(processCode, specificationModel);
}

/**
 * 根据原材料类型Id获取采集项信息
 */
export function getAddRawMaterialCheckInfoByProcessId(processId: string, specificationModel: string) {
  const url = withApiGateway(
    `admin-api/system/metadataModelInfo/findCategoryByProcessIdAndSpecificationModel/${processId}`
  );
  return http.get<string, IResponse<Array<IRawMaterialCheckCollectionItem>>>(url, {
    params: {
      specificationModel
    }
  });
}

/**
 * 新增原材料检测数据
 */
export function addSelectRawMaterialCheck(paramData: IRawMaterialReq) {
  return addRawMaterialCheck(paramData);
}

/**
 * 更新原材料，组部件信息
 */
export function updatRawMaterialGroupUnit(paramData: IRawMaterialReq) {
  // const url: string = withApiGateway(`admin-api/business/productionMaterialProcess/update`);
  const url: string = withApiGateway(`admin-api/business/productionMaterialProcess/v2/update`);
  return http.put<IRawMaterialReq, IResponse<Boolean>>(url, { data: paramData }, { showErrorInDialog: true });
}

/** 从生成订单中复制原材料检 */
export function productionMaterialProcessCopy(paramData: ICopyFromOrder) {
  const url: string = withApiGateway(`admin-api/business/productionMaterialProcess/v2/copy`);
  return http.post<ICopyFromOrder, IResponse<Boolean>>(url, { data: paramData });
}
