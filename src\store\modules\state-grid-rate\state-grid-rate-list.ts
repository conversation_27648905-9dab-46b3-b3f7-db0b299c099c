import { defineStore } from "pinia";
import { IStateGridRate } from "@/models";
import * as api from "@/api/state-grid-rate";

type StateGridRateListType = {
  rates: Array<IStateGridRate>;
  purchaseOrderId: string;
};

export const useStateGridRateListStore = defineStore({
  id: "cx-state-grid-rate-list",
  state: (): StateGridRateListType => ({
    rates: [],
    purchaseOrderId: undefined
  }),
  actions: {
    setPurchaseOrderId(id: string) {
      this.purchaseOrderId = id;
    },
    async refreshStateGridRateList() {
      this.rates = (await api.getStateGridRateList(this.purchaseOrderId)).data;
    }
  }
});
