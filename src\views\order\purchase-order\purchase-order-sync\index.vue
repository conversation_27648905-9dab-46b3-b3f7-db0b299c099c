<template>
  <el-dialog title="拉取采购订单" v-model="visible" destroy-on-close class="default" :close-on-press-escape="false">
    <div class="w-[600px] flex-bc m-auto flex-col gap-[16px] px-4">
      <p class="text-middle text-secondary">
        今日已拉取 <span class="text-primaryText">{{ store.result.todayCount }}</span> 次
      </p>
      <WaveProgress class="mb-5" :percentage="percentage" :status="progressStatus" />
      <StepScale />
      <el-progress
        class="w-full"
        :text-inside="true"
        :stroke-width="14"
        :percentage="percentage"
        :status="progressStatus"
      >
        <span />
      </el-progress>
      <p class="text-lg">{{ tips }}</p>
    </div>

    <template #footer>
      <div class="text-center my-4">
        <template v-if="fail">
          <el-button size="large" @click="visible = false">关闭</el-button>
          <el-button size="large" type="primary" @click="reSync">重新拉取</el-button>
        </template>
        <el-button size="large" type="primary" v-if="running" @click="visible = false">后台进行</el-button>
        <el-button size="large" type="primary" v-if="success" @click="visible = false">完成</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import StepScale from "@/views/order/purchase-order/purchase-order-sync/step-scale.vue";
import { computed, watch } from "vue";
import { usePurchaseOrderSyncStore } from "@/store/modules";
import { PurchaseOrderSyncStatus } from "@/enums";
import { useNotification } from "@/views/order/purchase-order/purchase-order-sync/useNotification";
import WaveProgress from "@/views/order/purchase-order/purchase-order-sync/wave-progress/wave-progress.vue";
import { whenever } from "@vueuse/core";

const store = usePurchaseOrderSyncStore();

const visible = computed({
  get() {
    return store.dialogVisible;
  },
  set(value) {
    store.$patch({ dialogVisible: value });
  }
});
const running = computed(
  () =>
    store.sync.status === PurchaseOrderSyncStatus.RUNNING ||
    store.sync.status === PurchaseOrderSyncStatus.BACKGROUND_PULL
);
const success = computed(() => store.sync.status === PurchaseOrderSyncStatus.SUCCESS);
const fail = computed(() => store.sync.status === PurchaseOrderSyncStatus.FAIL);
const progressStatus = computed(() => (fail.value ? "exception" : ""));
const percentage = computed(() => store.sync.percentage);
const tips = computed(() => store.sync.tips);

const { openNotification, closeNotificationDelay, closeNotificationImmediate } = useNotification();

watch(
  () => [store.dialogVisible, store.sync.status],
  ([visible, status]) => {
    if (!visible && status === PurchaseOrderSyncStatus.RUNNING) {
      openNotification();
      watchCloseNotification();
    }
    if (visible) {
      closeNotificationImmediate();
    }
  }
);

function reSync() {
  store.reSyncPurchaseOrder();
}

function watchCloseNotification() {
  let unwatch = whenever(
    () => store.sync.status !== PurchaseOrderSyncStatus.RUNNING,
    () => {
      unwatch();
      unwatch = null;
      closeNotificationDelay();
    }
  );
}
</script>

<style scoped lang="scss">
:deep(.el-progress-bar__inner) {
  $color: rgba(255, 255, 255, 0.2);
  background: repeating-linear-gradient(-60deg, $color 0, $color 2px, transparent 2px, transparent 8px) no-repeat bottom
    var(--el-color-primary);
}
</style>

<style lang="scss">
.el-notification__sync-purchase-order {
  --el-notification-width: 400px;

  .el-notification__group {
    width: 100%;
  }
}
</style>
