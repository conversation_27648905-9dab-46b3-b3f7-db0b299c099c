import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 低压电缆
 */
export const lowVoltageCablesConfig: MaterialSubClassConfig = {
  name: "低压电缆",
  subClassCode: MaterialSubclassCode.LOW_VOLTAGE_CABLES,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 配网导、地线
 */
export const distributionWireGroundConfig: MaterialSubClassConfig = {
  name: "配网导、地线",
  subClassCode: MaterialSubclassCode.DISTRIBUTION_WIRE_GROUND,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 物资品类-配网线缆
 */
export const distributionCableConfig: MaterialCategoryConfig = {
  name: "配网线缆",
  categoryCode: MaterialCategoryCode.DISTRIBUTION_CABLE,
  subClassMap: {
    lowVoltageCablesConfig,
    [MaterialSubclassCode.LOW_VOLTAGE_CABLES]: lowVoltageCablesConfig,
    distributionWireGroundConfig,
    [MaterialSubclassCode.DISTRIBUTION_WIRE_GROUND]: distributionWireGroundConfig
  }
};

export default distributionCableConfig;
