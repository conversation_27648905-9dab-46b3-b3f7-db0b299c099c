<template>
  <el-dialog
    v-model="productOrderStore.productOrderModalVisible"
    title="新增生产订单"
    class="middle"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
    @open="onDialogOpen"
    @close="onDialogClose"
    @closed="onDialogClosed"
  >
    <ProductOrder :salesOrderId="salesOrderDetailStore.saleOrderId" ref="productOrderRef" />

    <template #footer>
      <el-button @click="productOrderStore.setProductOrderModalVisible(false)">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="handleOnSave()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ProductOrder from "./product-order/index.vue";
import { useSalesOrderDetailStore, useSalesProductOrderStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { ICreateProductOrder } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";

let addedProductionOrder = false;
const productOrderRef = ref<InstanceType<typeof ProductOrder>>();
const productOrderStore = useSalesProductOrderStore();
const salesOrderDetailStore = useSalesOrderDetailStore();

const emits = defineEmits<{
  (event: "shouldRefresh"): void;
}>();

const saveLoading = ref<boolean>(false);
const handleOnSave = useLoadingFn(save, saveLoading);

async function save() {
  const success = await createProductOrder()
    .then(res => res.data)
    .catch(() => false);
  if (!success) {
    return;
  }
  ElMessage.success("新增成功");
  addedProductionOrder = true;
  productOrderStore.setProductOrderModalVisible(false);
}

function onDialogOpen() {
  addedProductionOrder = false;
}

function onDialogClose() {
  if (addedProductionOrder) {
    emits("shouldRefresh");
  }
}

function onDialogClosed() {
  productOrderStore.setCreateProductOrderFromBySoItemNo(false);
  productOrderStore.initCreateProductOrderDetail();
}

async function createProductOrder() {
  const formValue: ICreateProductOrder | boolean = await productOrderRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return Promise.reject();
  }
  if (formValue.salesLineDetails) {
    delete formValue.salesLineDetails;
  }
  return productOrderStore.createProductOrderV2(formValue);
}
</script>
