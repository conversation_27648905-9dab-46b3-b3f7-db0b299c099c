import { computed, ref, watch, onUnmounted } from "vue";

/**
 * @description: 表格选中
 */
export function useTableSelect() {
  const selectedMap = new Map<string, { id: string }>();
  const selectedList = ref<{ id: string }[]>([]);
  const selectedCount = computed(() => selectedList.value.length);

  function addItem(id: string, item: { id: string }) {
    selectedMap.set(id, item);
  }

  function clearMap() {
    selectedMap.clear();
  }

  function selectChange(selection: Array<{ id: string }>) {
    selectedList.value = selection;
  }

  function clearSelectedList() {
    selectedList.value = [];
  }

  function clear() {
    clearMap();
    clearSelectedList();
  }

  onUnmounted(clear);

  // 更新映射关系
  watch(selectedList, list => {
    clearMap();
    list.forEach(item => {
      addItem(item.id, item);
    });
  });

  return {
    selectChange,
    clearSelectedList,
    clearMap,
    clear,
    selectedCount,
    selectedList
  };
}
