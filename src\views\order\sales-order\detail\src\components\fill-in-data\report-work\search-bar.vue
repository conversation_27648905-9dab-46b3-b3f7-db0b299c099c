<template>
  <el-form :model="formValue" :inline="true">
    <el-form-item label="报工批次号:" prop="productBatchNo">
      <el-input v-model="formValue.productBatchNo" class="!w-52" placeholder="请输入报工批次号" clearable />
    </el-form-item>
    <el-form-item label="工序:" prop="processId">
      <ElSelect v-model="formValue.processId" placeholder="请选择工序" clearable filterable>
        <el-option v-for="item in processOptionsRef || []" :key="item.value" :label="item.label" :value="item.value" />
      </ElSelect>
    </el-form-item>
    <el-form-item label="设备:" prop="deviceId">
      <ElSelect v-model="formValue.deviceId" placeholder="请选择设备" clearable filterable>
        <el-option
          v-for="item in deviceStore.options || []"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </ElSelect>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watchEffect, computed } from "vue";
import { IReportWorkReq, IOption } from "@/models";
import { useDeviceStore, useProcessStore, useSalesFillInDataStore } from "@/store/modules";

const props = withDefaults(
  defineProps<{
    subclassCode: string;
    processIds: string;
    minClassCode?: string;
  }>(),
  {
    subclassCode: "",
    processIds: "",
    minClassCode: ""
  }
);

const formValue = reactive<IReportWorkReq>({
  productBatchNo: undefined,
  deviceId: undefined,
  processId: undefined
});

const deviceStore = useDeviceStore();
const processStore = useProcessStore();
const processOptionsRef = ref<Array<IOption>>([]);

const isArmourClamp = computed(() => useSalesFillInDataStore().isArmourClamp);

watchEffect(() => {
  if (!props.subclassCode && !props.minClassCode) {
    return;
  }
  processStore.queryProcess(!isArmourClamp.value ? props.subclassCode : props.minClassCode);
});

deviceStore.queryDeviceList();

const emits = defineEmits<{
  (event: "searchForm", value: IReportWorkReq): void;
}>();

const search = () => {
  emits("searchForm", {
    ...formValue,
    productBatchNo: formValue.productBatchNo || null,
    deviceId: formValue.deviceId || null,
    processId: formValue.processId || null
  });
};

watchEffect(() => {
  if (!props.processIds) {
    return;
  }
  const processIdArr: Array<string> = props.processIds.split("-");
  processOptionsRef.value = processStore.options?.filter(x => processIdArr.includes(x.value as string)) || [];
});

const reset = () => {
  formValue.productBatchNo = undefined;
  formValue.deviceId = undefined;
  formValue.processId = undefined;
  emits("searchForm", formValue);
};
</script>

<style scoped lang="scss"></style>
