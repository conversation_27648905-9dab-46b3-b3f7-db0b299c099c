import { http } from "@/utils/http";
import { IResponse, IUpLoadFileRes } from "@/models";
import { withApiGateway } from "@/api/util";

export const uploadFile = (fileData: FormData) => {
  const url = withApiGateway(`admin-api/ecode/common/upload`);
  return http.post<FormData, IResponse<IUpLoadFileRes>>(url, {
    data: fileData,
    headers: { "Content-type": "multipart/form-data" }
  });
};

export const downLoadFile = (fileId: string) => {
  const url = withApiGateway(`admin-api/infra/file/downloadFile/${fileId}`);
  return http.get<void, Blob>(url, {
    responseType: "blob"
  });
};
