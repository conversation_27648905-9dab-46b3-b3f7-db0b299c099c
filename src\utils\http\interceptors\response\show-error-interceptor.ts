import { AxiosInstance } from "axios";
import { <PERSON><PERSON><PERSON><PERSON>, ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { ElErrorType, ErrorLevel, ErrorType, getElErrorType, getErrorTitle } from "@/enums";
import { h, render, Teleport } from "vue";

const DIALOG_ERROR_CONTAINER_ID = "dialogErrorContainer";

export function showErrorInterceptor(instance: AxiosInstance): number {
  return instance.interceptors.response.use(
    response => response,
    error => {
      const { isCancelRequest, config, response } = error;
      const { silentError, showErrorInDialog } = config;
      if (!isCancelRequest && !silentError) {
        const msgType = response?.data?.msgType ?? ErrorType.ERROR;
        const msgLevel = response?.data?.msgLevel ?? ErrorLevel.NORMAL;
        const message = error.message;
        const title = getErrorTitle(msgType);
        const elType = getElErrorType(msgType);
        const showMessageFn = getMessageFunction(msgType, msgLevel, showErrorInDialog);
        showMessageFn(title, message, elType, showErrorInDialog);
      }
      return Promise.reject(error);
    }
  );
}

function getMessageFunction(type: ErrorType, level: ErrorLevel, showErrorInDialog?: boolean) {
  if (type === ErrorType.ERROR && showErrorInDialog) {
    return showAlert;
  }
  return (
    {
      [ErrorType.SUCCESS]: {
        [ErrorLevel.NORMAL]: showMessage,
        [ErrorLevel.MEDIUM]: showMessage,
        [ErrorLevel.HIGH]: showMessage,
        [ErrorLevel.HIGHEST]: showNotification
      },
      [ErrorType.INFO]: {
        [ErrorLevel.NORMAL]: showMessage,
        [ErrorLevel.MEDIUM]: showMessage,
        [ErrorLevel.HIGH]: showMessage,
        [ErrorLevel.HIGHEST]: showNotification
      },
      [ErrorType.WARNING]: {
        [ErrorLevel.NORMAL]: showMessage,
        [ErrorLevel.MEDIUM]: showMessage,
        [ErrorLevel.HIGH]: showConfirm,
        [ErrorLevel.HIGHEST]: showConfirm
      },
      [ErrorType.ERROR]: {
        [ErrorLevel.NORMAL]: showMessage,
        [ErrorLevel.MEDIUM]: showConfirm,
        [ErrorLevel.HIGH]: showConfirm,
        [ErrorLevel.HIGHEST]: showConfirm
      }
    }[type]?.[level] || showMessage
  );
}

function showMessage(title: string, msg: string, type: ElErrorType): void {
  ElMessage({ message: msg, type });
}

function showNotification(title: string, message: string, type: ElErrorType): void {
  ElNotification({
    type,
    message,
    duration: 3000
  });
}

function showConfirm(title: string, message: string, type: ElErrorType): void {
  ElMessageBox.alert(message, title, { type });
}

function showAlert(
  title: string,
  message: string,
  type: ElErrorType,
  showErrorInDialog: boolean | string | HTMLElement
): void {
  const activeDialogBody = getActiveDialogBody();
  if (!activeDialogBody) {
    return;
  }
  activeDialogBody.querySelector(`#${DIALOG_ERROR_CONTAINER_ID}`)?.remove();
  const to = typeof showErrorInDialog !== "boolean" ? showErrorInDialog : undefined;
  const container = createAlert(message, type, to);
  activeDialogBody.prepend(container);
}

function createAlert(message: string, type: ElErrorType, to?: string | HTMLElement): HTMLElement {
  let container = document.createElement("div");
  container.id = DIALOG_ERROR_CONTAINER_ID;
  const alert = h(ElAlert, {
    type,
    title: message,
    class: "!mb-2",
    onClose: () => {
      container.remove();
      container = null;
    }
  });
  const vNode = to ? h(Teleport, { to }, alert) : alert;
  render(vNode, container);
  return container;
}

function getActiveDialogBody(): HTMLElement {
  const dialogs = document.querySelectorAll(".el-overlay-dialog");
  const maxZIndex: number = Number.MIN_SAFE_INTEGER;
  let activeDialog: HTMLElement;
  dialogs.forEach(dialog => {
    const parent = dialog.parentElement;
    if (parent.style.display === "none") {
      return;
    }
    if (Number(parent.style.zIndex) > maxZIndex) {
      activeDialog = dialog as HTMLElement;
    }
  });
  return activeDialog?.querySelector(".el-dialog__body");
}
