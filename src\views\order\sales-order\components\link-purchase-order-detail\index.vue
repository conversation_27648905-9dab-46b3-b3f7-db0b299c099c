<template>
  <ElForm :inline="true" :model="state.params">
    <ElRow v-if="licenseAuthIncludeCSG">
      <ElFormItem label="订单来源：">
        <ElRadioGroup v-model="state.params.channel" @change="changeChannel">
          <ElRadio :label="PurchaseChannel.EIP">国网</ElRadio>
          <ElRadio :label="PurchaseChannel.CSG_GuangZhou">广州供电局</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElRow>
    <ElFormItem label="采购订单号：">
      <ElInput class="!w-[200px]" v-model="state.params.orFilters[0].po_no" clearable placeholder="请输入采购订单号" />
    </ElFormItem>
    <ElFormItem label="采购方公司名称：">
      <ElInput
        class="!w-[200px]"
        v-model="state.params.orFilters[1].buyer_name"
        clearable
        placeholder="请输入采购方公司名称"
      />
    </ElFormItem>
    <ElFormItem>
      <ElButton type="primary" @click="requestSalesLinkPurchaseOrders">搜索</ElButton>
    </ElFormItem>
  </ElForm>
  <PureTable
    ref="tableRef"
    class="pagination tooltip-max-w mt-1"
    height="400"
    :row-key="getRowKeyOfTarget"
    :data="state.salesLinkPurchaseOrders"
    :columns="columns"
    showOverflowTooltip
    :loading="loading"
    :pagination="pagination"
    @page-size-change="requestSalesLinkPurchaseOrders"
    @page-current-change="requestSalesLinkPurchaseOrders"
    @selection-change="handleSelectionChange"
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
  </PureTable>
  <OrderSelectPoNo :multipleSelection="state.multipleSelection" @deleteTag="deleteTag" :title="'已选采购订单号：'" />
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useColumns } from "./columns";
import { reactive, onMounted, onUnmounted, ref, watch, inject } from "vue";
import { ISalesLinkPurchaseOrder, ISalesLinkPurchaseOrderParams } from "@/models";
import { isEmpty, pickBy, identity, cloneDeep, omit, find } from "lodash-unified";
import { useSalesOrderManagementStore, useSystemAuthStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import OrderSelectPoNo from "../order-select-pono/index.vue";
import { useSalesOrderHook } from "../../hooks/sales-order-hook";
import { PurchaseChannel } from "@/enums";
import { computedAsync } from "@vueuse/core";
import { SalesOrderFormCtx, SalesOrderFormToken } from "../../ctx-token";

defineExpose({
  getSalesLinkPurchaseOrdersPoNo,
  getChannel
});

const props = defineProps<{
  selectItems: Array<ISalesLinkPurchaseOrder>;
  categoryCode: string;
  channel?: PurchaseChannel;
}>();

const ctx = inject<SalesOrderFormCtx>(SalesOrderFormToken);
const systemAuthStore = useSystemAuthStore();

const state = reactive<{
  params: ISalesLinkPurchaseOrderParams;
  salesLinkPurchaseOrders: Array<ISalesLinkPurchaseOrder>;
  selectSalesLinkPurchaseOrders: Array<ISalesLinkPurchaseOrder>;
  multipleSelection: Array<string>;
}>({
  params: {
    categoryCode: props.categoryCode,
    orFilters: [{ po_no: "" }, { buyer_name: "" }],
    channel: props.channel ? props.channel : PurchaseChannel.EIP
  } as ISalesLinkPurchaseOrderParams,
  salesLinkPurchaseOrders: [],
  selectSalesLinkPurchaseOrders: [],
  multipleSelection: []
});

const { columns } = useColumns();
const { pagination } = useTableConfig();
const tableRef = ref<PureTableInstance>();
const loading = ref<boolean>(false);

const salesOrderManagementStore = useSalesOrderManagementStore();

const { querySalesLinkPurchaseOrders } = useSalesOrderHook();
const getSalesLinkPurchaseOrders = useLoadingFn(querySalesLinkPurchaseOrders, loading);
const licenseAuthIncludeCSG = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeCSG);

async function changeChannel() {
  pagination.currentPage = 1;
  try {
    tableRef.value.getTableRef().clearSelection();
    state.multipleSelection = [];
    await requestSalesLinkPurchaseOrders();
  } catch (error) {
    console.log(error);
  }
}

async function requestSalesLinkPurchaseOrders() {
  state.salesLinkPurchaseOrders = await getSalesLinkPurchaseOrders(queryParams());
}

onMounted(requestSalesLinkPurchaseOrders);

onUnmounted(() => {
  state.selectSalesLinkPurchaseOrders.length = 0;
  state.multipleSelection.length = 0;
});

const handleSelectionChange = (val: Array<ISalesLinkPurchaseOrder>) => {
  state.selectSalesLinkPurchaseOrders = val;
  state.multipleSelection = val.map(salesLinkPurchaseOrder => salesLinkPurchaseOrder.poNo);
};

const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  props.selectItems.length
    ? (state.params.poNo = props.selectItems.map(select => select.poNo))
    : (state.params = omit(state.params, "poNo"));
  const params = cloneDeep(state.params);
  // 如果渠道来源于广州供电局，不传物资种类
  if (state.params.channel === PurchaseChannel.CSG_GuangZhou) {
    delete params.categoryCode;
  }
  const filter = params.orFilters.filter(param => !isEmpty(pickBy(param, identity)));
  if (filter.length) {
    params.orFilters = filter;
    return params;
  } else {
    return omit(params, "orFilters");
  }
};

watch(
  () => state.salesLinkPurchaseOrders,
  (newValue, oldValue) => {
    if (props.selectItems) {
      if (!oldValue.length) {
        state.multipleSelection = props.selectItems.map(select => select.poNo);
      }
      props.selectItems.forEach(select => {
        const index = state.salesLinkPurchaseOrders.findIndex(
          salesLinkPurchase => salesLinkPurchase.poNo == select.poNo
        );
        const selectRows: Array<ISalesLinkPurchaseOrder> = tableRef.value?.getTableRef()?.getSelectionRows();
        if (selectRows?.length && find(selectRows, { poNo: select.poNo })) {
          return;
        }
        if (index > -1) {
          tableRef.value?.getTableRef()?.toggleRowSelection(state.salesLinkPurchaseOrders[index], true);
        }
      });
    }
  }
);
watch(
  () => salesOrderManagementStore.salesLinkPurchaseOrdersTotal,
  () => {
    pagination.total = salesOrderManagementStore.salesLinkPurchaseOrdersTotal;
  },
  {
    immediate: true
  }
);

watch(
  () => state.selectSalesLinkPurchaseOrders,
  list => {
    const toggleSelectedDisabled = (ctx as any).toggleSelectedDisabled;

    toggleSelectedDisabled(!list.length);
  }
);

function getSalesLinkPurchaseOrdersPoNo(): Array<ISalesLinkPurchaseOrder> {
  return state.selectSalesLinkPurchaseOrders;
}

function getChannel() {
  return state.params.channel;
}

function getRowKeyOfTarget(row) {
  return `${row.id}-${row.poNo}`;
}

const deleteTag = (val: string) => {
  state.multipleSelection = state.multipleSelection.filter(multipleSelection => multipleSelection != val);
  state.selectSalesLinkPurchaseOrders = state.selectSalesLinkPurchaseOrders.filter(select => select.poNo != val);
  const index = state.salesLinkPurchaseOrders.findIndex(salesLinkPurchase => salesLinkPurchase.poNo == val);
  if (index > -1) {
    tableRef.value?.getTableRef()?.toggleRowSelection(state.salesLinkPurchaseOrders[index], false);
  }
};
</script>

<style scoped lang="scss"></style>
