<!-- 第三步：填报生产数据 - 线缆 -->
<template>
  <div class="overflow-hidden w-full flex flex-col">
    <QuickSolver title="需生产销售订单行" :items="productOrderStore.productOrderStatistics?.notProductionOrder">
      <template v-slot="{ item }">
        <NoProductionSchedulingSaleOrderLine :item="item" />
      </template>
    </QuickSolver>
    <div class="card flex-1 py-3">
      <div class="mb-4">
        <SearchBar @operateChange="onProductOrderModalVisible" @searchForm="searchForm" />
      </div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="productOrderStore.productOrders"
        :columns="columns"
        size="large"
        :loading="productOrderStore.loading"
        showOverflowTooltip
        :pagination="pagination"
        @page-current-change="onPageCurrentPage"
        @page-size-change="onPageSizeChange"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
      </PureTable>

      <el-dialog
        v-model="editProductOrderModalVisibleRef"
        title="编辑生产订单"
        class="middle"
        align-center
        :destroy-on-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="onCloseEditProductOrder()"
      >
        <ProductOrder ref="productOrderFormRef" />
        <template #footer>
          <span>
            <el-button @click="onCloseEditProductOrder()">取消</el-button>
            <el-button type="primary" @click="onConfirmEditProductOrder()">保存</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="填报生产数据"
        v-model="productOrderStore.fillInDataModalVisible"
        fullscreen
        class="state-grid-order-sync-detail-dialog"
        :destroy-on-close="true"
        :close-on-press-escape="false"
        @close="onCloseFillInDataModal()"
        :show-close="false"
      >
        <template #header="{ close, titleId, titleClass }">
          <div :id="titleId" class="flex justify-between">
            <div class="flex gap-3 items-center">
              <el-button link @click="close">
                <el-icon size="20"><Back /></el-icon>
              </el-button>
              <div :class="titleClass">【填报生产数据】</div>
              <div :class="titleClass" v-if="salesOrderDetailStore.salesOrder.soNo">
                销售订单号：{{ salesOrderDetailStore.salesOrder.soNo }}
              </div>
            </div>
            <el-button type="danger" @click="close">
              <el-icon size="20"><Close /></el-icon>
            </el-button>
          </div>
        </template>
        <FillInData />
      </el-dialog>
      <el-dialog
        title="数据检查"
        v-model="productOrderStore.dataMissingModalVisible"
        class="default"
        :align-center="true"
      >
        <DataMissing :data-missing="productOrderStore.productOrderDataMissing" />
      </el-dialog>
      <el-dialog
        v-model="productOrderStore.productOrderDetailVisible"
        title="生产订单详情"
        class="middle"
        align-center
        destroy-on-close
        :close-on-click-modal="false"
      >
        <ProductOrderDetail />
      </el-dialog>
      <!-- v2 -->
      <AddProductOrderV2 @shouldRefresh="refreshAfterCreateProductionOrder" />
    </div>
  </div>
</template>
<script setup lang="ts">
import SearchBar from "./search-bar.vue";
import AddProductOrderV2 from "../add-product-order/index.vue";
import ProductOrderDetail from "../product-order-detail/index.vue";
import FillInData from "../../fill-in-data/fill-in-data/index.vue";
import NoProductionSchedulingSaleOrderLine from "../no-production-scheduling-sale-order-line/index.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import QuickSolver from "@/views/order/sales-order/detail/src/components/quick-solver/quick-solver.vue";
import DataMissing from "@/views/order/sales-order/detail/src/components/data-missing/data-missing.vue";
import ProductOrder from "../add-product-order/product-order/index.vue";
import { useProductOrderHook } from "../hooks";
import { useSalesOrderDetailStore, useSalesProductOrderStore } from "@/store/modules";
import { useColumns } from "./columns";
import { reactive, ref, watchEffect, onMounted } from "vue";
import { ICreateProductOrder, IResponse, IProductOrderReq, IProductOrderRef } from "@/models";
import { ElMessage } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";
import { Close, Back } from "@element-plus/icons-vue";

const { columns, editProductOrderModalVisibleRef } = useColumns();
const { pagination } = useTableConfig();
const productOrderFormRef = ref<IProductOrderRef | null>();

const {
  onProductOrderModalVisible,
  onConfirmEditProductOrder: onConfirmEditProductOrderHook,
  onCloseEditProductOrder: onCloseEditProductOrderHook
} = useProductOrderHook();

const productOrderStore = useSalesProductOrderStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
let queryFormValue: IProductOrderReq;
const state = reactive<{ salesId: string }>({ salesId: "" });

onMounted(() => {
  state.salesId = salesOrderDetailStore.saleOrderId;
  productOrderStore.queryProductOrderStatistics(state.salesId);
  productOrderStore.queryProductOrders(filterParams());
});

watchEffect(() => {
  if (productOrderStore.deleteProductOrderStatus) {
    pagination.currentPage = 1;
    productOrderStore.queryProductOrders(filterParams());
    productOrderStore.setDeleteProductOrderStatus(false);
  }
});

watchEffect(() => {
  pagination.total = productOrderStore.total;
});

// 搜索的时候重置页码
const searchForm = (value: IProductOrderReq) => {
  queryFormValue = value;
  pagination.currentPage = 1;
  productOrderStore.queryProductOrders(filterParams());
};

const onConfirmEditProductOrder = async () => {
  const formValue: ICreateProductOrder | boolean = await productOrderFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  if (formValue.salesLineDetails) {
    delete formValue.salesLineDetails;
  }
  const editRes: IResponse<boolean> = await onConfirmEditProductOrderHook(formValue as ICreateProductOrder);
  if (!editRes.data) {
    ElMessage.warning(editRes.msg);
    return;
  }
  ElMessage.success("编辑成功");
  onCloseEditProductOrder();
  productOrderStore.queryProductOrders(filterParams());
};

const onCloseEditProductOrder = () => {
  editProductOrderModalVisibleRef.value = false;
  onCloseEditProductOrderHook();
};

const refreshAfterCreateProductionOrder = () => {
  pagination.currentPage = 1;
  productOrderStore.queryProductOrders(filterParams());
  productOrderStore.queryProductOrderStatistics(salesOrderDetailStore.saleOrderId);
  salesOrderDetailStore.refreshStepStatus();
};

function filterParams() {
  let params: IProductOrderReq = {
    saleId: state.salesId,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (queryFormValue) {
    params = {
      ...params,
      ...queryFormValue
    };
  }

  return params;
}

const onCloseFillInDataModal = () => {
  productOrderStore.setFillInDataModalVisible(false);
  productOrderStore.queryProductOrders(filterParams());
};

function onPageCurrentPage() {
  productOrderStore.queryProductOrders(filterParams());
}

function onPageSizeChange() {
  pagination.currentPage = 1;
  productOrderStore.queryProductOrders(filterParams());
}
</script>
<style scoped lang="scss">
@import "../../../../../../../../style/mixin";

.card {
  @include productCard;
}
</style>
