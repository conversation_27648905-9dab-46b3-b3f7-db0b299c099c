<template>
  <el-select
    class="w-full"
    v-if="loaded"
    v-model="modelValue"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择工单"
    filterable
    clearable
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <el-select
    v-else
    class="w-full"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择工单"
    filterable
    clearable
  />
</template>

<script setup lang="ts">
import { ref, watch, withDefaults } from "vue";
import { useVModels } from "@vueuse/core";
import { queryWorkOrder } from "@/api/work-order";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";

interface IOption {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    /** 工单id */
    modelValue: string;
    productionId: string;
    disabled?: boolean;
  }>(),
  {
    disabled: false
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);
const loading = ref(false);
const loaded = ref(false);
const options = ref<Array<IOption>>([]);

const requestWorkOrderList = useLoadingFn(async () => {
  const { data } = await queryWorkOrder({ productionId: props.productionId });
  if (!data) {
    return;
  }
  options.value = data.map(({ id, woNo }) => ({
    label: woNo,
    value: id
  }));
  loaded.value = true;
}, loading);

watch(
  () => props.productionId,
  id => {
    if (id) {
      requestWorkOrderList();
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  height: var(--el-input-inner-height) !important;
}
</style>
