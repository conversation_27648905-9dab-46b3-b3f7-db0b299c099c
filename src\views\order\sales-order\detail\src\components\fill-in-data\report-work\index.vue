<template>
  <SearchBar
    @searchForm="onSearchForm"
    :processIds="reportWorkStore.reportWorkFormProps?.processIds"
    :subclassCode="reportWorkStore.reportWorkFormProps?.subclassCode"
    :minClassCode="reportWorkStore.reportWorkFormProps?.minClassCode"
  />
  <PureTable
    row-key="id"
    :columns="columns"
    :data="reportWorkStore.reportWorks"
    :loading="reportWorkStore.loading"
    :height="salesOrderStore.isCable ? 300 : undefined"
    :pagination="pagination"
    showOverflowTooltip
    @page-current-change="onPageCurrentPage"
    @page-size-change="onPageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </PureTable>
</template>

<script setup lang="ts">
import SearchBar from "./search-bar.vue";
import {
  useSalesFillInDataStore,
  useReportWorkStore,
  useWorkOrderStore,
  useSalesOrderDetailStore
} from "@/store/modules";
import { useColumns } from "./columns";
import { computed, onMounted, watch, watchEffect } from "vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { IReportWorkReq } from "@/models";

const { columns } = useColumns();
const reportWorkStore = useReportWorkStore();
const salesOrderStore = useSalesOrderDetailStore();
const fillInDataStore = useSalesFillInDataStore();
const workOrderStore = useWorkOrderStore();
const { pagination } = useTableConfig();
let queryFormValue: IReportWorkReq;

const reportWorkParams = computed(() => {
  const workId = salesOrderStore.isCable ? workOrderStore.workOrderDetail.id : fillInDataStore.dataId;
  return { workId };
});

onMounted(() => {
  watch(
    reportWorkParams,
    params => {
      reportWorkStore.setReportWorkParams({
        ...(params || {}),
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize
      });
      reportWorkStore.queryReportWorkPaging();
    },
    { immediate: true }
  );
});

watchEffect(() => {
  pagination.total = reportWorkStore.total;
});

watchEffect(() => {
  if (reportWorkStore.freshReportWorkTag) {
    pagination.currentPage = 1;
    queryReportWorkPaging();
    reportWorkStore.setFreshReportWorkTag(false);
  }
});

function onPageCurrentPage() {
  queryReportWorkPaging();
}
function onPageSizeChange() {
  pagination.currentPage = 1;
  queryReportWorkPaging();
}

function queryReportWorkPaging() {
  let reportWorkParams: IReportWorkReq = reportWorkStore.reportWorkParams;
  if (!reportWorkParams) {
    return;
  }
  reportWorkParams.pageNo = pagination.currentPage;
  reportWorkParams.pageSize = pagination.pageSize;
  if (queryFormValue) {
    reportWorkParams = { ...reportWorkParams, ...queryFormValue };
  } else {
    reportWorkParams.productBatchNo = undefined;
    reportWorkParams.deviceId = undefined;
    reportWorkParams.processId = undefined;
  }
  reportWorkStore.setReportWorkParams(reportWorkParams);
  reportWorkStore.queryReportWorkPaging();
}

function onSearchForm(value: IReportWorkReq) {
  queryFormValue = value;
  pagination.currentPage = 1;
  queryReportWorkPaging();
}
</script>

<style scoped lang="scss"></style>
