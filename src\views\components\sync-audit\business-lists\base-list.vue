<template>
  <pure-table
    show-overflow-tooltip
    size="large"
    row-key="id"
    class="flex-1 overflow-hidden"
    :data="data"
    :columns="props.columns"
    :loading="loading"
    :pagination="pagination"
    :max-height="500"
    @page-current-change="pageChange"
    @page-size-change="pageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import { PureTable } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { onMounted, ref, shallowRef } from "vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { BusinessSyncListType, IBusinessSyncCommon, IPagingReq } from "@/models";
import { useTableConfig } from "@/utils/useTableConfig";

const props = defineProps<{
  type: BusinessSyncListType;
  columns: TableColumnList;
  checkData?: boolean;
}>();
const emits = defineEmits<{
  (e: "dataChange", data: Array<IBusinessSyncCommon>): void;
}>();

const store = useStateGridSyncAuditStore();

const loading = ref(false);
const data = shallowRef<Array<IBusinessSyncCommon>>();
const handleRefresh = useLoadingFn(store.getSyncListByType, loading);
// 分页信息
const { pagination } = useTableConfig();
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};
pagination.hideOnSinglePage = true;

onMounted(() => {
  refresh(pageInfo);
});

async function refresh(pageInfo?: IPagingReq) {
  const res = await handleRefresh(props.type, props.checkData, null, pageInfo);
  data.value = res?.list || [];
  pagination.total = res?.total || 0;
  emits("dataChange", data.value);
}

/**
 * 切换页码
 */
function pageChange(pageNo: number) {
  refresh({ pageNo, pageSize: pagination.pageSize });
}
/**
 * 切换页码数量
 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  refresh({ pageNo: 1, pageSize });
}

defineExpose({ refresh });
</script>

<style scoped></style>
