<template>
  <div class="status">
    <span class="title">填报生产数据</span>
  </div>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    :size="size"
    :placeholder="placeholder"
    @search="onSearch()"
  >
    <el-button
      v-auth="PermissionKey.form.formPurchaseWorkOrderCreate"
      v-track="TrackPointKey.FORM_PURCHASE_PWO_CREATE"
      type="primary"
      @click="onAddCreateWorkOrderDialogVisible()"
    >
      <template #icon>
        <FontIcon class="mr-2" icon="icon-plus" />
      </template>
      新增工单
    </el-button>
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm";
import { IKeywordField, ISearchItem, SizeType } from "@/components/SearchForm";
import { ElDatePicker, ElInput, ElOption, ElSelect } from "element-plus";
import { reactive, defineComponent, h } from "vue";
import { PermissionKey, TrackPointKey } from "@/consts";
import { IWorkOrderReq } from "@/models";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateoption } from "@/enums";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { computedAsync } from "@vueuse/core";

const keyWordAliasHook = usekeyWordAliasHook();
const size = SizeType.DEFAULT;

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `请输入工单编号/${KeywordAliasEnum.IPO_NO}/销售订单行项目`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const WoStatusComponents = defineComponent({
  name: "woStatus",
  render() {
    // 品类下拉框数据
    const options = ProductionStateoption.map(option =>
      h(ElOption, { label: option.label, value: option.value }, () => option.label)
    );
    return h(ElSelect, { clearable: true, filterable: true }, () => options);
  }
});

const form = reactive({
  materialName: undefined,
  plantStartTime: undefined,
  plantEndTime: undefined,
  woStatus: undefined
});

const items: Array<ISearchItem> = [
  {
    key: "materialName",
    label: "物料名称：",
    component: ElInput,
    componentProps: {
      placeholder: "请输入物料名称"
    }
  },
  {
    key: "plantStartTime",
    label: "计划开始日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划开始日期"
    }
  },
  {
    key: "plantEndTime",
    label: "计划结束日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划结束日期"
    }
  },
  {
    key: "woStatus",
    label: "工单状态：",
    component: WoStatusComponents
  }
];

const emits = defineEmits<{
  (event: "onCreateWorkOrderDialogVisible", params: boolean): void;
  (event: "searchForm", value: IWorkOrderReq): void;
}>();

const keywordFields: Array<IKeywordField> = [
  { key: "woNo", title: "工单编号" },
  { key: "soNo", title: "生产订单号" },
  { key: "soItemNo", title: "销售订单行项目" }
];

const onSearch = () => {
  emits("searchForm", { ...form, materialName: form.materialName || null, woStatus: form.woStatus || null });
};

/** 操作事件 */
const onAddCreateWorkOrderDialogVisible = () => {
  emits("onCreateWorkOrderDialogVisible", true);
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;

  .title {
    color: var(--el-text-color-primary);
    font-size: 1.125rem;
    font-weight: normal;
    line-height: 1.625rem;
    letter-spacing: 0;
    padding-bottom: 0.75rem;
  }
}
</style>
