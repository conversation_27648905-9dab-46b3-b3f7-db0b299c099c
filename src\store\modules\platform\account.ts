import { IAccount, IAccountForm, IAccountReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/platform/account";

export const useAccountStore = defineStore({
  id: "cx-account",
  state: () => ({
    accounts: [] as Array<IAccount>,
    total: 0 as number,
    accountForm: {} as IAccountForm,
    loading: false as boolean
  }),
  actions: {
    async queryAccount(params?: IAccountReq) {
      this.loading = true;
      const res = await api.queryAccount(params);
      this.accounts = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async addAccount(data: IAccountForm) {
      return api.addAccount(data);
    },

    async editAccount(data: Partial<IAccountForm>) {
      return api.editAccount(data);
    },

    setAccountForm(accountForm?: Partial<IAccountForm>) {
      this.accountForm = accountForm;
    }
  }
});
