<template>
  <el-form ref="formRef" :model="formValue" :rules="rules" :validate-on-rule-change="false">
    <el-row :gutter="40">
      <el-col :span="10">
        <el-form-item label="物资种类" prop="subClassCode">
          <subclass-select v-model="formValue.subClassCode" />
        </el-form-item>
      </el-col>

      <el-col :span="10">
        <el-form-item label="工序" prop="processId">
          <experiment-process-selector
            :sub-class-code="formValue.subClassCode"
            :exclude-process-code="['JFNY']"
            v-model="formValue.processId"
            v-model:process-code="formValue.processCode"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item prop="fileList">
          <el-upload
            drag
            ref="uploadRef"
            v-model:file-list="formValue.fileList"
            :limit="1"
            :auto-upload="false"
            :accept="excelAccept"
            :on-change="onUploadFileChange"
            :on-exceed="onUploadFileExceed"
          >
            <el-icon size="24">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              <div class="upload-text"><span>将导入Excel数据模板拖到此处，或</span><em>点击上传</em></div>
              <div class="upload-tips">
                <span>仅支持Excel格式文件.</span>
              </div>
            </div>
          </el-upload>
          <div class="rule">
            <div>1.仅支持上传excel，文件大小不能超过20M</div>
            <div>2.请基于模板维护数据，请勿修改单元格</div>
            <div>3.每次只允许上传一个文件</div>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { ElMessage, type FormInstance, type FormRules, type UploadFile, type UploadInstance } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import ExperimentProcessSelector from "@/views/components/process-selector/experiment-process-selector.vue";
import SubclassSelect from "@/views/components/subclass-select";

interface IFormValue {
  subClassCode: string;
  processId: string;
  processCode: string;
  fileList: Array<UploadFile>;
}

const uploadRef = ref<UploadInstance>();
const formRef = ref<FormInstance>();

const formValue = reactive<IFormValue>({
  subClassCode: "",
  processId: "",
  processCode: "",
  fileList: []
});

const rules: FormRules = {
  subClassCode: [{ required: true, message: "请选择物资种类", trigger: "change" }],
  processId: [{ required: true, message: "请选择工序", trigger: "change" }],
  fileList: [{ required: true, type: "array", message: "请选择导入文件", trigger: "change" }]
};

const excelTypes = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"];
const excelAccept: string = excelTypes.join(",");

watch(
  () => formValue.fileList,
  v => {
    console.log(v);
  }
);

watch(
  () => formValue.subClassCode,
  () => {
    formValue.processId = "";
    formValue.processCode = "";
  }
);

function onUploadFileChange(file: UploadFile) {
  if (!excelTypes.includes(file.raw.type)) {
    formValue.fileList = [];
    uploadRef.value?.clearFiles();
    ElMessage.warning("上传模板仅支持Excel类型");
    return;
  }
}

function onUploadFileExceed(files: Array<any>) {
  const file = files[0];
  if (!file) {
    return;
  }
  if (!excelTypes.includes(file.type)) {
    formValue.fileList = [];
    uploadRef.value?.clearFiles();
    ElMessage.warning("上传模板仅支持Excel类型");
    return;
  }

  formValue.fileList = [file];
}

/**
 * @description: 验证表单，并返回验证结果
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单值
 */
function initFormValue(v: any) {
  Object.assign(formValue, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const value = new FormData();
  value.append("subClassCode", formValue.subClassCode);
  value.append("processCode", formValue.processCode);
  value.append("excelFile", formValue.fileList[0].raw);
  return value;
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  display: block;
}

:deep(.el-upload-list__item-status-label) {
  display: none;
}
</style>
