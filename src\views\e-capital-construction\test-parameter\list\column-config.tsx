import { ColumnWidth, TableWidth, SpeciTypeEnum } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { EParamsStandard } from "@/models/e-capital-construction";
import { TableColumnRenderer } from "@pureadmin/table";
import { RouterLink } from "vue-router";

/**
 * @description: 试验参数标准表格配置
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "编号",
      prop: "no",
      minWidth: ColumnWidth.Char8,
      fixed: "left"
    },
    {
      label: "试验参数标准",
      prop: "name",
      width: ColumnWidth.Char8,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <RouterLink class="text-primary" to={{ name: "testParameterDetail", query: { id: data.row.id } }}>
            {data.row.name}
          </RouterLink>
        );
      }
    },
    {
      label: "物资种类",
      prop: "categoryName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "设备名称",
      prop: "speciType",
      minWidth: ColumnWidth.Char10,
      formatter: (row: EParamsStandard) => `${SpeciTypeEnum[row.speciType]}`
    },
    {
      label: "备注",
      prop: "remarks",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "更新时间",
      prop: "updateTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "更新人",
      prop: "updaterName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "创建人",
      prop: "creatorName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];

  return { columnsConfig };
}
