import {
  IListResponse,
  IDeliveryOrderListData,
  IDeliveryOrderReqParams,
  IResponse,
  IDeliveryOrderDetailReqParams,
  IDeliveryOrderDetailListData
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 查询供货单列表数据 */
export const queryDeliveryOrderDataList = (data: IDeliveryOrderReqParams) => {
  const url = withApiGateway("admin-api/business/deliveryOrder/getDeliveryOrderList");
  return http.post<IDeliveryOrderReqParams, IListResponse<IDeliveryOrderListData>>(url, {
    data
  });
};

/** 查询供货单数据根据ID */
export const getDeliveryOrderDetailById = (id: string) => {
  const url = withApiGateway(`admin-api/business/deliveryOrder/getDeliveryOrder/${id}`);
  return http.get<string, IResponse<IDeliveryOrderListData>>(url);
};

/** 创建供货单数据 */
export const createDeliveryOrder = (data: IDeliveryOrderListData) => {
  const url = withApiGateway("admin-api/business/deliveryOrder/createDeliveryOrder");
  return http.post<IDeliveryOrderListData, IResponse<string>>(url, {
    data
  });
};

/** 编辑供货单数据 */
export const editDeliveryOrder = (data: IDeliveryOrderListData) => {
  const url = withApiGateway(`admin-api/business/deliveryOrder/editDeliveryOrder/${data.id}`);
  return http.post<IDeliveryOrderListData, IResponse<boolean>>(url, {
    data
  });
};

/** 删除供货单数据 */
export const deleteDeliveryOrder = (id: string) => {
  const url = withApiGateway(`admin-api/business/deliveryOrder/deleteDeliveryOrder/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/** 查询供货单明细列表数据 */
export const queryDeliveryOrderDetailDataList = (data: IDeliveryOrderDetailReqParams) => {
  const url = withApiGateway("admin-api/business/deliveryOrderDetail/getList");
  return http.post<IDeliveryOrderDetailReqParams, IListResponse<IDeliveryOrderDetailListData>>(url, {
    data
  });
};

/** 创建供货单明细数据 */
export const createDeliveryOrderDetail = (data: IDeliveryOrderListData) => {
  const url = withApiGateway("admin-api/business/deliveryOrderDetail/createDeliveryOrderDetail");
  return http.post<IDeliveryOrderListData, IResponse<void>>(url, {
    data
  });
};

/** 编辑供货单明细数据 */
export const editDeliveryOrderDetail = (data: IDeliveryOrderListData) => {
  const url = withApiGateway(`admin-api/business/deliveryOrderDetail/editDeliveryOrderDetail/${data.id}`);
  return http.post<IDeliveryOrderListData, IResponse<string>>(url, {
    data
  });
};

/**删除供货单明细数据 */
export const deleteDeliveryOrderDetailById = (id: string) => {
  const url = withApiGateway(`admin-api/business/deliveryOrderDetail/deleteDeliveryOrderDetail/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/** 查询供货单明细列表数据 */
export const queryDeliveryOrderDetailDataListById = (id: string) => {
  const url = withApiGateway(`admin-api/business/deliveryOrderDetail/getDeliveryOrderDetail/${id}`);
  return http.get<string, IResponse<IDeliveryOrderDetailListData>>(url);
};
