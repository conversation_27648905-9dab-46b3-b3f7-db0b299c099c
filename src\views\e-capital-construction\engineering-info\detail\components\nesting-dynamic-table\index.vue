<template>
  <el-form ref="nestingFormRef" class="p-0" :model="formModel" :rules="formRules">
    <el-table :data="props.fields" style="width: 100%" :span-method="objectSpanMethod" border>
      <el-table-column prop="positionName" label="一级部位" width="120" />
      <el-table-column prop="secondPositionName" label="二级部位" width="120" />
      <el-table-column prop="conditionName" label="测试条件" width="120" />
      <el-table-column prop="measureItemName" label="检测项目" width="180" />
      <el-table-column prop="dataValue" label="检测值">
        <template #default="{ row, $index }">
          <el-form-item :prop="row.measureItemCode">
            <!-- 文本输入 -->
            <el-input
              v-if="row[props.fieldType] === 'string' || row[props.fieldType] === 'text'"
              v-model="formModel[row[props.fieldKey as string]]"
              type="text"
              :placeholder="`请输入${row.measureItemName}`"
            />

            <!-- 数字输入 -->
            <el-input-number
              v-else-if="row[props.fieldType] === 'number'"
              controls-position="right"
              v-model="formModel[row[props.fieldKey as string]]"
              class="!w-full"
              :placeholder="`请输入${row.measureItemName}`"
            />

            <!-- 下拉选择 -->
            <el-select
              v-else-if="row[props.fieldType] === 'select'"
              v-model="formModel[row[props.fieldKey as string]]"
              :placeholder="`请选择${row.measureItemName}`"
              style="width: 100%"
            >
              <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>

            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="row[props.fieldType] === 'date' || row[props.fieldType] === 'datetime'"
              v-model="formModel[row[props.fieldKey as string]]"
              type="date"
              :placeholder="`选择${row.measureItemName}`"
              style="width: 100%"
            />

            <!-- 开关 -->
            <el-switch
              v-else-if="row[props.fieldType] === 'switch'"
              v-model="formModel[row[props.fieldKey as string]]"
              active-text="是"
              inactive-text="否"
            />
            <!-- 附件上传 -->
            <dynamic-table-file
              v-else-if="row[props.fieldType] === 'file'"
              :ref="`uploadRef${$index}`"
              v-model="formModel[row[props.fieldKey as string]]"
            />

            <!-- 默认文本输入 -->
            <el-input
              v-else
              v-model="formModel[row[props.fieldKey as string]]"
              :placeholder="`请输入${row.measureItemName}`"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="measureUnit" label="单位" width="90" />
    </el-table>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, watch, computed, reactive } from "vue";
import type { FormInstance, FormRules, TableColumnCtx } from "element-plus";
import { FactoryTableItemModel } from "@/models";
import DynamicTableFile from "../dynamic-table-file/index.vue";

// 暴露给父组件的方法
defineExpose({
  validateForm,
  resetFields,
  getFormValue
});

interface SpanMethodProps {
  row: FactoryTableItemModel;
  column: TableColumnCtx<FactoryTableItemModel>;
  rowIndex: number;
  columnIndex: number;
}

const props = defineProps<{
  fields: FactoryTableItemModel[];
  modelValue: Record<string, any>;
  fieldKey: string;
  fieldType: string;
}>();

const state = reactive({
  spanArr: [],
  mergeArr: [],
  pos: null
});

const nestingFormRef = ref<FormInstance>();
const formModel = ref<Record<string, any>>({});

/** 计算合并数据 */
const getSpanArr = (data: FactoryTableItemModel[]) => {
  state.spanArr = [];
  for (let i = 0; i < data.length; i++) {
    if (i === 0) {
      state.spanArr.push(1);
      state.pos = 0;
    } else {
      // 判断当前元素与上一个元素是否相同
      if (data[i].positionCode && data[i].positionCode === data[i - 1].positionCode) {
        state.spanArr[state.pos] += 1;
        state.spanArr.push(0);
      } else {
        state.spanArr.push(1);
        state.pos = i;
      }
    }
  }
};

// 初始化表单模型
const initFormModel = () => {
  formModel.value = JSON.parse(JSON.stringify(props.modelValue));
  if (props.fields.length) {
    getSpanArr(props.fields);
  }
};

// 监听props变化，初始化表单
watch(() => props.fields, initFormModel, { immediate: true });

const objectSpanMethod = ({ rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex === 0) {
    const _row = state.spanArr[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      // [0,0] 表示这一行不显示， [2,1]表示行的合并数
      rowspan: _row,
      colspan: _col
    };
  }
};

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {};
  return rules;
});

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const params = [];
  props.fields.forEach(item => {
    if (formModel.value[item[props.fieldKey as string]] != "") {
      const newItem = {
        ...item,
        dataValue: formModel.value[item[props.fieldKey as string]]
      };
      if (item[props.fieldType] == "file") {
        newItem.fileInfo = formModel.value[item[`${props.fieldKey}`]];
        newItem.dataValue = newItem.fileInfo.id;
      }
      params.push(newItem);
    }
  });
  return Object.assign([], params as Array<FactoryTableItemModel>);
}

// 暴露验证方法
function validateForm() {
  return nestingFormRef.value?.validate();
}

// 暴露重置方法
function resetFields() {
  nestingFormRef.value?.resetFields();
  initFormModel();
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}

:deep(.el-upload-list__item) {
  transition: none;
}
</style>
