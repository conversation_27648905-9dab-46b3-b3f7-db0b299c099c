import { defineStore } from "pinia";
import {
  ICreateProductOrder,
  IListResponse,
  IProductOrder,
  IProductOrderReq,
  IProductOrderStatistics,
  IResponse,
  IDataCheck
} from "@/models";
import { CreateProductOrderStepEnum } from "@/enums";
import * as productionOrderService from "@/api/production-order";
import { useRowspan } from "@/utils/useRowspan";
import { queryDataIntegrityByProductionOrderList } from "@/api/data-integrity-check/data-integrity-check";

export const useProductOrderStore = defineStore({
  id: "cx-product-order-store",
  state: () => ({
    productOrders: [] as Array<IProductOrder>,
    selectedSaleOrderLineId: "" as string,
    productOrderDetail: {} as IProductOrder,
    createProductOrderDetail: {} as ICreateProductOrder,
    productOrderStatistics: {} as IProductOrderStatistics,
    activeCreateProductOrderStep: CreateProductOrderStepEnum.selectSaleOrderLine as CreateProductOrderStepEnum,
    productOrderModalVisible: false as boolean,
    createProductOrderFromBySoItemNo: false as boolean,
    fillInDataModalVisible: false as boolean,
    loading: false as boolean,
    dataMissingModalVisible: false as boolean,
    productOrderDataMissing: {} as IDataCheck,
    productOrderDetailVisible: false as boolean,
    /** 生产订单编辑 新增模式  默认为编辑模式  */
    productOrderFormAddMode: false as boolean,
    total: 0,
    deleteProductOrderStatus: false
  }),
  actions: {
    /**查询生产订单 */
    async queryProductOrders(params: IProductOrderReq) {
      this.loading = true;
      const productDataRes: IListResponse<IProductOrder> = await productionOrderService.queryProductOrderPaging(params);
      if (productDataRes.data?.list?.length) {
        this.productOrders = productDataRes.data?.list || [];
        this.total = productDataRes.data?.total;
        this.queryDataMissingByList(this.productOrders);
      } else {
        this.productOrders = [];
        this.total = 0;
      }
      this.loading = false;
    },

    /**
     * @description: 查询订单列表每一项的数据完整性检查
     */
    async queryDataMissingByList(list: Array<IProductOrder>) {
      if (!list.length) {
        return;
      }
      const idList = list.map(({ id }) => id);
      const { data } = await queryDataIntegrityByProductionOrderList(idList);
      const integrityMap = new Map<string, boolean>();
      data.forEach(({ productionId, missingData }) => {
        integrityMap.set(productionId, missingData);
      });
      list.forEach(item => {
        item.missingData = integrityMap.get(item.id);
      });
    },

    /** 状态查询：销售订单行总数：3 有生产订单行数11条 无生产订单行数：：3 */
    async queryProductOrderStatistics(purchaseId: string) {
      const productOrderStatisticsRes: IResponse<IProductOrderStatistics> =
        await productionOrderService.queryProductOrderStatistics(purchaseId);

      const productOrderStatistics = productOrderStatisticsRes.data;
      if (
        Array.isArray(productOrderStatistics.notProductionOrder) &&
        productOrderStatistics.notProductionOrder.length
      ) {
        productOrderStatistics.notProductionOrder = useRowspan(productOrderStatistics.notProductionOrder, "soNo");
      }
      this.productOrderStatistics = productOrderStatistics;
    },

    /** 创建生产订单 */
    async createProductOrder(data: ICreateProductOrder) {
      this.delParamsElement(data);
      return await productionOrderService.createProductOrder({ ...data });
    },

    /** 创建生产订单--v2 */
    async createProductOrderV2(data: ICreateProductOrder) {
      this.delParamsElement(data);
      return await productionOrderService.createProductOrderV2(data);
    },

    async editProductOrder(productOrder: ICreateProductOrder): Promise<IResponse<boolean>> {
      this.delParamsElement(productOrder);
      return await productionOrderService.editProductOrder(productOrder);
    },

    /** 删除生产订单 */
    async deleteProductOrder(id: string): Promise<IResponse<boolean>> {
      return await productionOrderService.deleteProductOrder(id);
    },

    async getProductOrderDetailById(id: string) {
      const res: IResponse<IProductOrder> = await productionOrderService.getProductOrderDetailById(id);
      this.productOrderDetail = res.data;
      return res.data;
    },

    async getProductOrderDataMissingById(id: string) {
      const dataMissingRes = await productionOrderService.getProductOrderDataMissingById(id);
      this.productOrderDataMissing = dataMissingRes.data;
    },

    setSelectedSaleOrderLineId(id?: string) {
      this.selectedSaleOrderLineId = id;
    },

    setProductOrderDetail(productOrder?: IProductOrder) {
      this.productOrderDetail = productOrder;
    },

    setCreateProductOrder(productOrder: ICreateProductOrder) {
      const { materialsCode, materialsName, materialId } = productOrder;
      this.createProductOrderDetail = {
        ...productOrder,
        materialCode: materialsCode,
        materialName: materialsName,
        materialId
      };
    },

    setActiveCreateProductOrderStep(step: CreateProductOrderStepEnum) {
      this.activeCreateProductOrderStep = step;
    },

    /** 生产订单可见性 */
    setProductOrderModalVisible(visible: boolean) {
      this.productOrderModalVisible = visible;
    },

    /** 生产订单详情可见性 */
    setProductOrderDetailVisible(visible: boolean) {
      this.productOrderDetailVisible = visible;
    },

    setCreateProductOrderFromBySoItemNo(fromBySoItemNo: boolean) {
      this.createProductOrderFromBySoItemNo = fromBySoItemNo;
    },
    setFillInDataModalVisible(visible: boolean) {
      this.fillInDataModalVisible = visible;
    },

    setDataMissingModalVisible(visible: boolean) {
      this.dataMissingModalVisible = visible;
    },

    setProductOrderFormAddMode(mode: boolean) {
      this.productOrderFormAddMode = mode;
    },
    setDeleteProductOrderStatus(status: boolean) {
      this.deleteProductOrderStatus = status;
    },

    initProductOrderDetail() {
      this.productOrderDetail = {};
      this.createProductOrderDetail = {};
    },
    delParamsElement(data: ICreateProductOrder) {
      if (data.salesLineDetails) {
        delete data.salesLineDetails;
      }
    }
  }
});
