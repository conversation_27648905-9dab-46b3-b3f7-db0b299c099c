import { ElNotification, NotificationHandle } from "element-plus";
import { h } from "vue";
import NotificationMessage from "./notification-message.vue";

export function useNotification() {
  let notificationHandle: NotificationHandle;
  let close: () => void;
  let timeoutHandle: ReturnType<typeof setTimeout>;

  const openNotification = () => {
    if (notificationHandle) {
      return;
    }
    notificationHandle = ElNotification({
      message: h(NotificationMessage),
      duration: 0,
      showClose: false,
      customClass: "el-notification__sync-purchase-order"
    });
  };

  const closeNotificationDelay = () => {
    if (!notificationHandle) {
      return;
    }
    close = notificationHandle.close;
    notificationHandle = null;
    timeoutHandle = setTimeout(() => {
      close();
      close = null;
      timeoutHandle = null;
    }, 2000);
  };

  const closeNotificationImmediate = () => {
    if (!close) {
      return;
    }
    close();
    clearTimeout(timeoutHandle);
    timeoutHandle = null;
    close = null;
  };

  return {
    openNotification,
    closeNotificationDelay,
    closeNotificationImmediate
  };
}
