import { TableColumnRenderer } from "@pureadmin/table";
import { useSchedulingPlanStore } from "@/store/modules";
import { ISchedulingPlanSaleOrderLine } from "@/models";
import { ElTooltip } from "element-plus";
import { FontIcon } from "@/components/ReIcon";
import { CreateSchedulingPlanStepEnum, TableWidth } from "@/enums";

export function useColumnsSalesDetail() {
  const schedulingPlanStore = useSchedulingPlanStore();
  const columns: TableColumnList = [
    {
      label: "销售订单号",
      prop: "soNo",
      width: TableWidth.order
    },
    {
      prop: "soItemNo",
      headerRenderer: () => {
        return (
          <div class="flex items-center">
            <span class="mr-1">销售订单行项目</span>
            <ElTooltip effect="dark" content="点击销售订单行项目号可快速添加排产计划" placement="top">
              <div class="w-5 text-center cursor-pointer">
                <FontIcon class="text-base text-secondary" icon="icon-warning-fill"></FontIcon>
              </div>
            </ElTooltip>
          </div>
        );
      },
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div class={"cursor-pointer text-primary"} onClick={() => onQuickSchedulingPlan(data.row)}>
            {data.row.soItemNo}
          </div>
        );
      }
    }
  ];

  const onQuickSchedulingPlan = (data: ISchedulingPlanSaleOrderLine) => {
    schedulingPlanStore.setSchedulingPlanFormValue({
      id: undefined,
      salesLineId: data.id,
      measUnit: data.materialUnit,
      purchaseId: undefined,
      ppNo: undefined,
      amount: data.materialNumber,
      planDate: undefined,
      planWorkDuration: undefined,
      deliveryDate: undefined,
      actStartDate: undefined,
      actEndDate: undefined
    });
    schedulingPlanStore.setSchedulingPlanFormAddMode(true);
    schedulingPlanStore.setIsQuickCreateSchedulingPlan(true);
    schedulingPlanStore.setAddSchedulingPlanModalVisible(true);
    schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.createSchedulingPlan);
  };
  return columns;
}
