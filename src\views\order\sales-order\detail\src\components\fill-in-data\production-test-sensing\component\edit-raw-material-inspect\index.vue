<template>
  <div class="edit-raw-material-dialog">
    <NormalEditRawMaterialInspect
      ref="editRawMaterialRef"
      v-if="isNormal"
      :notRefreshList="notRefreshList"
      @stateChange="compStateChange($event)"
    />
    <AcEditRawMaterialInspect
      ref="editRawMaterialRef"
      v-else
      :notRefreshList="notRefreshList"
      @stateChange="compStateChange($event)"
    />
  </div>
</template>

<script setup lang="ts">
import NormalEditRawMaterialInspect from "../../raw-material-group-unit-check/normal/add-raw-material/index.vue";
import AcEditRawMaterialInspect from "../../raw-material-group-unit-check/armour-clamp/add-raw-material/index.vue";
import { inject, onMounted, onUnmounted, ref } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";

// 传入的属性值
withDefaults(
  defineProps<{
    notRefreshList?: boolean;
  }>(),
  {}
);
// 定义抛出的事件
const emits = defineEmits<{
  (event: "stateChange", step: number): void;
}>();

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const rawMaterialStore = useRawMaterialGroupUnitStore();
const isNormal = ref<boolean>(true);
const editRawMaterialRef = ref<
  InstanceType<typeof NormalEditRawMaterialInspect> | InstanceType<typeof AcEditRawMaterialInspect>
>();

onMounted(() => {
  isNormal.value = prodCtx?.detailMaterialCategory === EMaterialCategory.Normal;
});

/** 数据抛出事件 */
const compStateChange = (step: number) => {
  emits("stateChange", step);
};

onUnmounted(() => {
  rawMaterialStore.initStoreData();
});

defineExpose({
  editRawMaterialRef
});
</script>

<style scoped lang="scss"></style>
