<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditFinishingWarehousingDialog ref="editFinishingWarehousingDialog" :subclass-code="subclassCode" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { IFinishedProductionSync } from "@/models";
import { useStateGridOrderSyncDetailListStore } from "@/store/modules";
import { useSync } from "@/views/order/purchase-order/detail/src/sg-order/detail/lists/hooks/useSync";
import EditFinishingWarehousingDialog from "@/views/components/state-grid-order-sync/dialogs/edit-finishing-warehousing-dialog.vue";
import { h, provide, reactive, ref } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useSubclassCode } from "@/views/order/purchase-order/detail/src/sg-order/detail/lists/hooks/useSubclassCode";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const type = StateGridOrderSyncType.FINISHED_PRODUCT;
const { sync, syncByDataId, getHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getHistoryByDataId,
  editFn: openEditDialog
});
// const { enumFormatter } = useTableCellFormatter();
const { subclassCode } = useSubclassCode();

// const purchaseOrderStore = usePurchaseOrderDetailStore();
const listStore = useStateGridOrderSyncDetailListStore();
listStore.setType(type);

const editFinishingWarehousingDialog = ref<InstanceType<typeof EditFinishingWarehousingDialog>>();

const columns: Array<TableColumns> = [
  {
    label: "生产订单号",
    prop: "ipoNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),
    minWidth: TableWidth.order
  },
  {
    label: "成品编号",
    prop: "productNo",
    minWidth: TableWidth.largeOrder
  },
  {
    label: "生产批次",
    prop: "productBatchNo",
    minWidth: TableWidth.largeOrder
  },
  // {
  //   label: "应用类型",
  //   prop: "applicationType",
  //   minWidth: TableWidth.order,
  //   formatter: enumFormatter(DeviceTerminalEnum, "deviceTerminalEnum"),
  //   hide: () => !purchaseOrderStore.isCable
  // },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

async function openEditDialog(sync: IFinishedProductionSync) {
  editFinishingWarehousingDialog.value.openEditDialog(sync);
}
</script>

<style scoped></style>
