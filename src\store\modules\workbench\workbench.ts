import { defineStore } from "pinia";
import { useEipReportStatisticsStore } from "@/store/modules/workbench/eip-report-statistics";
import {
  useAchievementStatisticsStore,
  usePurchaseOrderStatisticsStore,
  useSyncErrorStatisticsStore
} from "@/store/modules";

export const useWorkbenchStore = defineStore({
  id: "cx-workbench",
  state: () => ({}),
  actions: {
    refresh(loading = false) {
      useSyncErrorStatisticsStore().refresh(loading);
      useAchievementStatisticsStore().refresh(loading);
      usePurchaseOrderStatisticsStore().refresh(loading);
      useEipReportStatisticsStore().refresh(loading);
    }
  }
});
