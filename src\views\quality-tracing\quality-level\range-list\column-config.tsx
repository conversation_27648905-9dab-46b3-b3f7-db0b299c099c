import { ColumnWidth } from "@/enums";
import { ref } from "vue";

/**
 * @description: 生成质量等级公式表格配置
 */
export function genQualityLevelColumnsConfig() {
  const columnsConfig = ref<TableColumnList>([
    {
      label: "序号",
      prop: "index",
      width: ColumnWidth.Char4,
      fixed: "left",
      cell<PERSON><PERSON><PERSON>(data) {
        return <span>{data.index + 1}</span>;
      }
    },
    {
      label: "等级",
      prop: "level",
      align: "center",
      minWidth: ColumnWidth.Char6,
      slot: "level"
    },
    {
      label: "分值范围",
      prop: "range",
      align: "center",
      minWidth: ColumnWidth.Char29,
      showOverflowTooltip: false,
      slot: "range"
    },
    {
      label: "操作",
      prop: "opertion",
      align: "center",
      width: ColumnWidth.Char7,
      fixed: "right",
      slot: "opertion"
    }
  ]);

  return { columnsConfig };
}
