<template>
  <el-row>
    <el-col :span="20">
      <el-form class="!p-0" inline ref="formRef" :model="form">
        <el-form-item class="!mb-2 !mr-4" prop="keyWords">
          <el-input
            class="!w-64"
            clearable
            placeholder="请输入质量规范编号/名称"
            v-model="form.keyWords"
            @clear="onSearch"
          />
        </el-form-item>
        <el-form-item class="!mb-2">
          <el-button type="primary" @click="onSearch">搜索</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="4" class="text-right">
      <slot name="right" />
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance } from "element-plus";

/**
 * 质量规范列表搜索栏
 */

interface FormType {
  keyWords: string;
}

const emit = defineEmits<{
  (event: "handleSearch", value: FormType): void;
}>();

const form = reactive<FormType>({
  keyWords: ""
});

const formRef = ref<FormInstance>();

/**
 * @description: 搜索
 */
function onSearch() {
  emit("handleSearch", Object.assign({}, form));
}
</script>
<style lang="scss" scoped></style>
