import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { IWarningRule, IWarningRuleDto, IWarningRuleParams } from "@/models/gateway";

export const queryWarningRules = (params: Partial<IWarningRuleParams>) => {
  const url = withApiGateway(`admin-api/reporting/dataInterfaceScene/getSceneDetailList`);
  return http.post<Partial<IWarningRuleParams>, IResponse<Array<IWarningRule>>>(url, { data: params ?? {} });
};

export const updateWarningRule = (data: IWarningRuleDto) => {
  const url = withApiGateway(`admin-api/reporting/dataInterfaceScene/updateSceneWarningConfig`);
  return http.post<IWarningRuleDto, IResponse<boolean>>(url, { data }, { showErrorInDialog: true });
};
