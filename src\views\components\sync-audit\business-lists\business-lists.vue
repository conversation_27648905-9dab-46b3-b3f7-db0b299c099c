<template>
  <div>
    <div class="flex-bc mb-2.5">
      <TitleBar title="业务数据" />
      <CardCollapse />
    </div>
    <Card class="card" v-for="item in items" :key="item.id" :title="item.title" :id="item.id" v-model="item.collapsed">
      <component :is="item.component" :cardId="item.id" />
    </Card>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { computed, inject } from "vue";
import Card from "../card.vue";
import CardCollapse from "../card-collapse.vue";
import { syncAuditStateKey } from "../tokens";

const ctx = inject(syncAuditStateKey);
const items = computed(() => ctx.businessItems);
</script>

<style scoped>
.card + .card {
  @apply mt-5;
}
</style>
