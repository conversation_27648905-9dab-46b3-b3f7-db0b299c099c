import { defineStore } from "pinia";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { getSalesOrderLinesByPurchaseOrderId } from "@/api";
import * as salesOrderLineService from "@/api";
import { usePurchaseOrderDetailSalesOrderLineStore } from "@/store/modules/purchase-order-detail/sales-order-line";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail/sales-order";
import { ISalesOrderLineLinkDto } from "@/models";
import { ToggleStyleEnum } from "@/enums";

export const usePurchaseOrderDetailPurchaseOrderLineStore = defineStore({
  id: "cx-purchase-order-detail-purchase-order-line",
  state: () => ({
    activePurchaseOrderLineNo: "",
    toggleStyle: ToggleStyleEnum.MENU as ToggleStyleEnum
  }),
  getters: {
    lines: () => usePurchaseOrderDetailStore().purchaseOrder?.lines,
    activeSalesOrderId: () => usePurchaseOrderDetailSalesOrderStore().activeOrder?.id
  },
  actions: {
    async queryLinesByPurchaseOrderLine(purchaseOrderLineId: string) {
      return getSalesOrderLinesByPurchaseOrderId(purchaseOrderLineId).then(res => res.data);
    },
    async cancelSalesOrderLineLink(purchaseOrderLineId: string, salesOrderLineId: string) {
      return salesOrderLineService.cancelSalesOrderLineLink(purchaseOrderLineId, salesOrderLineId);
    },
    refreshSalesOrderLines() {
      return usePurchaseOrderDetailSalesOrderLineStore().refreshSalesOrderLines();
    },
    async linkSalesOrderLine(data: ISalesOrderLineLinkDto) {
      return salesOrderLineService.linkSalesOrderLine(data);
    },
    setActivePurchaseOrderLine(no: string): void {
      this.activePurchaseOrderLineNo = no;
    },
    setToggleStyle(toggleStyle: ToggleStyleEnum) {
      this.toggleStyle = toggleStyle;
    }
  }
});
