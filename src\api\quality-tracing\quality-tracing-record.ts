/**
 * @description: 质量追溯记录
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse } from "@/models";
import {
  BatchMatchingQualitySpecificationRes,
  BindingQualitySpecificationParams,
  QualityTracingCategoryRecordListItem,
  QualityTracingCategoryRecordListParams,
  QualityTracingRecordDetail,
  QualityTracingRecordLevelListItem,
  QualityTracingRecordLevelListParams,
  QualityTracingRecordListItem,
  QualityTracingRecordListParams
} from "@/models/quality-tracing";

/**
 * @description: 获取质量追溯记录等级列表
 */
export const getQualityTracingRecordLevelList = (params: QualityTracingRecordLevelListParams) => {
  return http.post<QualityTracingRecordLevelListParams, IResponse<Array<QualityTracingRecordLevelListItem>>>(
    withApiGateway(`admin-api/business/quality-traceability/count`),
    {
      data: params
    }
  );
};

/**
 * @description: 获取质量追溯记录列表
 */
export const getQualityTracingRecordList = (params: QualityTracingRecordListParams) => {
  return http.post<QualityTracingRecordListParams, IListResponse<QualityTracingRecordListItem>>(
    withApiGateway(`admin-api/business/quality-traceability/page`),
    {
      data: params
    }
  );
};

/**
 * @description: 生成生产订单或者工单的质量追溯记录
 * @param {string} id 生产订单或者工单ID
 */
export const genQualityTracingRecord = (id: string, subClassCode: string) => {
  return http.put<void, IResponse<boolean>>(
    withApiGateway(`admin-api/business/quality-traceability/calc/${id}?subClassCode=${subClassCode}`)
  );
};

/**
 * @description: 为生产订单/工单绑定质量规范
 */
export const bindingQualitySpecification = (params: BindingQualitySpecificationParams) => {
  return http.put<BindingQualitySpecificationParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/quality-traceability/maintenance-quality`),
    {
      params
    }
  );
};

/**
 * @description: 查询追溯记录详情
 */
export const getQualityTracingRecordDetail = (id: string) => {
  return http.get<string, IResponse<QualityTracingRecordDetail>>(
    withApiGateway(`admin-api/business/quality-traceability/detail/${id}`)
  );
};

/**
 * @description: 查询追溯记录详情分类的列表
 */
export const queryQualityTracingCategoryRecordList = (id: string, params: QualityTracingCategoryRecordListParams) => {
  return http.post<QualityTracingCategoryRecordListParams, IListResponse<QualityTracingCategoryRecordListItem>>(
    withApiGateway(`admin-api/business/quality-traceability/detail/page/${id}`),
    {
      data: params
    }
  );
};

/**
 * @description: 为所有生产订单/工单匹配质量规范
 */
export const batchMatchingQualitySpecification = () => {
  return http.post<void, IResponse<BatchMatchingQualitySpecificationRes>>(
    withApiGateway(`admin-api/business/quality-traceability/calc/auto`)
  );
};
