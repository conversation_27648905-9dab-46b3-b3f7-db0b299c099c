/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { UnitBasicInfoModel } from "@/models";
/**
 * @description: 单元基本信息列表
 */
export const UnitBasicInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<UnitBasicInfoModel>>>(
    withApiGateway(`admin-api/ejj/project/unit/list/${id}`)
  );
};

/**
 * @description: 单元基本信息创建
 */
export const UnitBasicCreateApi = (params: UnitBasicInfoModel) => {
  return http.post<UnitBasicInfoModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/unit`), {
    data: params
  });
};

/**
 * @description: 单元基本信息编辑
 */
export const UnitBasicEditApi = (params: UnitBasicInfoModel) => {
  return http.put<UnitBasicInfoModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/unit`), {
    data: params
  });
};

/**
 * @description: 单元基本信息删除
 */
export const UnitBasicDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/unit/${id}`));
};
