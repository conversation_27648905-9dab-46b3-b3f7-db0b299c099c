import { VoltageClassesEnum, VoltageTypeEnum } from "@/enums";
import { IOption } from "@/models";
import { formatEnum } from "./enum";

export const useVoltageTypeMapVoltageClasses = () => {
  function formatVoltageClassesEnum(voltageClassesEnum: VoltageClassesEnum) {
    return formatEnum(voltageClassesEnum, VoltageClassesEnum, "voltageClassesEnum");
  }

  const voltageTypeMapVoltageClasses: Record<VoltageTypeEnum, Array<IOption>> = {
    [VoltageTypeEnum.DIRECT_CURRENT]: [
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_160), value: VoltageClassesEnum.Voltage_160 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_200), value: VoltageClassesEnum.Voltage_200 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_320), value: VoltageClassesEnum.Voltage_320 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_400), value: VoltageClassesEnum.Voltage_400 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_1), value: VoltageClassesEnum.Voltage_1 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_6), value: VoltageClassesEnum.Voltage_6 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_15), value: VoltageClassesEnum.Voltage_15 }
    ],
    [VoltageTypeEnum.INTERFLOW]: [
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_10), value: VoltageClassesEnum.Voltage_10 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_20), value: VoltageClassesEnum.Voltage_20 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_35), value: VoltageClassesEnum.Voltage_35 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_66), value: VoltageClassesEnum.Voltage_66 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_110), value: VoltageClassesEnum.Voltage_110 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_220), value: VoltageClassesEnum.Voltage_220 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_330), value: VoltageClassesEnum.Voltage_330 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_500), value: VoltageClassesEnum.Voltage_500 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_1), value: VoltageClassesEnum.Voltage_1 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_6), value: VoltageClassesEnum.Voltage_6 },
      { label: formatVoltageClassesEnum(VoltageClassesEnum.Voltage_15), value: VoltageClassesEnum.Voltage_15 }
    ]
  };
  return voltageTypeMapVoltageClasses;
};
