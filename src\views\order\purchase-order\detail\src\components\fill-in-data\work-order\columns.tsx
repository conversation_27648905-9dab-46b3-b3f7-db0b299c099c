import { TableColumnRenderer } from "@pureadmin/table";
import { ProductionStateEnum, TableWidth } from "@/enums";
import { ElButton, ElMessage } from "element-plus";
import { IReportWork, IWorkOrder } from "@/models";
import { useReportWorkStore, useWorkOrderStore } from "@/store/modules";
import dayjs from "dayjs";
import { dateFormat, MEASURE_UNIT, PermissionKey, TrackPointKey } from "@/consts";
import { EnumCell } from "@/components/TableCells";
import { useConfirm } from "@/utils/useConfirm";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const reportWorkStore = useReportWorkStore();
  const workOrderStore = useWorkOrderStore();
  const { withUnitFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "工单编号",
      prop: "woNo",
      width: TableWidth.largeOrder,
      cellRenderer(data: TableColumnRenderer) {
        return (
          <div onClick={() => onViewWorkOrderDetail(data.row)} class="text-primary cursor-pointer">
            {data.row?.woNo}
          </div>
        );
      }
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.type
    },
    {
      label: "工序",
      prop: "processName",
      minWidth: TableWidth.name
    },
    {
      label: "生产数量",
      prop: "amount",
      width: TableWidth.type,
      align: "center",
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "计划日期",
      prop: "planStartDate",
      width: TableWidth.dateRanger,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {`${data.row?.planStartDate ? dayjs(data.row?.planStartDate).format(dateFormat) : ""}` +
              ` ～ ` +
              `${data.row?.planStartDate ? dayjs(data.row?.planFinishDate).format(dateFormat) : ""}`}
          </div>
        );
      }
    },
    {
      label: "工单状态",
      prop: "woStatus",
      fixed: "right",
      width: TableWidth.largeType,
      cellRenderer: (data: TableColumnRenderer) => (
        <div>{EnumCell(data, ProductionStateEnum, "productionStateEnum")}</div>
      )
    },
    {
      label: "报工条数",
      prop: "workCount",
      align: "center",
      fixed: "right",
      width: TableWidth.number,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div class={data.row?.workCount > 0 ? "cursor-pointer" : ""} onClick={() => onViewReportWork(data.row)}>
            <span class={data.row?.workCount > 0 ? "rounded-full px-4 bg-neutral text-bg_color" : ""}>
              {data.row?.workCount}
            </span>
          </div>
        );
      }
    },
    {
      label: "操作",
      prop: "op",
      fixed: "right",
      width: TableWidth.operations,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseWorkReportCreate}
              type="primary"
              link
              onClick={() => onAddWorkOrder(data.row)}
            >
              新增报工
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseWorkOrderEdit}
              v-track={TrackPointKey.FORM_PURCHASE_PWO_EDIT}
              type="primary"
              link
              onClick={() => onEditWorkOrder(data.row)}
            >
              编辑
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseWorkOrderDelete}
              v-track={TrackPointKey.FORM_PURCHASE_PWO_DELETE}
              link
              type="danger"
              onClick={() => onDeleteWorkOrder(data.row)}
            >
              删除
            </ElButton>
          </div>
        );
      }
    }
  ];

  const onViewReportWork = (workOrder: IWorkOrder) => {
    if (workOrder.workCount <= 0) {
      return;
    }
    workOrderStore.setWorkOrderDetail(workOrder);
    reportWorkStore.setReportWorkProps(workOrder.subclassCode, workOrder.processIds, workOrder.minClassCode);
    reportWorkStore.setReportWorkTableModalVis(true);
  };

  const onAddWorkOrder = async (workOrder: IWorkOrder) => {
    reportWorkStore.setReportWorkProps(workOrder.subclassCode, workOrder.processIds, workOrder.minClassCode);
    reportWorkStore.setIsAddReportWork(true);
    reportWorkStore.clearReportWorkDetailAndShowReportWorkFormModal();
    workOrderStore.setWorkOrderDetail(workOrder);
    const reportWork: IReportWork = await reportWorkStore.getLatestReportWorkById(workOrder.id);
    if (reportWork && Object.keys(reportWork).length) {
      reportWorkStore.setReportWorkDetail({ buyerProvince: reportWork.buyerProvince });
    }
  };

  const onViewWorkOrderDetail = async (data: IWorkOrder) => {
    await workOrderStore.getWorkOrderDetailById(data.id);
    workOrderStore.setWorkOrderDetailModalVis(true);
  };

  const onEditWorkOrder = async (data: IWorkOrder) => {
    workOrderStore.setWorkOrderFormModalVis(true);
    await workOrderStore.getWorkOrderDetailById(data.id);
    workOrderStore.setCreateWorkOrderDetail(workOrderStore.workOrderDetail);
  };

  const onDeleteWorkOrder = async (data: IWorkOrder) => {
    if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
      return;
    }

    await workOrderStore.deleteWorkOrder(data.id);
    ElMessage({ type: "success", message: "删除成功" });
    workOrderStore.refreshWorkOrders(1, true);
  };
  return { columns };
}
