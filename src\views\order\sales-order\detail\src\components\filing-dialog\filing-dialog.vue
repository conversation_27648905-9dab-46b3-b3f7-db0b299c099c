<template>
  <el-dialog
    title="归档备注"
    align-center
    class="default"
    destroy-on-close
    v-model="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="tip-text px-5 mb-3">
      <el-alert title="归档后的数据将不会自动同步和触发评分" type="warning" show-icon :closable="false" />
    </div>
    <FilingForm ref="filingInstance" />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="filingLoading" @click="filingConfirm()">确认归档</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import FilingForm from "./dialog-form.vue";
import { computed, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { useSalesOrderManagementStore } from "@/store/modules";

const props = defineProps<{
  modelValue?: boolean;
  id?: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "fillingSuccess"): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const salesOrderManagementStore = useSalesOrderManagementStore();
const filingInstance = ref<InstanceType<typeof FilingForm>>();
const filingLoading = ref<boolean>(false);
const filingConfirm = useLoadingFn(postFilingConfirm, filingLoading);

/** 归档数据 */
async function postFilingConfirm() {
  const form = await filingInstance.value?.getFormValue();
  if (form.remark) {
    const { remark } = form;
    await salesOrderManagementStore.saleOrderToggleFiling({ id: props.id, remark });
    visible.value = false;
    emit("fillingSuccess");
    ElMessage.success("归档成功");
  }
}
</script>

<style scoped lang="scss">
.tip-text {
  color: var(--el-color-warning);
}
</style>
