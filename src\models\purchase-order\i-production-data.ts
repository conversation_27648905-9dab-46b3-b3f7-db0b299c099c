import { IPagingReq } from "../i-paging-req";
import { TableColumnCtx } from "element-plus";
import { ISalesOrderLine } from "../sales-order-line";

export interface IProductionData extends IPagingReq {
  /** 生产订单号 */
  id: number;
  /** 销售订单号 */
  salesLineId: number;
  /** 销售订单行项目 */
  soItemNo: string;
  /** 物料名称 */
  materialsName: string;
  /** 电压等级 */
  voltageClasses: string;
  /** 需求数量 */
  amount: number;
  /** 计量单位 */
  unit: string;
  /** 计划日期 */
  planDate: string;
  /** 实际开始日期 */
  actualStartDate: string;
  /** 实际结束日期 */
  actualFinishDate: string;
}

export enum ProductDataEnum {
  all = 0,
  product = 1,
  noProduct = 2
}

export interface SpanMethodProps {
  row: ISaleOrdersLine;
  column: TableColumnCtx<ISaleOrdersLine>;
  rowIndex: number;
  columnIndex: number;
}

export interface ISaleOrdersLine extends ISalesOrderLine {
  productionOrder?: string;
  rowSpan?: number;
  colSpan?: number;
}

export interface ISaleOrders {
  saleOrderNo: string;
  list: Array<ISaleOrdersLine>;
}
