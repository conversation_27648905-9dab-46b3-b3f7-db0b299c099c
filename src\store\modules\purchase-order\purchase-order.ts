import { IListResponse, IResponse, IStatisticsPurchaseOrder } from "@/models";
import { defineStore } from "pinia";
import { IPurchaseOrder, IPurchaseOrderLinkStep, IPurchaseOrderReq } from "@/models/purchase-order";
import { LinkStepEnum } from "@/enums";
import dayjs from "dayjs";
import { fullDateFormat } from "@/consts";
import * as api from "@/api/purchase-order";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

const defaultPageSize = 10;

export const usePurchaseOrderStore = defineStore({
  id: "cx-purchase-order-store",
  state: () => ({
    purchaseOrders: [] as Array<IPurchaseOrder>,
    total: 0 as number,
    linkStep: {} as IPurchaseOrderLinkStep,
    queryParams: {} as IPurchaseOrderReq,
    statisticsPurchaseOrders: {} as IStatisticsPurchaseOrder,
    queryLoading: false as boolean,
    initQueryParams: {} as IPurchaseOrderReq
  }),
  actions: {
    async queryPurchaseOrders(params: IPurchaseOrderReq = { pageSize: defaultPageSize }) {
      this.queryParams = params;
      await this.postPurchaseOrders(params);
    },

    async queryPurchaseOrdersByFilter(params?: IPurchaseOrderReq) {
      Object.assign(
        this.queryParams,
        { pageNo: 1, pageSize: this.queryParams.pageSize || defaultPageSize },
        { sellerSignTime: params?.sellerSignTime?.map(d => dayjs(d).format(fullDateFormat)) },
        params
      );
      await this.postPurchaseOrders();
      await this.queryPurchaseLinkStep();
      await this.getStatisticsPurchaseOrders(this.queryParams);
    },

    /** 根据linkStep查询数据 */
    async queryPurchaseOrdersByLinkStep(linkStep?: LinkStepEnum) {
      this.queryParams.linkStep = linkStep || undefined;
      this.queryParams.pageNo = 1;
      await this.postPurchaseOrders();
    },

    async queryPurchaseOrdersByPageIndex(pageIndex = 1) {
      this.queryParams.pageNo = pageIndex;
      await this.postPurchaseOrders();
    },

    async queryPurchaseOrdersByPageSize(pageSize: number) {
      this.queryParams.pageSize = pageSize;
      await this.postPurchaseOrders();
    },

    /**
     * @description: 查询订单来源、快捷筛选数量统计
     */
    async getStatisticsPurchaseOrders() {
      const res: IResponse<IStatisticsPurchaseOrder> = await api.statisticsPurchaseOrderTotalAndUnRedCount({
        ...(omitBy(this.queryParams, val => isAllEmpty(val)) || {}),
        linkStep: undefined
      });
      this.statisticsPurchaseOrders = res.data;
    },

    async queryPurchaseOrderByChannel(params: IPurchaseOrderReq = { pageNo: 1 }) {
      Object.assign(this.queryParams, params);
      await this.postPurchaseOrders();
    },

    /** 仅看未读采购订单 */
    async queryUnReadPurchaseOrder(params: IPurchaseOrderReq = { pageNo: 1 }) {
      Object.assign(this.queryParams, params);
      await this.postPurchaseOrders();
    },

    /** 仅看关注采购订单 */
    async queryFollowPurchaseOrder(params: IPurchaseOrderReq = { pageNo: 1 }) {
      Object.assign(this.queryParams, params);
      await this.postPurchaseOrders();
    },

    async postPurchaseOrders() {
      this.queryLoading = true;
      const purchaseOrderRes: IListResponse<IPurchaseOrder> = await api.queryPurchaseOrder(
        omitBy(this.queryParams, val => isAllEmpty(val))
      );
      const purchaseOrders: Array<IPurchaseOrder> = purchaseOrderRes.data.list;
      this.purchaseOrders = purchaseOrders;
      this.total = purchaseOrderRes.data.total;
      this.queryLoading = false;
    },

    /**
     * @description: 查询采购订单步骤对应的单子数量
     */
    async queryPurchaseLinkStep(params?: IPurchaseOrderReq) {
      let queryParams: IPurchaseOrderReq = this.queryParams;
      if (params && Object.keys(params).length) {
        queryParams = omitBy({ ...queryParams, ...params }, val => isAllEmpty(val));
      }
      const linkStepRes: IResponse<IPurchaseOrderLinkStep> = await api.queryLinkStep({
        ...queryParams,
        linkStep: undefined
      });
      this.linkStep = linkStepRes.data;
    },

    async purchaseOrderFollow(id: string) {
      return api.purchaseOrderFollow(id);
    },

    async purchaseOrderFilling(purchaseId: string, remark?: string) {
      return api.purchaseOrderFilling(purchaseId, remark);
    },

    patchQueryParams(params: Partial<IPurchaseOrderReq>) {
      Object.assign(this.queryParams, { ...params });
    },

    setInitQueryParams(params: Partial<IPurchaseOrderReq>) {
      this.initQueryParams = params;
    }
  }
});
