<template>
  <header>
    <form class="flex items-center mb-4" @submit.prevent="search">
      <el-input
        clearable
        v-model="keyword"
        class="!w-1/2 mr-4"
        :prefix-icon="Search"
        placeholder="请输入销售订单号/销售订单行项目"
        @clear="search"
      />
      <el-button type="primary" native-type="submit">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </form>
    <div class="flex mb-4">
      <el-radio-group v-model="ctx.salesOrderLineStatus">
        <el-radio-button v-for="option of statusOptions" :label="option.value" :key="option.value">{{
          option.label
        }}</el-radio-button>
      </el-radio-group>
      <div class="switch-item ml-auto">
        <label for="salesOrderLineSelectMultiple" class="label">多选</label>
        <el-switch id="salesOrderLineSelectMultiple" v-model="ctx.multiple" />
      </div>
      <div v-if="ctx.requiredCrossOrderBtn" class="switch-item">
        <label for="salesOrderLineSelectCross" class="label">跨销售订单</label>
        <el-switch id="salesOrderLineSelectCross" v-model="ctx.crossOrder" />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { inject, ref, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { IOption } from "@/models";
import { SaleOrderLineStateEnum } from "@/enums";
import { salesOrderLineSelectKey } from "../tokens";
import { whenever } from "@vueuse/core";

const ctx = inject(salesOrderLineSelectKey);
const keyword = ref("");
const statusOptions: Array<IOption> = [
  { label: "全部", value: SaleOrderLineStateEnum.all },
  { label: "有生产", value: SaleOrderLineStateEnum.ProductOrWorkOrderOrScheduling },
  { label: "无生产", value: SaleOrderLineStateEnum.NoProductOrWorkOrderOrScheduling }
];

/** 关闭多选，自动关闭跨销售订单，切换多选，清空选中 */
watch(
  () => ctx.multiple,
  multiple => {
    if (!multiple) {
      ctx.crossOrder = false;
    }
    ctx.selectedLines.length = 0;
  }
);

/** 打开跨销售订单，自动打开多选 */
whenever(
  () => ctx.crossOrder,
  () => (ctx.multiple = true)
);

function search(): void {
  // 关键词相同不触发搜索，手动触发
  if (ctx.keyword === keyword.value) {
    ctx.refreshLines();
  } else {
    ctx.keyword = keyword.value;
  }
}

function reset(): void {
  keyword.value = "";
  search();
}
</script>

<style lang="scss" scoped>
.switch-item {
  @apply flex items-center mr-3;

  .label {
    @apply cursor-pointer mr-2;
  }
}
</style>
