import { IBase } from "@/models";

export interface ITypeExperimenItem extends IBase {
  /**
   * 型式试验Id
   */
  typeExperimentId?: string;
  /**
   * 参数编码
   */
  itemCode?: string;
  /**
   * 参数名称
   */
  itemName?: string;
  /**
   * 判定标准
   */
  verdictStandard?: string;
  /**
   * 技术参数值
   */
  itemValue?: string;
  /**
   * 上限值
   */
  upperLimitValue?: string;
  /**
   * 下限值
   */
  lowerLimitValue?: string;
}
