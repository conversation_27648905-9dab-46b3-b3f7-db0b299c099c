<template>
  <el-form
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    size="default"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工序" prop="processId">
          <ElSelect
            class="flex-1"
            v-model="formValue.processId"
            placeholder="请选择接工序"
            clearable
            filterable
            @change="processChange"
            @clear="clearChange"
          >
            <el-option
              v-for="item in processOptionsRef || []"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="设备" prop="deviceId">
          <ElSelect class="flex-1" v-model="formValue.deviceId" placeholder="请选择设备" filterable>
            <el-option
              v-for="item in deviceStore.options || []"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div class="flex">
                <span>{{ item.label }}</span>
                <span>({{ item.deviceCode }})</span>
              </div>
            </el-option>
          </ElSelect>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="报工批次号" prop="productBatchNo">
          <el-input v-model="formValue.productBatchNo" placeholder="请输入报工批次号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="报工地址" prop="buyerProvince">
          <el-input v-model="formValue.buyerProvince" placeholder="请输入报工地址" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="开始时间" prop="workStartTime" ref="workStartTimeFormItem">
          <el-date-picker
            v-model="formValue.workStartTime"
            label="开始时间"
            placeholder="请选择开始时间"
            class="flex-1"
            clearable
            type="datetime"
            :disabled-date="actualStartDateDisabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="结束时间" prop="workEndTime" ref="workEndTimeFormItem">
          <el-date-picker
            v-model="formValue.workEndTime"
            label="结束时间"
            placeholder="请选择结束时间"
            class="flex-1"
            clearable
            type="datetime"
            :disabled-date="actualEndDateDisabledDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect, computed, watch } from "vue";
import type { FormInstance, FormItemInstance, FormRules } from "element-plus";
import { ICreateReportWork, IOption } from "@/models";
import { useDeviceStore, useProcessStore, useReportWorkStore } from "@/store/modules";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { Validators } from "@/utils/form";
import { whenever } from "@vueuse/core";
import {
  REPORT_WORK_END_TIME_GREATER_THAN_NOW,
  REPORT_WORK_END_TIME_LESS_THAN_START_DATE,
  REPORT_WORK_START_TIME_GREATER_THAN_END_DATE,
  REPORT_WORK_START_TIME_GREATER_THAN_NOW
} from "@/consts";

const props = withDefaults(
  defineProps<{
    subclassCode: string;
    processIds: string;
    minClassCode?: string;
  }>(),
  {
    subclassCode: "",
    processIds: "",
    minClassCode: ""
  }
);

const processStore = useProcessStore();
const deviceStore = useDeviceStore();
const reportWorkStore = useReportWorkStore();

// 金具
const isArmourClamp = computed(() => usePurchaseOrderDetailStore()?.isArmourClamp);
processStore.queryProcess(!isArmourClamp.value ? props.subclassCode : props.minClassCode);

const processOptionsRef = ref<Array<IOption>>([]);
const ruleFormRef = ref<FormInstance>();
const formValue = reactive<ICreateReportWork>({
  id: undefined,
  workId: undefined,
  purchaseId: undefined,
  processId: undefined,
  productBatchNo: undefined,
  deviceId: undefined,
  workStartTime: undefined,
  workEndTime: undefined,
  buyerProvince: undefined
});
const workStartTimeFormItem = ref<FormItemInstance>();
const workEndTimeFormItem = ref<FormItemInstance>();
const rules: FormRules = {
  processId: [{ required: true, message: "工序不能为空", trigger: "change" }],
  productBatchNo: [{ required: true, message: "报工批次号不能为空", trigger: "blur" }],
  deviceId: [{ required: true, message: "设备不能为空", trigger: "change" }],
  workStartTime: [
    { message: "报工开始时间不能为空", trigger: "change", required: true },
    {
      message: REPORT_WORK_START_TIME_GREATER_THAN_NOW,
      trigger: "change",
      validator: Validators.maxDate(() => new Date())
    },
    {
      message: REPORT_WORK_START_TIME_GREATER_THAN_END_DATE,
      trigger: "change",
      validator: Validators.maxDateWithoutBoundary(() => formValue.workEndTime)
    }
  ],
  workEndTime: [
    {
      message: REPORT_WORK_END_TIME_GREATER_THAN_NOW,
      trigger: "change",
      validator: Validators.maxDate(() => new Date())
    },
    {
      message: REPORT_WORK_END_TIME_LESS_THAN_START_DATE,
      trigger: "change",
      validator: Validators.minDateWithoutBoundary(() => formValue.workStartTime)
    }
  ],
  buyerProvince: [{ required: true, message: "报工地址不能为空", trigger: "change" }]
};

watchEffect(() => {
  if (reportWorkStore.reportWorkDetail && Object.keys(reportWorkStore.reportWorkDetail).length) {
    Object.assign(formValue, reportWorkStore.reportWorkDetail);
  }

  if (!props.processIds) {
    return;
  }

  const processIdArr: Array<string> = props.processIds.split("-");
  processOptionsRef.value = processStore.options?.filter(x => processIdArr.includes(x.value as string)) || [];
  if (processOptionsRef.value.length === 1) {
    formValue.processId = processOptionsRef.value[0].value as string;
  }
});

watch(
  () => formValue.processId,
  newVal => {
    if (newVal) {
      // 根据工序获取设备信息
      deviceStore.queryDeviceList({ processIds: [newVal] });
    }
  },
  // 初始化时，如果有已选中工序应该自动获取设备列表
  {
    immediate: true
  }
);

whenever(
  () => workStartTimeFormItem.value?.validateState === "success",
  () => {
    if (workEndTimeFormItem.value?.validateMessage === REPORT_WORK_END_TIME_LESS_THAN_START_DATE) {
      workEndTimeFormItem.value.clearValidate();
    }
  }
);
whenever(
  () => workEndTimeFormItem.value?.validateState === "success",
  () => {
    if (workStartTimeFormItem.value?.validateMessage === REPORT_WORK_START_TIME_GREATER_THAN_END_DATE) {
      workStartTimeFormItem.value.clearValidate();
    }
  }
);

const resetFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields();
};

const getFormValue = async (): Promise<boolean | ICreateReportWork> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});
  if (!valid) {
    return valid;
  }
  return {
    ...formValue
  };
};

const actualStartDateDisabledDate = (date: Date) => {
  if (!formValue.workEndTime) {
    return date > new Date();
  }

  const workEndTime =
    typeof formValue.workEndTime === "string" ? new Date(formValue.workEndTime) : formValue.workEndTime;

  return workEndTime ? date > workEndTime : false;
};

const actualEndDateDisabledDate = (date: Date) => {
  if (!formValue.workStartTime) {
    return date > new Date();
  }

  const workStartTime =
    typeof formValue.workStartTime === "string" ? new Date(formValue.workStartTime) : formValue.workStartTime;

  const hours = workStartTime.getHours();
  const minutes = workStartTime.getMinutes();
  const seconds = workStartTime.getSeconds();
  date.setHours(hours);
  date.setMinutes(minutes);
  date.setSeconds(seconds);
  return !(date <= new Date() && date >= workStartTime);
};

/** 清除工序 */
function clearChange() {
  resetDeviceId();
  deviceStore.queryDeviceList();
}

function processChange() {
  resetDeviceId();
}

function resetDeviceId() {
  formValue.deviceId = undefined;
}

defineExpose({
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss"></style>
