import { withApiGateway } from "@/api/util";
import { http } from "@/utils/http";
import type { IOrderSyncChannel, IOrderSyncChannelReq, IResponse } from "@/models";

/** 获取订单同步渠道 */
export const getOrderSyncChannel = (data: IOrderSyncChannelReq) => {
  const url: string = withApiGateway("admin-api/business/sync/permission");
  return http.post<IOrderSyncChannelReq, IResponse<IOrderSyncChannel>>(url, { data });
};
