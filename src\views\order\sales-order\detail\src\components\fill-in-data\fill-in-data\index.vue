<template>
  <el-scrollbar class="w-full" ref="fillInDataScrollRef" wrap-class="fill-in-data-scroll">
    <el-collapse v-model="state.collapseItems" class="work-order-detail-collapse">
      <BaseInfo />
      <!-- 工单与报工 -->
      <WorkOrderAndReportWork
        :mode="salesOrderDetailStore.isCable ? 'all' : 'report-work'"
        :production-id="productionId"
        :work-id="workId"
        :sub-class-code="subClassCode"
        :production-no="productionNo"
      />
      <!-- 生产试验感知 -->
      <ProductionTestSensing />
      <div v-if="showStandardAndProcessDocument">
        <!-- 技术标准 -->
        <TechnicalStandard />
        <!-- 附件报告--工序文档 -->
        <AttachedReport />
      </div>
    </el-collapse>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";
import { computedAsync } from "@vueuse/core";
import BaseInfo from "@/views/order/sales-order/detail/src/components/fill-in-data/items/base-info/base-info.vue";
import WorkOrderAndReportWork from "@/views/components/work-order-and-report-work/index.vue";
import ProductionTestSensing from "@/views/order/sales-order/detail/src/components/fill-in-data/items/production-test-sensing/production-test-sensing.vue";
import TechnicalStandard from "@/views/order/sales-order/detail/src/components/fill-in-data/items/technical-standard/index.vue";
import AttachedReport from "@/views/order/sales-order/detail/src/components/fill-in-data/items/attached-report/index.vue";
import { FillInDataOfCollapseNameEnum } from "./types";
import {
  useSalesOrderDetailStore,
  useSalesProductOrderStore,
  useSystemAuthStore,
  useSalesFillInDataStore
} from "@/store/modules";
import { useRoute } from "vue-router";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";

const route = useRoute();

const systemAuthStore = useSystemAuthStore();

const state = reactive<{
  collapseItems: Array<string>;
}>({
  collapseItems: [
    FillInDataOfCollapseNameEnum.PRODUCT_ORDER_DETAIL,
    FillInDataOfCollapseNameEnum.WORK_ORDER_LIST,
    FillInDataOfCollapseNameEnum.PRODUCTION_TEST_SENSING,
    FillInDataOfCollapseNameEnum.TECHNICAL_STANDARD,
    FillInDataOfCollapseNameEnum.ATTACHED_REPORT
  ]
});

const fillInDataScrollRef = ref(null);
const fillInDataStore = useSalesFillInDataStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const salesProductOrderStore = useSalesProductOrderStore();
const showStandardAndProcessDocument = computedAsync(async () => {
  return (
    (await systemAuthStore.checkLicenseAuthIncludeIOT) &&
    salesOrderDetailStore.isCable &&
    salesOrderDetailStore.salesOrder.matSyncFlagId !== SynchronousFlagEnum.CSG_GuangZhou_Flag
  );
});

const subClassCode = computed(() => {
  if (fillInDataStore.data) {
    return fillInDataStore.data.subClassCode || fillInDataStore.data.subclassCode;
  }
  return "";
});

const productionNo = computed(() => {
  if (fillInDataStore.data) {
    return fillInDataStore.data.ipoNo;
  }
  return "";
});

const productionId = computed(() =>
  salesOrderDetailStore.isCable ? fillInDataStore.dataId : fillInDataStore.productionId
);

const workId = computed(() => (!salesOrderDetailStore.isCable ? fillInDataStore.dataId : undefined));

// 监听到路由变化时，重置state,防止页面后退再打开dialog时，数据未清除
watch(route, () => {
  salesProductOrderStore.$reset();
});
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
