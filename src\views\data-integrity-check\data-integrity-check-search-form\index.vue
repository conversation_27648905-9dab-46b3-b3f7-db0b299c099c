<template>
  <div>
    <div class="flex">
      <div>
        关键词：
        <el-input
          class="!w-96"
          clearable
          v-model.trim="filter.keyWords"
          :placeholder="placeholder"
          @clear="emits('clear')"
        />
      </div>
      <div class="mx-5">
        生产状态：
        <el-select
          v-model="filter.productionStatus"
          placeholder="请选择生产状态"
          clearable
          filterable
          @clear="emits('clear')"
        >
          <el-option v-for="item in ProductionStateoption" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="mx-5">
        缺失项：
        <el-select
          v-model="filter.dataMissing"
          placeholder="请选择数据缺失项"
          multiple
          clearable
          filterable
          collapse-tags
          @clear="emits('clear')"
        >
          <el-option v-for="item in missData" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="mt-2">
      同步状态：
      <el-radio-group v-model="filter.dataCheckEipSyncStatus">
        <el-radio :label="null" border @click.prevent="handleRadioClick(null, 'dataCheckEipSyncStatus')">
          全部
        </el-radio>
        <el-radio
          :label="IntegritySyncStatusEnum.NO_SYNC"
          border
          @click.prevent="handleRadioClick(IntegritySyncStatusEnum.NO_SYNC, 'dataCheckEipSyncStatus')"
        >
          {{ IntegritySyncStatusName[IntegritySyncStatusEnum.NO_SYNC] }}
        </el-radio>
        <el-radio
          :label="IntegritySyncStatusEnum.ALL_SYNCED"
          border
          @click.prevent="handleRadioClick(IntegritySyncStatusEnum.ALL_SYNCED, 'dataCheckEipSyncStatus')"
        >
          {{ IntegritySyncStatusName[IntegritySyncStatusEnum.ALL_SYNCED] }}
        </el-radio>
        <el-radio
          :label="IntegritySyncStatusEnum.PART_SYNCED"
          border
          @click.prevent="handleRadioClick(IntegritySyncStatusEnum.PART_SYNCED, 'dataCheckEipSyncStatus')"
        >
          {{ IntegritySyncStatusName[IntegritySyncStatusEnum.PART_SYNCED] }}
        </el-radio>
        <el-radio
          :label="IntegritySyncStatusEnum.SYNC_FALUT"
          border
          @click.prevent="handleRadioClick(IntegritySyncStatusEnum.SYNC_FALUT, 'dataCheckEipSyncStatus')"
        >
          {{ IntegritySyncStatusName[IntegritySyncStatusEnum.SYNC_FALUT] }}
        </el-radio>
      </el-radio-group>
    </div>
    <div class="mt-2">
      评分状态：
      <el-radio-group v-model="filter.eipTriggerStatus">
        <el-radio :label="null" border @click.prevent="handleRadioClick(null, 'eipTriggerStatus')"> 全部 </el-radio>
        <el-radio
          :label="IntegrityTriggerScoreEnum.NO_TRIGGER"
          border
          @click.prevent="handleRadioClick(IntegrityTriggerScoreEnum.NO_TRIGGER, 'eipTriggerStatus')"
        >
          {{ IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.NO_TRIGGER] }}
        </el-radio>
        <el-radio
          :label="IntegrityTriggerScoreEnum.TRIGGER_SUCCESS"
          border
          @click.prevent="handleRadioClick(IntegrityTriggerScoreEnum.TRIGGER_SUCCESS, 'eipTriggerStatus')"
        >
          {{ IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.TRIGGER_SUCCESS] }}
        </el-radio>
        <el-radio
          :label="IntegrityTriggerScoreEnum.TRIGGER_FAIL"
          border
          @click.prevent="handleRadioClick(IntegrityTriggerScoreEnum.TRIGGER_FAIL, 'eipTriggerStatus')"
        >
          {{ IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.TRIGGER_FAIL] }}
        </el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IDataIntegrityCheckParams, IOption } from "@/models";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateoption } from "@/enums";
import {
  IntegritySyncStatusEnum,
  IntegritySyncStatusName,
  IntegrityTriggerScoreEnum,
  IntegrityTriggerScoreName
} from "@/enums/data-integrity-check";
import { computedAsync, useVModel } from "@vueuse/core";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";

const props = defineProps<{
  missData: Array<IOption>;
  modelValue: IDataIntegrityCheckParams;
}>();

const keyWordAliasHook = usekeyWordAliasHook();
const emits = defineEmits(["update:modelValue", "clear", "radioClick"]);
const filter = useVModel(props, "modelValue", emits);

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `请输入采购订单号/销售订单号/${KeywordAliasEnum.IPO_NO}`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

function handleRadioClick(v: number | null, field: string) {
  filter.value[field] = v;
  emits("clear");
}
</script>

<style scoped>
:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}
</style>
