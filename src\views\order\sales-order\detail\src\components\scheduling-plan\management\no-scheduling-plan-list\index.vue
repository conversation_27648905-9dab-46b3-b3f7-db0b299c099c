<template>
  <div class="text-middle mb-1">
    <span class="mr-1">销售行</span>
    <span>{{ item?.soItemNo }}</span>
  </div>
  <div class="flex-bc">
    <div class="w-[200px] xl:w-[240px] text-secondary text-base">
      <span class="mr-1">销售订单号</span>
      <span>{{ item?.soNo }}</span>
    </div>
    <el-button
      v-auth="PermissionKey.form.formPurchasePlanBtnQuickCreate"
      v-track="TrackPointKey.FORM_PURCHASE_PLAN_FAST_CREATE"
      size="small"
      type="warning"
      @click="onQuickSchedulingPlan(item)"
      >快速创建</el-button
    >
  </div>
</template>

<script setup lang="ts">
import { ISalesOrderLine, ISchedulingPlanSaleOrderLine } from "@/models";
import { useSalesSchedulingPlanStore } from "@/store/modules";
import { CreateSchedulingPlanStepEnum } from "@/enums";
import { computed } from "vue";
import { PermissionKey, TrackPointKey } from "@/consts";

const props = defineProps<{
  item: ISalesOrderLine;
}>();

const item = computed(() => props.item);

const schedulingPlanStore = useSalesSchedulingPlanStore();

const onQuickSchedulingPlan = (data: ISchedulingPlanSaleOrderLine) => {
  schedulingPlanStore.setSchedulingPlanFormValue({
    id: undefined,
    salesLineId: data.id,
    measUnit: data.materialUnit,
    purchaseId: undefined,
    ppNo: undefined,
    amount: data.materialNumber,
    planDate: undefined,
    planWorkDuration: undefined,
    deliveryDate: undefined,
    actStartDate: undefined,
    actEndDate: undefined,
    subClassCode: data.subClassCode
  });
  schedulingPlanStore.setIsQuickCreateSchedulingPlan(true);
  schedulingPlanStore.setAddSchedulingPlanModalVisible(true);
  schedulingPlanStore.setSchedulingPlanFormAddMode(true);
  schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.createSchedulingPlan);
};
</script>
