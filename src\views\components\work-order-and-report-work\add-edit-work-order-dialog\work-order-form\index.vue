<template>
  <el-form
    ref="formRef"
    :model="formValue"
    :rules="rules"
    label-position="top"
    :validate-on-rule-change="false"
    require-asterisk-position="right"
  >
    <el-row>
      <el-col :span="24">
        <div class="panel">
          <div class="flex items-center gap-2 mb-2">
            <font-icon icon="icon-classify-fill" class="text-primary text-middle" />
            <div class="text-text_color_primary font-semibold text-middle">物料编号 {{ formValue.materialsCode }}</div>
          </div>
          <el-descriptions>
            <el-descriptions-item :span="2" label="物料名称">{{ formValue.materialName }} </el-descriptions-item>
            <el-descriptions-item label="物料单位">
              <dictionary-name
                :sub-class-code="subClassCode"
                :code="formValue.materialUnit"
                :parent-code="MEASURE_UNIT"
              />
            </el-descriptions-item>
            <el-descriptions-item :span="3" label="物料描述">{{ formValue.materialDesc }} </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工单编号" prop="woNo">
          <serial-number
            v-model="formValue.woNo"
            :code="PRODUCT_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :create="isAddMode"
            :parent-no="productionNo"
            placeholder="请输入工单编号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工艺路线" prop="processRouteNo">
          <process-route :disabled="isEditMode" v-model="formValue.processRouteNo" :sub-class-code="subClassCode" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item class="flex-1 gap-y-10" label="工序" prop="processIds">
          <process-selector
            :disabled="isEditMode"
            :process-route="formValue.processRouteNo"
            :sub-class-code="subClassCode"
            v-model="formValue.processIds"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="生产数量" prop="amount">
          <el-input-number
            :min="0"
            v-model="formValue.amount"
            controls-position="right"
            placeholder="请输入生产数量"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="计量单位" prop="unit">
          <dictionary
            :parent-code="MEASURE_UNIT"
            :sub-class-code="subClassCode"
            class="w-full"
            placeholder="请选择计量单位"
            v-model="formValue.unit"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1" label="规格型号" prop="specificationType">
          <el-input v-model="formValue.specificationType" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="电压等级" prop="voltageLevel">
          <enum-select
            class="flex-1"
            v-model="formValue.voltageLevel"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enum-name="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工单状态" prop="woStatus">
          <enum-select
            class="flex-1"
            v-model="formValue.woStatus"
            placeholder="请选择接工单状态"
            :enum="ProductionStateEnum"
            enum-name="productionStateEnum"
            clearable
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="计划日期" prop="planDateArray">
          <el-date-picker
            v-model="formValue.planDateArray"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            clearable
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1" label="实际开始日期" prop="actualStartDate">
          <el-date-picker
            v-model="formValue.actualStartDate"
            placeholder="请选择实际开始日期"
            class="flex-1"
            clearable
            :disabled-date="calcDisabledActualStartDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1" label="实际完成日期" prop="actualFinishDate">
          <el-date-picker
            v-model="formValue.actualFinishDate"
            placeholder="请选择实际完成日期"
            class="flex-1"
            clearable
            :disabled-date="calcDisabledActualEndDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import type { FormInstance } from "element-plus";
import EnumSelect from "@/components/EnumSelect";
import { ProductionStateEnum, VoltageClassesEnum } from "@/enums";
import { PRODUCT_ORDER_NO_CODE, MEASURE_UNIT } from "@/consts";
import SerialNumber from "@/components/SerialNumber";
import Dictionary from "@/components/Dictionary";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";
import ProcessRoute from "@/views/order/components/process-route/process-route.vue";
import { genWorkOrderFormRules, ICreateWorkOrderExe } from "./rules-config";
import ProcessSelector from "@/views/components/process-selector/index.vue";

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add";
  /** 物资种类code */
  subClassCode: string;
  /** 生产订单编号, 新增模式必传 */
  productionNo?: string;
}>();

/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");

const formRef = ref<FormInstance>();

const formValue = reactive<ICreateWorkOrderExe>({
  /** 工单id */
  id: undefined,
  /** 子种类 */
  minClassCode: undefined,
  /** 产品名称 */
  productName: undefined,
  /** 质量追溯码 */
  qualityTraceCode: undefined,
  /** 工单编号 */
  woNo: undefined,
  /** 产品编号 */
  productCode: undefined,
  /** 工艺路线编号 */
  processRouteNo: "",
  /** 工艺路线id */
  processRouteId: undefined,
  /** 生产数量 */
  amount: undefined,
  /** 计量单位 */
  unit: undefined,
  /** 物料编号 */
  materialsCode: undefined,
  /** 物料名称 */
  materialName: undefined,
  /** 物料描述 */
  materialDesc: undefined,
  /** 物料单位 */
  materialUnit: undefined,
  /** 规格型号 */
  specificationType: undefined,
  /** 电压等级 */
  voltageLevel: undefined,
  /** 工单状态 */
  woStatus: ProductionStateEnum.CREATE,
  /** 计划开始时间 */
  planStartDate: undefined,
  /** 计划完成时间 */
  planFinishDate: undefined,
  /** 实际开始时间 */
  actualStartDate: undefined,
  /** 实际完成时间 */
  actualFinishDate: undefined,
  /** 工序id */
  processIds: [],
  /** 生产订单id */
  productionId: undefined,
  //
  ipoNo: undefined,
  salesLineId: undefined,
  purchaseId: undefined,
  /** 物资种类code */
  subclassCode: undefined,
  /** 计划完成日期数组 */
  planDateArray: []
});

const rules = genWorkOrderFormRules(formValue);

/**
 * @description: 计算需要被禁用的实际开始日期
 */
function calcDisabledActualStartDate(date: Date) {
  if (date > new Date()) {
    return true;
  }
  return formValue.actualFinishDate
    ? date >
        (typeof formValue.actualFinishDate === "string"
          ? new Date(formValue.actualFinishDate)
          : formValue.actualFinishDate)
    : false;
}

/**
 * @description: 计算需要被禁用的实际结束日期
 */
function calcDisabledActualEndDate(date: Date) {
  if (date > new Date()) {
    return true;
  }

  return formValue.actualStartDate
    ? date <
        (typeof formValue.actualStartDate === "string"
          ? new Date(formValue.actualStartDate)
          : formValue.actualStartDate)
    : false;
}

/**
 * @description: 验证表单，并返回验证结果
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单值
 */
function initFormValue(v: any) {
  Object.assign(formValue, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formValue);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped lang="scss">
.panel {
  padding: 12px 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #dcdfe6;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.12);
}
</style>
