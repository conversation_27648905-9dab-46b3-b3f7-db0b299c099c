import { TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const baseColumn: TableColumnList = [
    {
      label: "序号",
      type: "index",
      align: "center",
      width: TableWidth.index
    },
    {
      label: "工序",
      prop: "processName",
      width: TableWidth.type
    },
    {
      label: "标准采集点",
      prop: "metadataModelName",
      minWidth: TableWidth.name
    },
    {
      label: "关键采集点",
      prop: "required",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const required: boolean = data.row.required;
        return required ? <CxTag type="success">是</CxTag> : <CxTag type="danger">否</CxTag>;
      }
    },
    {
      label: "计量单位",
      prop: "unit",
      width: TableWidth.unit
    }
  ];

  /** 展示列 */
  const showColumns: TableColumnList = [
    ...baseColumn,
    {
      label: "采集范围",
      prop: "collectRange",
      slot: "collectRange",
      width: TableWidth.name
    }
  ];

  /** 编辑列 */
  const editColumns: TableColumnList = [
    ...baseColumn,
    {
      label: "下限",
      prop: "minValue",
      slot: "minValue",
      fixed: "right",
      width: TableWidth.name
    },
    {
      label: "上限",
      prop: "maxValue",
      slot: "maxValue",
      fixed: "right",
      width: TableWidth.name
    }
  ];

  return { showColumns, editColumns };
}
