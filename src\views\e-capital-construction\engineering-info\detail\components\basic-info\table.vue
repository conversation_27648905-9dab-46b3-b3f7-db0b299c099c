<template>
  <!-- 原材料组件部基本信息 -->
  <div class="flex justify-end pb-3">
    <el-button
      :loading="loading"
      v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
      type="primary"
      @click="handleSynchronization"
      >一键同步</el-button
    >
    <el-button :loading="loading" :icon="Plus" type="primary" @click="handleAdd">新增</el-button>
  </div>
  <PureTable
    row-key="id"
    :data="tableData"
    class="flex-1 overflow-hidden pagination"
    :columns="columnsConfig"
    size="large"
    :loading="loading"
    showOverflowTooltip
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
    <template #pullStatus="data">
      <el-tag
        :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
        @click="handleShowDetail(data)"
        class="cursor-pointer"
      >
        {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
      </el-tag>
    </template>
    <template #operation="data">
      <ElButton type="primary" link @click="handleEdit(data.row)"> 编辑 </ElButton>
      <ElButton type="danger" link @click="handleDelete(data.row.id)"> 删除 </ElButton>
    </template>
  </PureTable>
  <!-- 同步明细 -->
  <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
  <el-dialog
    v-model="state.dialogShow"
    :title="state.isEdit ? '编辑' : '新增'"
    width="850px"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCloseDialog()"
  >
    <edit-form :loading="loading" ref="tableFormRef" :detail="state.selectRow" :isEdit="state.isEdit" />
    <template #footer>
      <span>
        <el-button :loading="loading" @click="handleCloseDialog()">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSave(false)">保存</el-button>
        <el-button v-if="!state.isEdit" :loading="loading" type="primary" @click="handleSave(true)"
          >保存并新增</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useConfirm } from "@/utils/useConfirm";
import { Plus } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useColumns } from "./column-config";
import EditForm from "./edit-form.vue";
import { PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
// utils
import {
  BasicInfoApi,
  BasicInfoCreateApi,
  BasicInfoEditApi,
  BasicInfoDeleteApi,
  IntermediateSyncByVoucherTypeApi
} from "@/api/e-capital-construction/engineering-info/index";

import { BasicInfoModel } from "@/models";
import { EquipmentTypeEnumExt } from "@/enums";
import { PermissionKey } from "@/consts";

const route = useRoute();

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

const columnsConfig = useColumns(isCombiner);

const loading = ref(false);
const tableFormRef = ref();

const tableData = ref([] as Array<BasicInfoModel>);

const state = reactive({
  isEdit: false,
  dialogShow: false,
  selectRow: {} as BasicInfoModel
});
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("BasicInfo", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await BasicInfoApi(route.query.id as string);
  tableData.value = data;
}, loading);

/**
 * @description: 新增
 */
const handleAdd = () => {
  state.dialogShow = true;
  state.selectRow = {
    monitoringTypesSelect: []
  } as BasicInfoModel;
  state.isEdit = false;
};

/**
 * @description: 取消
 */
const handleCloseDialog = () => {
  state.dialogShow = false;
  state.isEdit = false;
};

/**
 * @description: 编辑
 */
const handleEdit = async (row: BasicInfoModel) => {
  state.selectRow = row as BasicInfoModel;
  state.isEdit = true;
  state.dialogShow = true;
};

/**
 * @description: 删除
 */
const handleDelete = async (id: string) => {
  if (!(await useConfirm("是否确认删除", "删除确认"))) {
    return;
  }
  onDelete(id);
};

const onDelete = useLoadingFn(async (id: string) => {
  const { data } = await BasicInfoDeleteApi(id as string);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
}, loading);

/**
 * @description: 保存
 */
const handleSave = useLoadingFn(async (isAdd: boolean) => {
  if (!tableFormRef.value) {
    return;
  }
  const valid = await tableFormRef.value.validateForm();
  if (!valid) {
    return;
  }
  const params = {
    ...tableFormRef.value.getFormValue(),
    equipmentId: route.query.id as string,
    equipmentType: route.query.type as string
  };
  const { data } = state.isEdit ? await BasicInfoEditApi(params) : await BasicInfoCreateApi(params);
  if (data) {
    ElMessage.success(state.isEdit ? "编辑成功 ！" : "新增成功！");
    requestList();
    state.isEdit = false;
    if (isAdd) {
      tableFormRef.value.resetFormValue();
    } else {
      state.dialogShow = false;
    }
  }
}, loading);

onMounted(() => {
  requestList();
});
</script>

<style lang="scss" scoped>
.label {
  @apply text-base text-secondary mb-2;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
