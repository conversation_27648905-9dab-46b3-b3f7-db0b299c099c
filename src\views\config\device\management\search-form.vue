<template>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    placeholder="请输入设备编号/设备名称"
    @search="onSearch()"
  >
    <ElButton
      size="large"
      type="primary"
      v-if="licenseAuthIncludeIOT"
      v-auth="PermissionKey.meta.metaDeviceSyncIot"
      v-track="TrackPointKey.META_DEVICE_CREATE"
      @click="onKeySynchronization()"
    >
      <template #icon>
        <FontIcon icon="icon-sync" />
      </template>
      一键同步
    </ElButton>
    <el-tooltip content="已达到租户最大设备数量，无法新增" :disabled="deviceStore.allowAdd">
      <template #default>
        <ElButton
          size="large"
          type="primary"
          v-auth="PermissionKey.meta.metaDeviceCreate"
          v-track="TrackPointKey.META_DEVICE_CREATE"
          :icon="Plus"
          @click="onAddDeviceModalVis()"
          :disabled="!deviceStore.allowAdd"
        >
          新增设备
        </ElButton>
      </template>
    </el-tooltip>
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm/src/search-form.vue";
import { IKeywordField, ISearchItem } from "@/components/SearchForm";
import { defineComponent, h, onMounted, reactive } from "vue";
import { ElDatePicker, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox, ElMessage } from "element-plus";
import { FunctionTypeEnum, DeviceAcceptanceStatusEnum, DeviceAcceptanceStatusMapName } from "@/enums";
import { IDeviceReq } from "@/models";
import { Plus } from "@element-plus/icons-vue";
import { CHANNEL_IOT, PermissionKey, TrackPointKey } from "@/consts";
import { formatEnum } from "@/utils/format";
import { useConfirm } from "@/utils/useConfirm";
import { useDeviceStore, useSystemAuthStore } from "@/store/modules";
import { computedAsync } from "@vueuse/core";

const deviceStore = useDeviceStore();
const systemAuthStore = useSystemAuthStore();

const form = reactive({
  deviceCode: undefined,
  deviceName: undefined,
  orFilters: [],
  functionType: undefined,
  purchaseTime: undefined,
  acceptanceStatusList: [],
  gwAcceptanceStatus: false,
  iotAcceptanceStatus: false
});

const emits = defineEmits<{
  (e: "onSearch", params: IDeviceReq): void;
  (e: "onAddDevice"): void;
}>();

const licenseAuthIncludeIOT = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeIOT);

const FunctionType = defineComponent({
  name: "functionType",
  render() {
    const radios = [
      { label: "全部", value: undefined },
      {
        label: formatEnum(FunctionTypeEnum.PRODUCTION_DEVICE, FunctionTypeEnum, "FunctionTypeEnum"),
        value: FunctionTypeEnum.PRODUCTION_DEVICE
      },
      {
        label: formatEnum(FunctionTypeEnum.TEST_DEVICE, FunctionTypeEnum, "FunctionTypeEnum"),
        value: FunctionTypeEnum.TEST_DEVICE
      },
      {
        label: formatEnum(FunctionTypeEnum.JFNY_TEST_DEVICE, FunctionTypeEnum, "FunctionTypeEnum"),
        value: FunctionTypeEnum.JFNY_TEST_DEVICE
      }
    ].map(option => h(ElRadio, { label: option.value, border: true }, () => option.label));
    return h(ElRadioGroup, () => radios);
  }
});

const AcceptanceStatus = defineComponent({
  name: "acceptanceStatus",
  render() {
    const checkbox = [
      {
        label: DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS],
        value: DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS
      }
    ];

    if (systemAuthStore.businessLicenseAuth?.authorization.integrationChannel.includes(CHANNEL_IOT)) {
      checkbox.push({
        label: DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS],
        value: DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS
      });
    }

    const checkboxGroup = checkbox.map(option =>
      h(ElCheckbox, { label: option.value, border: true }, () => option.label)
    );
    return h(ElCheckboxGroup, () => checkboxGroup);
  }
});

const items: Array<ISearchItem> = [
  {
    key: "purchaseTime",
    full: true,
    label: "购入时间：",
    style: { width: "600px" },
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择购入时间"
    }
  },
  { full: true, key: "functionType", label: "功能类型：", component: FunctionType },
  { full: true, key: "acceptanceStatusList", label: "设备验收状态：", component: AcceptanceStatus }
];

const keywordFields: Array<IKeywordField> = [
  { key: "device_code", title: "设备编号" },
  { key: "device_name", title: "设备名称" }
];

const onSearch = () => {
  form.gwAcceptanceStatus = form.acceptanceStatusList?.some(
    item => item === DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS
  );
  form.iotAcceptanceStatus = form.acceptanceStatusList?.some(
    item => item === DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS
  );

  emits("onSearch", {
    orFilters: form.orFilters,
    functionType: form.functionType,
    purchaseTime: form.purchaseTime,
    gwAcceptanceStatus: form.gwAcceptanceStatus,
    iotAcceptanceStatus: form.iotAcceptanceStatus
  });
};

// 新增设备
const onAddDeviceModalVis = () => {
  deviceStore.setDevice();
  emits("onAddDevice");
};

// 一键同步
const onKeySynchronization = async () => {
  if (!(await useConfirm("确认一键同步生产设备", "一键同步"))) {
    return;
  }

  await deviceStore.onKeySynchronization();
  ElMessage({ type: "success", message: "一键同步成功" });
};

onMounted(deviceStore.queryDeviceAllowAdd);
</script>

<style scoped>
:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}

:deep(.el-checkbox-group .el-checkbox) {
  margin-right: 12px;
}
</style>
