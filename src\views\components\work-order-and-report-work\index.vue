<template>
  <el-collapse-item :name="FillInDataOfCollapseNameEnum.WORK_ORDER_LIST">
    <template #title>
      <collapse-item-title
        class="order-report-work"
        :title="isReportWorkMode ? '报工' : '工单与报工'"
        :name="FillInDataOfCollapseNameEnum.WORK_ORDER_LIST"
        v-track="TrackPointKey.FORM_PURCHASE_PD_UPDOWN_2"
      >
        <el-tooltip effect="dark" content="缺少报工信息" placement="top" v-if="showMissingTip">
          <el-icon size="18" class="status-icon">
            <warning-filled />
          </el-icon>
        </el-tooltip>
      </collapse-item-title>
    </template>
    <div class="m-5 flex-1">
      <div class="relative">
        <el-tabs v-model="activeName" :class="!isCable && 'hide-tab-header'">
          <el-tab-pane v-for="{ name, label, mineComponent } in tabConfig" :label="label" :name="name" :key="name" lazy>
            <component
              v-if="activeName === name"
              ref="tabPaneRefList"
              :is="mineComponent"
              :production-id="productionId"
              :work-id="workId || ''"
              :sub-class-code="subClassCode"
              :production-no="productionNo"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from "vue";
import { FillInDataOfCollapseNameEnum } from "@/views/order/purchase-order/detail/src/components/fill-in-data/fill-in-data/types";
import CollapseItemTitle from "@/views/order/purchase-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import { WarningFilled } from "@element-plus/icons-vue";
import { generateTabConfig } from "./tab-config";
import { IModeType } from "./type";
import { TrackPointKey } from "@/consts";
import { queryProductionOrderReportWorkIntegrity, queryWorkOrderReportWorkIntegrity } from "@/api/work-order";
import { useMaterial as genMaterialTool } from "@/utils/material";
import { emitter as eventBus } from "@/utils/mitt";

const props = defineProps<{
  /** 模式: 全部 还是 仅报工 */
  mode: IModeType;
  /** 生产订单ID */
  productionId: string;
  /** 物资种类code */
  subClassCode: string;
  /** 生产订单编号 */
  productionNo: string;
  /** 工单ID */
  workId?: string;
}>();

const tabPaneRefList = ref();
const activeName = ref<IModeType>(props.mode === "all" ? "work-order" : "report-work");
const isReportWorkMode = computed(() => props.mode === "report-work");
const showMissingTip = ref<boolean>(false);
const tabConfig = generateTabConfig(props.mode);
const materialTool = genMaterialTool();
const isCable = computed(() => materialTool.isCableBySubClassCode(props.subClassCode));

/**
 * @description: 检查订单报工完整性
 */
async function checkProductionOrderReportWorkIntegrity(productionId: string) {
  const { data: result } = await queryProductionOrderReportWorkIntegrity(productionId);
  if (result == null) {
    return false;
  }
  return result;
}

/**
 * @description: 检查工单报工完整性
 */
async function checkWorkOrderReportWorkIntegrity(workId: string) {
  const { data: result } = await queryWorkOrderReportWorkIntegrity(workId);
  if (result == null) {
    return false;
  }
  return result;
}

/**
 * @description: 刷新数据完整性检查结果
 */
async function refreshDataIntegrityCheckResult() {
  if (!props.subClassCode) {
    return;
  }
  // 线缆检查订单
  if (isCable.value) {
    const isIntegrity = await checkProductionOrderReportWorkIntegrity(props.productionId);
    showMissingTip.value = !isIntegrity;
  } else {
    // 非线缆检查工单
    if (props.workId) {
      const isIntegrity = await checkWorkOrderReportWorkIntegrity(props.workId);
      showMissingTip.value = !isIntegrity;
    }
  }
}

eventBus.on("refreshWorkOrderAndReportWorkTip", refreshDataIntegrityCheckResult);

watch(
  () => props.subClassCode,
  () => {
    if (props.subClassCode) {
      refreshDataIntegrityCheckResult();
    }
  },
  {
    immediate: true
  }
);

onUnmounted(() => {
  eventBus.off("refreshWorkOrderAndReportWorkTip");
});
</script>

<style scoped lang="scss">
:deep(.el-tabs__nav-wrap::after) {
  background-color: transparent;
}

.hide-tab-header {
  :deep(.el-tabs__header) {
    display: none;
  }
}

:deep(.status-icon) {
  color: var(--el-color-warning);
}

:deep(.el-tabs__active-bar) {
  height: 3px !important;
}
</style>
