import { withApiGateway } from "@/api/util";
import { IListResponse } from "@/models";
import {
  IEmphasisRawmaterialList,
  ISearchEmphasisRawMaterial
} from "@/models/stock-data-center/i-emphasis-raw-material";
import { http } from "@/utils/http";
import { getEmphasisRawMaterialList } from "../emphasis-raw-material";

/**
 * 获取同步的重点原材料数据
 */
export function getSyncEmphasisRawMaterialList(queryParams: ISearchEmphasisRawMaterial) {
  return getEmphasisRawMaterialList(queryParams);
}

/**
 * 重新同步数据
 * @param queryParams
 */
export function agaigSync(queryParams: ISearchEmphasisRawMaterial) {
  const url = withApiGateway(`admin-api/business/keyRawMaterial/page`);
  return http.post<ISearchEmphasisRawMaterial, IListResponse<IEmphasisRawmaterialList>>(url, {
    data: queryParams
  });
}
