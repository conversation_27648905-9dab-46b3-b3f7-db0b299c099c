import {
  getDownLoadUrlOfDetailLog,
  getInterfaceRecordPageByScene,
  getSearchOptionList
} from "@/api/logging/gateway/gateway-log";
import {
  EStatusCode,
  IDownLoadDetailLog,
  IGatewayDetailList,
  IGatewayList,
  IGatewayTodayListReq,
  ISearchGatewayReq,
  ISearchOptionList
} from "@/models/logging";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isNullOrUnDef } from "@pureadmin/utils";

export const useGatewayTodayStore = defineStore({
  id: "gateway-today-store",
  state: () => ({
    tableTotal: 0,
    gatewayTodayQuery: {} as ISearchGatewayReq,
    gatewayTodayTableData: [] as Array<IGatewayList>,
    matOptions: [] as Array<ISearchOptionList>
  }),
  getters: {
    getFetchErrorNumber() {
      return this.detailTableData.filter((item: IGatewayDetailList) => item.statusCode !== EStatusCode.Success)?.length;
    },
    getFetchSuccessNumber() {
      return this.detailTableData.filter((item: IGatewayDetailList) => item.statusCode === EStatusCode.Success)?.length;
    }
  },
  actions: {
    /** 获取网关设备的搜索下拉框 */
    async getGatewaySearchOptions(subClassCode: string) {
      const res = await getSearchOptionList(subClassCode);
      this.matOptions = res.data || [];
    },

    /** 查询网关设备当日记录日志列表 */
    async queryGatewayTodayLogList(params: IGatewayTodayListReq) {
      this.gatewayTodayQuery = { ...this.gatewayTodayQuery, ...params };
      await this.getGatewayTodayLogList();
    },
    /** 网关设备日志 */
    async getGatewayTodayLogList() {
      const queryParams = omitBy(this.gatewayTodayQuery, value => isNullOrUnDef(value));
      const res = await getInterfaceRecordPageByScene(queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.gatewayTodayTableData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.gatewayTodayTableData = [];
        this.tableTotal = 0;
      }
    },

    /** 获取网关日志信息-文件 */
    async getGatewayDetailLogDownloadUrl(id: string): Promise<IDownLoadDetailLog> {
      return (await getDownLoadUrlOfDetailLog(id)).data;
    }
  }
});
