<template>
  <el-form :model="formValue" :inline="true">
    <el-form-item label="报工批次号:" prop="productBatchNo">
      <el-input v-model="formValue.productBatchNo" class="!w-52" placeholder="请输入报工批次号" clearable />
    </el-form-item>
    <el-form-item label="工序:" prop="processId">
      <process-single-selector v-model="formValue.processId" :sub-class-code="subclassCode" />
    </el-form-item>
    <el-form-item label="设备:" prop="deviceId">
      <device-selector v-model="formValue.deviceId" mode="filter" :process-id="formValue.processId" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { IReportWorkReq } from "@/models";
import DeviceSelector from "@/views/components/device-selector/index.vue";
import ProcessSingleSelector from "@/views/components/process-selector/process-single-selector.vue";

defineProps<{
  subclassCode: string;
}>();

const formValue = reactive<IReportWorkReq>({
  productBatchNo: "",
  deviceId: "",
  processId: ""
});

const emits = defineEmits<{
  (event: "searchForm", value: IReportWorkReq): void;
}>();

const search = () => {
  emits("searchForm", {
    ...formValue,
    productBatchNo: formValue.productBatchNo,
    deviceId: formValue.deviceId,
    processId: formValue.processId
  });
};

const reset = () => {
  formValue.productBatchNo = "";
  formValue.deviceId = "";
  formValue.processId = "";
  emits("searchForm", formValue);
};
</script>

<style scoped lang="scss"></style>
