<template>
  <el-form
    class="overflow-x-hidden"
    ref="formInstance"
    label-position="top"
    require-asterisk-position="right"
    :scroll-to-error="true"
    :model="form"
    :rules="rules"
  >
    <div class="base-info">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="试验编号" prop="experimentNo">
            <SerialNumber
              :code="OUT_FACTORY_EXPERIMENT_NO_CODE"
              :create="!exFactoryQMXStore.outGoingFactoryQmxExperimentDetail?.id"
              v-model="form.experimentNo"
              placeholder="请输入试验编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="产品名称" prop="productName">
            <el-input placeholder="请输入产品名称" v-model="form.productName" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="产品型号" prop="model">
            <el-input placeholder="请输入试验编号" v-model="form.model" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产批次号" prop="productBatchNo">
            <el-input placeholder="请输入 生产批次号" v-model="form.productBatchNo" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <div class="inspect-info">
      <TitleBar title="检验信息" class="mb-3" />
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="抽检批次号" prop="inspectBatchNo">
            <el-input placeholder="请输入抽检批次号" v-model="form.inspectBatchNo" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检验日期" prop="inspectDate">
            <el-date-picker
              v-model="form.inspectDate"
              type="datetime"
              placeholder="请选择检验日期"
              :disabled-date="disabledNowAfterDate"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="样品编号" prop="sampleNo">
            <el-input placeholder="请输入样品编号" v-model="form.sampleNo" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测人员" prop="inspectOperate">
            <el-input placeholder="请输入检测人员" v-model="form.inspectOperate" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="审核人员" prop="auditor">
            <el-input placeholder="请输入审核人员" v-model="form.auditor" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="2"
              show-word-limit
              :maxlength="200"
              resize="none"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import SerialNumber from "@/components/SerialNumber";
import { ref, reactive, watchEffect, onMounted } from "vue";
import { FormRules, FormInstance } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IOutGoingFactoryQMXExperimentForm, IArmourClampExperimentForm, IWorkOrder } from "@/models";
import { useEXFactoryQMXStore, useSalesFillInDataStore, usePurchaseOrderDetailStore } from "@/store/modules";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { SPECIAL_CHARACTER_REGEXP, OUT_FACTORY_EXPERIMENT_NO_CODE } from "@/consts";

const fillInDataStore = useSalesFillInDataStore();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const formInstance = ref<FormInstance>();
const armourClampForm = reactive<IArmourClampExperimentForm>({
  id: null,
  experimentNo: undefined,
  productName: undefined,
  productModel: undefined,
  productBatchNo: undefined,
  inspectBatchNo: undefined,
  sampleNo: undefined,
  inspectDateTime: undefined,
  inspectOperate: undefined,
  auditor: undefined,
  remark: undefined
});
const form: any = armourClampForm;
const rules: FormRules = {
  experimentNo: [
    { message: requiredMessage("试验编号"), trigger: "change", required: true },
    { message: "试验编号 不允许输入符号", trigger: "change", pattern: SPECIAL_CHARACTER_REGEXP }
  ],
  productName: [{ required: true, message: requiredMessage("产品名称"), trigger: "change" }],
  model: [{ required: true, message: requiredMessage("产品型号"), trigger: "change" }],
  productBatchNo: [{ required: true, message: requiredMessage("生产批次号"), trigger: "change" }],
  inspectBatchNo: [{ required: true, message: requiredMessage("抽检批次号"), trigger: "change" }],
  inspectDate: [{ required: true, message: requiredMessage("检验日期"), trigger: "change" }]
};
const exFactoryQMXStore = useEXFactoryQMXStore();
watchEffect(async () => {
  if (exFactoryQMXStore.outGoingFactoryQmxExperimentDetail && exFactoryQMXStore.outGoingFactoryQmxExperimentDetail.id) {
    Object.assign(form, exFactoryQMXStore.outGoingFactoryQmxExperimentDetail);
  } else {
    Object.assign(form, {
      id: undefined,
      experimentNo: undefined,
      finProNo: undefined,
      account: undefined,
      measUnit: undefined,
      startTime: undefined,
      endTime: undefined,
      experimentStatus: undefined,
      experimentResult: undefined,
      productionId: undefined,
      processId: undefined,
      processCode: undefined,
      processName: undefined
    });
  }
});

onMounted(() => {
  if (!exFactoryQMXStore.isEditTightness && !purchaseOrderDetailStore.isCable) {
    // 从工单带入数据
    const { specificationType: model, productName } = fillInDataStore.data as IWorkOrder;
    Object.assign(form, { model, productName });
  }
});

/** 获取表单的值 */
const getQMXExperimentFormValue = async (): Promise<boolean | IOutGoingFactoryQMXExperimentForm> => {
  if (!formInstance.value) {
    return false;
  }

  const valid = await formInstance.value.validate(() => {});

  if (!valid) {
    return valid;
  }

  return form;
};

const resetExperimentFormValue = () => {
  formInstance?.value?.resetFields();
};

defineExpose({
  getQMXExperimentFormValue,
  resetExperimentFormValue
});
</script>
<style scoped lang="scss"></style>
