<template>
  <div class="flex-1 flex overflow-hidden">
    <BaseList :columns="columns" v-bind="$attrs" />
    <EditProcessInspectionDialog ref="editProcessInspectionDialog" :subclass-code="subclassCode" />
  </div>
</template>

<script setup lang="ts">
import BaseList from "./base-list.vue";
import EditProcessInspectionDialog from "@/views/components/state-grid-order-sync/dialogs/edit-process-inspection-dialog.vue";
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncType, TableWidth } from "@/enums";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesFillInDataStore, useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { IProductionFlowSync } from "@/models";
import { provide, reactive, ref } from "vue";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useSubclassCode } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSubclassCode";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

const type = StateGridOrderSyncType.PRODUCTION_TECHNOLOGY;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const { subclassCode } = useSubclassCode();
const { dateFormatter } = useTableCellFormatter();

const fillInDataStore = useSalesFillInDataStore();
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const editProcessInspectionDialog = ref<InstanceType<typeof EditProcessInspectionDialog>>();

const typeSpecialColumns: Array<TableColumns> = fillInDataStore.isArmourClamp
  ? [
      {
        label: "产品型号",
        prop: "model",
        width: TableWidth.order
      },
      {
        label: "生产批次号",
        prop: "productBatchNo",
        width: TableWidth.order
      },
      {
        label: "加工方式(本体)",
        prop: "processMethod",
        width: TableWidth.order
      },
      {
        label: "加工件数(本体)",
        prop: "processNumber",
        width: TableWidth.order
      },
      {
        label: "生产完成时间",
        prop: "productFinishTime",
        formatter: dateFormatter(),
        width: TableWidth.dateTime
      }
    ]
  : [
      {
        label: "工序",
        prop: "processName",
        minWidth: TableWidth.order
      }
    ];

const columns: Array<TableColumns> = reactive([
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order
  },
  {
    label: "报告批次号",
    prop: "code",
    minWidth: TableWidth.largeOrder
  },
  ...typeSpecialColumns,
  ...normalColumns,
  operatorColumn
]);

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

async function openEditDialog(data: IProductionFlowSync) {
  editProcessInspectionDialog.value.openEditDialog(data);
}
</script>

<style scoped>
:deep(.base-list) {
  padding-top: 12px !important;
}
</style>
