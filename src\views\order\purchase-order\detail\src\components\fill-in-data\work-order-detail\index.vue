<template>
  <div class="header mb-2">
    <FontIcon icon="icon-classify-fill" class="text-primary text-middle" />
    <div class="title">工单编号 {{ workOrderStore.workOrderDetail?.woNo }}</div>
    <EnumTag
      v-if="workOrderStore.workOrderDetail?.woStatus"
      :value="workOrderStore.workOrderDetail?.woStatus"
      :enum="ProductionStateEnum"
      enumName="productionStateEnum"
    />
  </div>
  <el-descriptions>
    <el-descriptions-item label="工序" :span="3">
      {{ workOrderStore.workOrderDetail?.processName }}
    </el-descriptions-item>
    <el-descriptions-item label="生产数量">
      <template v-if="workOrderStore.workOrderDetail?.amount">
        {{ workOrderStore.workOrderDetail.amount }}
        <DictionaryName
          class="ml-1"
          :subClassCode="workOrderStore.workOrderDetail.subclassCode"
          :parentCode="MEASURE_UNIT"
          :code="workOrderStore.workOrderDetail.unit"
        />
      </template>
    </el-descriptions-item>
  </el-descriptions>
  <el-divider />

  <div class="header mb-2">
    <FontIcon icon="icon-classify-fill" class="text-primary text-middle" />
    <div class="title">物料编号 {{ workOrderStore.workOrderDetail?.materialsCode }}</div>
  </div>
  <el-descriptions>
    <el-descriptions-item :span="3" label="物料名称">
      {{ workOrderStore.workOrderDetail?.materialName }}
    </el-descriptions-item>
    <el-descriptions-item :span="3" label="物料单位">
      <DictionaryName
        :subClassCode="workOrderStore.workOrderDetail?.subclassCode"
        :parentCode="MEASURE_UNIT"
        :code="workOrderStore.workOrderDetail?.materialUnit"
      />
    </el-descriptions-item>
    <el-descriptions-item :span="3" label="电压等级">
      {{ formatEnum(workOrderStore.workOrderDetail?.voltageLevel, VoltageClassesEnum, "voltageClassesEnum") }}
    </el-descriptions-item>
    <el-descriptions-item :span="3" label="规格型号">
      {{ workOrderStore.workOrderDetail?.specificationType }}
    </el-descriptions-item>
    <el-descriptions-item label="物料描述">
      {{ workOrderStore.workOrderDetail?.materialDesc }}
    </el-descriptions-item>
  </el-descriptions>
  <el-divider />

  <div class="header mb-2">
    <IconifyIconOffline :icon="Time" class="text-primary text-middle" />
    <div class="title">工单排期</div>
  </div>
  <el-descriptions>
    <el-descriptions-item :span="3" label="计划日期">
      {{ formatDate(workOrderStore.workOrderDetail?.planStartDate) }}
      ～
      {{ formatDate(workOrderStore.workOrderDetail?.planFinishDate) }}
    </el-descriptions-item>
    <el-descriptions-item :span="3" label="实际开始日期">
      {{ formatDate(workOrderStore.workOrderDetail?.actualStartDate) }}
    </el-descriptions-item>
    <el-descriptions-item label="实际完成日期">
      {{ formatDate(workOrderStore.workOrderDetail?.actualFinishDate) }}
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup lang="ts">
import Time from "@iconify-icons/ri/time-fill";
import EnumTag from "@/components/EnumTag";
import { useWorkOrderStore } from "@/store/modules";
import { formatEnum, formatDate } from "@/utils/format";
import { ProductionStateEnum, VoltageClassesEnum } from "@/enums";
import { MEASURE_UNIT } from "@/consts";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";

const workOrderStore = useWorkOrderStore();
</script>

<style scoped lang="scss">
.header {
  @apply flex items-center gap-2;

  .title {
    @apply text-text_color_primary font-semibold text-middle;
  }
}
</style>
