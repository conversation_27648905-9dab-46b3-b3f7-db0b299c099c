import { defineStore } from "pinia";
import { IWarningRuleDto, IWarningRuleParams } from "@/models/gateway";
import * as api from "@/api/gateway";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

export const useWarningRuleStore = defineStore({
  id: "gateway-warning-rule",
  actions: {
    queryWarningRules(params: Partial<IWarningRuleParams>) {
      return api.queryWarningRules(omitBy(params, val => isAllEmpty(val))).then(res => res.data);
    },
    updateWarningRule(rule: IWarningRuleDto) {
      return api.updateWarningRule(rule);
    }
  }
});
