<template>
  <div>
    <div class="flex justify-between mb-2">
      <div class="flex-1">
        <SwitchTab
          is-process-check
          :switch-tags="productionProcessInspecStore.switchTabList"
          @switch-change="switchTag($event)"
        />
      </div>
      <div class="select-material" v-if="isCanOperateAddAndEdit">
        <el-button
          v-auth="PermissionKey.form.formPurchaseProcessCreate"
          type="primary"
          @click="addProductionInspection()"
        >
          <FontIcon class="mr-2" icon="icon-plus" />
          新增过程检
        </el-button>
        <el-button type="primary" @click="copyOfProduction" v-auth="PermissionKey.form.formPurchaseProcessCreate">
          <FontIcon class="mr-2" icon="icon-plus" />
          {{ copyButtonText }}
        </el-button>
      </div>
    </div>
    <!-- 过程检测和自动采集切换 -->
    <div class="flex" :class="isCanSwitch ? 'justify-between' : 'justify-end'">
      <section class="switch-tab flex items-center pb-4 h-[54px]" v-if="isCanSwitch">
        <CxTabSwitch
          class="!px-1"
          v-model:current-tab-index="currentIndex"
          :operateModules="tabs"
          :showCount="true"
          @switchModuleEvent="tabSwitch"
        />
        <!-- 报工批次号 -->
        <AutoCollectFilter class="ml-16" v-model="batchNoInfo" :options="batchNumberList" v-if="autoCollectComp" />
      </section>
      <card-list-switcher class="mb-4" v-model="activeTab" v-show="!autoCollectComp" />
    </div>

    <div class="inspection overflow-hidden min-h-[348px]" v-loading="productionProcessInspecStore.loadingInspect">
      <!-- 自动采集 -->
      <ProcessInspectAutoCollect
        v-if="autoCollectComp"
        ref="autoCollectChartRef"
        :options="batchNumberList"
        :batchNoInfo="batchNoInfo"
        :autoCollectItems="autoCollectItems"
      />
      <ProcessInspectionInfo
        v-else
        @editProdInspect="editProdInspect($event)"
        @delProdInspect="delProdInspect($event)"
        @copyProcessInspection="copyProcessInspection($event)"
        @loadMore="loadMore"
        :active-tab="activeTab"
      />
    </div>

    <!-- 新增过程检 -->
    <el-dialog
      v-model="addProcessInspecVisible"
      :title="dialogTitle"
      class="middle"
      align-center
      draggable
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @closed="cancelChange"
    >
      <div class="cx-form inspect-content">
        <AddProcessInspection ref="addProcessInspecRef" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addProcessInspecVisible = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="saveProcessInspectionClick">保存</el-button>
          <el-button
            v-if="productionProcessInspecStore.mode === 'add'"
            type="primary"
            :loading="loading"
            @click="saveProcessInspectionAndAddClick"
          >
            保存并新增
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 从生产订单中复制 -->
    <CopyProductDialog v-model="copyVisible" :isStepKey="prodCtx.stepKey" @saveSuccess="saveSuccess" />
  </div>
</template>

<script setup lang="ts">
import ProcessInspectionInfo from "./process-inspec-info/index.vue";
import AddProcessInspection from "./add-production-process-inspec/index.vue";
import SwitchTab from "../../component/switch-tab/index.vue";
import CopyProductDialog from "../../component/copy-production-dialog/index.vue";
import CxTabSwitch from "@/components/cx-tab-switch";
import AutoCollectFilter from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/production-process-inspection/component/auto-collect/auto-collect-filter.vue";
import ProcessInspectAutoCollect from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/production-process-inspection/component/auto-collect/auto-collect.vue";
import { onUnmounted, ref, computed, inject, reactive, watch } from "vue";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { ISwitchTagEventType, ITagType } from "../../component/switch-tab/types";
import { ElMessageBox, ElMessage } from "element-plus";
import { IProductionProcessList } from "@/models/production-test-sensing/i-production-process-inspection";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { useSalesFillInDataStore, useSalesOrderDetailStore } from "@/store/modules";
import { PermissionKey } from "@/consts";
import { SyncDetailProcessInspectEnum } from "@/enums/sync-detail-process-inspect";
import { IModule } from "@/components/cx-tab-switch/types";
import { IReportWork } from "@/models";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ProductOrWorkOrderEnum } from "@/enums";
import { IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";
import CardListSwitcher from "@/components/card-list-switcher/index.vue";
import { emitter } from "@/utils/mitt";

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
// 获取生产过程工艺检测
const productionProcessInspecStore = useProductionProcessInspecStore();
const currentTagInfo = ref();
const dialogTitle = ref("新增过程检");
const addProcessInspecVisible = ref(false);
const addProcessInspecRef = ref();
const currentEditProcessInfo = ref();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const fillInDataStore = useSalesFillInDataStore();
const processInfoList = computed(() => productionProcessInspecStore.switchTabList);
/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => productionProcessInspecStore.getIsCanOperateAddAndEdit);
const loading = ref<boolean>(false);
const saveProcessInspectionClick = useLoadingFn(saveProcessInspection, loading);
const saveProcessInspectionAndAddClick = useLoadingFn(saveProcessInspectionAndAdd, loading);
const defaultPageInfo = {
  pageNo: 1,
  pageSize: 10
};
productionProcessInspecStore.getProdProcessInspecProcessAction(defaultPageInfo, true);

const totalPage = computed(() => {
  return Math.ceil(productionProcessInspecStore.processInspectionTableTotal / defaultPageInfo.pageSize);
});

// tab 切换
const salesOrderStore = useSalesOrderDetailStore();
const isCanSwitch = computed(() => {
  return salesOrderStore?.isCable;
});
const tabs = ref<Array<IModule>>([]);
const processInspectTabs = reactive<Array<IModule>>([
  {
    moduleCode: SyncDetailProcessInspectEnum.PROCESS_INSPECT,
    moduleName: "过程检"
  }
]);
const collectTab = [
  {
    moduleCode: SyncDetailProcessInspectEnum.AUTO_COLLECT,
    moduleName: "自动采集"
  }
];
const currentIndex = ref<number>(0);
const currentTab = ref();
currentTab.value = isCanSwitch.value ? processInspectTabs[0] : null;
const autoCollectComp = computed(() => {
  return currentTab.value?.moduleCode === SyncDetailProcessInspectEnum.AUTO_COLLECT;
});
const batchNumberList = ref<Array<IReportWork>>([]);
const batchNoInfo = ref<IReportWork>();
const autoCollectChartRef = ref<InstanceType<typeof ProcessInspectAutoCollect>>();
const autoCollectItems = ref<Array<IRawMaterialCheckCollectionItem>>([]);

// 生产订单/工单中复制
const copyButtonText = computed(() => {
  return salesOrderStore.isCable ? "从生产订单中选用" : "从生产工单中选用";
});
const copyVisible = ref<boolean>(false);

/** 当前激活的样式风格 */
const activeTab = ref<"card" | "list">("list");
watch(processInfoList, newVal => {
  if (newVal) {
    if (currentTagInfo.value && currentTagInfo.value.processId) {
      currentTagInfo.value = newVal.find(item => item.processId === currentTagInfo.value.processId);
    } else {
      currentTagInfo.value = newVal[0];
    }
    productionProcessInspecStore.currentTagInfo = currentTagInfo.value;
    // 获取工序下的自动采集信息
    getAutoCollectItemsByProcessId(currentTagInfo.value);
    // 获取报工批次号
    getReportWorkBatchNo(currentTagInfo.value);
  }
});

watch(
  batchNoInfo,
  newVal => {
    autoCollectChartRef.value?.initCharts(newVal);
  },
  {
    immediate: true
  }
);

/**
 * 加载更多
 */
const loadMore = () => {
  defaultPageInfo.pageNo++;
  if (defaultPageInfo.pageNo > totalPage.value) {
    return;
  }

  const { processCode } = currentTagInfo.value;
  productionProcessInspecStore.getProdProcessInspecProcessAction({ processCode, ...defaultPageInfo }, true);
};

// 新增过程检测弹框显示
const addProductionInspection = async () => {
  currentTagInfo.value = currentTagInfo.value ? currentTagInfo.value : productionProcessInspecStore.switchTabList[0];
  // 保存当前选择的原材料工序
  productionProcessInspecStore.currentTagInfo = currentTagInfo.value;
  dialogTitle.value = "新增过程检";
  productionProcessInspecStore.mode = "add";
  addProcessInspecVisible.value = true;
};

// 切换过程检测过程
const switchTag = (tag: ISwitchTagEventType) => {
  initTab();
  activeTab.value = "list";
  productionProcessInspecStore.initQueryAndLoading();
  currentTagInfo.value = tag.data;
  productionProcessInspecStore.currentTagInfo = currentTagInfo.value;
  defaultPageInfo.pageNo = 1;
  const { processCode } = tag.data;
  productionProcessInspecStore.getProductionProcessList({ processCode, ...defaultPageInfo }, true);
  getAutoCollectItemsByProcessId(tag.data);
  getReportWorkBatchNo(tag.data);
  emitter.emit("refreshProcessInspecList");
};

// 关闭弹框
const cancelChange = () => {
  addProcessInspecVisible.value = false;
  productionProcessInspecStore.initDetailOfProcessInfo();
};

// 编辑过程检测信息
const editProdInspect = async (processInfo: IProductionProcessList) => {
  currentTagInfo.value = currentTagInfo.value ? currentTagInfo.value : productionProcessInspecStore.switchTabList[0];
  dialogTitle.value = "编辑过程检";
  productionProcessInspecStore.mode = "edit";
  currentEditProcessInfo.value = processInfo;
  addProcessInspecVisible.value = true;
  productionProcessInspecStore.initDetailOfProcessInfo();
  productionProcessInspecStore.getDetailOfProdProcessInfo(processInfo.id);
};

// 复制过程检测信息
const copyProcessInspection = async (processInfo: IProductionProcessList) => {
  console.log(processInfo);
  currentTagInfo.value = currentTagInfo.value ? currentTagInfo.value : productionProcessInspecStore.switchTabList[0];
  dialogTitle.value = "复制过程检";
  productionProcessInspecStore.mode = "copy";
  currentEditProcessInfo.value = processInfo;
  addProcessInspecVisible.value = true;
  productionProcessInspecStore.initDetailOfProcessInfo();
  productionProcessInspecStore.getDetailOfProdProcessInfo(processInfo.id);
};

// 保存数据
async function saveProcessInspection() {
  const saveValid = await addProcessInspecRef.value.getProcessInspectionForm();
  if (saveValid?.code) {
    const { processCode, processName, processId } = currentTagInfo.value;
    const dataId = fillInDataStore.dataId;
    const { isCable } = salesOrderStore;
    const orderParam: { productionId?: string; workOrderId?: string } = {};
    isCable && (orderParam.productionId = dataId);
    !isCable && ((orderParam.workOrderId = dataId), (orderParam.productionId = fillInDataStore.productionId));
    const params = {
      ...productionProcessInspecStore.saveFormData,
      ...orderParam,
      processId,
      processName,
      processCode,
      id: null
    };
    // 如果是编辑模式
    if (productionProcessInspecStore.mode === "edit") {
      params.id = currentEditProcessInfo.value?.id;
    }
    const saveRes = await productionProcessInspecStore.saveProductionProcessCheck(params);
    // 失败
    if (!saveRes.data) return;
    const message = {
      add: "新增成功",
      edit: "编辑成功",
      copy: "复制成功"
    };
    // 保存成功
    ElMessage.success(message[productionProcessInspecStore.mode]);
    cancelChange();
    refreshProcessCheckList(saveRes.data);
    emitter.emit("refreshProcessInspecList");
  }
}

// 保存并新增
async function saveProcessInspectionAndAdd() {
  const saveValid = await addProcessInspecRef.value.getProcessInspectionForm();
  if (saveValid?.code) {
    const { processCode, processName, processId } = currentTagInfo.value;
    const dataId = fillInDataStore.dataId;
    const { isCable } = salesOrderStore;
    const orderParam: { productionId?: string; workOrderId?: string } = {};
    isCable && (orderParam.productionId = dataId);
    !isCable && ((orderParam.workOrderId = dataId), (orderParam.productionId = fillInDataStore.productionId));
    const saveRes = await productionProcessInspecStore.saveProductionProcessCheck({
      ...productionProcessInspecStore.saveFormData,
      ...orderParam,
      processId,
      processName,
      processCode,
      id: null
    });
    // 失败
    if (!saveRes.data) return;
    // 保存成功
    ElMessage.success("新增成功");
    addProcessInspecRef.value.refreshNo();
    refreshProcessCheckList(saveRes.data);
    emitter.emit("refreshProcessInspecList");
  }
}

// 删除过程检测信息
const delProdInspect = (processInfo: IProductionProcessList) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      await productionProcessInspecStore.delProductionProcessCheck(processInfo.id);
      ElMessage({ type: "success", message: "删除成功" });
      initRefresh();
      emitter.emit("refreshProcessInspecList");
    })
    .catch(() => {});
};

// 刷新列表数据
const refreshProcessCheckList = async (detail?: IProductionProcessList) => {
  if (productionProcessInspecStore.mode === "add" || productionProcessInspecStore.mode === "copy") {
    initTab();
    initRefresh();
  } else {
    // 编辑时单独更新当前数据
    if (detail?.id) {
      await productionProcessInspecStore.updateDetailInfoById(detail);
    }
  }
};

async function initRefresh() {
  productionProcessInspecStore.initQueryAndLoading();
  defaultPageInfo.pageNo = 1;
  const { processCode } = currentTagInfo.value;
  await productionProcessInspecStore.getProdProcessInspecProcessAction({ processCode, ...defaultPageInfo }, true);
  await productionTestSensingStore.refreshProductionProcessStatus();
}

/** 复制 */
function copyOfProduction() {
  copyVisible.value = true;
}

async function saveSuccess() {
  ElMessage.success("保存成功");
  copyVisible.value = false;
  initTab();
  initRefresh();
}

/** 获取报工批次号 */
async function getReportWorkBatchNo(processInfo: ITagType) {
  const { processId } = processInfo || {};
  const { isCable } = salesOrderStore;
  const params = {
    orderId: fillInDataStore.dataId,
    processId,
    orderType: isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER
  };
  batchNumberList.value = await productionProcessInspecStore.getReportWorkBatchNo(params);
  batchNoInfo.value = batchNumberList.value[0];
}

/** 根据工序Id获取自动采集项  */
async function getAutoCollectItemsByProcessId(processInfo: ITagType) {
  const { processId, containsSystemPushItem } = processInfo || {};
  tabs.value = [];
  autoCollectItems.value = await productionProcessInspecStore.getCollectionItemsFromProcess(processId);
  if (containsSystemPushItem && autoCollectItems.value?.length) {
    tabs.value = [...processInspectTabs, ...collectTab];
  } else if (!containsSystemPushItem) {
    // 只有采集项得信息时
    tabs.value = [...collectTab];
  } else {
    tabs.value = [...processInspectTabs];
  }
  tabSwitch(tabs.value[0], 0);
}

/** 切换过程检测过程 */
function tabSwitch(data: IModule, index: number) {
  currentTab.value = data;
  currentIndex.value = index;
  batchNoInfo.value = undefined;
}

/** 初始化tab */
function initTab() {
  currentIndex.value = 0;
  currentTab.value = tabs.value[0];
}

// 组件销毁时初始化数据
onUnmounted(() => {
  productionProcessInspecStore.initDetailOfProcessInfo();
  productionProcessInspecStore.initQueryAndLoading();
  productionProcessInspecStore.initCurrentTagInfo();
});
</script>

<style scoped lang="scss">
.inspect-content {
  min-height: 446px;
  max-height: 532px;
}

:deep(.tab-view) {
  @apply text-base;
}
</style>
