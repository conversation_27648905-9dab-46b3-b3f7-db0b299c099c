<template>
  <div class="inline-block ml-3">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      align-center
      v-model="dialogVisible"
      title="合格证打印"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <!-- 内容 -->
      <div class="h-[500px]">
        <print-certificate-form ref="formRef" :disabled="props.disabled" />
      </div>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <!-- 表单禁止填写时仅支持调用打印功能 -->
          <el-button v-if="!disabled" type="primary" @click="handleSaveAndPrint" :loading="loading">打印</el-button>
          <el-button v-else type="primary" @click="handlePrint" :loading="loading">打印</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import PrintCertificateForm from "./print-certificate-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  addPrintCertificate,
  editPrintCertificate,
  getEcodeInfoDetail,
  getOneUnusedEcode
} from "../../api/ecode-manage";
import { AddPrintCertificate, EditPrintCertificate, IEcodeInfoDetail } from "../../models/i-ecode-manage";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/store/modules/user";
/**
 * 新增/编辑合格证打印
 */

const loading = ref(false);

const props = withDefaults(
  defineProps<{
    /** 模式 */
    mode: "edit" | "add";
    /** ecode*/
    ecode?: string;
    /** 是否禁用表单 */
    disabled?: boolean;
    /** 禁止自动填充检查人员 */
    disabledFillInspector?: boolean;
  }>(),
  {
    disabled: false
  }
);

const emits = defineEmits(["postSaveSuccess"]);
const detail = ref<Partial<IEcodeInfoDetail>>({});

const formRef = ref<InstanceType<typeof PrintCertificateForm>>();
const dialogVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

const requestEcode = async () => {
  const { data } = await getOneUnusedEcode();
  return data.ecode;
};

const requestDetail = useLoadingFn(async () => {
  const { data } = await getEcodeInfoDetail(props.ecode);
  detail.value = data;
  if (data.certificateInfo) {
    return { ...data.certificateInfo, ecode: data.basicInfo.ecode };
  } else {
    return {
      ...data.certificateInfo,
      standard: data.printerInfo.standard,
      model: data.printerInfo.model,
      categoryCode: data.printerInfo.categoryCode,
      categoryName: data.printerInfo.categoryName,
      voltageClasses: data.printerInfo.voltageClasses,
      ecode: data.basicInfo.ecode
    };
  }
}, loading);

const requestSave = useLoadingFn(async form => {
  if (isAddMode.value) {
    // 新增
    return await addPrintCertificate(form as AddPrintCertificate);
  } else {
    // 编辑
    if (detail.value.certificateInfo) {
      return await editPrintCertificate(form as EditPrintCertificate);
    } else {
      return await addPrintCertificate(form as AddPrintCertificate);
    }
  }
}, loading);

/**
 * @description: 保存并打印
 */
async function handleSaveAndPrint() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();

  // 请求保存
  const res = await requestSave(formVal);

  if (res) {
    // 处理保存后续事件
    ElMessage.success("保存成功");
    emits("postSaveSuccess");
    formRef.value.printCertificate();
  }
}

/**
 * @description: 打印
 */
function handlePrint() {
  if (!formRef.value) {
    return;
  }
  formRef.value.printCertificate();
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  if (formRef.value) {
    formRef.value.clearTemplateBlob();
  }
  dialogVisible.value = false;
}

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, async visible => {
  if (visible) {
    const userStore = useUserStore();
    if (isEditMode.value) {
      // 请求编辑数据
      const data = await requestDetail();
      if (!data.inspector && !props.disabledFillInspector) {
        data.inspector = userStore.profile.nickname;
      }
      formRef.value.initFormValue(data);
    } else {
      // 请求新增数据
      const data = await requestEcode();
      formRef.value.initFormValue({ ecode: data, inspector: userStore.profile.nickname });
    }
  }
});
</script>

<style scoped></style>
