<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="px-6 pb-2 bg-bg_color flex justify-between items-center">
      <Header :supervisionPlan="state.supervisionPlan" />
      <ElButton type="primary" :loading="syncLoading" :icon="Position" @click="handleSyncSupervisionPlan()">
        一键同步
      </ElButton>
    </div>
    <div class="m-4 p-4 bg-bg_color flex-1 flex flex-col">
      <el-radio-group v-model="state.productionStage" @change="onChangeProductionStage($event)">
        <el-radio-button v-for="item in ProductionStageOptions" :label="item.value" :key="item.value">{{
          item.label
        }}</el-radio-button>
      </el-radio-group>
      <div class="flex-1">
        <component :is="productionStageComponent" :supervisionPlan="state.supervisionPlan" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from "./header.vue";
import { ProductionStageOptions, ProductionStageEnum } from "./production-stage-options";
import { reactive, ref, shallowRef } from "vue";
import StartWork from "./components/start-work/index.vue";
import ProductionDocument from "./components/production-document/index.vue";
import SchedulingPlan from "./components/scheduling-plan/index.vue";
import EquipmentInfo from "./components/equipment-info/index.vue";
import RawMaterialInspection from "./components/raw-material-inspection/index.vue";
import FactoryTest from "./components/factory-test/index.vue";
import ProductionProcessInspection from "./components/production-process-inspection/index.vue";
import { onMounted } from "vue";
import { useRoute } from "vue-router";
import { getSupervisionPlanById, syncSupervisionPlan } from "@/api/south-grid-access";
import { ISupervisionPlan } from "@/models/south-grid-access";
import { Position } from "@element-plus/icons-vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";

const ProductionStageMapComponent: Record<ProductionStageEnum, any> = {
  [ProductionStageEnum.PRODUCTION_DOCUMENT]: ProductionDocument,
  [ProductionStageEnum.START_WORK]: StartWork,
  [ProductionStageEnum.EQUIPMENT_INFO]: EquipmentInfo,
  [ProductionStageEnum.SCHEDULING_PLAN]: SchedulingPlan,
  [ProductionStageEnum.MATERIAL_INSPECTION]: RawMaterialInspection,
  [ProductionStageEnum.FACTORY_TEST]: FactoryTest,
  [ProductionStageEnum.PROCESS_MONITORING]: ProductionProcessInspection
};
const route = useRoute();
const productionStageComponent = shallowRef(ProductionDocument);
const syncLoading = ref();
const handleSyncSupervisionPlan = useLoadingFn(onSyncSupervisionPlan, syncLoading);
const state = reactive<{
  productionStage: ProductionStageEnum;
  supervisionPlan: ISupervisionPlan;
}>({
  productionStage: ProductionStageEnum.PRODUCTION_DOCUMENT,
  supervisionPlan: {}
});

onMounted(() => {
  handleGetSupervisionPlan(route.params.id as string);
});

async function onSyncSupervisionPlan() {
  await syncSupervisionPlan({ idList: [state.supervisionPlan.id] });
  ElMessage.success("同步成功");
  handleGetSupervisionPlan(state.supervisionPlan.id);
}

const onChangeProductionStage = (value: string | number | boolean) => {
  productionStageComponent.value = ProductionStageMapComponent[value as ProductionStageEnum];
};

const handleGetSupervisionPlan = async (id: string) => {
  const { data } = await getSupervisionPlanById(id);
  state.supervisionPlan = data;
};
</script>

<style scoped lang="scss"></style>
