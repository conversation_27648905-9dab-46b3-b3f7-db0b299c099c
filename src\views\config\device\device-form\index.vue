<template>
  <el-form
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
    v-loading="loading"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="设备编号" prop="deviceCode">
          <el-input
            v-model="formValue.deviceCode"
            placeholder="请输入设备编号"
            clearable
            :maxlength="40"
            :disabled="disableEdit"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="formValue.deviceName" placeholder="请输入设备名称" clearable :maxlength="40" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="功能类型" prop="functionType">
          <EnumSelect
            class="w-full"
            placeholder="请选择功能类型"
            v-model="formValue.functionType"
            :enum="FunctionTypeEnum"
            enumName="FunctionTypeEnum"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备产地" prop="deviceUnit">
          <el-input v-model="formValue.deviceUnit" placeholder="请输入设备产地" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="购入年月" prop="purchaseTime">
          <el-date-picker
            v-model="formValue.purchaseTime"
            label="购入年月"
            placeholder="请选择日期"
            class="flex-1"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input v-model="formValue.specificationModel" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="应用工序" prop="processIds">
          <el-tree-select
            class="w-full"
            v-model="formValue.processIds"
            :data="categoryContainProcessTree"
            multiple
            :render-after-expand="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="所属车间" prop="workshopId">
          <workshop-selector v-model="formValue.workshopId" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="数采方式" prop="collectionMode">
          <el-radio-group v-model="formValue.collectionMode">
            <el-radio border :label="CollectionModeEnum.AUTO_COLLECT">{{
              t("enum.CollectionModeEnum.AUTO_COLLECT")
            }}</el-radio>
            <el-radio border :label="CollectionModeEnum.MANUAL_FILL_IN">{{
              t("enum.CollectionModeEnum.MANUAL_FILL_IN")
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备验收状态" prop="processIds">
          <el-checkbox-group v-model="processList">
            <el-checkbox :label="DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS">{{
              DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS]
            }}</el-checkbox>
            <el-checkbox v-if="licenseAuthIncludeIOT" :label="DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS">{{
              DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS]
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ICategoryContainProcess, ICreateDevice } from "@/models";
import {
  FunctionTypeEnum,
  CollectionModeEnum,
  DeviceAcceptanceStatusMapName,
  DeviceAcceptanceStatusEnum
} from "@/enums";
import { useCategoryStore, useDeviceStore, useSystemAuthStore } from "@/store/modules";
import EnumSelect from "@/components/EnumSelect";
import { useI18n } from "vue-i18n";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { computedAsync } from "@vueuse/core";
import WorkshopSelector from "@/views/components/workshop-selector/index.vue";

export interface IDeviceForm {
  getFormValue: () => ICreateDevice | boolean;
  resetFormValue: () => {};
}

const { t } = useI18n();
const systemAuthStore = useSystemAuthStore();
const deviceStore = useDeviceStore();
const categoryStore = useCategoryStore();
const ruleFormRef = ref<FormInstance>();
const disableEdit = ref<boolean>();
const processList = ref<Array<string>>([]);
const categoryContainProcessTree = ref<Array<ICategoryContainProcess>>();
const loading = ref(false);
const formValue = reactive<ICreateDevice>({
  id: undefined,
  deviceCode: undefined,
  deviceName: undefined,
  functionType: undefined,
  deviceUnit: undefined,
  purchaseTime: undefined,
  specificationModel: undefined,
  processIds: undefined,
  collectionMode: undefined,
  gwAcceptanceStatus: false,
  iotAcceptanceStatus: false,
  workshopId: undefined
});
const rules: FormRules = {
  deviceCode: [{ required: true, message: "设备编号不能为空", trigger: "blur" }],
  deviceName: [{ required: true, message: "设备名称不能为空", trigger: "blur" }],
  functionType: [{ required: true, message: "功能类型不能为空", trigger: "change" }]
};
const licenseAuthIncludeIOT = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeIOT);

const queryCategoryContainProcessTree = async () => {
  categoryContainProcessTree.value = await categoryStore.queryCategoryContainProcessTree();

  // 树形选择框网络数据获取后，再初始化form值，避免编辑时等待加载网络数据过程中显示ID的问题
  if (deviceStore.deviceDetail && deviceStore.deviceDetail.id) {
    Object.assign(formValue, deviceStore.deviceDetail);
  }

  if (formValue.gwAcceptanceStatus) {
    processList.value.push(DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS);
  }
  if (formValue.iotAcceptanceStatus) {
    processList.value.push(DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS);
  }

  disableEdit.value = !!deviceStore.deviceDetail?.id;
};

const requestData = useLoadingFn(queryCategoryContainProcessTree, loading);

onMounted(requestData);

const resetFormValue = () => {
  if (!ruleFormRef.value) return;
  processList.value.length = 0;
  ruleFormRef.value.resetFields();
};

const getFormValue = async (): Promise<boolean | ICreateDevice> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});
  if (!valid) {
    return valid;
  }
  formValue.gwAcceptanceStatus = processList.value.includes(DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS);
  formValue.iotAcceptanceStatus = processList.value.includes(DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS);
  return formValue;
};

defineExpose({
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss"></style>
