import { VoltageClassesEnum } from "@/enums";
import { IDictionaryOption } from "../platform";

export interface IMaterial {
  id: string;
  materialCode: string;
  materialDescribe: string;
  specificationModel: string;
  materialName: string;
  materialUnit: string;
  materialUnitName: string;
  voltageClass: VoltageClassesEnum;
  categoryCode: string;
  categoryName: string;
  subClassCode: string;
  subClassName: string;
  createTime: Date;
  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;
}
