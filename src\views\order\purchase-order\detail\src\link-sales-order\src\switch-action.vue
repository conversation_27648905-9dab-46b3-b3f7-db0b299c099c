<template>
  <el-button-group class="btn-group">
    <el-button :class="{ 'is-active': toggleStyleCMP }" @click="getMenuVisible(ToggleStyleEnum.MENU)">
      <IconifyIconOffline :icon="Menu" />
    </el-button>
    <el-button :class="{ 'is-active': !toggleStyleCMP }" @click="getMenuVisible(ToggleStyleEnum.TABLE)">
      <FontIcon icon="icon-list" />
    </el-button>
  </el-button-group>
</template>

<script setup lang="ts">
import { ToggleStyleEnum } from "@/enums";
import { usePurchaseOrderDetailPurchaseOrderLineStore } from "@/store/modules/purchase-order-detail";
import Menu from "@iconify-icons/ep/menu";
import { computed } from "vue";

const store = usePurchaseOrderDetailPurchaseOrderLineStore();
const toggleStyleCMP = computed(() => store.toggleStyle === ToggleStyleEnum.MENU);

function getMenuVisible(toggleStyle: ToggleStyleEnum) {
  store.setToggleStyle(toggleStyle);
}
</script>

<style scoped lang="scss">
.btn-group {
  .el-button {
    padding: 8px;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
