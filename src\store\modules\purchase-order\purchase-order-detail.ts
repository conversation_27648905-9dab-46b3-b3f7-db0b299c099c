import { defineStore } from "pinia";
import { IPurchaseOrderDetail, IPurchaseOrderProcess, IPurchaseOrderStep } from "@/models";
import { getPurchaseOrder, getPurchaseOrderProcess, goToStep } from "@/api/purchase-order";
import { ProcessStepEnum, PurchaseChannel } from "@/enums";
import { computed, ref } from "vue";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { useThrottleFn } from "@vueuse/core";
import { EMaterialCategory } from "@/enums/purchase-order/production-test-sensing";
import { useMaterial } from "@/utils/material";

/**
 * @description: 计算非线缆步骤
 * @param {PurchaseChannel} chaneel 采购渠道
 */
function calcNonCableSteps(chaneel: PurchaseChannel): Array<ProcessStepEnum> {
  const NON_CABLE_STEPS: Array<ProcessStepEnum> = [
    ProcessStepEnum.SALES_ORDER,
    ProcessStepEnum.PRODUCTION_SCHEDULE,
    ProcessStepEnum.PRODUCTION_ORDER,
    ProcessStepEnum.PRODUCTION_DATA,
    ProcessStepEnum.SG_ORDER
  ];
  if (chaneel !== PurchaseChannel.CSG_GuangZhou) {
    NON_CABLE_STEPS.push(ProcessStepEnum.SG_RATE);
  }
  return NON_CABLE_STEPS;
}

/**
 * @description: 计算线缆步骤
 * @param {PurchaseChannel} chaneel 采购渠道
 */
function calcCablesSteps(chaneel: PurchaseChannel): Array<ProcessStepEnum> {
  const CABLE_STEPS: Array<ProcessStepEnum> = [
    ProcessStepEnum.SALES_ORDER,
    ProcessStepEnum.PRODUCTION_SCHEDULE,
    ProcessStepEnum.PRODUCTION_DATA,
    ProcessStepEnum.SG_ORDER
  ];
  if (chaneel !== PurchaseChannel.CSG_GuangZhou) {
    CABLE_STEPS.push(ProcessStepEnum.SG_RATE);
  }
  return CABLE_STEPS;
}

const LINE_COLORS: Array<string> = [
  "#FC5C65",
  "#FD9644",
  "#FED330",
  "#64DFA1",
  "#7CD2F0",
  "#45AAF2",
  "#4B7BEC",
  "#AA84F6",
  "#D1D8E0",
  "#778CA3",
  "#E76CAB",
  "#CDA97D",
  "#ECEE7B",
  "#A8E355",
  "#55C870",
  "#2987E1",
  "#2039B3",
  "#89A0FC",
  "#95A5C4",
  "#3B7AA1"
];

export const usePurchaseOrderDetailStore = defineStore("cx-purchase-order-detail", () => {
  const materialTool = useMaterial();
  const _stepStatus = ref<IPurchaseOrderProcess>();
  const _stepKeys = ref<Array<ProcessStepEnum>>([]);
  const purchaseOrder = ref<IPurchaseOrderDetail>({} as IPurchaseOrderDetail);
  const activeStepKey = ref<ProcessStepEnum>();
  const purchaseOrderId = ref<string>();
  const isCable = ref<boolean>();
  const isArmourClamp = ref<boolean>();
  const detailMaterialCategory = ref<string>();

  const refreshStepStatusHandle = useThrottleFn(refreshStepStatus, REFRESH_SYNC_DATA_PERIOD, true);

  const steps = computed(() => {
    return _stepKeys.value.map(key => {
      const step: IPurchaseOrderStep = { key };
      const status = _stepStatus.value;
      if (status) {
        const {
          purchaseLineLinkedCount,
          purchaseLineCount,
          salesLineCount,
          salesLinePlanCount,
          salesLineProductCount,
          purchaseLineSyncCount,
          purchaseLineGradeCount,
          workOrderHasProductionCount,
          productionCount
        } = status;
        switch (key) {
          case ProcessStepEnum.SALES_ORDER:
            step.completedCount = purchaseLineLinkedCount;
            step.total = purchaseLineCount;
            break;
          case ProcessStepEnum.PRODUCTION_SCHEDULE:
            step.completedCount = salesLinePlanCount;
            step.total = salesLineCount;
            break;
          case ProcessStepEnum.PRODUCTION_ORDER:
            step.completedCount = salesLineProductCount;
            step.total = salesLineCount;
            break;
          case ProcessStepEnum.PRODUCTION_DATA:
            if (isCable.value) {
              step.completedCount = salesLineProductCount;
              step.total = salesLineCount;
            } else {
              step.completedCount = workOrderHasProductionCount;
              step.total = productionCount;
            }
            break;
          case ProcessStepEnum.SG_ORDER:
            step.completedCount = purchaseLineSyncCount;
            step.total = purchaseLineCount;
            break;
          case ProcessStepEnum.SG_RATE:
            step.completedCount = purchaseLineGradeCount;
            step.total = purchaseLineCount;
            break;
        }
        step.complete = step.completedCount >= step.total && step.total > 0;
      }
      return step;
    });
  });

  function setPurchaseOrderId(id: string) {
    purchaseOrderId.value = id;
  }

  async function refreshPurchaseOrder() {
    purchaseOrder.value = (await getPurchaseOrder(purchaseOrderId.value)).data;
    _updatePurchaseLineColor();
    isCable.value = materialTool.isCable(purchaseOrder.value.categoryCode);
    // 金具
    isArmourClamp.value = materialTool.isArmourClamp(purchaseOrder.value.subClassCode);
    // 生产试验感知 物资种类
    _handleMaterialCategory();
    const chaneel = purchaseOrder.value.syncChannel;
    const steps = isCable.value ? calcCablesSteps(chaneel) : calcNonCableSteps(chaneel);
    _updateSteps(steps);
  }

  async function refreshStepStatus(): Promise<void> {
    _stepStatus.value = (await getPurchaseOrderProcess(purchaseOrderId.value)).data;
  }

  async function next(): Promise<void> {
    const activeIndex: number = _stepKeys.value.findIndex(key => key === activeStepKey.value);
    const nextIndex: number = Math.min(activeIndex + 1, _stepKeys.value.length - 1);
    return goto(_stepKeys.value[nextIndex]);
  }

  async function goto(key: ProcessStepEnum) {
    if (activeStepKey.value === key) {
      return;
    }
    if (key <= purchaseOrder.value.linkStep) {
      activeStepKey.value = key;
      return;
    }
    try {
      await goToStep(purchaseOrderId.value, key);
      activeStepKey.value = key;
      refreshPurchaseOrder();
    } catch (e) {
      activeStepKey.value = activeStepKey.value ?? _stepKeys.value[0];
    }
  }

  function $reset() {
    activeStepKey.value = null;
  }

  function _updatePurchaseLineColor() {
    const colorCount = LINE_COLORS.length;
    purchaseOrder.value.lines.forEach((line, index) => {
      if (!line.linkedCount) {
        return;
      }
      line.color = LINE_COLORS[index % colorCount];
    });
  }

  function _updateSteps(keys: Array<ProcessStepEnum>) {
    _stepKeys.value = keys;
    if (!activeStepKey.value || !keys.includes(activeStepKey.value)) {
      activeStepKey.value = purchaseOrder.value.linkStep || keys[0];
    }
  }

  function _updateQueryActiveStep() {
    const url = new URL(location.href);
    url.searchParams.set("activeStep", activeStepKey.value.toString());
    history.replaceState(history.state, "", url.search);
  }

  function _handleMaterialCategory() {
    detailMaterialCategory.value = isArmourClamp.value ? EMaterialCategory.ArmourClamp : EMaterialCategory.Normal;
  }

  return {
    purchaseOrderId,
    purchaseOrder,
    isCable,
    steps,
    activeStepKey,
    isArmourClamp,
    detailMaterialCategory,
    setPurchaseOrderId,
    refreshPurchaseOrder,
    refreshStepStatus,
    refreshStepStatusHandle,
    next,
    goto,
    $reset
  };
});
