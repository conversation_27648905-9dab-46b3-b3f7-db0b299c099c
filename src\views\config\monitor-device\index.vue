<template>
  <div class="flex h-full gap-5 px-6 py-5">
    <div class="w-[280px] mr-1 p-5 overflow-hidden raw-material-type bg-bg_color">
      <div class="flex-bc">
        <ElInput v-model="filterText" placeholder="输入名称" clearable @input="filterRawDeviceGroup">
          <template #prefix>
            <el-icon class="el-input__icon"><Search /></el-icon>
          </template>
        </ElInput>
        <el-button v-auth="PermissionKey.meta.metaMonitorDeviceCreate" @click="onAddDeviceGroup()" class="!p-2 ml-1">
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>
      <el-scrollbar height="calc(100% - 25px)">
        <div class="flex flex-col pt-2 pb-3">
          <template v-for="item in deviceGroupLists" :key="item.id">
            <span
              v-if="!item.id"
              :class="['info-item', { bg_primary: item.id === state.groupId }]"
              @click="onSelectDeviceGroup(item)"
            >
              {{ item.groupName }}
            </span>
            <span
              v-else
              :class="['info-item', { bg_primary: item.id === state.groupId }]"
              @click="onSelectDeviceGroup(item)"
            >
              {{ item.groupName }}
              <el-button
                v-auth="PermissionKey.meta.metaMonitorDeviceEdit"
                :icon="EditPen"
                class="el-icon-edit-hover right-7"
                link
                @click.stop="onEditDeviceGroup(item)"
              />
              <el-button
                v-auth="PermissionKey.meta.metaMonitorDeviceDelete"
                :icon="Delete"
                class="el-icon-delete-hover right-2"
                link
                @click.stop="onDeleteDeviceGroup(item)"
              />
            </span>
          </template>
          <div class="list" v-if="!deviceGroupLists.length">
            <el-empty :image-size="120">
              <template #image> <EmptyData /> </template>
            </el-empty>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="flex flex-col flex-1 overflow-hidden raw-material-container bg-bg_color">
      <div class="w-full px-6 pt-[20px] flex justify-between">
        <ElForm :inline="true" :model="state.params" size="large" class="flex-1 ml-6">
          <ElFormItem label="摄像头编号：">
            <ElInput
              clearable
              v-model="state.params.no"
              placeholder="请输入摄像头编号"
              class="!w-[200px]"
              @clear="onSearch"
            />
          </ElFormItem>
          <ElFormItem label="摄像头名称：">
            <ElInput
              clearable
              v-model="state.params.name"
              placeholder="请输入摄像头名称"
              class="!w-[200px]"
              @clear="onSearch"
            />
          </ElFormItem>
          <ElFormItem>
            <ElButton size="large" type="primary" @click="onSearch()">搜索</ElButton>
            <ElButton size="large" @click="onResetSearch">重置</ElButton>
          </ElFormItem>
        </ElForm>

        <ElButton
          size="large"
          type="warning"
          :icon="RefreshRight"
          :loading="checkLoading"
          @click="onCheckAllMonitorDeviceRunStatus"
          >更新运行状态
        </ElButton>
        <ElButton
          v-auth="PermissionKey.meta.metaMonitorDeviceCreate"
          size="large"
          type="primary"
          :icon="Plus"
          @click="onAddMonitorDevice()"
          >新增监控设备
        </ElButton>
      </div>
      <div class="refrsh text-right text-xs px-6" v-if="state.monitorDeviceLastRefreshTime?.lastRefreshTime">
        运行状态更新时间：{{ state.monitorDeviceLastRefreshTime.lastRefreshTime }}
      </div>
      <div class="flex flex-col flex-1 p-6 overflow-hidden raw-material-type">
        <PureTable
          class="flex-1 pagination tooltip-max-w"
          row-key="id"
          :data="monitorDeviceStore.monitorDevices"
          :columns="state.columns"
          :pagination="pagination"
          :loading="state.loading"
          @page-size-change="onPageSizeChange()"
          @page-current-change="onCurrentPageChange()"
        >
          <template #operation="data">
            <div>
              <ElButton type="primary" link @click="onviewLiveStream(data.row)"> 查看直播 </ElButton>
              <ElButton type="primary" link @click="onViewMonitorDeviceDetail(data.row.id)"> 详情 </ElButton>
              <ElButton
                type="primary"
                link
                @click="onEdit(data.row.id)"
                v-auth="PermissionKey.meta.metaMonitorDeviceEdit"
              >
                编辑
              </ElButton>
              <ElButton
                link
                type="danger"
                @click="onDelete(data.row.id)"
                v-auth="PermissionKey.meta.metaMonitorDeviceDelete"
              >
                删除
              </ElButton>
              <ElButton
                type="primary"
                link
                v-if="licenseAuthIncludeIOT"
                v-auth="PermissionKey.meta.metaMonitorDeviceSyncIot"
                @click="onSynchronizationOT(data.row.id)"
              >
                同步到IOT
              </ElButton>
            </div>
          </template>
          <template #empty>
            <el-empty :image-size="120">
              <template #image>
                <EmptyData />
              </template>
            </el-empty>
          </template>
        </PureTable>
      </div>
    </div>
  </div>
  <div>
    <el-dialog
      :title="state.title"
      align-center
      class="default"
      destroy-on-close
      v-model="state.monitorDeviceModalVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="onCloseMonitorDevice"
    >
      <MonitorDeviceForm ref="equipmentFormRef" :monitorDevice="state.monitorDeviceForm" :groupId="state.groupId" />
      <template #footer>
        <el-button @click="onCancelMonitorDevice()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveMonitorDevice()">保存</el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="监控设备详情"
      align-center
      class="default"
      destroy-on-close
      v-model="state.monitorDeviceDetailModalVisible"
    >
      <MonitorDeviceDetal :monitorDevice="state.monitorDevicDetail" />
    </el-dialog>

    <el-dialog
      :title="state.title"
      align-center
      class="small"
      destroy-on-close
      v-model="state.groupingDeviceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="onCloseDeviceGroup"
    >
      <DeviceGroupForm ref="deviceGroupFormRef" :deviceGroup="state.deviceGroupForm" />
      <template #footer>
        <el-button @click="onCloseDeviceGroup()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveDeviceGroup()">保存</el-button>
      </template>
    </el-dialog>

    <el-dialog
      :title="state.viewLiveStreamDeviceName"
      align-center
      destroy-on-close
      v-model="state.viewLiveStreamVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <CXPlayer :url="state.viewLiveStreamUrl" :isLive="true" :volume="0" :autoplay="true" @errorHandling="onError" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus, Search, EditPen, Delete, RefreshRight } from "@element-plus/icons-vue";
import Xgplayer from "xgplayer";
import { useColumns } from "./columns";
import { reactive, ref, watch, onMounted, onUnmounted } from "vue";
import {
  IMonitorDevice,
  IMonitorDeviceForm,
  IMonitorDeviceReq,
  IDeviceGroupForm,
  IDeviceGroup,
  IMonitorDeviceRefreshTime
} from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useMonitorDeviceStore, useDeviceGroupStore, useSystemAuthStore } from "@/store/modules";
import MonitorDeviceForm from "./monitor-device-form/index.vue";
import { useConfirm } from "@/utils/useConfirm";
import MonitorDeviceDetal from "./detail/index.vue";
import DeviceGroupForm from "./device-group-form/index.vue";
import { useDebounceFn } from "@vueuse/core";
import { PermissionKey } from "@/consts";
import CXPlayer from "@/components/cx-player";
import { omitBy } from "lodash-unified";
import { computedAsync } from "@vueuse/core";
import { getToken } from "@/utils/auth";
import { getMonitorDeviceWatchUrlById } from "@/api/monitor-device/monitor-device";
import { useIntervalFn } from "@vueuse/core";
import { MonitorDeviceRefreshStatusEmum } from "@/enums/monitor-device";

usePageStoreHook().setTitle("监控设备");

const { columns } = useColumns();
const { pagination } = useTableConfig();
const monitorDeviceStore = useMonitorDeviceStore();
const deviceGroupStore = useDeviceGroupStore();
const systemAuthStore = useSystemAuthStore();

const saveLoading = ref<boolean>(false);
const checkLoading = ref<boolean>(false);
const equipmentFormRef = ref<InstanceType<typeof MonitorDeviceForm>>();
const deviceGroupFormRef = ref<InstanceType<typeof DeviceGroupForm>>();
const onSaveMonitorDevice = useLoadingFn(onSave, saveLoading);
const onSaveDeviceGroup = useLoadingFn(saveDeviceGroup, saveLoading);
const onCheckAllMonitorDeviceRunStatus = useLoadingFn(checkAllMonitorDeviceRunStatus, checkLoading);
const filterRawDeviceGroup = useDebounceFn(filterDeviceGroup, 200);
const licenseAuthIncludeIOT = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeIOT);

const filterText = ref();
const deviceGroupLists = ref<Array<IDeviceGroup>>([]);
const errorHttpCode = [404, 401];

const state = reactive<{
  monitorDeviceModalVisible: boolean;
  params: IMonitorDeviceReq;
  loading: boolean;
  title: string;
  monitorDeviceForm: IMonitorDeviceForm;
  monitorDeviceDetailModalVisible: boolean;
  monitorDevicDetail?: IMonitorDevice;
  groupingDeviceVisible: boolean;
  groupId: string;
  deviceGroupForm?: IDeviceGroup;
  viewLiveStreamVisible: boolean;
  viewLiveStreamDeviceName?: string;
  viewLiveStreamUrl?: string;
  columns: TableColumnList;
  livingDeviceId: string;
  monitorDeviceLastRefreshTime?: IMonitorDeviceRefreshTime;
}>({
  monitorDeviceModalVisible: false,
  params: {},
  loading: false,
  title: undefined,
  monitorDeviceForm: undefined,
  monitorDeviceDetailModalVisible: false,
  groupingDeviceVisible: false,
  groupId: undefined,
  viewLiveStreamVisible: false,
  columns: [],
  livingDeviceId: ""
});

const { pause: stopDeviceCheckRunStatus, resume: resumeDeviceCheckRunStatus } = useIntervalFn(
  pollingDeviceCheckRunStatus,
  1000,
  {
    immediate: false
  }
);

watch(licenseAuthIncludeIOT, auth => {
  if (auth) {
    state.columns = columns;
  } else {
    state.columns = columns.filter(
      ({ prop }) => !["iotSyncStatus", "syncResult", "lastSyncTime"].includes(prop as string)
    );
  }
});

watch(
  () => monitorDeviceStore.total,
  () => {
    pagination.total = monitorDeviceStore.total;
  }
);

watch(
  () => deviceGroupStore.deviceGroupList,
  (newVal: IDeviceGroup[]) => {
    deviceGroupLists.value = [
      {
        id: undefined,
        groupName: "全部",
        orderNum: 0
      },
      ...newVal
    ];
    queryMonitorDevices();
  }
);

onUnmounted(() => {
  monitorDeviceStore.$reset();
  stopDeviceCheckRunStatus();
});

onMounted(async () => {
  queryDeviceGroup();
  getMonitorDeviceLastRefreshTime();
});

const onSearch = () => {
  queryMonitorDevices();
};

const onResetSearch = () => {
  state.params.no = undefined;
  state.params.name = undefined;
  queryMonitorDevices();
};

const onPageSizeChange = async () => {
  queryMonitorDevices();
};

const onCurrentPageChange = async () => {
  queryMonitorDevices();
};

/**
 * @description: 同步到IOT
 * @param {*} id
 * @return {*}
 */
const onSynchronizationOT = async (id: string) => {
  if (!(await useConfirm("确认监控设备同步到IOT", "同步到IOT"))) {
    return;
  }
  const { data: isCompleted } = await monitorDeviceStore.syncMonitorDeviceById(id);

  if (isCompleted) {
    ElMessage({ type: "success", message: "监控设备同步完成" });
    queryMonitorDevices();
  }
};

async function onAddMonitorDevice() {
  state.title = "新增监控设备";
  state.monitorDeviceModalVisible = true;
}

const onViewMonitorDeviceDetail = async (id: string) => {
  state.monitorDeviceDetailModalVisible = true;
  state.monitorDevicDetail = await monitorDeviceStore.getMonitorDeviceDetailById(id);
};

async function onSave() {
  const formValue: IMonitorDeviceForm | false = await equipmentFormRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.id) {
    await monitorDeviceStore.createMonitorDevice(formValue);
  } else {
    await monitorDeviceStore.editMonitorDevice(formValue);
  }

  state.monitorDeviceModalVisible = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  queryMonitorDevices();
}

const onCancelMonitorDevice = () => {
  state.monitorDeviceModalVisible = false;
};

const onCloseMonitorDevice = () => {
  state.monitorDeviceForm = undefined;
  state.livingDeviceId = "";
};

const onEdit = async (id: string) => {
  state.title = "编辑监控设备";
  state.monitorDeviceModalVisible = true;
  const monitoringEquipmentDetail: IMonitorDevice = await monitorDeviceStore.getMonitorDeviceDetailById(id);
  state.monitorDeviceForm = monitoringEquipmentDetail;
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await monitorDeviceStore.deleteMonitorDevice(id);
  ElMessage.success("删除成功");
  queryMonitorDevices();
};

const onviewLiveStream = async (data: IMonitorDevice) => {
  const { accessToken } = getToken();
  const res = await monitorDeviceStore.getMonitorDeviceWatchUrlById(data.id);
  state.livingDeviceId = data.id;
  state.viewLiveStreamDeviceName = data.name;
  state.viewLiveStreamUrl = `${res.data}?token=${accessToken}`;
  state.viewLiveStreamVisible = true;
};

async function queryMonitorDevices() {
  state.loading = true;
  const searchParams: IMonitorDeviceReq = {
    groupId: state.groupId,
    no: state.params.no,
    name: state.params.name,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  await monitorDeviceStore.queryMonitorDevices(omitBy(searchParams, x => !x)).finally(() => (state.loading = false));
}
/**
 * 搜索设备分组数据
 */
function filterDeviceGroup() {
  deviceGroupLists.value = deviceGroupStore.deviceGroupList.filter(item => item.groupName.includes(filterText.value));

  if (!deviceGroupLists.value.length) {
    return;
  }
  onSelectDeviceGroup(deviceGroupLists.value[0]);
}

/** 点击分组 */
function onSelectDeviceGroup(groupInfo: IDeviceGroup) {
  state.groupId = groupInfo.id;
  state.params.no = "";
  state.params.name = "";
  pagination.currentPage = 1;
  queryMonitorDevices();
}
// 新增分组
const onAddDeviceGroup = () => {
  state.title = "新建分组";
  state.groupingDeviceVisible = true;
  state.deviceGroupForm = {} as IDeviceGroup;
};
const onEditDeviceGroup = (item: IDeviceGroup) => {
  state.title = "编辑分组";
  state.groupingDeviceVisible = true;
  state.deviceGroupForm = item;
};

// 删除分组
const onDeleteDeviceGroup = async (item: IDeviceGroup) => {
  if (!(await useConfirm(`确认删除${item.groupName}吗？`, "删除分组"))) {
    return;
  }
  const { data } = await deviceGroupStore.deleteDeviceGroup(item);
  if (data) {
    ElMessage.success("删除成功");
  }
  queryDeviceGroup();
  // 如果删除
  if (item.id === state.groupId) {
    state.groupId = "";
    queryMonitorDevices();
  }
};

const onCloseDeviceGroup = () => {
  state.groupingDeviceVisible = false;
};
async function saveDeviceGroup() {
  const formValue: IDeviceGroupForm | false = await deviceGroupFormRef.value.getFormValue().catch(() => false);

  if (!formValue) {
    return;
  }
  if (!formValue.id) {
    await deviceGroupStore.createDeviceGroup(formValue);
  } else {
    await deviceGroupStore.editDeviceGroup(formValue);
  }
  state.groupingDeviceVisible = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  queryDeviceGroup();
}

/** 请求设备分组 **/
const queryDeviceGroup = () => {
  deviceGroupStore.queryDeviceGroup({});
};

/**
 * @description: 视频错误处理
 */
const onError = async (error: any, player: Xgplayer) => {
  if (error.httpCode && errorHttpCode.includes(error.httpCode) && state.livingDeviceId) {
    const { accessToken } = getToken();
    const { data: streamUrl } = await getMonitorDeviceWatchUrlById(state.livingDeviceId);
    const url = `${streamUrl}?token=${accessToken}`;
    player.setConfig({
      url
    });
    player.retry();
  }
};

async function checkAllMonitorDeviceRunStatus() {
  ElMessage.warning("更新中");
  await monitorDeviceStore.checkAllMonitorDeviceRunStatus();
  resumeDeviceCheckRunStatus();
}

async function getMonitorDeviceLastRefreshTime() {
  const { data } = await monitorDeviceStore.getMonitorDeviceLastRefreshTime();
  state.monitorDeviceLastRefreshTime = data;
  return data;
}

async function pollingDeviceCheckRunStatus() {
  checkLoading.value = true;
  const { refreshStatus } = await getMonitorDeviceLastRefreshTime();
  await queryMonitorDevices();
  if (refreshStatus === MonitorDeviceRefreshStatusEmum.Completed) {
    stopDeviceCheckRunStatus();
    ElMessage.success("更新成功");
    checkLoading.value = false;
  }
}
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.info-item {
  @apply text-base text-regular p-0.5 pl-2;
  position: relative;

  .el-icon-edit-hover,
  .el-icon-delete-hover {
    display: none;
    position: absolute;
  }

  .el-icon-edit-hover {
    &:hover {
      color: var(--el-color-primary);
    }
  }

  .el-icon-delete-hover {
    &:hover {
      color: var(--el-color-danger);
    }
  }

  &:hover {
    @apply cursor-pointer;
    background: var(--el-fill-color-light);

    .el-icon-edit-hover,
    .el-icon-delete-hover {
      display: inline-block;
    }
  }
}

.bg_primary {
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);

  &:hover {
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
  }
}
</style>
