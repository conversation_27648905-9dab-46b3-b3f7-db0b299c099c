<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditSalesOrderLineDialog ref="editDialog" />
  <DetailSalesOrderLineDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncType, TableWidth } from "@/enums";
import { provide, reactive } from "vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import BaseList from "./base-list.vue";
import { useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import EditSalesOrderLineDialog from "@/views/components/state-grid-order-sync/dialogs/edit-sales-order-line-dialog.vue";
import { ISalesOrderSync } from "@/models";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import DetailSalesOrderLineDialog from "@/views/components/state-grid-order-sync/dialogs/detail-sales-order-line-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";

const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditSalesOrderLineDialog>,
  InstanceType<typeof DetailSalesOrderLineDialog>,
  ISalesOrderSync
>();

const type = StateGridOrderSyncType.SALE_ORDER_ITEM;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const columns: Array<TableColumns> = [
  {
    label: "销售订单明细",
    prop: "soItemNo",
    minWidth: TableWidth.suborder,
    formatter: linkFormatter(openDetailDialog)
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);
</script>

<style scoped></style>
