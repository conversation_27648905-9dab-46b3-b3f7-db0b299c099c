<template>
  <div class="overflow-hidden w-full flex flex-1 flex-col h-full">
    <div class="bg-bg_color flex justify-end">
      <el-button
        :loading="loading"
        v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
        type="primary"
        @click="handleSynchronization"
        >一键同步</el-button
      >
      <el-button :loading="loading" class="mb-5" :icon="Plus" type="primary" @click="handleAdd">新增</el-button>
    </div>

    <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        v-loading="loading"
        showOverflowTooltip
      >
        <template #pullStatus="data">
          <el-tag
            :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
            @click="handleShowDetail(data)"
            class="cursor-pointer"
          >
            {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <div>
            <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
            <ElButton link type="danger" @click="handleDelete(row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
    <!-- 同步明细 -->
    <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
    <el-dialog
      v-model="state.dialogShow"
      :title="state.isEdit ? '编辑' : '新增'"
      width="600px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCloseDialog()"
    >
      <edit-form v-loading="loading" ref="tableFormRef" :row="state.seleRow" />
      <template #footer>
        <span>
          <el-button :loading="loading" @click="handleCloseDialog()">取消</el-button>
          <el-button :loading="loading" type="primary" @click="handleSave(false)">保存</el-button>
          <el-button v-if="!state.isEdit" :loading="loading" type="primary" @click="handleSave(true)"
            >保存并新增</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="experiment-report">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  queryExperimentReport,
  deleteExperimentReportById,
  createExperimentReport,
  updateExperimentReport
} from "@/api/e-capital-construction/engineering-info/experiment-report";
import { IExperimentReport } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { useRoute } from "vue-router";
import EditForm from "./add-edit-experiment-report/edit-form.vue";
import { EquipmentTypeEnumExt, PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
import { IntermediateSyncByVoucherTypeApi } from "@/api/e-capital-construction/engineering-info";
import { PermissionKey } from "@/consts";

const loading = ref(false);
const tableFormRef = ref();
const state = reactive<{
  list: Array<IExperimentReport>;
  params: { [key: string]: string };
  dialogShow: boolean;
  isEdit: boolean;
  seleRow: IExperimentReport;
}>({
  list: [],
  params: {},
  dialogShow: false,
  isEdit: false,
  seleRow: {} as IExperimentReport
});

const route = useRoute();
const { columns } = useColumns(route.query.type == EquipmentTypeEnumExt.Combiner.toString());
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
onMounted(() => {
  requestList();
});
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("ExperimentReport", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
const handleEdit = (row: IExperimentReport) => {
  state.seleRow = row;
  state.isEdit = true;
  state.dialogShow = true;
};

const handleAdd = () => {
  state.seleRow = {} as unknown as IExperimentReport;
  state.dialogShow = true;
  state.isEdit = false;
};

const handleDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  onDelete(id);
};

const onDelete = useLoadingFn(async (id: string) => {
  const { data } = await deleteExperimentReportById(id as string);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
}, loading);

/**
 * @description: 取消
 */
const handleCloseDialog = () => {
  state.dialogShow = false;
  state.isEdit = false;
};

/**
 * @description: 保存
 */
const handleSave = useLoadingFn(async (isAdd: boolean) => {
  if (!tableFormRef.value) {
    return;
  }
  const valid = await tableFormRef.value.validateForm();
  if (!valid) {
    return;
  }
  const params = {
    ...tableFormRef.value.getFormValue(),
    equipmentId: route.query.id,
    equipmentType: route.query.type
  };
  const { data } = state.isEdit ? await updateExperimentReport(params) : await createExperimentReport(params);
  if (data) {
    ElMessage.success(state.isEdit ? "编辑成功 ！" : "新增成功！");
    requestList();
    state.isEdit = false;
    if (isAdd) {
      tableFormRef.value.resetFormValue();
    } else {
      state.dialogShow = false;
    }
  }
}, loading);

const requestList = useLoadingFn(async () => {
  const { data } = await queryExperimentReport(route.query.id as string);
  state.list = data;
}, loading);
</script>
