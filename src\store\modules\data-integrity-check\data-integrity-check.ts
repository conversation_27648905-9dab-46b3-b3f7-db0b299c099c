import { IDataIntegrityCheckParams, IDataIntegrityCheckTable, IResponse } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useDataIntegrityCheckStore = defineStore({
  id: "cx-data-integrity-check",
  state: () => ({
    total: 0
  }),
  actions: {
    /**
     * @description: 获取数据完整性检查列表数据
     */
    async queryDataIntegrityCheckDataList(params: IDataIntegrityCheckParams) {
      const res: IResponse<IDataIntegrityCheckTable> = await api.queryDataIntegrityCheckDataList(params);
      this.total = res.data.total;
      return res.data;
    },
    /**
     * @description: 根据生产订单ID重新计算数据完整性
     */
    async queryReCalculateByProductionId(productionId: string) {
      const res = await api.queryReCalculateByProductionId(productionId);
      return res.data;
    }
  }
});
