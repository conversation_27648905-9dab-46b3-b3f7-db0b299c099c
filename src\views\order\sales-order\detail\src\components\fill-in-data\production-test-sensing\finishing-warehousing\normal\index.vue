<template>
  <div class="h-full flex flex-col">
    <div class="flex items-center mb-2 justify-end">
      <transition name="fade">
        <div class="flex justify-between items-center mr-6 text-base" v-if="Boolean(selectedCount)">
          已选中:
          <span class="text-primary mx-4">
            {{ selectedCount }}
          </span>
          项
          <el-icon class="close ml-4" @click="clearSelection"><CircleCloseFilled /></el-icon>
        </div>
      </transition>
      <batch-modify-dialog :id-list="selectedIds" @post-save-success="batchModifySuccess">
        <template #trigger="{ openDialog }">
          <el-button
            :disabled="!isBatching"
            :loading="loading"
            v-auth="PermissionKey.form.formPurchaseFinishProductEdit"
            @click="openDialog"
          >
            批量修改
          </el-button>
        </template>
      </batch-modify-dialog>
      <ElButton
        v-auth="PermissionKey.form.formPurchaseFinishProductCreate"
        type="primary"
        :icon="Plus"
        @click="onAddFinishingWarehousingModalVis()"
        >新增成品信息</ElButton
      >
    </div>
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="finishedProductStorageStore.finishedProductStorages"
      :columns="columns"
      :height="300"
      showOverflowTooltip
      :pagination="pagination"
      :loading="loading"
      @selection-change="selectChange"
      @page-current-change="pageChange"
      @page-size-change="pageSizeChange"
    >
      <template #operation="data">
        <div>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFinishProductCreate"
            type="primary"
            link
            @click="onCopyFinishingWarehousingModalVis(data.row)"
          >
            复制
          </ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFinishProductEdit"
            type="primary"
            link
            @click="onEditFinishingWarehousingModalVis(data.row)"
          >
            编辑
          </ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFinishProductDelete"
            link
            type="danger"
            @click="onDeleteFinishingWarehousing(data.row)"
          >
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>

    <el-dialog
      :title="getFinishingWarehousingFormModalTitle()"
      align-center
      draggable
      class="default"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-model="state.finishingWarehousingFormModalVis"
    >
      <FinishingWarehousingForm ref="finishingWarehousingFormRef" :mode="state.mode" />

      <template #footer>
        <el-button @click="onCancelCreateFinishingWarehousing()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleOnCreateFinishingWarehousing()">保存</el-button>
        <el-button
          v-if="state.mode === 'add'"
          type="primary"
          :loading="saveLoading"
          @click="handleOnCreateFinishingWarehousingAndAdd"
        >
          保存并新增
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useSalesFillInDataStore, useFinishedProductStorageStore, useSalesOrderDetailStore } from "@/store/modules";
import { useFinishedWarehousingHook } from "../hooks/useFinishingWarehousing";
import { computed, onMounted, reactive, ref, watchEffect } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import FinishingWarehousingForm from "./finishing-warehousing-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  ICreateFinishedProductStorage,
  IFinishedProductStorage,
  IProductOrder,
  IWorkOrder,
  IFinishedProductStorageReq
} from "@/models";
import { Plus } from "@element-plus/icons-vue";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { PermissionKey } from "@/consts";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { useTableSelect } from "./useTableSelect";
import { CircleCloseFilled } from "@element-plus/icons-vue";
import BatchModifyDialog from "./batch-modify-dialog/index.vue";

const { selectChange, selectedCount, selectedList, clear } = useTableSelect();

const { columns } = useFinishedWarehousingHook();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const state = reactive<{
  finishingWarehousingFormModalVis: boolean;
  mode: "add" | "edit" | "copy";
}>({
  finishingWarehousingFormModalVis: false,
  mode: "add"
});

const tableInstance = ref<PureTableInstance>();
const finishingWarehousingFormRef = ref<InstanceType<typeof FinishingWarehousingForm>>();
const saveLoading = ref<boolean>(false);
/** 是否处于批量操作中 */
const isBatching = computed(() => Boolean(selectedIds.value.length));
const selectedIds = computed(() => selectedList.value.map(item => item.id));

const handleOnCreateFinishingWarehousing = useLoadingFn(
  async () => await onCreateFinishingWarehousing("save"),
  saveLoading
);
const handleOnCreateFinishingWarehousingAndAdd = useLoadingFn(
  async () => await onCreateFinishingWarehousing("saveAndAdd"),
  saveLoading
);

const getFinishingWarehousingFormModalTitle = () => {
  switch (state.mode) {
    case "add":
      return "新增成品信息";
    case "edit":
      return "编辑成品信息";
    case "copy":
      return "复制成品信息";
  }
};

const finishedProductStorageStore = useFinishedProductStorageStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const salesStore = useSalesOrderDetailStore();
const fillInDataStore = useSalesFillInDataStore();
const loading = ref<boolean>(false);
const initFinishingWarehousingData = useLoadingFn(getFinishedProductList, loading);
const defaultPageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

watchEffect(() => {
  pagination.total = finishedProductStorageStore.finishedProductStoragesTotal;
});

onMounted(() => {
  initFinishingWarehousingData(defaultPageInfo);
});

async function getFinishedProductList(params: IFinishedProductStorageReq) {
  await finishedProductStorageStore.queryFinishedProductStorage(params, true);
}

function clearSelection() {
  tableInstance.value.getTableRef().clearSelection();
}

/**
 * 页码改变
 */
const pageChange = (pageNo: number) => {
  initFinishingWarehousingData({ pageNo, pageSize: pagination.pageSize });
};

/**
 * 批量修改成功
 */
const batchModifySuccess = () => {
  clear();
  initFinishingWarehousingData({ pageNo: pagination.currentPage, pageSize: pagination.pageSize });
  productionTestSensingStore.refreshProductionProcessStatus();
};

/**
 * 页码数量改变
 */
const pageSizeChange = (pageSize: number) => {
  pagination.currentPage = 1;
  initFinishingWarehousingData({ pageNo: 1, pageSize });
};

function getFinishingWarehousingDefaultValue() {
  if (salesStore.isCable) {
    const productOrder = fillInDataStore.data as IProductOrder;
    return {
      specificationModel: productOrder.specificationModel,
      voltageClasses: productOrder.voltageClasses,
      unit: productOrder.unit,
      productionId: productOrder.id,
      purchaseId: productOrder.purchaseId
    };
  }
  const workOrder = fillInDataStore.data as IWorkOrder;
  return {
    specificationModel: workOrder.specificationType,
    voltageClasses: workOrder.voltageLevel,
    unit: workOrder.unit,
    workOrderId: workOrder.id,
    productionId: fillInDataStore.productionId
  };
}

/**
 * @description: 新增成品信息
 */
const onAddFinishingWarehousingModalVis = () => {
  state.mode = "add";
  state.finishingWarehousingFormModalVis = true;

  const { specificationModel, voltageClasses, unit, productionId, workOrderId } = getFinishingWarehousingDefaultValue();
  finishedProductStorageStore.setCreateFinishedProductStorageDetail({
    specificationModel,
    voltageLevel: voltageClasses,
    unit,
    id: undefined,
    amount: undefined,
    storageTime: undefined,
    shipmentTime: undefined,
    shipmentStatus: undefined,
    isQualified: undefined,
    productionId,
    workOrderId,
    productNo: undefined,
    productBatchNo: undefined
  });
};

/**
 * @description: 编辑成品信息
 */
const onEditFinishingWarehousingModalVis = async (data: IFinishedProductStorage) => {
  state.mode = "edit";
  state.finishingWarehousingFormModalVis = true;
  const res = await finishedProductStorageStore.getFinishedProductStorageDetailById(data.id);
  if (res.code !== 0) {
    ElMessage.error(res.msg || "网络异常");
    return;
  }

  finishedProductStorageStore.setFinishedProductStorageDetail(res.data);
};

/**
 * @description: 复制成品信息
 */
const onCopyFinishingWarehousingModalVis = async (data: IFinishedProductStorage) => {
  state.mode = "copy";
  state.finishingWarehousingFormModalVis = true;
  const res = await finishedProductStorageStore.getFinishedProductStorageDetailById(data.id);
  if (res.code !== 0) {
    ElMessage.error(res.msg || "网络异常");
    return;
  }

  finishedProductStorageStore.setFinishedProductStorageDetail({ ...res.data, id: undefined, productNo: undefined });
};

const onCancelCreateFinishingWarehousing = () => {
  state.finishingWarehousingFormModalVis = false;
  finishingWarehousingFormRef.value.resetFormValue();
};

/** 保存或者修改成品信息 */
async function onCreateFinishingWarehousing(type: "save" | "saveAndAdd") {
  const formValue: ICreateFinishedProductStorage | boolean = await finishingWarehousingFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }

  // 新增/编辑
  if (!formValue.id) {
    await finishedProductStorageStore.createFinishedProductStorage(formValue);
  } else {
    await finishedProductStorageStore.editFinishedProductStorage(formValue);
  }

  if (type === "save") {
    state.finishingWarehousingFormModalVis = false;
    finishingWarehousingFormRef.value.resetFormValue();
  }

  const msg = {
    add: "新增成功",
    edit: "编辑成功",
    copy: "复制成功"
  };
  ElMessage.success(msg[state.mode]);
  if (type === "saveAndAdd") {
    finishingWarehousingFormRef.value.clearProductNo();
  }
  initFinishingWarehousingData({
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
  productionTestSensingStore.refreshProductionProcessStatus();
}

const onDeleteFinishingWarehousing = (data: IFinishedProductStorage) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const delRes = await finishedProductStorageStore.deleteFinishedProductStorage(data.id);
      if (!delRes.data) {
        ElMessage.error(delRes.msg || "网络异常");
        return;
      }
      ElMessage.success("删除成功");
      initFinishingWarehousingData(defaultPageInfo);
      productionTestSensingStore.refreshProductionProcessStatus();
    })
    .catch(() => {});
};
</script>

<style scoped lang="scss">
.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}
</style>
