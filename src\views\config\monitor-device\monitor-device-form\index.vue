<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="摄像头编号" prop="no">
          <el-input
            placeholder="请输入摄像头编号"
            :maxlength="InputLengthEnum.normal"
            v-model="form.no"
            :disabled="disabled"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="摄像头名称" prop="name">
          <el-input placeholder="请输入摄像头名称" :maxlength="InputLengthEnum.normal" v-model="form.name" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="NVR IP地址" prop="nvrIp">
          <el-input placeholder="请输入NVR IP地址" :maxlength="InputLengthEnum.normal" v-model="form.nvrIp" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="item in statusOptions" :label="item.value" border :key="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="品牌" prop="brandCode">
          <el-radio-group v-model="form.brandCode">
            <el-radio v-for="item in brandCodeOptions" :label="item.value" border :key="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="通道号" prop="channel">
          <el-input
            v-model="form.channel"
            controls-position="right"
            placeholder="请输入通道号"
            maxlength="9"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="账号" prop="account">
          <el-input placeholder="请输入账号" maxlength="20" v-model="form.account" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="密码" prop="password">
          <el-input placeholder="请输入密码" maxlength="20" v-model="form.password" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="端口号" prop="port">
          <el-input
            v-model="form.port"
            controls-position="right"
            placeholder="请输入端口号"
            clearable
            class="!w-full"
            maxlength="9"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="码流" prop="subType">
          <el-radio-group v-model="form.subType">
            <el-radio v-for="item in subTypeOptions" :label="item.value" border :key="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="设备分组" prop="groupId">
          <el-select placeholder="请选择设备分组" class="w-full" v-model="form.groupId" clearable>
            <el-option
              v-for="item in deviceGroupStore.deviceGroupList || []"
              :key="item.id"
              :label="item.groupName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="转码" prop="shouldTranscode">
          <el-radio-group v-model="form.shouldTranscode">
            <el-radio :label="true" border>开启</el-radio>
            <el-radio :label="false" border>关闭</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IOption, IMonitorDeviceForm } from "@/models";
import { useDeviceGroupStore } from "@/store/modules";
import {
  MonitorDeviceStatusEnum,
  MonitorDeviceBrandEnum,
  MonitorDeviceSubTypeEnum,
  MonitorDeviceStatusMapDesc,
  MonitorDeviceBrandMapDesc,
  MonitorDeviceSubTypeMapDesc
} from "@/enums";
import { InputLengthEnum } from "@/enums/input-length";

defineExpose({
  validate,
  getValidValue
});

const props = withDefaults(
  defineProps<{
    monitorDevice?: IMonitorDeviceForm;
    groupId?: string;
  }>(),
  {}
);

const form = reactive<IMonitorDeviceForm>({
  id: undefined,
  no: undefined,
  name: undefined,
  nvrIp: undefined,
  status: undefined,
  brandCode: undefined,
  channel: undefined,
  account: undefined,
  password: undefined,
  port: undefined,
  subType: undefined,
  groupId: undefined,
  shouldTranscode: undefined
});
const rules: FormRules = {
  no: [{ required: true, message: requiredMessage("摄像头编号"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("摄像头名称"), trigger: "change" }],
  nvrIp: [{ required: true, message: requiredMessage("NVR IP地址"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }],
  brandCode: [{ required: true, message: requiredMessage("品牌"), trigger: "change" }],
  channel: [
    { required: true, message: requiredMessage("通道号"), trigger: "change" },
    { pattern: /^[0-9]*$/, message: "请输入数字", trigger: "change" }
  ],
  account: [{ required: true, message: requiredMessage("账号"), trigger: "change" }],
  password: [{ required: true, message: requiredMessage("密码"), trigger: "change" }],
  subType: [{ required: true, message: requiredMessage("码流"), trigger: "change" }],
  port: [
    { required: true, message: requiredMessage("端口号"), trigger: "change" },
    { pattern: /^[0-9]*$/, message: "请输入数字", trigger: "change" }
  ],
  groupId: [{ required: true, message: requiredMessage("选择设备分组"), trigger: "change" }]
};

const deviceGroupStore = useDeviceGroupStore();
const formRef = ref<FormInstance>();

const statusOptions: Array<IOption> = [
  {
    label: MonitorDeviceStatusMapDesc[MonitorDeviceStatusEnum.START],
    value: MonitorDeviceStatusEnum.START
  },
  {
    label: MonitorDeviceStatusMapDesc[MonitorDeviceStatusEnum.DISABLE],
    value: MonitorDeviceStatusEnum.DISABLE
  }
];

const brandCodeOptions: Array<IOption> = [
  {
    label: MonitorDeviceBrandMapDesc[MonitorDeviceBrandEnum.HK],
    value: MonitorDeviceBrandEnum.HK
  },
  {
    label: MonitorDeviceBrandMapDesc[MonitorDeviceBrandEnum.DH],
    value: MonitorDeviceBrandEnum.DH
  }
];

const subTypeOptions: Array<IOption> = [
  {
    label: MonitorDeviceSubTypeMapDesc[MonitorDeviceSubTypeEnum.MAIN_STREAM],
    value: MonitorDeviceSubTypeEnum.MAIN_STREAM
  },
  {
    label: MonitorDeviceSubTypeMapDesc[MonitorDeviceSubTypeEnum.DISABLE],
    value: MonitorDeviceSubTypeEnum.DISABLE
  }
];

const disabled = ref<boolean>(false);

watchEffect(() => {
  if (props.monitorDevice) {
    Object.assign(form, props.monitorDevice, { shouldTranscode: props.monitorDevice.shouldTranscode ?? false });
    disabled.value = !!form.id;
  }
});
watchEffect(() => {
  if (props.groupId) {
    form.groupId = props.groupId;
  }
});
async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IMonitorDeviceForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
