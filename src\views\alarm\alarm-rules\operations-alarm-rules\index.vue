<template>
  <div class="header">
    <div class="flex items-center">
      <TitleBar title="运维告警" />
      <div class="sub-title pl-2">每24小时触发一次运维告警规则</div>
    </div>
    <el-switch v-model="operationsRules.switchOn" active-text="开" inactive-text="关" inline-prompt />
  </div>

  <div class="mt-4 ml-4">
    <div class="flex flex-col">
      <div class="flex items-center mb-3">
        <el-checkbox
          label="当设备未绑定采集点，超过"
          v-model="operationsRules.deviceNoCollectItem"
          :disabled="disabledAlarmRule"
        />
        <div class="flex items-center">
          <div class="flex items-stretch">
            <el-input-number
              class="!w-24 ml-2"
              v-model="operationsRules.overValueDeviceDays"
              :min="1"
              :step="1"
              controls-position="right"
              :disabled="!operationsRules.deviceNoCollectItem || disabledAlarmRule"
              @click.prevent
            />
            <span class="mx-2 flex items-center px-2 ui-input-group-addon">天</span>
          </div>
          <div class="text-base label">后仍未绑定采集点则产生告警</div>
        </div>
      </div>

      <div class="flex items-center mb-3">
        <el-checkbox
          label="视频监控不显示视频画面，超过"
          v-model="operationsRules.videoMonitorNoView"
          :disabled="disabledAlarmRule"
        />
        <div class="flex items-center">
          <div class="flex items-stretch">
            <el-input-number
              class="!w-24 ml-2"
              v-model="operationsRules.overValueNoViewDays"
              :min="1"
              :step="1"
              controls-position="right"
              :disabled="!operationsRules.videoMonitorNoView || disabledAlarmRule"
              @click.prevent
            />
            <span class="mx-2 flex items-center px-2 ui-input-group-addon">天</span>
          </div>
          <div class="text-base label">后仍未处理则产生告警</div>
        </div>
      </div>

      <div class="flex items-center mb-3">
        <el-checkbox
          label="自动采集的生产装备，超过"
          v-model="operationsRules.deviceNoData"
          :disabled="disabledAlarmRule"
        />
        <div class="flex items-center">
          <div class="flex items-stretch">
            <el-input-number
              class="!w-24 ml-2"
              v-model="operationsRules.overValueDeviceNoDataDays"
              :min="1"
              :step="1"
              controls-position="right"
              :disabled="!operationsRules.deviceNoData || disabledAlarmRule"
              @click.prevent
            />
            <span class="mx-2 flex items-center px-2 ui-input-group-addon">天</span>
          </div>
          <div class="text-base label">未上传数据则产生告警</div>
        </div>
      </div>

      <div class="flex items-center">
        <el-checkbox
          label="工单中所选的工序，超过"
          v-model="operationsRules.processNoReport"
          :disabled="disabledAlarmRule"
        />
        <div class="flex items-center">
          <div class="flex items-stretch">
            <el-input-number
              class="!w-24 ml-2"
              v-model="operationsRules.overValueNoReportDays"
              :min="1"
              :step="1"
              controls-position="right"
              :disabled="!operationsRules.processNoReport || disabledAlarmRule"
              @click.prevent
            />
            <span class="mx-2 flex items-center px-2 ui-input-group-addon">天</span>
          </div>
          <div class="text-base label">未产生工序报工则产生告警</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watchEffect, watch, computed } from "vue";
import TitleBar from "@/components/TitleBar";
import { IOperationsRule } from "@/models";

const props = defineProps<{
  operationsRules: IOperationsRule;
}>();

const operationsRules = reactive<Partial<IOperationsRule>>({});
const emits = defineEmits<{
  (e: "setValue", operationsRules: IOperationsRule): void;
}>();

// 是否禁用告警配置
const disabledAlarmRule = computed(() => !operationsRules.switchOn);

watchEffect(() => {
  if (props.operationsRules != operationsRules) {
    Object.assign(operationsRules, props.operationsRules);
  }
});

watch(operationsRules, newValue => {
  emits("setValue", newValue as IOperationsRule);
});
</script>

<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;

  .sub-title {
    font-size: var(--el-font-size-extra-small);
    color: var(--el-text-color-placeholder);
    margin-left: 4px;
  }
}

.el-radio-group {
  display: grid;
}

.ui-input-group-addon {
  color: var(--el-text-color-secondary);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  box-shadow: 0 0 0 0.0625rem var(--el-input-border-color, var(--el-border-color)) inset;
}

:deep {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--el-text-color-regular);
  }
}

.label {
  color: var(--el-text-color-regular);
}
</style>
