<template>
  <pure-table
    ref="tableInstance"
    show-overflow-tooltip
    row-key="id"
    :height="300"
    :columns="columns"
    :data="ctx.data"
    :loading="ctx.loading"
    :pagination="ctx.pagination"
    @row-click="handleRowClick"
    @page-size-change="ctx.refresh"
    @page-current-change="ctx.refresh"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import { PureTable, TableColumns } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty";
import { inject, toRef } from "vue";
import { IMaterial } from "@/models";
import { materialSelectKey } from "./token";
import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

const ctx = inject(materialSelectKey);
const { singleSelectFormatter } = useTableCellFormatter();

const columns: Array<TableColumns> = [
  {
    label: "",
    prop: "id",
    width: 40,
    fixed: "left",
    align: "center",
    formatter: singleSelectFormatter(toRef(ctx, "selectedId"), { validateEvent: false })
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "物料编号",
    prop: "materialCode"
  },
  {
    label: "物料名称",
    prop: "materialName"
  },
  {
    label: "物料描述",
    prop: "materialDescribe"
  },
  {
    label: "物料单位",
    prop: "materialUnitName"
  },
  {
    label: "规格型号",
    prop: "specificationModel"
  },
  {
    label: "电压等级",
    prop: "voltageClass"
  }
];

function handleRowClick(material: IMaterial) {
  ctx.selectedId = material.id;
  ctx.selectedMaterial = material;
}
</script>

<style scoped></style>
