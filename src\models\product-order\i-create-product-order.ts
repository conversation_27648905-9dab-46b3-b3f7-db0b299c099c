import { ISalesOrderLine } from "../sales-order-line";
import { IProductOrder } from "./i-product-order";

/** 创建采购订单 */
export type ICreateProductOrder = Pick<
  IProductOrder,
  | "id"
  | "ipoNo"
  | "ipoStatus"
  | "applicationType"
  | "jointCategories"
  | "voltageType"
  | "voltageClasses"
  | "amount"
  | "unit"
  | "specificationModel"
  | "planStartDate"
  | "planFinishDate"
  | "actualStartDate"
  | "actualFinishDate"
  | "purchaseId"
  | "salesLineId"
  | "remark"
  | "materialId"
  | "quantityShipped"
  | "quantityWarehousing"
> & {
  salesLineIds?: string[];
  categoryCode?: string;
  saleId?: string;
  materialsCode?: string;
  materialsName?: string;
  materialsUnit?: string;
  materialCode?: string;
  subClassCode?: string;
  salesLineDetails?: ISalesOrderLine[];
};
