import { defineStore } from "pinia";
import * as api from "@/api/production-test-sensing/copy-product-order";
import { IOrdersForChooseReq } from "@/models";

export const useCopyProductOrderStore = defineStore({
  id: "copy-product-order-store",
  state: () => ({}),
  actions: {
    /** 获取某一个生产订单或者工单下的原材检数据 */
    async getRawMaterialInspectFromOrder(params: IOrdersForChooseReq) {
      return (await api.getRawMaterialInspectFromOrder(params)).data;
    },

    /** 获取某一条生产订单或者工单下的过程检测数据 */
    async getProcessInspectFromOrder(params: IOrdersForChooseReq) {
      return (await api.getProcessInspectFromOrder(params)).data;
    },

    /** 获取某一条生产订单或者工单下的出厂试验数据 */
    async getOutFactoryExperimentFromOrder(params: IOrdersForChooseReq) {
      return (await api.getOutFactoryExperimentFromOrder(params)).data;
    }
  }
});
