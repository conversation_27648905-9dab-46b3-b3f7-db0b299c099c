import { IListResponse, IAlarmData, IAlarmDataParam, IResponse, IAlarmSolveReq, IAlarmDataDetail } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";
import { AlarmTypeEnum } from "@/enums";

export const useAlarmDataStore = defineStore({
  id: "cx-alarm-data",
  state: () => ({
    total: 0,
    alarmDataDetail: {} as IAlarmDataDetail
  }),
  actions: {
    // 查询告警数据列表数据
    async queryAlarm(params: IAlarmDataParam) {
      const res: IListResponse<IAlarmData> = await api.queryAlarm(params, this.getUrlByAlarmType(params.alarmType));
      this.total = res.data.total;
      return res.data.list;
    },

    // 根据ID查询质量告警数据详情
    async getAlarmDataById(id: string, alarmType: AlarmTypeEnum) {
      const res: IResponse<IAlarmDataDetail> = await api.getAlarmDataById(id, this.getUrlByAlarmType(alarmType));
      this.alarmDataDetail = res.data;
      return res.data;
    },
    // 根据ID解决质量告警
    async solveAlarmById(params: IAlarmSolveReq, alarmType: AlarmTypeEnum) {
      const res: IResponse<boolean> = await api.solveAlarmById(params, this.getUrlByAlarmType(alarmType));
      return res.data;
    },
    // 告警数据明细详情赋值
    setAlarmDataStorage(alarmDataDetail: IAlarmData) {
      this.alarmDataDetail = alarmDataDetail;
    },
    // 告警数据明细详情清空
    clearAlarmDataStorage() {
      this.alarmDataDetail = {};
    },
    getUrlByAlarmType(alarmType: AlarmTypeEnum) {
      let url = ``;
      switch (alarmType) {
        case AlarmTypeEnum.qulityAlarm:
          url = "alarm-quality";
          break;
        case AlarmTypeEnum.progressAlarm:
          url = "alarm-progress";
          break;
        case AlarmTypeEnum.devOpsAlarm:
          url = "dev-ops-alarm";
          break;
        default:
          break;
      }
      return url;
    }
  }
});
