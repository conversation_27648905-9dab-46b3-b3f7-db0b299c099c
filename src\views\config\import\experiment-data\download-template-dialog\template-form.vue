<template>
  <el-form
    ref="formRef"
    :model="formValue"
    :rules="rules"
    class="cx-form"
    label-position="top"
    :validate-on-rule-change="false"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item class="flex-1 gap-y-10" label="物资种类" prop="subClassCode">
          <subclass-select ref="subClassSelectRef" v-model="formValue.subClassCode" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item class="flex-1 gap-y-10" label="工序" prop="processCode">
          <experiment-process-selector
            ref="experimentProcessRef"
            :sub-class-code="formValue.subClassCode"
            :exclude-process-code="['JFNY']"
            v-model:process-code="formValue.processCode"
            v-model="formValue.processId"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import ExperimentProcessSelector from "@/views/components/process-selector/experiment-process-selector.vue";
import SubclassSelect from "@/views/components/subclass-select";

const formRef = ref<FormInstance>();
const subClassSelectRef = ref<InstanceType<typeof SubclassSelect>>();
const experimentProcessRef = ref<InstanceType<typeof ExperimentProcessSelector>>();

const formValue = reactive({
  subClassCode: "",
  processId: "",
  processCode: ""
});

const rules: FormRules = {
  subClassCode: [{ required: true, message: "请选择物资种类", trigger: "change" }],
  processCode: [{ required: true, message: "请选择工序", trigger: "change" }]
};

watch(
  () => formValue.subClassCode,
  () => {
    formValue.processId = "";
    formValue.processCode = "";
  }
);

/**
 * @description: 获取选中表单项的name
 */
function getSelectedInfo() {
  const subClassName = subClassSelectRef.value.getSelectedSubClassName();
  const experimentProcessName = experimentProcessRef.value.getSelectedProcessName();
  return {
    subClassName,
    experimentProcessName
  };
}

/**
 * @description: 验证表单，并返回验证结果
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单值
 */
function initFormValue(v: any) {
  Object.assign(formValue, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formValue);
}

defineExpose({
  getSelectedInfo,
  validateForm,
  initFormValue,
  getFormValue
});
</script>
