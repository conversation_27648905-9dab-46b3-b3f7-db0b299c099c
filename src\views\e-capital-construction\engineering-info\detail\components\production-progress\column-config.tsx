import { TableWidth, DataTypeEnumEjjMapDesc, ProcedureTypeEnumMapDesc, MainRegulatEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 生产进度
 */
export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig: TableColumnList = [];
  const commonColumns: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      minWidth: TableWidth.operation
    }
  ];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "子实物ID",
        prop: "subPhysicalItemId",
        minWidth: TableWidth.largeName
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      },
      {
        label: "进度计划次数",
        prop: "progressPlanNum",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return data.row.progressPlanNum ? `第${data.row.progressPlanNum}次` : "--";
        }
      },
      {
        label: "计划开始时间",
        prop: "plannedStartTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "计划结束时间",
        prop: "plannedEndTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "实际开始时间",
        prop: "actualStartTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "实际完成时间",
        prop: "actualEndTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "所属工序",
        prop: "procedureType",
        minWidth: TableWidth.largeName,
        cellRenderer: (data: TableColumnRenderer) => {
          return ProcedureTypeEnumMapDesc[data.row.procedureType];
        }
      },
      {
        label: "主体变/调补变类型",
        prop: "mainRegulatingTransformer",
        minWidth: TableWidth.largeType,
        cellRenderer: (data: TableColumnRenderer) => {
          return MainRegulatEnumMapDesc[data.row.mainRegulatingTransformer];
        }
      },
      {
        label: "进度计划次数",
        prop: "progressPlanNum",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return data.row.progressPlanNum ? `第${data.row.progressPlanNum}次` : "--";
        }
      },
      {
        label: "计划开始时间",
        prop: "plannedStartTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "计划完成时间",
        prop: "plannedEndTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "实际开始时间",
        prop: "actualStartTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "实际完成时间",
        prop: "actualEndTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "计划具备发运时间",
        prop: "plannedReadyForShipmentTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "实际具备发运时间",
        prop: "actualReadyForShipmentTime",
        minWidth: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "物资供应计划具备发运时间",
        prop: "provisioningPlannedReadyForShipmentTime",
        minWidth: TableWidth.dateRanger,
        formatter: dateFormatter()
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      }
    ];
  }
  return [...columnsConfig, ...commonColumns];
}
