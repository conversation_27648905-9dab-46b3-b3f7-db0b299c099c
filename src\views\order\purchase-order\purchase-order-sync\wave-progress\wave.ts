import { Point } from "./point";

export class Wave {
  private readonly index: number;
  private readonly totalPoints: number;
  private color: string;
  private points: Array<Point>;
  private stageWidth: number;
  private stageHeight: number;
  private pointGap: number;

  constructor(index, totalPoints) {
    this.index = index;
    this.totalPoints = totalPoints;
    this.points = [];
  }
  resize(stageWidth: number, stageHeight: number) {
    this.stageWidth = stageWidth;
    this.stageHeight = stageHeight;

    this.pointGap = this.stageWidth / (this.totalPoints - 1);

    this.init();
  }

  init() {
    this.points = [];
    for (let i = 0; i < this.totalPoints; i++) {
      this.points[i] = new Point(this.index + i, this.pointGap * i);
    }
  }

  setOffsetY(offsetY: number) {
    this.points.forEach(p => p.setOffsetY(offsetY));
  }

  setColor(color: string) {
    this.color = color;
  }

  draw(ctx: CanvasRenderingContext2D, complete?: boolean) {
    ctx.beginPath();
    ctx.fillStyle = this.color;

    if (!complete) {
      let prevX = this.points[0].x;
      let prevY = this.points[0].y;
      ctx.moveTo(prevX, prevY);
      for (let i = 0; i < this.totalPoints; i++) {
        this.points[i].update();

        const cx = (prevX + this.points[i].x) / 2;
        const cy = (prevY + this.points[i].y) / 2;
        ctx.quadraticCurveTo(prevX, prevY, cx, cy);
        prevX = this.points[i].x;
        prevY = this.points[i].y;
      }
      ctx.lineTo(prevX, prevY);
      ctx.lineTo(this.stageWidth, this.stageHeight);
      ctx.lineTo(this.points[0].x, this.stageHeight);
    } else {
      ctx.lineTo(0, 0);
      ctx.lineTo(this.stageWidth, 0);
      ctx.lineTo(this.stageWidth, this.stageHeight);
      ctx.lineTo(0, this.stageHeight);
    }
    ctx.closePath();
    ctx.fill();
  }
}
