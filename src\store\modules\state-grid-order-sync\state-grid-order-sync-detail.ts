import { defineStore } from "pinia";
import { IIOTStateGridOrderSync, IOrderSyncChannelReq, IStateGridOrderSync } from "@/models";
import { PurchaseChannel, StateGridOrderSyncStep } from "@/enums";
import { usePurchaseOrderDetailStore, useStateGridOrderSyncListStore } from "@/store/modules";
import * as api from "@/api/state-grid-order-sync";
import * as orderSyncApi from "@/api/order-sync";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";

type StateGridOrderSyncDetailType = {
  sync: IStateGridOrderSync;
  dialogVisible: boolean;
  activeStepKey: StateGridOrderSyncStep;
  title: string;
  purchaseOrderId: string;
  iotSync: IIOTStateGridOrderSync;
  type: SyncOrderTabEnum;
  // 当前渠道
  channel: PurchaseChannel;
  subClassCode: string;
};

export const useStateGridOrderSyncDetailStore = defineStore({
  id: "purchase-order-sync-info",
  state: (): StateGridOrderSyncDetailType => ({
    sync: {} as IStateGridOrderSync,
    dialogVisible: false,
    activeStepKey: null,
    title: "",
    purchaseOrderId: "",
    iotSync: {} as IIOTStateGridOrderSync,
    type: null,
    channel: undefined,
    subClassCode: ""
  }),
  getters: {
    /** 是否是国网渠道 */
    isEipChannel() {
      return this.channel === PurchaseChannel.EIP;
    },
    /** 是否是广州供电局渠道 */
    isGuangzhouChannel() {
      return this.channel === PurchaseChannel.CSG_GuangZhou;
    },
    /** 是否是上海平台渠道 */
    isShanghaiIotChannel() {
      return this.channel === PurchaseChannel.IOT;
    }
  },
  actions: {
    openDialog(sync: IStateGridOrderSync) {
      this.setSyncData(sync);
      this.type = SyncOrderTabEnum.SYNC_STATE__GRID_ORDER;
      this.dialogVisible = true;
      this.title = `【国网平台】采购订单号：${sync.poNo} \u00a0 采购订单行项目号：${sync.poItemNo}`;
    },
    toggleActiveStep(step: StateGridOrderSyncStep) {
      this.activeStepKey = step;
    },
    async refreshSyncDetail() {
      if (this.channel === PurchaseChannel.EIP) {
        const data = (await api.getStateGridSyncDetail(this.sync.id)).data;
        this.sync.detail = data;
        useStateGridOrderSyncListStore().updateStateGridOrderSyncDetail(this.sync.id, data);
      } else {
        this.refreshIOTSyncDetail();
      }
    },
    setSyncData(sync: IStateGridOrderSync) {
      const purchaseDetailStore = usePurchaseOrderDetailStore();
      this.sync = sync;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.purchaseOrderId = sync.purchaseOrderId || purchaseDetailStore.purchaseOrderId;
    },
    setSyncType(type: string) {
      this.type = type;
    },
    setChannel(channel: PurchaseChannel) {
      this.channel = channel;
    },
    // 打开上海IOT，同步详情
    openIOTDialog(sync: IIOTStateGridOrderSync) {
      const purchaseDetailStore = usePurchaseOrderDetailStore();
      this.iotSync = sync;
      this.type = SyncOrderTabEnum.SYNC_SHANGHAI_IOT;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.purchaseOrderId = purchaseDetailStore.purchaseOrderId;
      this.channel = PurchaseChannel.IOT;
      this.dialogVisible = true;
      this.title = `【上海平台】销售订单号：${sync.soNo} \u00a0 销售订单行项目号：${sync.soItemNo}`;
    },
    /** 打开csg平台广州供电局同步详情 */
    openCsgGuangzhouDialog(sync: IIOTStateGridOrderSync) {
      const purchaseDetailStore = usePurchaseOrderDetailStore();
      this.iotSync = sync;
      this.type = SyncOrderTabEnum.SYNC_CSG_GUANGZHOU;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.purchaseOrderId = purchaseDetailStore.purchaseOrderId;
      this.channel = PurchaseChannel.CSG_GuangZhou;
      this.dialogVisible = true;
      this.title = `【广州供电局】销售订单号：${sync.soNo} \u00a0 销售订单行项目号：${sync.soItemNo}`;
    },
    // 更新IOT列表的详情（包括总数，失败数，等）
    async refreshIOTSyncDetail() {
      const data = (await api.getIotOrCsgGuangzhouSalesOrderLineDetail(this.iotSync.id, this.channel)).data;
      this.iotSync.detail = data;
      useStateGridOrderSyncListStore().updateIotOrCsgSyncDetail(this.iotSync.id, data);
    },

    /** 根据授权，租户IOT EIP配置，当前物资种类 查询 订单同步渠道 */
    async getOrderSyncChannel(data: IOrderSyncChannelReq) {
      return (await orderSyncApi.getOrderSyncChannel(data)).data;
    }
  }
});
