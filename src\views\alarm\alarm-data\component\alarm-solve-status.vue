<template>
  <CxTag :type="type" v-if="name">{{ name }}</CxTag>
</template>

<script setup lang="ts">
import CxTag from "@/components/CxTag/index.vue";
import { AlarmSolveStatusEnum, AlarmSolveStatusEnumMapDisplayName } from "@/enums";
import { computed } from "vue";

const props = defineProps<{
  status: AlarmSolveStatusEnum;
}>();

const type = computed(() => {
  return props.status == AlarmSolveStatusEnum.UNRESOLVED
    ? "warning"
    : props.status == AlarmSolveStatusEnum.RESOLVED
    ? "success"
    : "danger";
});

const name = computed(() => {
  return AlarmSolveStatusEnumMapDisplayName[props.status];
});
</script>

<style scoped></style>
