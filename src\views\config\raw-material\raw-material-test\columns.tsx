import { downLoadFile } from "@/api/upload-file";
import { dateFormat } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums/table-width.enum";
import { previewUploadFile } from "@/utils/uploadFiles";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { downloadByData } from "@pureadmin/utils";
import { ElButton, ElDivider, ElPopover } from "element-plus";

const { dateFormatter } = useTableCellFormatter();
export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "检验批次号",
      prop: "inspectBatchNo",
      width: TableWidth.largeOrder
    },
    {
      label: "检验日期",
      prop: "inspectDate",
      width: TableWidth.dateTime,
      formatter: dateFormatter(dateFormat)
    },
    {
      label: "生产厂检测报告",
      prop: "inspectionReport",
      minWidth: TableWidth.largeName,
      formatter: (row: any) => {
        return row.inspectionReport?.length > 0 ? (
          <div class="text-ellipsis overflow-hidden">
            {row?.inspectionReport.map(file => {
              return previewDownload(file);
            })}
          </div>
        ) : (
          <span>--</span>
        );
      }
    },
    {
      label: "来料检测报告",
      prop: "spotCheckReport",
      minWidth: TableWidth.largeName,
      formatter: (row: any) => {
        return row.spotCheckReport?.length > 0 ? (
          <div class="text-ellipsis overflow-hidden">
            {row?.spotCheckReport.map(file => {
              return previewDownload(file);
            })}
          </div>
        ) : (
          <span>--</span>
        );
      }
    },
    {
      label: "备注",
      prop: "remark",
      width: TableWidth.largeName,
      hide: () => true
    },
    {
      label: "操作",
      prop: "op",
      width: ColumnWidth.Char9,
      fixed: "right",
      slot: "operate"
    }
  ];

  // 预览文件
  const previewFile = fileData => {
    const { id, name, url } = fileData;
    if (id) {
      previewUploadFile({ id, name, url });
    }
  };

  async function downloadFile(id: string, name: string) {
    const blob = await downLoadFile(id);
    downloadByData(blob, name, blob.type);
  }

  const previewDownload = (file: { id: string; name: string }) => {
    return (
      <ElPopover width={60} placement="left" trigger="hover">
        {{
          default: () => (
            <div class="flex flex-col items-center">
              <ElButton type="primary" link onClick={() => previewFile(file)}>
                预览
              </ElButton>
              <ElDivider class="!my-3" />
              <ElButton type="primary" link onClick={() => downloadFile(file.id, file.name)}>
                下载
              </ElButton>
            </div>
          ),
          reference: () => <span class="file-info text-primary cursor-pointer">{file.name}</span>
        }}
      </ElPopover>
    );
  };

  return {
    columns
  };
}
