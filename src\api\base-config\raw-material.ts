import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models";
import {
  IProductionStageProcessCode,
  IRawMaterialCheckCollectionItem,
  IRawMaterialReq,
  IResponseRawMaterial,
  ISearchRawList,
  SubClassItemByRawMaterial
} from "@/models/raw-material/i-raw-material-res";
import {
  IAddRawMaterialInspection,
  IRawMaterialInspectDetail,
  IRawMaterialList,
  ISaveAddRawMaterial,
  ISearchRawMaterialInspecReq,
  ISearchRawMaterialListReq
} from "@/models/raw-material/i-raw-material-res-v2";

/**
 * 根据租户信息获取原材料类型
 */
export function getRawMaterilKinds() {
  const url = withApiGateway(`admin-api/system/ProductionStageProcess/getProductionStageProcessCodeAndName`);
  return http.get<void, IResponse<Array<IProductionStageProcessCode>>>(url);
}

/**
 * 获取原材料检测数据
 */
export function getRawMaterialList(data: ISearchRawList) {
  const url: string = withApiGateway(`admin-api/business/rawMaterialProcess/page`);
  return http.post<ISearchRawList, IResponse<IResponseRawMaterial>>(url, { data });
}

/**
 * 新增原材料检测数据
 */
export function addRawMaterialCheck(paramData: IRawMaterialReq) {
  const url: string = withApiGateway(`admin-api/business/rawMaterialProcess/save`);
  return http.post<IRawMaterialReq, IResponse<boolean>>(url, { data: paramData });
}

/**
 * 获取单个原材料检测信息
 */
export function getOneRawMaterialCheckInfo(materialId: number) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/getOne/${materialId}`);
  return http.get<number, IResponse<IRawMaterialReq>>(url);
}

/**
 * 通过工序获取原材料检测信息 优化版本（v2）继续使用
 */
export function getRawMaterialCheckInfoByProductStePro(processCode: string, specificationModel: string) {
  const url = withApiGateway(
    `admin-api/system/metadataModelInfo/findCategoryByProcessCodeAndSpecificationModel/${processCode}`
  );
  return http.get<string, IResponse<Array<IRawMaterialCheckCollectionItem>>>(url, {
    params: {
      specificationModel
    }
  });
}

/**
 * 通过原材料code获取物资种类列表
 */
export function getSubClassListByRawMaterialCode(processCode: string) {
  const url = withApiGateway(`admin-api/system/metadataModelInfo/findSubClassListByProcessCode/${processCode}`);
  return http.get<string, IResponse<Array<SubClassItemByRawMaterial>>>(url);
}

/** ========== 原材料优化V2 =============== */
/**
 * 根据租户信息获取工序列表
 */
export function getRawMaterialKindsV2() {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/process/list`);
  return http.get<void, IResponse<Array<IProductionStageProcessCode>>>(url);
}

/**
 * 根据工序信息获取当前工序下的原材料列表
 * @params pageNo, pageSize, keyWords, processCode
 */
export function getRawMaterialListByProcessCode(params: ISearchRawMaterialListReq) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/page`);
  return http.post<ISearchRawMaterialListReq, IListResponse<IRawMaterialList>>(url, { data: params });
}

/**
 * 保存新增的原材料信息
 */
export function saveAddRawmaterialInfo(params: ISaveAddRawMaterial) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/create`);
  return http.post<ISaveAddRawMaterial, IResponse<boolean>>(url, { data: params }, { showErrorInDialog: true });
}

/**
 * 更新新增的原材料信息
 */
export function putAddRawmaterialInfo(params: ISaveAddRawMaterial) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/update`);
  return http.put<ISaveAddRawMaterial, IResponse<boolean>>(url, { data: params }, { showErrorInDialog: true });
}

/**
 * 获取单个原材料的详情信息
 * @param id
 */
export function getDetailRawMaterialById(id: string) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/detail/${id}`);
  return http.get<void, IResponse<ISaveAddRawMaterial>>(url);
}

/**
 * 删除原材料信息
 */
export function delAddRawmaterialInfo(id: string) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}

/**
 * 查询原材料检验批次分页列表
 */
export function getRawMaterialInspecListByCode(params: ISearchRawMaterialInspecReq) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/inspect/page`);
  return http.post<ISearchRawMaterialInspecReq, IListResponse<IRawMaterialList>>(url, { data: params });
}

/**
 * 新增原材料检
 */
export function addRawmaterialInspecInfo(params: IAddRawMaterialInspection) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/inspect/create`);
  return http.post<IAddRawMaterialInspection, IResponse<boolean>>(url, { data: params }, { showErrorInDialog: true });
}

/**
 * 编辑原材料检
 */
export function putRawmaterialInspecInfo(params: IAddRawMaterialInspection) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/inspect/update`);
  return http.put<IAddRawMaterialInspection, IResponse<boolean>>(url, { data: params }, { showErrorInDialog: true });
}

/**
 * 查询单个原材料检测的信息
 */
export function getDetailRawMaterialInspectInfoById(id: string) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/inspect/detail/${id}`);
  return http.get<void, IResponse<IRawMaterialInspectDetail>>(url);
}

/**
 * 删除原材料检信息
 */
export function delRawmaterialInspecInfo(id: string) {
  const url = withApiGateway(`admin-api/business/rawMaterialProcess/v2/raw-material/inspect/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}
