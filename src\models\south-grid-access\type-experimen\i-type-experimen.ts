import { SyncStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";
import { ITypeExperimenItem } from "../type-experimen-item";

export interface ITypeExperimen extends IBase {
  /**
   * 规格型号
   */
  model?: string;
  /**
   * 设备型号规格名称
   */
  modelName?: string;
  /**
   * 试验报告编号
   */
  testReportNo?: string;
  /**
   * 报告日期
   */
  testReportDate?: string;
  /**
   * 实验报告
   */
  testReportId?: string;
  testReportFileId?: string;
  testReportFileName?: string;
  fileUrl?: string;
  isSync: SyncStatusEnum;

  technicalDatas?: Array<ITypeExperimenItem>;
}
