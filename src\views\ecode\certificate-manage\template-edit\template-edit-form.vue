<template>
  <div class="inline-block mx-3">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      title="上传合格证"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="24">
            <el-form-item required prop="excelFile" label="合格证模板">
              <el-upload
                class="w-full"
                ref="uploadRef"
                drag
                v-model:file-list="fileList"
                :auto-upload="false"
                :accept="excelAccept"
                :on-change="onFileChange"
                :limit="1"
                :on-exceed="onExceed"
                :on-remove="onRemove"
              >
                <el-icon size="24"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  <div class="upload-text"><span>将模板文件拖到此处，或</span><em>点击上传</em></div>
                  <div class="upload-tips">
                    <span>仅支持excel格式文件</span>
                  </div>
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, watchEffect } from "vue";
import { ElMessage, FormInstance, FormRules, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { ICertificateForm } from "../../models";
import { uploadFile } from "../../api/upload-file";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { editCertificate } from "../../api/certificate-manage";

const emits = defineEmits(["postSaveSuccess"]);
const excelTypes = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"];
const excelAccept: string = excelTypes.join(",");
const dialogVisible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const excelFile = ref<File>();
const fileList = ref<Array<File>>([]);
const uploadRef = ref();
const form = reactive<ICertificateForm>({ id: undefined, templateName: undefined });

const rules: FormRules = {
  excelFile: [{ required: true, message: "请选择合格证模板", validator: validateExcelFile }]
};

const props = withDefaults(
  defineProps<{
    templateName: string;
    id: string;
    size: string;
    location: string;
  }>(),
  {
    templateName: "",
    size: "",
    location: ""
  }
);

watchEffect(() => {
  form.templateName = props.templateName;
  form.id = props.id;
  form.size = props.size;
  form.location = props.location;
});

watch(excelFile, () => {
  formRef.value.clearValidate(["excelFile"]);
});

const requestSave = useLoadingFn(async (form: ICertificateForm) => {
  const { data } = await editCertificate(form);
  return data;
}, loading);

const onExceed = (files: Array<File>) => {
  uploadRef.value?.handleRemove(files);
  fileList.value = files;
  if (files && files.length) {
    excelFile.value = files[0];
  }
};

const onRemove = () => {
  fileList.value = undefined;
  excelFile.value = undefined;
};

const onFileChange = async (file: UploadFile) => {
  excelFile.value = file.raw;
};

function validateExcelFile(rule: any, value: any, callback: Function) {
  if (!excelFile.value) {
    return callback(new Error("请选择合格证模板"));
  }
  return callback();
}

const handleClickSaveBtn = async () => {
  const validResult = await formRef.value.validate(valid => valid);
  if (!validResult) {
    return;
  }
  const formData = new FormData();
  formData.append("file", excelFile.value);
  const { data } = await uploadFile(formData);
  // 请求保存
  const res = await requestSave({
    templateName: form.templateName,
    id: form.id,
    fileId: data.id,
    bucketName: data.bucket,
    objectName: data.name,
    size: form.size,
    location: form.location
  });
  if (res) {
    closeDialog();
    emits("postSaveSuccess");
    ElMessage({ message: "编辑成功", type: "success" });
  }
};

function openDialog() {
  dialogVisible.value = true;
}

function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
