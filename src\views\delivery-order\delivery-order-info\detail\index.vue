<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="供货单项目号" prop="poItemNo">
          <el-input
            placeholder="请输入供货单项目号"
            :maxlength="InputLengthEnum.normal"
            v-model="form.poItemNo"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收货方公司名称" prop="receivedName">
          <el-input
            placeholder="请输入收货方公司名称"
            :maxlength="InputLengthEnum.normal"
            v-model="form.receivedName"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            placeholder="请输入物料编码"
            :maxlength="InputLengthEnum.normal"
            v-model="form.materialCode"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资品类" prop="categoryCode">
          <CategorySelect v-model="form.categoryCode" placeholder="请选择物资品类" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <SubclassSelect
            v-model="form.subClassCode"
            :category-code="form.categoryCode"
            limit-by-category-code
            placeholder="请选择物资种类"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="采购数量" prop="amount">
          <el-input-number
            v-model="form.amount"
            :min="0"
            controls-position="right"
            class="!w-full"
            placeholder="请选择采购数量"
            :max="MAX_NUMBER"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="计量单位" prop="measUnit">
          <el-input placeholder="请输入计量单位" maxlength="10" v-model="form.measUnit" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input placeholder="请输入物料描述" maxlength="50" v-model="form.materialDesc" type="textarea" :rows="2" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input placeholder="请输入备注" maxlength="50" v-model="form.remark" type="textarea" :rows="2" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect, watch } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDeliveryOrderDetailListData } from "@/models";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import CategorySelect from "@/views/components/category-select/category-select.vue";
import { useDeliveryOrderStore } from "@/store/modules/delivery-order";
import { ALPHA_NUM_REGEXP, MEAS_UNIT_REGEXP } from "@/consts";
import { useCategoryStore } from "@/store/modules";
import { MAX_NUMBER } from "@/consts";
import { InputLengthEnum } from "@/enums/input-length";

defineExpose({
  getValidValue,
  validate
});

const formRef = ref<FormInstance>();
const deliveryOrderStore = useDeliveryOrderStore();
const form = reactive<IDeliveryOrderDetailListData>({
  id: null,
  deliveryOrderId: null,
  poItemNo: null,
  receivedName: null,
  materialCode: null,
  materialDesc: null,
  categoryCode: null,
  subClassCode: null,
  categoryName: null,
  subClassName: null,
  amount: null,
  measUnit: null,
  remark: null
});
const rules: FormRules = {
  poItemNo: [
    { required: true, trigger: "change", message: requiredMessage("供货单项目号") },
    { trigger: "change", message: "供货单项目号 不允许输入符号", pattern: ALPHA_NUM_REGEXP }
  ],
  receivedName: [{ required: true, message: requiredMessage("收货方公司名称"), trigger: "change" }],
  categoryCode: [{ required: true, message: requiredMessage("品类"), trigger: "change" }],
  subClassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  materialCode: [{ required: true, message: requiredMessage("物料编码"), trigger: "change" }],
  materialDesc: [{ required: true, message: requiredMessage("物料描述"), trigger: "change" }],
  amount: [{ required: true, message: requiredMessage("采购数量"), trigger: "change" }],
  measUnit: [
    { required: true, trigger: "change", message: requiredMessage("计量单位") },
    { trigger: "change", message: "计量单位 单位格式不正确", pattern: MEAS_UNIT_REGEXP }
  ],
  remark: [{ required: false }]
};
const categoryStore = useCategoryStore();

watchEffect(() => {
  if (deliveryOrderStore.deliveryOrderDetail) {
    Object.assign(form, deliveryOrderStore.deliveryOrderDetail);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IDeliveryOrderDetailListData> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}

watch(
  () => form.categoryCode,
  (newVal, oldVal) => {
    form.categoryName = categoryStore.categories.find(
      categories => categories.categoryCode == form.categoryCode
    ).categoryName;
    if (oldVal != form.categoryCode) {
      form.subClassCode = null;
      form.subClassName = null;
    }
  }
);

watch(
  () => form.subClassCode,
  async () => {
    if (!categoryStore.subclasses.length) {
      await categoryStore.getAllSubclasses();
    }
    if (form.subClassCode) {
      form.subClassName = categoryStore.subclasses.find(
        subclasses => subclasses.categoryCode == form.subClassCode
      ).categoryName;
    }
  }
);

watchEffect(() => {
  if (!form.id) {
    Object.assign(form, deliveryOrderStore.deliveryOrderDetail);
  }
});
</script>

<style scoped></style>
