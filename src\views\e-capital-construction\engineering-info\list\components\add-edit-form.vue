<template>
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item
          label="所属工程ID"
          prop="projectId"
          :rules="{ required: true, message: '请输入所属工程ID', trigger: 'change' }"
        >
          <el-input v-model="formData.projectId" placeholder="请输入所属工程ID" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="物资种类"
          prop="equipmentCode"
          :rules="{ required: true, message: '请选择物资种类', trigger: 'change' }"
        >
          <el-select
            v-model="formData.equipmentCode"
            class="w-full"
            :disabled="editMode"
            placeholder="请选择物资种类"
            clearable
            filterable
          >
            <el-option v-for="(val, key) in equipmentAll" :key="key" :label="val.name" :value="Number(key)" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="设备名称"
          prop="equipmentName"
          :rules="{ required: true, message: '请输入设备名称', trigger: 'change' }"
        >
          <el-select
            v-model="formData.equipmentName"
            class="w-full"
            :disabled="editMode"
            placeholder="请选择设备名称"
            clearable
            filterable
          >
            <el-option
              v-for="item in EquipmentNameEnumOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="电压等级"
          prop="voltageLevel"
          :rules="{ required: true, message: '请选择电压等级', trigger: 'change' }"
        >
          <el-select v-model="formData.voltageLevel" class="w-full" placeholder="请选择电压等级" clearable filterable>
            <el-option v-for="(val, key) in voltageList" :key="key" :label="val" :value="key" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="设备型号"
          prop="equipmentModel"
          :rules="{
            required: formData.equipmentCode == EquipmentTypeEnumExt.Combiner,
            message: '请输入设备型号',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.equipmentModel" maxlength="50" placeholder="请输入设备型号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="厂内生产工号"
          prop="factoryWorkNumber"
          :rules="{ required: true, message: '请输入厂内生产工号', trigger: 'change' }"
        >
          <el-input v-model="formData.factoryWorkNumber" maxlength="50" placeholder="请输入厂内生产工号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="关联生产工单" prop="refWoNo">
          <div class="flex w-full">
            <el-input class="flex-1 mr-2" v-model="formData.refWoNo" placeholder="请选择 生产工单" disabled />
            <work-order-table-select
              :subClassCode="EquipmentTypeEnumToEIPSubClassCodeMap[formData.equipmentCode]"
              :selectedId="formData.refWoId"
              @work-order-change="workOrderChange"
            />
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实物ID" prop="utcNum">
          <el-input v-model="formData.utcNum" placeholder="请输入实物ID" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="formData.equipmentCode == EquipmentTypeEnumExt.Combiner" :span="12">
        <el-form-item
          label="生产批次"
          prop="productionBatch"
          :rules="{ required: true, message: '请输入生产批次', trigger: 'change' }"
        >
          <el-input v-model="formData.productionBatch" placeholder="请输入生产批次" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="formData.equipmentCode == EquipmentTypeEnumExt.ElectricReactor" :span="12">
        <el-form-item
          label="电抗器结构"
          prop="reactorStructure"
          :rules="{
            required: formData.equipmentName == EquipmentNameEnum.ElectricReactor,
            message: '请选择电抗器结构',
            trigger: 'change'
          }"
        >
          <el-select
            v-model="formData.reactorStructure"
            class="w-full"
            placeholder="请选择电抗器结构"
            clearable
            filterable
          >
            <el-option v-for="(val, key) in rStructureList" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="formData.equipmentCode == EquipmentTypeEnumExt.Transformer" :span="12">
        <el-form-item
          label="变压器结构"
          prop="transformerStructure"
          :rules="{
            required: formData.equipmentName == EquipmentNameEnum.Transformer,
            message: '请选择变压器结构',
            trigger: 'change'
          }"
        >
          <el-select
            v-model="formData.transformerStructure"
            class="w-full"
            placeholder="请选择变压器结构"
            clearable
            filterable
          >
            <el-option v-for="(val, key) in tStructureList" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { FormInstance } from "element-plus";
import {
  VoltageListEnum,
  EquipmentNameEnumOptions,
  EquipmentNameEnum,
  RStructureEnum,
  TStructureEnum,
  EquipmentTypeEnumExt,
  EquipmentTypeEnumToEIPSubClassCodeMap
} from "@/enums";
import { EquipmentJSONModel, EProjectModel, IWorkOrder } from "@/models";
import WorkOrderTableSelect from "@/views/components/work-order-table-select/work-order-table-select.vue";

const props = withDefaults(
  defineProps<{
    detail: EProjectModel; // 表格表单数据
    equipmentList: EquipmentJSONModel; // 物资种类
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as EProjectModel;
    },
    equipmentList: () => {
      return {};
    },
    isEdit: false
  }
);
const formData = reactive({} as EProjectModel);
const equipmentAll = reactive({} as EquipmentJSONModel);
const editMode = ref(false);
const voltageList = VoltageListEnum;
const rStructureList = RStructureEnum;
const tStructureList = TStructureEnum;

watch(
  () => props.detail,
  newVal => {
    Object.assign(formData, newVal);
  },
  { immediate: true }
);

watch(
  () => props.equipmentList,
  newVal => {
    Object.assign(equipmentAll, newVal);
  },
  { immediate: true }
);

watch(
  () => props.isEdit,
  newVal => {
    editMode.value = newVal;
  },
  { immediate: true }
);

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as EProjectModel);
}

// 保存选择的工单
const workOrderChange = (workOrder: IWorkOrder) => {
  const { id, woNo, entityId } = workOrder;
  debugger;
  Object.assign(formData, {
    refWoNo: woNo,
    refWoId: id
  });
  if (entityId) {
    formData.utcNum = entityId;
  }
};

defineExpose({
  validateForm,
  getFormValue
});
</script>

<style scoped></style>
