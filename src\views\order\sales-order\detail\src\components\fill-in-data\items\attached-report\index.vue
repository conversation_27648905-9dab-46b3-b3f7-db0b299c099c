<template>
  <el-collapse-item :name="name">
    <template #title>
      <CollapseItemTitle title="工序文档" :name="name" />
    </template>
    <div class="m-5 flex-1">
      <AttachedReport />
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import AttachedReport from "../../attached-report/attached-report.vue";
import { FillInDataOfCollapseNameEnum } from "../../fill-in-data/types";

const name = FillInDataOfCollapseNameEnum.ATTACHED_REPORT;
</script>
