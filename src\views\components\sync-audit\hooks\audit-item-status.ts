import { inject } from "vue";
import { syncAuditStateKey } from "../tokens";

export function useAuditItemStatus() {
  const ctx = inject(syncAuditStateKey);

  /**
   * 更新确认项状态(是否有缺失项)
   * @param id item id
   * @param status true: invalid, false: valid
   */
  function updateItemStatus(id: string, status: boolean) {
    const target = ctx.navigationItems.find(item => item.id === id);
    if (target) {
      target.hasError = status;
    }
  }

  return {
    updateItemStatus
  };
}
