<template>
  <el-select
    v-if="loaded"
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <!-- 数据未加载完成时显示空白选择器 -->
  <el-select
    v-else
    class="w-full"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  />
</template>

<script setup lang="ts">
/**
 * 原材料、组部件 工序选择器
 * 注意区别之前的工序选择器，之前的工序选择器是过程检的工序选择器
 */
import { ref, watch, withDefaults, computed } from "vue";
import { useVModels } from "@vueuse/core";
import { getRawMaterialProcessList } from "@/api/process";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";

interface IOption {
  label: string;
  value: string;
  code: string;
}

const props = withDefaults(
  defineProps<{
    /** 物资种类 */
    subClassCode: string;
    /** 已选中id列表 */
    modelValue: string;
    /** 已选中的processCode */
    processCode?: string;
  }>(),
  {
    processCode: ""
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue, processCode } = useVModels(props, emits);

const options = ref<Array<IOption>>([]);
const loading = ref(false);
const loaded = ref(false);

const placeholder = computed(() => {
  return "请选择原材料类型";
});

/**
 * @description: 根据物资种类查询工序
 */
const requestDefaultProcessBySubclassCode = useLoadingFn(async () => {
  const { data } = await getRawMaterialProcessList(props.subClassCode);
  if (!data) {
    return;
  }
  options.value = data.map(({ id, processName, processCode }) => ({
    label: processName,
    value: id,
    code: processCode
  }));
  loaded.value = true;
}, loading);

function getSelectedProcessName() {
  const selectedItem = options.value.find(({ value }) => value === modelValue.value);
  if (selectedItem) {
    return selectedItem.label;
  }
  return "";
}

watch(
  () => props.subClassCode,
  code => {
    if (code) {
      requestDefaultProcessBySubclassCode();
    }
  },
  {
    immediate: true
  }
);

watch(modelValue, id => {
  const activeItem = options.value.find(({ value }) => value === id);
  if (activeItem) {
    processCode.value = activeItem.code;
  }
});

defineExpose({
  getSelectedProcessName
});
</script>

<style scoped></style>
