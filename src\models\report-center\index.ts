import { IPagingReq, ISortReq } from "../i-paging-req";

export interface PurchaseReportStatistics {
  /** 应完成订单数量 */
  shouldFinished: string;
  /** 未绑定销售订单数量 */
  unbound: string;
  /** 本月新增订单数量 */
  monthlyNew: string;
  /** 已绑定销售订单数量 */
  binded: string;
  /** 已完成订单数量 */
  finished: string;
  /** 已归档订单数量 */
  archived: string;
}

/**
 * @description: 采购订单列表请求入参
 */
export interface PurchaseReportListParams extends IPagingReq, ISortReq {
  /** 合同签订时间 */
  sellerSignTime?: string;
  /** 报表类型 */
  reportType?: number;
  /** 物资种类 */
  subClassCode?: string;
  /** 或条件 */
  orFilters?: Array<Recordable<string>>;
}

export interface PurchaseReportListItem {
  /** 采购订单ID */
  id: number;
  /** 采购订单号 */
  poNo: string;
  /** 合同编号 */
  conCode: string;
  /** 合同名称 */
  conName: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 采购方名称 */
  buyerName: string;
  /** 项目名称 */
  prjName: string;
  /** 合同签订日期 */
  sellerSignTime: Date;
  /** 合同计划交货日期 */
  dlvTime: Date;
  /** 关联销售订单号，多个用逗号分隔 */
  soNos: string;
  /** 同步结果 */
  synType: boolean;
  /** 触发同步状态 */
  triggerType: boolean;
}

/**
 * @description: 登录报表列表请求入参
 */
export interface LoginReportListParams extends IPagingReq, ISortReq {
  startTime?: string;
  endTime?: string;
}

/**
 * @description: 登录报表列表项
 */
export interface LoginReportListItem {
  /** 用户姓名 */
  userName: string;
  /** 用户名 */
  nickName: string;
  /** 最近一次登录时间 */
  latestLoginTime: string;
  /** 最近一次登录IP */
  latestLoginIP: string;
  /** 最近一次登录设备 */
  latestLoginDevice: string;
  /** 累计登录次数 */
  loginCount: number;
  /** 当月登录次数 */
  monthlyLoginCount: number;
}

/**
 * @description: 采购订单报表
 */
export interface ExportRecordListItem {
  /** 报表文件ID */
  reportId: number;
  /** 报表文件名称 */
  fileName: string;
  /** 报表类型 */
  reportType: number;
  /** 姓名 */
  userName: string;
  /** 昵称 */
  nickName: string;
  /** 导出时间 */
  exportTime: string;
  /** 已处理数量 */
  processed: number;
  /** 总数量 */
  total: number;
  /** 报表状态 */
  reportStatus: number;
}
