<template>
  <div class="px-6 pb-2 bg-bg_color">
    <!-- <el-divider /> -->
    <Header />
  </div>
  <div class="flex-1 mx-6 overflow-hidden">
    <div class="py-5 flex gap-5 h-full">
      <Step />
      <Component :is="component" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import Header from "./src/header/header.vue";
import Step from "./src/step/step.vue";
import LinkSalesOrder from "./src/link-sales-order/link-seles-order.vue";
import ProductionSchedule from "./src/production-schedule/production-schedule.vue";
import ProductionOrder from "./src/production-order/production-order.vue";
import ProductionData from "./src/production-data/production-data.vue";
import SGOrder from "./src/sg-order/sg-order.vue";
import SGRate from "./src/sg-rate/sg-rate.vue";
import { usePurchaseOrderDetailStore, usePurchaseOrderStore } from "@/store/modules";
import { computed, h, onMounted, onUnmounted, watchEffect } from "vue";
import { ProcessStepEnum } from "@/enums";
import { useRoute } from "vue-router";
import { StarFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { IPurchaseOrderDetail } from "@/models";

const pageStore = usePageStoreHook();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const purchaseOrderStore = usePurchaseOrderStore();

watchEffect(() => {
  const pageTitle = `采购订单 ${purchaseOrderDetailStore.purchaseOrder?.poNo ?? ""}`;
  pageStore.setTitle(pageTitle);

  pageStore.setComponent(
    h(StarFilled, {
      width: "20",
      color: purchaseOrderDetailStore.purchaseOrder?.follow ? "#E6A23C" : "#DEDFE0",
      cursor: "pointer",
      onclick: () => onToggleFollow(purchaseOrderDetailStore.purchaseOrder)
    })
  );
});

onMounted(() => {
  const { params } = useRoute();
  purchaseOrderDetailStore.setPurchaseOrderId(params.id as string);
  purchaseOrderDetailStore.refreshPurchaseOrder();
  purchaseOrderDetailStore.refreshStepStatus();
});

onUnmounted(() => {
  pageStore.setComponent();
  purchaseOrderDetailStore.$reset();
});

const component = computed(() => getComponent(purchaseOrderDetailStore.activeStepKey));

function getComponent(key: ProcessStepEnum) {
  switch (key) {
    case ProcessStepEnum.SALES_ORDER:
      return LinkSalesOrder;
    case ProcessStepEnum.PRODUCTION_SCHEDULE:
      return ProductionSchedule;
    case ProcessStepEnum.PRODUCTION_ORDER:
      return ProductionOrder;
    case ProcessStepEnum.PRODUCTION_DATA:
      return ProductionData;
    case ProcessStepEnum.SG_ORDER:
      return SGOrder;
    case ProcessStepEnum.SG_RATE:
      return SGRate;
    default: {
      const _exhaustiveCheck = key;
      return null;
    }
  }
}

async function onToggleFollow(data: IPurchaseOrderDetail) {
  if (data.follow) {
    if (!(await useConfirm("是否确认取消关注该采购订单", "取消关注"))) {
      return;
    }
  }

  await purchaseOrderStore.purchaseOrderFollow(data.id);
  ElMessage.success(data.follow ? "取消关注订单成功" : "关注订单成功");
  data.follow = !data.follow;
}
</script>

<style lang="scss" scoped>
.el-divider {
  @apply my-1;
}
</style>
