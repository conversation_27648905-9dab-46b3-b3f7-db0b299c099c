export interface IStateGridOrderSyncHistory {
  id: string;
  purchaseId: string;
  purchaseLineId: string;
  dataId: string;
  taskItemId: string;
  logStep: string;
  logStepName: string;
  logTime: string;
  syncProcess: string;
  /** 是否发生错误 */
  isFault: boolean;
  /** 错误原因 */
  reason: string;
}

export interface IStateGridOrderJFNYSyncHistory {
  id: string;

  purchaseId: string;

  purchaseLineId: string;

  dataId: string;

  dataDetailItem: IStateGridOrderJFNYSyncHistoryDataDetailItem[];

  logStep: string;

  logStepName: string;

  logTime: string;

  syncProcess: string;

  reason: string;

  isFault: boolean;
}

export interface IStateGridOrderJFNYSyncHistoryDataDetailItem {
  taskId: string;

  taskItemId: string;
  // 试验分类
  experimentCategory: string;
  // 试验分类名称
  categoryName: string;
}
