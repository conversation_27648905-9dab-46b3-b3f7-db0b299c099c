<template>
  <BaseList :columns="columns" type="sales" v-bind="$attrs" ref="baseList" @dataChange="dataChange" />
  <EditSalesOrderLineDialog ref="editDialog" />
  <DetailSalesOrderLineDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { provide, ref } from "vue";
import { OperatorCell } from "@/components/TableCells";
import { StateGridOrderSyncResult, TableWidth, VoltageClassesEnumMapDesc } from "@/enums";
import { ISalesOrderSync } from "@/models";
import { formatDecimal } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditSalesOrderLineDialog from "@/views/components/state-grid-order-sync/dialogs/edit-sales-order-line-dialog.vue";
import DetailSalesOrderLineDialog from "@/views/components/state-grid-order-sync/dialogs/detail-sales-order-line-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import BaseList from "../base-list.vue";

const store = useStateGridSyncAuditStore();
const { enumFormatter, mapFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditSalesOrderLineDialog>,
  InstanceType<typeof DetailSalesOrderLineDialog>,
  ISalesOrderSync
>();

provide(stateGridOrderSyncEditKey, {
  refreshFn: pageInfo => baseList.value?.refresh(pageInfo)
});

const columns: TableColumnList = [
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    width: TableWidth.suborder,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "销售订单号",
    prop: "soNo",
    width: TableWidth.order
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.largeName
  },
  {
    label: "物料数量",
    prop: "materialNumber",
    minWidth: TableWidth.number,
    formatter: row => {
      const { materialNumber, materialUnitName } = row;
      return materialNumber ? `${formatDecimal(materialNumber)} ${materialUnitName}` : null;
    }
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    width: TableWidth.type,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    width: TableWidth.operation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        }
      ])
  }
];

function dataChange(data: Array<ISalesOrderSync>): void {
  store.setEmpty(!data.length);
}
</script>

<style scoped></style>
