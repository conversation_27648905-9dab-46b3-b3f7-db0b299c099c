<template>
  <el-collapse-item :name="name">
    <template #title>
      <CollapseItemTitle title="生产试验感知" :name="name" v-track="TrackPointKey.FORM_PURCHASE_PD_UPDOWN_3" />
    </template>
    <div class="m-5 flex-1">
      <ProductionTestSensing />
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import ProductionTestSensing from "../../production-test-sensing/index.vue";
import { TrackPointKey } from "@/consts";
import { FillInDataOfCollapseNameEnum } from "../../fill-in-data/types";

const name = FillInDataOfCollapseNameEnum.PRODUCTION_TEST_SENSING;
</script>
