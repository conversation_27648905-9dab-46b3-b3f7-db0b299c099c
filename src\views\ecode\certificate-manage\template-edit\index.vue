<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex justify-between items-center bg-white p-5 pt-0">
      <el-descriptions :column="4" class="flex-1">
        <el-descriptions-item label="模板名称">{{ templateDetail?.templateName }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ templateDetail?.createName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ templateDetail?.createTime }}</el-descriptions-item>
        <el-descriptions-item label="修改时间">{{ templateDetail?.updateTime }}</el-descriptions-item>
      </el-descriptions>

      <TemplateEditForm
        class="ml-0"
        :id="templateDetail?.id"
        :template-name="templateDetail?.templateName"
        :size="templateDetail?.size"
        :location="templateDetail?.location"
        @post-save-success="onQueryLatestTemplate(templateDetail?.id)"
      >
        <template #trigger="{ openDialog }">
          <el-button type="primary" :icon="UploadFilled" @click="openDialog()">重新上传</el-button>
        </template>
      </TemplateEditForm>
    </div>
    <div class="mx-6 my-5 bg-white flex-1 flex flex-col overflow-hidden">
      <ExcelEditor ref="excelEditorRef" class="flex-1" :blob="templateBlob" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { UploadFilled } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { getCertificateDetail } from "../../api/certificate-manage";
import { usePageStore } from "@/store/modules/page";
import { ICertificate } from "../../models";
import TemplateEditForm from "./template-edit-form.vue";
import { downLoadFile } from "../../api/upload-file";
import ExcelEditor from "../../components/excel-editor/editor.vue";
import { ElMessage } from "element-plus";

usePageStore().setTitle("合格证编辑");

const route = useRoute();
const templateBlob = ref<Blob>();
const templateDetail = ref<ICertificate>();
const excelEditorRef = ref<InstanceType<typeof ExcelEditor>>();

onMounted(async () => {
  await onQueryLatestTemplate(route.params.id as string);
  getFileBlob(templateDetail.value?.fileId);
});

const onQueryLatestTemplate = async (id: string) => {
  const { data } = await getCertificateDetail(id);
  templateDetail.value = data;
  await getFileBlob(templateDetail.value.fileId);
};

const getFileBlob = async (filedId: string) => {
  const blob: Blob = await downLoadFile(filedId);
  if (!blob) {
    ElMessage({ message: "查询模板文件异常", type: "error" });
    return;
  }
  templateBlob.value = blob;
};
</script>

<style scoped lang="scss">
:deep(.el-descriptions) {
  .el-descriptions__body {
    .el-descriptions__table {
      .el-descriptions__cell {
        display: contents;
      }
    }
  }
}
</style>
