<template>
  <el-dialog
    :title="chartData ? chartData.collectName : '--'"
    align-center
    class="large"
    destroy-on-close
    width="90%"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="resetData"
  >
    <div class="flex">
      <div class="w-[22%]" v-loading="tableLoading">
        <div class="mb-2 flex justify-between">
          <div>原始值列表</div>
          <el-button size="small" type="primary" @click="onQueryHistoryAutoCollectCharts()">刷新</el-button>
        </div>
        <el-table-v2
          :data="deviceHistoryDataAcquisition"
          :columns="columns"
          v-model:sort-state="sortState"
          :width="340"
          :height="650"
          @column-sort="onSort"
        />
      </div>
      <div class="w-[78%]">
        <div class="h-[30vh]">
          <nomal-distribution
            :device-id="deviceId"
            :start-time="formatDate(workStartTime, fullDateFormat)"
            :end-time="formatDate(workEndTime, fullDateFormat) || formatDate(Date.now(), fullDateFormat)"
            :point-name="collectPoint"
            :part-count="11"
          />
        </div>
        <div class="h-[45vh] flex" v-loading="loading">
          <auto-collect-chart :data="chartData" data-zoom />
          <div class="w-32">
            <el-tag>历史数据</el-tag>
            <el-radio-group v-model="aggModel" class="mt-4">
              <el-radio :label="AggModelEnum.rawValue" size="small">原始值</el-radio>
              <el-radio :label="AggModelEnum.oneMinute" size="small">1分钟</el-radio>
              <el-radio :label="AggModelEnum.fiveMinute" size="small">5分钟</el-radio>
              <el-radio :label="AggModelEnum.tenMinute" size="small">10分钟</el-radio>
              <el-radio :label="AggModelEnum.fifteenMinute" size="small">15分钟</el-radio>
              <el-radio :label="AggModelEnum.thirtyMinute" size="small">30分钟</el-radio>
              <el-radio :label="AggModelEnum.sxityMinute" size="small">1小时</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import NomalDistribution from "./normal-distribution.vue";
import AutoCollectChart from "@/views/components/auto-collection-charts/auto-collection-charts.vue";
import { getHistoryAutoCollectCharts } from "@/api/production-test-sensing/production-process-inspection";
import { IAutoCollectChartReq, IItem } from "@/models/production-test-sensing/i-production-process-inspection";
import { IAutoCollectChart, handleAutoCollectCharts } from "@/utils/auto-collect-points";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getPointByDeviceIdProcessId } from "@/api/device/acquisition-maintain";
import { AggModelEnum } from "@/enums/device/device.enum";
import { TableV2SortOrder } from "element-plus";
import type { SortBy, SortState } from "element-plus";
import { cloneDeep } from "@pureadmin/utils";

const columns: { [key: string]: any } = [
  {
    title: "时间",
    key: "time",
    dataKey: "time",
    sortable: true,
    width: 180
  },
  {
    title: "码点值",
    key: "point",
    align: "center",
    dataKey: "point",
    width: 140,
    sortable: true
  }
];

/**
 * 自动采集-历史数据弹窗
 */

const loading = ref(false);
const tableLoading = ref(false);
const dialogVisible = ref(false);
const deviceId = ref("");
const deviceCode = ref("");
const workStartTime = ref("");
const workEndTime = ref("");
const collectPoint = ref("");
const processId = ref("");
const sortState = ref<SortState>({
  time: TableV2SortOrder.DESC,
  point: TableV2SortOrder.DESC
});
const deviceHistoryDataAcquisition = ref([]);
const aggModel = ref<AggModelEnum>(AggModelEnum.rawValue);

const chartData = ref<IAutoCollectChart>({
  deviceName: "",
  collectPoint: "",
  collectName: "",
  values: []
});

/**
 * @description: 获取码点
 */
const getPoint = async () => {
  const params = {
    deviceId: deviceId.value,
    processId: processId.value
  };
  const { data: points } = await getPointByDeviceIdProcessId(params);
  const p = points.find(point => point.pointNo === collectPoint.value);
  return p;
};

/**
 * @description: 查询echarts数据
 */
const requestEchartsData = useLoadingFn(async () => {
  if (!deviceId.value) {
    return;
  }
  const params: IAutoCollectChartReq = {
    deviceCode: deviceCode.value,
    deviceId: deviceId.value,
    processId: processId.value,
    timeFrom: formatDate(workStartTime.value, fullDateFormat),
    timeEnd: formatDate(workEndTime.value, fullDateFormat) || formatDate(Date.now(), fullDateFormat),
    points: [collectPoint.value],
    aggModel: aggModel.value,
    func: "MEAN"
  };
  const { data } = await getHistoryAutoCollectCharts(params);
  if (!Array.isArray(deviceHistoryDataAcquisition.value) || deviceHistoryDataAcquisition.value.length === 0) {
    deviceHistoryDataAcquisition.value = cloneDeep(data.items)?.map(getPointOriginValue)?.reverse();
  }

  const point = await getPoint();
  const tempList = handleAutoCollectCharts(data.items, data.points);
  if (tempList.length) {
    chartData.value = {
      ...tempList[0],
      collectName: point.pointName
    };
  }
}, loading);

/**
 * @description: 查询原始值数据
 */
const onQueryHistoryAutoCollectCharts = useLoadingFn(async () => {
  if (!deviceId.value) {
    return;
  }
  const params: IAutoCollectChartReq = {
    deviceCode: deviceCode.value,
    deviceId: deviceId.value,
    processId: processId.value,
    timeFrom: formatDate(workStartTime.value, fullDateFormat),
    timeEnd: formatDate(workEndTime.value, fullDateFormat) || formatDate(Date.now(), fullDateFormat),
    points: [collectPoint.value],
    aggModel: AggModelEnum.rawValue,
    func: "MEAN"
  };
  const { data } = await getHistoryAutoCollectCharts(params);
  deviceHistoryDataAcquisition.value = data.items?.map(getPointOriginValue)?.reverse();
}, tableLoading);

/**
 * @description: 打开自动采集弹窗
 * @param {*} dId 设备id
 * @param {*} dCode 设备code
 * @param {*} colPoint 采集点
 * @param {*} startTime 报工开始时间
 * @param {*} endTime 报工结束时间
 * @param {*} colPoint 码点
 */
function openAccquisitionDialog(
  dId: string,
  dCode: string,
  pId: string,
  colPoint: string,
  startTime: string,
  endTime: string
) {
  deviceId.value = dId;
  deviceCode.value = dCode;
  processId.value = pId;
  workStartTime.value = startTime;
  workEndTime.value = endTime;
  collectPoint.value = colPoint;
  dialogVisible.value = true;
  requestEchartsData();
}

function resetData() {
  deviceId.value = "";
  deviceCode.value = "";
  workStartTime.value = "";
  workEndTime.value = "";
  collectPoint.value = "";
  processId.value = "";
  aggModel.value = AggModelEnum.rawValue;
  chartData.value = {
    deviceName: "",
    collectPoint: "",
    collectName: "",
    values: []
  };
}

const getPointOriginValue = (data: IItem) => {
  return {
    time: formatDate(data.time, fullDateFormat),
    point: getPointValue(data)
  };
};

const getPointValue = (item: IItem) => {
  if (!item?.points || Object.keys(item?.points).length === 0) {
    return;
  }
  return item.points[Object.keys(item.points)[0]];
};

const onSort = ({ key, order }: SortBy) => {
  sortState.value[key] = order;
  deviceHistoryDataAcquisition.value = deviceHistoryDataAcquisition.value.reverse();
};

watch(aggModel, requestEchartsData);

defineExpose({
  openAccquisitionDialog
});
</script>

<style scoped></style>
