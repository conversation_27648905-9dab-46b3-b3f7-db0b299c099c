<template>
  <div class="status">
    <span class="title">制定排产计划</span>
  </div>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    :size="size"
    placeholder="请输入排产计划编码/销售订单号/销售订单行项目"
    @search="onSearch()"
  >
    <el-button
      v-auth="PermissionKey.form.formPurchasePlanCreate"
      v-track="TrackPointKey.FORM_PURCHASE_PLAN_CREATE"
      @click="onAddSchedulingPlanModalVis()"
      type="primary"
      :icon="Plus"
      >新增排产计划</el-button
    >
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm";
import { IKeywordField, ISearchItem, SizeType } from "@/components/SearchForm";
import { ElDatePicker, ElInput } from "element-plus";
import { reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey, TrackPointKey } from "@/consts";
import { ISchedulingPlanQuery } from "@/models";

const size = SizeType.DEFAULT;

const form = reactive({
  materialName: undefined,
  plantStartTime: undefined,
  plantEndTime: undefined,
  data: undefined
});

const items: Array<ISearchItem> = [
  {
    key: "materialName",
    label: "物料名称：",
    component: ElInput,
    componentProps: {
      placeholder: "请输入物料名称"
    }
  },
  {
    key: "plantStartTime",
    label: "计划开始日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划开始日期"
    }
  },
  {
    key: "plantEndTime",
    label: "计划结束日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划结束日期"
    }
  }
];

const emits = defineEmits<{
  (event: "operateChange"): void;
  (event: "searchForm", value: ISchedulingPlanQuery): void;
}>();

const keywordFields: Array<IKeywordField> = [
  { key: "ppNo", title: "排产计划编码" },
  { key: "soNo", title: "销售订单号" },
  { key: "soItemNo", title: "销售订单行项目" }
];

const onSearch = () => {
  emits("searchForm", { ...form, materialName: form.materialName || null });
};

/** 操作事件 */
const onAddSchedulingPlanModalVis = () => {
  emits("operateChange");
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;

  .title {
    color: var(--el-text-color-primary);
    font-size: 1.125rem;
    font-weight: normal;
    line-height: 1.625rem;
    letter-spacing: 0;
    padding-bottom: 0.75rem;
  }
}
</style>
