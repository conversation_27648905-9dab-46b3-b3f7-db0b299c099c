import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IResponse } from "@/models";
import { IPerceptionProcessStatus } from "@/models/production-test-sensing/i-common";

/**
 * 获取生产试验感知步骤的数据状态
 */
export function getProductionProcessStatus(productionId: string) {
  const url = withApiGateway(`admin-api/business/production/perception/process/status/${productionId}`);
  return http.get<void, IResponse<IPerceptionProcessStatus>>(url);
}

/**
 * 获取非线缆生产试验感知步骤的数据状态
 */
export function getNonCableProductionProcessStatus(workOrderId: string) {
  const url = withApiGateway(`admin-api/business/production/perception/process/status/non-cable/${workOrderId} `);
  return http.get<void, IResponse<IPerceptionProcessStatus>>(url);
}

/**
 * 删除文件
 */
export function delUploadFile(id: string) {
  const url = withApiGateway(`admin-api/infra/file/delete?id=${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}
