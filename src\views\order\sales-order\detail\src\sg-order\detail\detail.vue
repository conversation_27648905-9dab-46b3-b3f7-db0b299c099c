<!-- 国网平台-同步销售订单 -->
<template>
  <div class="bg-bg_color px-6 py-3 flex flex-col h-full">
    <Steps />
    <Component :is="component" class="flex-1" />
    <SyncTimelineDialog :syncTabType="store.activeDetailType" :stepKey="listStore.type" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useSalesStateGridOrderSyncDetailListStore, useSalesOrderSyncInfo } from "@/store/modules";
import Steps from "./steps.vue";
import { RefreshSceneEnum, SocketEventEnum, StateGridOrderSyncStep } from "@/enums";
import SalesOrderLine from "./lists/sales-order-line.vue";
import ProductionPlan from "./lists/production-plan.vue";
import Production from "./lists/production.vue";
import WorkOrder from "./lists/work-order.vue";
import WorkReport from "./lists/work-report.vue";
import MaterialCompQuality from "./lists/material-comp-quality.vue";
import TechProcessQuality from "./lists/tech-process-quality.vue";
import TechProcessAutoCollect from "./lists/tech-process-auto-collect.vue";
import OutgoingQuality from "./lists/outgoing-quality.vue";
import ProductionInfo from "./lists/production-info.vue";
import TechnicalStandard from "./lists/technical-standard.vue";
import ProcessDocument from "./lists/process-document.vue";
import SyncTimelineDialog from "@/views/components/state-grid-order-sync/dialogs/sync-timeline-dialog.vue";
import { useThrottleFn } from "@vueuse/core";
import { useSocket } from "@/utils/useSocket";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";

const store = useSalesOrderSyncInfo();
const listStore = useSalesStateGridOrderSyncDetailListStore();
const socket = useSocket();

const refreshStatus = useThrottleFn(store.refreshSyncDetail, REFRESH_SYNC_DATA_PERIOD, true);
const refreshList = useThrottleFn(listStore.refreshSyncListSilence, REFRESH_SYNC_DATA_PERIOD, true);

const component = computed(() => getComponent(store.activeStepKey));

onMounted(() => {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, dataType, purchaseOrderItemId } = event;
    if (
      type !== RefreshSceneEnum.SYNC_DATA &&
      type !== RefreshSceneEnum.IOT_SYNC_DATA &&
      type !== RefreshSceneEnum.CSG_GUANGZHOU_DATA
    ) {
      return;
    }
    if (purchaseOrderItemId !== store.sync?.id) return;
    refreshStatus();
    if (listStore.type !== dataType) return;
    refreshList();
  });
});

function getComponent(key: StateGridOrderSyncStep) {
  switch (key) {
    case StateGridOrderSyncStep.SALES_LINE:
      return SalesOrderLine;
    case StateGridOrderSyncStep.PRODUCTION_PLAN:
      return ProductionPlan;
    case StateGridOrderSyncStep.PRODUCTION:
      return Production;
    case StateGridOrderSyncStep.WORK_ORDER:
      return WorkOrder;
    case StateGridOrderSyncStep.WORK_REPORT:
      return WorkReport;
    case StateGridOrderSyncStep.MATERIAL_COMP_QUALITY:
      return MaterialCompQuality;
    case StateGridOrderSyncStep.TECH_PROCESS_QUALITY:
      return TechProcessQuality;
    case StateGridOrderSyncStep.PROCESS_QUALITY_AUTO_COLLECT:
      return TechProcessAutoCollect;
    case StateGridOrderSyncStep.OUTGOING_QUALITY:
      return OutgoingQuality;
    case StateGridOrderSyncStep.PRODUCTION_INFO:
      return ProductionInfo;
    case StateGridOrderSyncStep.TECHNICAL_STANDARD:
      return TechnicalStandard;
    case StateGridOrderSyncStep.ATTACHED_REPORT:
      return ProcessDocument;
  }
}
</script>
