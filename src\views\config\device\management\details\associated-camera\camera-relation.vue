<template>
  <div class="production-select">
    <div class="flex items-center mb-5">
      <el-input
        class="!w-96 mr-2"
        placeholder="请输入摄像头编号/名称"
        v-model="params.searchTerm"
        clearable
        @clear="onQueryCamera"
        @keydown.enter="onQueryCamera"
      />
      <el-button class="ml-2" type="primary" @click="onQueryCamera">查询</el-button>
    </div>
    <pure-table
      show-overflow-tooltip
      row-key="id"
      :height="460"
      :data="associatedCameraStore.cameraRelationList"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @selection-change="selectiCameraChange"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </pure-table>
  </div>
</template>
<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { reactive, ref, onMounted, watchEffect, h } from "vue";
import { TableWidth, CameraStatusEnum } from "@/enums";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useRoute } from "vue-router";
import { useAssociatedCameraStore } from "@/store/modules/device";
import { IAssociatedCamera } from "@/models/device";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

const route = useRoute();
const emits = defineEmits<{
  (e: "onSelectCameraIds", value: Array<string>): void;
}>();

const deviceId: string = route.params.id as string;
const associatedCameraStore = useAssociatedCameraStore();
const { pagination } = useTableConfig();
const loading = ref(false);

const onQueryCamera = useLoadingFn(cameraSearch, loading);

const columns: TableColumnList = [
  {
    width: TableWidth.check,
    type: "selection"
  },
  {
    label: "摄像头编号",
    prop: "no",
    minWidth: TableWidth.order
  },
  {
    label: "摄像头名称",
    prop: "name",
    minWidth: TableWidth.name
  },
  {
    label: "NVR IP地址",
    prop: "nvrIp",
    minWidth: TableWidth.name
  },
  {
    label: "状态",
    prop: "status",
    minWidth: TableWidth.unit,
    cellRenderer(data: TableColumnRenderer) {
      const status: CameraStatusEnum = data.row.status;
      if (!status) {
        return null;
      }
      return status === CameraStatusEnum.DISABLE
        ? h(CxTag, { type: "danger" }, "关闭")
        : h(CxTag, { type: "success" }, "开启");
    }
  }
];

const params = reactive<{
  searchTerm: string;
}>({
  searchTerm: undefined
});

onMounted(() => {
  onQueryCamera();
});

watchEffect(() => {
  pagination.total = associatedCameraStore.total;
});

/**
 * 查询数据
 */
async function cameraSearch() {
  pagination.currentPage = 1;
  associatedCameraStore.queryCamera(queryParams());
}

// 选择数据
const selectiCameraChange = (lines: Array<IAssociatedCamera>) => {
  emits(
    "onSelectCameraIds",
    lines.map(item => item.id)
  );
};

/**
 * 根据页码改变查询列表
 */
const onCurrentPageChange = () => {
  associatedCameraStore.queryCamera(queryParams());
};
/**
 * 根据页码数量改变查询列表
 */
const onPageSizeChange = () => {
  associatedCameraStore.queryCamera(queryParams());
};

function queryParams() {
  return {
    deviceId,
    searchTerm: params.searchTerm,
    relationFlag: false,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
}
</script>

<style scoped lang="scss"></style>
