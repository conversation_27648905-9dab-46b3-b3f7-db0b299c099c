<template>
  <div class="message-info overflow-hidden min-h-[300px] relative" v-loading="loading">
    <!-- 默认数据报文 -->
    <el-descriptions v-if="displayMode === 'default'" direction="horizontal" :column="3">
      <el-descriptions-item label="请求时间" :span="3">
        {{ formatDate(message?.requestTime, fullDateFormat) }}
      </el-descriptions-item>

      <el-descriptions-item label="请求头" :span="3" class-name="message">
        <template #default>
          <el-scrollbar v-if="message?.requestHeader">
            <p class="whitespace-normal">
              {{ message?.requestHeader }}
            </p>
          </el-scrollbar>
        </template>
      </el-descriptions-item>

      <el-descriptions-item label="请求内容" :span="3" class-name="message">
        <template #default>
          <el-scrollbar v-if="message?.requestContent">
            <p class="whitespace-normal">
              {{ message?.requestContent }}
            </p>
          </el-scrollbar>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="响应时间" :span="3">
        {{ formatDate(message?.responseTime, fullDateFormat) }}
      </el-descriptions-item>
      <el-descriptions-item label="响应内容" :span="3" class-name="message">
        <el-scrollbar v-if="message?.responseContent">
          <template #default>
            <el-scrollbar v-if="message?.requestContent">
              <p class="whitespace-normal">
                {{ message?.responseContent }}
              </p>
            </el-scrollbar>
          </template>
        </el-scrollbar>
      </el-descriptions-item>
    </el-descriptions>
    <!-- EIP业务数据报文  -->
    <div v-if="displayMode === 'eip-business'">
      <template v-for="(item, index) in eipBussinessList" :key="index">
        <el-descriptions direction="horizontal" :column="3">
          <el-descriptions-item label="请求时间" :span="3">
            {{ formatDate(item.requestTime, fullDateFormat) }}
          </el-descriptions-item>

          <el-descriptions-item label="请求头" :span="3" class-name="message">
            <template #default>
              <el-scrollbar v-if="item.requestHeader">
                <p class="whitespace-normal">
                  {{ item.requestHeader }}
                </p>
              </el-scrollbar>
            </template>
          </el-descriptions-item>

          <el-descriptions-item label="请求内容" :span="3" class-name="message">
            <template #default>
              <el-scrollbar v-if="item.requestContent">
                <p class="whitespace-normal">
                  {{ item.requestContent }}
                </p>
              </el-scrollbar>
            </template>
          </el-descriptions-item>
          <el-descriptions-item label="响应时间" :span="3">
            {{ formatDate(item.responseTime, fullDateFormat) }}
          </el-descriptions-item>
          <el-descriptions-item label="响应内容" :span="3" class-name="message">
            <el-scrollbar v-if="item.responseContent">
              <template #default>
                <el-scrollbar v-if="item?.requestContent">
                  <p class="whitespace-normal">
                    {{ item?.responseContent }}
                  </p>
                </el-scrollbar>
              </template>
            </el-scrollbar>
          </el-descriptions-item>
        </el-descriptions>
        <el-divider direction="horizontal" />
      </template>
    </div>
    <!-- EIP物联数据报文 -->
    <el-descriptions v-if="displayMode === 'eip-wulian'" direction="horizontal" :column="3">
      <el-descriptions-item label="数据处理时间" :span="3">
        {{ formatDate(message?.recordTime, fullDateFormat) }}
      </el-descriptions-item>

      <el-descriptions-item label="报文信息" :span="3" class-name="message">
        <template #default>
          <el-scrollbar v-if="message?.dataContent">
            <p class="whitespace-normal">
              {{ message?.dataContent }}
            </p>
          </el-scrollbar>
        </template>
      </el-descriptions-item>
    </el-descriptions>

    <el-tag type="warning" v-if="showTip" class="absolute bottom-0 left-2">
      注： 历史数据，不支持报文信息查看和下载
    </el-tag>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from "vue";
import { useStateGridOrderSyncTimelineStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IStateGridOrderSyncMessage } from "@/models";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { getEipMessageListByTaskId, getEipWulianMessageByTaskId } from "@/api/state-grid-order-sync";
import { isWulianStep } from "@/enums/state-grid-order/state-grid-order-sync-type.enum";

interface IMessage {
  requestTime: string;
  requestContent: string;
  responseTime: string;
  responseContent: string;
  requestHeader: string;
  recordTime: string;
  dataContent: string;
}

const props = defineProps<{
  taskId: string;
  syncTabType?: string;
  stepKey: number;
}>();

const emit = defineEmits<{
  (e: "update:messageId", id: string): void;
}>();

const store = useStateGridOrderSyncTimelineStore();
const showTip = ref(false);
const loading = ref<boolean>(false);
const message = ref<IMessage>();

const eipBussinessList = ref<Array<IMessage>>([]);

const displayMode = computed(() => {
  // eip平台路线，区分 业务数据/物联数据
  if (props.syncTabType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
    if (isWulianStep(props.stepKey)) {
      return "eip-wulian";
    }
    return "eip-business";
  }
  // 其他平台路线，走默认路线，不区分 业务数据/物联数据
  return "default";
});
const loadingEipBusinessMessageInfo = useLoadingFn(getEipMessageListByTaskId, loading);
const loadingEipWulianMessageInfo = useLoadingFn(getEipWulianMessageByTaskId, loading);
const loadingIOTMessageInfo = useLoadingFn(store.getMessageIOTByTaskId, loading);

onMounted(() => {
  watchEffect(async () => {
    const taskId = props.taskId;
    if (props.syncTabType === SyncOrderTabEnum.SYNC_SHANGHAI_IOT) {
      const data = await loadingIOTMessageInfo(taskId);
      if (!data) {
        showTip.value = true;
      }
      if (data && taskId === props.taskId) {
        message.value = parseMessage(data);
        emit("update:messageId", data.id);
      }
    } else {
      if (displayMode.value === "eip-wulian") {
        const { data } = await loadingEipWulianMessageInfo(taskId);
        if (!data) {
          showTip.value = true;
          return;
        }
        message.value = parseMessage(data);
        emit("update:messageId", data.id);
      } else {
        const { data: list } = await loadingEipBusinessMessageInfo(taskId);
        if (!list || !list.length) {
          showTip.value = true;
          eipBussinessList.value = [message.value];
        }
        if (list && list.length && taskId === props.taskId) {
          eipBussinessList.value = list.map(item => parseMessage(item));
          emit("update:messageId", props.taskId);
        }
      }
    }
  });
});

function parseMessage(message: IStateGridOrderSyncMessage) {
  const {
    requestTime = "",
    requestContent = "",
    responseTime = "",
    responseContent = "",
    requestHeader = "",
    recordTime = "",
    dataContent = ""
  } = message;
  return {
    requestTime,
    requestContent,
    responseTime,
    responseContent,
    requestHeader,
    recordTime,
    dataContent
  };
}
</script>

<style scoped lang="scss">
:deep(.message) {
  white-space: pre;
  max-height: 300px;
  overflow: auto;
}
</style>
