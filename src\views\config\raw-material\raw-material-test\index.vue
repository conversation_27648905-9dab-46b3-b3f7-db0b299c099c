<template>
  <div class="flex-1 flex flex-col overflow-hidden">
    <div class="title flex justify-between">
      <TitleBar title="检验批次" />
      <div class="add-inpection-no mr-2">
        <add-edit-copy-raw-material-test
          mode="add"
          title="新增原材料检"
          :material-id="currentRawMaterial?.id"
          @post-save-success="search"
        >
          <template #trigger="{ openDialog }">
            <el-button
              v-auth="PermissionKey.meta.metaInspectCreate"
              v-track="TrackPointKey.META_RAW_MATERIAL_EXP_CREATE"
              type="primary"
              :icon="Plus"
              :disabled="!currentRawMaterial?.id"
              @click="openDialog"
              >新增原材料检
            </el-button>
          </template>
        </add-edit-copy-raw-material-test>
      </div>
    </div>
    <div class="test-container px-3 h-full flex flex-col overflow-hidden">
      <div class="search flex flex-wrap items-center">
        <el-form ref="testFormInstance" :model="form" class="px-0 flex items-center py-2">
          <el-form-item label="检验批次号" prop="inspectNo" class="w-[320px] !mb-0 mr-8">
            <el-input v-model="form.inspectNo" placeholder="请输入检验批次号" clearable @clear="search" />
          </el-form-item>

          <el-form-item label="检验日期" prop="inspectDate" class="w-[300px] !mb-0 mr-8">
            <el-date-picker
              class="w-full"
              v-model="form.inspectDate"
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
        </el-form>
        <div class="py-2">
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset" class="mr-3">重置</el-button>
        </div>
      </div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="rawMaterialStore.rawMaterialTestList"
        :columns="columns"
        showOverflowTooltip
        :pagination="pagination"
        @page-size-change="onPageSizeChange($event)"
        @page-current-change="onCurrentPageChange($event)"
        :loading="rawMaterialStore.$state.rawMaterialTestListLoading"
      >
        <template #operate="{ row }">
          <div class="operate-btn">
            <el-button
              link
              type="primary"
              v-auth="PermissionKey.meta.metaInspectView"
              v-track="TrackPointKey.META_RAW_MATERIAL_EXP_DETAIL"
              @click="detailOfRawMaterialInspec(row?.id)"
              >详情</el-button
            >
            <add-edit-copy-raw-material-test
              mode="edit"
              title="编辑原材料检"
              :material-id="currentRawMaterial?.id"
              :id="row?.id"
              @post-save-success="search"
            >
              <template #trigger="{ openDialog }">
                <el-button
                  link
                  type="primary"
                  v-auth="PermissionKey.meta.metaInspectEdit"
                  v-track="TrackPointKey.META_RAW_MATERIAL_EXP_EDIT"
                  @click="openDialog"
                >
                  编辑
                </el-button>
              </template>
            </add-edit-copy-raw-material-test>
            <add-edit-copy-raw-material-test
              mode="copy"
              title="复制原材料检"
              :material-id="currentRawMaterial?.id"
              :id="row?.id"
              @post-save-success="search"
            >
              <template #trigger="{ openDialog }">
                <el-button
                  link
                  type="primary"
                  v-auth="PermissionKey.meta.metaInspectCreate"
                  v-track="TrackPointKey.META_RAW_MATERIAL_EXP_EDIT"
                  @click="openDialog"
                >
                  复制
                </el-button>
              </template>
            </add-edit-copy-raw-material-test>
            <el-button
              link
              type="primary"
              v-auth="PermissionKey.meta.metaInspectDelete"
              v-track="TrackPointKey.META_RAW_MATERIAL_EXP_DELETE"
              @click="delRawMaterialInspec(row?.id)"
              >删除</el-button
            >
          </div>
        </template>
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useColumns } from "./columns";
import { Plus } from "@element-plus/icons-vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { reactive, ref, watchEffect, watch, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { formatDate } from "@/utils/format";
import { dateFormat } from "@/consts";
import { ISearchRawMaterialInspecReq, IAddRawMaterialInspection } from "@/models/raw-material/i-raw-material-res-v2";
import { PermissionKey, TrackPointKey } from "@/consts";
import AddEditCopyRawMaterialTest from "@/views/config/raw-material/add-edit-copy-raw-material-test/index.vue";

const emits = defineEmits<{
  (event: "addRawMaterialInspec"): void;
  (event: "editRawMaterialInspec", editInspectInfo: IAddRawMaterialInspection): void;
  (event: "detailRawMaterialInspec", id: string): void;
  (event: "copyRawMaterialInspec", editInspectInfo: IAddRawMaterialInspection): void;
}>();

// 表头和页码信息
const { columns } = useColumns();
const { pagination } = useTableConfig();
const rawMaterialStore = useRawMaterialV2Store();
const testFormInstance = ref<FormInstance>();
const form = reactive({
  inspectDate: null,
  inspectNo: null
});
const currentRawMaterial = computed(() => rawMaterialStore.currentClickRawMaterial);

watchEffect(() => {
  pagination.total = rawMaterialStore.rawMaterialTestTotal;
});

watch(
  currentRawMaterial,
  newVal => {
    if (newVal?.id) {
      // 根据id获取单个原材料对应的检测信息
      const { id } = newVal;
      // 当前选中原材料发生改变时应该将表格切换为第1页
      pagination.currentPage = 1;
      // 当所选原材料发生更改时应该清空筛选条件
      testFormInstance.value?.resetFields();
      rawMaterialStore.queryRawMaterialInspectList({
        rawMaterialId: id,
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize
      });
    }
  },
  {
    immediate: true
  }
);

/**
 * 查看检验批次的详情信息
 */
const detailOfRawMaterialInspec = (id: string) => {
  emits("detailRawMaterialInspec", id);
};

/**
 * 删除检验批次
 */
const delRawMaterialInspec = async (inspectId: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await rawMaterialStore.delRawMaterialInspectById(inspectId);
  ElMessage.success("删除成功");
  const { id: rawMaterialId } = currentRawMaterial.value;
  rawMaterialStore.queryRawMaterialInspectList({ rawMaterialId });
};

/**
 * 重置
 */
const reset = () => {
  testFormInstance.value?.resetFields();
  search();
};

/**
 * 查询检验批次
 */
const search = () => {
  pagination.currentPage = 1;
  const { inspectNo, inspectDate } = form;
  const { id } = currentRawMaterial.value;
  const params = {
    inspectNo: inspectNo?.length ? inspectNo : null,
    startDate: inspectDate && inspectDate.length ? formatDate(inspectDate[0], dateFormat) : null,
    endDate: inspectDate && inspectDate.length ? formatDate(inspectDate[1], dateFormat) : null,
    rawMaterialId: id,
    pageNo: 1,
    pageSize: pagination.pageSize
  };
  queryRawMaterialInspectList(params);
};

/**
 * 页码数量改变
 */
const onPageSizeChange = (pageSize: number) => {
  const { inspectNo, inspectDate } = form;
  const { id } = currentRawMaterial.value;
  pagination.currentPage = 1;
  pagination.pageSize = pageSize;
  const params = {
    inspectNo: inspectNo?.length ? inspectNo : null,
    startDate: inspectDate && inspectDate.length ? formatDate(inspectDate[0], dateFormat) : null,
    endDate: inspectDate && inspectDate.length ? formatDate(inspectDate[1], dateFormat) : null,
    rawMaterialId: id,
    pageNo: pagination.currentPage,
    pageSize
  };
  queryRawMaterialInspectList(params);
};

/**
 * 页码改变
 */
const onCurrentPageChange = (pageNo: number) => {
  const { inspectNo, inspectDate } = form;
  const { id } = currentRawMaterial.value;
  const params = {
    inspectNo: inspectNo?.length ? inspectNo : null,
    startDate: inspectDate && inspectDate.length ? formatDate(inspectDate[0], dateFormat) : null,
    endDate: inspectDate && inspectDate.length ? formatDate(inspectDate[1], dateFormat) : null,
    rawMaterialId: id,
    pageNo,
    pageSize: pagination.pageSize
  };
  queryRawMaterialInspectList(params);
};

/**
 * 查询原材料列表
 */
const queryRawMaterialInspectList = (params: ISearchRawMaterialInspecReq) => {
  rawMaterialStore.queryRawMaterialInspectList(params);
};
</script>

<style scoped lang="scss"></style>
