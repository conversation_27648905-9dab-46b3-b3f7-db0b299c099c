import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { MEASURE_UNIT } from "@/consts";

export function useColumns() {
  const { dictionaryFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "销售订单号",
      prop: "soNo"
    },
    {
      label: "销售订单行项目号",
      prop: "soItemNo"
    },
    {
      label: "物料编码",
      prop: "materialsCode"
    },
    {
      label: "物料名称",
      prop: "materialsName"
    },
    {
      label: "物料单位",
      prop: "materialsUnit",
      formatter: dictionaryFormatter(MEASURE_UNIT, "subClassCode")
    }
  ];
  return { columns };
}
