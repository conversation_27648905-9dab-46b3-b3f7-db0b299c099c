import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IFileInfo, IOpenApiLogReq, IOpenApiLog } from "@/models";
import { http } from "@/utils/http";

export const queryOpenApiLog = (data: IOpenApiLogReq) => {
  return http.post<IOpenApiLogReq, IListResponse<IOpenApiLog>>(
    withApiGateway("admin-api/infra/interfaceLog/getOpenApiLogPage"),
    { data }
  );
};

export const getDownloadFileUrl = (id: string) => {
  return http.get<string, IResponse<IFileInfo>>(
    withApiGateway(`admin-api/infra/interfaceLog/getLogInterfaceDataFile/${id}`)
  );
};
