import { IPagingReq, ISortReq } from "@/models";

/**
 * 未结束报工列表项
 */
export interface UnfinishedReportWorkItem {
  /** 报工地址 */
  buyerProvince: string;
  /** 设备 */
  deviceCode: string;
  deviceId: string;
  deviceName: string;
  id: string;
  /** 生产订单号 */
  ipoNo: string;
  /** 工序*/
  processCode: string;
  processId: string;
  processName: string;
  /** 报工批次号 */
  productBatchNo: string;
  productionId: string;
  /** 工单编号 */
  woNo: string;
  /** 报工结束时间 */
  workEndTime: string;
  workStartTime: string;

  subClassCode: string;

  /** 销售订单 */
  soIds: string;
  soNos: string;
}

/**
 * 未结束报工列表请求参数
 */
export interface UnfinishedReportWorkListParams extends IPagingReq, ISortReq {
  keyWords?: string;
  /** 范围开始日期 */
  timeFrom?: string;
  /** 范围结束日期 */
  timeTo?: string;
}
