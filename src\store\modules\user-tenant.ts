import { defineStore } from "pinia";
import { CABLE_CATEGORY_CODE } from "@/consts";
import { useUserStore } from "@/store/modules/user";
import { LinkStepEnum, SalesLinkStepEnumAlias } from "@/enums";

export const useUserTenantStore = defineStore({
  id: "cx-user-tenant",
  getters: {
    supportCable: () => {
      const userStore = useUserStore();
      const supportSubclassCodes = userStore.profile?.tenantInfo?.subclassCode;
      return supportSubclassCodes?.some(code => code.slice(0, 2) === CABLE_CATEGORY_CODE);
    },
    supportNonCable: () => {
      const userStore = useUserStore();
      const supportSubclassCodes = userStore.profile?.tenantInfo?.subclassCode;
      return supportSubclassCodes?.some(code => code.slice(0, 2) !== CABLE_CATEGORY_CODE);
    },
    supportLinkSteps(): Array<LinkStepEnum> {
      return [
        LinkStepEnum.linkSales,
        LinkStepEnum.productionPlan,
        ...(this.supportNonCable ? [LinkStepEnum.productionOrder] : []),
        LinkStepEnum.productionData,
        LinkStepEnum.syncOrder,
        LinkStepEnum.qualityEval
      ];
    },
    supportSalesLinkSteps(): Array<SalesLinkStepEnumAlias> {
      return [
        SalesLinkStepEnumAlias.SALES_LINE,
        SalesLinkStepEnumAlias.PRODUCTION_PLAN,
        ...(this.supportNonCable ? [SalesLinkStepEnumAlias.PRODUCTION_ORDER] : []),
        SalesLinkStepEnumAlias.PRODUCTION_DATA,
        SalesLinkStepEnumAlias.SYNC_ORDER,
        SalesLinkStepEnumAlias.QUALITY_EVAL
      ];
    }
  }
});
