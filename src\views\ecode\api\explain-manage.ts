/**
 * @description: 说明书管理
 */

import { http } from "@/utils/http";
import { IListResponse, IPagingReq, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { IExplain, IExplainForm, IExplainSelectItem } from "../models";

/**
 * @description: 获取说明书列表
 */
export const getExplainList = (params: IPagingReq) => {
  const url: string = withApiGateway(`admin-api/ecode/productInstruction/list`);
  return http.get<void, IListResponse<IExplain>>(url, { params });
};

/**
 * @description: 获取说明书详情
 */
export const getExplainDetail = (id: string) => {
  const url: string = withApiGateway(`/admin-api/ecode/productInstruction/detail/${id}`);
  return http.get<string, IResponse<IExplain>>(url);
};

/**
 * @description: 新增说明书
 */
export const addExplain = (data: IExplainForm) => {
  const url: string = withApiGateway(`admin-api/ecode/productInstruction/create`);
  return http.post<IExplainForm, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑说明书
 */
export const editExplain = (data: IExplainForm) => {
  const url: string = withApiGateway(`admin-api/ecode/productInstruction/update`);
  return http.put<IExplainForm, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除说明书
 */
export const deleteExplain = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/productInstruction/delete/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/**
 * @description: 获取所有说明书
 */
export const getAllExplain = () => {
  const url: string = withApiGateway(`admin-api/ecode/productInstruction/nameAll`);
  return http.get<void, IResponse<Array<IExplainSelectItem>>>(url);
};
