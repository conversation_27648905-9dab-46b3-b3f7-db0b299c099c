import { defineStore } from "pinia";
import {
  ISchedulingPlan,
  ICreateSchedulingPlan,
  ISchedulingPlanDetail,
  ISchedulingPlanStatistics,
  ISchedulingPlanQuery
} from "@/models";
import * as SchedulingPlanService from "@/api/scheduling-plan";
import { IResponse } from "@/models";
import { CreateSchedulingPlanStepEnum } from "@/enums";
import { useRowspan } from "@/utils/useRowspan";

export const useSchedulingPlanStore = defineStore({
  id: "cx-scheduling-plan-store",
  state: () => ({
    schedulingPlanData: [] as Array<ISchedulingPlan>,
    schedulingPlanDetail: {} as ISchedulingPlanDetail,
    schedulingPlanForm: {} as ICreateSchedulingPlan,
    addSchedulingPlanModalVisible: false as boolean,
    schedulingPlanDetailVisible: false as boolean,
    schedulingPlanStatistics: {} as ISchedulingPlanStatistics,
    isQuickCreateSchedulingPlan: false as boolean,
    activeCreateSchedulingPlanStep: CreateSchedulingPlanStepEnum.selectSaleOrderLine as CreateSchedulingPlanStepEnum,
    loading: false as boolean,
    /** 排产计划编辑 新增模式  默认为新增模式  */
    schedulingPlanFormAddMode: false as boolean,
    total: 0,
    deleteSchedulingPlanStatus: false
  }),
  actions: {
    // async querySchedulingPlan(purchaseId: string) {
    //   this.loading = true;
    //   const res = await SchedulingPlanService.querySchedulingPlan(purchaseId);
    //   this.schedulingPlanData = res.data || [];
    //   this.loading = false;
    // },

    async querySchedulingPlanPaging(params: ISchedulingPlanQuery) {
      this.loading = true;
      const res = await SchedulingPlanService.querySchedulingPlanPaging(params).finally(() => {
        this.loading = false;
      });
      this.schedulingPlanData = res.data?.list || [];
      this.total = res.data?.total;
    },

    async getSchedulingPlanStatistics(purchaseId: string) {
      const res = await SchedulingPlanService.querySchedulingPlanStatistic(purchaseId);
      const schedulingPlanStatistics = res.data;
      if (Array.isArray(res.data?.unSchduled) && res.data.unSchduled.length) {
        schedulingPlanStatistics.unSchduled = useRowspan(schedulingPlanStatistics.unSchduled, "soNo");
      }
      this.schedulingPlanStatistics = schedulingPlanStatistics;
    },

    async deleteSchedulingPlan(id: string): Promise<IResponse<boolean>> {
      return await SchedulingPlanService.deleteSchedulingPlan(id);
    },

    async editProductionPlan(params: ICreateSchedulingPlan): Promise<IResponse<boolean>> {
      return await SchedulingPlanService.editProductionPlan(params);
    },

    async getSchedulingPlanDetailById(id: string) {
      return SchedulingPlanService.getSchedulingPlanDetailById(id);
    },
    async createSchedulingPlan(params: ICreateSchedulingPlan): Promise<IResponse<boolean>> {
      return await SchedulingPlanService.createProductionPlan(params);
    },

    setSchedulingPlanFormValue(schedulingPlanValue?: ICreateSchedulingPlan) {
      this.schedulingPlanForm = schedulingPlanValue;
    },

    setSchedulingPlanDetail(schedulingPlanDetail?: ISchedulingPlanDetail) {
      this.schedulingPlanDetail = schedulingPlanDetail;
    },

    setActiveCreateSchedulingPlanStep(step: CreateSchedulingPlanStepEnum) {
      this.activeCreateSchedulingPlanStep = step;
    },
    setAddSchedulingPlanModalVisible(visible: boolean) {
      this.addSchedulingPlanModalVisible = visible;
    },
    setSchedulingPlanDetailVisible(visible: boolean) {
      this.schedulingPlanDetailVisible = visible;
    },
    setIsQuickCreateSchedulingPlan(isQuickCreateSchedulingPlan: boolean) {
      this.isQuickCreateSchedulingPlan = isQuickCreateSchedulingPlan;
    },

    setSchedulingPlanFormAddMode(addMode: boolean) {
      this.schedulingPlanFormAddMode = addMode;
    },

    setDeleteSchedulingPlanStatus(status: boolean) {
      this.deleteSchedulingPlanStatus = status;
    }
  }
});
