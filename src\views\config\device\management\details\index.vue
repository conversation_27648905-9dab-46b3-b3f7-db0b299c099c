<template>
  <div class="flex flex-col flex-1 overflow-hidden operate-log">
    <!-- 设备管理详情 -->
    <div class="flex flex-col p-5 mx-6 my-5 overflow-hidden bg-bg_color">
      <TitleBar title="基础信息" class="mb-2" />
      <el-descriptions class="describe-content">
        <el-descriptions-item label="设备编号">
          {{ deviceStore.deviceDetail?.deviceCode }}
        </el-descriptions-item>
        <el-descriptions-item label="设备名称">
          {{ deviceStore.deviceDetail?.deviceName }}
        </el-descriptions-item>
        <el-descriptions-item label="购入年月">
          {{ formatDate(deviceStore.deviceDetail?.purchaseTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="规格型号">
          {{ deviceStore.deviceDetail?.specificationModel }}
        </el-descriptions-item>
        <el-descriptions-item label="应用工序" class="max-h-5">
          <span v-if="!deviceStore.deviceDetail?.processList || !deviceStore.deviceDetail?.processList.length">
            {{ formatProcess(deviceStore.deviceDetail?.processList) }}
          </span>
          <el-tooltip v-else placement="bottom">
            <template #default>
              <div class="line-clamp-2 h-18">
                {{ formatProcess(deviceStore.deviceDetail?.processList) }}
              </div>
            </template>
            <template #content>
              <div class="w-80">
                {{ formatProcess(deviceStore.deviceDetail?.processList) }}
              </div>
            </template>
          </el-tooltip>
        </el-descriptions-item>
        <el-descriptions-item label="功能类型">
          {{ formatEnum(deviceStore.deviceDetail?.functionType, FunctionTypeEnum, "FunctionTypeEnum") }}
        </el-descriptions-item>
        <el-descriptions-item label="数采方式">
          {{ formatEnum(deviceStore.deviceDetail?.collectionMode, CollectionModeEnum, "CollectionModeEnum") }}
        </el-descriptions-item>
        <el-descriptions-item label="最后采集时间">
          {{ formatDate(deviceStore.deviceDetail?.lastCollectionTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="所属车间">
          {{ deviceStore.deviceDetail?.workshopName }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="flex flex-col flex-1 p-5 mx-6 mb-5 overflow-hidden bg-bg_color">
      <TabSwitch
        :operate-modules="operateModules"
        @switchModuleEvent="toggleTab($event)"
        style="padding: 0"
        v-model:current-tab-index="activeTabIndex"
      />
      <Component :is="currentComp" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, shallowRef } from "vue";
import TitleBar from "@/components/TitleBar";
import { FunctionTypeEnum, CollectionModeEnum } from "@/enums";
import TabSwitch from "@/components/cx-tab-switch";
import { IModule } from "@/components/cx-tab-switch/types";
import DeviceAcquisition from "./device-acquisition/index.vue";
import AcquisitionMaintain from "./acquisition-maintain/index.vue";
import AssociatedCamera from "./associated-camera/index.vue";
import DataAcquisition from "./data-acquisition/index.vue";
import { formatEnum, formatDate } from "@/utils/format";
import { useRouter, useRoute } from "vue-router";
import { useDeviceStore } from "@/store/modules";

enum EModuleTypeEnum {
  /** 设备采集点 */
  DeviceAcquisition = "device-acquisition",
  /** 采集点关系维护 */
  AcquisitionMaintain = "acquisition-maintain",
  /** 关联摄像头 */
  AssociatedCamera = "associated-camera",
  /** 数据采集 */
  DataAcquisition = "data-acquisition"
}

const deviceStore = useDeviceStore();
const router = useRouter();
const route = useRoute();
const deviceId: string = route.params.id as string;
const activeTabIndex = computed(() => {
  const temp = operateModules.value.findIndex(m => m.moduleCode === route.query.tab);
  if (temp >= 0) {
    return temp;
  }
  return 0;
});

// tab可以切换的模块根据设备的功能类型自动转换
const operateModules = computed<Array<IModule>>(() => {
  if (!deviceStore.deviceDetail) return [];
  switch (deviceStore.deviceDetail.functionType) {
    case FunctionTypeEnum.PRODUCTION_DEVICE:
      return [
        {
          moduleCode: EModuleTypeEnum.DeviceAcquisition,
          moduleName: "设备采集点"
        },
        {
          moduleCode: EModuleTypeEnum.AcquisitionMaintain,
          moduleName: "标准采集点维护"
        },
        {
          moduleCode: EModuleTypeEnum.AssociatedCamera,
          moduleName: "关联摄像头"
        },
        {
          moduleCode: EModuleTypeEnum.DataAcquisition,
          moduleName: "数据采集"
        }
      ];
    case FunctionTypeEnum.TEST_DEVICE:
      return [
        {
          moduleCode: EModuleTypeEnum.AssociatedCamera,
          moduleName: "关联摄像头"
        }
      ];
    case FunctionTypeEnum.JFNY_TEST_DEVICE:
      return [
        {
          moduleCode: EModuleTypeEnum.DeviceAcquisition,
          moduleName: "设备采集点"
        },
        {
          moduleCode: EModuleTypeEnum.AssociatedCamera,
          moduleName: "关联摄像头"
        }
      ];
    default:
      return [];
  }
});
const currentComp = shallowRef();

onMounted(async () => {
  // 页面加载完成后切换至首个tab栏
  await deviceStore.getDeviceDetailById(deviceId);
  initToggleTab();
});

function updateUrl(
  tab: EModuleTypeEnum,
  subTab?: string | string[],
  workStartTime?: string | string[],
  workEndTime?: string | string[]
) {
  router.replace({
    path: route.path,
    query: {
      tab,
      subTab,
      workStartTime,
      workEndTime
    }
  });
}

const toggleTab = (item: IModule) => {
  switch (item.moduleCode) {
    case EModuleTypeEnum.DeviceAcquisition:
      currentComp.value = DeviceAcquisition;
      updateUrl(EModuleTypeEnum.DeviceAcquisition);
      break;
    case EModuleTypeEnum.AcquisitionMaintain:
      currentComp.value = AcquisitionMaintain;
      updateUrl(EModuleTypeEnum.AcquisitionMaintain);
      break;
    case EModuleTypeEnum.AssociatedCamera:
      currentComp.value = AssociatedCamera;
      updateUrl(EModuleTypeEnum.AssociatedCamera);
      break;
    case EModuleTypeEnum.DataAcquisition:
      currentComp.value = DataAcquisition;
      updateUrl(
        EModuleTypeEnum.DataAcquisition,
        route.query.subTab,
        route.query.workStartTime,
        route.query.workEndTime
      );
      break;
    default:
      break;
  }
};

/**
 * @description: 列表数据取值转为字符串
 */
const formatProcess = (list: Array<{ processName: string; processId: string }>) => {
  if (!Array.isArray(list) || list.length === 0) {
    return "--";
  }
  return list.map(x => x.processName).join(",");
};

/**
 * @description: 初始化时切换tab
 */
function initToggleTab() {
  const currentModule = operateModules.value[activeTabIndex.value];
  toggleTab(currentModule);
}
</script>

<style scoped lang="scss"></style>
