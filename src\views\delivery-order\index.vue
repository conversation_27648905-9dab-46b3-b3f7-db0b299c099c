<template>
  <div class="bg-bg_color pt-[8px] pr-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="flex-1">
      <ElFormItem label="采购供货单编号：">
        <ElInput
          class="!w-[200px]"
          clearable
          v-model="state.params.supplyNo"
          placeholder="请输入采购供货单编号"
          @clear="onConfirmQuery"
        />
      </ElFormItem>
      <ElFormItem label="合同类型：">
        <EnumSelect
          class="w-full"
          v-model="state.params.conType"
          placeholder="请选择合同类型"
          :enum="ContractTypeEnum"
          enumName="ContractTypeEnum"
        />
      </ElFormItem>
      <ElFormItem label="创建时间：">
        <el-date-picker
          v-model="state.params.createTime"
          type="daterange"
          range-separator="～"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton
      v-auth="PermissionKey.form.formSupplyOrderCreate"
      size="large"
      type="primary"
      :icon="Plus"
      @click="onAddDeliveryOrder()"
      >新增供货单
    </ElButton>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.orderTableData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
      @selection-change="handleSelectionChange"
    >
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onQuery(data.row.id)"> 详情 </ElButton>
          <ElButton v-auth="PermissionKey.form.formSupplyOrderEdit" type="primary" link @click="onEdit(data.row.id)">
            编辑
          </ElButton>
          <ElButton v-auth="PermissionKey.form.formSupplyOrderDelete" link type="danger" @click="onDelete(data.row.id)">
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>

  <el-dialog
    :title="getOrderFormModalTitle()"
    align-center
    class="default"
    destroy-on-close
    v-model="state.orderFormModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCancelOrderFormModal"
  >
    <OrderForm ref="orderFormRef" />
    <template #footer>
      <el-button @click="onCancelOrderFormModal()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSaveOrderFormModal()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { reactive, ref, onMounted, watch } from "vue";
import { IDeliveryOrderListData, IDeliveryOrderReqParams } from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useDeliveryOrderHook } from "./hooks/delivery-order-hook";
import OrderForm from "./order-form/index.vue";
import { useConfirm } from "@/utils/useConfirm";
import { ContractTypeEnum } from "@/enums";
import { useRouter } from "vue-router";
import { useDeliveryOrderStore } from "@/store/modules";
import { pickBy } from "lodash-unified";
import EnumSelect from "@/components/EnumSelect";
import { PermissionKey } from "@/consts";

usePageStoreHook().setTitle("供货单信息");
const router = useRouter();
const { columns } = useColumns();
const { pagination } = useTableConfig();
const deliveryOrderStore = useDeliveryOrderStore();
const deliveryOrderSelection = ref<IDeliveryOrderListData[]>([]);

const {
  queryDeliveryOrderDataList,
  getDeliveryOrderDetailById,
  createDeliveryOrder,
  editDeliveryOrder,
  deleteDeliveryOrder,
  setDeliveryOrderStorage,
  clearDeliveryOrderStorage
} = useDeliveryOrderHook();

const state = reactive<{
  orderFormModalVis: boolean;
  params: IDeliveryOrderReqParams;
  orderTableData: Array<IDeliveryOrderListData>;
  selectId: string;
}>({
  orderFormModalVis: false,
  params: {},
  orderTableData: [],
  selectId: ""
});

watch(
  () => deliveryOrderStore.total,
  () => {
    pagination.total = deliveryOrderStore.total;
  },
  {
    immediate: true
  }
);

onMounted(async () => {
  state.orderTableData = await getTableData(queryParams());
});

const onPageSizeChange = async () => {
  state.orderTableData = await getTableData(queryParams());
};
const onCurrentPageChange = async () => {
  state.orderTableData = await getTableData(queryParams());
};

const onConfirmQuery = async () => {
  state.orderTableData = await getTableData(queryParams());
};

const onResetQuery = async () => {
  state.params = {};
  pagination.currentPage = 1;
  state.orderTableData = await getTableData(queryParams());
};
const saveLoading = ref<boolean>(false);
const loading = ref<boolean>(false);
const orderFormRef = ref<InstanceType<typeof OrderForm>>();
const getOrderFormModalTitle = () => (!state.selectId ? "新增供货单" : "编辑供货单");

async function onAddDeliveryOrder() {
  state.selectId = "";
  state.orderFormModalVis = true;
}
const onQuery = async (id: string) => {
  state.selectId = id;
  router.push(`/supply-order/${id}`);
};

const onSave = async () => {
  const formValue: IDeliveryOrderListData | false = await orderFormRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.id) {
    await createDeliveryOrder(formValue);
  } else {
    await editDeliveryOrder(formValue);
  }
  clearForm();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  state.params = {};
  pagination.currentPage = 1;
  state.orderTableData = await getTableData(queryParams());
};

const onEdit = async (id: string) => {
  const deliveryOrderDetail: IDeliveryOrderListData = await getDeliveryOrderDetailById(id);
  if (!deliveryOrderDetail) {
    return;
  }
  state.orderFormModalVis = true;
  state.selectId = id;
  setDeliveryOrderStorage(deliveryOrderDetail);
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteDeliveryOrder(id);
  ElMessage.success("删除成功");
  state.orderTableData = await getTableData(queryParams());
};

const onCancelOrderFormModal = () => {
  clearForm();
};
const onSaveOrderFormModal = useLoadingFn(onSave, saveLoading);
const getTableData = useLoadingFn(queryDeliveryOrderDataList, loading);

const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  const params: IDeliveryOrderReqParams = pickBy(state.params, value => !!value);
  return params;
};

function clearForm() {
  clearDeliveryOrderStorage();
  state.orderFormModalVis = false;
}

const handleSelectionChange = (val: IDeliveryOrderListData[]) => {
  deliveryOrderSelection.value = val;
};
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
