/**
 * @description: 质量规范
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse } from "@/models";
import {
  QualitySpecificationItem,
  QualitySpecificationListParams,
  EditQualitySpecificationParams
} from "@/models/quality-tracing";

//#region 质量规范
/**
 * @description: 查询质量规范列表
 */
export const queryQualitySpecificationList = (params: QualitySpecificationListParams) => {
  return http.post<QualitySpecificationListParams, IListResponse<QualitySpecificationItem>>(
    withApiGateway("admin-api/system/quality-specification/page"),
    {
      data: params
    }
  );
};

/**
 * @description: 根据id查询某条质量规范
 */
export const queryQualitySpecificationById = (id: string) => {
  return http.get<string, IResponse<QualitySpecificationItem>>(
    withApiGateway(`admin-api/system/quality-specification/info/${id}`)
  );
};

/**
 * @description: 新增质量规范
 */
export const addQualitySpecification = (params: EditQualitySpecificationParams) => {
  return http.post<EditQualitySpecificationParams, IResponse<string>>(
    withApiGateway("admin-api/system/quality-specification/add"),
    {
      data: params
    }
  );
};

/**
 * @description: 编辑质量规范
 */
export const editQualitySpecification = (params: EditQualitySpecificationParams, id: string) => {
  return http.put<EditQualitySpecificationParams, IResponse<boolean>>(
    withApiGateway(`admin-api/system/quality-specification/update/${id}`),
    {
      data: params
    }
  );
};

/**
 * @description: 复制质量规范
 */
export const copyQualitySpecification = (params: EditQualitySpecificationParams, id: string) => {
  return http.put<EditQualitySpecificationParams, IResponse<string>>(
    withApiGateway(`admin-api//system/quality-specification/clone/${id}`),
    {
      data: params
    }
  );
};

/**
 * @description: 切换质量规范使用状态
 */
export const toggleQualitySpecificationUseStatus = (id: string) => {
  return http.put<string, IResponse<boolean>>(
    withApiGateway(`admin-api/system/quality-specification/enable-disable/${id}`)
  );
};

/**
 * @description: 请求生成一条质量规范编号
 */
export const genQualitySpecificationNumber = (subClassCode: string) => {
  return http.get<string, IResponse<string>>(
    withApiGateway(`admin-api/system/serial-number/get-by-prefix?code=QUALITY_CODE&prefiox=${subClassCode}`)
  );
};
//#endregion
