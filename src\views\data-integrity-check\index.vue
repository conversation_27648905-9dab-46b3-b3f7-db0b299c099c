<template>
  <div class="flex flex-col flex-1 overflow-hidden">
    <TabSwitch
      :operate-modules="dataIntegrityCheckModules"
      @switchModuleEvent="clickModuleChange"
      v-model:currentTabIndex="currentIndex"
    />
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden" v-if="categoryCode">
      <DataIntegrityCheckTable :category-code="categoryCode" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import TabSwitch from "@/components/cx-tab-switch";
import { IModule } from "@/components/cx-tab-switch/types";
import { useCategoryStore } from "@/store/modules";
import { ICategory } from "@/models";
import { map } from "lodash-unified";
import DataIntegrityCheckTable from "./data-integrity-check-table/index.vue";

// 设置标题
usePageStoreHook().setTitle("数据完整性检查");

const dataIntegrityCheckModules = reactive<Array<IModule>>([]);
const categoryStore = useCategoryStore();
const categoryCode = ref<string>();
const currentIndex = ref(0);

onMounted(async () => {
  const categoryList: Array<ICategory> = await categoryStore.getAllSubclasses();
  const modules = map(categoryList, ({ categoryCode, categoryName }) => ({
    moduleCode: categoryCode,
    moduleName: categoryName
  }));
  Object.assign(dataIntegrityCheckModules, modules);
  categoryCode.value = categoryList[0].categoryCode;
});

/**
 * 点击不同的模块
 */
const clickModuleChange = (item: IModule) => {
  categoryCode.value = item.moduleCode;
};
</script>

<style scoped lang="scss"></style>
