<template>
  <el-dialog
    class="small-md"
    title="风险提醒"
    v-model="visible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeChange"
  >
    <section class="trigger-content px-2">
      <div class="continue-text py-3 text-danger">
        <FontIcon icon="icon-warning-fill" class="text-danger" />
        是否确定继续触发质量评分？
      </div>
      <div class="confirm-text mb-5 p-3 rounded-[4px]">
        <span>{{ checkText }}</span
        >, 直接触发评分可能会影响质量评分分值, 建议<span class="eip-text pb-0.5 cursor-pointer" @click="EIPCheck"
          >在EIP检查数据</span
        >是否完整后再触发评分
      </div>
    </section>
    <template #footer>
      <section class="footer flex justify-between">
        <div class="agree-check">
          <el-checkbox v-model="agreeCheck">我已知晓</el-checkbox>
        </div>
        <div class="footer-actions">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" :disabled="!agreeCheck" @click="continueTrigger">继续触发质量评分</el-button>
        </div>
      </section>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, inject, ref } from "vue";
import { eipGuideKey } from "./tokens";

const props = defineProps<{
  modelValue: boolean;
  checkText?: string;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "triggerScore"): void;
}>();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const agreeCheck = ref<boolean>(false);
const ctx = inject(eipGuideKey);

function closeChange() {
  visible.value = false;
  agreeCheck.value = false;
}

function continueTrigger() {
  emit("triggerScore");
}

function EIPCheck() {
  ctx.visible = true;
}
</script>

<style scoped lang="scss">
.eip-text {
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-color-primary);
}

.confirm-text {
  background-color: var(--el-color-info-light-9);
  line-height: 26px;
}
</style>
