export interface IReportWork {
  id?: string;
  /** 供应商编码 */
  supplierCode: string;

  /** 生产订单编号 */
  ipoNo: string;

  /** 生产批次号 报工批次号 */
  productBatchNo: string;
  /**  */
  productionId: string;

  /** 工序名称 */
  processName?: string;

  /** 	品类编码 */
  categoryCode: string;

  /** 种类编码 */
  subclassCode: string;
  /** 工序编码 */
  processCode: string;

  /** 工序Id */
  processId: string;

  /** 生产工单编号 */
  woNo: string;
  /** 	客户所属省份 报工地址 */
  buyerProvince: string;
  /** 设备名称 */
  deviceName: string;

  /** 设备编码 */
  deviceCode: string;

  /** 设备Id */
  deviceId: string;
  /** 	工单Id */
  workId: string;

  /** 采购订单ID */
  purchaseId?: string;

  /** 工单开始日期 */
  workStartTime: Date;

  /** 工单完成日期 */
  workEndTime: Date;

  /** 报工是否已打包完成 */
  dataPrepared?: boolean;
}

/** 报工批次号 */
export interface IReportWorkBatchNoReq {
  orderId: string;
  processId: string;
  orderType?: number;
}
