import { IProductOrder, IWorkOrder } from "@/models";

export function useWorkOrderDefaultValueFromProductOrder(productOrder: IProductOrder): Partial<IWorkOrder> {
  const {
    materialsCode,
    materialsName,
    materialsDesc,
    materialsUnit,
    specificationModel,
    voltageClasses,
    planStartDate,
    planFinishDate,
    amount,
    unit
  } = productOrder;
  return {
    unit,
    amount,
    planStartDate,
    planFinishDate,
    materialsCode,
    materialUnit: materialsUnit,
    materialName: materialsName,
    materialDesc: materialsDesc,
    specificationType: specificationModel,
    voltageLevel: voltageClasses
  };
}
