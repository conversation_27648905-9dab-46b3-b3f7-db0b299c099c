import { fullDateFormat, dateFormat } from "@/consts";
import {
  ColumnWidth,
  IntegritySyncStatusEnum,
  IntegritySyncStatusName,
  IntegrityTriggerScoreEnum,
  IntegrityTriggerScoreName,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc,
  ProductionStateEnum,
  TableWidth
} from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { EnumCell } from "@/components/TableCells";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
import { h } from "vue";
import { TableColumnCtx } from "element-plus";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();

  const syncStatusFormatter = (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    if (cellValue === IntegritySyncStatusEnum.ALL_SYNCED) {
      return h(
        CxTag,
        { type: "success", icon: "icon-success-fill" },
        () => IntegritySyncStatusName[IntegritySyncStatusEnum.ALL_SYNCED]
      );
    } else if (cellValue === IntegritySyncStatusEnum.SYNC_FALUT) {
      return h(
        CxTag,
        { type: "danger", icon: "icon-fail-fill" },
        () => IntegritySyncStatusName[IntegritySyncStatusEnum.SYNC_FALUT]
      );
    } else if (cellValue === IntegritySyncStatusEnum.PART_SYNCED) {
      return h(
        CxTag,
        { type: "neutral", icon: "icon-sync" },
        () => IntegritySyncStatusName[IntegritySyncStatusEnum.PART_SYNCED]
      );
    } else if (cellValue === IntegritySyncStatusEnum.NO_SYNC) {
      return h(CxTag, { type: "info" }, () => IntegritySyncStatusName[IntegritySyncStatusEnum.NO_SYNC]);
    }
    return null;
  };

  /**
   * @description: 触发评分
   */
  const triggerScoreFormatter = (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    if (cellValue === IntegrityTriggerScoreEnum.TRIGGER_SUCCESS) {
      return h(
        CxTag,
        { type: "success", icon: "icon-success-fill" },
        () => IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.TRIGGER_SUCCESS]
      );
    } else if (cellValue === IntegrityTriggerScoreEnum.TRIGGER_FAIL) {
      return h(
        CxTag,
        { type: "danger", icon: "icon-fail-fill" },
        () => IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.TRIGGER_FAIL]
      );
    } else if (cellValue === IntegrityTriggerScoreEnum.NO_TRIGGER) {
      return h(CxTag, { type: "info" }, () => IntegrityTriggerScoreName[IntegrityTriggerScoreEnum.NO_TRIGGER]);
    }
    return null;
  };

  const columns: TableColumnList = [
    {
      type: "selection",
      width: TableWidth.check
    },
    {
      label: "生产订单号",
      prop: "ipoNo",
      headerRenderer: () => (
        <KeywordAliasHeader
          code={KeywordAliasEnum.IPO_NO}
          defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
        />
      ),
      slot: "ipoNo",
      fixed: "left",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "采购订单号",
      prop: "poNos",
      slot: "poNos",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "销售订单号",
      prop: "soNos",
      slot: "soNos",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "合同签订日期",
      prop: "sellerSignTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(dateFormat)
    },
    {
      label: "生产状态",
      prop: "productionStatus",
      width: TableWidth.largeType,
      cellRenderer: (data: TableColumnRenderer) => EnumCell(data, ProductionStateEnum, "productionStateEnum")
    },
    {
      label: "统计时间",
      prop: "updateTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "首次同步时间",
      prop: "firstSyncEipTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat),
      sortable: "custom"
    },
    {
      label: "评分状态",
      prop: "eipTriggerStatus",
      width: ColumnWidth.Char7,
      formatter: triggerScoreFormatter
    },
    {
      label: "评分时间",
      prop: "lastTriggerTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "同步状态",
      prop: "eipSyncStatus",
      width: ColumnWidth.Char7,
      formatter: syncStatusFormatter,
      fixed: "right"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char15
    }
  ];
  return { columns };
}
