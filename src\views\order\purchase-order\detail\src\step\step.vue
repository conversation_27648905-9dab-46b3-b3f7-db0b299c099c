<template>
  <Step :entryOrder="entryOrder" :steps="steps" @gotoEvent="goto" />
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import Step from "@/views/order/components/detail/step.vue";
import { OrderType } from "@/enums/order";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { useSocket } from "@/utils/useSocket";
import { RefreshSceneEnum, SocketEventEnum } from "@/enums";

const socket = useSocket();

const purchaseOrderDetailStore = usePurchaseOrderDetailStore();

const steps = computed(() => {
  return purchaseOrderDetailStore.steps || [];
});
const entryOrder = OrderType.PURCHASE;

function listenStepStatusChange() {
  socket.on(SocketEventEnum.REFRESH, event => {
    if (
      event.type !== RefreshSceneEnum.SYNC_DATA_LIST ||
      purchaseOrderDetailStore.purchaseOrderId !== event.purchaseOrderId
    ) {
      return;
    }
    purchaseOrderDetailStore.refreshStepStatusHandle();
  });
}

function goto(key) {
  purchaseOrderDetailStore.goto(key);
}

onMounted(listenStepStatusChange);
</script>
