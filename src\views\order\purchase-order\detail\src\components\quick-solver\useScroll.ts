import { ref, watchEffect } from "vue";
import { useEventListener } from "@vueuse/core";
import { debounce } from "@pureadmin/utils";

export function useScroll() {
  const container = ref<HTMLDivElement>();
  let delta = 0;
  let scrollCleanUp: Fn;
  let scrolling = false;
  const hasNext = ref(false);
  const hasPrev = ref(false);

  watchEffect(() => {
    calcDelta();
    if (scrollCleanUp) {
      scrollCleanUp();
    }
    scrollCleanUp = useEventListener(
      container.value,
      "scroll",
      debounce(() => {
        checkStatus();
        scrolling = false;
      }),
      { passive: true }
    );
  });

  useEventListener(window, "resize", () => calcDelta(), { passive: true });

  function calcDelta() {
    if (!container.value) {
      delta = 0;
      return;
    }
    const parentWidth = container.value.clientWidth;
    const childWidth = container.value.querySelector(".card-item").getBoundingClientRect().width;
    const itemWith = childWidth + parseFloat(getComputedStyle(container.value).gap);
    delta = Math.floor(parentWidth / itemWith) * itemWith;
    checkStatus();
  }

  function prev() {
    if (!delta || scrolling) {
      return;
    }
    scrollTo(container.value.scrollLeft - delta);
  }

  function next() {
    if (!delta || scrolling) {
      return;
    }
    scrollTo(container.value.scrollLeft + delta);
  }

  function scrollTo(left: number) {
    scrolling = true;
    container.value.scrollTo({ left, behavior: "smooth" });
  }

  function checkStatus() {
    const { scrollLeft, clientWidth, scrollWidth } = container.value;
    hasNext.value = scrollLeft + clientWidth < scrollWidth;
    hasPrev.value = scrollLeft > 0;
  }

  return {
    container,
    prev,
    next,
    hasNext,
    hasPrev
  };
}
