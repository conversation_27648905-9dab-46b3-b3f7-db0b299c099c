<template>
  <div class="w-full h-full overflow-hidden">
    <!-- 销售订单 -->
    <el-scrollbar v-if="syncs?.length || loading" class="w-full h-full">
      <div class="order-list flex flex-col gap-5 py-1">
        <LoadingSkeleton :loading="loading" :count="count">
          <item v-for="sync in syncs" :key="sync.id" :sync="sync" />
        </LoadingSkeleton>
      </div>
      <el-dialog
        :title="detailStore.title"
        v-model="visible"
        fullscreen
        destroy-on-close
        :close-on-press-escape="false"
        class="state-grid-order-sync-detail-dialog"
        :show-close="false"
      >
        <template #header="{ close, titleId, titleClass }">
          <div :id="titleId" class="flex justify-between">
            <div class="flex gap-3 items-center">
              <el-button link @click="close">
                <el-icon size="20"><Back /></el-icon>
              </el-button>
              <div :class="titleClass" v-if="detailStore.title">{{ detailStore.title }}</div>
            </div>
            <el-button type="danger" @click="close">
              <el-icon size="20"><Close /></el-icon>
            </el-button>
          </div>
        </template>
        <Detail />
      </el-dialog>
    </el-scrollbar>
    <template v-else>
      <EmptyDataDisplay />
    </template>
  </div>
</template>

<script setup lang="ts">
import LoadingSkeleton from "@/views/order/sales-order/detail/src/sg-order/list/loading-skeleton.vue";
import Item from "@/views/order/sales-order/detail/src/sg-order/list/item.vue";
import Detail from "@/views/order/sales-order/detail/src/sg-order/detail/detail.vue";
import {
  useSalesOrderDetailStore,
  useSalesOrderSyncInfo,
  useSalesStateGridOrderSyncIotListStore
} from "@/store/modules";
import { computed, onMounted, ref } from "vue";
import { StateGridOrderSyncStep } from "@/enums/state-grid-order";
import { useThrottleFn } from "@vueuse/core";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { useSocket } from "@/utils/useSocket";
import { PurchaseChannel, RefreshSceneEnum, SocketEventEnum } from "@/enums";
import EmptyDataDisplay from "@/views/order/components/empty-data-display/index.vue";
import { isOrderLineDataType } from "../sync-step-tool";
import { Close, Back } from "@element-plus/icons-vue";

const store = useSalesOrderSyncInfo();
store.toggleActiveStep(StateGridOrderSyncStep.SALES_LINE);
const salesDetailStore = useSalesOrderDetailStore();
const detailStore = useSalesOrderSyncInfo();
const listStore = useSalesStateGridOrderSyncIotListStore();

const socket = useSocket();
const loading = ref<boolean>(false);
const count = computed(() => salesDetailStore.salesOrder?.saleLineCount);
const syncs = computed(() => listStore.iotSyncs);
const visible = computed({
  get() {
    return detailStore.dialogVisible;
  },
  set(value) {
    detailStore.$patch({ dialogVisible: value });
  }
});

const handleRefresh = useThrottleFn(refresh, REFRESH_SYNC_DATA_PERIOD, true);

onMounted(() => {
  loading.value = true;
  listStore.setSalesOrderId(salesDetailStore.saleOrderId);
  listStore.setChannel(PurchaseChannel.IOT);
  handleRefresh();
  addRefreshEventListener();
});

async function refresh() {
  await listStore.refreshSalesOrderSyncIotList().finally(() => {
    loading.value = false;
  });
}

function addRefreshEventListener() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, dataType, purchaseOrderItemId } = event;
    const matching = listStore.iotSyncs.find(item => item.id === purchaseOrderItemId);
    if ((type !== RefreshSceneEnum.SYNC_DATA && type !== RefreshSceneEnum.IOT_SYNC_DATA) || !matching) return;
    if (isOrderLineDataType(dataType)) {
      handleRefresh();
    }
  });
}
</script>
