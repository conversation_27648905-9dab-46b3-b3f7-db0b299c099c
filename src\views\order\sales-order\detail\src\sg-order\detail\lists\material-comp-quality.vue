<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditRawMaterialDialog ref="editDialog" :subclass-code="subclassCode" />
  <DetailRawMaterialDialog ref="detailDialog" />
  <FileSyncListDialog
    :files="files"
    v-model="fileSyncListVisible"
    @sync="syncFile"
    @syncDetailEvent="getSyncHistoryByDataId"
  />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncDataFormatEnum, StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesOrderDetailStore, useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { IFileSync, IRawMaterialSync } from "@/models";
import { computed, provide, reactive, ref } from "vue";
import EditRawMaterialDialog from "@/views/components/state-grid-order-sync/dialogs/edit-raw-material-dialog.vue";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import {
  PROVIDE_PROCESS_INSPECT_TOKEN,
  stateGridOrderSyncEditKey
} from "@/views/components/state-grid-order-sync/tokens";
import { useSubclassCode } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSubclassCode";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import DetailRawMaterialDialog from "@/views/components/state-grid-order-sync/dialogs/detail-raw-material-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import { fileSyncFormatter } from "@/views/components/state-grid-order-sync/formatters/file-sync-formatter";
import FileSyncListDialog from "@/views/components/state-grid-order-sync/dialogs/file-sync-list-dialog.vue";

const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditRawMaterialDialog>,
  InstanceType<typeof DetailRawMaterialDialog>,
  IRawMaterialSync
>();

const type = StateGridOrderSyncType.RAW_MATERIAL_INSPECTION;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const { subclassCode } = useSubclassCode();

const salesOrderStore = useSalesOrderDetailStore();
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const activeFileSyncId = ref<string>();
const fileSyncListVisible = ref(false);

const files = computed(() => {
  const sync = listStore.data.find(datum => datum.id === activeFileSyncId.value) as IRawMaterialSync | undefined;
  return sync?.fileList || [];
});

const columns: Array<TableColumns> = [
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order
  },
  {
    label: "原材料编号",
    prop: "code",
    minWidth: TableWidth.order,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "文件同步状态",
    prop: "fileList",
    minWidth: TableWidth.type,
    formatter: fileSyncFormatter(id => {
      activeFileSyncId.value = id;
      fileSyncListVisible.value = true;
    })
  },
  {
    label: "原材料名称",
    prop: "name",
    minWidth: TableWidth.name
  },
  {
    label: "原材料类型",
    prop: "processName",
    minWidth: TableWidth.name
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);
provide(
  PROVIDE_PROCESS_INSPECT_TOKEN,
  reactive({
    detailMaterialCategory: salesOrderStore.detailMaterialCategory
  })
);

function syncFile(file: IFileSync) {
  const { dataId, fileName } = file;
  syncByDataId(dataId, fileName, StateGridOrderSyncDataFormatEnum.FILE);
}
</script>

<style scoped></style>
