<template>
  <el-dialog
    v-model="visible"
    title="编辑技术标准"
    align-center
    draggable
    class="small"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <EditForm ref="editFormRef" />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :saveLoading="saveLoading" @click="saveEditInfo">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useLoadingFn } from "@/utils/useLoadingFn";
import EditForm from "../components/edit-form.vue";
import { computed, inject, ref, watch } from "vue";
import { useTechnicalStandardStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { technicalStandardKey } from "../tokens";
import { ITechnicalStandardInfo } from "@/models/technical-standard";

const props = defineProps<{
  modelValue?: boolean;
  standardInfo?: ITechnicalStandardInfo;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});

const store = useTechnicalStandardStore();
const saveLoading = ref<boolean>(false);
const saveEditInfo = useLoadingFn(saveStandardInfo, saveLoading);
const editFormRef = ref<InstanceType<typeof EditForm>>();
const ctx = inject(technicalStandardKey);

watch(
  [visible, () => props.standardInfo],
  newVal => {
    const [res, info] = newVal;
    if (res && info) {
      patchFormValue();
    }
  },
  {
    immediate: true
  }
);

/** 更新表单数据 */
async function patchFormValue() {
  if (props.standardInfo) {
    setTimeout(() => {
      editFormRef.value?.patchFormValue(props.standardInfo);
    }, 0);
  }
}

/** 保存数据 */
async function saveStandardInfo() {
  const formValue = await editFormRef.value?.getFormValue();
  await store.saveEditStandardInfo(props.standardInfo?.id, formValue);
  ElMessage.success("保存成功");
  visible.value = false;
  ctx.refresh();
}
</script>
