<template>
  <BaseList
    :columns="columns"
    type="production"
    :check-data="store.isCable"
    v-bind="$attrs"
    ref="baseList"
    @dataChange="dataChange"
  />
  <EditProductionOrderDialog :orderType="orderType" :orderId="orderId" ref="editDialog" />
  <DetailProductionOrderDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import {
  ColumnWidth,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc,
  StateGridOrderSyncResult,
  TableWidth,
  VoltageClassesEnumMapDesc
} from "@/enums";
import { computed, provide, ref } from "vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { dateFormat } from "@/consts";
import { IProductionOrderSync } from "@/models";
import { formatDecimal } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { OperatorCell } from "@/components/TableCells";
import { infoHeader } from "@/components/TableHeader";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditProductionOrderDialog from "@/views/components/state-grid-order-sync/dialogs/edit-production-order-dialog.vue";
import DetailProductionOrderDialog from "@/views/components/state-grid-order-sync/dialogs/detail-production-order-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import BaseList from "../base-list.vue";
import { missingDataFormatter } from "../formatters/missing-data-formatter";
import { useAuditItemStatus } from "@/views/components/sync-audit/hooks/audit-item-status";
import { h } from "vue";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const props = defineProps<{
  cardId: string;
}>();

const store = useStateGridSyncAuditStore();

const { updateItemStatus } = useAuditItemStatus();
const { enumFormatter, dateFormatter, mapFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditProductionOrderDialog>,
  InstanceType<typeof DetailProductionOrderDialog>,
  IProductionOrderSync
>();
const orderType = computed(() => store.baseInfo.orderType);
const orderId = computed(() => store.baseInfo.orderId);

provide(stateGridOrderSyncEditKey, {
  refreshFn: pageInfo => baseList.value?.refresh(pageInfo)
});

const columns: TableColumnList = [
  {
    label: "生产订单号",
    prop: "ipoNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),
    minWidth: ColumnWidth.Char14,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "销售订单号",
    prop: "soNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.largeName
  },
  {
    label: "电压等级",
    prop: "voltageClasses",
    width: TableWidth.type,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "需求数量",
    prop: "amount",
    minWidth: TableWidth.number,
    formatter: row => {
      const { amount, unitName } = row;
      return amount ? `${formatDecimal(amount)} ${unitName}` : null;
    }
  },
  {
    label: "实际开始日期",
    prop: "actualStartDate",
    width: TableWidth.date,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "实际结束日期",
    prop: "actualEndDate",
    width: TableWidth.date,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "数据检查",
    prop: "missingData",
    width: TableWidth.type,
    fixed: "right",
    hide: () => !store.isCable,
    formatter: missingDataFormatter(),
    headerRenderer: infoHeader("未显示有缺失时，建议人工检查相关数据，请在系统中维护所有生产数据数据")
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    width: TableWidth.operation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        }
      ])
  }
];

function dataChange(data: Array<IProductionOrderSync>) {
  if (!store.isCable || !props.cardId) {
    return;
  }
  updateItemStatus(
    props.cardId,
    data.some(datum => datum.missingData)
  );
}
</script>

<style scoped></style>
