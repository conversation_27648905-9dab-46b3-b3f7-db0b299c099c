import { ProductionStateEnum, TableWidth } from "@/enums";
import { EnumCell } from "@/components/TableCells";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { dateFormat, MEASURE_UNIT } from "@/consts";
import { dayjs } from "element-plus";
import { TableColumnRenderer } from "@pureadmin/table";

/**
 * @description: 生成生产工单表格配置
 */
export function genProductionWorkOrderTableColumnsConfig() {
  const { withUnitFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "工单编号",
      prop: "woNo",
      width: TableWidth.largeOrder,
      slot: "woNo"
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.type
    },
    {
      label: "工序",
      prop: "processName",
      minWidth: TableWidth.name
    },
    {
      label: "生产数量",
      prop: "amount",
      width: TableWidth.type,
      align: "center",
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "计划日期",
      prop: "planStartDate",
      sortable: "custom",
      width: TableWidth.dateRanger,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {`${data.row?.planStartDate ? dayjs(data.row?.planStartDate).format(dateFormat) : ""}` +
              ` ～ ` +
              `${data.row?.planStartDate ? dayjs(data.row?.planFinishDate).format(dateFormat) : ""}`}
          </div>
        );
      }
    },
    {
      label: "工单状态",
      prop: "woStatus",
      fixed: "right",
      width: TableWidth.largeType,
      cellRenderer: (data: TableColumnRenderer) => (
        <div>{EnumCell(data, ProductionStateEnum, "productionStateEnum")}</div>
      )
    },
    {
      label: "报工条数",
      prop: "workCount",
      align: "center",
      fixed: "right",
      slot: "workCount",
      width: TableWidth.number
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      width: TableWidth.operations,
      slot: "operation"
    }
  ];

  return { columns };
}
