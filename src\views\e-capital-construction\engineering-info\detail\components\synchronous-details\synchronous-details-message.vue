<template>
  <el-dialog
    v-model="drawerVisible"
    title="报文明细"
    width="800px"
    align-center
    @close="closeDialog"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-row v-if="props.pullData" class="flex flex-row">
      <el-col :span="2" class="label">请求内容</el-col>
      <el-col :span="22" class="value">
        {{ props.pullData }}
      </el-col>
    </el-row>
    <CxEmpty v-else />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";

const drawerVisible = ref(false);
const emits = defineEmits(["close"]);

const props = defineProps<{
  dialogShow: boolean;
  pullData?: string;
}>();

// 订阅弹窗开启状态，请求数据
watch(
  () => props.dialogShow,
  newValue => {
    if (newValue) {
      drawerVisible.value = newValue;
    }
  }
);

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
  emits("close");
}
</script>

<style scoped>
.label {
  width: 300px;
  color: #909399;
}
</style>
