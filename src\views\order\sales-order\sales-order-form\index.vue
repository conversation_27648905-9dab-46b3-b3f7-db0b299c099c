<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="cx-form" label-width="8rem">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="销售订单号" prop="soNo">
          <SerialNumber
            :maxlength="SALES_ORDER_MAX_LENGTH"
            :create="!form.id"
            v-model="form.soNo"
            :code="SALES_ORDER_NO_CODE"
            placeholder="请输入销售订单号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资品类" prop="categoryCode">
          <CategorySelect v-model="form.categoryCode" placeholder="请选择物资品类" clearable :disabled="!!form.id" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="合同编号" prop="conCode">
          <el-input placeholder="请输入合同编号" v-model="form.conCode" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目名称" prop="prjName">
          <el-input placeholder="请输入项目名称" v-model="form.prjName" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="订单进度" prop="orderProgress">
          <EnumSelect
            :enum="SalesOrderStatus"
            enumName="SalesOrderStatus"
            class="w-full"
            v-model="form.orderProgress"
            placeholder="请选择"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="交货日期" prop="deliveryDate">
          <el-date-picker
            class="!w-full"
            v-model="form.deliveryDate"
            type="date"
            placeholder="请选择交货日期"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="采购方公司名称" prop="buyerName">
          <el-input placeholder="请输入采购方公司名称" v-model="form.buyerName" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="标识" prop="matSyncFlagIdList">
          <SynchronousFlagSelect v-model="form.matSyncFlagIdList" placeholder="请选择标识"
        /></el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark" class="!mb-0">
          <el-input type="textarea" placeholder="请输入备注" :rows="2" resize="none" v-model="form.remark" clearable />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <div class="w-full pb-2 px-2 flex justify-between mt-8">
    <TitleBar title="关联采购订单" class="mb-2" />
    <div>
      <el-button @click="select()"><FontIcon class="mr-1.5" icon="icon-a-search2" />选择采购订单号 </el-button>
      <el-button type="primary" @click="add()" :icon="Plus">手动添加</el-button>
    </div>
  </div>
  <PureTable
    class="flex-1 pagination py-1 px-1 h-[225px]"
    row-key="id"
    :columns="columns"
    size="default"
    :data="orderList"
    showOverflowTooltip
  >
    <template #operation="data">
      <ElButton type="danger" link @click="onDelete(data)">移除</ElButton>
    </template>
    <template #poNo="data">
      <span v-if="data.row?.id">{{ data.row.poNo }}</span>
      <div v-else>
        <el-input
          v-model="data.row.poNo"
          placeholder="请输入采购订单号"
          v-if="!data.row?.isExist"
          @input="input(data)"
        />
        <div class="flex flex-col" v-else>
          <el-input v-model="data.row.poNo" class="danger-input" @input="input(data)" />
          <span class="danger-info">采购订单号重复</span>
        </div>
      </div>
    </template>
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
  </PureTable>
  <el-dialog
    v-model="state.selectPurchaseOrderDialogVisible"
    title="选择采购订单号"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <LinkPurchaseOrder
      ref="purchaseOrderRef"
      :select-items="selectItems"
      :category-code="form.categoryCode"
      :channel="curChannel"
    />
    <template #footer>
      <span>
        <el-button @click="onCancelSelectPurchaseOrder()">取消</el-button>
        <el-button type="primary" @click="onConfirmSelectPurchaseOrder()" :disabled="disabledSelected">选中</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, watchEffect, provide } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import LinkPurchaseOrder from "../components/link-purchase-order-detail/index.vue";
import { ICategory, ISalesLinkPurchaseOrder, ISalesOrderDetail } from "@/models";
import { PurchaseChannel, SalesOrderStatus } from "@/enums";
import EnumSelect from "@/components/EnumSelect";
import SerialNumber from "@/components/SerialNumber";
import { SALES_ORDER_NO_CODE } from "@/consts";
import SynchronousFlagSelect from "@/views/components/synchronous-flag";
import CategorySelect from "@/views/components/category-select";
import { useCategoryStore, useSalesOrderManagementStore } from "@/store/modules";
import TitleBar from "@/components/TitleBar";
import { Plus } from "@element-plus/icons-vue";
import { concat, differenceBy, omit } from "lodash-unified";
import { useColumns } from "./columns";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { SALES_ORDER_MAX_LENGTH } from "@/consts";
import { useOrderSaleSyncFlagHook } from "../../hooks/order-sale-sync-flag";
import { SHANG_HAI_IDENTIFIER, GUO_WANG_IDENTIFIER } from "@/consts/buyer-identifier";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";
import { SalesOrderFormCtx, SalesOrderFormToken } from "../ctx-token";

provide<SalesOrderFormCtx>(SalesOrderFormToken, {
  toggleSelectedDisabled
});

defineExpose({
  getValidValue,
  validate,
  toggleSelectedDisabled
});
const state = reactive<{
  selectPurchaseOrderDialogVisible: boolean;
}>({
  selectPurchaseOrderDialogVisible: false
});

const formRef = ref<FormInstance>();
const orderList = ref<Array<ISalesLinkPurchaseOrder & { isExist?: boolean }>>([]);
const form = reactive<Partial<ISalesOrderDetail>>({
  orderProgress: SalesOrderStatus.InProduction,
  matSyncFlagIdList: []
});
const rules = reactive<FormRules>({
  soNo: [{ required: true, message: requiredMessage("销售订单号"), trigger: "change" }],
  categoryCode: [{ required: true, message: requiredMessage("物资品类"), trigger: "change" }],
  prjName: [{ required: true, message: requiredMessage("项目名称"), trigger: "change" }],
  orderProgress: [{ required: true, message: requiredMessage("订单进度"), trigger: "change" }],
  buyerName: [{ required: true, message: requiredMessage("采购方公司名称"), trigger: "change" }]
});

const purchaseOrderRef = ref<InstanceType<typeof LinkPurchaseOrder>>();
const selectItems = computed(() => {
  const list = orderList.value.filter(order => !!order.id);
  return list;
});
const categoryStore = useCategoryStore();
const salesOrderManagementStore = useSalesOrderManagementStore();
const { columns } = useColumns();
const { licenseOnlyIOT, licenseOnlyEIP } = useOrderSaleSyncFlagHook();
const disabledSelected = ref(true);

const curChannel = computed(() => {
  const item = orderList.value.find(({ syncChannel }) => typeof syncChannel === "number");
  if (item) {
    return item.syncChannel;
  }
  return PurchaseChannel.EIP;
});

function toggleSelectedDisabled(disabled: boolean) {
  disabledSelected.value = disabled;
}

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

const onCancelSelectPurchaseOrder = () => {
  state.selectPurchaseOrderDialogVisible = false;
};

const onConfirmSelectPurchaseOrder = () => {
  state.selectPurchaseOrderDialogVisible = false;
  const channel = purchaseOrderRef.value.getChannel();
  if (channel === PurchaseChannel.CSG_GuangZhou) {
    form.matSyncFlagIdList = [SynchronousFlagEnum.CSG_GuangZhou_Flag];
  } else {
    form.matSyncFlagIdList = form.matSyncFlagIdList.filter(flag => flag !== SynchronousFlagEnum.CSG_GuangZhou_Flag);
  }
  const poNos: Array<ISalesLinkPurchaseOrder> = purchaseOrderRef.value.getSalesLinkPurchaseOrdersPoNo();
  const noIds: Array<ISalesLinkPurchaseOrder> = orderList.value.filter(order => !order.id);
  orderList.value = concat(differenceBy(noIds, poNos, "poNo"), poNos);
  toggleSelectedDisabled(true);
};

async function getValidValue(): Promise<ISalesOrderDetail> {
  if (!(await validate()) || orderList.value.some(order => order.isExist)) {
    return Promise.reject("invalid");
  }
  form.poNo = orderList.value.filter(value => !!value.poNo).map(value => value.poNo) || [];
  return omit(form, ["matSyncFlagId", "linkStep", "synType"]) as ISalesOrderDetail;
}

async function select() {
  if (!form.categoryCode) {
    await formRef.value.validateField(["categoryCode"]);
    return;
  }
  state.selectPurchaseOrderDialogVisible = true;
}

function add() {
  orderList.value.unshift({
    poNo: ""
  });
}

/**
 * @description: 检查关联采购订单是否包含XX标识符
 * @return {*}
 */
function checkLinkedPurchaseOrderIncludeIdentifier(name: string) {
  return orderList.value.some(({ buyerName }) => new RegExp(name).test(buyerName));
}

watch(
  [() => form.buyerName, licenseOnlyEIP, licenseOnlyIOT, orderList],
  ([name, onlyEIP, onlyIOT], [, oldOnlyEIP, oldOnlyIOT]) => {
    // 如果当前是编辑，并且license还未读取成功，直接返回，防止默认赋值
    if (salesOrderManagementStore.salesOrderDetail.id && (oldOnlyEIP === undefined || oldOnlyIOT === undefined)) {
      return;
    }
    if (onlyIOT) {
      // 如果只有IOT授权，自动添加 上海标识
      form.matSyncFlagIdList = Array.from(new Set(form.matSyncFlagIdList.concat(SHANG_HAI_IDENTIFIER.id)));
    } else if (onlyEIP) {
      // 如果只有EIP授权，自动添加国网标识
      form.matSyncFlagIdList = Array.from(new Set(form.matSyncFlagIdList.concat(GUO_WANG_IDENTIFIER.id)));
    } else {
      // 如果含有IOT和EIP授权，并且采购方是上海公司或者关联了上海的采购订单，自动添加上海标识符
      if (
        new RegExp(SHANG_HAI_IDENTIFIER.name).test(name) ||
        checkLinkedPurchaseOrderIncludeIdentifier(SHANG_HAI_IDENTIFIER.name)
      ) {
        form.matSyncFlagIdList = Array.from(new Set(form.matSyncFlagIdList.concat(SHANG_HAI_IDENTIFIER.id)));
      }
    }
  },
  {
    deep: true
  }
);

watch(
  () => form.categoryCode,
  (newValue, oldValue) => {
    const categoryOpt: ICategory = categoryStore.categories.find(
      categories => categories.categoryCode == form.categoryCode
    );
    if (oldValue) {
      orderList.value.fill({});
    }
    if (categoryOpt) {
      form.categoryName = categoryOpt.categoryName;
    }
  }
);

watchEffect(() => {
  if (salesOrderManagementStore.salesOrderDetail) {
    Object.assign(form, salesOrderManagementStore.salesOrderDetail);
    orderList.value = salesOrderManagementStore.salesOrderDetail.purchaseList || [];
    const matSyncFlagId: string = salesOrderManagementStore.salesOrderDetail.matSyncFlagId;
    form.matSyncFlagIdList = matSyncFlagId ? salesOrderManagementStore.salesOrderDetail.matSyncFlagId.split(",") : [];
  }
});

watch(selectItems, list => {
  if (!list.length) {
    form.matSyncFlagIdList = [];
  }
});

function onDelete(data) {
  orderList.value.splice(data.index, 1);
  orderList.value.forEach(data => {
    if (!data.id) {
      data.isExist = orderList.value.some(order => order.poNo == data.poNo && !!order.id);
    }
  });
}

function input(data) {
  data.row.isExist = orderList.value.some((order, index) => order.poNo == data.row.poNo && data.index != index);
}
</script>

<style scoped lang="scss">
.danger-info {
  color: var(--el-color-danger);
  font-size: 0.75rem;
}

:deep(.danger-input .el-input__wrapper) {
  box-shadow: 0 0 0 0.0625rem var(--el-color-danger) inset;
}
</style>
