<template>
  <div class="detail-raw-material-inspect-dialog">
    <NormalDetailRawMaterialInspect v-if="isNormal" @detailCloseDiag="detailCloseDiagEvent" />
    <ACDetailRawMaterialInspect v-else @detailCloseDiag="detailCloseDiagEvent" />
  </div>
</template>

<script setup lang="ts">
import NormalDetailRawMaterialInspect from "../../raw-material-group-unit-check/normal/detai-raw-material/index.vue";
import ACDetailRawMaterialInspect from "../../raw-material-group-unit-check/armour-clamp/detai-raw-material/index.vue";
import { inject, ref, onMounted } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/production-test-sensing";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

// 定义抛出的事件
const emits = defineEmits<{
  (event: "detailCloseDiag"): void;
}>();

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const isNormal = ref<boolean>(true);

onMounted(() => {
  isNormal.value = prodCtx?.detailMaterialCategory === EMaterialCategory.Normal;
});

const detailCloseDiagEvent = () => {
  emits("detailCloseDiag");
};
</script>

<style scoped lang="scss"></style>
