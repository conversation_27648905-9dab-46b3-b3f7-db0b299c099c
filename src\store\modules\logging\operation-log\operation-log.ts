import { getPointConfigModuleApi, getPointerConfigPageApi } from "@/api/logging/operate-log";
import { defineStore } from "pinia";

export const useOperationLogStore = defineStore({
  id: "operation-log-store",
  state: () => ({
    configModule: [],
    configPageFrame: []
  }),
  actions: {
    /** 获取模块 和 页面信息 */
    async getPointConfigModuleAndPage() {
      const options = await Promise.all([getPointConfigModuleApi(), getPointerConfigPageApi()]);
      if (Array.isArray(options) && options?.length) {
        const [modules, pages] = options;
        this.configModule = modules.data || [];
        this.configPageFrame = pages.data || [];
      }
    }
  }
});
