<template>
  <el-dialog
    title="触发评分确认"
    v-model="ctx.visible"
    destroy-on-close
    fullscreen
    :close-on-press-escape="false"
    class="sync-audit-full-screen"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" class="flex justify-between">
        <div class="flex gap-3 items-center">
          <el-button link @click="close">
            <el-icon size="20"><Back /></el-icon>
          </el-button>
          <div :class="titleClass">触发评分确认</div>
        </div>
        <el-button type="danger" @click="close">
          <el-icon size="20"><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <SyncAudit :base-info="baseInfo" :iot-audit-items="IOT_STATE_GRID_CARD_IDS" allow-filter-by-order-no />
    <template #footer>
      <el-button @click="ctx.visible = false">取消</el-button>
      <el-button type="primary" @click="triggerScoreCheck" :loading="loading">确认触发</el-button>
    </template>
  </el-dialog>
  <TriggerScoreCheckDialog v-model="checkVisible" :checkText="checkText" @trigger-score="confirmTriggerScore" />
</template>

<script setup lang="ts">
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import TriggerScoreCheckDialog from "@/views/components/state-grid-trigger-score/trigger-score-check-dialog.vue";
import { computed, inject, ref } from "vue";
import { ElNotification } from "element-plus";
import { useStateGriRateDetailStore } from "@/store/modules/state-grid-rate";
import { syncAuditKey } from "@/views/order/purchase-order/detail/src/sg-rate/tokens";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";
import { Close, Back } from "@element-plus/icons-vue";

const detailStore = useStateGriRateDetailStore();
const ctx = inject(syncAuditKey);
const loading = ref(false);
const triggerScoreCheck = useLoadingFn(confirmTrigger, loading);
const handleSyncAll = useLoadingFn(syncAll, loading);

const baseInfo = computed(() => ctx.baseInfo);
const checkVisible = ref<boolean>(false);
const checkText = ref();

/** 触发评分 */
async function confirmTrigger() {
  const { purchaseLineId } = ctx.score;
  const checkRes = await detailStore.allowTriggerStateGridScore(purchaseLineId);
  const { verify, message } = checkRes;
  if (!verify) {
    checkText.value = message;
    checkVisible.value = true;
    return;
  }
  await confirmTriggerScore();
}

async function confirmTriggerScore() {
  await handleSyncAll();
}

async function syncAll() {
  const { poItemNo } = ctx.score;
  await detailStore.triggerScore(ctx.score);
  ElNotification.info({
    title: "触发评分",
    message: `正在触发采购订单行项目【${poItemNo}】下生产数据的质量评分`,
    duration: 3000
  });
  checkVisible.value = false;
  ctx.visible = false;
}
</script>

<style lang="scss">
@import "@/views/order/purchase-order/detail/styles/mixin";

.sync-audit-full-screen {
  @include full-screen-dialog;
}
</style>
