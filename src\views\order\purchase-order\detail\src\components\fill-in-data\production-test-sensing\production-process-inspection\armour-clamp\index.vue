<template>
  <div class="flex-1 flex flex-col">
    <div class="flex justify-between mb-2">
      <div class="flex-1">
        <SwitchTab :switch-tags="prodProcessInspecStore.switchTabList" @switch-change="switchTag($event)" />
      </div>
      <div class="select-material">
        <el-button
          v-auth="PermissionKey.form.formPurchaseProcessCreate"
          type="primary"
          v-if="isCanOperateAddAndEdit"
          @click="addAcProductionInspection()"
        >
          <FontIcon class="mr-2" icon="icon-plus" />
          新增过程检
        </el-button>
      </div>
    </div>
    <div class="min-h-[298px]">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="prodProcessInspecStore.prodProcessInspectList"
        :columns="columns"
        :height="300"
        showOverflowTooltip
        :pagination="pagination"
        :loading="loading"
        @page-current-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #operation="{ row }">
          <div>
            <ElButton
              v-auth="PermissionKey.form.formPurchaseFinishProductEdit"
              type="primary"
              link
              v-if="isCanOperateAddAndEdit"
              @click="editArmourClampInspect(row)"
            >
              编辑
            </ElButton>
            <ElButton
              v-auth="PermissionKey.form.formPurchaseFinishProductDelete"
              link
              type="danger"
              @click="deleteArmourClampInspect(row)"
            >
              删除
            </ElButton>
          </div>
        </template>
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
      </PureTable>
    </div>

    <!-- 新增生产过程工艺 -->
    <el-dialog
      v-model="addProcessInspecRef"
      :title="dialogTitle"
      class="middle"
      align-center
      :destroy-on-close="true"
      @close="cancelChange"
    >
      <div class="cx-form">
        <AddAcProcessInspection ref="addProcessInspecIns" :isAddOrEdit="isAddOrEdit" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelChange">取消</el-button>
          <el-button type="primary" @click="saveProcessInspection">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import SwitchTab from "../../component/switch-tab/index.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import AddAcProcessInspection from "./add-Armour-clamp-inspect/index.vue";
import { useArmourClampProdProcessInspectStore } from "@/store/modules/production-test-sensing";
import { PermissionKey } from "@/consts";
import { ref, computed, onMounted, watchEffect } from "vue";
import { ISwitchTagEventType } from "../../component/switch-tab/types";
import { useColumns } from "./columns";
import { ElMessage } from "element-plus";
import { useProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { IProductionProcessList } from "@/models";
import { useConfirm } from "@/utils/useConfirm";
import { watchOnce } from "@vueuse/core";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ISearchProductionProcessList } from "@/models/production-test-sensing";

// 获取生产过程工艺检测
const prodProcessInspecStore = useArmourClampProdProcessInspectStore();
const productionTestSensingStore = useProductionTestSensingStore();
const addProcessInspecRef = ref<boolean>(false);
const addProcessInspecIns = ref();
const dialogTitle = ref("新增过程检测");
const isAddOrEdit = ref<boolean>(true);
const currentTagInfo = ref();
const { columns } = useColumns();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const processInfoList = computed(() => prodProcessInspecStore.switchTabList);
/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => prodProcessInspecStore.getIsCanOperateAddAndEdit);

const loading = ref<boolean>(false);
const initProdProcessInspect = useLoadingFn(getProdProcessInspecProcessAction, loading);
const defaultPageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

watchEffect(() => {
  pagination.total = prodProcessInspecStore.inspectListTotal;
});

onMounted(() => {
  initProdProcessInspect(defaultPageInfo);
});

watchOnce(processInfoList, newVal => {
  if (newVal) {
    currentTagInfo.value = newVal[0];
    prodProcessInspecStore.currentTagInfo = currentTagInfo.value;
  }
});

async function getProdProcessInspecProcessAction(parmaData: ISearchProductionProcessList) {
  await prodProcessInspecStore.getProdProcessInspecProcessAction(parmaData);
}

/**
 * 切换页码
 */
const pageChange = (pageNo: number) => {
  const { processCode } = currentTagInfo.value;
  initProdProcessInspect({ processCode, pageNo, pageSize: pagination.pageSize });
};

/**
 * 切换页码数量
 */
const pageSizeChange = (pageSize: number) => {
  const { processCode } = currentTagInfo.value;
  initProdProcessInspect({ processCode, pageNo: 1, pageSize });
};

/**
 * 切换工序
 */
const switchTag = (tag: ISwitchTagEventType) => {
  currentTagInfo.value = tag.data;
  // 保存当前选择的原材料工序
  prodProcessInspecStore.currentTagInfo = currentTagInfo.value;
  const { processCode } = tag.data;
  initProdProcessInspect({ processCode, ...defaultPageInfo });
};

/**
 * 新增金具生产过程工艺检测
 */
const addAcProductionInspection = () => {
  dialogTitle.value = "新增过程检测";
  addProcessInspecRef.value = true;
  isAddOrEdit.value = true;
};

/**
 * 关闭弹框
 */
const cancelChange = () => {
  addProcessInspecRef.value = false;
};

/**
 * 保存过程检测
 */
const saveProcessInspection = async () => {
  const saveFormData = await addProcessInspecIns.value?.getProcessInspectForm();
  if (saveFormData?.processCode) {
    await prodProcessInspecStore.saveAcProductionProcessCheck(saveFormData);
    saveSuccess();
  }
};
/**
 * 保存过程检测成功
 */
const saveSuccess = () => {
  ElMessage.success(isAddOrEdit.value ? "新增成功" : "编辑成功");
  cancelChange();
  refreshProcessCheckList();
  if (!isAddOrEdit.value) {
    prodProcessInspecStore.initAcDetailOfProcessInspect();
  }
};

/**
 * 编辑过程检测
 */
const editArmourClampInspect = async (row: IProductionProcessList) => {
  isAddOrEdit.value = false;
  addProcessInspecRef.value = true;
  prodProcessInspecStore.getAcDetailOfProdProcessInfo(row?.id);
};

/**
 * 删除过程检测
 */
const deleteArmourClampInspect = async (row: IProductionProcessList) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) return;
  await prodProcessInspecStore.delAcProductionProcessCheck(row?.id);
  ElMessage.success("删除成功");
  refreshProcessCheckList();
};

// 刷新列表数据
const refreshProcessCheckList = async () => {
  const { processCode } = currentTagInfo.value;
  initProdProcessInspect({ processCode, ...defaultPageInfo });
  await productionTestSensingStore.refreshProductionProcessStatus();
};
</script>

<style scoped lang="scss"></style>
