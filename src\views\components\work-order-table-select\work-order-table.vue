<template>
  <pure-table
    ref="tableInstance"
    show-overflow-tooltip
    row-key="id"
    :height="300"
    :columns="columns"
    :data="ctx.data"
    :loading="ctx.loading"
    :pagination="ctx.pagination"
    @row-click="handleRowClick"
    @page-size-change="ctx.refresh"
    @page-current-change="ctx.refresh"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
</template>
<script setup lang="ts">
import { inject, toRef } from "vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty";
import { workOrderSelectKey } from "@/views/components/work-order-table-select/token";
import { ProductionStateEnum, TableWidth, VoltageClassesEnumMapDesc } from "@/enums";
import { formatDate } from "@/utils/format";
import { dateFormat } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { IWorkOrder } from "@/models";
const { mapFormatter, dateFormatter, enumFormatter, singleSelectFormatter } = useTableCellFormatter();

const ctx = inject(workOrderSelectKey);

const columns: Array<TableColumns> = [
  {
    label: "",
    prop: "id",
    width: 40,
    fixed: "left",
    align: "center",
    formatter: singleSelectFormatter(toRef(ctx, "selectedId"), { validateEvent: false })
  },
  {
    label: "工单编号",
    prop: "woNo",
    width: TableWidth.largeOrder,
    fixed: "left"
  },
  {
    label: "生产订单号",
    prop: "ipoNo",
    width: TableWidth.order
  },
  {
    label: "销售订单行项目",
    prop: "soItemNo",
    width: TableWidth.order
  },
  {
    label: "物料名称",
    prop: "materialName",
    width: TableWidth.name
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    width: TableWidth.number,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "需求数量",
    prop: "amount",
    width: TableWidth.number,
    formatter: row => {
      const { amount, unitDictionary } = row;
      if (!amount) {
        return null;
      }
      return unitDictionary ? `${amount} ${unitDictionary?.name}` : amount;
    }
  },
  {
    label: "计划日期",
    prop: "planStartDate",
    width: TableWidth.dateRanger,
    formatter: row => {
      const { planStartDate, planFinishDate } = row;
      if (!planStartDate && !planFinishDate) {
        return null;
      }
      return `${formatDate(planStartDate) || ""} ～ ${formatDate(planFinishDate) || ""}`;
    }
  },
  {
    label: "工单状态",
    prop: "woStatus",
    width: TableWidth.largeType,
    formatter: enumFormatter(ProductionStateEnum, "productionStateEnum")
  },
  {
    label: "实物Id",
    prop: "entityId"
  },
  {
    label: "实际开始日期",
    prop: "actualStartDate",
    width: TableWidth.date,
    className: "actual-start-date",
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "实际结束日期",
    prop: "actualFinishDate",
    width: TableWidth.date,
    className: "actual-finish-date",
    formatter: dateFormatter(dateFormat)
  }
];

function handleRowClick(workOrder: IWorkOrder) {
  ctx.selectedId = workOrder.id;
  ctx.selectedWorkOrder = workOrder;
}
</script>
