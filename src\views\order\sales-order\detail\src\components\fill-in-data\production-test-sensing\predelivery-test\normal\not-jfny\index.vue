<template>
  <div class="air-tightness">
    <!-- 试验信息 -->
    <AirTightnessList
      v-if="activeTab === 'card'"
      v-loading="loading"
      :subClassCode="subClassCode"
      :isCable="salesOrderDetailStore.isCable"
      @editQMXExperiment="onEditQMXExperiment"
      @copyQMXExperiment="onCopyQMXExperiment"
      @deleteQMXExperimentSuccess="onDeleteQMXExperimentSuccess"
      @loadMoreEvent="loadMore"
      @uploadWaveData="onUploadWaveData"
    />

    <ExperimentList
      class="h-96"
      v-if="activeTab === 'list'"
      :editExperiment="onEditQMXExperiment"
      :deleteExperimentSuccess="onDeleteQMXExperimentSuccess"
      :copyExperiment="onCopyQMXExperiment"
      :onUploadWaveData="onUploadWaveData"
    />

    <!-- 添加试验信息 -->
    <el-dialog
      v-model="addAirTightnessRef"
      :title="diagTitle"
      class="middle"
      align-center
      destroy-on-close
      draggable
      :close-on-click-modal="false"
      @closed="onCancelQMXExperiment"
    >
      <AddAirTightnessExperiment
        ref="qmxExperimentInstFormInst"
        :subClassCode="subClassCode"
        :isCable="salesOrderDetailStore.isCable"
        :mode="mode"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addAirTightnessRef = false">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="handleOnSaveQMXExperiment()">保存</el-button>
          <el-button
            v-if="mode === 'add'"
            type="primary"
            :loading="saveLoading"
            @click="handleOnSaveQMXExperimentAndAdd()"
          >
            保存并新增
          </el-button>
        </span>
      </template>
    </el-dialog>
    <UploadWave
      v-model="uploadWaveDialogVisible"
      :id="qutGoingFactoryQmxExperiment?.id"
      @uploadSuccess="handleUploadSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import AirTightnessList from "./air-tightness-list/index.vue";
import AddAirTightnessExperiment from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/predelivery-test/normal/not-jfny/add-air-tightness/index.vue";
import { onMounted, ref, computed, onUnmounted, watchEffect } from "vue";
import {
  useEXFactoryExperimentStore,
  useEXFactoryQMXStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";
import {
  IOutGoingFactoryQMXExperimentForm,
  IOutGoingFactoryQmxExperiment,
  IOutGoingFactoryNotJfnyReq,
  IProductOrder
} from "@/models";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { EExFactoryTest } from "../../../component/switch-tab/types";
import { EControlType } from "@/enums/dynamic-form.enum";
import ExperimentList from "./experiment-list/index.vue";
import { emitter } from "@/utils/mitt";
import UploadWave from "@/views/components/upload-wave/index.vue";

const props = defineProps<{
  activeTab: "card" | "list";
}>();

const emits = defineEmits<{
  (event: "delSuccess", component: string): void;
  (event: "addDataSuccess", component: string): void;
}>();

const qmxExperimentInstFormInst = ref<InstanceType<typeof AddAirTightnessExperiment>>();
const saveLoading = ref<boolean>();
const uploadWaveDialogVisible = ref(false);
const handleOnSaveQMXExperiment = useLoadingFn(async () => await onSaveQMXExperiment("save"), saveLoading);
const handleOnSaveQMXExperimentAndAdd = useLoadingFn(async () => await onSaveQMXExperiment("saveAndAdd"), saveLoading);
const exFactoryQMXStore = useEXFactoryQMXStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const fillInDataStore = useSalesFillInDataStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const addAirTightnessRef = ref(false);
const mode = ref<"add" | "edit" | "copy">("add");
const qutGoingFactoryQmxExperiment = ref<IOutGoingFactoryQmxExperiment>();
const diagTitle = ref("新增试验");
const defaultPageInfo = {
  pageNo: 1,
  pageSize: 50
};

const subClassCode = computed(() => fillInDataStore.data.subClassCode || fillInDataStore.data.subclassCode);

const totalPage = computed(() => {
  return Math.ceil(exFactoryQMXStore.experimentListTotal / defaultPageInfo.pageSize);
});
const loading = ref<boolean>(false);
const initOutGoingFactoryQMXExperimentList = useLoadingFn(queryOutGoingFactoryQMXExperimentList, loading);

onMounted(() => {
  watchEffect(() => {
    exFactoryQMXStore.initExperimentList();
    defaultPageInfo.pageNo = 1;
    initOutGoingFactoryQMXExperimentList(defaultPageInfo);
  });
});

async function queryOutGoingFactoryQMXExperimentList(params: IOutGoingFactoryNotJfnyReq) {
  await exFactoryQMXStore.queryOutGoingFactoryQMXExperimentList(params, true);
}

// 新增气密性试验信息 / 其他非局放耐压试验
const addAirTightness = () => {
  mode.value = "add";
  addAirTightnessRef.value = true;
  diagTitle.value = "新增试验";
  exFactoryQMXStore.isEditTightness = false;
  exFactoryExperimentStore.getMetadataModelInfo(
    exFactoryExperimentStore.activeProcess.processId,
    (fillInDataStore.data as IProductOrder).specificationModel || fillInDataStore.data.specificationType
  );
};

// 编辑气密性试验 / 其他非局放耐压试验
const onEditQMXExperiment = (data: IOutGoingFactoryQmxExperiment) => {
  mode.value = "edit";
  addAirTightnessRef.value = true;
  diagTitle.value = "编辑试验";
  exFactoryQMXStore.isEditTightness = true;
  exFactoryQMXStore.getOutGoingFactoryQMXExperimentDetailById(data.id);
  exFactoryExperimentStore.setMetadataModelInfo(data.rawMetadataValue);
};

// 复制气密性试验 / 其他非局放耐压试验
const onCopyQMXExperiment = (data: IOutGoingFactoryQmxExperiment) => {
  mode.value = "copy";
  addAirTightnessRef.value = true;
  diagTitle.value = "复制试验";
  exFactoryQMXStore.isEditTightness = true;
  exFactoryQMXStore.getOutGoingFactoryQMXExperimentDetailById(data.id);
  exFactoryExperimentStore.setMetadataModelInfo(data.rawMetadataValue);
};

/**
 * 上传波形数据
 */
const onUploadWaveData = (data: IOutGoingFactoryQmxExperiment) => {
  uploadWaveDialogVisible.value = true;
  qutGoingFactoryQmxExperiment.value = data;
};

const onDeleteQMXExperimentSuccess = () => {
  emits("delSuccess", EExFactoryTest.QMXSY);
  exFactoryQMXStore.initExperimentList();
  defaultPageInfo.pageNo = 1;
  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
};

// 关闭弹框
const onCancelQMXExperiment = () => {
  addAirTightnessRef.value = false;
  exFactoryQMXStore.setOutGoingFactoryQmxExperimentDetail();
  exFactoryExperimentStore.setMetadataModelInfo();
};

/**
 * @description: 删除提交数据中的波形图数据
 */
function deleteWaveRoseDataForSubmitData(form: IOutGoingFactoryQMXExperimentForm) {
  const configArray = form.rawMetadataValue;
  if (configArray && configArray.length) {
    const waveRoseConfig = configArray.find(({ identityCode }) => identityCode === EControlType.WaveRoseControl);
    if (waveRoseConfig) {
      waveRoseConfig.dataValue = "";
    }
  }
}

/** 保存气密性试验 / 其他非局放耐压试验 */
async function onSaveQMXExperiment(type: "save" | "saveAndAdd") {
  if (!qmxExperimentInstFormInst.value) {
    return;
  }

  const experimentBaseInfo = await qmxExperimentInstFormInst.value.getQMXExperimentBaseFormValue();
  if (mode.value === "copy") {
    experimentBaseInfo.id = undefined;
  }
  const qmxAirTightnessExpersValue = await qmxExperimentInstFormInst.value.getQMXAirTightnessExpersFormValue();
  /*** 表单数据验证不过 */
  if (
    (typeof experimentBaseInfo === "boolean" && !experimentBaseInfo) ||
    !Array.isArray(qmxAirTightnessExpersValue.submitData) ||
    qmxAirTightnessExpersValue.submitData.length === 0
  ) {
    return;
  }

  const { processCode, processName, processId } = exFactoryExperimentStore.activeProcess;
  const dataId = fillInDataStore.dataId;
  const { isCable } = salesOrderDetailStore;
  const orderParam: { productionId?: string; workOrderId?: string } = {};
  isCable && (orderParam.productionId = dataId);
  !isCable && (orderParam.workOrderId = dataId);
  const factoryQMXExperimentForm: IOutGoingFactoryQMXExperimentForm = {
    ...(experimentBaseInfo as IOutGoingFactoryQMXExperimentForm),
    ...exFactoryExperimentStore.activeProcess,
    rawMetadataValue: qmxAirTightnessExpersValue.submitData.map(item => ({
      dataCode: item.targetCode,
      identityCode: item.dataTypeIdentityDetail.identityCode,
      dataValue: item.targetValue
    })),
    ...orderParam,
    processCode,
    processName,
    processId
  };

  let res = null;
  if (!factoryQMXExperimentForm.id) {
    await exFactoryQMXStore.createOutGoingFactoryQMXExperiment(factoryQMXExperimentForm);
    exFactoryQMXStore.initExperimentList();
  } else {
    // 由于波形图数据过大，应后端要求，删除波形图数据
    deleteWaveRoseDataForSubmitData(factoryQMXExperimentForm);
    res = await exFactoryQMXStore.editOutGoingFactoryQMXExperiment(factoryQMXExperimentForm);
  }
  const obj = {
    add: "新增成功",
    edit: "编辑成功",
    copy: "复制成功"
  };
  ElMessage.success(obj[mode.value]);
  // 如果当前处于列表模式，则刷新列表
  if (props.activeTab === "list") {
    emitter.emit("refreshOutGoingFactoryExperimentList");
  }

  if (type === "saveAndAdd") {
    qmxExperimentInstFormInst.value?.refreshNo();
  } else {
    addAirTightnessRef.value = false;
  }

  exFactoryQMXStore.setOutGoingFactoryQmxExperimentDetail();

  if (type === "save") {
    exFactoryExperimentStore.setMetadataModelInfo();
  }
  // 编辑时不全量刷新
  if (!factoryQMXExperimentForm?.id) {
    defaultPageInfo.pageNo = 1;
    initOutGoingFactoryQMXExperimentList(defaultPageInfo);
  } else {
    // 更新单个试验
    if (res && res.data?.id) {
      exFactoryQMXStore.updateDetailInfoById(res.data);
    }
  }

  if (!factoryQMXExperimentForm?.id) emits("addDataSuccess", EExFactoryTest.QMXSY);
  productionTestSensingStore.refreshProductionProcessStatus();
}

/**
 * 加载更多
 */
const loadMore = () => {
  if (totalPage.value <= 1) {
    return;
  }
  defaultPageInfo.pageNo++;
  if (defaultPageInfo.pageNo > totalPage.value) {
    return;
  }
  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
};

function refresh() {
  exFactoryQMXStore.initExperimentList();
  defaultPageInfo.pageNo = 1;
  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
}

const handleUploadSuccess = () => {
  emitter.emit("refreshOutGoingFactoryExperimentList");
  exFactoryQMXStore.initExperimentList();
  defaultPageInfo.pageNo = 1;
  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
};

onUnmounted(() => {
  exFactoryQMXStore.initExperimentList();
});

defineExpose({
  addAirTightness,
  refresh
});
</script>

<style scoped lang="scss">
.air-tightness {
  .add-air-tightness {
    text-align: right;
    margin-bottom: 20px;
  }
}

.process-inspect-content {
  min-height: 652px;
  max-height: 672px;
}
</style>
