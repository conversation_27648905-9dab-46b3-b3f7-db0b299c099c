<template>
  <div class="flex h-24">
    <div
      v-for="step in steps"
      :key="step.title"
      class="step"
      :class="getStepClass(step)"
      @click="toggleActiveStep(step)"
    >
      <el-badge :value="step.errorCount" :hidden="!step.errorCount">
        <div class="w-10 h-10">
          <div class="icon flex-c text-sm">
            <SyncStepIcon :type="step.type" />
          </div>
        </div>
      </el-badge>
      <div class="whitespace-nowrap text-primaryText text-base text-bold max-w-[5em] lg:max-w-none">
        <ShowTooltip class-name="w-full text-center" :content="step.title" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSalesOrderSyncInfo } from "@/store/modules";
import { computed } from "vue";
import { formatSyncSteps, ISyncStep } from "@/views/order/sales-order/detail/src/sg-order/sync-step-tool";
import SyncStepIcon from "@/views/order/sales-order/detail/src/sg-order/components/sync-step-icon.vue";
import ShowTooltip from "@/components/ShowTooltip";
import { PurchaseChannel, StateGridOrderSyncStep } from "@/enums";

const store = useSalesOrderSyncInfo();
const steps = computed(() => {
  const rawSteps = store.sync?.detail;
  if (store.channel === PurchaseChannel.CSG_GuangZhou) {
    const filterSteps = rawSteps.filter(
      ({ key }) => ![StateGridOrderSyncStep.TECHNICAL_STANDARD, StateGridOrderSyncStep.ATTACHED_REPORT].includes(key)
    );
    return formatSyncSteps(filterSteps);
  }
  return formatSyncSteps(rawSteps);
});

function getStepClass(step: ISyncStep): string {
  const active = store.activeStepKey === step.key;
  return active ? `${step.type} active` : step.type;
}

function toggleActiveStep(step: ISyncStep) {
  store.toggleActiveStep(step.key);
}
</script>

<style scoped lang="scss">
.icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.success .icon {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
  border: 1px solid var(--el-color-success-light-8);
}

.fail .icon {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  border: 1px solid var(--el-color-danger-light-8);
}

.info,
.syncing {
  .icon {
    color: var(--disabled-text-color);
    background-color: var(--el-color-info-light-9);
    border: 1px solid var(--el-color-info-light-8);
  }

  .text-base {
    color: var(--disabled-text-color);
  }
}

.step {
  @apply flex-1 flex-c flex-col h-24 cursor-pointer relative;
}

.step + .step {
  border-left: 2px solid #f4f8f9;
}

.active,
.step:hover {
  background: linear-gradient(180deg, #feffff 0%, var(--el-color-primary-light-9) 100%);
}

.active.fail,
.step.fail:hover {
  background: linear-gradient(180deg, #feffff 0%, var(--el-color-danger-light-9) 100%);
}

.step:after {
  width: 0;
  content: "";
  height: 1px;
  background-color: var(--el-color-primary);
  position: absolute;
  bottom: 0;
  transition: width 0.2s;
}

.step.fail:after {
  background-color: var(--el-color-danger);
}

.active:after {
  width: 100%;
}
</style>
