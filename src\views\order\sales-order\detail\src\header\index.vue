<template>
  <div class="flex-bc py-2">
    <div class="flex items-center flex-1 overflow-hidden mr-2">
      <div>
        <div class="label">采购订单号</div>
        <div class="with-default">
          <PurchaseOrderNo :orderNoes="poNo" />
        </div>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">物资品类</div>
        <span class="with-default">
          <CxTag type="custom" icon="icon-application-fill" v-if="categoryName">{{ categoryName }}</CxTag>
        </span>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">项目名称</div>
        <div class="flex-bc text-middle">
          <FontIcon icon="icon-pm-fill" class="!text-primary_light_5" />
          <ShowTooltip
            className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl"
            :content="props.salesOrderDetail?.prjName"
          />
        </div>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">标识</div>
        <div class="flex-bc text-middle">
          <FontIcon icon="icon-biaoshi" class="!text-primary_light_5" />
          <span class="with-default">{{ flagNames }}</span>
        </div>
      </div>
    </div>

    <el-tooltip
      class="box-item"
      v-if="allowArchive"
      effect="dark"
      content="归档后的数据将不会自动同步和触发评分"
      placement="top-start"
    >
      <el-button
        v-auth="PermissionKey.form.formPurchaseSalesArchived"
        size="large"
        type="primary"
        :icon="MessageBox"
        @click="archive"
        >归档</el-button
      >
    </el-tooltip>

    <el-tooltip
      v-else
      class="box-item"
      effect="dark"
      content="归档后，关联数据不会自动同步，默认列表隐藏，同步问题需手动处理。"
      placement="top-start"
    >
      <el-button v-auth="PermissionKey.form.formPurchaseSalesArchived" size="large" @click="unarchive"
        >取消归档</el-button
      >
    </el-tooltip>
  </div>
  <FilingDialog
    v-model="filingDialogVisible"
    :id="props.salesOrderDetail?.id"
    @fillingSuccess="handleArchiveSuccess()"
  />
</template>

<script setup lang="ts">
import { ISalesOrder } from "@/models";
import { computed, ref } from "vue";
import { useSalesOrderDetailStore, useSalesOrderManagementStore, useSynchronousFlagStore } from "@/store/modules";
import FilingDialog from "../components/filing-dialog/filing-dialog.vue";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";
import { MessageBox } from "@element-plus/icons-vue";
import CxTag from "@/components/CxTag/index.vue";
import ShowTooltip from "@/components/ShowTooltip";
import { computedAsync } from "@vueuse/core";
import PurchaseOrderNo from "./purchase-order-no.vue";
import { PermissionKey } from "@/consts";

const synchronousFlagStore = useSynchronousFlagStore();
const salesOrderManagementStore = useSalesOrderManagementStore();
const salesOrderDetailStore = useSalesOrderDetailStore();

const props = defineProps<{
  salesOrderDetail: ISalesOrder;
}>();

const filingDialogVisible = ref(false);

const poNo = computed(() => props.salesOrderDetail?.poNo);
const categoryName = computed(() => props.salesOrderDetail?.categoryName);
const flagIds = computed(() => {
  const matSyncFlagId = props.salesOrderDetail?.matSyncFlagId;
  return matSyncFlagId ? matSyncFlagId.split(",") : [];
});
const flagNames = computedAsync(() => translateFlagNames(flagIds.value));
const allowArchive = computed(() => !props.salesOrderDetail?.documentation);

function translateFlagNames(ids: Array<string>): string | Promise<string> {
  if (!Array.isArray(ids) || !ids.length) {
    return "";
  }
  return synchronousFlagStore.querySynchronousFlag().then(flags => {
    const nameMap = flags.reduce((prev, cur) => {
      prev[cur.id] = cur.flagName;
      return prev;
    }, {});
    return ids
      .map(id => nameMap[id])
      .filter(name => !!name)
      .join(", ");
  });
}

/** 销售订单归档 */
function archive() {
  filingDialogVisible.value = true;
}

async function unarchive() {
  if (!(await useConfirm("正在执行取消归档，是否继续？", "确认取消"))) {
    return;
  }
  await salesOrderManagementStore.saleOrderToggleFiling({ id: props.salesOrderDetail.id });
  salesOrderDetailStore.refreshSalesOrder();
  ElMessage.success("取消成功");
}

/** 完成归档操作 */
const handleArchiveSuccess = () => {
  salesOrderDetailStore.refreshSalesOrder();
};
</script>

<style scoped lang="scss">
.label {
  @apply text-base text-secondary mb-2;
}

.iconfont {
  @apply mr-2 text-primary_light_5 text-xxl leading-none;
}

.el-divider {
  @apply h-10 mx-6;
}

.with-default:empty:after {
  color: var(--default-color);
  content: var(--default-value);
}
</style>
