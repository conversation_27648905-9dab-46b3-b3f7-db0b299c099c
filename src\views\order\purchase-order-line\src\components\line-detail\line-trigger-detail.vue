<template>
  <div class="detail-trigger-info h-full flex flex-col overflow-hidden">
    <Detail ref="lineDetailTrigger" />
  </div>
  <EipGuideDialog />
  <AuditDialog />
</template>

<script setup lang="ts">
import Detail from "@/views/order/purchase-order/detail/src/sg-rate/detail/detail.vue";
import EipGuideDialog from "@/views/components/state-grid-trigger-score/eip-guide-dialog.vue";
import AuditDialog from "@/views/order/purchase-order/detail/src/sg-rate/audit-dialog/audit-dialog.vue";
import { provide, reactive, ref, watch } from "vue";
import { eipGuideKey } from "@/views/components/state-grid-trigger-score/tokens";
import { useStateGriRateDetailStore } from "@/store/modules/state-grid-rate/state-grid-rate-detail";
import { syncAuditKey } from "@/views/order/purchase-order/detail/src/sg-rate/tokens";
import { IPurchaseOrderLineRes } from "@/models/purchase-order";

const props = withDefaults(
  defineProps<{
    info: IPurchaseOrderLineRes | null;
  }>(),
  {}
);

provide(eipGuideKey, reactive({ visible: false }));
provide(syncAuditKey, reactive({ visible: false }));

const detailStore = useStateGriRateDetailStore();
const lineDetailTrigger = ref<InstanceType<typeof Detail>>();

watch(
  () => props.info,
  newVal => {
    if (newVal?.id) {
      const { id, purchaseId, poNo, poItemNo } = newVal;
      const rate = { id, purchaseId, purchaseLineId: id, poNo, poItemNo };
      detailStore.setRate(rate);
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss"></style>
