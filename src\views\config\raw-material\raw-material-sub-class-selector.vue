<template>
  <el-select
    class="w-full"
    v-if="loaded"
    v-model="modelValue"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择物资种类"
    filterable
    clearable
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <el-select
    v-else
    class="w-full"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择物资种类"
    filterable
    clearable
  />
</template>

<script setup lang="ts">
import { ref, watch, withDefaults } from "vue";
import { useVModels } from "@vueuse/core";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";
import { getSubClassListByRawMaterialCode } from "@/api/base-config/raw-material";

interface IOption {
  label: string;
  value: string;
  processId: string;
}

const props = withDefaults(
  defineProps<{
    /** 物资种类 */
    modelValue: string;
    /** 原材料code */
    rawMaterialCode: string;
    disabled?: boolean;
  }>(),
  {
    disabled: false
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", code: string): void;
  (event: "loaded"): void;
}>();

const { modelValue } = useVModels(props, emits);
const loading = ref(false);
const loaded = ref(false);
const options = ref<Array<IOption>>([]);

const requestSubClassList = useLoadingFn(async () => {
  const { data } = await getSubClassListByRawMaterialCode(props.rawMaterialCode);
  if (!data) {
    return;
  }
  options.value = data.map(({ subClassCode, processId, subClassName }) => ({
    label: subClassName,
    value: subClassCode,
    processId
  }));
  loaded.value = true;
  emits("loaded");
}, loading);

function getInpectItemProcessId(code) {
  const selectedItem = options.value.find(item => item.value === code);
  if (selectedItem) {
    return selectedItem.processId || "";
  }
  return "";
}

watch(
  () => props.rawMaterialCode,
  async id => {
    if (id) {
      await requestSubClassList();
    }
  },
  {
    immediate: true
  }
);

defineExpose({
  getInpectItemProcessId,
  getSelectorLoaded: () => loaded.value
});
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  height: var(--el-input-inner-height) !important;
}
</style>
