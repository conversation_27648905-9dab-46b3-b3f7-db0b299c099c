import { defineStore } from "pinia";
import { outGoingFactoryQMXExperimentApi } from "@/api/production-test-sensing/out-going-factory";
import {
  IOutGoingFactoryNotJfnyReq,
  IOutGoingFactoryQMXExperimentForm,
  IOutGoingFactoryQmxExperiment,
  IResponse
} from "@/models";
import {
  useEXFactoryExperimentStore,
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";

/** 气密性试验store */
export const useEXFactoryQMXStore = defineStore({
  id: "ex-factory-qmx-experiment-store",
  state: () => ({
    editTightnessExperiRefVisible: false,
    isEditTightness: false,
    outGoingFactoryQmxExperimentDetail: {} as IOutGoingFactoryQmxExperiment,
    outGoingFactoryQmxExperimentList: [] as Array<IOutGoingFactoryQmxExperiment>,
    experimentListTotal: 0
  }),
  getters: {},
  actions: {
    /** 设置新增或者编辑气密性试验的弹框显示 */
    seteditTightnessExperiRefVisible(visible: boolean) {
      this.editTightnessExperiRefVisible = visible;
    },

    /** 获取出厂试验 气密性试验列表 */
    async queryOutGoingFactoryQMXExperimentList(params?: IOutGoingFactoryNotJfnyReq, isOnlySale = false) {
      const processId = useEXFactoryExperimentStore().activeProcess.processId;
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      const resData = await (isCable
        ? outGoingFactoryQMXExperimentApi.queryOutGoingFactoryQMXExperimentList({
            productionId: dataId,
            processId,
            ...params
          })
        : outGoingFactoryQMXExperimentApi.queryNonCableOutGoingFactoryQMXExperimentList({
            workOrderId: dataId,
            processId,
            ...params
          }));
      this.outGoingFactoryQmxExperimentList = [...this.outGoingFactoryQmxExperimentList, ...resData.data.list];
      this.experimentListTotal = resData.data.total || 0;
    },

    /** 获取出厂试验 气密性试验详情 */
    async getOutGoingFactoryQMXExperimentDetailById(id: string) {
      const res: IResponse<IOutGoingFactoryQmxExperiment> =
        await outGoingFactoryQMXExperimentApi.getOutGoingFactoryQMXExperimentDetailById(id);
      this.outGoingFactoryQmxExperimentDetail = res.data;
    },

    /** 新增 气密性试验 */
    async createOutGoingFactoryQMXExperiment(data: IOutGoingFactoryQMXExperimentForm) {
      return await outGoingFactoryQMXExperimentApi.createOutGoingFactoryQMXExperiment(data);
    },

    /** 编辑 气密性试验*/
    async editOutGoingFactoryQMXExperiment(data: IOutGoingFactoryQMXExperimentForm) {
      return await outGoingFactoryQMXExperimentApi.editOutGoingFactoryQMXExperiment(data);
    },

    /** 删除气密性试验 */
    async deleteOutGoingFactoryQMXExperiment(id: string) {
      return await outGoingFactoryQMXExperimentApi.deleteOutGoingFactoryQMXExperimentDetail(id);
    },

    setOutGoingFactoryQmxExperimentDetail(data?: IOutGoingFactoryQmxExperiment) {
      this.outGoingFactoryQmxExperimentDetail = data;
    },

    /** 更新单条数据 */
    async updateDetailInfoById(detailInfo: IOutGoingFactoryQmxExperiment) {
      (this.outGoingFactoryQmxExperimentList || []).forEach(item => {
        if (item.id === detailInfo.id) {
          Object.assign(item, { ...detailInfo });
        }
      });
    },

    /** 初始化数据 */
    initExperimentList() {
      this.outGoingFactoryQmxExperimentList = [];
      this.experimentListTotal = 0;
    }
  }
});
