<template>
  <!-- 生产工艺及过程检测 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="基础信息" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="采集类型编码"
          prop="collectionCode"
          :rules="{ required: false, message: '请选择采集类型编码', trigger: 'change' }"
        >
          <el-select
            v-model="formData.collectionCode"
            class="w-full"
            placeholder="请选择采集类型编码"
            clearable
            disabled
            filterable
          >
            <el-option v-for="item in collectionList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="检验次数"
          prop="checkNum"
          :rules="{ required: false, message: '请输入检验次数', trigger: 'change' }"
        >
          <el-input-number
            class="w-full"
            v-model="formData.checkNum"
            :min="0"
            placeholder="请输入检验次数"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="检验人员姓名"
          prop="inspectionPersonnel"
          :rules="{ required: false, message: '请输入检验人员姓名', trigger: 'change' }"
        >
          <el-input v-model="formData.inspectionPersonnel" placeholder="请输入检验人员姓名" clearable class="w-full" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="数据类型"
          prop="dataType"
          :rules="{ required: false, message: '请选择数据类型', trigger: 'change' }"
        >
          <el-select v-model="formData.dataType" class="w-full" placeholder="请选择数据类型" clearable filterable>
            <el-option
              v-for="item in DataTypeEnumEjjOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="钢印号/字头号"
          prop="stampingNumber"
          :rules="{ required: true, message: '请输入钢印号/字头号', trigger: 'change' }"
        >
          <el-input v-model="formData.stampingNumber" placeholder="请输入钢印号/字头号" clearable class="w-full" />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="主体变/调补变类型"
          prop="mainRegulatingTransformer"
          :rules="{
            required: formData.equipmentType == EquipmentTypeEnumExt.Transformer,
            message: '请选择主体变/调补变类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mainRegulatingTransformer">
            <el-radio v-for="item in MainRegulatEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物ID"
          prop="subPhysicalItemId"
          :rules="{ required: false, message: '请输入子实物ID', trigger: 'change' }"
        >
          <el-input
            v-model="formData.subPhysicalItemId"
            placeholder=" 请输入子实物ID
"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: false, message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder=" 请输入子实物编码" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="过程检测" />
      </el-col>
      <el-col v-if="isCombiner" :span="24">
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="标准力拒要求值"
              prop="standardForceRejectValue"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请输入标准力拒要求值',
                trigger: 'change'
              }"
            >
              <el-input-number
                class="w-full"
                v-model="formData.standardForceRejectValue"
                :precision="3"
                placeholder="请输入标准力拒要求值"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="实际力拒值"
              prop="actualForceRejectValue"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请输入实际力拒值',
                trigger: 'change'
              }"
            >
              <el-input-number
                class="w-full"
                v-model="formData.actualForceRejectValue"
                :precision="3"
                placeholder="请输入实际力拒值"
                controls-position="right"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="紧固合格状态"
              prop="fasteningPassStatus"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请选择紧固合格状态',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="formData.fasteningPassStatus"
                class="w-full"
                placeholder="请选择紧固合格状态"
                clearable
                filterable
              >
                <el-option
                  v-for="item in FasteningPassStatusEnumOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="紧固操作人员姓名"
              prop="fasteningOperator"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请输入紧固操作人员姓名',
                trigger: 'change'
              }"
            >
              <el-input
                v-model="formData.fasteningOperator"
                placeholder="请输入紧固操作人员姓名"
                clearable
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="互检人员"
              prop="mutualInspector"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请输入互检人员',
                trigger: 'change'
              }"
            >
              <el-input v-model="formData.mutualInspector" placeholder="请输入互检人员" clearable class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="专检人员"
              prop="specialInspector"
              :rules="{
                required: formData.collectionCode == '1',
                message: '请输入专检人员',
                trigger: 'change'
              }"
            >
              <el-input v-model="formData.specialInspector" placeholder="请输入专检人员" clearable class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="清洁操作人员"
              prop="cleaningOperator"
              :rules="{
                required:
                  formData.collectionCode == '2' ||
                  formData.collectionCode == '3' ||
                  formData.collectionCode == '4' ||
                  formData.collectionCode == '5',
                message: '请输入清洁操作人员',
                trigger: 'change'
              }"
            >
              <el-input v-model="formData.cleaningOperator" placeholder="请输入清洁操作人员" clearable class="w-full" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col v-else :span="24">
        <DynamicTable
          ref="dynamicTableRef"
          :fields="tableField"
          field-type="conType"
          field-key="measureItemCode"
          v-model="state.dataValueObj"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import DynamicTable from "../dynamic-table/index.vue";
import {
  DataTypeEnumEjjOptions,
  FasteningPassStatusEnumOptions,
  MainRegulatEnumOptions,
  EquipmentTypeEnumExt
} from "@/enums";
import { InspectProcessModel, CollectionModel, DataValueObjModel, FileInfoModel } from "@/models";
import { useRoute } from "vue-router";
import TitleBar from "@/components/TitleBar/index";

const props = withDefaults(
  defineProps<{
    detail: InspectProcessModel; // 表格表单数据
    isEdit: boolean;
    collection: Array<CollectionModel>;
    tableField: Array<DataValueObjModel>;
  }>(),
  {
    detail: () => {
      return {} as InspectProcessModel;
    },
    tableField: () => {
      return [] as Array<DataValueObjModel>;
    },
    isEdit: false
  }
);
const formData = reactive({} as InspectProcessModel);
const collectionList = ref([] as Array<CollectionModel>);
const dynamicTableRef = ref();
const tableField = ref([] as Array<DataValueObjModel>);
const route = useRoute();

const state = reactive<{
  dataValueObj: { [key: string]: string | number | FileInfoModel };
}>({
  dataValueObj: {}
});

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

watchEffect(() => {
  Object.assign(formData, props.detail);
  collectionList.value = props.collection;
  tableField.value = props.tableField;
  if (props.isEdit) {
    formData.dataValueObj.forEach(item => {
      if (item.conType === "file") {
        state.dataValueObj[item.measureItemCode] = item.fileInfo || ({} as FileInfoModel);
      } else if (item.conType === "number") {
        state.dataValueObj[item.measureItemCode] = item.dataValue ? Number(item.dataValue) : null;
      } else {
        state.dataValueObj[item.measureItemCode] = item.dataValue;
      }
    });
  } else {
    formData.equipmentType = Number(route.query.type);
  }
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  if (!dynamicTableRef.value) {
    return await formRef.value.validate(valid => valid);
  } else {
    return (await formRef.value.validate(valid => valid)) && (await dynamicTableRef.value.validateForm());
  }
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  if (!dynamicTableRef.value) {
    return Object.assign({}, formData as InspectProcessModel);
  }
  const dynamicTableValue = dynamicTableRef.value.getFormValue();
  const params = {
    ...formData,
    dataValueObj: dynamicTableValue.map((item: DataValueObjModel) => {
      return {
        measureItemCode: item.measureItemCode,
        dataValue: item.dataValue,
        conType: item.conType,
        fileInfo: item.fileInfo,
        conConfig: item.conConfig,
        validated: false
      };
    })
  };
  return Object.assign({}, params as InspectProcessModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  dynamicTableRef.value?.resetFields();
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
