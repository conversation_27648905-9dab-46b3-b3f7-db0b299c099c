<template>
  <el-form
    ref="formRef"
    :model="formValue"
    :rules="rules"
    size="default"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40" v-if="isNeedWorkOrderSelector">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工单" prop="workId">
          <work-order-selector
            v-model="formValue.workId"
            :production-id="productionId"
            :disabled="workOrderSelectorDisabled"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工序" prop="processId">
          <process-single-selector
            class="flex-1"
            v-model="formValue.processId"
            limited-by-work-order
            :work-id="formValue.workId"
            :sub-class-code="subClassCode"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="设备" prop="deviceId">
          <device-selector
            class="flex-1"
            v-model="formValue.deviceId"
            limited-by-process
            :process-id="formValue.processId"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="报工批次号" prop="productBatchNo">
          <el-input v-model="formValue.productBatchNo" placeholder="请输入报工批次号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="报工地址" prop="buyerProvince">
          <el-input v-model="formValue.buyerProvince" placeholder="请输入报工地址" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="开始时间" prop="workStartTime" ref="workStartTimeFormItem">
          <el-date-picker
            v-model="formValue.workStartTime"
            label="开始时间"
            placeholder="请选择开始时间"
            class="flex-1"
            clearable
            type="datetime"
            :disabled-date="actualStartDateDisabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="结束时间" prop="workEndTime" ref="workEndTimeFormItem">
          <el-date-picker
            v-model="formValue.workEndTime"
            label="结束时间"
            placeholder="请选择结束时间"
            class="flex-1"
            clearable
            type="datetime"
            :disabled-date="actualEndDateDisabledDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import type { FormInstance } from "element-plus";
import { genFormRules } from "./rules-config";
import { ICreateReportWork } from "@/models";
import ProcessSingleSelector from "@/views/components/process-selector/process-single-selector.vue";
import DeviceSelector from "@/views/components/device-selector/index.vue";
import WorkOrderSelector from "@/views/components/work-order-selector/index.vue";
import { getLatestReportWorkById } from "@/api/report-work";
import { useUserStore } from "@/store/modules/user";

type IReportWorkFormType = Pick<
  ICreateReportWork,
  "processId" | "deviceId" | "productBatchNo" | "buyerProvince" | "workStartTime" | "workEndTime" | "workId"
>;

const useStore = useUserStore();

const props = withDefaults(
  defineProps<{
    mode: "add" | "edit";
    subClassCode: string;
    /** 是否需要工单选择器 */
    isNeedWorkOrderSelector?: boolean;
    /** 开启工单选择器，必传 */
    productionId?: string;
    workId?: string;
    minClassCode?: string;
    workOrderSelectorDisabled?: boolean;
  }>(),
  {
    workId: "",
    minClassCode: "",
    isNeedWorkOrderSelector: false,
    workOrderSelectorDisabled: false
  }
);

const formRef = ref<FormInstance>();
const formValue = reactive<IReportWorkFormType>({
  processId: "",
  deviceId: "",
  workId: "",
  productBatchNo: undefined,
  buyerProvince: undefined,
  workStartTime: undefined,
  workEndTime: undefined
});

watch(
  () => formValue.workId,
  woId => {
    if (props.mode === "edit") {
      return;
    }
    // 如果企业个性化配置，配置了，默认报工地址，则切换工单时，不查询上一次的工单的报工地址
    if (useStore.getDefaultReportAddress) {
      formValue.buyerProvince = useStore.getDefaultReportAddress;
      return;
    }
    if (woId) {
      requestLatestBuyerProvince();
    }
  }
);

watch(
  () => useStore.getDefaultReportAddress,
  defaultReportAddress => {
    // 如果企业个性化配置，配置了，默认报工地址，则切换工单时，不查询上一次的工单的报工地址
    if (props.mode === "add" && useStore.getDefaultReportAddress) {
      formValue.buyerProvince = defaultReportAddress;
    }
  },
  {
    immediate: true
  }
);
const rules = genFormRules(formValue);

const actualStartDateDisabledDate = (date: Date) => {
  if (!formValue.workEndTime) {
    return date > new Date();
  }

  const workEndTime =
    typeof formValue.workEndTime === "string" ? new Date(formValue.workEndTime) : formValue.workEndTime;

  return workEndTime ? date > workEndTime : false;
};

const actualEndDateDisabledDate = (date: Date) => {
  if (!formValue.workStartTime) {
    return date > new Date();
  }

  const workStartTime =
    typeof formValue.workStartTime === "string" ? new Date(formValue.workStartTime) : formValue.workStartTime;

  const hours = workStartTime.getHours();
  const minutes = workStartTime.getMinutes();
  const seconds = workStartTime.getSeconds();
  date.setHours(hours);
  date.setMinutes(minutes);
  date.setSeconds(seconds);
  return !(date <= new Date() && date >= workStartTime);
};

/**
 * @description: 查询该工单上次报工的地址
 */
const requestLatestBuyerProvince = async () => {
  const { data } = await getLatestReportWorkById(formValue.workId);
  if (!data) {
    formValue.buyerProvince = "";
    return;
  } else {
    formValue.buyerProvince = data.buyerProvince;
  }
};

/**
 * @description: 验证表单，并返回验证结果
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单值
 */
function initFormValue(v: any) {
  Object.assign(formValue, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formValue);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped></style>
