import { withApiGateway } from "@/api/util";
import { DIALOG_ERROR_ID_SALES_ORDER_LINE } from "@/consts";
import {
  ICreateSchedulingPlan,
  IListNoPageResponse,
  IListResponse,
  IResponse,
  ISchedulingPlan,
  ISchedulingPlanDetail,
  ISchedulingPlanQuery,
  ISchedulingPlanStatistics
} from "@/models";
import { http } from "@/utils/http";

export const querySchedulingPlan = (salesOrderId: string) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/list/sale/${salesOrderId}/merge`);
  return http.get<void, IListNoPageResponse<ISchedulingPlan>>(url);
};

export const querySchedulingPlanStatistic = (salesOrderId: string) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/status/sale/${salesOrderId}/merge`);
  return http.get<void, IResponse<ISchedulingPlanStatistics>>(url);
};

export const createProductionPlan = (params: ICreateSchedulingPlan) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/save`);
  return http.post<ICreateSchedulingPlan, IResponse<boolean>>(
    url,
    { data: params },
    { showErrorInDialog: `#${DIALOG_ERROR_ID_SALES_ORDER_LINE}` }
  );
};

export const deleteSchedulingPlan = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/delete/${id}`);
  return http.get<void, IResponse<boolean>>(url);
};

export const editProductionPlan = (params: ICreateSchedulingPlan) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/update`);
  return http.post<ICreateSchedulingPlan, IResponse<boolean>>(url, { data: params }, { showErrorInDialog: true });
};

export const getSchedulingPlanDetailById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/production-plan/detail/${id}`);
  return http.get<void, IResponse<ISchedulingPlanDetail>>(url);
};

export const querySchedulingPlanPaging = (params: ISchedulingPlanQuery) => {
  const url: string = withApiGateway("admin-api/business/production-plan/getProductionPlanPage/merge");
  return http.post<ISchedulingPlanQuery, IListResponse<ISchedulingPlan>>(url, { data: params });
};
