import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IEventNotification, IEventNotificationForm, IEventNotificationReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryEventNotification = (data: IEventNotificationReq) => {
  const url: string = withApiGateway("admin-api/southgrid/eventNotifications/pageList");
  return http.post<IEventNotificationReq, IListResponse<IEventNotification>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getEventNotificationById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/eventNotifications/${id}`);
  return http.get<string, IResponse<IEventNotification>>(url);
};

/** 编辑 */
export const updateEventNotification = (data: IEventNotificationForm) => {
  return http.put<IEventNotificationForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/eventNotifications/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteEventNotificationById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/eventNotifications/${id}`));
};

/** 事件通知回复 */
export const eventNotificationReply = (data: IEventNotificationForm) => {
  return http.put<IEventNotificationForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/eventNotifications/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};
