<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 搜索条 -->
    <div class="bg-bg_color pt-[8px] pb-4 px-6">
      <search-bar @handle-search="updateSearchParams">
        <template #right>
          <add-edit-copy-dialog mode="add" title="新增质量规范" @post-save-success="requestList">
            <template #trigger="{ openDialog }">
              <el-button
                v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationAdd"
                type="primary"
                :icon="Plus"
                @click="openDialog"
                >质量规范</el-button
              >
            </template>
          </add-edit-copy-dialog>
        </template>
      </search-bar>
    </div>
    <!-- 表格 -->
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #opertion="{ row }">
          <RouterLink :to="`/quality-specification/${row.id}`">
            <el-button link type="primary">详情</el-button>
          </RouterLink>
          <add-edit-copy-dialog mode="edit" title="编辑质量规范" :id="row.id" @post-save-success="requestList">
            <template #trigger="{ openDialog }">
              <el-button
                v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationEdit"
                link
                type="primary"
                @click="openDialog"
                >编辑</el-button
              >
            </template>
          </add-edit-copy-dialog>
          <add-edit-copy-dialog mode="copy" title="复制质量规范" :id="row.id" @post-save-success="requestList">
            <template #trigger="{ openDialog }">
              <el-button
                v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationCopy"
                link
                type="primary"
                @click="openDialog"
                >复制</el-button
              >
            </template>
          </add-edit-copy-dialog>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { RouterLink } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQualitySpecificationList } from "@/api/quality-tracing";
import { PureTable } from "@pureadmin/table";
import { PermissionKey } from "@/consts/permission-key";
import SearchBar from "./search-bar.vue";
import AddEditCopyDialog from "./add-edit-copy-dialog/index.vue";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import { QualitySpecificationItem, QualitySpecificationListParams } from "@/models/quality-tracing";

/**
 * 质量规范列表
 */

const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<QualitySpecificationItem[]>([]);
const searchParams = ref({
  /** 质量规范名称 */
  keyWords: "",
  /** 物资种类编码 */
  subClassCode: ""
});

/**
 * @description: 更新搜索参数
 */
function updateSearchParams(params: any) {
  searchParams.value = params;
  reloadList();
}

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: QualitySpecificationListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    ...searchParams.value
  };
  const { data } = await queryQualitySpecificationList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(requestList);
</script>

<style scoped lang="scss"></style>
