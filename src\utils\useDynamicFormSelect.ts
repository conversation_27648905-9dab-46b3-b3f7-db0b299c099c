import { emptyDefaultValue } from "@/consts";
import { EControlType } from "@/enums";
import { EDateType } from "@/views/config/collection/types";
import { formatDate } from "./format";

/** 动态表单处理值的显示 */
export function useDynamicFormLabel() {
  /** 下拉框的表单值 */
  function getSelectLabel(rowData) {
    if (rowData) {
      const { dataTypeIdentityDetail, targetValue, targetValueLabel, format } = rowData;
      switch (dataTypeIdentityDetail.identityCode) {
        case EControlType.SelectControl:
        case EControlType.RadioControl:
          return targetValueLabel || emptyDefaultValue;
        case EControlType.DateControl:
          return format === EDateType.DateTime
            ? formatDate(targetValue, "YYYY-MM-DD HH:mm:ss")
            : formatDate(targetValue);
        default:
          return targetValue || emptyDefaultValue;
      }
    }
    return emptyDefaultValue;
  }

  return {
    getSelectLabel
  };
}
