import { Injection<PERSON><PERSON>, Ref, ShallowRef } from "vue";
import { IPurchaseLineCount, IPurchaseOrderLineRes } from "@/models";
import { PaginationProps } from "@pureadmin/table";
import { ISearchForm } from "../models";
import { OrderSyncPriorityEnum } from "@/enums";

export interface IPurchaseOrderLineDataCtx {
  data: ShallowRef<Array<IPurchaseOrderLineRes>>;
  loading: Ref<boolean>;
  searchForm: ISearchForm;
  pagination: PaginationProps;
  selections: Ref<Array<IPurchaseOrderLineRes>>;
  syncPriority: Ref<OrderSyncPriorityEnum>;
  purchaseLineCount: Ref<IPurchaseLineCount>;
  enablePagination(): void;
  disablePagination(): void;
  updateCurrentPage(currentPage: number): void;
  updatePageSize(pageSize: number): void;
  refreshPurchaseOrderLines(): Promise<void>;
  sortChange(sort: { prop: string; order: string }): void;
}
export const purchaseOrderLineDataKey: InjectionKey<IPurchaseOrderLineDataCtx> = Symbol("purchase order line data");
