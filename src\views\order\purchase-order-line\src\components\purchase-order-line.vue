<template>
  <div class="flex-1 flex flex-col overflow-hidden bg-bg_color">
    <div class="flex text-base mb-3 items-center justify-between">
      <div class="flex items-center">
        <div>{{ total }}</div>
        <div class="ml-5">
          <ElCheckbox :label="priorityCount" class="font-medium" @change="priorityChange" />
        </div>
      </div>
      <div>
        <LineBatch
          :selectedData="ctx.selections.value"
          @batchSyncSuccess="batchSync"
          @batchSyncCancel="batchSyncCancel"
          @batchTriggerSuccess="batchTrigger"
        />
      </div>
    </div>
    <PureTable
      ref="purchaseOrderLineRef"
      class="flex-1"
      row-key="id"
      :data="ctx.data.value"
      :columns="columns"
      size="large"
      showOverflowTooltip
      :loading="ctx.loading.value"
      :pagination="ctx.pagination"
      @selection-change="handleSelectionChange"
      @page-size-change="ctx.updatePageSize"
      @page-current-change="ctx.updateCurrentPage"
      @sort-change="ctx.sortChange"
      :class="batch"
    >
      <template #poItemNo="{ row }">
        <span v-if="!displaySyncPriorityButton(row)" class="urgent-icon">
          <el-icon color="#67C23A" :size="16"><CaretTop /></el-icon>
        </span>
        <span>{{ row.poItemNo }}</span>
      </template>
      <template #operate="{ row }">
        <div class="operate">
          <el-button
            v-auth="PermissionKey.form.formPurchaseLineSync"
            v-track="TrackPointKey.FORM_PURCHASE_SYNC_BTN_ONE_KEY"
            type="primary"
            link
            @click.stop.prevent="showSyncAuditDialog(row)"
            :disabled="isBatch"
          >
            一键同步
          </el-button>
          <el-button
            v-auth="PermissionKey.form.formPurchaseLineTriggerScore"
            v-track="TrackPointKey.FORM_PURCHASE_TRIGGER_BTN_ONE_KEY"
            type="primary"
            link
            @click.stop.prevent="showTriggerAuditDialog(row)"
            :disabled="isBatch"
            >触发评分</el-button
          >

          <el-button
            v-if="displaySyncPriorityButton(row)"
            v-auth="PermissionKey.form.formPurchaseLinePrioritySync"
            type="primary"
            link
            @click.stop.prevent="changePurchaseOrderLineSyncPriority(row)"
            :disabled="isBatch"
          >
            优先同步
          </el-button>
          <el-button
            v-else
            type="primary"
            link
            @click.stop.prevent="onCanclePurchaseOrderLineSyncPriority(row)"
            :disabled="isBatch"
            v-auth="PermissionKey.form.formPurchaseLinePrioritySync"
          >
            取消优先
          </el-button>

          <el-button type="primary" link @click.stop.prevent="showSyncTriggerDetailDialog(row)" :disabled="isBatch"
            >详情</el-button
          >
        </div>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
  <!-- 同步确认 -->
  <el-dialog
    title="同步确认"
    v-model="syncAuditVisible"
    :close-on-press-escape="false"
    destroy-on-close
    fullscreen
    class="sync-audit-full-screen"
  >
    <SyncAudit
      :base-info="syncAuditBaseInfo"
      :business-audit-items="BUSINESS_CARD_IDS"
      :iot-audit-items="IOT_STATE_GRID_CARD_IDS"
      allow-filter-by-order-no
      ref="syncAudit"
    />
    <template #footer>
      <el-button @click="syncAuditVisible = false">取消</el-button>
      <el-button type="primary" :loading="syncLoading" @click="handleConfirmSync">确认同步</el-button>
    </template>
  </el-dialog>
  <!-- 触发评分 -->
  <el-dialog
    title="触发评分确认"
    v-model="triggerVisible"
    destroy-on-close
    fullscreen
    :close-on-press-escape="false"
    class="sync-audit-full-screen"
  >
    <SyncAudit :base-info="triggerBaseInfo" :iot-audit-items="IOT_STATE_GRID_CARD_IDS" allow-filter-by-order-no />
    <template #footer>
      <el-button @click="triggerVisible = false">取消</el-button>
      <el-button type="primary" :loading="triggerLoading" @click="triggerScoreCheck">确认触发</el-button>
    </template>
  </el-dialog>
  <!-- 详情 -->
  <el-dialog
    v-model="syncTriggerDetailVisible"
    destroy-on-close
    fullscreen
    :close-on-press-escape="false"
    class="sync-audit-full-screen"
  >
    <template #header>
      <DetailHeaderInfo :info="currentDetailInfo" />
    </template>
    <div class="h-full">
      <LineDetailInfoDialog :info="currentDetailInfo" />
    </div>
  </el-dialog>

  <!-- 同步优先级同步确认  -->
  <el-dialog
    title="确认优先同步"
    v-model="syncPriorityAuditVisible"
    :close-on-press-escape="false"
    destroy-on-close
    fullscreen
    class="sync-audit-full-screen"
  >
    <SyncAudit
      :base-info="syncAuditBaseInfo"
      :business-audit-items="BUSINESS_CARD_IDS"
      :iot-audit-items="IOT_STATE_GRID_CARD_IDS"
      allow-filter-by-order-no
      ref="syncAudit"
    />
    <template #footer>
      <el-button @click="syncPriorityAuditVisible = false">取消</el-button>
      <el-button type="primary" :loading="syncLoading" @click="handleConfirmSyncPriority">确认优先同步</el-button>
    </template>
  </el-dialog>

  <TriggerScoreCheckDialog v-model="checkVisible" :checkText="checkText" @trigger-score="confirmTriggerScore" />
  <!-- EIP检查数据流程 -->
  <EipGuideDialog />
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import EipGuideDialog from "@/views/components/state-grid-trigger-score/eip-guide-dialog.vue";
import LineBatch from "./line-batch.vue";
import LineDetailInfoDialog from "./line-detail/index.vue";
import TriggerScoreCheckDialog from "@/views/components/state-grid-trigger-score/trigger-score-check-dialog.vue";
import { computed, inject, onMounted, onUnmounted, provide, reactive, ref, watchEffect } from "vue";
import { useColumns } from "./columns";
import { PermissionKey, TrackPointKey } from "@/consts";
import { IPurchaseOrderLineRes, IStateGridSyncAuditBaseInfo } from "@/models";
import { usePurchaseOrderLineStore } from "@/store/modules/purchase-order";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { eipGuideKey } from "@/views/components/state-grid-trigger-score/tokens";
import { SYNC_ORDER_TOKEN } from "@/views/order/purchase-order/detail/src/sg-order/tokens";
import { allowTriggerStateGridScore, triggerScore } from "@/views/components/state-grid-trigger-score/tools";
import { ElMessage, ElNotification } from "element-plus";
import { CaretTop } from "@element-plus/icons-vue";
import { useStateGridOrderSyncListStore } from "@/store/modules/state-grid-order-sync";
import { BUSINESS_CARD_IDS, IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";
import { OrderSyncPriorityEnum, OrderType } from "@/enums";
import { useConfirm } from "@/utils/useConfirm";
import DetailHeaderInfo from "./detail-header-info.vue";
import { purchaseOrderLineDataKey } from "../tokens/purchase-order-line-data-token";
import { formatDecimal } from "@/utils/format";

const ctx = inject(purchaseOrderLineDataKey);

const { columns } = useColumns();

const lineStore = usePurchaseOrderLineStore();
const gridOrderSyncStore = useStateGridOrderSyncListStore();
const purchaseOrderLineRef = ref<PureTableInstance>();
const syncAuditVisible = ref<boolean>(false);
const syncPriorityAuditVisible = ref<boolean>(false);
const syncAuditBaseInfo = ref<IStateGridSyncAuditBaseInfo>();
const triggerVisible = ref<boolean>(false);
const triggerBaseInfo = ref();
const syncTriggerDetailVisible = ref<boolean>(false);
const currentDetailInfo = ref<IPurchaseOrderLineRes>();
// 确认同步
const syncAudit = ref<InstanceType<typeof SyncAudit>>();
const syncLoading = ref<boolean>(false);
const handleConfirmSync = useLoadingFn(syncAll, syncLoading);
const handleConfirmSyncPriority = useLoadingFn(onConfirmSyncPriority, syncLoading);
// 触发评分
const triggerLoading = ref<boolean>(false);
const checkVisible = ref<boolean>(false);
const checkText = ref();
const triggerScoreCheck = useLoadingFn(confirmTrigger, triggerLoading);
const handleTriggerScoreAfterSyncAudit = useLoadingFn(triggerScoreAfterSyncAudit, triggerLoading);
// eipGuideKey provide
provide(eipGuideKey, reactive({ visible: false }));
provide(SYNC_ORDER_TOKEN, {
  currentKeyComp: computed(() => {
    return null;
  })
});

onMounted(async () => {
  await ctx.refreshPurchaseOrderLines();
});

onUnmounted(() => {
  currentDetailInfo.value = null;
});

/** 一键同步 */
function showSyncAuditDialog(row: IPurchaseOrderLineRes) {
  displaySyncAuditInfo(row);
  syncAuditVisible.value = true;
}

/** 调整优先级  */
function changePurchaseOrderLineSyncPriority(row: IPurchaseOrderLineRes) {
  displaySyncAuditInfo(row);
  syncPriorityAuditVisible.value = true;
}

/** 取消优先级 */
async function onCanclePurchaseOrderLineSyncPriority(row: IPurchaseOrderLineRes) {
  if (!(await useConfirm("取消优先同步", "确认取消"))) {
    return;
  }
  await lineStore.changePurchaseOrderLineSyncPriority({
    purchseLineId: row.id,
    priorityEnum: OrderSyncPriorityEnum.COMMON_PRIORITY
  });
  void ctx.refreshPurchaseOrderLines();
  ElMessage.success("取消优先同步成功");
}

/** 展示同步确认展示信息框 */
function displaySyncAuditInfo(row: IPurchaseOrderLineRes) {
  currentDetailInfo.value = row;
  const { id, poItemNo, purchaseId, poNo, materialCode, materialDesc, subClassCode } = row;
  syncAuditBaseInfo.value = {
    isCable: true,
    isArmourClamp: false,
    subClassCode,
    purchaseLineId: id,
    poNo,
    poItemNo,
    materialCode,
    materialDesc,
    orderType: OrderType.PURCHASE,
    orderId: purchaseId
  };
}

/** 触发评分 */
function showTriggerAuditDialog(row: IPurchaseOrderLineRes) {
  currentDetailInfo.value = row;
  const { id, poItemNo, purchaseId, poNo, materialCode, materialDesc, subClassCode } = row;
  triggerBaseInfo.value = {
    isCable: true,
    isArmourClamp: false,
    purchaseId,
    subClassCode,
    purchaseLineId: id,
    poNo,
    poItemNo,
    materialCode,
    materialDesc
  };
  triggerVisible.value = true;
}

/** 详情 */
function showSyncTriggerDetailDialog(row: IPurchaseOrderLineRes) {
  currentDetailInfo.value = row;
  syncTriggerDetailVisible.value = true;
}

/** 批量选择 */
function handleSelectionChange(selection: IPurchaseOrderLineRes[]) {
  ctx.selections.value = selection;
}

/** 确认同步 */
async function syncAll() {
  const { id, poItemNo, purchaseId } = currentDetailInfo.value;
  if (syncAudit.value.isEmpty()) {
    ElMessage.warning("当前采购订单行下没有数据可以同步，请先维护相关数据");
    return;
  }
  await gridOrderSyncStore.syncAll(id, purchaseId);
  ElNotification.info({
    title: "数据同步",
    message: `正在同步采购订单行项目【${poItemNo}】下的相关数据`,
    duration: 3000
  });
  syncAuditVisible.value = false;
}

/** 确认优先同步 */
async function onConfirmSyncPriority() {
  const { id } = currentDetailInfo.value;
  await lineStore.changePurchaseOrderLineSyncPriority({
    purchseLineId: id,
    priorityEnum: OrderSyncPriorityEnum.FAST_PRIORITY
  });

  ElMessage.success("设置优先同步成功");
  syncPriorityAuditVisible.value = false;
  void ctx.refreshPurchaseOrderLines();
}

/** 确认触发 */
async function confirmTrigger() {
  const { id } = currentDetailInfo.value;
  const checkRes = (await allowTriggerStateGridScore(id)).data;
  const { verify, message } = checkRes;
  if (!verify) {
    checkText.value = message;
    checkVisible.value = true;
    return;
  }
  await confirmTriggerScore();
}

async function confirmTriggerScore() {
  await handleTriggerScoreAfterSyncAudit();
}

async function triggerScoreAfterSyncAudit() {
  const { purchaseId, id } = currentDetailInfo.value;
  await triggerScore(purchaseId, id);
  checkVisible.value = false;
  triggerVisible.value = false;
  showNotification();
}

/** 批量同步 */
function batchSync() {
  ElMessage.success("批量同步成功");
  purchaseOrderLineRef.value?.getTableRef().clearSelection();
}

/** 批量触发评分 */
function batchTrigger() {
  ElMessage.success("批量触发成功");
  purchaseOrderLineRef.value?.getTableRef().clearSelection();
}

/** 取消批量操作 */
function batchSyncCancel() {
  ctx.selections.value.length = 0;
  purchaseOrderLineRef.value?.getTableRef().clearSelection();
}
function showNotification() {
  const { id } = currentDetailInfo.value;
  ElNotification.info({
    title: "触发评分",
    message: `正在触发采购订单行【${id}】下生产数据的质量评分`,
    duration: 3000
  });
}

function displaySyncPriorityButton(row: IPurchaseOrderLineRes) {
  return row.priority !== OrderSyncPriorityEnum.FAST_PRIORITY;
}

const priorityCount = computed(() => `优先同步（${formatDecimal(ctx.purchaseLineCount.value.priorityCount) || 0}条）`);
const total = computed(() => `订单共（${formatDecimal(ctx.purchaseLineCount.value.total) || 0}条）`);

const batch = computed(() => (ctx.selections.value.length ? `batch` : ``));
const isBatch = computed(() => !!ctx.selections.value.length);

watchEffect(() => {
  if (!ctx.selections.value.length) {
    purchaseOrderLineRef.value?.getTableRef().clearSelection();
  }
});

function priorityChange(event) {
  ctx.syncPriority.value = event ? OrderSyncPriorityEnum.FAST_PRIORITY : OrderSyncPriorityEnum.COMMON_PRIORITY;
  void ctx.refreshPurchaseOrderLines();
}
</script>

<style lang="scss" scoped>
@import "@/views/order/purchase-order/detail/styles/mixin";

.sync-audit-full-screen {
  @include full-screen-dialog;
}

.batch {
  ::v-deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  ::v-deep(.el-input__suffix-inner) {
    pointer-events: none;
  }
}

.urgent-icon {
  position: absolute;
  left: -6px;
  top: 14px;
}
</style>
