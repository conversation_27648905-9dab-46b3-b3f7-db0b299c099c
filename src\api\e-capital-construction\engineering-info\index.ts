/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, CollectionModel, DataValueObjModel } from "@/models";
import { EProjectModel, EParamsStandardReport } from "@/models";
export * from "./arrival";
export * from "./basic-info";
export * from "./inventory-info";
export * from "./production-progress";
export * from "./stack-manage";
export * from "./unit-basic-info";
export * from "./pkg-ship";
export * from "./experiment-report";
export * from "./env-report";
export * from "./spot-check";
export * from "./inspect-process";
export * from "./inspect-raw";
export * from "./factory-test";
export * from "./factory-standards";

/**
 * @description: 工程信息列表-分页
 */
export const ejjProjectPageApi = (params: EParamsStandardReport) => {
  return http.post<EParamsStandardReport, IListResponse<EProjectModel>>(withApiGateway(`admin-api/ejj/project/page`), {
    data: params
  });
};

/**
 * @description: 工程信息列表--新增
 */
export const createEngineeringApi = (params: EProjectModel) => {
  return http.post<EProjectModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/add`), {
    data: params
  });
};

/**
 * @description: 工程信息列表--编辑
 */
export const updateEngineeringApi = (params: EProjectModel) => {
  return http.put<EProjectModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/update`), {
    data: params
  });
};

/**
 * @description: 工程信息列表--删除
 */
export const deleteEngineeringApi = (id: string) => {
  return http.delete<EParamsStandardReport, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/${id}`));
};

/**
 * @description: 工程信息列表--查看
 */
export const detailEngineeringApi = (id: String) => {
  return http.get<string, IResponse<EProjectModel>>(withApiGateway(`admin-api/ejj/project/${id}`));
};

/**
 * @description: 根据生产阶段编码、设备类型获取采集类型
 */
export const CollectionApi = (stageCode: string, equipmentCode: number, equipmentName?: string) => {
  return http.get<string, IResponse<Array<CollectionModel>>>(
    withApiGateway(`admin-api/ejj/equipment/collection/${stageCode}/${equipmentCode}?speciType=${equipmentName}`)
  );
};

/**
 * @description: 根据采集类型编码获取采集项数据
 */
export const CollectionItemApi = (collectionCode: string) => {
  return http.get<string, IResponse<Array<DataValueObjModel>>>(
    withApiGateway(`admin-api/ejj/equipment/collection-item/${collectionCode}`)
  );
};

/**
 * @description: 一键同步
 */
export const IntermediateSyncApi = (equipmentId: string) => {
  return http.post<string, IResponse<boolean>>(withApiGateway(`admin-api/ejj/intermediate/sync/${equipmentId}`));
};

/**
 * @description: 一键同步
 */
export const IntermediateSyncByVoucherTypeApi = (voucherType: string, equipmentId: string) => {
  return http.post<string, IResponse<boolean>>(
    withApiGateway(`admin-api/ejj/intermediate/sync/${voucherType}/${equipmentId}`)
  );
};

/**
 * @description: 获取同步报文
 */
export const IntermediateApi = (batchId: string) => {
  return http.get<string, IResponse<any>>(withApiGateway(`admin-api/ejj/intermediate/${batchId}`));
};
