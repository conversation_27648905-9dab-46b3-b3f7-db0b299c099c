<template>
  <el-form ref="searchFormRef" :model="formValue" :inline="true" class="cx-form">
    <el-form-item label="编号:" prop="searchNo">
      <el-input class="min-w-[220px]" v-model="formValue.searchNo" clearable placeholder="请输入" />
    </el-form-item>
    <el-form-item label="同步状态:" prop="syncResult">
      <el-select v-model="formValue.syncResult" placeholder="请选择同步状态" clearable filterable>
        <el-option v-for="item in stateOptions || []" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="showProcessSelector" label="工序：" prop="processId">
      <process-single-selector :sub-class-code="store.subClassCode" v-model="formValue.processId" />
    </el-form-item>
    <el-form-item v-if="showRawMaterialSelector" label="原材料类型：" prop="processId">
      <raw-material-process-selector :sub-class-code="store.subClassCode" v-model="formValue.processId" />
    </el-form-item>
    <el-form-item v-if="showExperimentSelector" label="试验类型：" prop="processId">
      <experiment-process-selector
        :sub-class-code="store.subClassCode"
        v-model="formValue.processId"
        v-model:process-code="formValue.experimentCode"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { IOption, ISyncSearchParams } from "@/models";
import { FormInstance } from "element-plus";
import { computed, reactive, ref } from "vue";
import { StateGridOrderSyncResult } from "@/enums";
import { useStateGridOrderSyncDetailStore } from "@/store/modules";
import { StateGridOrderSyncStep } from "@/enums/state-grid-order/state-grid-order-sync-step.enum";
import ProcessSingleSelector from "@/views/components/process-selector/process-single-selector.vue";
import ExperimentProcessSelector from "@/views/components/process-selector/experiment-process-selector.vue";
import RawMaterialProcessSelector from "@/views/components/process-selector/raw-material-process-selector.vue";

interface FormType {
  searchNo?: string;
  syncResult?: string;
  processId?: string;
  // 只有试验选择器用，特殊处理，兼容后端需要从另一张表里查局放耐压试验
  experimentCode?: string;
}

const emits = defineEmits<{
  (e: "search", params: ISyncSearchParams): void;
}>();

const store = useStateGridOrderSyncDetailStore();

/** 是否显示工序选择器 */
const showProcessSelector = computed(
  () =>
    store.activeStepKey === StateGridOrderSyncStep.PROCESS_QUALITY_AUTO_COLLECT ||
    store.activeStepKey === StateGridOrderSyncStep.WORK_REPORT ||
    store.activeStepKey === StateGridOrderSyncStep.TECH_PROCESS_QUALITY
);

/** 是否显示原材料类型选择器 */
const showRawMaterialSelector = computed(() => store.activeStepKey === StateGridOrderSyncStep.MATERIAL_COMP_QUALITY);

/** 是否显示原材料类型选择器 */
const showExperimentSelector = computed(() => store.activeStepKey === StateGridOrderSyncStep.OUTGOING_QUALITY);

const searchFormRef = ref<FormInstance>();
const formValue = reactive<FormType>({
  searchNo: "",
  syncResult: "",
  processId: "",
  experimentCode: ""
});

const stateOptions: Array<IOption> = [
  { label: "全部", value: "" },
  { label: "未同步", value: StateGridOrderSyncResult.NO_SYNC },
  { label: "已触发同步", value: StateGridOrderSyncResult.SUCCESS },
  { label: "同步失败", value: StateGridOrderSyncResult.FAULT },
  { label: "排队中", value: StateGridOrderSyncResult.TRANSFERRING }
];

const search = () => {
  emits("search", genFormatValue(formValue));
};

const reset = () => {
  searchFormRef.value?.resetFields();
  formValue.experimentCode = "";
  emits("search", genFormatValue(formValue));
};

/**
 * @description: 生成格式化
 */
function genFormatValue(form: FormType) {
  const params = Object.assign({}, form);
  // 如果是局放耐压实验，按后端要求，将processId改为0
  if (formValue.experimentCode === "JFNY") {
    params.processId = "0";
  }

  delete params["experimentCode"];
  return params;
}

defineExpose({
  getSearchState: () => {
    return genFormatValue(formValue);
  }
});
</script>

<style scoped lang="scss">
.cx-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
