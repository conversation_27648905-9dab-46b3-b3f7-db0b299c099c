/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { ProductionProgressModel } from "@/models";

/**
 * @description: 设备、单元生产进度信息列表
 */
export const ProductionProgressInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<ProductionProgressModel>>>(
    withApiGateway(`admin-api/ejj/project/product-progress/list/${id}`)
  );
};

/**
 * @description: 原材料原材料到货进度创建
 */
export const ProductionProgressCreateApi = (params: ProductionProgressModel) => {
  return http.post<ProductionProgressModel, IResponse<boolean>>(
    withApiGateway(`admin-api/ejj/project/product-progress`),
    {
      data: params
    }
  );
};

/**
 * @description: 原材料原材料到货进度编辑
 */
export const ProductionProgressEditApi = (params: ProductionProgressModel) => {
  return http.put<ProductionProgressModel, IResponse<Boolean>>(
    withApiGateway(`admin-api/ejj/project/product-progress`),
    {
      data: params
    }
  );
};

/**
 * @description: 原材料原材料到货进度删除
 */
export const ProductionProgressDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`/admin-api/ejj/project/product-progress/${id}`));
};
