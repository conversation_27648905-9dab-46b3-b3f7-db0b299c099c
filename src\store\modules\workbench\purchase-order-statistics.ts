import { defineStore } from "pinia";
import * as api from "@/api/workbench";
import { IPurchaseOrderStatistics } from "@/models";

export const usePurchaseOrderStatisticsStore = defineStore({
  id: "cx-purchase-order-statistics",
  state: () => ({
    loading: false,
    statistics: {} as IPurchaseOrderStatistics
  }),
  getters: {
    followCount: state => state.statistics.followCount || 0,
    unReadCount: state => state.statistics.unReadCount || 0,
    linkSales: state => state.statistics.linkSales || 0,
    productionPlan: state => state.statistics.productionPlan || 0,
    productionOrder: state => state.statistics.productionOrder || 0,
    productionData: state => state.statistics.productionData || 0,
    syncOrder: state => state.statistics.syncOrder || 0,
    qualityEval: state => state.statistics.qualityEval || 0
  },
  actions: {
    refresh(loading = false) {
      this.loading = loading;
      api
        .getPurchaseOrderStatistics()
        .then(res => res.data)
        .then(statistics => (this.statistics = statistics))
        .finally(() => (this.loading = false));
    }
  }
});
