import { LinkStepEnum, PurchaseChannel } from "@/enums";
import { IPagingReq, ISortReq } from "../i-paging-req";

export interface IPurchaseOrderReq extends IPagingReq, ISortReq {
  /** 采购订单号 */
  poNo?: string;

  /** 采购方公司名称  */
  buyerName?: string;

  /** 合同签订日期 */
  sellerSignTime?: Array<Date | string>;

  /** 物资种类 */
  subclassCode?: string;

  /** 同步状态  */
  syncType?: boolean;

  /** 触发评分结果 */

  triggerType?: boolean;

  /** 	项目名称  */
  prjName?: string;

  /** 	合同名称,示例值(XXX采购合同)  */
  conName?: string;

  /** 当前填报环节,示例值(1)  */
  linkStep?: LinkStepEnum;

  /** 仅看未读 */
  readed?: boolean;

  /** 组合查询参数 */

  orFilters?: Array<Record<string, string>>;

  /** 关注 */
  follow?: boolean;

  /** 归档 */
  documentation?: boolean;

  /** 来源、渠道 */
  channel?: PurchaseChannel;

  /** 采购订单创建时间 */
  createTime?: Array<string>;
}

export interface IPurchaseList {
  purchaseOrderId: string;
  purchaseOrderItemId: string;
}
/** 批量同步 */
export interface IBatchSyncPurchaseOrderLineReq {
  purchaseList: IPurchaseList[];
  dataType: number;
}

/** 批量触发 */
export interface IBatchTriggerPurchaseOrderLineReq {
  purchaseId: string;
  purchaseLineId: string;
  productionId?: string;
  workId?: string;
}

export interface IBatchTriggerPurchaseOrderLineRes {
  purchaseLineId: string;
  taskId: string;
  success: boolean;
  errorMsg: string;
}
