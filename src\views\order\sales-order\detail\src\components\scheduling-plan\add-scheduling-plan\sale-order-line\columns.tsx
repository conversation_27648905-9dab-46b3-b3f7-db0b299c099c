import { TableWidth } from "@/enums";
import { useSalesSchedulingPlanStore } from "@/store/modules";
import { TableColumnRenderer } from "@pureadmin/table";
import { ElRadioGroup, ElRadio } from "element-plus";
import CxTag from "@/components/CxTag/index.vue";
import { MEASURE_UNIT } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const schedulingPlanStore = useSalesSchedulingPlanStore();
  const { withUnitFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "销售订单号",
      prop: "soNo",
      width: TableWidth.order
    },
    {
      label: "选择",
      prop: "40",
      width: 55,
      align: "center",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <ElRadioGroup modelValue={schedulingPlanStore.schedulingPlanForm?.salesLineId}>
            <ElRadio label={data.row?.id} style={{ marginRight: "0 !important" }}>
              {{
                default: () => ""
              }}
            </ElRadio>
          </ElRadioGroup>
        );
      }
    },
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      minWidth: TableWidth.order
    },
    {
      label: "生产工号",
      prop: "productionWorkNo",
      minWidth: TableWidth.order
    },
    {
      label: "物资种类",
      prop: "subClassName",
      width: TableWidth.type
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      width: TableWidth.number,
      align: "right",
      formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      width: TableWidth.number
    },
    {
      label: "有无排产",
      prop: "schduled",
      width: TableWidth.number,
      fixed: "right",
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.schduled ? (
          <CxTag disableTransitions type="success">
            有
          </CxTag>
        ) : (
          <CxTag disableTransitions type="warning">
            无
          </CxTag>
        );
      }
    }
  ];
  return { columns };
}
