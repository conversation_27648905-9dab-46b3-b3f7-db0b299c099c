import { ColumnWidth } from "@/enums";
import { MEASURE_UNIT } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 生成生产订单表格配置
 */
export function genProductionTableColumnsConfig() {
  const { withUnitFormatter, dateFormatter } = useTableCellFormatter();

  const columnsConfig: TableColumnList = [
    {
      label: "物资种类",
      prop: "subClassName",
      width: ColumnWidth.Char9,
      fixed: "left",
      slot: "subClassName"
    },
    {
      label: "工序",
      prop: "processName",
      width: ColumnWidth.Char20
    },
    {
      label: "最新导入时间",
      prop: "importTime",
      minWidth: ColumnWidth.Char9,
      formatter: dateFormatter()
    },
    {
      label: "总条数",
      prop: "totalCount",
      width: ColumnWidth.Char15,
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "导入成功条数",
      prop: "successCount",
      width: ColumnWidth.Char15,
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "导入失败条数",
      prop: "faultCount",
      width: ColumnWidth.Char15,
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "操作人",
      prop: "opUserName",
      minWidth: ColumnWidth.Char15
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char16,
      fixed: "right",
      slot: "opertion"
    }
  ];

  return { columnsConfig };
}
