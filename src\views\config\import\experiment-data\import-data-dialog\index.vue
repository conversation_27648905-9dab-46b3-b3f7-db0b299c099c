<template>
  <div class="inline-block">
    <el-button size="large" type="primary" @click="openDialog"> 导入数据 </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="导入数据"
      align-center
      :width="800"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <import-form ref="formRef" />
      <!-- 内容 -->
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleDownloadBtn" :loading="loading">导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import ImportForm from "./import-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { importExperimentData } from "@/api/import";

const emits = defineEmits(["postImportSuccess"]);

const loading = ref(false);
const dialogVisible = ref(false);
const formRef = ref<InstanceType<typeof ImportForm>>();

const requestImport = useLoadingFn(async () => {
  const params = formRef.value.getFormValue();
  return await importExperimentData(params);
}, loading);

const handleDownloadBtn = async () => {
  const verificationResult = await formRef.value.validateForm();

  if (!verificationResult) {
    return;
  }

  const { data } = await requestImport().finally(() => {
    // 处理保存后续事件
    closeDialog();
    emits("postImportSuccess");
  });

  if (data) {
    ElMessage({
      message: "导入成功",
      type: "success"
    });
  }
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
