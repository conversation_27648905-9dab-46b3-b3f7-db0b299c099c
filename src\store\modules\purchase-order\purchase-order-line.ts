import { defineStore } from "pinia";
import {
  IBatchSyncPurchaseOrderLineReq,
  IBatchTriggerPurchaseOrderLineReq,
  IPurchaseOrderLineRes,
  IPurchaseOrderLineSyncPriority
} from "@/models/purchase-order";
import * as api from "@/api/purchase-order-line";
import { omitBy } from "lodash-unified";
import { isEmpty } from "@pureadmin/utils";
import { IPurchaseOrderLineQueryParams } from "@/models";

export const usePurchaseOrderLineStore = defineStore({
  id: "use-purchase-order-line-store",
  state: () => ({
    purchaseOrderLines: [] as Array<IPurchaseOrderLineRes>,
    orderLineTotal: 0
  }),
  actions: {
    /** 查询采购订单行数据 */
    queryPurchaseOrderLines(params: IPurchaseOrderLineQueryParams) {
      return api.getPurchaseOrder(omitBy(params, isEmpty));
    },

    /** 批量同步数据 */
    async batchSyncOrderLine(params: IBatchSyncPurchaseOrderLineReq) {
      return await api.batchSyncPurchaseOrderLine(params);
    },

    async batchTriggerOrderLine(params: IBatchTriggerPurchaseOrderLineReq[]) {
      return await api.batchTriggerPurchaseOrderLine(params);
    },

    /** 调整采购订单行同步优先级 */
    async changePurchaseOrderLineSyncPriority(data: IPurchaseOrderLineSyncPriority) {
      return await api.changePurchaseOrderLineSyncPriority(data);
    },
    /** 数量和优先同步数量 */
    queryPurchaseCount(params: IPurchaseOrderLineQueryParams) {
      return api.queryPurchaseCount(params);
    }
  }
});
