import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, IEnvReport, IEnvReportForm } from "@/models";

/** 查询分页  */
export const queryEnvReport = (id: string) => {
  const url: string = withApiGateway(`admin-api/ejj/project/process/env-report/list/${id}`);
  return http.get<void, IResponse<Array<IEnvReport>>>(url);
};

/** 根据id 查询详情 */
export const getEnvReportById = (id: string) => {
  const url: string = withApiGateway(`admin-api/ejj/project/process/env-report/${id}`);
  return http.get<string, IResponse<IEnvReport>>(url);
};

/** 新增 */
export const createEnvReport = (data: IEnvReportForm) => {
  return http.post<IEnvReportForm, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/process/env-report"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateEnvReport = (data: IEnvReportForm) => {
  return http.put<IEnvReportForm, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/process/env-report"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteEnvReportById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/process/env-report/${id}`));
};
