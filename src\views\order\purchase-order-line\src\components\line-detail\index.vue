<template>
  <div class="purchase-order-line-detail h-full flex flex-col overflow-hidden">
    <!-- 切换详情 -->
    <div class="switch-purchase-order mb-5">
      <CxTag
        v-for="(item, index) in switchModule"
        :key="item.moduleCode"
        size="large"
        class="mr-4 cursor-pointer"
        :type="index === currentModuleIndex ? 'primary' : 'inactive'"
        @click="clickModuleChange(item, index)"
      >
        {{ item.moduleName }}
        <!-- <div class="count-info ml-1 flex items-center">
          <span>({{ item.result }})</span>
        </div> -->
      </CxTag>
    </div>
    <Component :is="currentComp" :info="info" />
  </div>
</template>

<script setup lang="ts">
import CxTag from "@/components/CxTag/index.vue";
import LineSyncDetail from "./line-sync-detail.vue";
import LineTriggerDetail from "./line-trigger-detail.vue";
import { IModule } from "@/components/cx-tab-switch/types";
import { PurchaseOrderLineDetailEnum } from "@/enums/purchase-order";
import { onMounted, reactive, ref, shallowRef } from "vue";
import { IPurchaseOrderLineRes } from "@/models";
import { usePurchaseOrderDetailStore } from "@/store/modules/purchase-order";

const props = withDefaults(
  defineProps<{
    info: IPurchaseOrderLineRes | null;
  }>(),
  {}
);

const purchaseDetailStore = usePurchaseOrderDetailStore();
const currentComp = shallowRef();
currentComp.value = LineSyncDetail;

// 同步Tab
const currentModuleIndex = ref(0);
const switchModule = reactive<Array<IModule>>([
  {
    moduleCode: PurchaseOrderLineDetailEnum.DATA_SYNC,
    moduleName: "数据同步",
    result: "NO_SYNC"
  },
  {
    moduleCode: PurchaseOrderLineDetailEnum.TRIGGER_SCORE,
    moduleName: "触发评分",
    result: "NO_TRIGGER"
  }
]);

onMounted(() => {
  purchaseDetailStore.setPurchaseOrderId(props.info?.purchaseId);
  purchaseDetailStore.refreshPurchaseOrder();
});

/** 切换 */
function clickModuleChange(item: IModule, index: number) {
  currentModuleIndex.value = index;
  switch (item.moduleCode) {
    case PurchaseOrderLineDetailEnum.TRIGGER_SCORE:
      currentComp.value = LineTriggerDetail;
      break;
    case PurchaseOrderLineDetailEnum.DATA_SYNC:
      currentComp.value = LineSyncDetail;
      break;
    default:
      break;
  }
}
</script>

<style scoped lang="scss"></style>
