<template>
  <el-scrollbar class="h-full" v-loading="props.loading">
    <div class="mt-2.5 mb-8">
      <div class="header">
        <TitleBar title="基础信息" />
      </div>
      <el-descriptions class="describe-content" :column="3">
        <el-descriptions-item label="告警时间">
          {{ formatDate(alarmDetail.alarmTime, fullDateFormat) }}
        </el-descriptions-item>
        <el-descriptions-item label="生产订单号">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>
          {{ alarmDetail.ipoNo }}
        </el-descriptions-item>
        <el-descriptions-item label="销售订单号">
          {{ alarmDetail.soNo }}
        </el-descriptions-item>
        <el-descriptions-item label="采购订单号">
          {{ alarmDetail.poNo }}
        </el-descriptions-item>
        <el-descriptions-item label="持续时长">
          {{ convertTime(alarmDetail.duration) }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="describe-content">
        <el-descriptions-item label="告警原因">
          <span v-html="content" />
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mb-8">
      <div class="header">
        <TitleBar title="解决告警" />
      </div>
      <el-descriptions class="describe-content">
        <el-descriptions-item label="解决状态">
          <AlarmSolveStatusEnumComponent :status="alarmDetail.solveStatus" />
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="describe-content !w-full">
        <el-descriptions-item label="备注">
          {{ alarmDetail.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mb-5">
      <div class="header">
        <TitleBar title="采集点详情" />
      </div>

      <el-descriptions class="describe-content" :column="3">
        <el-descriptions-item label="报工批次号">
          {{ alarmDetail.reportNo }}
        </el-descriptions-item>
        <el-descriptions-item label="工序">
          {{ alarmDetail.processName }}
        </el-descriptions-item>

        <el-descriptions-item label="设备编号">
          {{ alarmDetail.deviceCode }}
        </el-descriptions-item>
      </el-descriptions>
      <div class="mr-4" v-loading="chartLoading">
        <div class="mt-2 chart-group" v-if="experimentData.length">
          <el-card
            v-for="(item, index) in experimentData"
            :key="item.collectPoint"
            class="shadow-md mb-5 min-w-96"
            @click="toggleDialog(index)"
          >
            <div>{{ item.collectName }}</div>
            <div class="h-[200px]">
              <AutoCollectChart :data="item" />
            </div>
          </el-card>
          <el-dialog
            :title="focusData ? focusData.collectName : '--'"
            align-center
            class="middle"
            destroy-on-close
            v-model="dialogVisible"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
          >
            <div class="h-[280px]">
              <AutoCollectChart :data="focusData" :data-zoom="true" />
            </div>
          </el-dialog>
        </div>
        <CxEmpty v-else />
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { computed, reactive, watchEffect, watch, ref } from "vue";
import { IAlarmDataDetail, IAutoCollectChartReq, IDeviceAcquisition, IExperimentData } from "@/models";
import { useAlarmDataStore, useDeviceAcquisitionStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import AlarmSolveStatusEnumComponent from "../../component/alarm-solve-status.vue";
import AutoCollectChart from "@/views/components/auto-collection-charts/auto-collection-charts.vue";
import { highLight } from "../../untils/high-light-alarm-message";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { convertTime } from "../../untils/conver-time";
import CxEmpty from "@/components/CxEmpty";
import { useCancelHttp } from "@/utils/http/cancel-http";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

const props = defineProps<{
  loading: boolean;
}>();
const alarmDetail = reactive<Partial<IAlarmDataDetail>>({
  deviceId: ""
});
let experimentData = reactive<Array<IExperimentData>>([
  {
    collectPoint: "",
    values: [],
    collectName: ""
  }
]);
const alarmDataStore = useAlarmDataStore();
const productionProcessInspecStore = useProductionProcessInspecStore();
const chartLoading = ref<boolean>(false);
const cancelHttp = useCancelHttp();
const qeviceAcquisitionStore = useDeviceAcquisitionStore();
const focusData = ref<IExperimentData | null>(null);
const dialogVisible = ref(false);

//告警时间前后20分钟请求采集点数据
const timeRange = 20 * 60 * 1000;
watchEffect(() => {
  if (alarmDataStore.alarmDataDetail) {
    Object.assign(alarmDetail, alarmDataStore.alarmDataDetail);
  }
});

watch(
  () => alarmDataStore.alarmDataDetail.deviceCode,
  async (newVal, oldVal) => {
    if (oldVal) {
      cancelHttp.abort();
      experimentData.length = 0;
      return;
    }
    const params: IAutoCollectChartReq = {
      deviceCode: alarmDetail.deviceCode,
      timeFrom: new Date(new Date(alarmDetail.alarmTime).getTime() - timeRange).toISOString(),
      timeEnd: new Date(new Date(alarmDetail.alarmTime).getTime() + timeRange).toISOString()
    };
    await getHistoryAutoCollect(params);
  }
);

/**
 * @description: 切换弹窗显示
 */
const toggleDialog = (index: number) => {
  dialogVisible.value = !dialogVisible.value;
  focusData.value = experimentData[index];
};

/** 获取自动采集项数据 --历史 */
async function getHistoryAutoCollectCharts(params: IAutoCollectChartReq) {
  const pointList: Array<IDeviceAcquisition> =
    (await qeviceAcquisitionStore.queryAcquisitionAll(alarmDetail.deviceId)) || [];
  const historyExperiment: Array<IExperimentData> = [];
  if (!pointList.length) {
    return;
  }
  pointList.forEach(point => {
    if (point.no === alarmDetail.pointNo) {
      historyExperiment.push({
        collectPoint: point.no,
        values: [],
        collectName: point.name
      });
    }
  });
  const charts = await productionProcessInspecStore
    .getHistoryAutoCollectCharts(params, cancelHttp.signal.value)
    .catch(() => []);
  if (charts?.length) {
    Object.assign(
      experimentData,
      charts.filter(chart => chart.collectPoint == alarmDetail.pointNo)
    );
  }

  historyExperiment.forEach(experiment => {
    const index = charts.findIndex(chart => chart.collectPoint === experiment.collectPoint);
    if (index > -1) {
      experiment.values = charts[index].values;
    }
  });
  experimentData = Object.assign(experimentData, historyExperiment);
}
const content = computed(() => highLight(alarmDetail?.message));
const getHistoryAutoCollect = useLoadingFn(getHistoryAutoCollectCharts, chartLoading);
</script>

<style scoped lang="scss">
.describe-content {
  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    align-items: baseline;
  }

  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell .el-descriptions__content) {
    line-height: 1.5;
  }
}

.header {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;
}
</style>
