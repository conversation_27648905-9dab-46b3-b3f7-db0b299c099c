import { getLogDetailList } from "@/api/logging/operate-log";
import { IOperateLogDetail, IOperateLogReq } from "@/models/logging";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

export const useLogDetailStore = defineStore({
  id: "log-detail-store",
  state: () => ({
    queryParams: {} as IOperateLogReq,
    tableTotal: 0,
    logDetailTableData: [] as Array<IOperateLogDetail>
  }),
  actions: {
    /** 查询日志汇总列表 */
    async queryLogDetailList(params: IOperateLogReq) {
      this.queryParams = { ...this.queryParams, ...params };
      await this.getLogDetailTableData();
    },

    /**
     * 获取日志汇总列表
     */
    async getLogDetailTableData() {
      const queryParams = omitBy(this.queryParams, value => isAllEmpty(value));
      const res = await getLogDetailList(queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.logDetailTableData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.logDetailTableData = [];
        this.tableTotal = 0;
      }
    }
  }
});
