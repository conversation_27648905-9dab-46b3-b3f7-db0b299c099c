<template>
  <div>
    <div class="mb-5">
      <TitleBar title="基础信息" class="mb-2" />
      <el-descriptions>
        <el-descriptions-item label="生产订单号">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>

          {{ productOrder.ipoNo }}
        </el-descriptions-item>
        <el-descriptions-item label="生产数量">
          <template v-if="productOrder.amount">
            {{ formatDecimal(productOrder.amount) }}
            <DictionaryName
              class="ml-1"
              :subClassCode="productOrder.subclassCode"
              :parentCode="MEASURE_UNIT"
              :code="productOrder.unit"
            />
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="生产订单状态">
          <div class="flex items-center h-full">
            <EnumTag :enum="ProductionStateEnum" enumName="productionStateEnum" :value="productOrder.ipoStatus" />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="计划日期">
          {{ planRange }}
        </el-descriptions-item>
        <el-descriptions-item label="实际开始日期">
          {{ formatDate(productOrder.actualStartDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="实际完成日期">
          {{ formatDate(productOrder.actualFinishDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="电压等级">
          {{ formatEnum(productOrder.voltageClasses, VoltageClassesEnum, "voltageClassesEnum") }}
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="规格型号">
          {{ productOrder.specificationModel }}
        </el-descriptions-item>
        <el-descriptions-item label="发货数量">
          {{ productOrder.quantityShipped }}
        </el-descriptions-item>
        <el-descriptions-item label="入库数量">
          {{ productOrder.quantityWarehousing }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ productOrder.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mb-4">
      <TitleBar title="销售订单行项目" class="mb-2" />
      <pure-table row-key="id" :data="data" :columns="columns">
        <template #empty>
          <CxEmpty />
        </template>
      </pure-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { useColumns } from "./columns";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { computed } from "vue";
import { useSalesProductOrderStore } from "@/store/modules";
import { ProductionStateEnum, VoltageClassesEnum, KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";
import { formatDate, formatDecimal, formatEnum } from "@/utils/format";
import EnumTag from "@/components/EnumTag/EnumTag.vue";
import { IProductOrder } from "@/models";
import { MEASURE_UNIT } from "@/consts";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";

const store = useSalesProductOrderStore();

const { columns } = useColumns();

const productOrder = computed(() => {
  if (!store.productOrderDetail) {
    return {} as IProductOrder;
  }
  return store.productOrderDetail;
});
const data = computed(() => {
  const { soNo, soItemNo, materialsCode, materialsName, materialsUnit, subClassCode } = productOrder.value;
  return [{ soNo, soItemNo, materialsCode, materialsName, materialsUnit, subClassCode }];
});

const planRange = computed(() => {
  const { planStartDate, planFinishDate } = productOrder.value;
  const startDateStr = formatDate(planStartDate);
  const finishDateStr = formatDate(planFinishDate);
  if (!startDateStr && !finishDateStr) {
    return null;
  }
  return `${startDateStr || ""} ～ ${finishDateStr || ""}`;
});
</script>
