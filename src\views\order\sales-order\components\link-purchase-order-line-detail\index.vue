<template>
  <ElForm :inline="true" :model="state.params">
    <ElFormItem label="物料搜索：">
      <ElInput class="!w-[400px]" v-model="state.materialText" clearable placeholder="请输入物料编码/物料描述" />
    </ElFormItem>
    <ElFormItem>
      <ElButton type="primary" @click="onConfirmQuery()">搜索</ElButton>
    </ElFormItem>
  </ElForm>
  <PureTable
    ref="tableRef"
    class="pagination tooltip-max-w mt-1"
    height="400"
    :row-key="getRowKeyOfTarget"
    :data="state.salesLineLinkPurchaseOrders"
    :columns="columns"
    showOverflowTooltip
    :loading="loading"
    :pagination="pagination"
    @page-size-change="onPageSizeChange"
    @page-current-change="onCurrentPageChange"
    @selection-change="handleSelectionChange"
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
  </PureTable>
  <OrderSelectPoNo :multipleSelection="multipleSelectPoNo" @deleteTag="deleteTag" :title="'已选采购订单行号：'" />
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useColumns } from "./columns";
import { reactive, onMounted, onUnmounted, ref, watch, computed } from "vue";
import { IPurchaseLines, IPurchaseOderLine, ISaleLineLinkPurchaseLineParams, ISalesLinkPurchaseOrder } from "@/models";
import { cloneDeep, omit, find } from "lodash-unified";
import { useSalesOrderLineManagementStore, useSalesOrderDetailStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import OrderSelectPoNo from "../order-select-pono/index.vue";
import { useSalesOrderLineHook } from "../../hooks/sales-order-line-hook";
import { useRoute } from "vue-router";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";

defineExpose({
  getsalesLineLinkPurchaseItemIds
});

const props = defineProps<{
  purchaseItem: Array<IPurchaseLines>;
  subClassCode: string;
}>();
const route = useRoute();

const state = reactive<{
  materialText: string;
  params: ISaleLineLinkPurchaseLineParams;
  salesLineLinkPurchaseOrders: Array<IPurchaseOderLine>;
  multipleSelection: Array<IPurchaseLines>;
  saleId: string;
}>({
  materialText: "",
  params: {
    subClassCode: props.subClassCode
  },
  salesLineLinkPurchaseOrders: [],
  multipleSelection: [],
  saleId: `${route.params?.id}`
});

const { columns } = useColumns();
const { pagination } = useTableConfig();
const tableRef = ref<PureTableInstance>();
const loading = ref<boolean>(false);

const salesOrderLineManagementStore = useSalesOrderLineManagementStore();
const salesOrderDetailStore = useSalesOrderDetailStore();

const { querySaleLineLinkPurchaseOrderLine } = useSalesOrderLineHook();
const getSaleLineLinkPurchaseOrderLine = useLoadingFn(querySaleLineLinkPurchaseOrderLine, loading);

const onPageSizeChange = async () => {
  state.salesLineLinkPurchaseOrders = await getSaleLineLinkPurchaseOrderLine(state.saleId, queryParams());
};
const onCurrentPageChange = async () => {
  state.salesLineLinkPurchaseOrders = await getSaleLineLinkPurchaseOrderLine(state.saleId, queryParams());
};

const multipleSelectPoNo = computed(() => {
  return state.multipleSelection
    .filter(selectPoNo => selectPoNo.poItemNo && selectPoNo.poNo)
    .map(select => `${select.poNo} - ${select.poItemNo}`);
});
onMounted(async () => {
  state.salesLineLinkPurchaseOrders = await getSaleLineLinkPurchaseOrderLine(state.saleId, queryParams());
});

onUnmounted(() => {
  state.multipleSelection.length = 0;
});
const onConfirmQuery = async () => {
  state.salesLineLinkPurchaseOrders = await getSaleLineLinkPurchaseOrderLine(state.saleId, queryParams());
};

const handleSelectionChange = (val: Array<IPurchaseOderLine>) => {
  state.multipleSelection = val.map(salesLinkPurchaseOrder => ({
    purchaseLineId: salesLinkPurchaseOrder.id,
    purchaseId: salesLinkPurchaseOrder.purchaseId,
    poItemId: salesLinkPurchaseOrder.poItemId,
    poItemNo: salesLinkPurchaseOrder.poItemNo,
    poNo: salesLinkPurchaseOrder.poNo,
    conName: salesLinkPurchaseOrder.conName
  }));
};

const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  props.purchaseItem.length
    ? (state.params.purchaseItemId = props.purchaseItem.map(select => select.purchaseLineId))
    : (state.params = omit(state.params, "purchaseItemId"));
  const params = cloneDeep(state.params);
  if (
    salesOrderDetailStore.salesOrder.matSyncFlagId &&
    salesOrderDetailStore.salesOrder.matSyncFlagId === SynchronousFlagEnum.CSG_GuangZhou_Flag
  ) {
    delete params.subClassCode;
  }
  state.materialText
    ? (params.orFilters = [{ material_code: state.materialText }, { material_desc: state.materialText }])
    : omit(params, "orFilters");
  return params;
};

watch(
  () => state.salesLineLinkPurchaseOrders,
  (newValue, oldValue) => {
    if (props.purchaseItem) {
      if (!oldValue.length) {
        state.multipleSelection = props.purchaseItem;
      }
      props.purchaseItem.forEach(select => {
        const index = state.salesLineLinkPurchaseOrders.findIndex(
          salesLinkPurchase => salesLinkPurchase.poItemNo == select.poItemNo
        );
        const selectRows: Array<ISalesLinkPurchaseOrder> = tableRef.value?.getTableRef()?.getSelectionRows();
        if (selectRows?.length && find(selectRows, { poItemNo: select.poItemNo })) {
          return;
        }
        if (index > -1) {
          tableRef.value?.getTableRef()?.toggleRowSelection(state.salesLineLinkPurchaseOrders[index], true);
        }
      });
    }
  }
);
watch(
  () => salesOrderLineManagementStore.saleLineLinkPurchaseOrderLineTotal,
  () => {
    pagination.total = salesOrderLineManagementStore.saleLineLinkPurchaseOrderLineTotal;
  },
  {
    immediate: true
  }
);

function getsalesLineLinkPurchaseItemIds(): Array<IPurchaseLines> {
  return state.multipleSelection;
}

function getRowKeyOfTarget(row: IPurchaseOderLine) {
  return `${row.id}-${row.poItemNo}`;
}

const deleteTag = (val: string) => {
  const poItemNo = val.split("-")[1].trim();
  const poNo = val.split("-")[0].trim();
  state.multipleSelection = state.multipleSelection.filter(
    multipleSelection => multipleSelection.poItemNo != poItemNo && multipleSelection.poNo != poNo
  );
  const index = state.salesLineLinkPurchaseOrders.findIndex(
    salesLinkPurchase => salesLinkPurchase.poItemNo == poItemNo && salesLinkPurchase.poNo == poNo
  );
  if (index > -1) {
    tableRef.value?.getTableRef()?.toggleRowSelection(state.salesLineLinkPurchaseOrders[index], false);
  }
};
</script>

<style scoped lang="scss"></style>
