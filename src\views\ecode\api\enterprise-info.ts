/**
 * @description: 企业信息
 */

import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { ICompany } from "../models";

/**
 * @description: 获取企业信息详情
 */
export const getCompanyInfo = (tenantId: string) => {
  const url: string = withApiGateway(`admin-api/ecode/companyInfo/getInfo?tenantId=${tenantId}`);
  return http.get<any, IResponse<ICompany>>(url);
};

/**
 * @description: 编辑企业信息
 */
export const editCompanyInfo = (data: ICompany) => {
  const url: string = withApiGateway(`admin-api/ecode/companyInfo/update`);
  return http.put<ICompany, IResponse<boolean>>(url, { data });
};
