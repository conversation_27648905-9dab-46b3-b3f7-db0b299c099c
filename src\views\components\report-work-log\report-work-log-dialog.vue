<template>
  <div class="report-work" @click="onToggleeReportWorkLogDialogbVisible(true)">
    <slot name="report-work" />
  </div>
  <el-dialog
    v-model="state.visible"
    title="报工详情"
    align-center
    width="60%"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <ReportWorkLog :id="props.id" />
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import ReportWorkLog from "./index.vue";

const props = withDefaults(
  defineProps<{
    id?: string;
  }>(),
  {}
);

const state = reactive<{
  visible: boolean;
}>({
  visible: false
});
const onToggleeReportWorkLogDialogbVisible = (visible: boolean) => {
  state.visible = visible;
};
</script>

<style scoped lang="scss">
.report-work {
  color: var(--el-color-primary);

  &:hover {
    cursor: pointer;
  }
}
</style>
