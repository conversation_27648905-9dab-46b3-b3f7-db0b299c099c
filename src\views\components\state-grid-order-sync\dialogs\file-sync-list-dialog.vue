<template>
  <el-dialog class="large" v-model="visible" title="文件同步详情" destroy-on-close>
    <FileSyncList :data="files" :columns="columns" v-bind="$attrs" />
  </el-dialog>
</template>

<script setup lang="ts">
import FileSyncList from "@/views/components/state-grid-order-sync/components/file-sync-list.vue";
import { IFileSync, ISyncCommon } from "@/models";
import { computed } from "vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { StateGridOrderSyncType, TableWidth } from "@/enums";

const props = defineProps<{
  modelValue: boolean;
  files: Array<IFileSync>;
  hideOperation?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "sync", data: ISyncCommon): void;
  (e: "syncDetailEvent", data: ISyncCommon): void;
}>();

const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type: StateGridOrderSyncType.RAW_MATERIAL_INSPECTION,
  syncFn: data => emit("sync", data),
  syncDetailFn: syncDetailFn
});

const files = computed(() => props.files || []);
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(visible: boolean) {
    emit("update:modelValue", visible);
  }
});
const columns = computed<TableColumnList>(() => [
  {
    label: "文件",
    prop: "fileName",
    width: TableWidth.name
  },
  ...normalColumns,
  ...(props.hideOperation ? [] : [operatorColumn])
]);

function syncDetailFn(data: ISyncCommon) {
  emit("syncDetailEvent", data);
}
</script>

<style scoped></style>
