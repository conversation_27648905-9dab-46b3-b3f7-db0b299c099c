import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IStartWork, IStartWorkForm, IStartWorkReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryStartWork = (data: IStartWorkReq) => {
  const url: string = withApiGateway("admin-api/southgrid/startWorks/pageList");
  return http.post<IStartWorkReq, IListResponse<IStartWork>>(url, {
    data
  });
};

/** 查询分页  */
export const queryStartWorkList = (data: IStartWorkReq) => {
  const url: string = withApiGateway("admin-api/southgrid/startWorks/list");
  return http.post<IStartWorkReq, IResponse<Array<IStartWork>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getStartWorkById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/startWorks/${id}`);
  return http.get<string, IResponse<IStartWork>>(url);
};

/** 新增 */
export const createStartWork = (data: IStartWorkForm) => {
  return http.post<IStartWorkForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/startWorks/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateStartWork = (data: IStartWorkForm) => {
  return http.put<IStartWorkForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/startWorks/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteStartWorkById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/startWorks/${id}`));
};
