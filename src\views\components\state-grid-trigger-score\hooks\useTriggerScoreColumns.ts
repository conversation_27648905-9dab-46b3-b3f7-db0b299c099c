import { ITriggerScoreProductOrder } from "@/views/components/state-grid-trigger-score/tokens";
import { TableColumns } from "@pureadmin/table";
import { OrderType, StateGridOrderTriggerStatus, TableWidth } from "@/enums";
import { dateFormat, PermissionKey, TrackPointKey } from "@/consts";
import { ITableOperator, OperatorCell } from "@/components/TableCells";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { IStateGridRateTrigger } from "@/models";
import { usePurchaseOrderDetailStore } from "@/store/modules";

export function useTriggerScoreColumns(ctx: ITriggerScoreProductOrder) {
  const purchaseOrderStore = usePurchaseOrderDetailStore();
  const { dateFormatter, statusFormatter, enumFormatter } = useTableCellFormatter();
  const sortable = (ctx.dateSupportOrder && "custom") as false | "custom";

  const triggerScoreColumns: Array<TableColumns> = [
    {
      label: "实际开始日期",
      prop: "actualStartDate",
      width: TableWidth.dateTime,
      formatter: dateFormatter(dateFormat)
    },
    {
      label: "实际结束日期",
      prop: "actualFinishDate",
      width: TableWidth.dateTime,
      formatter: dateFormatter(dateFormat)
    },
    {
      sortable,
      label: "最后更新时间",
      prop: "lastUpdateTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      sortable,
      label: "最后触发时间",
      prop: "lastTriggerTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "订单同步状态",
      prop: "syncResult",
      fixed: "right",
      width: TableWidth.type,
      formatter: statusFormatter("已触发同步", "同步失败", "未同步")
    },
    {
      label: "触发评分状态",
      prop: "status",
      width: TableWidth.type,
      fixed: "right",
      formatter: enumFormatter(StateGridOrderTriggerStatus, "StateGridOrderTriggerStatus")
    },
    {
      label: "可能原因",
      prop: "reason",
      fixed: "right",
      width: TableWidth.reason
    },
    {
      label: "操作",
      fixed: "right",
      width: TableWidth.largeOperation,
      cellRenderer: data => {
        const actions: Array<ITableOperator> = [
          {
            name: "触发质量评分",
            action: () => openSyncAuditDialog(data.row),
            props: { type: "primary" },
            permissionKey: PermissionKey.form.formPurchaseTriggerScoreBtnTrigger,
            trackKey: TrackPointKey.FORM_PURCHASE_TRIGGER_ITEM
          }
        ];
        if (data.row.status !== StateGridOrderTriggerStatus.SUCCESS) {
          actions.unshift({
            name: "修改数据",
            action: () => openEditDialog(data.row),
            props: { type: "primary" },
            permissionKey: PermissionKey.form.formPurchaseTriggerScoreEdit,
            trackKey: TrackPointKey.FORM_PURCHASE_TRIGGER_EDIT
          });
        }
        return OperatorCell(actions);
      }
    }
  ];

  async function fetchPurchaseOrderIfNeed(purchaseId: string) {
    if (purchaseOrderStore.purchaseOrder?.id === purchaseId) {
      return;
    }
    purchaseOrderStore.setPurchaseOrderId(purchaseId);
    await purchaseOrderStore.refreshPurchaseOrder();
  }

  async function openEditDialog(data: IStateGridRateTrigger) {
    await fetchPurchaseOrderIfNeed(data.purchaseId);
    ctx.editVisible = true;
    ctx.editData = data;
  }

  async function openSyncAuditDialog(data: IStateGridRateTrigger) {
    await fetchPurchaseOrderIfNeed(data.purchaseId);
    ctx.editData = data;
    ctx.syncAuditVisible = true;
    ctx.syncAuditBaseInfo = {
      isCable: purchaseOrderStore.isCable,
      isArmourClamp: purchaseOrderStore.isArmourClamp,
      // 触发评分暂时不需要订单id，后面需要的时候需要改为正确的值
      orderType: OrderType.PURCHASE,
      orderId: purchaseOrderStore.purchaseOrderId,
      purchaseLineId: data.purchaseLineId,
      poNo: purchaseOrderStore.purchaseOrder.poNo,
      poItemNo: data.poItemNo,
      materialCode: data.materialCode,
      materialDesc: data.materialDesc,
      subClassCode: data.subClassCode
    };
  }

  return {
    triggerScoreColumns
  };
}
