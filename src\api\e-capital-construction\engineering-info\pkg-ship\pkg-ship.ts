import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, IPkgShip, IPkgShipForm } from "@/models";

/** 查询分页  */
export const queryPkgShip = (id: string) => {
  const url: string = withApiGateway(`admin-api/ejj/project/pkg-ship/list/${id}`);
  return http.get<void, IResponse<Array<IPkgShipForm>>>(url);
};

/** 根据id 查询详情 */
export const getPkgShipById = (id: string) => {
  const url: string = withApiGateway(`/admin-api/ejj/project/pkg-ship/${id}`);
  return http.get<string, IResponse<IPkgShip>>(url);
};

/** 新增 */
export const createPkgShip = (data: IPkgShipForm) => {
  return http.post<IPkgShipForm, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/pkg-ship"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updatePkgShip = (data: IPkgShipForm) => {
  return http.put<IPkgShipForm, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/pkg-ship"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deletePkgShipById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`/admin-api/ejj/project/pkg-ship/${id}`));
};
