/**
 * @description: 喷码机管理
 */

import { http } from "@/utils/http";
import { IListResponse, IPagingReq, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { PrintMachine, PrintMachineFactory } from "../models/i-print-machine-manage";

/**
 * @description: 获取喷码机列表
 */
export const getPrintMachineList = (data: IPagingReq) => {
  const url: string = withApiGateway(`admin-api/ecode/printerMachine/list`);
  return http.get<IPagingReq, IListResponse<PrintMachine>>(url, { params: data });
};

/**
 * @description: 获取喷码机详情
 */
export const getPrintMachineDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/printerMachine/detail`);
  return http.get<any, IResponse<PrintMachine>>(url, { params: { id } });
};

/**
 * @description: 添加喷码机
 */
export const addPrintMachine = (data: FormData) => {
  const url: string = withApiGateway(`admin-api/ecode/printerMachine/add`);
  return http.post<FormData, IResponse<boolean>>(
    url,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * @description: 编辑喷码机
 */
export const editPrintMachine = (data: FormData) => {
  const url: string = withApiGateway(`admin-api/ecode/printerMachine/update`);
  return http.put<FormData, IResponse<boolean>>(
    url,
    { data },
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  );
};

/**
 * @description: 删除喷码机
 */
export const deletePrintMachine = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/printerMachine/delete`);
  return http.delete<string, IResponse<boolean>>(url, { params: { id } });
};

/**
 * @description: 获取喷码机厂家列表
 */
export const getPrintMachineFactoryList = () => {
  const url: string = withApiGateway(`admin-api/ecode/printerFactory/pullDownList`);
  return http.get<any, IResponse<PrintMachineFactory[]>>(url);
};
