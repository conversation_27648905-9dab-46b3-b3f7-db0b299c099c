import { ProductionStateEnum, VoltageClassesEnum } from "@/enums";
import { IDictionaryOption } from "../platform";

/**工单模型 */
export interface IWorkOrder {
  id: string;
  /**	采购订单ID */
  purchaseId: string;

  /**产品编号  */
  productCode: string;

  /** 供应商生产订单编号  */
  ipoNo: string;

  /** 销售订单行Id */
  salesLineId: string;
  /** 	供应商编码 */
  supplierCode: string;
  /** 	供应商名称 */
  supplierName: string;

  /** 	生产工单编码 */
  woNo: string;
  /** 	品类编码 */
  categoryCode: string;

  /** 种类编码 */
  subclassCode: string;

  /** 种类编码 */
  subClassCode?: string;

  /** 厂家物料编码 */
  materialsCode: string;
  /** 	物料名称 */
  materialName: string;
  /**	物料描述  */
  materialDesc: string;

  /** 	物料单位 */
  materialUnit: string;

  /** 	生产数量 */
  amount: number;
  /**  计量单位 */
  unit: string;

  /** 计划开始日期 */
  planStartDate: Date;
  /** 	计划完成日期 */
  planFinishDate: Date;
  /** 实际开始日期 */
  actualStartDate: Date;

  /** 实际完成日期 */
  actualFinishDate: Date;

  /** 工单状态  */
  woStatus?: ProductionStateEnum;

  /** 工序名称 -分割 ? */
  processName: string;

  /** 工序Ids -分割 ? */
  processIds: string;
  /** 规格型号 */
  specificationType: string;

  /** 	电压等级 */
  voltageLevel: VoltageClassesEnum;

  /** 销售订单号 */
  soNo: string;

  /** 销售订单行项目 */
  soItemNo: string;

  /** 报工条数 */
  workCount: number;

  /** 生成订单Id */
  productionId: string;

  /** 单位字典 */
  unitDictionary?: IDictionaryOption;

  /** 物资子种类编码 */
  minClassCode?: string;
  /** 物资子种类名称 */
  minClassName?: string;
  /** 产品名称 */
  productName?: string;
  /** 质量追溯码 */
  qualityTraceCode?: string;
  /** 是否缺失必填数据 */
  missingData?: boolean;

  /** 实物Id */
  entityId?: string;
}
