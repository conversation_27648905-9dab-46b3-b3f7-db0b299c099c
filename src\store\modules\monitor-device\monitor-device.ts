import { IListResponse, IMonitorDevice, IMonitorDeviceForm, IMonitorDeviceReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useMonitorDeviceStore = defineStore({
  id: "cx-monitor-device",
  state: () => ({
    total: 0,
    monitorDevices: [] as Array<IMonitorDevice>
  }),
  actions: {
    /** 查询监控设备列表数据 */
    async queryMonitorDevices(params?: IMonitorDeviceReq) {
      const res: IListResponse<IMonitorDevice> = await api.queryMonitorDevices(params);
      this.total = res.data.total;
      this.monitorDevices = res.data.list;
    },

    /** 根据ID查询监控设备详情 */
    async getMonitorDeviceDetailById(id?: string) {
      return (await api.getMonitorDeviceDetailById(id)).data;
    },

    /** 创建监控设备 */
    async createMonitorDevice(params: IMonitorDeviceForm) {
      return (await api.createMonitorDevice(params)).data;
    },

    /** 编辑监控设备 */
    async editMonitorDevice(params: IMonitorDeviceForm) {
      return (await api.editMonitorDevice(params)).data;
    },

    /** 删除监控设备 */
    async deleteMonitorDevice(id: string) {
      return (await api.deleteMonitorDevice(id)).data;
    },

    async getMonitorDeviceWatchUrlById(id: string) {
      return await api.getMonitorDeviceWatchUrlById(id);
    },

    /**
     * @description: 监控设备同步到IOT
     */
    async syncMonitorDeviceById(id: string) {
      return await api.syncMonitorDeviceByID(id);
    },

    async checkAllMonitorDeviceRunStatus() {
      return await api.checkAllMonitorDeviceRunStatus();
    },

    async getMonitorDeviceLastRefreshTime() {
      return await api.getMonitorDeviceLastRefreshTime();
    }
  }
});
