import {
  IListResponse,
  ITechnicalStandardLibrary,
  ITechnicalStandardLibraryReqParams,
  IResponse,
  ICollectionItem,
  ITechnicalStandardLibraryDetail,
  IProcessData,
  ICollectionParams,
  IBatchCollectionItem,
  IBatchCollectionProperty
} from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";
import { map, pick, mapValues, isNull } from "lodash-unified";
export const useTechnicalStandardLibraryStore = defineStore({
  id: "cx-technical-standard-library",
  state: () => ({
    total: 0,
    technicalStandardLibrary: {} as ITechnicalStandardLibraryDetail,
    collectionItems: [] as Array<ICollectionItem>
  }),
  actions: {
    // 查询技术标准库列表数据
    async queryTechnicalStandardLibrary(params: ITechnicalStandardLibraryReqParams) {
      const res: IListResponse<ITechnicalStandardLibrary> = await api.queryTechnicalStandardLibrary(params);
      this.total = res.data.total;
      return res.data.list;
    },

    // 根据ID查询技术标准库详情
    async getTechnicalStandardLibraryById(id: string) {
      const res: IResponse<ITechnicalStandardLibraryDetail> = await api.getTechnicalStandardLibraryDetailById(id);
      return res.data;
    },

    // 创建技术标准库
    async createTechnicalStandardLibrary(params: ITechnicalStandardLibraryDetail) {
      const res: IResponse<string> = await api.createTechnicalStandardLibrary(params);
      return res.data;
    },

    // 编辑技术标准库
    async editTechnicalStandardLibrary(params: ITechnicalStandardLibraryDetail) {
      const res: IResponse<boolean> = await api.editTechnicalStandardLibrary(params);
      return res.data;
    },

    // 技术标准库详情赋值
    setTechnicalStandardLibraryStorage(technicalStandardLibrary: ITechnicalStandardLibraryDetail) {
      this.technicalStandardLibrary = technicalStandardLibrary;
    },
    // 技术标准库详情清空
    clearTechnicalStandardLibraryStorage() {
      this.technicalStandardLibrary = {};
    },

    // 根据物资种类下的生产工艺及过程检验的生产工序
    async getProductProcessByStandardId(id: string) {
      const res: IResponse<Array<IProcessData>> = await api.getProductProcessByStandardId(id);
      return res.data;
    },

    // 根据物资品类获取技术标准详情列表（当后端在最大值和最小值是否包含时，null转为空，el-checkbox的显示问题）
    async queryStandardLibraryCollectionPoint(params: ICollectionParams) {
      const res: IResponse<Array<ICollectionItem>> = await api.queryStandardLibraryCollectionPoint(params);
      const collectionItems = res.data || [];
      this.collectionItems = map(collectionItems, collectionItem =>
        mapValues(collectionItem, (value, key) =>
          isNull(value) && (key == "includeMaxValue" || key == "includeMinValue") ? "" : value
        )
      ) as Array<ICollectionItem>;
      return this.collectionItems;
    },

    getCollectionItems() {
      return this.collectionItems;
    },

    async batchUpdateCollectionItems(collectionItems: Array<ICollectionItem>) {
      const property: Array<string> = IBatchCollectionProperty;
      const batchCollectionItems: Array<IBatchCollectionItem> = map(
        collectionItems,
        collectionItem => pick(collectionItem, property) as IBatchCollectionItem
      );
      const res: IResponse<string> = await api.batchUpdateCollectionItems(batchCollectionItems);
      return res.data;
    },
    /**  对技术标准 进行同步 */
    async standardLibrarySync() {
      const res: IResponse<boolean> = await api.standardLibrarySync();
      return res.data;
    }
  }
});
