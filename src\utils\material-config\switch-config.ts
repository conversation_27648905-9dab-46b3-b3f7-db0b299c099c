import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 组合电器
 */
export const combinationApparatusConfig: MaterialSubClassConfig = {
  name: "组合电器",
  subClassCode: MaterialSubclassCode.COMBINATION_APPARATUS,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 断路器
 */
export const circuitBreakerConfig: MaterialSubClassConfig = {
  name: "断路器",
  subClassCode: MaterialSubclassCode.CIRCUIT_BREAKER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 电容器
 */
export const capacitorConfig: MaterialSubClassConfig = {
  name: "电容器",
  subClassCode: MaterialSubclassCode.CAPACITOR,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 隔离开关
 */
export const disconnectorConfig: MaterialSubClassConfig = {
  name: "隔离开关",
  subClassCode: MaterialSubclassCode.DISCONNECTOR,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 物资品类-配网线缆
 */
export const switchConfig: MaterialCategoryConfig = {
  name: "配网线缆",
  categoryCode: MaterialCategoryCode.SWITCH,
  subClassMap: {
    combinationApparatusConfig,
    [MaterialSubclassCode.COMBINATION_APPARATUS]: combinationApparatusConfig,
    circuitBreakerConfig,
    [MaterialSubclassCode.CIRCUIT_BREAKER]: circuitBreakerConfig,
    capacitorConfig,
    [MaterialSubclassCode.CAPACITOR]: capacitorConfig,
    disconnectorConfig,
    [MaterialSubclassCode.DISCONNECTOR]: disconnectorConfig
  }
};

export default switchConfig;
