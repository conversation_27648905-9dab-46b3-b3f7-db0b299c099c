import { KeywordAliasEnum, KeywordAliasEnumMapDesc, TableWidth } from "@/enums";
import { IJfnyExperimentList } from "@/models/production-test-sensing/i-out-going-factory-jfny-experiment";
import { previewUploadFile } from "@/utils/uploadFiles";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";
import { useJfnyHook } from "@/views/leave-factory/hook/useJfnyhook";
import { TableColumnRenderer } from "@pureadmin/table";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const { formateVoltageLevelType } = useJfnyHook();
  const columns: TableColumnList = [
    {
      label: "试验编号",
      prop: "experimentNo",
      minWidth: TableWidth.largeOrder
    },
    {
      label: "成品编号",
      prop: "finProNo",
      minWidth: TableWidth.order
    },
    {
      label: "生产订单号",
      prop: "ipoNos",
      headerRenderer: () => (
        <KeywordAliasHeader
          code={KeywordAliasEnum.IPO_NO}
          defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
        />
      ),
      minWidth: TableWidth.order
    },
    {
      label: "电压类型",
      prop: "finishedVoltageLevelType",
      minWidth: TableWidth.type,
      formatter: row => {
        return formateVoltageLevelType(row);
      }
    },
    {
      label: "电压等级",
      prop: "finishedVoltageLevel",
      minWidth: TableWidth.type
    },
    {
      label: "产品型号",
      prop: "productModel",
      minWidth: TableWidth.order
    },
    {
      label: "开始时间",
      prop: "startTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "结束时间",
      prop: "endTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "盘号",
      prop: "reelNo",
      minWidth: TableWidth.order
    },
    {
      label: "试验报告",
      prop: "fileInfo",
      minWidth: TableWidth.largeName,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div class="cursor-pointer text-primary" onClick={() => previewReport(data.row)}>
            <span>{data.row?.reportFileName || "--"}</span>
          </div>
        );
      }
    }
  ];

  // 浏览报告
  const previewReport = (reportFile: IJfnyExperimentList) => {
    if (reportFile?.reportFileId) {
      const { reportFileId: id, reportFileName: name, reportFileUrl: url } = reportFile;
      previewUploadFile({ id, name, url });
    }
  };

  return {
    columns
  };
}
