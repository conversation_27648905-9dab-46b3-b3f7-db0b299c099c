<template>
  <div class="">
    <div class="status">
      <span class="title">{{ title }}</span>
    </div>
    <SearchForm
      keyword-filter-key="orFilters"
      :keyword-fields="keywordFields"
      :search-form="form"
      :search-items="items"
      :size="size"
      :placeholder="placeholder"
      @search="onSearch()"
    >
      <el-button
        v-auth="PermissionKey.form.formPurchaseProductionOrderCreate"
        v-track="TrackPointKey.FORM_PURCHASE_PO_CREATE"
        @click="onProductOrderModalVisible()"
        type="primary"
        :icon="Plus"
        size="default"
      >
        新增生产订单
      </el-button>
    </SearchForm>
  </div>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm";
import { IKeywordField, ISearchItem, SizeType } from "@/components/SearchForm";
import { ElDatePicker, ElInput, ElOption, ElSelect } from "element-plus";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { computed, reactive, defineComponent, h } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey, TrackPointKey } from "@/consts";
import { IProductOrderReq } from "@/models";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateoption } from "@/enums";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { computedAsync } from "@vueuse/core";

const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const keyWordAliasHook = usekeyWordAliasHook();
const title = computed(() => (purchaseOrderDetailStore.isCable ? "填报生产数据" : "填报生产订单"));

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `请输入${KeywordAliasEnum.IPO_NO}/销售订单号/销售订单行号`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const size = SizeType.DEFAULT;

const WoStatusComponents = defineComponent({
  name: "woStatus",
  render() {
    // 品类下拉框数据
    const options = ProductionStateoption.map(option =>
      h(ElOption, { label: option.label, value: option.value }, () => option.label)
    );
    return h(ElSelect, { clearable: true, filterable: true }, () => options);
  }
});

const form = reactive({
  /** 物料名称 */
  materialName: undefined,

  /** 计划结束时间 */
  plantStartTime: undefined,

  /** 计划结束时间 */
  plantEndTime: undefined,

  /** 工单状态 */
  ipoStatus: undefined
});

const items: Array<ISearchItem> = [
  {
    key: "materialName",
    label: "物料名称：",
    component: ElInput,
    componentProps: {
      placeholder: "请输入物料名称"
    }
  },
  {
    key: "plantStartTime",
    label: "计划开始日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划开始日期"
    }
  },
  {
    key: "plantEndTime",
    label: "计划结束日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划结束日期"
    }
  },
  {
    key: "ipoStatus",
    label: "生产状态：",
    component: WoStatusComponents
  }
];

const emits = defineEmits<{
  (event: "operateChange"): void;
  (event: "searchForm", params: IProductOrderReq): void;
}>();

const keywordFields: Array<IKeywordField> = [
  { key: "ipoNo", title: "生产订单号" }
  // { key: "soNo", title: "销售订单号" },
  // { key: "soItemNo", title: "销售订单行项目号" }
];

const onSearch = () => {
  emits("searchForm", { ...form, materialName: form.materialName || null, ipoStatus: form.ipoStatus || null });
};

/** 操作事件 */
const onProductOrderModalVisible = () => {
  emits("operateChange");
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;

  .title {
    color: var(--el-text-color-primary);
    font-size: 1.125rem;
    font-weight: normal;
    line-height: 1.625rem;
    letter-spacing: 0;
    padding-bottom: 0.75rem;
  }
}
</style>
