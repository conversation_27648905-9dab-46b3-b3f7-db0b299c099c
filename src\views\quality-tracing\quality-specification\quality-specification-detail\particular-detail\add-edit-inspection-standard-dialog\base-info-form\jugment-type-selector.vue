<template>
  <el-select
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    placeholder="请选择检测类别"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useVModels } from "@vueuse/core";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";
import { JudgeTypeEnum, JudgeTypeEnumMap } from "@/enums/quality-specification";
import { useSelectedItem } from "./use-selected-item";
import { ECollectionItemsType } from "@/models/collection-items";

/**
 * 检测类别选择器
 */

interface IOption {
  label: string;
  value: number;
}

const props = defineProps<{
  /** 已选中类别的id */
  modelValue?: number;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);
const { seletectItem } = useSelectedItem();
const loading = ref(false);

/**
 * @description: 根据生产阶段Id获取检测类别
 */
const options = computed<Array<IOption>>(() => {
  const baseOptions: Array<IOption> = [
    {
      label: JudgeTypeEnumMap[JudgeTypeEnum.ResultValue],
      value: JudgeTypeEnum.ResultValue
    },
    {
      label: JudgeTypeEnumMap[JudgeTypeEnum.ReachStandardRate],
      value: JudgeTypeEnum.ReachStandardRate
    },
    {
      label: JudgeTypeEnumMap[JudgeTypeEnum.MeanSquareError],
      value: JudgeTypeEnum.MeanSquareError
    }
  ];

  if (!seletectItem.value) {
    return baseOptions;
  }

  // 根据选中项的采集方式，返回不同的选项
  const { collectionType } = seletectItem.value;
  switch (collectionType) {
    case ECollectionItemsType.AutoCollection:
      return baseOptions.slice(1);
    case ECollectionItemsType.SystemPush:
      return [baseOptions[0]];
    default:
      return baseOptions;
  }
});

watch(
  seletectItem,
  item => {
    // 如果是自动采集项，并且之前被选中了结果值，则清空选中
    if (item && item.collectionType && modelValue.value === JudgeTypeEnum.ResultValue) {
      modelValue.value = undefined;
    }
    // 如果是系统推送项，则直接选中结果值
    if (item && item.collectionType === ECollectionItemsType.SystemPush) {
      modelValue.value = JudgeTypeEnum.ResultValue;
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped></style>
