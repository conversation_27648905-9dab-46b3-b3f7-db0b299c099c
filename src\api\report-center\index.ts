/**
 * @description: 报表中心接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IPagingReq, IResponse } from "@/models";
import {
  ExportRecordListItem,
  LoginReportListItem,
  LoginReportListParams,
  PurchaseReportListItem,
  PurchaseReportListParams,
  PurchaseReportStatistics
} from "@/models/report-center";

/**
 * @description: 获取报表数据统计
 */
export const getPurchaseReportStatistics = () => {
  return http.get<void, IResponse<PurchaseReportStatistics>>(
    withApiGateway(`admin-api/business/purchase/report/summary`)
  );
};

/**
 * @description: 获取采购订单报表列表
 */
export const getPurchaseReportList = (params: PurchaseReportListParams) => {
  return http.post<PurchaseReportListParams, IListResponse<PurchaseReportListItem>>(
    withApiGateway(`admin-api/business/purchase/report/query`),
    {
      data: params
    }
  );
};

/**
 * @description: 导出采购订单报表
 */
export const exportPurchaseReport = (params: PurchaseReportListParams) => {
  return http.post<PurchaseReportListParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/purchase/report/export`),
    {
      data: params
    }
  );
};

/**
 * @description: 下载采购订单报表文件
 */
export const downloadPurchaseReportFile = (reportId: string) => {
  return http.get<void, Blob>(withApiGateway(`admin-api/business/purchase/report/download?reportId=${reportId}`), {
    responseType: "blob"
  });
};

/**
 * @description: 获取导出记录列表
 */
export const getExportRecordList = (params: IPagingReq) => {
  return http.post<IPagingReq, IListResponse<ExportRecordListItem>>(
    withApiGateway(`admin-api/business/purchase/report/list`),
    {
      data: params
    }
  );
};

/**
 * @description: 获取登录报表列表
 */
export const getLoginReportList = (params: LoginReportListParams) => {
  return http.post<LoginReportListParams, IListResponse<LoginReportListItem>>(
    withApiGateway(`admin-api/infra/loginReport/query`),
    {
      data: params
    }
  );
};

/**
 * @description: 导出登录报表
 */
export const exportLoginReport = () => {
  return http.post<{ reportType: 7 }, IResponse<boolean>>(withApiGateway(`admin-api/business/purchase/report/export`), {
    data: {
      reportType: 7
    }
  });
};
