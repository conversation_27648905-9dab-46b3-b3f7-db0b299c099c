/**
 * 质量等级配置VO DTO转换器
 */

import { QualityLevelListItem } from "@/models/quality-tracing";
import { InputFormualModelVal } from "@/components/input-formual/types";

/**
 * @description: 格式化范围列表为请求参数列表
 */
export function rangeListToEditDTO(
  list: Array<{
    /** 判断公式 */
    range: InputFormualModelVal;
    /** 等级 */
    level: string;
    id: string;
  }>
): Array<QualityLevelListItem> {
  return list.map(item => {
    const { leftValue, leftSymbol, rightSymbol, rightValue } = item.range;
    return {
      id: item.id,
      minValue: Number(leftValue),
      minOp: leftSymbol,
      maxValue: Number(rightValue),
      maxOp: rightSymbol,
      levelName: item.level
    };
  });
}

/**
 * @description: 响应数据转为范围列表
 */
export function responseDataToRangeList(list: Array<QualityLevelListItem>) {
  return list.map(item => {
    const { minValue, minOp, maxValue, maxOp, levelName } = item;
    return {
      range: {
        leftValue: String(minValue),
        leftSymbol: minOp,
        midValue: "s",
        rightValue: String(maxValue),
        rightSymbol: maxOp
      },
      level: levelName,
      id: item.id
    };
  });
}
