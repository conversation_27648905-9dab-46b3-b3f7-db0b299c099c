<template>
  <el-dialog
    title="EIP检查数据流程"
    v-model="ctx.visible"
    destroy-on-close
    class="middle"
    :align-center="true"
    @open="change(0)"
    :close-on-click-modal="false"
  >
    <el-carousel :autoplay="false" :loop="false" arrow="hover" class="aspect-[667/421]" @change="change">
      <el-carousel-item>
        <img :src="guide1" alt="guide 1" />
      </el-carousel-item>
      <el-carousel-item>
        <img :src="guide2" alt="guide 2" />
      </el-carousel-item>
      <el-carousel-item>
        <img :src="guide3" alt="guide 3" />
      </el-carousel-item>
      <el-carousel-item>
        <img :src="guide4" alt="guide 4" />
      </el-carousel-item>
    </el-carousel>
  </el-dialog>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import { ElCarousel } from "element-plus";
import { eipGuideKey } from "./tokens";
import guide1 from "@/assets/img/eip_guide_1.png";
import guide2 from "@/assets/img/eip_guide_2.png";
import guide3 from "@/assets/img/eip_guide_3.png";
import guide4 from "@/assets/img/eip_guide_4.png";

const ctx = inject(eipGuideKey);

const index = ref(0);

function change(current: number) {
  index.value = current;
}
</script>

<style scoped lang="scss">
:deep(.el-carousel__container) {
  height: 100%;
}

:deep(.el-carousel__button) {
  background: var(--el-color-primary-light-8);
}

:deep(.el-carousel__indicator.is-active) {
  .el-carousel__button {
    background: var(--el-color-primary);
  }
}

:deep(.el-carousel__indicators--horizontal) {
  bottom: 20px;
}

:deep(.el-carousel__arrow) {
  background: rgba(0, 0, 0, 0.5);
}
</style>
