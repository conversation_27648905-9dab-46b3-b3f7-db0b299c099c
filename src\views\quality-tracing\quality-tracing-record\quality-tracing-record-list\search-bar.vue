<template>
  <el-form inline ref="formRef" :model="form">
    <el-form-item label="关键词：" prop="keyWord">
      <el-input class="!w-64" clearable :placeholder="placeholder" v-model="form.keyWord" @clear="onSearch" />
    </el-form-item>
    <el-form-item label="记录更新时间：" prop="dateRange">
      <el-date-picker
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        v-model="form.dateRange"
        type="daterange"
        :shortcuts="shortcuts"
      />
    </el-form-item>
    <el-form-item class="!mr-2">
      <el-button type="primary" @click="onSearch">搜索</el-button>
    </el-form-item>
    <el-form-item>
      <el-button @click="onReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { dayjs, type FormInstance } from "element-plus";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";
import { computedAsync } from "@vueuse/core";
/**
 * 质量规范列表搜索栏
 */

interface FormType {
  keyWord: string;
  dateRange: [string, string];
}
const keyWordAliasHook = usekeyWordAliasHook();
const emit = defineEmits<{
  (event: "handleSearch", value: FormType): void;
}>();

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `${KeywordAliasEnum.IPO_NO}/销售订单号/采购订单号`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const form = reactive<FormType>({
  keyWord: "",
  dateRange: ["", ""]
});

const formRef = ref<FormInstance>();

const shortcuts = [
  {
    text: "最近一周",
    value: [dayjs().subtract(7, "day").toISOString(), dayjs().toISOString()]
  },
  {
    text: "最近一个月",
    value: [dayjs().subtract(30, "day").toISOString(), dayjs().toISOString()]
  },
  {
    text: "最近三个月",
    value: [dayjs().subtract(90, "day").toISOString(), dayjs().toISOString()]
  },
  {
    text: "最近一年",
    value: [dayjs().subtract(365, "day").toISOString(), dayjs().toISOString()]
  }
];

/**
 * @description: 搜索
 */
function onSearch() {
  emit("handleSearch", Object.assign({}, form));
}

/**
 * @description: 重置
 */
function onReset() {
  if (!formRef.value) {
    return;
  }
  formRef.value.resetFields();
  emit("handleSearch", Object.assign({}, form));
}

defineExpose({
  resetSearchBar: onReset
});
</script>
<style lang="scss" scoped>
:deep(.el-form) {
  padding: 0 !important;
  background-color: red;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
