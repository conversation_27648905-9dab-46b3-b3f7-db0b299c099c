<template>
  <el-row :gutter="20">
    <el-col :span="20">
      <el-form inline ref="formRef" :model="form">
        <el-form-item label="质量规范：" prop="keyWords">
          <el-input
            class="!w-52"
            clearable
            placeholder="请输入质量规范名称"
            v-model="form.keyWords"
            @clear="onSearch"
          />
        </el-form-item>
        <el-form-item label="物资种类：" prop="subClassCode">
          <sub-class-selector v-model="form.subClassCode" :clearable="true" @clear="onSearch" />
        </el-form-item>
        <el-form-item class="!mr-2">
          <el-button type="primary" @click="onSearch">搜索</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="4" class="text-right">
      <slot name="right" />
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useRoute } from "vue-router";
import type { FormInstance } from "element-plus";
import SubClassSelector from "@/views/components/subclass-select/subclass-select.vue";
import { usePageStoreHook } from "@/store/modules/page";

/**
 * 质量规范列表搜索栏
 */

interface FormType {
  keyWords: string;
  subClassCode: string;
}

const emit = defineEmits<{
  (event: "handleSearch", value: FormType): void;
}>();

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);
const form = reactive<FormType>({
  keyWords: "",
  subClassCode: ""
});

const formRef = ref<FormInstance>();

/**
 * @description: 搜索
 */
function onSearch() {
  emit("handleSearch", Object.assign({}, form));
}

/**
 * @description: 重置
 */
function onReset() {
  if (!formRef.value) {
    return;
  }
  formRef.value.resetFields();
  emit("handleSearch", Object.assign({}, form));
}
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
