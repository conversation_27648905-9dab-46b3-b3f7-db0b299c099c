<template>
  <!-- 附件上传 -->
  <el-upload
    ref="uploadRef"
    accept=".jpg, .jpeg, .png, .pdf"
    v-model:file-list="fileList"
    :auto-upload="true"
    :limit="1"
    :before-upload="beforeUpload"
    :on-success="uploadSuccess"
    :on-error="uploadError"
    :on-exceed="onExceed"
    :on-remove="uploadRemove"
    :on-preview="uploadReview"
    :http-request="uploadReportHttp"
  >
    <el-button type="primary"> 选择文件 </el-button>
    <template #tip>
      <span class="ml-2">仅支持20M内pdf，png，jpg，jpeg格式文件</span>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import { ElMessage } from "element-plus";
import { genFileId, UploadProps, UploadRawFile, UploadInstance, UploadRequestOptions, UploadFile } from "element-plus";
import { FileInfoModel } from "@/models";
import { uploadFile } from "@/api/upload-file";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

const props = withDefaults(
  defineProps<{
    modelValue?: FileInfoModel;
  }>(),
  {
    modelValue: () => {
      return {} as FileInfoModel;
    }
  }
);

const uploadRef = ref<UploadInstance>();
const fileList = ref([]);

watchEffect(() => {
  fileList.value = JSON.stringify(props.modelValue) == "{}" ? [] : props.modelValue?.id ? [props.modelValue] : [];
});

const emits = defineEmits<{
  (e: "change", value: FileInfoModel): void;
}>();

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
  uploadRef.value!.submit();
};

/**
 * 删除附件
 */
const uploadRemove = () => {
  emits("change", {} as FileInfoModel);
};

/**
 * 预览
 */
const uploadReview: UploadProps["onPreview"] = uploadFile => {
  downLoad(uploadFile as FileInfoModel);
};

/**
 *  上传前
 */

async function downLoad(fileInfo: FileInfoModel) {
  const blob = await downLoadFile(fileInfo.id);
  downloadByData(blob, fileInfo.name, blob.type);
}

const beforeUpload = (rawFile: UploadRawFile) => {
  const fileSize = rawFile.size / 1024 / 1024 < 20;
  let fileName = "";
  if (rawFile.name) {
    fileName = rawFile.name.substring(rawFile.name.lastIndexOf(".") + 1);
  }
  if (!fileSize) {
    ElMessage.error("上传附件大小不能超过20M");
    return false;
  } else if (fileName !== "pdf" && fileName !== "png" && fileName !== "jpg" && fileName !== "jpeg") {
    ElMessage.error("仅支持.pdf, .png, .jpg, .jpeg文件扩展名");
    return false;
  } else if (rawFile.size === 0) {
    ElMessage.error("上传附件大小不能为空");
    return false;
  } else {
    return true;
  }
};

/** 上传成功时执行的钩子函数 */
const uploadSuccess = async (res: any) => {
  if (!res.code) {
    emits("change", res.data);
  } else {
    ElMessage.error(`${res?.msg}`);
  }
};

/**
 * 上传失败的时候钩子函数
 */
const uploadError = (error: Error, file: UploadFile) => {
  // 请求失败
  const statusStr = file.status;
  if (statusStr === "fail") {
    ElMessage.error("文件上传失败");
  }
};

/**
 * 自定义上传接口
 */
const uploadReportHttp = async (options: UploadRequestOptions) => {
  const formData = new FormData();
  formData.append("file", options.file);
  return await uploadFile(formData);
};

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, fileList.value[0] as FileInfoModel);
}

/**
 * @description: 获取表单值
 */
function resetFormValue() {
  uploadRef.value!.clearFiles();
  return {} as FileInfoModel;
}

defineExpose({
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
:deep(.el-icon--close-tip) {
  display: none !important;
}

:deep(.el-upload-list__item) {
  transition: none;
}
</style>
