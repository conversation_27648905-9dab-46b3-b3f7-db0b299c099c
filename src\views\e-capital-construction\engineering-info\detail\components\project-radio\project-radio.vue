<template>
  <div v-loading="loading" class="flex items-center">
    <el-radio-group v-model="testItem" size="default" @change="handleChange">
      <el-radio-button v-for="item in filterEngineeringRadio" :key="item.value" :label="item.value">{{
        item.label
      }}</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { hasAuth } from "@/router/utils";
import {
  EngineeringTranRadioEnumOptions,
  EngineeringComRadioEnumOptions,
  EquipmentTypeEnumExt,
  EngineeringComRadioEnumPermission,
  EngineeringTranRadioEnumPermission
} from "@/enums";
import { PermissionKey } from "@/consts";

const route = useRoute();
const loading = ref(false);

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

const engineeringRadio = isCombiner ? EngineeringComRadioEnumOptions : EngineeringTranRadioEnumOptions;

let filterEngineeringRadio = [];
const testItem = ref();

const getAuth = () => {
  filterEngineeringRadio = [];
  engineeringRadio.forEach(item => {
    const permission = isCombiner
      ? EngineeringComRadioEnumPermission[item.value]
      : EngineeringTranRadioEnumPermission[item.value];
    if (hasAuth(PermissionKey.ejijian[permission])) {
      filterEngineeringRadio.push(item);
    }
  });
  testItem.value = filterEngineeringRadio[0]?.value;
  emits("change", testItem.value);
};

const emits = defineEmits<{
  (event: "change", value: string): void;
}>();

const handleChange = (val: string) => {
  emits("change", val);
};

onMounted(() => {
  getAuth();
});
</script>

<style scoped lang="scss"></style>
