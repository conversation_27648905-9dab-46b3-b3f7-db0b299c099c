import { EControlType } from "@/enums";

/**
 * 原材料检测信息的hook
 */
export function useRawMaterialCheckInfoHook() {
  // 获取原材料检测项的表单数据
  function getRawMaterialCheckFormData(rawMaterialCheckCollectionData) {
    return rawMaterialCheckCollectionData?.filter(
      item => item.dataTypeIdentityDetail?.identityCode !== EControlType.FileControl
    );
  }
  // 获取原材料检测项的文件上传数据
  function getRawMaterialCheckFileData(rawMaterialCheckCollectionData) {
    return rawMaterialCheckCollectionData
      .filter(item => item.dataTypeIdentityDetail?.identityCode === EControlType.FileControl)
      .map(item => {
        const { targetValue } = item;
        const fileList = targetValue ? JSON.parse(targetValue) : [];
        return {
          ...item,
          fileList
        };
      });
  }

  return {
    getRawMaterialCheckFormData,
    getRawMaterialCheckFileData
  };
}
