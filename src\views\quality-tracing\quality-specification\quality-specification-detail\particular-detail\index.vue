<template>
  <el-scrollbar class="flex-1">
    <el-collapse v-model="expandNames">
      <el-collapse-item v-for="item in stageListConfigArr" :key="item.key" :name="item.key" class="mb-5">
        <template #title>
          <collapse-item-title :title="item.title" :name="item.key" class="!py-0">
            <el-divider direction="vertical" />
            <div class="text-right">
              合计分值：<span class="text-primary score-text">{{ scoreMap[item.scoreKey] }}</span>
            </div>
          </collapse-item-title>
        </template>
        <universal-list
          :category-code="item.category"
          :production-stage-id="productionStageMap[item.collectionCode] || ''"
          :quality-id="qualityId"
          @refresh-score="requestSoreStat"
        />
      </el-collapse-item>
    </el-collapse>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import {
  QualityStageCategoryConfig,
  ProductionStageCodeToQualitySpecificationEnum
} from "@/enums/quality-specification";
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import UniversalList from "./universal-list/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getProductProcessByCategory } from "@/api";
import { getQualitySpecificationParticularDetailScoreMap } from "@/api/quality-tracing";
import { QualitySpecificationParticularDetailScore } from "@/models/quality-tracing";
import { stageListConfigArr } from "./list-config";
/**
 * 质量规范明细
 */

const props = defineProps<{
  subClassCode: string;
  /** 质量规范id */
  qualityId: string;
}>();

/** 生产阶段映射关系 */
const productionStageMap = ref<Record<string, string>>({});

const expandNames = ref(genAllExpandNames());
const scoreMap = reactive<Partial<QualitySpecificationParticularDetailScore>>({});
const loading = ref(false);

/**
 * @description: 生成所有展开项名称
 */
function genAllExpandNames() {
  return Object.values(QualityStageCategoryConfig).map(({ name }) => name);
}

/**
 * @description: 展开全部折叠面板
 */
function handleExpandAll() {
  expandNames.value = genAllExpandNames();
}

/**
 * @description: 收起全部折叠面板
 */
function handleCollapseAll() {
  expandNames.value = [];
}

/**
 * @description: 请求合计分值统计
 */
const requestSoreStat = useLoadingFn(async () => {
  const { data } = await getQualitySpecificationParticularDetailScoreMap(props.qualityId);
  Object.assign(scoreMap, data);
}, loading);

/**
 * @description: 请求生产阶段映射关系
 */
async function requestProductionStageMap() {
  const { data: list } = await getProductProcessByCategory(props.subClassCode);
  productionStageMap.value = list.reduce((prev, { productionStageCode, id }) => {
    prev[productionStageCode] = id;
    return prev;
  }, {} as Record<ProductionStageCodeToQualitySpecificationEnum, string>);
}

watch(
  () => props.subClassCode,
  (code: string) => {
    if (code) {
      requestProductionStageMap();
    }
  },
  {
    immediate: true
  }
);

onMounted(requestSoreStat);

defineExpose({
  /** 展开所有菜单 */
  handleExpandAll,
  /** 收起所有菜单 */
  handleCollapseAll
});
</script>
<style lang="scss" scoped>
:deep(.el-collapse-item__arrow) {
  display: none;
}

.score-text:empty:after {
  content: var(--default-value) !important;
}
</style>
