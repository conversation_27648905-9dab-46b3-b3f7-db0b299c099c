<template>
  <div class="inline-block">
    <div>
      <slot :open-dialog="openDialog">
        <el-button
          v-if="isAddMode"
          v-auth="PermissionKey.form.formPurchaseWorkReportCreate"
          type="primary"
          link
          @click="openDialog"
        >
          新增报工
        </el-button>
        <el-button
          v-if="isEditMode"
          v-auth="PermissionKey.form.formPurchaseWorkReportEdit"
          link
          type="primary"
          @click="openDialog"
        >
          编辑
        </el-button>
      </slot>
    </div>
    <el-dialog
      align-center
      draggable
      v-model="dialogVisible"
      :title="isAddMode ? '新增报工' : '编辑报工'"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <report-work-form
        ref="formRef"
        v-loading="loading"
        :mode="mode"
        :is-need-work-order-selector="isNeedWorkOrderSelector"
        :work-order-selector-disabled="workOrderSelectorDisabled"
        :sub-class-code="subClassCode"
        :production-id="productionId"
        :limted-process-arr="limtedProcessArr"
      />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, withDefaults, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { PermissionKey } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import ReportWorkForm from "./report-work-form/index.vue";
import { createReportWork, editReportWork, getReportWorkById } from "@/api/report-work";
import { ICreateReportWork } from "@/models";
import { emitter as eventBus } from "@/utils/mitt";

/**
 * 新增/编辑 报工
 */

const props = withDefaults(
  defineProps<{
    /** 模式 */
    mode: "edit" | "add";
    /** 物资种类 */
    subClassCode: string;
    /** 是否需要工单选择器 */
    isNeedWorkOrderSelector?: boolean;
    /** 开启工单选择器、add模式，必传 */
    productionId?: string;
    /** 工单id */
    workId?: string;
    /** 报工id 编辑模式必传 */
    reportWorkId?: string;
    /** 禁用工单选择器 */
    workOrderSelectorDisabled?: boolean;
    limtedProcessArr?: Array<string>;
  }>(),
  {
    workId: "",
    reportWorkId: "",
    isNeedWorkOrderSelector: false,
    workOrderSelectorDisabled: false,
    limtedProcessArr: () => []
  }
);

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const formRef = ref<InstanceType<typeof ReportWorkForm>>();

/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");

/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

const loading = ref(false);

const requestReportWorkDetail = useLoadingFn(async () => {
  const { data } = await getReportWorkById(props.reportWorkId);
  if (!formRef.value) {
    return;
  }
  formRef.value.initFormValue(data);
}, loading);

const requestCreateReportWorkById = useLoadingFn(async () => {
  if (!formRef.value) {
    return;
  }
  const params = formRef.value.getFormValue();
  return createReportWork(params);
}, loading);

const requestEditReportWork = useLoadingFn(async () => {
  if (!formRef.value) {
    return;
  }
  const formValue = formRef.value.getFormValue();
  const params: ICreateReportWork = { id: props.reportWorkId, ...formValue };
  return editReportWork(params);
}, loading);

async function requestAdd() {
  const { data: workOrderId, msg } = await requestCreateReportWorkById();
  if (!workOrderId) {
    ElMessage({ type: "warning", message: msg || "网络异常, 请稍后再试" });
    return;
  }
  closeDialog();
  emits("postSaveSuccess");
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
  ElMessage({
    message: "新增成功",
    type: "success"
  });
}

async function requestEdit() {
  const { data: result, msg } = await requestEditReportWork();
  if (!result) {
    ElMessage({ type: "warning", message: msg || "网络异常, 请稍后再试" });
    return;
  }
  closeDialog();
  emits("postSaveSuccess");
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
  ElMessage({
    message: "编辑成功",
    type: "success"
  });
}

const handleClickSaveBtn = async () => {
  if (!formRef.value) {
    return;
  }
  const verified = await formRef.value.validateForm();
  if (!verified) {
    return;
  }

  // 新增
  if (isAddMode.value) {
    requestAdd();
  } else if (isEditMode.value) {
    // 编辑
    requestEdit();
  }
};

function openDialog() {
  dialogVisible.value = true;
}

function closeDialog() {
  dialogVisible.value = false;
}

watch(dialogVisible, visible => {
  if (visible) {
    if (isEditMode.value) {
      requestReportWorkDetail();
    } else {
      nextTick(() => {
        formRef.value.initFormValue({
          workId: props.workId
        });
      });
    }
  }
});
</script>

<style scoped></style>
