import { InjectionKey } from "vue";
import { IStateGridRateTrigger, IStateGridSyncAuditBaseInfo } from "@/models";

export interface IEIPGuideCtx {
  visible: boolean;
}

export const eipGuideKey: InjectionKey<IEIPGuideCtx> = Symbol("eip guide");

export interface ITriggerScoreProductOrder {
  editVisible?: boolean;
  syncAuditVisible?: boolean;
  refresh: Function;
  editData?: IStateGridRateTrigger;
  syncAuditBaseInfo?: IStateGridSyncAuditBaseInfo;
  dateSupportOrder?: boolean;
}

export const triggerScoreProductOrderKey: InjectionKey<ITriggerScoreProductOrder> = Symbol("edit product order");
