<template>
  <div class="ex-factory-experiment">
    <div class="ex-factory-tabs flex justify-between">
      <div class="flex-1">
        <SwitchTab :switch-tags="exFactoryExperimentStore.processes" @switch-change="switchTag" />
      </div>
      <!-- 局放耐压不显示 -->
      <div class="operate">
        <div class="link-jfny-experiment ml-2 flex justify-end" v-if="componentTab === WithstandPressure">
          <el-button
            type="primary"
            @click="testJfnyData"
            v-auth="PermissionKey.form.formPurchaseFactoryTrialBtnLink"
            v-if="!hideSimulateJFNYBtn"
            >模拟局放耐压数据</el-button
          >
          <el-button
            type="primary"
            v-auth="PermissionKey.form.formPurchaseFactoryTrialBtnLink"
            @click="linkJfnyExperiment"
            >关联局放耐压试验</el-button
          >
        </div>
        <div class="add-air-tightness flex" v-if="componentTab === AirTightness">
          <div v-if="isCanOperateAddAndEdit">
            <el-button
              v-auth="PermissionKey.form.formPurchaseFactoryTrialCreate"
              type="primary"
              @click="addAirTightness()"
            >
              <FontIcon class="mr-2" icon="icon-plus" />
              新增试验
            </el-button>
            <el-button
              type="primary"
              @click="copyOfProduction"
              v-auth="PermissionKey.form.formPurchaseFactoryTrialCreate"
            >
              <FontIcon class="mr-2" icon="icon-plus" />
              {{ copyButtonText }}
            </el-button>
          </div>
          <card-list-switcher class="ml-3" v-model="activeTab" />
        </div>
      </div>
    </div>
    <!-- 局放耐压试验 -->
    <div class="flex">
      <div class="sensing-content min-h-[300px] w-full">
        <component
          ref="compInstance"
          :is="componentTab"
          :active-tab="activeTab"
          @delSuccess="delSuccess($event)"
          @addDataSuccess="addDataSuccess($event)"
        />
      </div>
    </div>
  </div>
  <!-- 从生产订单中复制 -->
  <CopyProductDialog v-model="copyVisible" :isStepKey="prodCtx.stepKey" @saveSuccess="saveSuccess" />
</template>

<script setup lang="ts">
import SwitchTab from "../../component/switch-tab/index.vue";
import WithstandPressure from "./jfny/index.vue";
import AirTightness from "./not-jfny/index.vue";
import CopyProductDialog from "../../component/copy-production-dialog/index.vue";
import { ISwitchTagEventType, EExFactoryTest } from "../../component/switch-tab/types";
import { inject, onUnmounted, ref, shallowRef } from "vue";
import { useEXFactoryExperimentStore, useEXFactoryQMXStore, useSalesOrderDetailStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { computed, watch } from "vue";
import { PermissionKey } from "@/consts";
import { onMounted } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing/index";
import CardListSwitcher from "@/components/card-list-switcher/index.vue";
import { emitter } from "@/utils/mitt";

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
// 切换组件
const componentTab = shallowRef();
const exFactoryExperimentStore = useEXFactoryExperimentStore();
const notInit = ref(false);
const tabIndex = ref(0);
const compInstance = ref();
const hideSimulateJFNYBtn = ref<boolean>(true);

const processesList = computed(() => exFactoryExperimentStore.processes);
/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => exFactoryExperimentStore.getIsCanOperateAddAndEdit);

// 赋值初始试验值
const setInitExperimentProcess = newValue => {
  const { processCode, processName, processId, hasData, autoCollect } = newValue[tabIndex.value];
  exFactoryExperimentStore.setActiveProcess({ processCode, processName, processId, hasData, autoCollect });
  componentTab.value = processCode === EExFactoryTest.JFNY ? WithstandPressure : AirTightness;
};
const exFactoryQMXStore = useEXFactoryQMXStore();
const salesProductionTestSensingStore = useSalesProductionTestSensingStore();
// 生产订单/工单中复制
const copyButtonText = computed(() => {
  return useSalesOrderDetailStore().isCable ? "从生产订单中选用" : "从生产工单中选用";
});
const copyVisible = ref<boolean>(false);
const activeTab = ref<"card" | "list">("list");

watch(
  processesList,
  newValue => {
    if (!newValue.length || notInit.value) return;
    setInitExperimentProcess(newValue);
  },
  {
    immediate: true
  }
);

onMounted(async () => {
  exFactoryExperimentStore.getOutGoingFactoryProcess(true);
  // 获取是否显示局放耐压
  hideSimulateJFNYBtn.value = await exFactoryExperimentStore.getSimulateJFNYBtn();
});

// 切换试验
const switchTag = (tag: ISwitchTagEventType, index: number) => {
  if (!tag.data.processCode) {
    ElMessage.warning("工序编码不能为空");
    return;
  }
  tabIndex.value = index;
  activeTab.value = "list";
  exFactoryQMXStore.initExperimentList();
  const { processCode, processName, processId, hasData, autoCollect } = tag.data;
  exFactoryExperimentStore.setActiveProcess({ processCode, processName, processId, hasData, autoCollect });
  componentTab.value = tag.data.processCode === EExFactoryTest.JFNY ? WithstandPressure : AirTightness;
  emitter.emit("refreshOutGoingFactoryExperimentList");
};

const delSuccess = (component: string) => {
  notInit.value = true;
  exFactoryExperimentStore.getOutGoingFactoryProcess(true);
  componentTab.value = component === EExFactoryTest.JFNY ? WithstandPressure : AirTightness;
};
const addDataSuccess = (component: string) => {
  notInit.value = true;
  exFactoryExperimentStore.getOutGoingFactoryProcess(true);
  componentTab.value = component === EExFactoryTest.JFNY ? WithstandPressure : AirTightness;
};

/** 新增试验 */
const addAirTightness = () => {
  compInstance.value?.addAirTightness();
};

/**
 * 模拟局放耐压数据
 */
const testJfnyData = () => {
  compInstance.value?.testJfnyData();
};

/**
 * 关联局放耐压数据
 */
const linkJfnyExperiment = () => {
  compInstance.value?.linkJfnyExperiment();
};

/** 复制 */
function copyOfProduction() {
  copyVisible.value = true;
}

async function saveSuccess() {
  ElMessage.success("保存成功");
  copyVisible.value = false;
  compInstance.value?.refresh();
  exFactoryExperimentStore.getOutGoingFactoryProcess(true);
  salesProductionTestSensingStore.refreshProductionProcessStatus();
}

onUnmounted(() => {
  exFactoryQMXStore.initExperimentList();
  exFactoryExperimentStore.$reset();
});
</script>

<style scoped lang="scss"></style>
