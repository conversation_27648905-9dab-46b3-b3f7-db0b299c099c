import { defineStore } from "pinia";
import { IPurchaseOrderStep, ISalesOrder, ISalesOrderProcess } from "@/models";
import { EMaterialCategory, SalesLinkStepEnum } from "@/enums";
import { computed, ref } from "vue";
import { ARMOUR_CLAMP_CODE, REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { useThrottleFn } from "@vueuse/core";
import { getSalesOrderDetailById, getStepsById, salesGoToStep } from "@/api/sales-order-management";
import { useMaterial } from "@/utils/material";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";

/**
 * @description: 计算非线缆步骤
 * @param {string} flag 订单标识
 */
function calcNonCableSteps(flag: string): Array<SalesLinkStepEnum> {
  const NON_CABLE_STEPS: Array<SalesLinkStepEnum> = [
    SalesLinkStepEnum.SALES_LINE,
    SalesLinkStepEnum.PRODUCTION_PLAN,
    SalesLinkStepEnum.PRODUCTION_ORDER,
    SalesLinkStepEnum.PRODUCTION_DATA,
    SalesLinkStepEnum.SYNC_ORDER
  ];
  if (flag !== SynchronousFlagEnum.CSG_GuangZhou_Flag) {
    NON_CABLE_STEPS.push(SalesLinkStepEnum.QUALITY_EVAL);
  }
  return NON_CABLE_STEPS;
}

/**
 * @description: 计算线缆步骤
 * @param {string} flag 订单标识
 */
function calcCablesSteps(flag: string): Array<SalesLinkStepEnum> {
  const CABLE_STEPS: Array<SalesLinkStepEnum> = [
    SalesLinkStepEnum.SALES_LINE,
    SalesLinkStepEnum.PRODUCTION_PLAN,
    SalesLinkStepEnum.PRODUCTION_DATA,
    SalesLinkStepEnum.SYNC_ORDER
  ];
  if (flag !== SynchronousFlagEnum.CSG_GuangZhou_Flag) {
    CABLE_STEPS.push(SalesLinkStepEnum.QUALITY_EVAL);
  }
  return CABLE_STEPS;
}

export const useSalesOrderDetailStore = defineStore("sales-order-detail-store", () => {
  const _stepStatus = ref<ISalesOrderProcess>();
  const _stepKeys = ref<Array<SalesLinkStepEnum>>([]);
  const salesOrder = ref<ISalesOrder>({} as ISalesOrder);
  const activeStepKey = ref<SalesLinkStepEnum>();
  const saleOrderId = ref<string>();
  const isCable = ref<boolean>();
  const syncIotFlag = ref<boolean>();
  const isArmourClamp = ref<boolean>();
  const detailMaterialCategory = ref<string>();
  const materialTool = useMaterial();

  const refreshStepStatusHandle = useThrottleFn(refreshStepStatus, REFRESH_SYNC_DATA_PERIOD, true);

  const steps = computed(() => {
    return _stepKeys.value.map(key => {
      const step: IPurchaseOrderStep = { key };
      const status = _stepStatus.value;
      if (status) {
        const {
          salesLineLinkedCount,
          salesLineCount,
          salesLinePlanCount,
          salesLineProductCount,
          salesLineSyncCount,
          salesLineGradeCount,
          workOrderHasProductionCount
        } = status;
        switch (key) {
          case SalesLinkStepEnum.SALES_LINE:
            step.completedCount = salesLineLinkedCount;
            step.total = salesLineCount;
            break;
          case SalesLinkStepEnum.PRODUCTION_PLAN:
            step.completedCount = salesLinePlanCount;
            step.total = salesLineCount;
            break;
          case SalesLinkStepEnum.PRODUCTION_ORDER:
            step.completedCount = salesLineProductCount;
            step.total = salesLineCount;
            break;
          case SalesLinkStepEnum.PRODUCTION_DATA:
            if (isCable.value) {
              step.completedCount = salesLineProductCount;
              step.total = salesLineCount;
            } else {
              step.completedCount = workOrderHasProductionCount;
              step.total = salesLineCount;
            }
            break;
          case SalesLinkStepEnum.SYNC_ORDER:
            step.completedCount = salesLineSyncCount;
            step.total = salesLineCount;
            break;
          case SalesLinkStepEnum.QUALITY_EVAL:
            step.completedCount = salesLineGradeCount;
            step.total = salesLineCount;
            break;
        }
        step.complete = step.completedCount >= step.total && step.total > 0;
      }
      return step;
    });
  });

  function setSaleOrderId(id: string) {
    saleOrderId.value = id;
  }

  async function refreshSalesOrder() {
    salesOrder.value = (await getSalesOrderDetailById(saleOrderId.value)).data;
    isCable.value = materialTool.isCable(salesOrder.value.categoryCode);
    syncIotFlag.value = salesOrder.value.syncIotFlag;
    // 金具
    isArmourClamp.value = salesOrder.value.subClassCode?.includes(ARMOUR_CLAMP_CODE);
    // 生产试验感知 物资种类
    _handleMaterialCategory(salesOrder.value.subClassCode);
    const steps = isCable.value
      ? calcCablesSteps(salesOrder.value.matSyncFlagId)
      : calcNonCableSteps(salesOrder.value.matSyncFlagId);
    _updateSteps(steps);
  }

  async function refreshStepStatus(): Promise<void> {
    _stepStatus.value = (await getStepsById(saleOrderId.value)).data;
  }

  async function goto(key: SalesLinkStepEnum) {
    if (activeStepKey.value === key) {
      return;
    }
    if (key <= salesOrder.value.linkStep) {
      activeStepKey.value = key;
      return;
    }
    try {
      await salesGoToStep(saleOrderId.value, key);
      activeStepKey.value = key;
      refreshSalesOrder();
    } catch (e) {
      activeStepKey.value = activeStepKey.value ?? _stepKeys.value[0];
    }
  }

  function $reset() {
    activeStepKey.value = null;
  }

  function _updateSteps(keys: Array<SalesLinkStepEnum>) {
    _stepKeys.value = keys;
    if (!activeStepKey.value || !keys.includes(activeStepKey.value)) {
      activeStepKey.value = salesOrder.value.linkStep || keys[0];
    }
  }

  function _handleMaterialCategory(subClassCode: string) {
    switch (subClassCode) {
      case ARMOUR_CLAMP_CODE:
        detailMaterialCategory.value = EMaterialCategory.ArmourClamp;
        break;
      default:
        detailMaterialCategory.value = EMaterialCategory.Normal;
        break;
    }
  }

  return {
    saleOrderId,
    salesOrder,
    isCable,
    syncIotFlag,
    steps,
    activeStepKey,
    isArmourClamp,
    detailMaterialCategory,
    setSaleOrderId,
    refreshSalesOrder,
    refreshStepStatus,
    refreshStepStatusHandle,
    goto,
    $reset
  };
});
