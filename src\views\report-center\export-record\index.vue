<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 搜索条 -->
    <div class="bg-bg_color mt-4 mx-6" />
    <!-- 表格 -->
    <div class="bg-bg_color p-5 pt-3 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="{ row }">
          <download-report-btn
            v-if="+row.processed >= +row.total"
            link
            type="primary"
            :id="row.id"
            :fileName="row.fileName"
          >
            下载
          </download-report-btn>
          <span v-else>生成中...</span>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import { getExportRecordList } from "@/api/report-center";
import { ExportRecordListItem } from "@/models/report-center";
import { genExportReportTableColumnsConfig } from "./column-config";
import DownloadReportBtn from "./download-report-btn.vue";

/**
 * 报表中心-登录报表
 */

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);

const { pagination } = useTableConfig();
const { columnsConfig } = genExportReportTableColumnsConfig();
const loading = ref(false);
const list = ref<ExportRecordListItem[]>([]);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
  const { data } = await getExportRecordList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(() => {
  requestList();
});
</script>

<style scoped></style>
