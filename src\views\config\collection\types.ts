/** 日期时间格式 */
export enum EDateType {
  /** 年/月/日 */
  Date = "date",
  /** 年月日/时分秒 */
  DateTime = "datetime"
}

export const DATE_TYPE_OPTIONS = [
  {
    label: "日期(年月日 / YYYY-MM-dd)",
    value: EDateType.Date
  },
  {
    label: "时间(年月日-时间 / YYYY-MM-dd HH:mm:ss)",
    value: EDateType.DateTime
  }
];

export enum EFileType {
  /** pdf */
  PDF = "pdf",
  /** png 图片 */
  PNG = "png",
  /** jpg图片 */
  JPG = "jpg，jpeg",
  /** word */
  MsWord = "msword",
  /** word */
  OfficeWord = "vnd.openxmlformats-officedocument.wordprocessingml.document",
  /** txt */
  Txt = "text/plain"
}

export const FILE_TYPE_OPTIONS: Array<{
  label: string;
  value: string;
}> = [
  {
    label: "PDF类型",
    value: EFileType.PDF
  },
  {
    label: "PNG图片",
    value: EFileType.PNG
  },
  {
    label: "JPG/JPEG图片",
    value: EFileType.JPG
  },
  {
    label: "DOC文档",
    value: EFileType.MsWord
  },
  {
    label: "DOCX文档",
    value: EFileType.OfficeWord
  },
  {
    label: "TXT",
    value: EFileType.Txt
  }
];
