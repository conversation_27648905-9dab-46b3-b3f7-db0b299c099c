/**
 * @description: 公共接口
 */
import { http } from "@/utils/http";
import { IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { UniqueCodeType } from "../models/i-common";

/**
 * @description: 获取唯一码
 */
export const getUniqueCode = (type: UniqueCodeType) => {
  const url: string = withApiGateway(`admin-api/ecode/common/generateCode`);
  return http.get<UniqueCodeType, IResponse<string>>(url, { params: { code: type } });
};
