<template>
  <div class="header">
    <div class="flex items-center">
      <TitleBar title="质量告警" />
      <div class="sub-title pl-2">满足质量告警规则，会立即产生告警数据</div>
    </div>
    <el-switch v-model="qualityRules.switchOn" active-text="开" inactive-text="关" inline-prompt />
  </div>
  <div class="mt-4 ml-4">
    <div class="flex flex-col">
      <el-radio-group v-model="qualityRules.qualityAlarm" :disabled="disabledAlarmRule">
        <el-radio :label="QulityAlarmRulesEnum.IMMEDIATELY">只要有超限就产生告警</el-radio>
        <el-radio :label="QulityAlarmRulesEnum.SETMULTIPLERULES">设置多项规则</el-radio>
      </el-radio-group>
      <div v-show="!checkIsDisabled" class="pl-6">
        <div class="flex items-center mb-3">
          <el-checkbox v-model="qualityRules.overRangeAlarm" :disabled="disabledAlarmRule" label="当采集点持续超限" />
          <div class="flex items-center">
            <el-input-number
              class="!w-24 ml-2 mr-2"
              v-model="qualityRules.overValue"
              :min="1"
              :step="1"
              controls-position="right"
              :disabled="ruleCollectionTime"
              @click.prevent
            />
            <el-select
              v-model="qualityRules.qulityUnit"
              class="w-20 mr-2"
              placeholder="秒"
              :disabled="ruleCollectionTime"
              @click.prevent
            >
              <el-option :label="DateUnitEnumMapDisplayName[DateUnitEnum.Minutes]" :value="DateUnitEnum.Minutes" />
              <el-option :label="DateUnitEnumMapDisplayName[DateUnitEnum.Seconds]" :value="DateUnitEnum.Seconds" />
            </el-select>
            <div class="text-base label">后则产生报警</div>
          </div>
        </div>

        <div class="flex items-center">
          <el-checkbox
            v-model="qualityRules.overPercentAlarm"
            :disabled="disabledAlarmRule"
            label="在报工时间内，设备采集异常数据比例超"
          />
          <div class="flex items-center">
            <div class="flex items-stretch">
              <el-input-number
                class="!w-24 ml-2"
                v-model="qualityRules.percentValue"
                :min="1"
                :step="1"
                controls-position="right"
                :disabled="ruleCollectionRatio"
                @click.prevent
              />
              <span class="mx-2 flex items-center px-2 ui-input-group-addon">%</span>
            </div>
            <div class="text-base label">后则产生报警</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, watchEffect, watch } from "vue";
import TitleBar from "@/components/TitleBar";
import { IQualityRule } from "@/models";
import { DateUnitEnum, DateUnitEnumMapDisplayName, QulityAlarmRulesEnum } from "@/enums";

const props = defineProps<{
  qualityRules: IQualityRule;
}>();

const qualityRules = reactive<Partial<IQualityRule>>({});

const checkIsDisabled = computed(() => qualityRules.qualityAlarm === QulityAlarmRulesEnum.IMMEDIATELY);

// 是否可以配置采集点超限告警
const ruleCollectionTime = computed(() => !qualityRules.overRangeAlarm || disabledAlarmRule.value);

// 是否可以配置 在报工时间内，设备采集异常数据比例超
const ruleCollectionRatio = computed(() => !qualityRules.overPercentAlarm || disabledAlarmRule.value);

// 是否禁用告警配置
const disabledAlarmRule = computed(() => !qualityRules.switchOn);

const emits = defineEmits<{
  (e: "setValue", qualityRules: IQualityRule): void;
}>();

watchEffect(() => {
  if (props.qualityRules != qualityRules) {
    Object.assign(qualityRules, props.qualityRules);
  }
});

watch(qualityRules, newValue => {
  emits("setValue", newValue as IQualityRule);
});
</script>

<style scoped lang="scss">
.describe-content {
  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    line-height: 30px;
    margin-top: 15px;
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;

  .sub-title {
    font-size: var(--el-font-size-extra-small);
    color: var(--el-text-color-placeholder);
    margin-left: 4px;
  }
}

.el-radio-group {
  display: grid;
}

.ui-input-group-addon {
  color: var(--el-text-color-secondary);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  box-shadow: 0 0 0 0.0625rem var(--el-input-border-color, var(--el-border-color)) inset;
}

:deep {
  .el-radio__input.is-checked + .el-radio__label,
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--el-text-color-regular);
  }
}

.label {
  color: var(--el-text-color-regular);
}
</style>
