import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, IExperimentReport, IExperimentReportReq } from "@/models";

/** 查询分页  */
export const queryExperimentReport = (id: string) => {
  const url: string = withApiGateway(`admin-api/ejj/project/experiment/report/list/${id}`);
  return http.get<IExperimentReportReq, IResponse<Array<IExperimentReport>>>(url);
};

/** 根据id 查询详情 */
export const getExperimentReportById = (id: string) => {
  const url: string = withApiGateway(`admin-api/ejj/project/experiment/report/${id}`);
  return http.get<string, IResponse<IExperimentReport>>(url);
};

/** 新增 */
export const createExperimentReport = (data: IExperimentReport) => {
  return http.post<IExperimentReport, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/experiment/report"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateExperimentReport = (data: IExperimentReport) => {
  return http.put<IExperimentReport, IResponse<boolean>>(
    withApiGateway("admin-api/ejj/project/experiment/report"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteExperimentReportById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/experiment/report/${id}`));
};
