import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IAssociatedCameraReq, IAssociatedCamera, IListResponse, IAssociatedCameraForm, IResponse } from "@/models";
// 获取关联摄像头列表
export const queryAssociatedCamera = (data: IAssociatedCameraReq) => {
  return http.post<IAssociatedCameraReq, IListResponse<IAssociatedCamera>>(
    withApiGateway("admin-api/business/monitorDevice/getList"),
    { data }
  );
};
// 关联摄像头
export const deviceRelationCamera = (data: IAssociatedCameraForm) => {
  return http.post<IAssociatedCameraForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/device/relationMonitor"),
    { data }
  );
};
// 移除关联摄像头
export const deleteAssociatedCamera = (deviceId: string, monitorId: string) => {
  return http.post<{ deviceId: string; monitorId: string }, IResponse<boolean>>(
    withApiGateway("admin-api/business/device/deleteRelationMonitor"),
    { data: { deviceId, monitorId } }
  );
};
