import { computed } from "vue";
import { IModeType, IModuleConfigItem } from "./type";
import WorkOrder from "./work-order/index.vue";
import ReportOrder from "./report-work/index.vue";

export function generateTabConfig(mode: IModeType) {
  return computed<Array<IModuleConfigItem>>(() => {
    if (mode === "all") {
      return [
        {
          name: "work-order",
          label: "工单",
          mineComponent: WorkOrder
        },
        {
          name: "report-work",
          label: "报工",
          mineComponent: ReportOrder
        }
      ];
    }
    return [
      {
        name: "report-work",
        label: "报工",
        mineComponent: ReportOrder
      }
    ];
  });
}
