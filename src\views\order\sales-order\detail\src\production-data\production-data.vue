<!-- 第三步：填报生产数据 -->
<template>
  <!-- 线缆 -->
  <ProductionOrder v-if="salesOrderStore.isCable" />
  <!-- 非线缆 -->
  <WorkOrder v-else />
</template>

<script setup lang="ts">
import ProductionOrder from "@/views/order/sales-order/detail/src/components/production-order/management/index.vue";
import WorkOrder from "./non-cable/work-order/index.vue";
import { useSalesOrderDetailStore } from "@/store/modules";

const salesOrderStore = useSalesOrderDetailStore();
</script>

<style scoped></style>
