import { getDataLogList, getTableOptions } from "@/api/logging/data-log";
import { IDataBaseList, IDataLogList, ISearchDataLog } from "@/models/logging";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isEmpty, isNullOrUnDef } from "@pureadmin/utils";
import { IOption } from "@/models";

export const useDataLogStore = defineStore({
  id: "data-log-store",
  state: () => ({
    tableOptions: [] as Array<IOption>,
    tableTotal: 0,
    queryParams: {} as ISearchDataLog,
    dataLogTableData: [] as Array<IDataLogList>
  }),
  actions: {
    /** 获取对象表的数据 */
    async getTableOptionsAction() {
      const res = await getTableOptions();
      this.tableOptions = (res.data || []).map((item: IDataBaseList) => {
        return {
          label: `${item.tableExplain}(${item.tableName})`,
          value: item.id
        };
      });
    },
    /** 查询数据日志 */
    async queryDataLogList(params: ISearchDataLog) {
      this.queryParams = { ...this.queryParams, ...params };
      await this.getDataLogListAction();
    },
    /** 获取日志列表 */
    async getDataLogListAction() {
      const res = await getDataLogList(omitBy(this.queryParams, value => isEmpty(value) || isNullOrUnDef(value)));
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.dataLogTableData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.dataLogTableData = [];
        this.tableTotal = 0;
      }
    }
  }
});
