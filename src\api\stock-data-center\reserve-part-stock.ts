import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models";
import {
  ISearchSparePartReq,
  ISparePartProduct,
  IUpateSparePartReq
} from "@/models/stock-data-center/i-reserve-part-stock";

/**
 * 获取备品备件的数据列表
 */
export function getSparePartProductList(queryParams: ISearchSparePartReq) {
  const url = withApiGateway(`admin-api/business/spare/getSparePartPage`);
  return http.post<ISearchSparePartReq, IListResponse<ISparePartProduct>>(url, {
    data: queryParams
  });
}

/**
 * 新增备品备件
 */
export function addSparePart(paramsData: ISparePartProduct) {
  const url = withApiGateway(`admin-api/business/spare/createSparePart`);
  return http.post<ISparePartProduct, IResponse<boolean>>(
    url,
    {
      data: paramsData
    },
    { showErrorInDialog: true }
  );
}

/**
 * 修改备品备件
 */
export function putSparePart(paramsData: IUpateSparePartReq) {
  const url = withApiGateway(`admin-api/business/spare/updateSparePart/${paramsData?.id}`);
  return http.put<ISparePartProduct, IResponse<boolean>>(
    url,
    {
      data: paramsData
    },
    { showErrorInDialog: true }
  );
}

/**
 * 删除备品备件
 */
export function delSparePart(id: string) {
  const url = withApiGateway(`admin-api/business/spare/deleteSparePart/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}

/**
 * 根据Id获取备品备件的详情
 */
export function getSparePartDetailById(id: string) {
  const url = withApiGateway(`admin-api/business/spare/getSparePartById/${id}`);
  return http.get<void, IResponse<ISparePartProduct>>(url);
}
