<template>
  <div class="select-product-process-inspect">
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="productionProcessData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      @row-click="rowClick"
      @selection-change="selectionChange"
      @page-current-change="pageNoChange"
      @page-size-change="pageSizeChange"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { IProductOrder } from "@/models/product-order";
import { IOrdersForChooseReq, IProcessInspectFromOrderRes } from "@/models/production-test-sensing";
import { useTableConfig } from "@/utils/useTableConfig";
import { inject, onMounted, ref } from "vue";
import { useColumns } from "./columns";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCopyProductOrderStore } from "@/store/modules/production-test-sensing";
import { ProductOrWorkOrderEnum } from "@/enums/purchase-order";
import { COPY_FROM_ORDER_TOKEN } from "../../tokens";

const props = defineProps<{
  productInfo?: IProductOrder;
  modelValue?: IProcessInspectFromOrderRes[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", data: IProcessInspectFromOrderRes[]);
}>();
const copyCtx = inject(COPY_FROM_ORDER_TOKEN);
const copyProductOrderStore = useCopyProductOrderStore();
const { pagination } = useTableConfig();
const queryParams = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize,
  orderIds: [props.productInfo?.id]
};
const { columns } = useColumns();
const tableInstance = ref<PureTableInstance>();
const productionProcessData = ref<Array<IProcessInspectFromOrderRes>>([]);
const loading = ref<boolean>(false);
const initProcessInspectList = useLoadingFn(getProcessInspectList, loading);

onMounted(() => {
  initProcessInspectList(queryParams);
});

/** 获取原材料检测数据 */
async function getProcessInspectList(params: IOrdersForChooseReq) {
  const orderType = copyCtx.isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
  const res = await copyProductOrderStore.getProcessInspectFromOrder({ ...params, orderType });
  productionProcessData.value = res.list || [];
  pagination.total = res.total || 0;
}

/** 点击行 */
function rowClick(row: IProcessInspectFromOrderRes) {
  tableInstance.value?.getTableRef()?.toggleRowSelection(row, undefined);
}

/** 页码 */
function pageNoChange(pageNo: number) {
  Object.assign(queryParams, { pageNo });
  initProcessInspectList(queryParams);
}

/** 页码数量 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  Object.assign(queryParams, { pageNo: pagination.currentPage, pageSize });
  initProcessInspectList(queryParams);
}

/** 选择切换  */
function selectionChange(val: IProcessInspectFromOrderRes[]) {
  emit("update:modelValue", val);
}
</script>

<style scoped lang="scss"></style>
