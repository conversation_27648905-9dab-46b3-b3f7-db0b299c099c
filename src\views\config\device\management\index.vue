<template>
  <div class="bg-bg_color pt-[8px] pb-4 px-6">
    <SearchForm @onSearch="onSearch($event)" @onAddDevice="onAddDevice()" />
  </div>
  <div class="flex flex-col flex-1 p-5 mx-6 my-5 overflow-hidden bg-bg_color">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="deviceStore.devices"
      :columns="state.columns"
      :pagination="pagination"
      close-on-click-modal
      destroy-on-close
      :loading="deviceStore.loading"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
    >
      <template #operation="data">
        <div>
          <ElButton type="primary" v-track="TrackPointKey.META_DEVICE_EDIT" link @click="onDetailsDevice(data.row)">
            详情
          </ElButton>
          <ElButton
            type="primary"
            v-auth="PermissionKey.meta.metaDeviceEdit"
            v-track="TrackPointKey.META_DEVICE_EDIT"
            link
            @click="onEditDeviceModalVis(data.row)"
          >
            编辑
          </ElButton>
          <ElButton
            type="danger"
            link
            v-auth="PermissionKey.meta.metaDeviceDelete"
            v-track="TrackPointKey.META_DEVICE_DELETE"
            @click="onDeleteDevice(data.row)"
          >
            删除
          </ElButton>
          <ElButton
            type="primary"
            link
            v-auth="PermissionKey.meta.metaDeviceSyncIot"
            v-if="state.licenseAuthIncludeIOT"
            @click="onSynchronizationOT(data.row.id)"
          >
            同步到IOT
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getDeviceFormModalTitle()"
      align-center
      class="default"
      v-model="state.deviceFormModalVis"
      :close-on-click-modal="false"
      destroy-on-close
      :close-on-press-escape="false"
      @close="onCloseDeviceFormModal()"
    >
      <DeviceForm ref="deviceFormRef" />
      <template #footer>
        <el-button @click="onCancelDeviceFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveDevice()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ICreateDevice, IDevice, IDeviceReq } from "@/models";
import { useColumns } from "./columns";
import SearchForm from "./search-form.vue";
import { reactive, ref, watchEffect } from "vue";
import { useDeviceStore, useSystemAuthStore } from "@/store/modules";
import { ElMessageBox, ElMessage } from "element-plus";
import { IDeviceForm } from "../device-form/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import DeviceForm from "../device-form/index.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { PermissionKey, TrackPointKey } from "@/consts";
import { usePageStoreHook } from "@/store/modules/page";
import { useRouter } from "vue-router";
import { useConfirm } from "@/utils/useConfirm";
import { TableWidth } from "@/enums";
usePageStoreHook().setTitle("生产设备管理");

const { columns } = useColumns();
const deviceStore = useDeviceStore();
const { pagination } = useTableConfig();
const systemAuthStore = useSystemAuthStore();

const router = useRouter();
pagination.pageSize = 20;

const state = reactive<{
  deviceFormModalVis: boolean;
  isAddDevice: boolean;
  params: IDeviceReq;
  licenseAuthIncludeIOT: boolean;
  columns: TableColumnList;
}>({
  deviceFormModalVis: false,
  isAddDevice: false,
  params: {},
  licenseAuthIncludeIOT: false,
  columns: []
});

const deviceFormRef = ref<IDeviceForm | null>();
const saveLoading = ref<boolean>(false);
const handleSaveDevice = useLoadingFn(saveDevice, saveLoading);
const getDeviceFormModalTitle = () => (state.isAddDevice ? "新增设备" : "编辑设备");

deviceStore.queryDevice(queryParams());

watchEffect(() => {
  pagination.total = deviceStore.total;
});

watchEffect(async () => {
  state.licenseAuthIncludeIOT = await systemAuthStore.checkLicenseAuthIncludeIOT;
  if (!state.licenseAuthIncludeIOT) {
    state.columns = columns
      .map(col => ({
        ...col,
        width: col.prop === "operation" ? TableWidth.operations : col.width
      }))
      .filter(({ prop }) => !["iotSyncStatus", "syncResult", "lastSyncTime"].includes(prop as string));
  } else {
    state.columns = columns;
  }
});

const onSearch = (params: IDeviceReq) => {
  state.params = params;
  pagination.currentPage = 1;
  deviceStore.queryDevice(queryParams());
};

const onAddDevice = () => {
  state.isAddDevice = true;
  state.deviceFormModalVis = true;
};

const onEditDeviceModalVis = async (device: IDevice) => {
  state.isAddDevice = false;
  await deviceStore.getDeviceDetailById(device.id);
  state.deviceFormModalVis = true;
};

const onDetailsDevice = (device: IDevice) => {
  router.push("/device/" + device.id);
};

const onDeleteDevice = (device: IDevice) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const delRes = await deviceStore.deleteDevice(device.id);
      if (!delRes.data) {
        ElMessage.error(delRes.msg || "网络异常");
        return;
      }
      ElMessage.success("删除成功");
      pagination.currentPage = 1;
      deviceStore.queryDevice(queryParams());
      deviceStore.queryDeviceAllowAdd();
    })
    .catch(() => {});
};

async function saveDevice() {
  const formValue: ICreateDevice | boolean = await deviceFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }

  if (!formValue.id) {
    await deviceStore.createDevice(formValue);
    // 设备新增完成后，查询是否可以继续新增设备
    await deviceStore.queryDeviceAllowAdd();
  } else {
    await deviceStore.editDevice(formValue);
  }
  state.deviceFormModalVis = false;
  deviceStore.setDevice();
  deviceFormRef.value.resetFormValue();
  ElMessage.success(formValue.id ? "修改成功" : "新增成功");
  pagination.currentPage = 1;
  deviceStore.queryDevice(queryParams());
}

const onSynchronizationOT = async (id: string) => {
  if (!(await useConfirm("确认生产设备同步到IOT", "同步到IOT"))) {
    return;
  }
  const { data: isCompleted } = await deviceStore.syncDeviceById(id);
  if (isCompleted) {
    ElMessage({ type: "success", message: "生产设备同步完成" });
    deviceStore.queryDevice(queryParams());
  }
};

const onCancelDeviceFormModal = () => {
  state.deviceFormModalVis = false;
};

const onCloseDeviceFormModal = () => {
  deviceFormRef.value.resetFormValue();
  deviceStore.setDevice();
};

const onPageSizeChange = () => {
  deviceStore.queryDevice(queryParams());
};

const onCurrentPageChange = () => {
  deviceStore.queryDevice(queryParams());
};
function queryParams() {
  return { ...state.params, pageNo: pagination.currentPage, pageSize: pagination.pageSize };
}
</script>

<style scoped lang="scss"></style>
