import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IEquipmentInfo, IEquipmentInfoForm, IEquipmentInfoReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryEquipmentInfo = (data: IEquipmentInfoReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/entityInfoDOs/pageList`);
  return http.post<IEquipmentInfoReq, IListResponse<IEquipmentInfo>>(url, {
    data
  });
};

/** 查询分页  */
export const queryEquipmentInfoList = (data: IEquipmentInfoReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/entityInfoDOs/getEntityInfoList`);
  return http.post<IEquipmentInfoReq, IResponse<Array<IEquipmentInfo>>>(url, {
    data
  });
};

/** 查询分页  */
export const queryEquipmentInfoListByProductionOrderNo = (productionOrderNo: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/entityInfoDOs/getEntityInfoList/${productionOrderNo}`);
  return http.get<IEquipmentInfoReq, IResponse<Array<IEquipmentInfo>>>(url);
};

/** 根据id 查询详情 */
export const getEquipmentInfoById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/entityInfoDOs/${id}`);
  return http.get<string, IResponse<IEquipmentInfo>>(url);
};

/** 新增 */
export const createEquipmentInfo = (data: IEquipmentInfoForm) => {
  return http.post<IEquipmentInfoForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/entityInfoDOs/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateEquipmentInfo = (data: IEquipmentInfoForm) => {
  return http.put<IEquipmentInfoForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/entityInfoDOs/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteEquipmentInfoById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/entityInfoDOs/${id}`));
};
