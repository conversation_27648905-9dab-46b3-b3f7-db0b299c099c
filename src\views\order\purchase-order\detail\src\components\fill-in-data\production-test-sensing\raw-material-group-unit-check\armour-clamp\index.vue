<template>
  <div>
    <div class="flex-1 flex flex-col justify-between mb-2">
      <div class="flex-1">
        <SwitchTab :switch-tags="rawMaterialGroupUnitStore.switchTabList" @switch-change="switchTag($event)" />
      </div>
      <div class="select-material">
        <el-button
          v-auth="PermissionKey.form.formPurchaseRawMaterialEdit"
          v-track="TrackPointKey.FORM_PURCHASE_PD_RAWMATERIAL_EXP_SELECT"
          type="primary"
          @click="selectRawMaterial()"
        >
          <FontIcon class="mr-2" icon="icon-plus" />
          选用原材料检
        </el-button>
        <el-button
          v-auth="PermissionKey.form.formPurchaseRawMaterialCreate"
          v-track="TrackPointKey.FORM_PURCHASE_PD_RAWMATERIAL_EXP_CREATE"
          type="primary"
          @click="addRawMaterialInspect()"
          v-if="isCanOperateAddAndEdit"
        >
          <FontIcon class="mr-2" icon="icon-plus" />
          新增原材料检
        </el-button>
      </div>
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="rawMaterialGroupUnitStore.rawMaterialTableData"
      :columns="columns"
      :height="300"
      :pagination="pagination"
      showOverflowTooltip
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <!-- 选用原材料检 -->
    <el-dialog
      v-model="selectRawMaterialVisible"
      title="选用原材料检"
      class="middle"
      align-center
      :destroy-on-close="true"
      @close="onSelectCancel"
    >
      <div class="material-content">
        <SelectRawMaterial />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onSelectCancel">取消</el-button>
          <el-button
            type="primary"
            :disabled="!rawMaterialGroupUnitStore.checkedRawMaterialData?.length"
            :loading="saveLoading"
            @click="saveRawMaterialInspect"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 新增原材料检(v1 暂时只用在编辑时，使用) -->
    <el-dialog
      v-model="rawMaterialGroupUnitStore.addRawMaterialVisibleRef"
      :title="diagTitle"
      class="middle"
      top="5vh"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="onCancelAddOrder()"
    >
      <div class="">
        <AddRawMaterial ref="addRawMaterialInstance" :notRefreshList="false" @stateChange="stateChange($event)" />
      </div>
      <template #footer>
        <span :class="{ hidden: state.step !== StepEnum.materialBaseInfo }">
          <el-button @click="onCancelAddOrder()">取消</el-button>
          <el-button type="primary" @click="onNextStep()">下一步</el-button>
        </span>

        <span :class="['hidden', { block: state.step !== StepEnum.materialBaseInfo }]">
          <el-button @click="onCancelAddOrder()">取消</el-button>
          <el-button @click="onPreStep()">上一步</el-button>
          <el-button type="warning" v-if="isAddDiag" @click="onSaveAndAdd()">保存并继续新增</el-button>
          <el-button type="primary" :loading="saveLoading" @click="onSave()">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新增原材料检（V2） -->
    <el-dialog
      v-model="addRawMaterialInspecRef"
      title="新增原材料检"
      class="middle"
      top="5vh"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @closed="closeAddRawMaterialInspect()"
    >
      <AddRawMaterialInspec
        ref="addRawMaterialV2Instance"
        :processInfo="currentTagInfo"
        @saveAddSuccess="saveAddSuccess"
      />
      <template #footer>
        <el-button @click="addRawMaterialInspecRef = false">取消</el-button>
        <el-button type="primary" :loading="addRawMaterialV2Instance?.saveLoading" @click="saveAddInspect()"
          >保存</el-button
        >
      </template>
    </el-dialog>

    <!-- 原材料详情 -->
    <DetailRawMaterial @detailCloseDiag="detailCloseDiag" />
  </div>
</template>

<script setup lang="ts">
import SelectRawMaterial from "../component/select-raw-material/index.vue";
import AddRawMaterial from "./add-raw-material/index.vue";
import DetailRawMaterial from "./detai-raw-material/index.vue";
import SwitchTab from "../../component/switch-tab/index.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { ref, onUnmounted, reactive, computed, onMounted, watchEffect } from "vue";
import { useRawMaterialInspectColumns } from "../hooks/useRawMaterialInspectColumns";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { ISwitchTagEventType } from "../../component/switch-tab/types";
import { ElMessage } from "element-plus";
import { useFillInDataStore, usePurchaseOrderDetailStore } from "@/store/modules";
import { useProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { watchOnce } from "@vueuse/core";
import { PermissionKey, TrackPointKey } from "@/consts";
import { EDiagType } from "@/models/raw-material/i-raw-material-res";
/**  ================= 原材料优化 ============= */
import AddRawMaterialInspec from "./add-raw-material-v2/index.vue";
import { useTableConfig } from "@/utils/useTableConfig";

// 获取原材料检测组部件的工序及表格数据
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const productionTestSensingStore = useProductionTestSensingStore();
const fillInDataStore = useFillInDataStore();

// 原材料检，组部件表格数据
const currentTagInfo = ref();
// 选用原材料
const selectRawMaterialVisible = ref(false);
const addRawMaterialInstance = ref();
const addRawMaterialV2Instance = ref<InstanceType<typeof AddRawMaterialInspec>>();
const isAddDiag = computed(() => rawMaterialGroupUnitStore.isAdd);
const diagTitle = computed(() => {
  return rawMaterialGroupUnitStore.isAdd ? "新增原材料检" : "编辑原材料检";
});
const processInfoList = computed(() => rawMaterialGroupUnitStore.switchTabList);
// 自定义列模板
const { columns } = useRawMaterialInspectColumns();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const saveLoading = ref<boolean>(false);

/**  ================= 原材料优化 ============= */
// 新增原材料
const addRawMaterialInspecRef = ref<boolean>(false);
/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => rawMaterialGroupUnitStore.getIsCanOperateAddAndEdit);

watchEffect(() => {
  pagination.total = rawMaterialGroupUnitStore.rawMaterialTableTotal;
});

onMounted(() => {
  // 初始化的时重置弹框状态
  rawMaterialGroupUnitStore.setEditRawMaterialVisible(false, EDiagType.Add);
  rawMaterialGroupUnitStore.getRawMaterialGroupUnitProcessAction();
});

watchOnce(processInfoList, newVal => {
  if (newVal) {
    currentTagInfo.value = newVal[0];
    rawMaterialGroupUnitStore.currentTagInfo = currentTagInfo.value;
  }
});

enum StepEnum {
  materialBaseInfo = 0,
  materialTestInfo = 1,
  complete = 2
}

interface IProp {
  step: StepEnum;
}

// 步骤条
const state = reactive<IProp>({
  step: StepEnum.materialBaseInfo
});

// 选用原材料弹框显示
const selectRawMaterial = () => {
  selectRawMaterialVisible.value = true;
  currentTagInfo.value = currentTagInfo.value ? currentTagInfo.value : rawMaterialGroupUnitStore.switchTabList[0];
  // 保存当前选择的原材料工序
  rawMaterialGroupUnitStore.currentTagInfo = currentTagInfo.value;
};
// 关闭选用原材料弹框
const onSelectCancel = () => {
  rawMaterialGroupUnitStore.selectQueryParams.keyWords = null;
  selectRawMaterialVisible.value = false;
};

// 保存选择的原材料
const saveRawMaterialInspect = async () => {
  const checkedRawMaterialData = rawMaterialGroupUnitStore.checkedRawMaterialData;
  if (checkedRawMaterialData.length) {
    const { processId, processCode, processName } = currentTagInfo.value;
    const dataId = fillInDataStore.dataId;
    const { purchaseOrderId, isCable } = usePurchaseOrderDetailStore();
    const orderParam: { productionId?: string; workOrderId?: string } = {};
    isCable && (orderParam.productionId = dataId);
    !isCable && ((orderParam.workOrderId = dataId), (orderParam.productionId = fillInDataStore.productionId));
    const paramsData = {
      ...orderParam,
      purchaseId: purchaseOrderId,
      // rawMaterialId: (checkedRawMaterialData || []).map(item => item.id),
      processId,
      processCode,
      processName,
      inspectIds: (checkedRawMaterialData || []).map(item => item.id)
    };
    saveLoading.value = true;
    await rawMaterialGroupUnitStore.batchSaveRawMaterial(paramsData).finally(() => (saveLoading.value = false));
    onSelectCancel();
    rawMaterialGroupUnitStore.checkedRawMaterialData = [];
    rawMaterialGroupUnitStore.getRawMaterialGroupUnitProcessAction();
    productionTestSensingStore.refreshProductionProcessStatus();
  } else {
    ElMessage.warning("请选择原材料数据再保存！");
  }
};

// 切换同步的原材料时
const switchTag = (tag: ISwitchTagEventType) => {
  currentTagInfo.value = tag.data;
  rawMaterialGroupUnitStore.currentTagInfo = currentTagInfo.value;
  const { processCode } = tag.data;
  rawMaterialGroupUnitStore.getRawMaterialGroupUnitTable({ processCode });
};

// 关闭详情的弹框
const detailCloseDiag = () => {
  rawMaterialGroupUnitStore.setDetailRawMaterialVisible(false);
};

// 关闭新增原材料的弹框
const onCancelAddOrder = () => {
  resetData();
  rawMaterialGroupUnitStore.addRawMaterialVisibleRef = false;
};

// 下一步
const onNextStep = () => {
  // 校验表单
  addRawMaterialInstance.value?.onNextStep();
};

// 上一步
const onPreStep = () => {
  addRawMaterialInstance.value?.onPreStep();
};

// 保存并继续新增
const onSaveAndAdd = async () => {
  await addRawMaterialInstance.value?.saveFormData(true);
  ElMessage.success("新增成功");
};

// 保存
const onSave = async () => {
  saveLoading.value = true;
  await addRawMaterialInstance.value?.saveFormData().finally(() => (saveLoading.value = false));
  onCancelAddOrder();
  ElMessage.success(rawMaterialGroupUnitStore.isAdd ? "新增成功" : "编辑成功");
};

/**
 * 步骤调改变
 * @param step
 */
const stateChange = (step: StepEnum) => {
  state.step = step;
};

// 重置数据信息
const resetData = () => {
  state.step = StepEnum.materialBaseInfo;
  rawMaterialGroupUnitStore.initStoreData();
};

/**  ================= 原材料优化（v2）开始 ============= */
/**
 * 新增原材料检
 */
const addRawMaterialInspect = () => {
  addRawMaterialInspecRef.value = true;
  rawMaterialGroupUnitStore.setEditRawMaterialVisible(false, EDiagType.Add);
};
/**
 * 关闭新增原材料检弹框
 */
const closeAddRawMaterialInspect = () => {
  addRawMaterialInspecRef.value = false;
  rawMaterialGroupUnitStore.initStoreCollectionItem();
};

/**
 * 保存新增的原材料检测
 */
const saveAddInspect = () => {
  addRawMaterialV2Instance.value.addRawMaterialInspectData();
};
/**
 * 新增原材料检测数据成功
 */
const saveAddSuccess = () => {
  ElMessage.success("新增成功");
  closeAddRawMaterialInspect();
  rawMaterialGroupUnitStore.getRawMaterialGroupUnitProcessAction();
  productionTestSensingStore.refreshProductionProcessStatus();
};
/**  ================= 原材料优化（v2）结束 ============= */

// 组件销毁时清空过了条件
onUnmounted(() => {
  rawMaterialGroupUnitStore.queryRawMaterialParams = {};
});
</script>

<style scoped lang="scss">
.hidden {
  display: none;
}

.block {
  display: block;
}
</style>
