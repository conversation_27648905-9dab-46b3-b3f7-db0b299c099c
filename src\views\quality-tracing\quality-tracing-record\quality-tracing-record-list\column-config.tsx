import { computed, ComputedRef } from "vue";
import { RouterLink } from "vue-router";
import { useMaterial } from "@/utils/material";
import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateEnum } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { EnumCell } from "@/components/TableCells";
import { emptyDefaultValue } from "@/consts";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const { isCableBySubClassCode } = useMaterial();
/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig(subClassCode: ComputedRef<string>) {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = computed<TableColumnList>(() => [
    {
      label: isCableBySubClassCode(subClassCode.value) ? "生产订单号" : "生产工单号",
      prop: "dataNo",
      minWidth: ColumnWidth.Char14,
      headerRenderer: () => (
        <KeywordAliasHeader
          code={isCableBySubClassCode(subClassCode.value) ? KeywordAliasEnum.IPO_NO : "product_work_order_no"}
          defaultText={
            isCableBySubClassCode(subClassCode.value) ? KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] : "生产工单号"
          }
        />
      ),
      fixed: "left",
      slot: "dataNo"
    },
    {
      label: "销售订单号",
      prop: "soNos",
      minWidth: ColumnWidth.Char14,
      slot: "soNos"
    },
    {
      label: "采购订单号",
      prop: "poNos",
      minWidth: ColumnWidth.Char14,
      slot: "poNos"
    },
    {
      label: "生产状态",
      prop: "ipoStatus",
      width: ColumnWidth.Char11,
      cellRenderer: (data: TableColumnRenderer) => EnumCell(data, ProductionStateEnum, "productionStateEnum")
    },
    {
      label: "质量规范",
      prop: "qualityName",
      minWidth: ColumnWidth.Char8,
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.qualityId ? (
          <RouterLink to={`/quality-specification/${data.row.qualityId}`} class="text-primary">
            {data.row.qualityName}
          </RouterLink>
        ) : (
          <span>{emptyDefaultValue}</span>
        );
      }
    },
    {
      label: "原材料检（分）",
      prop: "rawMaterialScore",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "生产过程（分）",
      prop: "processScore",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "出厂试验（分）",
      prop: "experimentScore",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "工艺稳定性（分）",
      prop: "processStabilityScore",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "更新时间",
      prop: "calcTime",
      sortable: "custom",
      minWidth: ColumnWidth.Char14,
      formatter: dateFormatter()
    },
    {
      label: "得分",
      prop: "totalScore",
      sortable: "custom",
      fixed: "right",
      minWidth: ColumnWidth.Char4
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char14,
      fixed: "right",
      slot: "opertion"
    }
  ]);

  return { columnsConfig };
}
