<template>
  <div class="h-full flex" v-loading="loading">
    <div class="flex-1">
      <v-chart :option="option" autoresize class="w-full h-full" />
    </div>
    <div class="w-60 flex items-center">
      <el-descriptions :column="1" size="small">
        <el-descriptions-item align="right" label="样本数" class="flex justify-between">{{
          cardData.totalCount
        }}</el-descriptions-item>
        <el-descriptions-item align="right" label="均值">{{ cardData.means }}</el-descriptions-item>
        <el-descriptions-item align="right" label="均方差">{{ cardData.standardDeviations }}</el-descriptions-item>
        <el-descriptions-item align="right" label="最大值">{{ cardData.maxV }}</el-descriptions-item>
        <el-descriptions-item align="right" label="最小值">{{ cardData.minV }}</el-descriptions-item>
        <el-descriptions-item align="right" label="开始时间">{{ cardData.firstTime }}</el-descriptions-item>
        <el-descriptions-item align="right" label="结束时间">{{ cardData.tailTime }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import VChart from "vue-echarts";
import { getNormalDistributionData } from "@/api/device/acquisition-maintain";
import { fullDateFormat } from "@/consts/date-format";
import { INormalDistributionData } from "@/models/device/acquisition/i-device-acquisition";
import { formatDate } from "@/utils/format/date";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genEchartsConfig } from "./chart-config";
import { formatDecimal } from "@/utils/format/decimal";

const props = defineProps<{
  deviceId: string;
  startTime: string;
  endTime: string;
  pointName: string;
  partCount: 11;
}>();
// echrts数据
const countList = ref<Array<number>>([]);
const probabilityList = ref<Array<number>>([]);
const categoryList = ref<Array<string>>([]);
const loading = ref(false);

/** 卡片展示数据 */
const cardData = ref({
  totalCount: "",
  means: "",
  standardDeviations: "",
  maxV: "",
  minV: "",
  firstTime: "",
  tailTime: ""
});

const option = genEchartsConfig(categoryList, countList, probabilityList);
const decimalType = "0.-2";

/**
 * @description: 根据原始数据生成echarts数据
 */
function genEchartsData(data: INormalDistributionData) {
  const { means, rangeCount: temp = [], standardDeviations } = data;
  const categoryArr: Array<string> = [];
  const countArr: Array<number> = [];
  const probabilityArr: Array<number> = [];
  temp.forEach(str => {
    const strArr = str.split(",");
    categoryArr.push(`[${formatDecimal(+strArr[0], decimalType)},${formatDecimal(+strArr[1], decimalType)}]`);
    countArr.push(+strArr[2]);
    probabilityArr.push(
      calcNormalDistributionPointer(
        +formatDecimal(+strArr[0], decimalType),
        +formatDecimal(means, decimalType),
        +formatDecimal(standardDeviations, decimalType)
      )
    );
  });
  categoryList.value = categoryArr;
  countList.value = countArr;
  probabilityList.value = probabilityArr;
}

/**
 * @description: 根据原始数据生成卡片数据
 */
function genCardData(data: INormalDistributionData) {
  cardData.value = {
    totalCount: formatDecimal(data.totalCount, decimalType),
    means: formatDecimal(data.means, decimalType),
    standardDeviations: formatDecimal(data.standardDeviations, decimalType),
    maxV: formatDecimal(data.maxV, decimalType),
    minV: formatDecimal(data.minV, decimalType),
    firstTime: formatDate(data.firstTime, fullDateFormat),
    tailTime: formatDate(data.tailTime, fullDateFormat)
  };
}

/**
 * @description: 计算正太分布点
 * @param {*} x 数据源中数据项的第一个数
 * @param {*} means 平均值
 * @param {*} standardDeviations 均方差
 */
function calcNormalDistributionPointer(x: number, means: number, standardDeviations: number) {
  if (standardDeviations === 0) {
    return 0;
  }
  return +(
    (1 / (Math.sqrt(Math.PI * 2) * standardDeviations)) *
    Math.exp((((x - means) * (x - means)) / (standardDeviations * standardDeviations * 2)) * -1)
  ).toFixed(2);
}

const requestData = useLoadingFn(async () => {
  const { data } = await getNormalDistributionData(props);
  genEchartsData(data);
  genCardData(data);
}, loading);

onMounted(requestData);
</script>

<style scoped lang="scss">
:deep(.el-descriptions .el-descriptions__body .el-descriptions__table:not(.is-bordered)) {
  .el-descriptions__row {
    @apply mt-2;
  }

  .el-descriptions__cell {
    width: 100%;

    .el-descriptions__content {
      display: block;
    }
  }
}
</style>
