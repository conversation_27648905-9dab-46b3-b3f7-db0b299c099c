import { defineStore } from "pinia";
import * as api from "@/api/production-test-sensing/copy-product-order";
import { IOrderListReq } from "@/models/production-test-sensing";

export const useSelectProductOrderStore = defineStore({
  id: "select-product-order-store",
  state: () => ({
    dataList: [],
    total: 0
  }),
  actions: {
    /** 获取生产订单的列表数据 */
    async getProductOrderList(productionId: string, params: IOrderListReq) {
      const res = (await api.getProductionOrderList(productionId, params)).data;
      if (Array.isArray(res.list) && res.list?.length) {
        this.dataList = res.list;
        this.total = res.total;
      } else {
        this.dataList = [];
        this.total = 0;
      }
    },

    /** 获取生产工单的列表数据 */
    async getWorkOrderList(productionId: string, params: IOrderListReq) {
      const res = (await api.getWorkOrderList(productionId, params)).data;
      if (Array.isArray(res.list) && res.list?.length) {
        this.dataList = res.list;
        this.total = res.total;
      } else {
        this.dataList = [];
        this.total = 0;
      }
    }
  }
});
