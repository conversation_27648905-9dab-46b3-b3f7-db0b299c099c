<template>
  <pure-table
    class="h-full overflow-hidden"
    row-key="id"
    show-overflow-tooltip
    size="large"
    :columns="props.columns"
    :data="props.data"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { PureTable } from "@pureadmin/table";
import { IFileSync } from "@/models";

const props = defineProps<{
  data: Array<IFileSync>;
  columns: TableColumnList;
}>();
</script>

<style scoped></style>
