<template>
  <el-dialog
    title="编辑工序文档"
    class="cx-form"
    v-model="editVisible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <ProcessDocumentForm ref="editFormRef" :processDocInfo="processDocInfo" :sub-class-code="subclassCode" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import ProcessDocumentForm from "@/views/order/purchase-order/detail/src/components/fill-in-data/attached-report/attached-report-info/attached-report-from.vue";
import { useEdit } from "@/views/components/state-grid-order-sync/hooks";
import { provide, reactive, ref, watch } from "vue";
import { useAttachReportStore } from "@/store/modules";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";
import { IProcessDocumentSync } from "@/models/state-grid-sync/iot-sync/i-process-document-sync";
import { IFileSync } from "@/models";

const props = defineProps<{
  subclassCode: string | undefined;
}>();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof ProcessDocumentForm>>(updateProductionInfo);

const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});
const attachReportStore = useAttachReportStore();
const processDocInfo = ref();

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

async function openEditDialog(sync: IProcessDocumentSync) {
  const fileInfo = sync.fileList.map((item: IFileSync) => {
    return {
      id: item.fileId,
      name: item.fileName,
      url: item.fileUrl
    };
  });
  processDocInfo.value = { ...sync, fileInfo: fileInfo[0] };
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: sync.dataId,
    no: sync.ipoNo
  };
}

async function updateProductionInfo() {
  const formValue = await editFormRef.value.getFormValue();
  if (!formValue) {
    return;
  }
  const { processIds, fileInfo, id } = formValue;
  const params = {
    id,
    processId: processIds,
    fileId: fileInfo.id
  };
  await attachReportStore.saveProcessDocument(params);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
