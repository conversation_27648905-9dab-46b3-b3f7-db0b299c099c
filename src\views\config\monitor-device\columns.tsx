import {
  ColumnWidth,
  IOTSyncStatusEnum,
  MonitorDeviceBrandEnum,
  MonitorDeviceRunningStatusEnum,
  MonitorDeviceRunningStatusMapColor,
  MonitorDeviceRunningStatusMapDesc,
  MonitorDeviceStatusEnum,
  MonitorDeviceSubTypeEnum,
  TableWidth
} from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
import { IMonitorDevice } from "@/models";
import { formatEnum } from "@/utils/format";
import dayjs from "dayjs";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { enumFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "摄像头编号",
      prop: "no",
      width: TableWidth.largeNumber
    },
    {
      label: "摄像头名称",
      prop: "name",
      width: TableWidth.name
    },
    {
      label: "NVR IP地址",
      prop: "nvrIp",
      width: TableWidth.order,
      showOverflowTooltip: true
    },
    {
      label: "通道号",
      prop: "channel",
      showOverflowTooltip: true
    },
    {
      label: "端口",
      prop: "port",
      showOverflowTooltip: true
    },
    {
      label: "品牌",
      prop: "brandCode",
      formatter: (row: IMonitorDevice) => formatEnum(row.brandCode, MonitorDeviceBrandEnum, "MonitorDeviceBrandEnum"),
      showOverflowTooltip: true
    },
    {
      label: "码流类型",
      prop: "subType",
      formatter: (row: IMonitorDevice) => formatEnum(row.subType, MonitorDeviceSubTypeEnum, "MonitorDeviceSubTypeEnum"),
      showOverflowTooltip: true
    },
    {
      label: "iot同步状态",
      prop: "iotSyncStatus",
      width: TableWidth.status,
      formatter: enumFormatter(IOTSyncStatusEnum, "IOTSyncStatusEnum"),
      showOverflowTooltip: true
    },
    {
      label: "同步日志",
      prop: "syncResult",
      width: TableWidth.reason,
      showOverflowTooltip: true
    },
    {
      label: "最近同步时间",
      prop: "lastSyncTime",
      width: TableWidth.dateTime,
      formatter: (row: IMonitorDevice) => {
        if (dayjs(row.lastSyncTime).isValid()) {
          return dayjs(row.lastSyncTime).format("YYYY-MM-DD HH:mm:ss");
        }
      },
      showOverflowTooltip: true
    },
    {
      label: "启用状态",
      prop: "status",
      fixed: "right",
      cellRenderer(data: TableColumnRenderer) {
        const status: MonitorDeviceStatusEnum = data.row.status;
        if (!status) {
          return null;
        }
        return status === MonitorDeviceStatusEnum.DISABLE ? (
          <CxTag type="danger">关闭</CxTag>
        ) : (
          <CxTag type="success">开启</CxTag>
        );
      },
      showOverflowTooltip: true
    },
    {
      label: "运行状态",
      prop: "runningStatus",
      fixed: "right",
      cellRenderer(data: TableColumnRenderer) {
        const status: MonitorDeviceRunningStatusEnum = data.row.runningStatus;
        if (typeof status !== "number") {
          return null;
        }

        return (
          <CxTag type={MonitorDeviceRunningStatusMapColor[status]}>{MonitorDeviceRunningStatusMapDesc[status]}</CxTag>
        );
      },
      showOverflowTooltip: true
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char20
    }
  ];
  return { columns };
}
