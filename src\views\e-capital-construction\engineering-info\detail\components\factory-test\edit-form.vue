<template>
  <!-- 出厂试验 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="基础信息" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="试验项目"
          prop="testItemCode"
          :rules="{ required: false, message: '请选择试验项目', trigger: 'change' }"
        >
          <el-select
            v-model="formData.testItemCode"
            class="w-full"
            disabled
            placeholder="请选择试验项目"
            clearable
            filterable
          >
            <el-option v-for="item in collectionList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物ID(单元)"
          prop="subPhysicalItemId"
          :rules="{ required: false, message: '请输入子实物ID(单元)', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemId" placeholder=" 请输入子实物ID(单元)" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: isSubPhysicalItemCodeRequire(), message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder=" 请输入子实物编码" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="联合试验单元"
          prop="jointExperimentalUnit"
          :rules="{ required: true, message: '请输入联合试验单元', trigger: 'change' }"
        >
          <el-input v-model="formData.jointExperimentalUnit" placeholder=" 请输入联合试验单元" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="出厂试验贯通次数"
          prop="penetrationsNum"
          :rules="{ required: true, message: '请输入出厂试验贯通次数', trigger: 'change' }"
        >
          <el-input-number
            class="w-full"
            v-model="formData.penetrationsNum"
            placeholder="请输入出厂试验贯通次数"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="试验是否通过"
          prop="testConclusion"
          :rules="{ required: true, message: '请选择试验是否通过', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.testConclusion">
            <el-radio v-for="item in TestConclusionEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="试验人员"
          prop="testPerson"
          :rules="{ required: true, message: '请输入试验人员', trigger: 'change' }"
        >
          <el-input v-model="formData.testPerson" placeholder=" 请输入试验人员" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="不通过原因"
          prop="failureReason"
          :rules="{
            required: formData.testConclusion == TestConclusionEnum.No,
            message: '请输入不通过原因',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.failureReason" placeholder=" 请输入不通过原因" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="返修状态"
          prop="reworkStatus"
          :rules="{ required: false, message: '请选择返修状态', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.reworkStatus">
            <el-radio v-for="item in ReworkStatusEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="数据类型"
          prop="dataType"
          :rules="{
            required: formData.reworkStatus == ReworkStatusEnum.Repair,
            message: '请选择数据类型',
            trigger: 'change'
          }"
        >
          <el-select v-model="formData.dataType" class="w-full" placeholder="请选择数据类型" clearable filterable>
            <el-option
              v-for="item in DataTypeEnumEjjOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="试验环境参数" />
      </el-col>
      <el-col :span="24">
        <DynamicTable
          ref="dynamicTableRef"
          field-type="conType"
          :fields="props.tableField.enviromentItems"
          field-key="measureItemCode"
          v-model="state.enviromentValueData"
        />
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="试验检测参数" />
      </el-col>
      <el-col :span="24">
        <NestingDynamicTable
          ref="nestingDynamicTableRef"
          :fields="props.tableField.collectionItems"
          field-type="conType"
          field-key="collectionId"
          v-model="state.measureValueValue"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import TitleBar from "@/components/TitleBar/index";
import {
  CollectionModel,
  FactoryTestModel,
  FactoryTableModel,
  DataValueObjModel,
  FactoryTableItemModel,
  FileInfoModel
} from "@/models";
import NestingDynamicTable from "../nesting-dynamic-table/index.vue";
import DynamicTable from "../dynamic-table/index.vue";
import {
  TestConclusionEnumOptions,
  TestConclusionEnum,
  ReworkStatusEnum,
  ReworkStatusEnumOptions,
  EquipmentTypeEnumExt,
  DataTypeEnumEjjOptions,
  BaseCollectionEnum
} from "@/enums";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    detail: FactoryTestModel; // 表格表单数据
    isEdit: boolean;
    collection: Array<CollectionModel>;
    tableField: FactoryTableModel;
  }>(),
  {
    detail: () => {
      return {} as FactoryTestModel;
    },
    collection: () => {
      return [];
    },
    tableField: () => {
      return {} as FactoryTableModel;
    },
    isEdit: false
  }
);

const formData = reactive({} as FactoryTestModel);
/** 采集项目 */
const collectionList = ref([] as Array<CollectionModel>);

const route = useRoute();

const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

const state = reactive<{
  measureValueValue: { [key: string]: string | number | FileInfoModel };
  enviromentValueData: { [key: string]: string | number | FileInfoModel };
}>({
  measureValueValue: {},
  enviromentValueData: {}
});

const dynamicTableRef = ref();
const nestingDynamicTableRef = ref();

/**
 * @description: 子实物编码是否必填
 */
const isSubPhysicalItemCodeRequire = () => {
  if (route.query.type == EquipmentTypeEnumExt.Transformer.toString()) {
    if (
      formData.testItemCode == BaseCollectionEnum.TGJYDZ ||
      formData.testItemCode == BaseCollectionEnum.TGDL ||
      formData.testItemCode == BaseCollectionEnum.TGDLHGQ ||
      formData.testItemCode == BaseCollectionEnum.TGDLGPNY
    ) {
      return true;
    } else {
      return false;
    }
  } else if (route.query.type == EquipmentTypeEnumExt.ElectricReactor.toString()) {
    if (
      formData.testItemCode == BaseCollectionEnum.TGDLHGQRZ ||
      formData.testItemCode == BaseCollectionEnum.TGJYDZJZ ||
      formData.testItemCode == BaseCollectionEnum.TGDLHGQJG ||
      formData.testItemCode == BaseCollectionEnum.TGDLSY ||
      formData.testItemCode == BaseCollectionEnum.TGNZ
    ) {
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
};

watchEffect(() => {
  Object.assign(formData, props.detail);
  collectionList.value = props.collection;
  if (props.isEdit) {
    formData.measureValueValue.forEach(item => {
      if (item.conType == "file") {
        state.measureValueValue.value[item.collectionId] = item.fileInfo || ({} as FileInfoModel);
      } else if (item.conType == "number") {
        state.measureValueValue[item.collectionId] = item.dataValue ? Number(item.dataValue) : null;
      } else {
        state.measureValueValue[item.collectionId] = item.dataValue;
      }
    });
    formData.enviromentValueData.forEach(item => {
      if (item.conType == "file") {
        state.enviromentValueData[item.measureItemCode] = item.fileInfo || ({} as FileInfoModel);
      } else if (item.conType == "number") {
        state.enviromentValueData[item.measureItemCode] = item.dataValue ? Number(item.dataValue) : null;
      } else {
        state.enviromentValueData[item.measureItemCode] = item.dataValue;
      }
    });
  } else {
    formData.equipmentType = route.query.type as string;
  }
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  if (!dynamicTableRef.value && !nestingDynamicTableRef.value) {
    return await formRef.value.validate(valid => valid);
  } else if (dynamicTableRef.value && !nestingDynamicTableRef.value) {
    return (await formRef.value.validate(valid => valid)) && (await dynamicTableRef.value.validateForm());
  } else {
    return (
      (await formRef.value.validate(valid => valid)) &&
      (await dynamicTableRef.value.validateForm()) &&
      (await nestingDynamicTableRef.value.validateForm())
    );
  }
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const dynamicTableValue = dynamicTableRef.value.getFormValue();
  const nestingDynamicTable = nestingDynamicTableRef.value.getFormValue();
  const params = {
    ...formData,
    enviromentValueData: dynamicTableValue.map((item: DataValueObjModel) => {
      return {
        measureItemCode: item.measureItemCode,
        dataValue: item.dataValue,
        conType: item.conType,
        validated: false
      };
    }),
    measureValueValue: nestingDynamicTable.map((item: FactoryTableItemModel) => {
      return {
        measureItemCode: item.measureItemCode,
        dataValue: item.dataValue,
        collectionId: item.collectionId,
        conType: item.conType,
        validated: false
      };
    })
  };
  return Object.assign({}, params as FactoryTestModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  dynamicTableRef.value?.resetFields();
  nestingDynamicTableRef.value?.resetFields();
  state.measureValueValue = {};
  state.enviromentValueData = {};
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
