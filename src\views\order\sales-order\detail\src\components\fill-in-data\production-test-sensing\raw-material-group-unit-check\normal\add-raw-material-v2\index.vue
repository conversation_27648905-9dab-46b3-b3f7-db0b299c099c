<template>
  <div class="add-raw-material">
    <div class="raw-content">
      <RawMaterialInfo ref="rawMaterialInstance" />
      <MaterialForm
        ref="materialTestFormIns"
        :collectionData="rawMaterialGroupUnitStore.rawMaterialCheckCollectionItem"
        v-model="collectionFormData"
        @update:model-value="updateCollectionData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import RawMaterialInfo from "./raw-material-info/index.vue";
import MaterialForm from "./material-form/index.vue";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import { provide, onMounted, ref } from "vue";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check/index";
import { useSalesFillInDataStore } from "@/store/modules/fill-in-data/sales-fill-in-data";
import { IProductOrder } from "@/models";

const props = withDefaults(
  defineProps<{
    processInfo: IProductionMaterialProcess;
  }>(),
  {}
);
const emits = defineEmits<{
  (event: "saveAddSuccess"): void;
}>();
// 工序信息
provide("addRawMaterialToken", {
  processInfo: props.processInfo
});

const fillInDataStore = useSalesFillInDataStore();
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const rawMaterialInstance = ref();
const materialTestFormIns = ref();
const collectionFormData = ref(null);
const saveLoading = ref<boolean>(false);

onMounted(() => {
  if (props.processInfo?.processId) {
    const { processId } = props.processInfo;
    if (processId) getCollectionItemsByProcessCode(processId);
  }
});

// 获取工序下的采集项数据
function getCollectionItemsByProcessCode(processId: string) {
  rawMaterialGroupUnitStore.getRawMaterialCheckInfoByProductStePro(
    processId,
    (fillInDataStore.data as IProductOrder).specificationModel || fillInDataStore.data.specificationType
  );
}

/**
 * 更新采集项数据
 */
const updateCollectionData = val => {
  collectionFormData.value = val;
};
/**
 * 保存数据
 */
const addRawMaterialInspectData = async () => {
  // 获取表单的数据
  const rawMaterialInspectInfo = await rawMaterialInstance.value.validateAndSendFormData(res => res);
  // 获取采集项数据
  await materialTestFormIns.value.validRawMaterialInspect();
  if (rawMaterialInspectInfo && collectionFormData.value && Object.keys(collectionFormData.value)?.length) {
    const { submitData: inspectInfo, uploadData } = collectionFormData.value;
    const formData = inspectInfo.map(item => {
      const { targetCode, targetValue, dataTypeIdentityDetail } = item;
      const { identityCode } = dataTypeIdentityDetail;
      return {
        dataCode: targetCode,
        dataValue: targetValue,
        identityCode
      };
    });
    const uploadFile = uploadData.map(item => {
      const { targetCode, fileList, dataTypeIdentityDetail } = item;
      const { identityCode } = dataTypeIdentityDetail;
      return {
        dataCode: targetCode,
        dataValue: JSON.stringify(fileList),
        identityCode
      };
    });
    const { processId } = props.processInfo;
    // 去除原材料中Id
    delete rawMaterialInspectInfo["id"];
    const saveData = {
      ...rawMaterialInspectInfo,
      processId,
      rawMetadataValue: [...formData, ...uploadFile]
    };
    saveLoading.value = true;
    await rawMaterialGroupUnitStore
      .saveAddRawMaterialInspect(saveData, true)
      .finally(() => (saveLoading.value = false));
    emits("saveAddSuccess");
  }
};

defineExpose({
  addRawMaterialInspectData,
  saveLoading
});
</script>

<style scoped lang="scss"></style>
