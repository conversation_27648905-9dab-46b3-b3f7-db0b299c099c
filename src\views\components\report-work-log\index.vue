<template>
  <div class="h-full flex flex-col overflow-hidden">
    <TitleBar class="mb-2" title="基础信息" />
    <el-descriptions>
      <el-descriptions-item label="工序">{{ reportWork?.processName }}</el-descriptions-item>
      <el-descriptions-item label="设备">{{ reportWork?.deviceName }}</el-descriptions-item>
      <el-descriptions-item label="报工批次号">{{ reportWork?.productBatchNo }}</el-descriptions-item>
      <el-descriptions-item label="报工地址">{{ reportWork?.buyerProvince }}</el-descriptions-item>
      <el-descriptions-item label="开始时间">{{
        formatDate(reportWork?.workStartTime, fullDateFormat)
      }}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{
        formatDate(reportWork?.workEndTime, fullDateFormat)
      }}</el-descriptions-item>
    </el-descriptions>

    <div class="mt-4 flex flex-col flex-1 overflow-hidden">
      <TitleBar class="mb-2" title="操作日志" />
      <PureTable
        class="flex-1 overflow-hidden tooltip-max-w"
        row-key="id"
        :data="logs"
        :columns="columns"
        showOverflowTooltip
        :loading="loading"
      >
        <template #eventType="{ row }">
          {{ OperationLogTypeMapDesc[row.eventType] }}
        </template>
        <template #dataOld="{ row }">
          {{ row.eventType === OperationLogTypeEnum.REPORT_DATA_ADD ? "" : row.dataOld }}
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import TitleBar from "@/components/TitleBar";
import { IOperationLog } from "@/models";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import { queryOperateLog } from "@/api/operate-log";
import { OperationLogTypeMapDesc, OperationLogSourceEnum, OperationLogTypeEnum } from "@/enums";
import { getReportWorkById } from "@/api/report-work";

const props = withDefaults(
  defineProps<{
    id: string;
  }>(),
  {}
);

const loading = ref();
const reportWork = ref();
const logs = ref<Array<IOperationLog>>([]);

const columns: TableColumnList = [
  {
    label: "操作人",
    prop: "opUserName"
  },
  {
    label: "操作时间",
    prop: "opTime"
  },
  {
    label: "操作类型",
    prop: "eventType",
    slot: "eventType"
  },
  {
    label: "原值",
    prop: "dataOld"
  },
  {
    label: "新值",
    prop: "dataNew"
  }
];

watchEffect(() => {
  getOperateLog(props.id);
  getReportWorkDetailById(props.id);
});

async function getOperateLog(dataId: string) {
  loading.value = true;
  const { data } = await queryOperateLog({ dataId, dataType: OperationLogSourceEnum.REPORT_WORK });
  logs.value = data;
  loading.value = false;
}

async function getReportWorkDetailById(id: string) {
  const { data } = await getReportWorkById(id);
  reportWork.value = data;
}
</script>

<style scoped lang="scss"></style>
