<template>
  <v-chart :option="option" autoresize class="min-w-[280px] w-full h-full" />
</template>

<script setup lang="ts">
import { PropType, computed } from "vue";
import VChart from "vue-echarts";
import { formatDate } from "@/utils/format";
import { hoursFormat } from "@/consts";

const props = defineProps({
  xAxisData: {
    type: Array as PropType<Array<string>>,
    default: () => []
  },
  yAxisData: {
    type: Array as PropType<Array<string>>,
    default: () => []
  },
  dataZoom: {
    type: Boolean,
    detault: false
  }
});

const option = computed(() => {
  const yDataArr = props.yAxisData.map(value => +value);
  const maxValue = Math.max(...yDataArr);
  const minValue = Math.min(...yDataArr);
  return {
    animation: false,
    grid: {
      left: "10%",
      top: "10%",
      right: "5%",
      bottom: props.dataZoom ? 70 : "15%"
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.xAxisData,
      axisLabel: {
        show: true,
        color: "#909399",
        formatter: (time: string) => {
          return formatDate(time, hoursFormat);
        }
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        show: true,
        color: "#909399"
      },
      splitLine: {
        show: false
      },
      min: Math.floor(minValue - Math.abs(minValue * 0.1)),
      max: Math.ceil(maxValue + Math.abs(maxValue * 0.1))
    },
    tooltip: {
      trigger: "axis"
    },
    series: [
      {
        type: "line",
        smooth: true,
        sampling: "lttb",
        data: props.yAxisData
      }
    ],
    dataZoom: props.dataZoom &&
      props.xAxisData.length && [
        {
          type: "inside"
        },
        {
          type: "slider",
          left: "15%",
          right: "15%",
          labelFormatter: (index: number | null) => {
            if (index) {
              return props.xAxisData[index].split(" ").join("\n");
            }
            return "";
          }
        }
      ],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: !!props.xAxisData.length,
      style: {
        text: "暂无采集点数据",
        fontSize: "16px",
        fill: "#909399"
      }
    }
  };
});
</script>

<style scoped></style>
