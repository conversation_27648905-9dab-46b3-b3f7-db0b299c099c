<template>
  <el-form ref="formRef" :model="form" label-position="top">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item
          label="监测位置"
          prop="monitoringLocation"
          :rules="{ required: true, trigger: 'change', message: '监测位置不能为空' }"
        >
          <el-radio-group v-model="form.monitoringLocation">
            <el-radio v-for="(item, index) in MonitoringLocationEnumOptions" :label="item.value" :key="index">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="设备编号"
          prop="equipmentNumber"
          :rules="{ required: true, trigger: 'change', message: '设备编号不能为空' }"
        >
          <el-input v-model="form.equipmentNumber" maxlength="20" clearable placeholder="请输入设备编号 " />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="检验位置点名称" prop="pointName">
          <el-input v-model="form.pointName" maxlength="100" clearable placeholder="请输入检验位置点名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="报告时间"
          prop="reportTime"
          :rules="{ required: true, trigger: 'change', message: '报告时间不能为空' }"
        >
          <el-date-picker
            class="!w-full"
            v-model="form.reportTime"
            type="datetime"
            clearable
            placeholder="请选择报告时间"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="环境参数" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="温度（单位：℃）"
          prop="temperature"
          :rules="{ required: true, trigger: 'change', message: '湿度不能为空' }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.temperature"
            clearable
            controls-position="right"
            placeholder="请输入温度（单位：℃）"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="湿度"
          prop="humidity"
          :rules="{
            required:
              form.monitoringLocation == MonitoringLocationEnum.InteriorRoom ||
              form.monitoringLocation == MonitoringLocationEnum.Exterior,
            trigger: 'change',
            message: '湿度不能为空'
          }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.humidity"
            clearable
            controls-position="right"
            placeholder="请输入湿度"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="洁净度指标1"
          prop="cleanliness1"
          :rules="{
            required: form.monitoringLocation == MonitoringLocationEnum.DryingRoom,
            trigger: 'change',
            message: '洁净度指标1不能为空'
          }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.cleanliness1"
            clearable
            controls-position="right"
            placeholder="请输入洁净度指标1"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="洁净度指标2"
          prop="cleanliness2"
          :rules="{
            required: form.monitoringLocation == MonitoringLocationEnum.DryingRoom,
            trigger: 'change',
            message: '洁净度指标2不能为空'
          }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.cleanliness2"
            clearable
            controls-position="right"
            placeholder="请输入洁净度指标2"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="洁净度指标3"
          prop="cleanliness3"
          :rules="{
            required: form.monitoringLocation == MonitoringLocationEnum.DryingRoom,
            trigger: 'change',
            message: '洁净度指标3不能为空'
          }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.cleanliness3"
            clearable
            controls-position="right"
            placeholder="请输入洁净度指标3"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance } from "element-plus";
import { IEnvReportForm } from "@/models";
import TitleBar from "@/components/TitleBar/index";
import { MonitoringLocationEnumOptions, MonitoringLocationEnum } from "@/enums";

defineExpose({
  validateForm,
  initFormValue,
  resetFormValue,
  getFormValue
});

const form = reactive<IEnvReportForm>({});
const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IEnvReportForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}
</script>

<style scoped></style>
