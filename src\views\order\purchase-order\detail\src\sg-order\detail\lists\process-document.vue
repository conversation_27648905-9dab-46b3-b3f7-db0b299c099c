<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditProcessDocumentDialog ref="editDialog" :subclass-code="subclassCode" />
  <FileSyncListDialog
    :files="files"
    v-model="fileSyncListVisible"
    @sync="syncFile"
    @syncDetailEvent="getHistoryByDataId"
  />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncDataFormatEnum, StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import EditProcessDocumentDialog from "@/views/components/state-grid-order-sync/dialogs/edit-process-document.vue";
import FileSyncListDialog from "@/views/components/state-grid-order-sync/dialogs/file-sync-list-dialog.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useStateGridOrderSyncDetailListStore } from "@/store/modules";
import { useSync } from "@/views/order/purchase-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { computed, provide, reactive, ref } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { fileSyncFormatter } from "@/views/components/state-grid-order-sync/formatters/file-sync-formatter";
import { IProcessDocumentSync } from "@/models/state-grid-sync/iot-sync/i-process-document-sync";
import { IFileSync } from "@/models";
import { useSubclassCode } from "./hooks/useSubclassCode";

const type = StateGridOrderSyncType.PROCESS_DOCUMENT;
const { sync, syncByDataId, getHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getHistoryByDataId,
  editFn: openEditDialog
});
const { subclassCode } = useSubclassCode();
const editDialog = ref<InstanceType<typeof EditProcessDocumentDialog>>();

const listStore = useStateGridOrderSyncDetailListStore();
listStore.setType(type);

const activeFileSyncId = ref<string>();
const fileSyncListVisible = ref(false);

const files = computed(() => {
  const sync = listStore.data.find(datum => datum.id === activeFileSyncId.value) as IProcessDocumentSync | undefined;
  return sync?.fileList || [];
});

const columns: Array<TableColumns> = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.order
  },
  {
    label: "文件同步状态",
    prop: "fileList",
    minWidth: TableWidth.type,
    formatter: fileSyncFormatter(id => {
      activeFileSyncId.value = id;
      fileSyncListVisible.value = true;
    })
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

function openEditDialog(data) {
  editDialog.value.openEditDialog(data);
}

function syncFile(file: IFileSync) {
  const { dataId, fileName } = file;
  syncByDataId(dataId, fileName, StateGridOrderSyncDataFormatEnum.FILE);
}
</script>

<style scoped lang="scss"></style>
