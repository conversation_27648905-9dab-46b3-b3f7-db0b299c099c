import { withApiGateway } from "@/api/util";
import {
  IDownLoadDetailLog,
  IGatewayDetailList,
  IGatewayList,
  IGatewayTodayList,
  IGatewayTodayListReq,
  ISearchDetailReq,
  ISearchGatewayReq,
  ISearchOptionList
} from "@/models/logging/i-gateway";
import { IListResponse, IResponse } from "@/models/response";
import { http } from "@/utils/http";

/**
 * 获取下拉列表的值
 */
export function getSearchOptionList(categoryCode: string) {
  const url = withApiGateway(`admin-api/reporting/dataInterfaceScene/querySceneTreeByCategoryCode`);
  return http.get<void, IResponse<ISearchOptionList>>(url, {
    params: {
      categoryCode
    }
  });
}

/** 获取网关设备日志 */
export function getGatewayLogList(params: ISearchGatewayReq) {
  const url = withApiGateway(`admin-api/infra/eipInterface/getEIPDailySummaryPage`);
  return http.post<ISearchGatewayReq, IListResponse<IGatewayList>>(url, { data: params });
}

/**
 * 根据ID获取某一个接口的日志详情
 */
export function getGatewayDetailLogList(params: ISearchDetailReq) {
  const url = withApiGateway(`admin-api/infra/eipInterface/getInterfaceScenePage`);
  return http.post<ISearchDetailReq, IListResponse<IGatewayDetailList>>(url, { data: params });
}

/**
 * 根据ID获取某一个网关设备的日志详情
 */
export function getGatewayDetailLogById(id: string) {
  const url = withApiGateway(`admin-api/infra/eipInterface/getEIPDailySummaryDetail/${id}`);
  return http.get<void, IResponse<IGatewayList>>(url);
}

/** 下载日志详情 */
export const getDownLoadUrlOfDetailLog = (id: string) => {
  return http.get<void, IResponse<IDownLoadDetailLog>>(
    withApiGateway(`admin-api/infra/eipInterface/getEIPProductionDataFile/${id}`)
  );
};

/**
 * 获取网关日志当日记录
 */
export function getInterfaceRecordPageByScene(params: IGatewayTodayListReq) {
  const url = withApiGateway(`admin-api/infra/eipInterface/getInterfaceRecordPageByScene`);
  return http.post<IGatewayTodayListReq, IListResponse<IGatewayTodayList>>(url, { data: params });
}
