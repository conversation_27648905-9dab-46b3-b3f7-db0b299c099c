import {
  IProductionProcessAutoSyncParams,
  IEIPSyncProductionProcessAutoItem,
  IIOTSyncCommon,
  IIOTSyncProductionProcessAutoItem,
  ISyncCommon
} from "@/models";
import {
  useStateGridOrderSyncDetailListStore,
  useStateGridOrderSyncDetailStore,
  useStateGridOrderSyncTimelineStore
} from "@/store/modules";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import {
  getStateGridOrderSyncTypeTitle,
  PurchaseChannel,
  StateGridOrderSyncDataFormatEnum,
  StateGridOrderSyncType
} from "@/enums";
import type { Action } from "element-plus";
import { cancelProcessInspectSync, autoAcquisitionItemPrioritySync } from "@/api/state-grid-order-sync";
import { JFNYCode } from "@/consts/experiment-code";

export function useSync() {
  const store = useStateGridOrderSyncDetailListStore();
  const timeStore = useStateGridOrderSyncTimelineStore();
  const syncDetailStore = useStateGridOrderSyncDetailStore();
  const sync = (data: ISyncCommon) => {
    const no = data[getNoField()];
    return syncByDataId(data.dataId, no);
  };

  const cancelSync = (data: IEIPSyncProductionProcessAutoItem | IIOTSyncProductionProcessAutoItem) => {
    ElMessageBox.alert("已同步的数据将不会撤回，是否继续？", "提示", {
      type: "warning",
      confirmButtonText: "确定",
      callback: async (action: Action) => {
        if (action !== "confirm") {
          return;
        }
        const params = calcRequstParams(data);

        // 发送取消请求
        const { code, msg } = await cancelProcessInspectSync(params);

        if (code === 0) {
          // 取消成功
          ElMessage({
            type: "success",
            message: "取消成功"
          });
          store.refreshSyncList();
        } else if (code === 200) {
          if (msg) {
            // 数据已同步完成，无法取消同步
            ElMessageBox.alert("数据已同步完成，无法取消同步", "提示", {
              type: "warning",
              confirmButtonText: "确定"
            });
          }
        }
      }
    });
  };

  const syncByDataId = async (dataId: string, no?: string, dataFormat?: StateGridOrderSyncDataFormatEnum) => {
    await store.syncStateGridOrder(dataId, dataFormat);
    const title = getStateGridOrderSyncTypeTitle(store.type);
    no = no ? `【${no}】` : "";
    ElNotification.info({
      title: "数据同步",
      message: `正在同步${title}${no}数据`,
      duration: 3000
    });
  };

  const getNoField = () => {
    return {
      [StateGridOrderSyncType.SALE_ORDER_ITEM]: "soItemNo",
      [StateGridOrderSyncType.PRODUCTION_PLAN]: "ppNo",
      [StateGridOrderSyncType.PRODUCTION_ORDER]: "ipoNo",
      [StateGridOrderSyncType.WORK_ORDER]: "woNo",
      [StateGridOrderSyncType.REPORT_WORK]: undefined,
      [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: "code",
      [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: "code",
      [StateGridOrderSyncType.EXPERIMENT]: "experimentNo",
      [StateGridOrderSyncType.FINISHED_PRODUCT]: "productNo",
      [StateGridOrderSyncType.TECHNICAL_STANDARD]: "standardNo",
      [StateGridOrderSyncType.PROCESS_DOCUMENT]: "processDocNo"
    }[store.type];
  };

  /** 获取同步明细历史数据 */
  const getHistoryByDataId = async (data: ISyncCommon | IIOTSyncCommon) => {
    const channel = syncDetailStore.channel;
    // EIP
    if (channel === PurchaseChannel.EIP) {
      if (
        (data as IEIPSyncProductionProcessAutoItem).processCode === JFNYCode &&
        store.type === StateGridOrderSyncType.EXPERIMENT
      ) {
        return await timeStore.getJFNYSyncHistories(data as ISyncCommon);
      }
      return await timeStore.getSyncHistories(store.type, data as ISyncCommon);
    } else {
      // CSG IOT
      return await timeStore.getSyncIotOrCsgGuangzhouHistories(data as IIOTSyncCommon, channel);
    }
  };

  /** 优先同步 */
  const prioritySync = (data: IEIPSyncProductionProcessAutoItem | IIOTSyncProductionProcessAutoItem) => {
    ElMessageBox.alert(
      "系统将优先同步当前数据，期间将会停止同工序其他数据的同步，直至同步完成后再恢复，是否继续？",
      "提示",
      {
        type: "warning",
        confirmButtonText: "确定",
        callback: async (action: Action) => {
          if (action !== "confirm") {
            return;
          }
          const params = calcRequstParams(data);

          // 发送优先同步请求
          const { code, msg } = await autoAcquisitionItemPrioritySync(params);

          if (code === 0) {
            ElNotification.info({
              title: "数据同步",
              message: `正在同步生产过程自动采集项数据`,
              duration: 3000
            });
            store.refreshSyncList();
          } else if (code === 200) {
            if (msg) {
              // 数据已同步完成，无法取消同步
              ElMessageBox.alert(msg, "提示", {
                type: "warning",
                confirmButtonText: "确定"
              });
            }
          }
        }
      }
    );
  };

  /**
   * @description: 计算 取消同步 和 优先同步的请求参数
   */
  function calcRequstParams(data: IEIPSyncProductionProcessAutoItem | IIOTSyncProductionProcessAutoItem) {
    const channel = syncDetailStore.channel;
    let params: IProductionProcessAutoSyncParams = {} as IProductionProcessAutoSyncParams;
    // 国网平台
    if (channel === PurchaseChannel.EIP) {
      const temp: IEIPSyncProductionProcessAutoItem = data as IEIPSyncProductionProcessAutoItem;
      params = {
        orderId: temp.purchaseId,
        orderItemId: temp.purchaseLineId,
        productReportId: temp.productReportId,
        processCode: temp.processCode,
        channel: PurchaseChannel.EIP
      };
    } else {
      // 上海平台 广州供电局
      const temp: IIOTSyncProductionProcessAutoItem = data as IIOTSyncProductionProcessAutoItem;
      params = {
        orderId: temp.salesId,
        orderItemId: temp.salesLineId,
        productReportId: temp.productReportId,
        processCode: temp.processCode,
        channel: PurchaseChannel.IOT
      };
    }
    return params;
  }

  return {
    sync,
    cancelSync,
    syncByDataId,
    getHistoryByDataId,
    prioritySync
  };
}
