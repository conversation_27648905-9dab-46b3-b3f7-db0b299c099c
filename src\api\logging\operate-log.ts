import { IConfigModule, IConfigPageFrame, IOperateLog, IOperateLogDetail, IOperateLogReq } from "@/models/logging";
import { withApiGateway } from "../util";
import { http } from "@/utils/http";
import { IListResponse, IResponse } from "@/models";
import { getToken } from "@/utils/auth";

/**
 * 获取操作日志 配置的模块信息
 */
export function getPointConfigModuleApi() {
  const url = withApiGateway(`admin-api/infra/track/getPointConfigModule`);
  return http.post<void, IResponse<IConfigModule>>(url);
}

/**
 * 获取操作日志 配置的页面信息
 */
export function getPointerConfigPageApi() {
  const url = withApiGateway(`admin-api/infra/track/getPointConfigPageFrame`);
  return http.post<void, IResponse<IConfigPageFrame>>(url);
}

/**
 * 获取操作日志汇总列表
 */
export function getLogSummaryList(params: IOperateLogReq) {
  const url = withApiGateway(`admin-api/infra/track/gainTrackRecordStatistic`);
  return http.post<IOperateLogReq, IListResponse<IOperateLog>>(url, {
    data: {
      ...params
    }
  });
}

/**
 * 获取日志详情的列表
 */
export function getLogDetailList(params: IOperateLogReq) {
  const url = withApiGateway(`admin-api/infra/track/queryTrackRecord`);
  return http.post<IOperateLogReq, IListResponse<IOperateLogDetail>>(url, {
    data: {
      ...params
    }
  });
}

/** 新增操作日志记录 */
export const addTrackRecord = (pointCode: string) => {
  const url = withApiGateway(`admin-api/infra/track/addTrackRecordV2`);
  const data = {
    pointCode,
    authorization: getToken()?.accessToken
  };
  if (navigator?.sendBeacon) {
    const blob = new Blob([JSON.stringify(data)], { type: "application/json" });
    return navigator.sendBeacon(url, blob);
  }
  return http.post<{ pointCode: string; authorization?: string }, IResponse<boolean>>(url, { data });
};
