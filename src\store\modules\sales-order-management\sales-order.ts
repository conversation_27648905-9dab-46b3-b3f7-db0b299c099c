import {
  IListResponse,
  IPagingReq,
  IPurchaseOrder,
  IPurchaseOrderStep,
  IResponse,
  ISalesLinkPurchaseOrderParams,
  ISalesOrder,
  ISalesOrderDetail,
  ISalesOrderFiling,
  ISalesOrderParams,
  ISalesOrderStatistic,
  IStepValue
} from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useSalesOrderManagementStore = defineStore({
  id: "cx-sales-order-management-store",
  state: () => ({
    // 无采购订单号
    faultPoNoCount: 0,
    // 关注
    followCount: 0,
    /** 归档数量 */
    documentationCount: 0,
    stepValue: {} as IStepValue,
    salesOrderDetail: {} as ISalesOrder,
    selectId: undefined,
    total: 0,
    // 关联采购订单的列表总数
    salesLinkPurchaseOrdersTotal: 0,
    steps: [] as Array<IPurchaseOrderStep>,
    activeStepKey: undefined
  }),
  actions: {
    // 根据ID查询销售订单详情
    async getSalesOrderDetailById(id: string) {
      const res: IResponse<ISalesOrder> = await api.getSalesOrderDetailById(id);
      this.salesOrderDetail = res.data;
      return res.data;
    },

    // 查询销售订单列表数据
    async querySalesOrders(params: ISalesOrderParams) {
      const res: IListResponse<ISalesOrder> = await api.querySalesOrders(params);
      this.total = res.data.total;
      return res.data.list;
    },

    async queryStepValue(params: IPagingReq) {
      const stepValue: IStepValue = (await api.queryStepValue(params)).data;
      Object.entries(stepValue).forEach(([key, value]) => {
        this.stepValue[key] = value;
      });
    },

    async querySalesOrderStatistic(params: ISalesOrderParams) {
      const salesStatistic: ISalesOrderStatistic = (await api.querySalesOrderStatistic(params)).data;
      this.faultPoNoCount = salesStatistic.faultPoNoCount || 0;
      this.followCount = salesStatistic.followCount || 0;
      this.documentationCount = salesStatistic.documentationCount || 0;
    },

    async deleteSalesOrdersById(id: string) {
      const res: IResponse<boolean> = await api.deleteSalesOrdersById(id);
      return res.data;
    },

    async ceateSalesOrder(salesOrderDetail: ISalesOrderDetail) {
      const res: IResponse<string> = await api.ceateSalesOrder(salesOrderDetail);
      return res.data;
    },

    async updateSalesOrdersById(salesOrderDetail: ISalesOrderDetail) {
      const res: IResponse<string> = await api.updateSalesOrdersById(salesOrderDetail);
      return res.data;
    },

    async querySalesLinkPurchaseOrders(params: ISalesLinkPurchaseOrderParams) {
      const queryParams: ISalesLinkPurchaseOrderParams = params as ISalesLinkPurchaseOrderParams;
      const res: IListResponse<IPurchaseOrder> = await api.querySalesLinkPurchaseOrders(queryParams);
      this.salesLinkPurchaseOrdersTotal = res.data.total;
      return res.data.list;
    },

    /** 销售订单归档 */
    async saleOrderToggleFiling(data: ISalesOrderFiling) {
      return api.saleOrderToggleFiling(data);
    },

    // 销售订单行详情赋值
    setSalesOrderDetailStorage(saleOrderDetail: ISalesOrder) {
      this.salesOrderDetail = saleOrderDetail;
    },
    // 销售订单行详情清空
    clearSalesOrderDetailStorage() {
      this.salesOrderDetail = {};
    },

    // 关注 取消关注销售订单
    async salesOrderFollow(id: string) {
      await api.salesOrderFollow(id);
    }
  }
});
