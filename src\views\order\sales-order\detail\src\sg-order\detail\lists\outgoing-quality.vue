<template>
  <BaseList :columns="columns" v-model="$attrs" />
  <EditAirghtnessExperienceDialog
    ref="editAirghtnessExperienceDialog"
    :subclass-code="subclassCode"
    :isCable="salesOrderDetail.isCable"
  />
  <DetailJfnyExperienceDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc, StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import {
  useSalesOrderDetailStore,
  useSalesStateGridOrderSyncDetailListStore,
  useSalesOrderSyncInfo
} from "@/store/modules";
import { IExperimentSync } from "@/models";
import EditAirghtnessExperienceDialog from "@/views/components/state-grid-order-sync/dialogs/edit-airghtness-experience-dialog.vue";
import { h, provide, reactive, ref } from "vue";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useSubclassCode } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSubclassCode";
import DetailJfnyExperienceDialog from "@/views/components/state-grid-order-sync/dialogs/detail-jfny-experience-dialog.vue";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const type = StateGridOrderSyncType.EXPERIMENT;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const { subclassCode } = useSubclassCode();
const salesOrderDetail = useSalesOrderDetailStore();
const listStore = useSalesStateGridOrderSyncDetailListStore();
const syncStore = useSalesOrderSyncInfo();
listStore.setType(type);

const editAirghtnessExperienceDialog = ref<InstanceType<typeof EditAirghtnessExperienceDialog>>();
const detailDialog = ref<InstanceType<typeof DetailJfnyExperienceDialog>>();

const columns: Array<TableColumns> = [
  {
    label: "生产订单号",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),
    prop: syncStore.activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER ? "orderNo" : "ipoNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "试验编号",
    prop: "experimentNo",
    minWidth: TableWidth.largeOrder,
    formatter: (row, column, value) => {
      if (row.processCode !== "JFNY") {
        return value;
      }
      const dataId: string = row.dataId;
      return h("a", { class: "text-primary", onClick: () => detailDialog.value.openDetailDialog(dataId) }, value);
    }
  },
  {
    label: "试验名称",
    prop: "processName",
    minWidth: TableWidth.order
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

async function openEditDialog(data: IExperimentSync) {
  editAirghtnessExperienceDialog.value.openEditDialog(data);
}
</script>

<style scoped></style>
