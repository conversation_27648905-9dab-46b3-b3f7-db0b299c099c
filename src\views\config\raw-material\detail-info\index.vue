<template>
  <el-scrollbar :max-height="618">
    <div class="detail-raw-material-inspec cx-form">
      <!-- 原材料检测信息 -->
      <div class="raw-material-inspec">
        <TitleBar title="原材料检信息" class="mb-2" />
        <div class="raw-material-info mx-5 mb-6 cx-form">
          <el-descriptions>
            <el-descriptions-item label="物资种类">
              {{ inspectInfo.subClassName }}
            </el-descriptions-item>
            <el-descriptions-item label="检验批次号">
              {{ inspectInfo.inspectBatchNo }}
            </el-descriptions-item>
            <el-descriptions-item label="检验日期">
              {{ inspectInfo.inspectDate }}
            </el-descriptions-item>
            <el-descriptions-item label="检验标准" v-if="isArmourClamp">
              {{ inspectInfo.inspectStandard }}
            </el-descriptions-item>
            <el-descriptions-item label="炉/批号" v-if="isArmourClamp">
              {{ inspectInfo.furnaceNo }}
            </el-descriptions-item>
            <el-descriptions-item label="检测人员" v-if="isArmourClamp">
              {{ inspectInfo.inspectOperate }}
            </el-descriptions-item>
            <el-descriptions-item label="检测单位" v-if="isArmourClamp">
              {{ inspectInfo.inspectEnterprise }}
            </el-descriptions-item>
            <el-descriptions-item label="审核人员" v-if="isArmourClamp">
              {{ inspectInfo.auditor }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions>
            <el-descriptions-item label="备注" :span="3">
              {{ inspectInfo.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div class="pb-6 my-5" v-loading="loading">
        <div class="my-4">
          <div class="mb-2">
            <TitleBar title="原材料检测" />
          </div>
          <DynamicForm
            v-if="resFormData?.length"
            ref="dynamicTableFormRef"
            :dynamic-form-data="resFormData"
            :editMode="false"
          />
        </div>
        <div class="flex-col my-3">
          <div class="flex justify-between mb-3">
            <TitleBar title="原材料检验报告" />
          </div>
          <div class="mx-5">
            <UploadFrom
              v-if="resFileData?.length"
              ref="dynamicFileRef"
              :dynamic-file-data="resFileData"
              :editMode="false"
            />
            <div class="empty text-center" v-else>
              <CxEmpty />
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import DynamicForm from "@/components/DynamicForm";
import UploadFrom from "@/components/Upload";
import TitleBar from "@/components/TitleBar";
import CxEmpty from "@/components/CxEmpty";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { useRawMaterialCheckInfoHook } from "../hook/useRawMaterialCheck";
import { reactive, watchEffect, computed, ref, watch } from "vue";

const props = withDefaults(
  defineProps<{
    isArmourClamp?: boolean;
  }>(),
  {}
);

const rawMaterialStore = useRawMaterialV2Store();
const inspectInfo = reactive<{
  inspectBatchNo: string;
  inspectDate: string;
  inspectStandard: string;
  furnaceNo: string;
  inspectOperate: string;
  inspectEnterprise: string;
  auditor: string;
  remark: string;
  subClassName: string;
}>({
  inspectBatchNo: null,
  inspectDate: null,
  inspectStandard: null,
  furnaceNo: null,
  inspectOperate: null,
  inspectEnterprise: null,
  auditor: null,
  remark: null,
  subClassName: null
});

watchEffect(() => {
  Object.assign(inspectInfo, { ...rawMaterialStore.editRawMaterialInspect });
});

// 获取采集项目信息
const loading = computed(() => rawMaterialStore.collectionItemLoading);
const collectionData = computed(() => rawMaterialStore.detailRawMaterialCheckItem);
// 原材料检测项数据
const resFormData = ref([]);
const resFileData = ref([]);
// 金具
const isArmourClamp = props.isArmourClamp || computed(() => rawMaterialStore.isArmourClamp);

watch(
  collectionData,
  newVal => {
    if (newVal.length) {
      handleCollectionItemsData();
    }
  },
  {
    immediate: true
  }
);

// 获取表单采集项信息
function handleCollectionItemsData() {
  const { getRawMaterialCheckFormData, getRawMaterialCheckFileData } = useRawMaterialCheckInfoHook();
  resFormData.value = getRawMaterialCheckFormData(collectionData.value) || [];
  resFileData.value = getRawMaterialCheckFileData(collectionData.value) || [];
}
</script>

<style scoped lang="scss">
.detail-raw-material-inspec {
  min-height: 618px;
  max-height: 618px;
}
</style>
