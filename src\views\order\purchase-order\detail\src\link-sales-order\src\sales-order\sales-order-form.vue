<template>
  <el-form
    ref="formInstance"
    label-position="top"
    require-asterisk-position="right"
    :model="form"
    :rules="rules"
    class="cx-form"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="销售订单号" prop="soNo">
          <SerialNumber
            :maxlength="SALES_ORDER_MAX_LENGTH"
            :create="isCreate"
            v-model="form.soNo"
            :code="SALES_ORDER_NO_CODE"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资品类" required prop="categoryCode">
          <el-select
            class="w-full"
            v-model="form.categoryCode"
            placeholder="请选择"
            filterable
            :disabled="calcCategoryDisabled"
          >
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="采购方公司名称" prop="buyerName">
          <el-input placeholder="请输入" v-model="form.buyerName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目名称" prop="prjName">
          <el-input placeholder="请输入" v-model="form.prjName" :maxlength="prjNameMaxlength" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="合同编号" prop="conCode">
          <el-input placeholder="请输入" disabled v-model="form.conCode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="订单进度" prop="orderProgress">
          <EnumSelect
            :enum="SalesOrderStatus"
            enumName="SalesOrderStatus"
            class="w-full"
            v-model="form.orderProgress"
            placeholder="请选择"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="交货日期" prop="deliveryDate">
          <el-date-picker v-model="form.deliveryDate" type="date" placeholder="请选择日期" class="!w-full" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="标识" prop="matSyncFlagIdList">
          <SynchronousFlagSelect v-model="form.matSyncFlagIdList" placeholder="请选择标识" clearable
        /></el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            placeholder="请输入备注"
            v-model="form.remark"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { SALES_ORDER_NO_CODE } from "@/consts";
import { PurchaseChannel, SalesOrderStatus } from "@/enums";
import EnumSelect from "@/components/EnumSelect";
import SerialNumber from "@/components/SerialNumber";
import { computed, onMounted, ref, watch } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { ISalesOrderDto } from "@/models";
import { maxlengthMessage, requiredMessage } from "@/utils/form";
import { useCategoryStore, usePurchaseOrderDetailStore } from "@/store/modules";
import SynchronousFlagSelect from "@/views/components/synchronous-flag";
import { SALES_ORDER_MAX_LENGTH } from "@/consts";
import { useOrderSaleSyncFlagHook } from "@/views/order/hooks";
import { SHANG_HAI_IDENTIFIER, GUO_WANG_IDENTIFIER } from "@/consts/buyer-identifier";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";

const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const purchaseOrder = computed(() => purchaseOrderDetailStore.purchaseOrder);

const props = defineProps<{
  type: "create" | "update";
  value?: Partial<ISalesOrderDto>;
}>();

defineExpose({
  validate,
  getFormValue
});

const categoryStore = useCategoryStore();
const prjNameMaxlength = 100;

const categoryOptions = ref<Array<{ label: string; value: string }>>([]);
const formInstance = ref<FormInstance>();
const form = ref<Partial<ISalesOrderDto & { matSyncFlagIdList?: Array<string> }>>({
  categoryCode: ""
});
const rules: FormRules = {
  soNo: [
    {
      required: true,
      message: requiredMessage("销售订单号"),
      trigger: "change"
    }
  ],
  categoryCode: [
    {
      required: true,
      message: requiredMessage("品类"),
      trigger: "change"
    }
  ],
  buyerName: [
    {
      required: true,
      message: requiredMessage("采购方公司"),
      trigger: "change"
    }
  ],
  prjName: [
    {
      required: true,
      message: requiredMessage("项目名称"),
      trigger: "change"
    },
    {
      max: prjNameMaxlength,
      message: maxlengthMessage("项目名称", prjNameMaxlength),
      trigger: "change"
    }
  ],
  orderProgress: [
    {
      required: true,
      message: requiredMessage("订单进度"),
      trigger: "change"
    }
  ]
};

const isCreate = computed(() => props.type === "create");
const { licenseOnlyIOT, licenseOnlyEIP } = useOrderSaleSyncFlagHook();

/** 计算是否禁用品类选择 */
const calcCategoryDisabled = computed(() => {
  if (!purchaseOrder.value) {
    return true;
  }
  const channel = purchaseOrder.value.syncChannel;
  const categoryCode = purchaseOrder.value.categoryCode;
  if (channel === PurchaseChannel.CSG_GuangZhou) {
    return !!categoryCode;
  }
  return true;
});

watch(
  () => props.value,
  value => {
    form.value = value;
    form.value.matSyncFlagIdList = value.matSyncFlagId ? value.matSyncFlagId.split(",") : [];
    if (purchaseOrder.value) {
      const channel = purchaseOrder.value.syncChannel;
      if (channel === PurchaseChannel.CSG_GuangZhou && isCreate.value) {
        form.value.matSyncFlagIdList = [SynchronousFlagEnum.CSG_GuangZhou_Flag];
      }
    }
  }
);

watch(
  [() => form.value.buyerName, licenseOnlyEIP, licenseOnlyIOT],
  ([v, onlyEIP, onlyIOT]) => {
    if (onlyIOT) {
      // 如果只有IOT授权，自动添加 上海标识
      form.value.matSyncFlagIdList = Array.from(new Set(form.value.matSyncFlagIdList.concat(SHANG_HAI_IDENTIFIER.id)));
    } else if (onlyEIP) {
      // 如果只有EIP授权，自动添加国网标识
      form.value.matSyncFlagIdList = Array.from(new Set(form.value.matSyncFlagIdList.concat(GUO_WANG_IDENTIFIER.id)));
    } else {
      // 如果同时含有IOT和EIP授权，并且采购方是上海公司，自动添加上海标识符
      if (new RegExp(SHANG_HAI_IDENTIFIER.name).test(v)) {
        form.value.matSyncFlagIdList = Array.from(
          new Set(form.value.matSyncFlagIdList.concat(SHANG_HAI_IDENTIFIER.id))
        );
      }
    }
  },
  {
    deep: true
  }
);

onMounted(() => initializeCategory());

async function getFormValue(): Promise<Partial<ISalesOrderDto>> {
  const valid = await validate();
  if (!valid) {
    return Promise.reject("invalid");
  }
  return form.value;
}

async function validate() {
  return formInstance.value.validate(() => {});
}

async function initializeCategory() {
  await categoryStore.getCategories();
  categoryOptions.value = categoryStore.categories.map(category => ({
    label: category.categoryName,
    value: category.categoryCode
  }));
}
</script>

<style scoped></style>
