import { ColumnWidth, ExperimentStatusEnumMapDesc, TableWidth } from "@/enums";
import { previewUploadFile } from "@/utils/uploadFiles";
import { TableColumnRenderer } from "@pureadmin/table";
import { useJfnyHook } from "@/views/leave-factory/hook/useJfnyhook";
import { IJfnyExperimentList } from "@/models/production-test-sensing/i-out-going-factory-jfny-experiment";
import { ElButton, ElDivider, ElPopover } from "element-plus";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

export function useColumns() {
  const { formateVoltageLevelType, formatExperimentRes } = useJfnyHook();
  const columns: TableColumnList = [
    {
      label: "试验编号",
      prop: "experimentNo",
      minWidth: TableWidth.order
    },
    {
      label: "成品编号",
      prop: "finProNo",
      minWidth: TableWidth.order
    },
    {
      label: "盘号",
      prop: "reelNo",
      minWidth: TableWidth.order
    },
    {
      label: "线芯名称",
      prop: "wireName",
      minWidth: TableWidth.name
    },
    {
      label: "电压等级",
      prop: "finishedVoltageLevel",
      minWidth: TableWidth.number
    },
    {
      label: "电压类型",
      prop: "finishedVoltageLevelType",
      minWidth: TableWidth.type,
      formatter: row => {
        return formateVoltageLevelType(row);
      }
    },
    {
      label: "试验状态",
      prop: "experimentStatus",
      minWidth: TableWidth.unit,
      cellRenderer: (data: TableColumnRenderer) => {
        return <div>{ExperimentStatusEnumMapDesc[data.row?.experimentStatus]}</div>;
      }
    },
    {
      label: "试验结果",
      prop: "experimentResult",
      minWidth: TableWidth.unit,
      formatter: row => {
        return formatExperimentRes(row);
      }
    },
    {
      label: "开始时间",
      prop: "startTime",
      minWidth: TableWidth.dateTime
    },
    {
      label: "结束时间",
      prop: "endTime",
      minWidth: TableWidth.dateTime
    },
    {
      label: "试验报告",
      prop: "fileInfo",
      minWidth: TableWidth.file,
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.reportFileId ? (
          <ElPopover width={60} placement="left" trigger="hover">
            {{
              default: () => (
                <div class="flex flex-col items-center">
                  <ElButton type="primary" link onClick={() => previewReport(data.row)}>
                    预览
                  </ElButton>
                  <ElDivider class="!my-3" />
                  <ElButton type="primary" link onClick={() => downLoadReport(data.row)}>
                    下载
                  </ElButton>
                </div>
              ),
              reference: () => (
                <ElButton type="primary" link>
                  <span class="name">{data.row?.reportFileName}</span>
                </ElButton>
              )
            }}
          </ElPopover>
        ) : null;
      }
    },
    {
      label: "操作",
      prop: "op",
      width: ColumnWidth.Char15,
      fixed: "right",
      slot: "operate"
    }
  ];

  // 浏览报告
  const previewReport = (reportFile: IJfnyExperimentList) => {
    if (reportFile?.reportFileId) {
      const { reportFileId: id, reportFileName: name, reportFileUrl: url } = reportFile;
      previewUploadFile({ id, name, url });
    }
  };

  const downLoadReport = async (rowData: IJfnyExperimentList) => {
    const { reportFileId: id, reportFileName: name } = rowData;
    const blob = await downLoadFile(id);
    downloadByData(blob, name, blob.type);
  };

  /**
   * @description: 计算电缆终端/电缆中间接头的列配置
   */
  const cableTerminalAdapterColumns = (columns: TableColumnList) => {
    return columns.filter(c =>
      [
        "experimentNo",
        "experimentResult",
        "startTime",
        "finishedVoltageLevel",
        "endTime",
        "fileInfo",
        "finProNo",
        "finishedVoltageLevelType",
        "experimentStatus",
        "op"
      ].includes(c.prop as string)
    );
  };

  /**
   * @description: 计算中高低压&配网导地线的列配置
   */
  const cableOrDistributionWireGroundColumns = (columns: TableColumnList) => {
    return columns.filter(c =>
      [
        "experimentNo",
        "experimentResult",
        "startTime",
        "finishedVoltageLevel",
        "endTime",
        "fileInfo",
        "finProNo",
        "reelNo",
        "wireName",
        "op"
      ].includes(c.prop as string)
    );
  };

  return {
    columns,
    /** 电缆中间接头&电缆终端列配置 */
    cableTerminalAdapterColumns: cableTerminalAdapterColumns(columns),
    /** 中高低压&配网导,地线配置 */
    cableOrDistributionWireGroundColumns: cableOrDistributionWireGroundColumns(columns)
  };
}
