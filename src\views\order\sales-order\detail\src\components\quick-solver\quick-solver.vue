<!-- 第三步 -填报生产数据 - 非线缆- 工单 - 快速创建 -->
<template>
  <el-collapse class="bg-bg_color px-5 w-full mb-5" model-value="quickResolver" v-if="count">
    <el-collapse-item name="quickResolver">
      <template #title>
        <FontIcon class="color-warning icon-warning-fill" />
        <span class="text_color_primary text-lg ml-1">{{ props.title }}</span>
        <span class="text-lg color-warning">{{ count }}</span>
      </template>

      <div class="wrapper relative">
        <div class="btn prev" @click="prev" v-if="hasPrev">
          <FontIcon class="icon-arrow-right" />
        </div>

        <div class="btn next" @click="next" v-if="hasNext">
          <FontIcon class="icon-arrow-right" />
        </div>

        <div class="flex flex-nowrap gap-4 overflow-hidden" ref="container">
          <div v-for="item in items" :key="item.id" class="card-item">
            <slot :item="item" />
          </div>
        </div>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useScroll } from "./useScroll";

const props = defineProps<{
  title: string;
  items?: Array<any>;
}>();

const { container, prev, next, hasPrev, hasNext } = useScroll();

const count = computed(() => {
  const length = props.items?.length;
  return length ? `（${length}条）` : "";
});
</script>

<style scoped lang="scss">
.color-warning {
  color: var(--el-color-warning);
}

.el-collapse {
  :deep(.el-collapse-item) {
    .el-collapse-item__content {
      padding-bottom: 20px;
    }
  }
}

.card {
  @apply flex flex-nowrap space-x-4;
}

.card-item {
  @apply flex flex-col px-4 py-3;
  background: var(--el-color-warning-light-9);
  border: 1px solid var(--el-color-warning-light-8);
  border-radius: 3px;
}

.btn {
  @apply absolute flex-c cursor-pointer top-1/2;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  color: white;
  background: rgba(0, 0, 0, 0.4);
  transform: translateY(-50%);
  display: none;
}

.prev {
  transform: translateY(-50%) rotate(180deg);
}

.next {
  right: 0;
}

.wrapper:hover {
  .btn {
    display: flex;
  }
}
</style>
