<template>
  <el-select
    v-if="loaded"
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <!-- 数据未加载完成时显示空白选择器 -->
  <el-select
    v-else
    class="w-full"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  />
</template>

<script setup lang="ts">
import { ref, watch, withDefaults, computed } from "vue";
import { useVModels } from "@vueuse/core";
import { getWorkOrderById } from "@/api/work-order";
import { getProductionProcessListByCategoryCodeAndProductionStage } from "@/api/process";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";

/**
 * 根据物资种类选择工序
 */

interface IOption {
  label: string;
  value: string;
  code: string;
}

const props = withDefaults(
  defineProps<{
    /** 物资种类 */
    subClassCode: string;
    /** 已选中id列表 */
    modelValue: string;
    /** 已选中的processCode */
    processCode?: string;
    /** 是否根据工单过滤 */
    limitedByWorkOrder?: boolean;
    /** 工单id 需开启 limitedByWorkOrder */
    workId?: string;
  }>(),
  {
    processCode: "",
    limitedByWorkOrder: false,
    workId: ""
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue, processCode } = useVModels(props, emits);

const options = ref<Array<IOption>>([]);
const loading = ref(false);
const loaded = ref(false);

const placeholder = computed(() => {
  if (props.limitedByWorkOrder && !props.workId) {
    return "请先选择工单";
  }
  return "请选择工序";
});

/**
 * @description: 根据物资种类查询工序
 */
const requestDefaultProcessBySubclassCode = useLoadingFn(async () => {
  const { data } = await getProductionProcessListByCategoryCodeAndProductionStage(props.subClassCode);
  if (!data) {
    return;
  }
  options.value = data.map(({ id, processName, processCode }) => ({
    label: processName,
    value: id,
    code: processCode
  }));
  loaded.value = true;
}, loading);

const requestProcessByWorkOrder = useLoadingFn(async () => {
  const { data } = await getWorkOrderById(props.workId);
  const processIdArr = data.processIds.split("-");
  const processNameArr = data.processName.split(",");
  options.value = processIdArr.map((id, index) => ({
    label: processNameArr[index],
    value: id,
    code: ""
  }));
  const isExist = options.value.find(({ value }) => value === modelValue.value);
  if (!isExist) {
    modelValue.value = "";
  }
  loaded.value = true;
}, loading);

watch(
  () => props.workId,
  async woId => {
    if (woId && props.limitedByWorkOrder) {
      requestProcessByWorkOrder();
    }
  },
  {
    immediate: true
  }
);

watch(
  () => props.subClassCode,
  code => {
    if (code && !props.limitedByWorkOrder) {
      requestDefaultProcessBySubclassCode();
    }
  },
  {
    immediate: true
  }
);

watch(modelValue, id => {
  const activeItem = options.value.find(({ value }) => value === id);
  if (activeItem) {
    processCode.value = activeItem.code;
  }
});
</script>

<style scoped></style>
