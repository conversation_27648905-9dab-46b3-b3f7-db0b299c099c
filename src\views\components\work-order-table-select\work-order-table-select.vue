<template>
  <el-button @click="visible = true" v-bind="$attrs" :icon="Plus" :disabled="disabled">选择工单</el-button>
  <el-dialog
    title="选择工单"
    v-model="visible"
    class="middle"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :validate-event="false"
    @open="dialogOpen"
  >
    <WorkOrderTableHeader :subclass-code="props.subClassCode" @after-create="save" />
    <WorkOrderTable />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import WorkOrderTableHeader from "./work-order-table-header.vue";
import WorkOrderTable from "./work-order-table.vue";
import { provide, reactive, ref } from "vue";
import { useTableConfig } from "@/utils/useTableConfig";
import {
  IWorkOrderQueryParams,
  IWorkOrderTableSelectCtx,
  workOrderSelectKey,
  queryWorkOrder
} from "@/views/components/work-order-table-select/token";
import { IWorkOrder } from "@/models";
import { ElMessage } from "element-plus";

const props = defineProps<{
  subClassCode?: string;
  selectedId?: string;
  disabled?: boolean;
}>();
const visible = ref(false);
const { pagination } = useTableConfig();
const ctx = reactive<IWorkOrderTableSelectCtx>({
  data: [],
  loading: false,
  pagination,
  refresh
});
const emit = defineEmits<{
  (e: "workOrderChange", workOrder: IWorkOrder): void;
}>();
provide(workOrderSelectKey, ctx);
function dialogOpen() {
  initializeCtx();
  refresh();
}
function initializeCtx() {
  ctx.keyword = null;
  ctx.data = [];
  ctx.loading = false;
  ctx.selectedId = props.selectedId;
  ctx.selectedWorkOrder = null;
  ctx.pagination = useTableConfig().pagination;
}

function refresh(): void {
  ctx.loading = true;
  const { keyword, pagination } = ctx;
  const { currentPage, pageSize } = pagination;
  const params: IWorkOrderQueryParams = {
    keyWords: keyword,
    subClassCode: props.subClassCode,
    pageNo: currentPage,
    pageSize
  };
  queryWorkOrder(params)
    .then(res => {
      const { list, total } = res.data;
      ctx.data = list;
      ctx.pagination.total = total;
    })
    .finally(() => (ctx.loading = false));
}
function save() {
  if (!ctx.selectedId) {
    ElMessage.warning("请选择物料");
    return;
  }
  if (ctx.selectedWorkOrder) {
    emit("workOrderChange", ctx.selectedWorkOrder);
  }
  visible.value = false;
}
</script>
