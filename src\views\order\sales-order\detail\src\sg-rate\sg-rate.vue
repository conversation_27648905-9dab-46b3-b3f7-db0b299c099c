<template>
  <div class="relative w-full h-full overflow-hidden">
    <el-scrollbar v-if="rates?.length || loadingReal" class="w-full h-full">
      <div class="order-list flex flex-col gap-5 py-1">
        <LoadingSkeleton :loading="loadingReal" :count="1">
          <item v-for="rate in rates" :key="rate.id" :rate="rate" />
        </LoadingSkeleton>
      </div>
      <el-dialog
        fullscreen
        destroy-on-close
        class="trigger-state-grid-rate-detail-dialog"
        v-model="visible"
        close-on-press-escape
        :title="detailStore.title"
        @closed="detailDialogClose"
        :show-close="false"
      >
        <template #header="{ close, titleId, titleClass }">
          <div :id="titleId" class="flex justify-between">
            <div class="flex gap-3 items-center">
              <el-button link @click="close">
                <el-icon size="20"><Back /></el-icon>
              </el-button>
              <div :class="titleClass" v-if="detailStore.title">{{ detailStore.title }}</div>
            </div>
            <el-button type="danger" @click="close">
              <el-icon size="20"><Close /></el-icon>
            </el-button>
          </div>
        </template>
        <Detail />
      </el-dialog>
      <EipGuideDialog />
      <AuditDialog />
    </el-scrollbar>
    <template v-else>
      <EmptyDataDisplay />
    </template>
  </div>
</template>

<script setup lang="ts">
import LoadingSkeleton from "@/views/order/sales-order/detail/src/sg-rate/list/loading-skeleton.vue";
import Item from "@/views/order/sales-order/detail/src/sg-rate/list/item.vue";
import Detail from "./detail/detail.vue";
import { computed, onMounted, provide, reactive, ref, watch } from "vue";
import {
  useSalesOrderDetailStore,
  useSalesStateGriRateDetailStore,
  useSalesStateGridRateListStore
} from "@/store/modules";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { isEqual } from "lodash-unified";
import { eipGuideKey } from "@/views/components/state-grid-trigger-score/tokens";
import EipGuideDialog from "@/views/components/state-grid-trigger-score/eip-guide-dialog.vue";
import AuditDialog from "@/views/order/sales-order/detail/src/sg-rate/audit-dialog/audit-dialog.vue";
import { syncAuditKey } from "@/views/order/sales-order/detail/src/sg-rate/tokens";
import { useThrottleFn } from "@vueuse/core";
import { RefreshSceneEnum, SocketEventEnum, StateGridOrderSyncType } from "@/enums";
import { useSocket } from "@/utils/useSocket";
import EmptyDataDisplay from "@/views/order/components/empty-data-display/index.vue";
import { Close, Back } from "@element-plus/icons-vue";

provide(eipGuideKey, reactive({ visible: false }));
provide(syncAuditKey, reactive({ visible: false }));

const listStore = useSalesStateGridRateListStore();
const detailStore = useSalesStateGriRateDetailStore();
const salesDetailStore = useSalesOrderDetailStore();
const socket = useSocket();
const loadingReal = ref(false);
const handleRefresh = useThrottleFn(refresh, REFRESH_SYNC_DATA_PERIOD, true);
const rates = computed(() => listStore.rates);
const visible = computed({
  get() {
    return detailStore.dialogVisible;
  },
  set(value) {
    detailStore.$patch({ dialogVisible: value });
  }
});

onMounted(() => {
  loadingReal.value = true;
  listStore.setPurchaseOrderId(salesDetailStore.saleOrderId);
  handleRefresh();
  addRefreshEventListener();
});

watch(
  () => listStore.rates.map(rate => rate.scoreResult),
  (value, oldValue) => {
    if (!isEqual(value, oldValue)) {
      salesDetailStore.refreshStepStatus();
    }
  }
);

async function refresh() {
  await listStore.refreshStateGridRateList();
  loadingReal.value = false;
}

function detailDialogClose() {
  detailStore.$reset();
}

function addRefreshEventListener() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, purchaseOrderItemId, dataType } = event;
    const matching = listStore.rates.find(item => item.purchaseLineId === purchaseOrderItemId);
    if (!matching) {
      return;
    }
    if (
      // 同步状态发生变化
      type === RefreshSceneEnum.SYNC_DATA_LIST ||
      // 触发质量评分状态发生变化
      (type === RefreshSceneEnum.SYNC_DATA && dataType === StateGridOrderSyncType.TRIGGER_SCORE)
    ) {
      handleRefresh();
    }
  });
}
</script>

<style lang="scss">
@import "@/views/order/sales-order/detail/styles/mixin";

.trigger-state-grid-rate-detail-dialog {
  @include full-screen-dialog;
}

.order-list {
  width: calc(100% - 16px);
  margin-left: 8px;
}
</style>
