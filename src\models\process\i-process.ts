export interface IProcess {
  /** 	唯一标识 */
  id: string;
  /** 	生产阶段唯一标识 */
  produceStageId: number;
  /** 	工序名称 */
  processName: string;
  /** 	工序编码 */
  processCode: string;
  /** 序号 */
  indexNo: number;
  /** 	定制 */
  customMade: boolean;
  /** 是否是自动导入 */
  autoCollect?: boolean;
  /** 备注 */
  remarks: string;

  /** 	工序id */
  processId: string;
  /** 设备是否必须 */
  requireDevice: boolean;
}

export interface IProcessRouteList {
  id: string;
  /** 品类 */
  categoryCode: string;
  categoryName: string;
  /** 工艺路线code */
  code: string;
  /** 工艺路线名称 */
  name: string;
  /** 状态 */
  status: boolean;
  /** 是否虚拟订单 */
  virtualOrderFlag?: boolean;
  /** 工序信息 */
  processInfos: IProcess[];
}
