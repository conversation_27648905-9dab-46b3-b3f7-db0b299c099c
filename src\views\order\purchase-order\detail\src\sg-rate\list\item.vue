<template>
  <div class="flex rounded item-container">
    <SyncStatus :success="rate.syncResult" />
    <div class="flex flex-col justify-between flex-1 px-6 py-3 bg-bg_color">
      <div class="flex-bc">
        <div class="flex-bc flex-1 mr-5 lg:mr-10">
          <Description :rate="rate" />
          <div v-if="rate.scoreResult == null" class="shrink-0">
            <img :src="RateTriggerBefore" alt="rate-trigger-before" />
          </div>
          <div v-else-if="rate.scoreResult" class="shrink-0">
            <img :src="RateTriggerSuccess" alt="rate-trigger-success" />
          </div>
          <div v-else class="shrink-0">
            <img :src="RateTriggerFail" alt="rate-trigger-fail" />
          </div>
        </div>
        <div class="flex flex-col gap-2 text-right">
          <el-badge :value="rate.resultCount" :hidden="!rate.resultCount">
            <el-button @click="openDetailDialog" v-track="TrackPointKey.FORM_PURCHASE_TRIGGER_DETAIL"
              >触发详情</el-button
            >
          </el-badge>
          <el-button
            v-auth="PermissionKey.form.formPurchaseTriggerScoreBtnOneKey"
            v-track="TrackPointKey.FORM_PURCHASE_TRIGGER_BTN_ONE_KEY"
            type="primary"
            @click="showSyncAuditDialog"
            >一键触发</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SyncStatus from "@/views/order/purchase-order/detail/src/components/sync-status.vue";
import Description from "@/views/order/purchase-order/detail/src/sg-rate/list/description.vue";
import RateTriggerBefore from "@/assets/img/rate_trigger_before.png";
import RateTriggerFail from "@/assets/img/rate_trigger_fail.png";
import RateTriggerSuccess from "@/assets/img/rate_trigger_success.png";
import { IStateGridRate } from "@/models";
import { computed, inject } from "vue";
import { useStateGriRateDetailStore } from "@/store/modules/state-grid-rate";
import { PermissionKey, TrackPointKey } from "@/consts";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { syncAuditKey } from "@/views/order/purchase-order/detail/src/sg-rate/tokens";
import { OrderType } from "@/enums";

const props = defineProps<{
  rate: IStateGridRate;
}>();

const detailStore = useStateGriRateDetailStore();
const purchaseDetailStore = usePurchaseOrderDetailStore();
const ctx = inject(syncAuditKey);

const rate = computed(() => props.rate);

function showSyncAuditDialog() {
  ctx.baseInfo = {
    isCable: purchaseDetailStore.isCable,
    isArmourClamp: purchaseDetailStore.isArmourClamp,
    poNo: purchaseDetailStore.purchaseOrder.poNo,
    subClassCode: purchaseDetailStore.purchaseOrder.subClassCode,
    orderType: OrderType.PURCHASE,
    orderId: purchaseDetailStore.purchaseOrderId,
    ...rate.value
  };
  ctx.score = rate.value;
  ctx.visible = true;
}

function openDetailDialog() {
  detailStore.openDialog(props.rate);
}
</script>

<style scoped>
.item-container {
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
}
</style>
