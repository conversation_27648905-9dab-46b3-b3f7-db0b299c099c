import { ContractTypeEnumMapDisplayName, TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "",
      type: "selection",
      reserveSelection: true
    },
    {
      label: "采购订单行项目号",
      prop: "poItemNo",
      minWidth: TableWidth.order
    },
    {
      label: "采购订单号",
      prop: "poNo",
      minWidth: TableWidth.order
    },
    {
      label: "采购订单行项目ID",
      prop: "poItemId",
      minWidth: TableWidth.suborder
    },
    {
      label: "合同名称",
      prop: "conName",
      width: TableWidth.largeName
    },
    {
      label: "合同类型",
      prop: "conType",
      width: TableWidth.largeType,
      cellRenderer(data: TableColumnRenderer) {
        return ContractTypeEnumMapDisplayName[data.row.conType];
      }
    },
    {
      label: "采购数量",
      prop: "amount",
      width: TableWidth.number
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: TableWidth.suborder
    },
    {
      label: "物料编码",
      prop: "materialCode",
      width: TableWidth.largeType
    },
    {
      label: "物料描述",
      prop: "materialDesc",
      width: TableWidth.largeOperation
    }
  ];

  return { columns };
}
