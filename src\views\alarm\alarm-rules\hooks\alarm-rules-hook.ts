import { IAlarmRuleSetting } from "@/models";
import { useAlarmRulesStore } from "@/store/modules";

/**
 * 告警规则的hook
 */
export const useAlarmRulesHook = () => {
  const alarmRulesStore = useAlarmRulesStore();

  // 获取告警规则
  async function queryAlarmRules() {
    await alarmRulesStore.queryAlarmRules();
  }

  // 保存修改告警规则
  async function saveAlarmRules(alarmRulesSetting: IAlarmRuleSetting) {
    await alarmRulesStore.saveAlarmRules(alarmRulesSetting);
  }
  return { queryAlarmRules, saveAlarmRules };
};
