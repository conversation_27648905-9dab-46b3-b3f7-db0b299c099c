<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />

    <el-dialog
      v-model="drawerVisible"
      :title="isEditMode ? '编辑开工' : '新增开工'"
      class="default"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeDialog"
    >
      <StartWorkForm ref="formRef" :supervisionPlanId="props.supervisionPlan?.id" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button v-if="isAddMode" type="warning" @click="handleSaveAndAddBtn()" :loading="saveAndAddloading"
            >保存并继续新增</el-button
          >
          <el-button type="primary" @click="handleSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import StartWorkForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getStartWorkById, createStartWork, updateStartWork } from "@/api/south-grid-access";
import { ISupervisionPlan } from "@/models/south-grid-access";

const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
  supervisionPlan?: ISupervisionPlan;
}>();

const loading = ref(false);
const saveAndAddloading = ref(false);
const emits = defineEmits(["postSaveSuccess"]);
let isSaveAndAddTag = false;
const formRef = ref<InstanceType<typeof StartWorkForm>>();
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn(onSave, loading);
const handleSaveAndAddBtn = useLoadingFn(onSaveAndAdd, saveAndAddloading);
// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    return;
  }
  const { data } = await getStartWorkById(props.id);
  formRef.value.initFormValue(data);
});

/**
 *  保存按钮点击事件
 */
async function onSave() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();
  if (isAddMode.value) {
    formVal.supervisionPlanId = props.supervisionPlan?.id;
    await createStartWork(formVal);
  } else {
    await updateStartWork(formVal);
  }

  closeDialog();
  emits("postSaveSuccess");
  ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
}

/**
 *  保存并继续新增
 */
async function onSaveAndAdd() {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }
  const formVal = formRef.value.getFormValue();
  formVal.supervisionPlanId = props.supervisionPlan?.id;
  await createStartWork(formVal);
  formRef.value.resetFormValue();
  isSaveAndAddTag = true;
  ElMessage({ message: "新增成功", type: "success" });
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  if (isSaveAndAddTag) {
    emits("postSaveSuccess");
  }
  isSaveAndAddTag = false;
  drawerVisible.value = false;
}
</script>

<style scoped></style>
