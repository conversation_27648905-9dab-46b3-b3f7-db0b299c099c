<template>
  <div :id="BASE_INFO_ID">
    <TitleBar title="基础信息" class="mb-2.5" />
    <el-descriptions>
      <el-descriptions-item label="采购订单号">
        {{ info.poNo }}
      </el-descriptions-item>
      <el-descriptions-item label="采购订单行号">
        {{ info.poItemNo }}
      </el-descriptions-item>
      <el-descriptions-item label="物料编码">
        {{ info.materialCode }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions>
      <el-descriptions-item label="物料描述" :span="3">
        {{ info.materialDesc }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { computed } from "vue";
import { IStateGridSyncAuditBaseInfo } from "@/models";
import { BASE_INFO_ID } from "./consts";

const store = useStateGridSyncAuditStore();

const info = computed(() => store.baseInfo || ({} as IStateGridSyncAuditBaseInfo));
</script>

<style scoped>
.el-descriptions {
  @apply px-5 py-2.5;
  background: var(--el-fill-color-blank);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
}
</style>
