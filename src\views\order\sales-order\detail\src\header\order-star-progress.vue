<template>
  <div class="flex items-center">
    <StarFilled :width="20" :color="starColor" class="cursor-pointer mr-2" @click="toggleSubscription" />
    <EnumTag :value="status" :enum="SalesOrderStatus" enumName="SalesOrderStatus" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from "vue";
import { SalesOrderStatus } from "@/enums";
import { StarFilled } from "@element-plus/icons-vue";
import EnumTag from "@/components/EnumTag";
import { ElMessage } from "element-plus";
import { useSalesOrderDetailStore, useSalesOrderManagementStore } from "@/store/modules";
import { useConfirm } from "@/utils/useConfirm";

const store = useSalesOrderDetailStore();
const salesOrderManagementStore = useSalesOrderManagementStore();

const subscription = ref(false);

const salesOrder = computed(() => store.salesOrder);
const starColor = computed(() => (subscription.value ? "#E6A23C" : "#DEDFE0"));
const status = computed(() => salesOrder.value?.orderProgress);

watchEffect(() => {
  subscription.value = salesOrder.value?.follow ?? false;
});

async function toggleSubscription() {
  if (subscription.value && !(await useConfirm("是否确认取消关注该销售订单", "取消关注"))) {
    return;
  }
  await salesOrderManagementStore.salesOrderFollow(salesOrder.value.id);
  ElMessage.success(subscription.value ? "取消关注订单成功" : "关注订单成功");
  subscription.value = !subscription.value;
}
</script>
