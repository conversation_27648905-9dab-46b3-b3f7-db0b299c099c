<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" :close-dialog="closeDialog" />
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <div v-loading="loading">
        <title-bar title="基础信息" />
        <base-info-form ref="baseInfoFormRef" class="mt-4" :mode="props.mode" />
        <title-bar title="数据权重" />
        <data-weight-form ref="dataWeightFormRef" class="mt-4" />
      </div>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="saveLoading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import TitleBar from "@/components/TitleBar";
import BaseInfoForm from "./base-info-form.vue";
import DataWeightForm from "./data-weight-form.vue";
import { EditQualitySpecificationParams } from "@/models/quality-tracing";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  addQualitySpecification,
  editQualitySpecification,
  copyQualitySpecification,
  queryQualitySpecificationById
} from "@/api/quality-tracing";

/**
 * 新增/编辑/复制 质量规范 弹窗
 * 根据不同模式，执行不同的操作：前置请求、保存请求等
 * 组成：
 * 1. 基础信息表单
 * 2. 数据权重表单
 */

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add" | "copy";
  /** 标题 */
  title: string;
  /** 质量标准id */
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const baseInfoFormRef = ref<InstanceType<typeof BaseInfoForm>>();
const dataWeightFormRef = ref<InstanceType<typeof DataWeightForm>>();
const saveLoading = ref(false);
const loading = ref(false);

const isAddMode = computed(() => props.mode === "add");
const isEditMode = computed(() => props.mode === "edit");

/**
 * @description: 校验两张表单
 */
const validateForms = async () => {
  const baseInfoValid = await baseInfoFormRef.value?.validateForm();
  const dataWeightValid = await dataWeightFormRef.value?.validateForm();

  if (!baseInfoValid || !dataWeightValid) {
    return false;
  }

  return true;
};

/**
 * @description: 获取最终(合计)表单值
 */
function getFinallyFormValue(): EditQualitySpecificationParams {
  const baseInfoFormVal = baseInfoFormRef.value?.getFormValue();
  const dataWeightFormVal = dataWeightFormRef.value?.getFormValue();
  return {
    ...baseInfoFormVal,
    rawMaterialWeight: Number(dataWeightFormVal.rawMaterialWeight),
    processWeight: Number(dataWeightFormVal.processWeight),
    experimentWeight: Number(dataWeightFormVal.experimentWeight),
    processStabilityWeight: Number(dataWeightFormVal.processStabilityWeight)
  };
}

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const valid = await validateForms();
  if (!valid) {
    return;
  }
  const finallyFormValue = getFinallyFormValue();
  requestSave(finallyFormValue);
};

/**
 * @description: 弹窗开启后置事件
 */
const postDialogOpen = useLoadingFn(async () => {
  if (isAddMode.value) {
    return;
  }
  const { data } = await queryQualitySpecificationById(props.id);
  if (!data) {
    return;
  }
  const baseInfoVal = {
    categoryCode: data.categoryCode,
    subClassCode: data.subClassCode,
    qualityCode: isEditMode.value ? data.qualityCode : "",
    qualityName: data.qualityName,
    enabled: data.enabled,
    remark: data.remark,
    matchType: data.matchType
  };

  baseInfoFormRef.value?.initFormValue(baseInfoVal);

  const dataWeightFormVal = {
    rawMaterialWeight: String(data.rawMaterialWeight),
    processWeight: String(data.processWeight),
    experimentWeight: String(data.experimentWeight),
    processStabilityWeight: String(data.processStabilityWeight)
  };
  dataWeightFormRef.value?.initFormValue(dataWeightFormVal);
}, loading);

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}

/**
 * @description: 请求保存
 */
const requestSave = useLoadingFn(async (params: EditQualitySpecificationParams) => {
  let apiFunction;
  switch (props.mode) {
    case "add":
      apiFunction = addQualitySpecification;
      break;
    case "edit":
      apiFunction = (p: EditQualitySpecificationParams) => {
        return editQualitySpecification(p, props.id);
      };
      break;
    case "copy":
      apiFunction = (p: EditQualitySpecificationParams) => {
        return copyQualitySpecification(p, props.id);
      };
      break;
  }

  const { data } = await apiFunction(params);
  if (data) {
    emits("postSaveSuccess");
    ElMessage({
      message: "保存成功",
      type: "success"
    });
    closeDialog();
  }
}, saveLoading);

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, visible => {
  if (visible) {
    postDialogOpen();
  }
});
</script>

<style scoped></style>
