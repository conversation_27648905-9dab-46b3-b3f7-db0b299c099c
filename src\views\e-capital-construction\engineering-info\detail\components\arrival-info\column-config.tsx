import { ColumnWidth, ComponentArrivalEnumMapDesc, MainRegulatEnumMapDesc, TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 到货进度
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "原材料组部件类型",
      prop: "componentType",
      minWidth: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return ComponentArrivalEnumMapDesc[data.row.componentType];
      }
    },
    {
      label: "主体变/调补变类型",
      prop: "mainRegulatingTransformer",
      minWidth: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return MainRegulatEnumMapDesc[data.row.mainRegulatingTransformer];
      }
    },
    {
      label: "子实物编码",
      prop: "subPhysicalItemCode",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "计划到货时间",
      prop: "plannedArrivalTime",
      minWidth: ColumnWidth.Char11,
      formatter: dateFormatter()
    },
    {
      label: "实际到货时间",
      prop: "actualArrivalTime",
      minWidth: ColumnWidth.Char11,
      formatter: dateFormatter()
    },
    {
      label: "滞后需求天数",
      prop: "demandLagDays",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char10,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];

  return columnsConfig;
}
