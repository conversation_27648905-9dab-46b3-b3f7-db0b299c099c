<template>
  <PureTable
    class="flex-1 overflow-hidden pagination"
    row-key="id"
    :data="list"
    :columns="columnsConfig"
    :loading="loading"
    showOverflowTooltip
    v-model:pagination="pagination"
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
    <template #opertion="{ row }">
      <el-button link type="primary" @click="handleDownloadErrorData(row.faultFileInfo)"> 错误数据下载 </el-button>
    </template>
  </PureTable>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import PureTable from "@pureadmin/table";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genProductionTableColumnsConfig } from "./columns-config";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { downloadByUrl } from "@pureadmin/utils";
import { getExperimentDataImortList } from "@/api/import";
import { FaultFileInfo } from "@/models/import/import-experiment-data";
import { ElMessage } from "element-plus";

const { pagination } = useTableConfig();
const { columnsConfig } = genProductionTableColumnsConfig();
const loading = ref(false);
const list = ref([]);

/**
 * @description: 请求试验数据导入列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await getExperimentDataImortList({
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 下载错误数据
 */
function handleDownloadErrorData(errorInfo: FaultFileInfo | null) {
  if (!errorInfo) {
    ElMessage({
      type: "info",
      message: "暂无错误数据"
    });
    return;
  }
  const { url, name } = errorInfo;
  downloadByUrl(url, name);
}

watch([() => pagination.currentPage, () => pagination.pageSize], requestList);

onMounted(requestList);

defineExpose({
  reloadExperimentList: () => {
    pagination.currentPage = 1;
    requestList();
  }
});
</script>

<style scoped></style>
