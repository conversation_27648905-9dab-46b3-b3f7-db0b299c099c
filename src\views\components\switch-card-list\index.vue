<template>
  <el-button-group class="btn-group">
    <el-button :class="{ 'is-active': toggleStyleCMP }" @click="getMenuVisible(ToggleStyleEnum.MENU)">
      <IconifyIconOffline :icon="Menu" />
    </el-button>
    <el-button :class="{ 'is-active': !toggleStyleCMP }" @click="getMenuVisible(ToggleStyleEnum.TABLE)">
      <FontIcon icon="icon-list" />
    </el-button>
  </el-button-group>
</template>

<script setup lang="ts">
import Menu from "@iconify-icons/ep/menu";
import { ToggleStyleEnum } from "@/enums";
import { computed, ref } from "vue";
import { onMounted } from "vue";

const emits = defineEmits<{
  (event: "toggleSwitch", seitchType: string);
}>();

const currentMenu = ref<string>("");
const toggleStyleCMP = computed(() => currentMenu.value === ToggleStyleEnum.MENU);

onMounted(() => {
  getMenuVisible(ToggleStyleEnum.MENU);
});

function getMenuVisible(toggleStyle: string) {
  currentMenu.value = toggleStyle;
  emits("toggleSwitch", currentMenu.value);
}
</script>

<style scoped lang="scss">
.btn-group {
  .el-button {
    padding: 8px;

    svg {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
