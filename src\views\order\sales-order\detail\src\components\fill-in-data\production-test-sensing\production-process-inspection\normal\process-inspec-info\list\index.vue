<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 表格 -->
    <div class="bg-bg_color mx-2 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="{ row }">
          <el-button
            v-auth="PermissionKey.form.formPurchaseProcessCreate"
            link
            type="primary"
            @click="props.copyProcessInspection(row)"
          >
            复制
          </el-button>
          <el-button
            v-auth="PermissionKey.form.formPurchaseProcessEdit"
            link
            type="primary"
            @click="props.editProdInspect(row)"
          >
            编辑
          </el-button>
          <el-button
            v-auth="PermissionKey.form.formPurchaseProcessDelete"
            link
            type="danger"
            @click="props.delProdInspect(row)"
          >
            删除
          </el-button>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onUnmounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import { getProcessInspecTableColumnsConfig } from "./column-config";
import { getProductionProcessList } from "@/api/production-test-sensing/production-process-inspection";
import { IProductionProcessList, ISearchProductionProcessList } from "@/models";
import { useSalesFillInDataStore, useSalesOrderDetailStore } from "@/store/modules";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { emitter } from "@/utils/mitt";
import { PermissionKey } from "@/consts";

/**
 * 过程检测-列表
 */
const props = defineProps<{
  editProdInspect: (processInfo: IProductionProcessList) => void;
  delProdInspect: (processInfo: IProductionProcessList) => void;
  copyProcessInspection: (processInfo: IProductionProcessList) => void;
}>();

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);

const fillInDataStore = useSalesFillInDataStore();
const productionProcessInspecStore = useProductionProcessInspecStore();
const salesOrderDetailStore = useSalesOrderDetailStore();

const { pagination } = useTableConfig();
const { columnsConfig } = getProcessInspecTableColumnsConfig();
const loading = ref(false);
const list = ref<IProductionProcessList[]>([]);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: ISearchProductionProcessList = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    processCode: productionProcessInspecStore.currentTagInfo.processCode
  };
  if (salesOrderDetailStore.isCable) {
    params.productionId = fillInDataStore.dataId;
  } else {
    params.workOrderId = fillInDataStore.dataId;
  }
  const { data } = await getProductionProcessList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  requestList();
}

emitter.on("refreshProcessInspecList", reloadList);

watch(
  () => productionProcessInspecStore.currentTagInfo.processCode,
  v => {
    if (v) {
      requestList();
    }
  },
  { immediate: true }
);

onUnmounted(() => {
  emitter.off("refreshProcessInspecList", reloadList);
});
</script>

<style scoped></style>
