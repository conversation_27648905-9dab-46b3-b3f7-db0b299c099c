import { ColumnWidth, MonitoringLocationEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "监测位置",
      prop: "monitoringLocation",
      minWidth: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return MonitoringLocationEnumMapDesc[data.row.monitoringLocation];
      }
    },
    {
      label: "设备编号",
      prop: "equipmentNumber",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "检验位置点名称",
      prop: "pointName",
      width: ColumnWidth.Char8
    },
    {
      label: "温度（单位：℃）",
      prop: "temperature",
      width: ColumnWidth.Char12
    },
    {
      label: "湿度",
      prop: "humidity",
      width: ColumnWidth.Char8
    },
    {
      label: "洁净度指标1",
      prop: "cleanliness1",
      width: ColumnWidth.Char8
    },
    {
      label: "洁净度指标2",
      prop: "cleanliness2",
      width: ColumnWidth.Char8
    },
    {
      label: "洁净度指标3",
      prop: "cleanliness3",
      width: ColumnWidth.Char8
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char10,
      slot: "pullStatus"
    },
    {
      label: "报告时间",
      prop: "reportTime",
      width: ColumnWidth.Char12,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char11
    }
  ];
  return { columns };
}
