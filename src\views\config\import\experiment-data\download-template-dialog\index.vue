<template>
  <div class="inline-block">
    <el-button size="large" type="primary" @click="openDialog"> 下载模板 </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="下载模板"
      align-center
      :width="500"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <template-form ref="formRef" />
      <!-- 内容 -->
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleDownloadBtn" :loading="loading">下载</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { downloadByData } from "@pureadmin/utils";
import TemplateForm from "./template-form.vue";
import { getExperimentDataImportTemplate } from "@/api/import";

const loading = ref(false);
const dialogVisible = ref(false);
const formRef = ref<InstanceType<typeof TemplateForm>>();

const requestTemplate = useLoadingFn(async () => {
  const params = formRef.value.getFormValue();
  return await getExperimentDataImportTemplate(params);
}, loading);

const genTemplateName = () => {
  const { subClassName, experimentProcessName } = formRef.value.getSelectedInfo();
  return `${subClassName}-${experimentProcessName}模板`;
};

const handleDownloadBtn = async () => {
  const verificationResult = await formRef.value.validateForm();

  if (!verificationResult) {
    return;
  }
  const blob = await requestTemplate();

  downloadByData(blob, genTemplateName(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

  // 处理保存后续事件
  closeDialog();
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
