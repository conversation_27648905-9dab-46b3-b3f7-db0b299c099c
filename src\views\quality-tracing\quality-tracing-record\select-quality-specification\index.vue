<template>
  <div class="inline-block">
    <el-button
      v-auth="PermissionKey.qualityTracing.qualityTracingRecordSelectSpecification"
      link
      type="primary"
      @click="openDialog"
    >
      维护质量规范
    </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="维护质量规范"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <select-list ref="selectListRef" :sub-class-code="subClassCode" :quality-id="qualityId" />
      <!-- 内容 -->
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { PermissionKey } from "@/consts";
import SelectList from "./select-list/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { bindingQualitySpecification } from "@/api/quality-tracing";
import { BindingQualitySpecificationParams } from "@/models/quality-tracing";

const props = defineProps<{
  /** 生产订单/工单的id */
  id: string;
  /** 物资种类 */
  subClassCode: string;
  /** 初始化时已选中的质量规范id */
  qualityId?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const selectListRef = ref<InstanceType<typeof SelectList>>();
const dialogVisible = ref(false);
const loading = ref(false);

/**
 * @description: 请求绑定质量规范
 */
const requestBindingQualitySpecification = useLoadingFn(async (params: BindingQualitySpecificationParams) => {
  return await bindingQualitySpecification(params);
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const selectedId = selectListRef.value.getSelectedId();

  if (!selectedId) {
    ElMessage("请先选择一条质量规范");
    return;
  }

  const params: BindingQualitySpecificationParams = {
    dataId: props.id,
    qualityId: selectedId,
    subClassCode: props.subClassCode
  };

  const { data: res } = await requestBindingQualitySpecification(params);

  if (!res) {
    return;
  }

  // 处理保存后续事件
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({
    message: "操作成功",
    type: "success"
  });
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
