<template>
  <div class="air-tightness">
    <!-- 试验信息 -->
    <AirTightnessList
      v-loading="loading"
      @editQMXExperiment="onEditQMXExperiment($event)"
      @deleteQMXExperimentSuccess="onDeleteQMXExperimentSuccess()"
      @loadMoreEvent="loadMore"
    />

    <!-- 添加气密性试验信息 -->
    <el-dialog
      v-model="addAirTightnessRef"
      :title="diagTitle"
      class="middle"
      align-center
      destroy-on-close
      :close-on-click-modal="false"
      @closed="onCancelQMXExperiment"
    >
      <div class="process-inspect-content">
        <AddAirTightnessExperiment ref="qmxExperimentInstFormInst" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addAirTightnessRef = false">取消</el-button>
          <el-button type="primary" :loading="saveLoading" @click="handleOnSaveQMXExperiment()">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import AirTightnessList from "./air-tightness-list/index.vue";
import AddAirTightnessExperiment from "./add-air-tightness/index.vue";
import { onMounted, ref, watchEffect, computed, onUnmounted } from "vue";
import {
  useEXFactoryExperimentStore,
  useEXFactoryQMXStore,
  useSalesFillInDataStore,
  usePurchaseOrderDetailStore
} from "@/store/modules";
import { IOutGoingFactoryQMXExperimentForm, IOutGoingFactoryQmxExperiment, IOutGoingFactoryNotJfnyReq } from "@/models";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { EExFactoryTest } from "../../../component/switch-tab/types";

const qmxExperimentInstFormInst = ref<InstanceType<typeof AddAirTightnessExperiment>>();
const saveLoading = ref<boolean>();
const handleOnSaveQMXExperiment = useLoadingFn(onSaveQMXExperiment, saveLoading);

const emits = defineEmits<{
  (event: "delSuccess", component: string): void;
  (event: "addDataSuccess", component: string): void;
}>();

const exFactoryQMXStore = useEXFactoryQMXStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const fillInDataStore = useSalesFillInDataStore();
const addAirTightnessRef = ref(false);
const diagTitle = ref("新增试验");
// 分页
const defaultPageInfo = {
  pageNo: 1,
  pageSize: 10
};
const totalPage = computed(() => {
  return Math.ceil(exFactoryQMXStore.experimentListTotal / defaultPageInfo.pageSize);
});
const loading = ref<boolean>(false);
const initOutGoingFactoryQMXExperimentList = useLoadingFn(queryOutGoingFactoryQMXExperimentList, loading);

onMounted(() => {
  watchEffect(() => {
    if (exFactoryExperimentStore.activeProcess.processId) {
      defaultPageInfo.pageNo = 1;
      initOutGoingFactoryQMXExperimentList(defaultPageInfo);
    }
  });
});

async function queryOutGoingFactoryQMXExperimentList(params: IOutGoingFactoryNotJfnyReq) {
  await exFactoryQMXStore.queryOutGoingFactoryQMXExperimentList(params);
}

/**
 * 加载更多
 */
const loadMore = () => {
  defaultPageInfo.pageNo++;
  if (defaultPageInfo.pageNo > totalPage.value) {
    return;
  }

  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
};

// 新增气密性试验信息
const addAirTightness = () => {
  addAirTightnessRef.value = true;
  diagTitle.value = "新增试验";
  exFactoryQMXStore.isEditTightness = false;
  exFactoryExperimentStore.getMetadataModelInfo(
    exFactoryExperimentStore.activeProcess.processId,
    fillInDataStore.data.specificationType
  );
};

// 编辑气密性试验
const onEditQMXExperiment = (data: IOutGoingFactoryQmxExperiment) => {
  addAirTightnessRef.value = true;
  diagTitle.value = "编辑试验";
  exFactoryQMXStore.isEditTightness = true;
  exFactoryQMXStore.getOutGoingFactoryQMXExperimentDetailById(data.id);
  exFactoryExperimentStore.setMetadataModelInfo(data.rawMetadataValue);
};

const onDeleteQMXExperimentSuccess = () => {
  emits("delSuccess", EExFactoryTest.QMXSY);
  exFactoryQMXStore.initExperimentList();
  defaultPageInfo.pageNo = 1;
  initOutGoingFactoryQMXExperimentList(defaultPageInfo);
};

// 关闭弹框
const onCancelQMXExperiment = () => {
  addAirTightnessRef.value = false;
  exFactoryQMXStore.setOutGoingFactoryQmxExperimentDetail();
  exFactoryExperimentStore.setMetadataModelInfo();
  qmxExperimentInstFormInst.value.resetQMXExperimentBaseFormValue();
};

/** 保存气密性试验 */
async function onSaveQMXExperiment() {
  if (!qmxExperimentInstFormInst.value) {
    return;
  }

  const qmxExperimentBaseValue = await qmxExperimentInstFormInst.value.getQMXExperimentBaseFormValue();
  const qmxAirTightnessExpersValue = await qmxExperimentInstFormInst.value.getQMXAirTightnessExpersFormValue();

  /*** 表单数据验证不过 */
  if (
    (typeof qmxExperimentBaseValue === "boolean" && !qmxExperimentBaseValue) ||
    !Array.isArray(qmxAirTightnessExpersValue.submitData) ||
    qmxAirTightnessExpersValue.submitData.length === 0
  ) {
    return;
  }
  const { processCode, processName, processId } = exFactoryExperimentStore.activeProcess;
  const dataId = fillInDataStore.dataId;
  const { purchaseOrderId, isCable } = usePurchaseOrderDetailStore();
  const orderParam: { productionId?: string; workOrderId?: string } = {};
  isCable && (orderParam.productionId = dataId);
  !isCable && (orderParam.workOrderId = dataId);
  const factoryQMXExperimentForm: IOutGoingFactoryQMXExperimentForm = {
    ...(qmxExperimentBaseValue as IOutGoingFactoryQMXExperimentForm),
    ...exFactoryExperimentStore.activeProcess,
    rawMetadataValue: qmxAirTightnessExpersValue.submitData.map(item => ({
      dataCode: item.targetCode,
      identityCode: item.dataTypeIdentityDetail.identityCode,
      dataValue: item.targetValue
    })),
    ...orderParam,
    purchaseId: purchaseOrderId,
    processCode,
    processName,
    processId
  };

  let res = null;
  if (!factoryQMXExperimentForm.id) {
    await exFactoryQMXStore.createOutGoingFactoryQMXExperiment(factoryQMXExperimentForm);
  } else {
    res = await exFactoryQMXStore.editOutGoingFactoryQMXExperiment(factoryQMXExperimentForm);
  }

  ElMessage.success(!factoryQMXExperimentForm?.id ? "新增成功" : "编辑成功");
  addAirTightnessRef.value = false;
  qmxExperimentBaseValue;
  qmxExperimentInstFormInst.value?.resetQMXExperimentBaseFormValue();
  exFactoryQMXStore.setOutGoingFactoryQmxExperimentDetail();
  exFactoryExperimentStore.setMetadataModelInfo();
  if (!factoryQMXExperimentForm?.id) {
    defaultPageInfo.pageNo = 1;
    initOutGoingFactoryQMXExperimentList(defaultPageInfo);
  } else {
    // 更新单个试验
    if (res && res.data?.id) {
      exFactoryQMXStore.updateDetailInfoById(res.data);
    }
  }
  if (!factoryQMXExperimentForm?.id) emits("addDataSuccess", EExFactoryTest.QMXSY);
  productionTestSensingStore.refreshProductionProcessStatus();
}

onUnmounted(() => {
  exFactoryQMXStore.initExperimentList();
});

defineExpose({
  addAirTightness
});
</script>

<style scoped lang="scss">
.air-tightness {
  .add-air-tightness {
    text-align: right;
    margin-bottom: 20px;
  }
}

.process-inspect-content {
  min-height: 652px;
  max-height: 672px;
}
</style>
