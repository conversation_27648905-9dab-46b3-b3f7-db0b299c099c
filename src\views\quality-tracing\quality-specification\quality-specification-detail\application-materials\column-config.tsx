import { ColumnWidth } from "@/enums";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig: TableColumnList = [
    {
      label: "物料编号",
      prop: "materialCode",
      fixed: "left",
      minWidth: ColumnWidth.Char12
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: ColumnWidth.Char17
    },
    {
      label: "物料描述",
      prop: "materialDescribe",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "物料单位",
      prop: "materialUnitName",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "规格型号",
      prop: "specificationModel",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "电压等级",
      prop: "voltageClass",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char4,
      fixed: "right",
      slot: "opertion"
    }
  ];

  return { columnsConfig };
}
