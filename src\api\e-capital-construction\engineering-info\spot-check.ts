/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { SpotCheckModel } from "@/models";
/**
 * @description: 原材料抽检信息列表
 */
export const SpotCheckInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<SpotCheckModel>>>(
    withApiGateway(`admin-api/ejj/project/raw-material/spotcheck/list/${id}`)
  );
};
/**
 * @description: 原材料抽检信息创建
 */
export const SpotCheckCreateApi = (params: SpotCheckModel) => {
  return http.post<SpotCheckModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/spotcheck`), {
    data: params
  });
};

/**
 * @description: 原材料抽检信息编辑
 */
export const SpotCheckEditApi = (params: SpotCheckModel) => {
  return http.put<SpotCheckModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/spotcheck`), {
    data: params
  });
};

/**
 * @description: 原材料抽检信息删除
 */
export const SpotCheckDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/spotcheck/${id}`));
};
