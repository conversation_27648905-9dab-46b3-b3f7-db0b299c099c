<template>
  <div class="flex flex-1 min-h-0">
    <el-scrollbar :height="450" v-loading="loading" class="flex-1">
      <section class="flex flex-row flex-wrap" v-if="collectValues.length">
        <el-card
          v-for="(item, index) in collectValues"
          :key="item.collectPoint"
          class="lg:basis-[31%] sm:basis-[40%] basis-[45%] shadow-md mb-5 mr-5 min-w-96"
          @click="toggleDialog(index)"
        >
          <div class="h-[200px] flex flex-col">
            <div class="pl-2">{{ item.collectName }}</div>
            <div class="flex-1">
              <AutoCollectChart :data="item" />
            </div>
          </div>
        </el-card>
        <el-dialog
          :title="focusData ? focusData.collectName : '--'"
          align-center
          draggable
          class="large"
          destroy-on-close
          v-model="dialogVisible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <div class="h-[280px]">
            <AutoCollectChart :data="focusData" :data-zoom="true" />
          </div>
        </el-dialog>
        <auto-acquisition-dialog ref="historyDialogRef" />
      </section>
      <CxEmpty v-if="!loading && !collectValues.length" />
    </el-scrollbar>
    <div class="w-96">
      <RealtimeLive v-if="batchNoInfo && batchNoInfo.deviceId" :device-id="batchNoInfo.deviceId" />
    </div>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import AutoCollectChart from "@/views/components/auto-collection-charts/auto-collection-charts.vue";
import { onMounted, onUnmounted, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { IReportWork } from "@/models/report-work";
import { IAutoCollectChartReq, IPointsRes, IProductionProcessList } from "@/models";
import { formatDate } from "@/utils/format";
import { AUTO_COLLECT_DATA_PERIOD, fullDateFormat } from "@/consts";
import { Pausable, useIntervalFn } from "@vueuse/core";
import { useAcquisitionMaintainStore } from "@/store/modules";
import { IAutoCollectChart } from "@/utils/auto-collect-points";
import RealtimeLive from "@/views/config/device/management/details/data-acquisition/realtime-live/index.vue";
import AutoAcquisitionDialog from "@/views/components/auto-acquisition-dialog/index.vue";

// 获取生产过程工艺检测
const productionProcessInspecStore = useProductionProcessInspecStore();
const acMaintainStore = useAcquisitionMaintainStore();
const collectValues = ref([]);
const pointList = ref([]);
const focusData = ref<any>(null);
const dialogVisible = ref(false);
const isRealtime = ref<boolean>(false);

const historyDialogRef = ref<InstanceType<typeof AutoAcquisitionDialog>>();

const loading = ref<boolean>(false);
const initAutoCollectCharts = useLoadingFn(getAutoCollectCharts, loading);
const initHistoryCollectCharts = useLoadingFn(getHistoryAutoCollectCharts, loading);
let timer: Pausable = null;

const props = withDefaults(
  defineProps<{
    /** 报工批次信息 */
    batchNoInfo: IReportWork;
    autoCollectItems?: IProductionProcessList[];
    options?: IReportWork[];
  }>(),
  {}
);

onMounted(async () => {
  if (props.options && props.options.length) {
    await initCharts(props.options[0]);
  }
});

const toggleDialog = (index: number) => {
  // 如果报工打包没有完成，禁止打开弹窗
  if (!props.batchNoInfo.dataPrepared) {
    return;
  }
  if (isRealtime.value) {
    dialogVisible.value = !dialogVisible.value;
    focusData.value = collectValues.value[index];
  } else {
    if (!historyDialogRef.value || !props.options.length) {
      return;
    }
    historyDialogRef.value.openAccquisitionDialog(
      props.batchNoInfo.deviceId,
      props.batchNoInfo.deviceCode,
      props.batchNoInfo.processId,
      collectValues.value[index].collectPoint,
      formatDate(props.batchNoInfo.workStartTime, fullDateFormat),
      formatDate(props.batchNoInfo.workEndTime, fullDateFormat) || formatDate(Date.now(), fullDateFormat)
    );
  }
};

async function initCharts(data: IReportWork | undefined) {
  clearInterval();
  collectValues.value = [];
  pointList.value = [];
  if (!data) {
    return;
  }
  const { processId, deviceCode, deviceId, workStartTime, workEndTime } = data || {};
  const params = {
    deviceCode,
    processId,
    deviceId
  };
  pointList.value = (await acMaintainStore.pointByProcessIdDeviceId(params)) || [];
  if (!pointList.value?.length) {
    return;
  }

  // 如果该数据没有打包完，只需要展示空码点，不需要继续查询历史码点数据等
  if (!props.batchNoInfo.dataPrepared) {
    const list = pointList.value.map(item => {
      return {
        collectPoint: item.pointNo,
        deviceName: "",
        values: []
      };
    });
    setCollectValues(list);
    return;
  }

  if (workEndTime) {
    isRealtime.value = false;
    Object.assign(params, formatParams(workStartTime, workEndTime));
    await refreshHistory(params);
  } else {
    isRealtime.value = true;
    clearInterval();
    timer = useIntervalFn(
      async () => {
        await refreshAuto(params);
      },
      AUTO_COLLECT_DATA_PERIOD,
      {
        immediateCallback: true
      }
    );
  }
}

/** 刷新采集项图表 */
async function refreshAuto(params: IAutoCollectChartReq) {
  const res = await initAutoCollectCharts(params).catch(() => {
    clearInterval();
    return [];
  });
  setCollectValues(res);
}

async function refreshHistory(params: IAutoCollectChartReq) {
  const res = await initHistoryCollectCharts(params);
  setCollectValues(res);
}

/** 获取自动采集项数据 -- 实时 */
async function getAutoCollectCharts(params: IAutoCollectChartReq) {
  return await productionProcessInspecStore.getAutoCollectCharts(params);
}

/** 获取自动采集项数据 --历史 */
async function getHistoryAutoCollectCharts(params: IAutoCollectChartReq) {
  return await productionProcessInspecStore.getHistoryAutoCollectCharts(params);
}

function setCollectValues(chartsData: IAutoCollectChart[]) {
  if (!chartsData.length) {
    clearInterval();
  }
  // 渲染空图表
  if (!chartsData.length && pointList.value) {
    renderEmptyChart(pointList.value);
    return;
  }
  // 渲染图表数据
  renderCharts(pointList.value, chartsData);
}

function clearInterval() {
  timer?.pause();
  timer = null;
}

// 渲染空图表
function renderEmptyChart(points: IPointsRes[]) {
  const pointValues = points.map(item => {
    const { pointNo, pointName } = item;
    return {
      collectPoint: pointNo,
      collectName: pointName,
      deviceName: pointNo,
      values: []
    };
  });
  collectValues.value = pointValues;
}

function renderCharts(points: IPointsRes[], chartsData: IAutoCollectChart[]) {
  const pointNos = points.map(item => item.pointNo);
  const pointCollect = chartsData.filter(chart => pointNos.includes(chart.collectPoint));
  const pointValues = pointCollect.map(chart => {
    const currentCollect = points.find(point => point.pointNo === chart.collectPoint);
    const collectName = currentCollect.pointName;
    return {
      ...chart,
      collectName
    };
  });
  collectValues.value = pointValues;
}

function formatParams(workStartTime: Date, workEndTime: Date) {
  return {
    timeFrom: formatDate(workStartTime, fullDateFormat),
    timeEnd: formatDate(workEndTime, fullDateFormat)
  };
}

onUnmounted(() => {
  clearInterval();
  collectValues.value.length = 0;
  pointList.value.length = 0;
});

defineExpose({
  initCharts
});
</script>

<style scoped lang="scss"></style>
