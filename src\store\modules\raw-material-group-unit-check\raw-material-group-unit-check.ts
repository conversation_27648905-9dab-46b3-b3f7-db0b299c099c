import {
  addSelectRawMaterialCheck,
  delRawMatrialGroupUnitList,
  getAddRawMaterialCheckInfoByProcessId,
  getAddRawMaterilKinds,
  getDetailRawMatrialByMaterialId,
  getNonCableRawMaterialGroupUnitProcess,
  getNonCableRawMaterialGroupUnitList,
  getRawMaterialGroupUnitProcess,
  getRawMaterialGroupUnitList,
  getSelectRawMaterialList,
  saveAddRawMaterialInspectInfo,
  saveSelectRawMaterial,
  updatRawMaterialGroupUnit,
  productionMaterialProcessCopy
} from "@/api/production-test-sensing/raw-material-group-unit-check";
import { dateFormat } from "@/consts/date-format";
import { IResponse } from "@/models";
import {
  IChooseSaveRawMaterialInspect,
  IProductionMaterialProcess,
  IRawMaterialInspectList,
  IResMaterialProcessList,
  ISaveRawMaterialInspectReq,
  ISearchMaterialList
} from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import {
  EDiagType,
  ICopyFromOrder,
  IRawMaterialCheckCollectionItem,
  IRawMaterialReq,
  ISearchRawList
} from "@/models/raw-material/i-raw-material-res";
import { defineStore } from "pinia";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";

import { IRawMaterialList, ISearchRawMaterialListReq } from "@/models/raw-material/i-raw-material-res-v2";
import { getRawMaterialListByProcessCode } from "@/api/base-config/raw-material";
import { formatDate } from "@/utils/format";
import { ProductOrWorkOrderEnum } from "@/enums/purchase-order";

export const useRawMaterialGroupUnitStore = defineStore({
  id: "raw-material-group-unit",
  state: () => ({
    switchTabList: [] as Array<IProductionMaterialProcess>,
    currentTagInfo: {} as IProductionMaterialProcess,
    rawMaterialTableData: [] as Array<IResMaterialProcessList>,
    rawMaterialTableTotal: 0,
    queryRawMaterialParams: {},
    addRawMaterialVisibleRef: false,
    selectQueryParams: {} as ISearchRawList,
    selectRawMaterialTableData: [] as Array<IRawMaterialInspectList>,
    checkedRawMaterialData: [] as Array<IRawMaterialInspectList>,
    selectTotal: 0,
    selectBaseInfoValid: false,
    selectRawMaterialBaseInfo: {} as IRawMaterialReq,
    rawMaterialKindOptions: [],
    rawMaterialCheckCollectionItem: [] as Array<IRawMaterialCheckCollectionItem>,
    materialTestFromValid: false,
    selectRawMaterialTestInfo: {}, // 保存的动态表表单数据
    detailRawMaterialVisible: false,
    detailRawMaterialGroupUnit: {},
    detailRawMaterialCheckItem: [] as Array<IRawMaterialCheckCollectionItem>,
    isAdd: true, // 是否是新增原材料还是编辑原材料
    rawMaterialList: [] as Array<IRawMaterialList>,
    rawMaterialTotal: 0,
    selectRawMaterialList: {} as IRawMaterialList,
    collectionItemLoading: false // 采集项loading
  }),
  getters: {
    /** 根据工序判断是否可以 新增/编辑 */
    getIsCanOperateAddAndEdit() {
      return !this.currentTagInfo?.autoCollect;
    }
  },
  actions: {
    // 设置详情控制显示
    setDetailRawMaterialVisible(visible: boolean) {
      this.detailRawMaterialVisible = visible;
    },
    // 设置控制新增/编辑显示
    setEditRawMaterialVisible(visible: boolean, type?: string) {
      this.isAdd = type === EDiagType.Add;
      this.addRawMaterialVisibleRef = visible;
    },
    // 获取原材料，组部件下的工序数据
    async getRawMaterialGroupUnitProcessAction(queryData?: ISearchMaterialList, isOnlySale = false) {
      const dataId = isOnlySale ? useSalesFillInDataStore().dataId : useFillInDataStore().dataId;
      const isCable = isOnlySale ? useSalesOrderDetailStore().isCable : usePurchaseOrderDetailStore().isCable;
      this.switchTabList = isCable
        ? await getRawMaterialGroupUnitProcess(dataId).then(res => res.data)
        : await getNonCableRawMaterialGroupUnitProcess(dataId).then(res => res.data);
      this.getRawMaterialGroupUnitTable(queryData, isOnlySale);
    },
    // 获取当前原材料对应的表格数据
    async getRawMaterialGroupUnitTable(queryData?: ISearchMaterialList, isOnlySale = false) {
      Object.assign(this.queryRawMaterialParams, { ...queryData });
      const isCable = isOnlySale ? useSalesOrderDetailStore().isCable : usePurchaseOrderDetailStore().isCable;
      if (!this.queryRawMaterialParams.processCode) {
        this.queryRawMaterialParams.processCode = this.switchTabList[0]?.processCode;
      }
      if (isCable) {
        const tableRes = await getRawMaterialGroupUnitList({
          ...this.queryRawMaterialParams,
          productionId: isOnlySale ? useSalesFillInDataStore().dataId : useFillInDataStore().dataId
        });
        this.rawMaterialTableData = tableRes.data.list || [];
        this.rawMaterialTableTotal = tableRes.data.total || 0;
      } else {
        const tableRes = await getNonCableRawMaterialGroupUnitList({
          ...this.queryRawMaterialParams,
          workOrderId: isOnlySale ? useSalesFillInDataStore().dataId : useFillInDataStore().dataId
        });
        this.rawMaterialTableData = tableRes.data.list || [];
        this.rawMaterialTableTotal = tableRes.data.total || 0;
      }
    },

    // 获取选择原材料检表格数据
    async getRawMaterialOfSelectAction(queryParams?: ISearchRawList) {
      this.selectQueryParams = {
        ...this.selectQueryParams,
        ...queryParams
      };
      const selectMaterialList = await getSelectRawMaterialList(this.selectQueryParams);
      if (selectMaterialList?.data) {
        this.selectRawMaterialTableData = selectMaterialList.data.list;
        this.selectTotal = selectMaterialList.data.total;
      } else {
        this.selectRawMaterialTableData = [];
        this.selectTotal = 0;
      }
    },

    // 搜索选择原材料检表格的数据
    searchRawMaterialOfSelectAction(queryParams: ISearchRawList) {
      this.selectQueryParams = { ...queryParams };
      this.getRawMaterialOfSelectAction();
    },

    // 选择原材料页码改变
    querySelectRawMaterialByPage(pageNo: number, pageSize: number) {
      this.selectQueryParams.pageNo = pageNo;
      this.selectQueryParams.pageSize = pageSize;
      this.getRawMaterialOfSelectAction();
    },

    // 选择原材料页码数量改变
    querySelectRawMaterialByPageSize(pageNo: number, pageSize: number) {
      this.selectQueryParams.pageNo = pageNo;
      this.selectQueryParams.pageSize = pageSize;
      this.getRawMaterialOfSelectAction();
    },
    // 批量保存原材料数据
    async batchSaveRawMaterial(paramData: IChooseSaveRawMaterialInspect): Promise<IResponse<boolean>> {
      return await saveSelectRawMaterial(paramData);
    },

    // 获取原材料类型下拉数据
    async getRawMaterialKindOptions() {
      const kindRes = await getAddRawMaterilKinds();
      this.rawMaterialKindOptions = kindRes.data || [];
    },

    // 获取原材料检测的采集项信息
    async getRawMaterialCheckInfoByProductStePro(processIdStr?: string, specificationModel?: string) {
      if (this.isAdd) {
        const processId = processIdStr || this.selectRawMaterialBaseInfo.processId;
        this.collectionItemLoading = true;
        const res = await getAddRawMaterialCheckInfoByProcessId(processId, specificationModel);
        this.collectionItemLoading = false;
        if (res.data.length) {
          this.rawMaterialCheckCollectionItem = res.data;
        }
      }
    },

    // 保存新增的原材料数据
    async addRawMaterialCheck() {
      const baseInfo = this.selectRawMaterialBaseInfo;
      if (Object.keys(this.selectRawMaterialTestInfo).length) {
        const { submitData, uploadData, inspectForm } = this.selectRawMaterialTestInfo;
        const formData = submitData.map(item => {
          const { targetCode, targetValue, dataTypeIdentityDetail } = item;
          const { identityCode } = dataTypeIdentityDetail;
          return {
            dataCode: targetCode,
            dataValue: targetValue,
            identityCode
          };
        });
        const uploadFile = uploadData.map(item => {
          const { targetCode, fileList, dataTypeIdentityDetail } = item;
          const { identityCode } = dataTypeIdentityDetail;
          return {
            dataCode: targetCode,
            dataValue: JSON.stringify(fileList),
            identityCode
          };
        });
        const { processId } = this.currentTagInfo;
        const saveData = {
          ...baseInfo,
          ...inspectForm,
          processId,
          rawMetadataValue: [...formData, ...uploadFile]
        };
        if (this.isAdd) {
          // 新增的时候 保存基础原材料数据接口
          return await addSelectRawMaterialCheck(saveData);
        } else {
          // 编辑的时候 更新原材料、组部件数据
          return await updatRawMaterialGroupUnit(saveData);
        }
      }
    },

    // 获取原材料检数据的详情信息
    async getDetailOfRawMaterialGroupUnit(materialId: string, type: string) {
      const detailRes = await getDetailRawMatrialByMaterialId(materialId);
      if (detailRes?.data?.id) {
        this.detailRawMaterialGroupUnit = detailRes.data;
        this.detailRawMaterialGroupUnit.inspectDate = formatDate(detailRes.data?.inspectDate, dateFormat);
        const { rawMetadataValue } = detailRes.data;
        // 编辑详情
        if (type === EDiagType.Edit) {
          this.selectRawMaterialBaseInfo = detailRes.data || null;
          this.rawMaterialCheckCollectionItem = rawMetadataValue || [];
        }
        // 查看详情
        if (type === EDiagType.Detail) {
          const detailBaseInfo = detailRes.data;
          detailBaseInfo.inspectDate = formatDate(detailBaseInfo.inspectDate, dateFormat);
          detailBaseInfo.productionDate = formatDate(detailBaseInfo.productionDate, dateFormat);
          this.detailRawMaterialBaseInfo = detailRes.data || null;
          this.detailRawMaterialCheckItem = rawMetadataValue || [];
        }
      }
    },

    // 删除原材料数据
    async delRawMaterialByIdAction(materialId: string): Promise<IResponse<number>> {
      return await delRawMatrialGroupUnitList(materialId);
    },

    /** ========== v2新增 ============== */
    /** 新增原材料检测时，获取选择原材料列表 v2 新增 */
    async getConfigOfRawMaterialList(params: ISearchRawMaterialListReq) {
      const res = await getRawMaterialListByProcessCode(params);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.rawMaterialList = res.data.list;
        this.rawMaterialTotal = res.data.total;
      } else {
        this.rawMaterialList = res.data.list;
        this.rawMaterialTotal = res.data.total;
      }
    },

    /**
     * 保存新增的原材料检测数据
     * isOnlySale true 销售订单进入 false 采购订单进入
     */
    async saveAddRawMaterialInspect(
      params: ISaveRawMaterialInspectReq,
      isOnlySale = false
    ): Promise<IResponse<boolean>> {
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      const purchaseOrderId = usePurchaseOrderDetailStore().purchaseOrderId;
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const { productionId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const paramsData = {
        ...params,
        purchaseId: purchaseOrderId,
        productionId: isCable ? dataId : productionId,
        workOrderId: !isCable ? dataId : null
      };
      return await saveAddRawMaterialInspectInfo({ ...paramsData });
    },
    /** ========== v2新增 ============== */

    /** 从订单中复制原材料检 */
    async copyRawMaterialFromOrder(paramData: ICopyFromOrder, isOnlySale = false) {
      const isCable = isOnlySale ? useSalesOrderDetailStore().isCable : usePurchaseOrderDetailStore().isCable;
      const orderType = isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
      return await productionMaterialProcessCopy({ ...paramData, orderType });
    },

    /** 初始化数据 */
    initStoreData() {
      this.materialTestFromValid = false;
      this.selectBaseInfoValid = false;
      this.selectRawMaterialBaseInfo = {};
      this.selectRawMaterialTestInfo = {};
      this.rawMaterialCheckCollectionItem = [];
    },

    /**
     * 初始化采集项数据
     */
    initStoreCollectionItem() {
      this.rawMaterialCheckCollectionItem = [];
    }
  }
});
