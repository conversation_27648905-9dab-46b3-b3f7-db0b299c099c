import {
  IDeliveryOrderListData,
  IDeliveryOrderReqParams,
  IDeliveryOrderDetailListData,
  IDeliveryOrderDetailReqParams
} from "@/models";
import { useDeliveryOrderStore } from "@/store/modules";

/**
 * 供货单信息的hook
 */

export const useDeliveryOrderHook = () => {
  const deliveryOrderStore = useDeliveryOrderStore();

  // 获取供货单信息的列表数据
  async function queryDeliveryOrderDataList(params: IDeliveryOrderReqParams) {
    const tableData: Array<IDeliveryOrderListData> = await deliveryOrderStore.queryDeliveryOrderDataList(params);
    return tableData;
  }

  // 新增供货单信息
  async function createDeliveryOrder(params: IDeliveryOrderListData) {
    await deliveryOrderStore.createDeliveryOrder(params);
  }

  // 编辑供货单信息
  async function editDeliveryOrder(params: IDeliveryOrderListData) {
    await deliveryOrderStore.editDeliveryOrder(params);
  }

  // 删除供货单信息
  async function deleteDeliveryOrder(id: string) {
    await deliveryOrderStore.deleteDeliveryOrder(id);
  }
  // 根据Id获取供货单信息详情
  async function getDeliveryOrderDetailById(id: string) {
    const deliveryOrderDetail: IDeliveryOrderListData = await deliveryOrderStore.getDeliveryOrderDetailById(id);
    return deliveryOrderDetail;
  }

  //给选中的供货单详情赋值
  function setDeliveryOrderStorage(deliveryOrderDetail: IDeliveryOrderListData) {
    deliveryOrderStore.setDeliveryOrderStorage(deliveryOrderDetail);
  }

  //清空选中的供货单详情
  function clearDeliveryOrderStorage() {
    deliveryOrderStore.clearDeliveryOrderStorage();
  }

  // 根据Id获取供货单明细信息详情
  async function queryDeliveryOrderDetailDataList(deliveryOrderDetailReqParams: IDeliveryOrderDetailReqParams) {
    const tableData: Array<IDeliveryOrderDetailListData> = await deliveryOrderStore.queryDeliveryOrderDetailDataList(
      deliveryOrderDetailReqParams
    );
    return tableData;
  }

  // 新增供货单明细
  async function createDeliveryOrderDetail(deliveryOrderDetailReqParams: IDeliveryOrderDetailListData) {
    await deliveryOrderStore.createDeliveryOrderDetail(deliveryOrderDetailReqParams);
  }

  // 编辑供货单明细
  async function editDeliveryOrderDetail(deliveryOrderDetailReqParams: IDeliveryOrderDetailListData) {
    await deliveryOrderStore.editDeliveryOrderDetail(deliveryOrderDetailReqParams);
  }

  // 查询供货单明细根据ID
  async function queryDeliveryOrderDetailDataListById(id: string) {
    const deliveryOrderDetail: IDeliveryOrderDetailListData =
      await deliveryOrderStore.queryDeliveryOrderDetailDataListById(id);
    return deliveryOrderDetail;
  }

  // 删除供货单明细
  async function deleteDeliveryOrderDetailById(id: string) {
    await deliveryOrderStore.deleteDeliveryOrderDetailById(id);
  }

  //给选中的供货单明细详情赋值
  function setDeliveryOrderDetailStorage(deliveryOrderDetail: IDeliveryOrderDetailListData) {
    deliveryOrderStore.setDeliveryOrderDetailStorage(deliveryOrderDetail);
  }

  //清空选中的供货单明细详情
  function clearDeliveryOrderDetailStorage() {
    deliveryOrderStore.clearDeliveryOrderDetailStorage();
  }

  return {
    queryDeliveryOrderDataList,
    getDeliveryOrderDetailById,
    createDeliveryOrder,
    editDeliveryOrder,
    deleteDeliveryOrder,
    setDeliveryOrderStorage,
    clearDeliveryOrderStorage,
    setDeliveryOrderDetailStorage,
    clearDeliveryOrderDetailStorage,
    queryDeliveryOrderDetailDataList,
    createDeliveryOrderDetail,
    editDeliveryOrderDetail,
    queryDeliveryOrderDetailDataListById,
    deleteDeliveryOrderDetailById
  };
};
