import { defineStore } from "pinia";
import { IIOTStateGridOrderSync, IOrderSyncChannelReq, IStateGridOrderSync } from "@/models";
import { PurchaseChannel, StateGridOrderSyncStep } from "@/enums";
import { useSalesStateGridOrderSyncListStore } from "@/store/modules";
import * as api from "@/api/state-grid-order-sync";
import * as orderSyncApi from "@/api/order-sync";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";

type SalesOrderSyncDetailType = {
  sync: IStateGridOrderSync | null;
  dialogVisible: boolean;
  activeStepKey: StateGridOrderSyncStep;
  title: string;
  purchaseOrderId?: string;
  salesOrderId?: string;
  activeDetailType?: string;
  salesLineId: string;
  // 当前渠道
  channel: PurchaseChannel;
  subClassCode: string;
};

/**
 * @description: 销售订单同步信息（当前销售订单同步界面 显示哪个平台，显示哪个界面的相关信息）
 */
export const useSalesOrderSyncInfo = defineStore({
  id: "sales-order-sync-info",
  state: (): SalesOrderSyncDetailType => ({
    sync: null,
    dialogVisible: false,
    activeStepKey: null,
    title: "",
    purchaseOrderId: "",
    activeDetailType: "",
    subClassCode: "",
    salesLineId: "",
    channel: undefined
  }),
  getters: {
    /** 是否是国网渠道 */
    isEipChannel() {
      return this.channel === PurchaseChannel.EIP;
    },
    /** 是否是广州供电局渠道 */
    isGuangzhouChannel() {
      return this.channel === PurchaseChannel.CSG_GuangZhou;
    },
    /** 是否是上海平台渠道 */
    isShanghaiIotChannel() {
      return this.channel === PurchaseChannel.IOT;
    }
  },
  actions: {
    openDialog(sync: IStateGridOrderSync) {
      this.setSyncData(sync);
      this.dialogVisible = true;
      this.title = `【国网平台】采购订单号：${sync.poNo} \u00a0 采购订单行项目号：${sync.poItemNo}`;
    },
    toggleActiveStep(step: StateGridOrderSyncStep) {
      this.activeStepKey = step;
    },
    async refreshSyncDetail() {
      if (
        this.activeDetailType === SyncOrderTabEnum.SYNC_SHANGHAI_IOT ||
        this.activeDetailType === SyncOrderTabEnum.SYNC_CSG_GUANGZHOU
      ) {
        this.getIotOrCsgGuangzhouSalesOrderLineDetail();
      } else {
        const data = (await api.getStateGridSyncDetail(this.sync.id)).data;
        this.sync.detail = data;
        useSalesStateGridOrderSyncListStore().updateStateGridOrderSyncDetail(this.sync.id, data);
      }
    },
    /**
     * @description: 获取iot平台或者csg平台的销售订单行详情数据
     */
    async getIotOrCsgGuangzhouSalesOrderLineDetail() {
      const data = (await api.getIotOrCsgGuangzhouSalesOrderLineDetail(this.salesLineId, this.channel)).data;
      if (!this.sync) {
        this.sync = {};
      }
      this.sync.detail = data;
    },
    openIotDialog(sync: IIOTStateGridOrderSync) {
      this.setSyncIotData(sync);
      this.dialogVisible = true;
      this.title = `【上海平台】销售订单号：${sync.soNo} \u00a0 销售订单行项目号：${sync.soItemNo}`;
    },
    /**
     * @description: 设置当前渠道
     */
    setChannel(channel: PurchaseChannel) {
      this.channel = channel;
    },
    setSyncData(sync: IStateGridOrderSync) {
      this.sync = sync;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.purchaseOrderId = sync.purchaseId;
    },
    setSyncIotData(sync: IStateGridOrderSync) {
      this.sync = sync;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.salesLineId = sync.id;
    },
    setSyncCsgGuangzhouData(sync: IStateGridOrderSync) {
      this.sync = sync;
      this.activeStepKey = StateGridOrderSyncStep.SALES_LINE;
      this.salesLineId = sync.id;
    },
    // 设置当前详情进入key
    setSyncDetailType(key: string) {
      this.activeDetailType = key;
    },
    openCsgGuangzhouDialog(sync: IIOTStateGridOrderSync) {
      this.setSyncCsgGuangzhouData(sync);
      this.dialogVisible = true;
      this.title = `【广州供电局】销售订单号：${sync.soNo} \u00a0 销售订单行项目号：${sync.soItemNo}`;
    },

    /** 根据授权，租户IOT EIP配置，当前物资种类 查询 订单同步渠道 */
    async getOrderSyncChannel(data: IOrderSyncChannelReq) {
      return (await orderSyncApi.getOrderSyncChannel(data)).data;
    }
  }
});
