<!-- 生产试验感知 -->
<template>
  <div class="production-test-sensing">
    <div class="sensing-steps">
      <template v-for="(step, index) in sensingTestProcess" :key="step.key">
        <div class="sensing-step" :class="{ active: currentIndex === index }" @click="stepChange(step, index)">
          <div class="info">
            <div class="title">{{ step.title }}</div>
            <div class="content">
              <div class="message mr-2">{{ step.message }}</div>
              <div class="status-icon" :class="{ success: step.hasData }">
                <el-tooltip effect="dark" :content="step.tip" placement="top" v-if="!step.hasData">
                  <el-icon size="18">
                    <WarningFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </div>
          <div class="step-img">
            <span class="font font-barlow-semibold">0{{ index + 1 }}</span>
          </div>
        </div>
      </template>
    </div>
    <div class="flex">
      <div class="sensing-content min-h-[350px] w-full">
        <component :is="currentComponent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, shallowRef, watch, provide, onUnmounted } from "vue";
import RawMaterialGroupUnitCheck from "./raw-material-group-unit-check/index.vue";
import ProductionProcessInspection from "./production-process-inspection/index.vue";
import PredeliveryTest from "./predelivery-test/index.vue";
import FinishingWarehousing from "./finishing-warehousing/index.vue";
import { TEST_SENSING_STEP_LIST } from "./types";
import { IPerceptionProcessStatus } from "@/models/production-test-sensing/i-common";
import { WarningFilled } from "@element-plus/icons-vue";
import { useSalesFillInDataStore } from "@/store/modules";
import { useSalesOrderDetailStore } from "@/store/modules";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing/sales-production-test-sensing";
import { ETestSensingType } from "@/enums";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { emitter } from "@/utils/mitt";

defineOptions({
  name: "sensing-test"
});

// 生产试验感知数据
const sensingTestProcess = ref(TEST_SENSING_STEP_LIST);
const currentIndex = ref(0);
const processStatus = computed(() => productionTestSensingStore.productionProcessStatus);

const fillInDataStore = useSalesFillInDataStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();

// 获取当前的物资种类
const materialCategory = computed(() => useSalesOrderDetailStore().detailMaterialCategory);
const isArmourClamp = computed(() => fillInDataStore.isArmourClamp);
provide(PROVIDE_PROCESS_INSPECT_TOKEN, {
  detailMaterialCategory: materialCategory.value,
  isArmourClamp: isArmourClamp.value,
  stepKey: computed(() => {
    return sensingTestProcess.value[currentIndex.value]?.key;
  })
});

onMounted(() => {
  watch(
    () => fillInDataStore.dataId,
    dataId => {
      productionTestSensingStore.setRefreshParams(dataId);
      productionTestSensingStore.refreshProductionProcessStatus();
    },
    { immediate: true }
  );
});

// 监听卡片数据状态变化
watch(processStatus, newValue => {
  if (!Object.keys(newValue)?.length) return;
  patchSensingProcess(newValue);
});

// 更新步骤的数据状态
const patchSensingProcess = (processStatus: IPerceptionProcessStatus) => {
  sensingTestProcess.value.forEach(process => {
    if (process.key === ETestSensingType.ProductionProcessInspection) {
      const { processData, processInfo } = processStatus || {};
      let tip = "缺少:";
      if (!processData) {
        tip = `${tip}自动采集 `;
      }
      if (!processInfo) {
        tip = `${tip}过程检`;
      }
      process.tip = tip;
      process.hasData = processData && processInfo;
    } else {
      process.hasData = processStatus[process.key];
    }
  });
};

// 切换不同的步骤
const currentComponent = shallowRef();
currentComponent.value = RawMaterialGroupUnitCheck;

const stepChange = (step: { key: ETestSensingType }, index: number) => {
  // 切换步骤
  currentIndex.value = index;
  switch (step.key) {
    case ETestSensingType.RawMaterialGroupUnitCheck:
      setCurrentComponent(RawMaterialGroupUnitCheck);
      break;
    case ETestSensingType.ProductionProcessInspection:
      setCurrentComponent(ProductionProcessInspection);
      break;
    case ETestSensingType.ExFactoryExperiment:
      setCurrentComponent(PredeliveryTest);
      break;
    case ETestSensingType.FinishingWarehousing:
      setCurrentComponent(FinishingWarehousing);
      break;
    default:
      break;
  }
};

/**
 * 当前组件
 */
const setCurrentComponent = comp => {
  currentComponent.value = comp;
};

onMounted(() => {
  emitter.on("dataIntegrity", ({ index, key }) => {
    console.log(index, key);
    stepChange({ key }, index);
  });
});

onUnmounted(() => {
  emitter.off("dataIntegrity");
});
</script>

<style scoped lang="scss">
.sensing-steps {
  @apply flex gap-2 mb-5;
}

.sensing-step {
  @apply flex-bc flex-1 px-5 py-3 bg-bg_color cursor-pointer border-transparent border;
  box-shadow: 1px 1px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 3px;

  .info {
    .title {
      @apply text-secondary text-base;
    }

    .content {
      @apply flex text-lg;
      align-items: center;

      .status-icon {
        @apply flex;

        :deep(.el-icon) {
          color: var(--el-color-warning);
        }

        &.success {
          :deep(.el-icon) {
            color: var(--el-color-success);
          }
        }
      }
    }
  }

  .step-img {
    color: #e4ebed;
    font-size: 48px;
    line-height: 1;
  }

  &:hover {
    border-color: var(--el-color-primary-light-5);
    // background: linear-gradient(0deg, var(--el-color-primary-light-8) 0%, var(--el-border-color-lighter) 79%);

    // .title {
    //   @apply text-primary;
    // }

    // .content {
    //   @apply text-primary;
    // }
  }
}

.sensing-step.active {
  border-color: var(--el-color-primary-light-5);
  background: var(--el-color-primary-light-9);

  .title {
    color: var(--el-color-primary-light-3);
  }

  .content {
    color: var(--el-color-primary);
  }

  .step-img {
    .font {
      display: inline-block;
      background: linear-gradient(180deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary-light-7) 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
}
</style>
