import { IDictionaryOption } from "../platform";

export interface ISchedulingPlan {
  id: string;
  /** 排产计划编码 */
  ppNo: string;
  /** 销售订单号 */
  soNo: string;
  /** 销售订单行项目 */
  soItemNo: string;
  /** 物料名称 */
  materialName?: string;
  /** 排产数量 */
  amount: number;

  /** 计量单位 */
  measUnit: string;
  /** 计划日期 */
  planDate: [Date, Date];
  /** 计划工期 */
  planWorkDuration: number;
  /** 交货日期 */
  deliveryDate: Date;
  actStartDate: Date;
  actEndDate: Date;

  /** 销售订单行Id */
  salesLineId: string;

  /** 采购订单Id */
  purchaseId: string;
  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;
  /** 排产进度 */
  schedule?: number;

  /** 物资种类 */
  subClassCode?: string;
}
