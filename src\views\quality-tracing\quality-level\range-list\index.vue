<template>
  <div class="w-[1000px] bg-white p-4 flex flex-col">
    <div class="flex justify-end mb-3 h-8">
      <el-button v-if="isEdit" type="primary" :icon="Plus" @click="handleAddRawRecord"> 新增 </el-button>
    </div>
    <PureTable row-key="id" :data="list" :columns="columnsConfig" height="500">
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
      <template #range="{ row }">
        <input-formual :disabled="!isEdit" v-model="row.range" />
      </template>
      <template #level="{ row, $index }">
        <el-input :disabled="!isEdit" clearable v-model="row.level" @input="handleInput($event, $index)" />
      </template>
      <template #opertion="{ $index }">
        <el-button v-if="isEdit" type="danger" link @click="handleDelete($index)"> 删除 </el-button>
      </template>
    </PureTable>
    <div>
      <div v-if="showEmptyTip && list.length === 0" class="text-danger">请添加质量等级</div>
    </div>
    <div v-if="emptyFormualList.length > 0">
      <div class="text-danger">第{{ emptyFormualList.map(index => index + 1).join("、") }}条等级数值范围存在空缺</div>
    </div>
    <div v-if="errorFormulaComparisionList.length > 0">
      <div class="text-danger">
        第{{ errorFormulaComparisionList.map(index => index + 1).join("、") }}条等级存在数值范围比较关系错误
      </div>
    </div>
    <div v-if="emptyLevelList.length > 0">
      <div class="text-danger">第{{ emptyLevelList.map(index => index + 1).join("、") }}条等级的质量等级名称未填</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRaw, watch } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import InputFormual from "@/components/input-formual/index.vue";
import { ComparisonSymbolEnum, InputFormualModelVal } from "@/components/input-formual/types";
import { genQualityLevelColumnsConfig } from "./column-config";

/**
 * 质量等级公式列表配置表单
 */

const props = withDefaults(
  defineProps<{
    /** 模式 */
    mode?: "browse" | "edit";
  }>(),
  {
    mode: "browse"
  }
);

const { columnsConfig } = genQualityLevelColumnsConfig();

const list = ref<
  Array<{
    /** 判断公式 */
    range: InputFormualModelVal;
    /** 等级 */
    level: string;
    id: string;
  }>
>([]);

const showEmptyTip = ref(false);
/** 空缺公式列表 */
const emptyFormualList = ref<Array<number>>([]);
/** 空缺等级列表 */
const emptyLevelList = ref<Array<number>>([]);
/** 公式比较关系错误列表 */
const errorFormulaComparisionList = ref<Array<number>>([]);

const isEdit = computed(() => {
  return props.mode === "edit";
});

const maxLength = 20;
const midValue = "s";

/**
 * @description: 输入时矫正长度
 */
function handleInput(s: string, index: number) {
  if (s.length > maxLength) {
    list.value[index]["level"] = s.slice(0, maxLength);
  }
}

/**
 * @description: 添加一条记录
 */
function handleAddRawRecord() {
  list.value.push({
    range: {
      leftValue: "",
      leftSymbol: ComparisonSymbolEnum.LessThanValue as number,
      midValue: midValue,
      rightSymbol: ComparisonSymbolEnum.LessThanValue as number,
      rightValue: ""
    },
    level: "",
    id: ""
  });
}

/**
 * @description: 删除记录
 */
function handleDelete(index: number) {
  list.value.splice(index, 1);
}

/**
 * @description: 检查公式比较关系
 */
function handleCheckFormulaComparision() {
  errorFormulaComparisionList.value = [];
  list.value.forEach((item, index) => {
    if (
      (item.range.leftSymbol === ComparisonSymbolEnum.LessThanValue ||
        item.range.rightSymbol === ComparisonSymbolEnum.LessThanValue) &&
      +item.range.leftValue >= +item.range.rightValue
    ) {
      errorFormulaComparisionList.value.push(index);
    }
  });
}

/**
 * @description: 校验分数
 */
function handleCheckLevel() {
  emptyLevelList.value = [];
  list.value.forEach((item, index) => {
    if (!item.level) {
      emptyLevelList.value.push(index);
    }
  });
}

/**
 * @description: 校验空缺公式
 */
function handleCheckFormula() {
  emptyFormualList.value = [];
  list.value.forEach((item, index) => {
    const result = item.range.leftValue && item.range.midValue && item.range.rightValue;
    if (!result) {
      emptyFormualList.value.push(index);
    }
  });
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (list.value.length === 0) {
    showEmptyTip.value = true;
    return false;
  } else {
    showEmptyTip.value = false;
  }
  // 校验空缺公式
  handleCheckFormula();
  handleCheckLevel();
  handleCheckFormulaComparision();
  return (
    emptyFormualList.value.length === 0 &&
    emptyLevelList.value.length === 0 &&
    errorFormulaComparisionList.value.length === 0
  );
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: any) {
  list.value = v;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return toRaw(list.value);
}

watch(
  list,
  () => {
    // 如果当前处于提交校验阶段，针对每一次的改变，进行校验
    if (
      emptyFormualList.value.length > 0 ||
      emptyLevelList.value.length > 0 ||
      errorFormulaComparisionList.value.length > 0
    ) {
      handleCheckFormula();
      handleCheckLevel();
      handleCheckFormulaComparision();
    }
  },
  {
    deep: true
  }
);

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style lang="scss" scoped>
:deep(.el-input__inner) {
  text-align: center;
}

:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
