<template>
  <el-dialog
    title="编辑销售订单行"
    v-model="editVisible"
    align-center
    class="default"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <SalesOrderLineForm ref="editFormRef" type="update" :category-code="categoryCode" v-loading="loading" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync">
        保存，并重新同步
      </el-button>
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SalesOrderLineForm from "@/views/order/purchase-order/detail/src/link-sales-order/src/sales-order-line/sales-order-line-form.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ISalesOrderLineForm, ISalesOrderSync, IUpdateSalesOrderLineDto } from "@/models";
import { ref } from "vue";
import { getSalesOrderLineById, updateSalesOrderLine } from "@/api";

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof SalesOrderLineForm>>(editSalesOrderLine);
const { loading, handlePatchValue } = usePatchValue(patchValue);
const categoryCode = ref<string>("");

function openEditDialog(data: ISalesOrderSync) {
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.soItemNo
  };
  handlePatchValue(data.dataId);
}

async function patchValue(dataId: string) {
  const salesOrderLine = await getSalesOrderLineById(dataId).then(res => res.data);
  categoryCode.value = salesOrderLine.categoryCode;
  editFormRef.value.initializeForm(salesOrderLine);
}

async function editSalesOrderLine() {
  const line: ISalesOrderLineForm = await editFormRef.value.getValue();
  const {
    id,
    materialNumber,
    specificationType,
    voltageLevel,
    materialCode,
    materialName,
    materialUnit,
    productionWorkNo,
    prjCode,
    entityId
  } = line;
  const data: IUpdateSalesOrderLineDto = {
    id,
    materialNumber,
    specificationType,
    voltageLevel,
    materialCode,
    materialName,
    materialUnit,
    productionWorkNo,
    prjCode,
    entityId
  };
  await updateSalesOrderLine(data);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
