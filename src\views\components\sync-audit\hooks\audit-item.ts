import * as itemIds from "../consts";
import { ISyncAuditItem } from "../tokens";
import { Component } from "vue";
import BaseInfo from "../base-info.vue";

import ProductionOrder from "../business-lists/production-order/production-order.vue";
import ProductionPlan from "../business-lists/production-plan/production-plan.vue";
import WorkOrder from "../business-lists/work-order/work-order.vue";
import WorkReport from "../business-lists/work-report/work-report.vue";
import SalesOrderLine from "../business-lists/sales-order-line/sales-order-line.vue";

import RawMaterial from "../iot-lists/raw-material/raw-material.vue";
import ProductionFlow from "../iot-lists/production-flow/production-flow.vue";
import Experiment from "../iot-lists/experiment/experiment.vue";
import FinishProduct from "../iot-lists/finish-product/finish-product.vue";
import TechnicalStandard from "../iot-lists/technical-standard/technical-standard.vue";
import ProcessDocument from "../iot-lists/process-document/process-document.vue";
import AutoCollectionInProcess from "../iot-lists/auto-collection-in-process/auto-collection-in-process.vue";

export function useAuditItem() {
  function getTitleById(id: string): string {
    switch (id) {
      case itemIds.BASE_INFO_ID:
        return "基础信息";
      case itemIds.SALES_ORDER_LINE_ID:
        return "销售订单行";
      case itemIds.PRODUCTION_PLAN_ID:
        return "排产计划";
      case itemIds.PRODUCTION_ORDER_ID:
        return "生产订单";
      case itemIds.WORK_ORDER_ID:
        return "工单";
      case itemIds.WORK_REPORT_ID:
        return "报工";
      case itemIds.RAW_MATERIAL_ID:
        return "原材料、组部件检验";
      case itemIds.INSPECTION_DATA_IN_PROCESS:
        return "过程检验数据";
      case itemIds.AUTO_COLLECTION_IN_PROCESS:
        return "生产过程自动采集项";
      case itemIds.EXPERIMENT_ID:
        return "出厂试验";
      case itemIds.FINISH_PRODUCT_ID:
        return "成品入库";
      case itemIds.TECHNICAL_STANDARD_ID:
        return "技术标准";
      case itemIds.PROCESS_DOCUMENT_ID:
        return "工序文档";
      default:
        return "";
    }
  }

  /** Object.preventExtensions: prevent component reactive */
  function getComponentById(id: string): Component {
    switch (id) {
      case itemIds.BASE_INFO_ID:
        return Object.preventExtensions(BaseInfo);
      case itemIds.SALES_ORDER_LINE_ID:
        return Object.preventExtensions(SalesOrderLine);
      case itemIds.PRODUCTION_PLAN_ID:
        return Object.preventExtensions(ProductionPlan);
      case itemIds.PRODUCTION_ORDER_ID:
        return Object.preventExtensions(ProductionOrder);
      case itemIds.WORK_ORDER_ID:
        return Object.preventExtensions(WorkOrder);
      case itemIds.WORK_REPORT_ID:
        return Object.preventExtensions(WorkReport);
      case itemIds.RAW_MATERIAL_ID:
        return Object.preventExtensions(RawMaterial);
      case itemIds.INSPECTION_DATA_IN_PROCESS:
        return Object.preventExtensions(ProductionFlow);
      case itemIds.AUTO_COLLECTION_IN_PROCESS:
        return Object.preventExtensions(AutoCollectionInProcess);
      case itemIds.EXPERIMENT_ID:
        return Object.preventExtensions(Experiment);
      case itemIds.FINISH_PRODUCT_ID:
        return Object.preventExtensions(FinishProduct);
      case itemIds.TECHNICAL_STANDARD_ID:
        return Object.preventExtensions(TechnicalStandard);
      case itemIds.PROCESS_DOCUMENT_ID:
        return Object.preventExtensions(ProcessDocument);
      default:
        return null;
    }
  }

  function generateItem(id: string): ISyncAuditItem {
    return {
      id,
      title: getTitleById(id),
      component: getComponentById(id)
    };
  }

  return {
    generateItem,
    getTitleById,
    getComponentById
  };
}
