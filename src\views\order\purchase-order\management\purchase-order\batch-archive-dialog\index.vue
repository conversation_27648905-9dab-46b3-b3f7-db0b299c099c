<template>
  <div class="inline-block">
    <slot name="trigger" :openDialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      title="归档备注"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <div class="px-4">
        <el-alert title="归档后的数据将不会自动同步和触发评分" type="warning" show-icon :closable="false" />
      </div>
      <batch-archive-form ref="batchArchiveFormRef" class="mt-4" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickArchiveBtn" :loading="loading">确认归档</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import BatchArchiveForm from "./batch-archive-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { BatchArchiveParams } from "@/models/batch-archive";
import { batchArchivePurchaseOrder } from "@/api/purchase-order";

const props = defineProps<{
  selectedList: Array<string>;
}>();

const emits = defineEmits(["postArchiveSuccess"]);

const batchArchiveFormRef = ref<InstanceType<typeof BatchArchiveForm>>();
const loading = ref(false);
const dialogVisible = ref(false);

const requestBatchArchive = useLoadingFn(async (params: BatchArchiveParams) => {
  return await batchArchivePurchaseOrder(params);
}, loading);

/**
 * @description: 按钮点击事件
 */
const handleClickArchiveBtn = async () => {
  const validateResult = await batchArchiveFormRef.value?.validateForm();
  if (!validateResult) return;
  const formValue = batchArchiveFormRef.value.getFormValue();
  const params = {
    ids: props.selectedList,
    remark: formValue.remark,
    documentation: true
  };
  const { data } = await requestBatchArchive(params);
  emits("postArchiveSuccess");
  if (!data) {
    return;
  }
  closeDialog();
  ElMessage({
    message: "归档成功",
    type: "success"
  });
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
