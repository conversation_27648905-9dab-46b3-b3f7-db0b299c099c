<template>
  <pure-table
    ref="tableInstance"
    show-overflow-tooltip
    :columns="columns"
    :data="store.lines"
    :height="props.height"
    row-key="id"
    @row-click="handleRowClick"
    @selection-change="handleSelectionChange"
    :class="{ 'disabled-check-all': checkAllDisabled }"
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <emptyData /> </template>
      </el-empty>
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import { computed, h, ref, watch } from "vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import { SingleSelectCell } from "@/components/TableCells/SingleSelectCell";
import { IPurchaseOrderLine } from "@/models/purchase-order/i-purchase-order-line";
import { ISalesOrderLine } from "@/models";
import { usePurchaseOrderDetailPurchaseOrderLineStore } from "@/store/modules/purchase-order-detail";
import { TableWidth } from "@/enums";
import emptyData from "@/assets/svg/empty_data.svg?component";
import CxTag from "@/components/CxTag/index.vue";

const props = defineProps(["modelValue", "multiple", "height"]);
const emit = defineEmits(["update:modelValue"]);

const selected = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const store = usePurchaseOrderDetailPurchaseOrderLineStore();
const tableInstance = ref<PureTableInstance>();
const normalColumns: Array<TableColumns> = [
  {
    label: "采购订单行项目号",
    prop: "poItemNo",
    minWidth: TableWidth.order
  },
  {
    label: "物料编码",
    prop: "materialCode",
    width: TableWidth.order
  },
  {
    label: "物资种类",
    prop: "subClassName",
    minWidth: TableWidth.name
  },
  {
    label: "物料描述",
    prop: "materialDesc",
    minWidth: TableWidth.name
  },
  {
    label: "采购数量",
    prop: "amount",
    width: TableWidth.unit,
    align: "center"
  },
  {
    label: "物资小类名称",
    prop: "matMinName",
    minWidth: TableWidth.name
  },
  {
    label: "关联销售行状态",
    prop: "linkedCount",
    width: TableWidth.type,
    fixed: "right",
    cellRenderer(data) {
      const linkedCount: boolean = data.row.linkedCount;
      if (linkedCount) {
        return h(CxTag, { type: "success" }, () => `已关联${linkedCount}条`);
      }
      return h(CxTag, { type: "warning" }, () => "未关联");
    }
  }
];

const columns = computed(() => {
  const selectColumn = props.multiple
    ? {
        width: 44,
        type: "selection",
        selectable: row => {
          const lines = store.lines;
          if (!selected.value.length || !lines.length) {
            return true;
          }
          const s = lines.find(line => line.id === selected.value[0]);
          if (s) {
            return s.subClassCode === row.subClassCode;
          }
          return false;
        }
      }
    : {
        label: "",
        prop: "id",
        showOverflowTooltip: false,
        width: 55,
        fixed: "left",
        cellRenderer: data => SingleSelectCell(selected, data)
      };
  return [selectColumn, ...normalColumns];
});
const elTableInstance = computed(() => tableInstance.value.getTableRef());

watch(
  () => [props.modelValue, props.multiple],
  ([selected, multiple]) => {
    if (multiple && (!selected || !selected.length)) {
      elTableInstance?.value.clearSelection();
    }
  }
);

const checkAllDisabled = computed(() => {
  const lines = store.lines;
  if (!lines.length) {
    return false;
  }
  // 如果所有采购行都是相同的物资种类则不禁用全选按钮
  const s = lines[0].subClassCode;
  return !lines.every(line => line.subClassCode === s);
});

function handleRowClick(line: IPurchaseOrderLine) {
  if (props.multiple) {
    if (line.subClassCode === selected.value[0] || !selected.value.length) {
      elTableInstance.value.toggleRowSelection(line, undefined);
    }
  } else {
    selected.value = line.id;
  }
}

function handleSelectionChange(lines: Array<ISalesOrderLine>) {
  selected.value = lines.map(line => line.id);
}

function getSelectedPurchaseLine() {
  return store.lines.filter(({ id }) => selected.value.includes(id));
}

defineExpose({
  getSelectedPurchaseLine
});
</script>

<style lang="scss" scoped>
.disabled-check-all {
  :deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
  }
}
</style>
