<template>
  <el-tooltip effect="dark" content="为没有质量规范的生产订/工单匹配质量规范" placement="bottom">
    <el-button
      v-auth="PermissionKey.qualityTracing.qualityTracingRecordBatchMatching"
      type="primary"
      @click="handleBatchMatching"
      :loading="loading"
      >一键匹配</el-button
    >
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { PermissionKey } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { batchMatchingQualitySpecification } from "@/api/quality-tracing";

/**
 * 一键匹配质量规范
 */

const loading = ref(false);

const requestMatching = useLoadingFn(async () => {
  const { data } = await batchMatchingQualitySpecification();
  return data;
}, loading);

/**
 * @description: 为所有数据库中未匹配质量规范的生产订单匹配质量规范
 */
async function handleBatchMatching() {
  const { productionNum, workOrderNum } = await requestMatching();

  ElMessage.success(`已将 ${productionNum} 条生产订单、 ${workOrderNum} 条生产工单加入后台匹配队列`);
}
</script>

<style scoped></style>
