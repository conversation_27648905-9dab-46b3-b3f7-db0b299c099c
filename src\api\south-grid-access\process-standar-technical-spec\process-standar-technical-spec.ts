import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IProcessStandarTechnicalSpec, IProcessStandarTechnicalSpecReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryProcessStandarTechnicalSpec = (params: IProcessStandarTechnicalSpecReq) => {
  const url: string = withApiGateway(
    `admin-api/business/process-standar-technical-spec/process-standar-technical-spec`
  );
  return http.get<IProcessStandarTechnicalSpecReq, IListResponse<IProcessStandarTechnicalSpec>>(url, {
    params
  });
};

/** 根据id 查询详情 */
export const getProcessStandarTechnicalSpecById = (id: string) => {
  const url: string = withApiGateway(
    `admin-api/business/process-standar-technical-spec/process-standar-technical-spec-detail-by-id/${id}`
  );
  return http.get<string, IResponse<IProcessStandarTechnicalSpec>>(url);
};

/** 删除根据Id */
export const deleteProcessStandarTechnicalSpecById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/process-standar-technical-spec/process-standar-technical-spec-by-id/${id}`)
  );
};
