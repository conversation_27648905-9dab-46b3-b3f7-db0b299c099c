import { ColumnWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { JudgeTypeEnumMap, JudgeTypeEnum } from "@/enums/quality-specification";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";
import { emptyDefaultValue } from "@/consts";

/**
 * @description: 生成质量追溯记录通用表格配置
 */
export function genQualityTracingRecordTableColumnsConfig(
  categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum
) {
  const columnsConfig: TableColumnList = [
    {
      label: "序号",
      prop: "index",
      minWidth: ColumnWidth.Char3,
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return <div>{data.$index + 1}</div>;
      }
    },
    {
      label: "检测类型",
      prop: "processName",
      width: ColumnWidth.Char7,
      sortable: "custom"
    },
    {
      label: "检测项",
      prop: "modelName",
      width: ColumnWidth.Char14
    },
    {
      label: "分值",
      prop: "score",
      width: ColumnWidth.Char5,
      sortable: "custom"
    },
    {
      label: "判定方式",
      prop: "judgeType",
      slot: "judgeType",
      minWidth: ColumnWidth.Char7,
      cellRenderer: (data: TableColumnRenderer) => {
        return <div>{JudgeTypeEnumMap[data.row.judgeType]}</div>;
      }
    },
    {
      label: "判定标准",
      prop: "judgeStandardStr",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "实测值",
      prop: "actualValue",
      minWidth: ColumnWidth.Char14,
      cellRenderer: (data: TableColumnRenderer) => {
        const list = data.row.actualValue as Array<number>;
        const judgeType = data.row.judgeType;
        if (Array.isArray(list) && list.length > 0) {
          // 如果是 达标率，补充百分号
          if (judgeType === JudgeTypeEnum.ReachStandardRate) {
            return <div>{list.map(item => `${item}%`).join(",")}</div>;
          }
          return <div>{list.join(",")}</div>;
        }
        return <div>{emptyDefaultValue}</div>;
      }
    },
    {
      label: "得分",
      prop: "actualScore",
      minWidth: ColumnWidth.Char5,
      fixed: "right",
      cellRenderer: (data: TableColumnRenderer) => {
        // 如果实际得分不是满分，标记为红色
        if (typeof data.row.actualScore === "number" && data.row.actualScore < data.row.score) {
          return <div class="text-danger">{data.row.actualScore}</div>;
        }
        return <div>{data.row.actualScore}</div>;
      }
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: ColumnWidth.Char5,
      fixed: "right"
    }
  ];

  // 如果是生产过程或者工艺稳定性稳定性，则添加设备列
  if (
    categoryCode === ProductionStageCodeToQualitySpecificationCategoryEnum.ProductiveProcess ||
    categoryCode === ProductionStageCodeToQualitySpecificationCategoryEnum.ProcessStability
  ) {
    columnsConfig.splice(3, 0, {
      label: "设备",
      prop: "deviceInfo",
      minWidth: ColumnWidth.Char14,
      cellRenderer: (data: TableColumnRenderer) => {
        const list = data.row.deviceInfo;
        if (Array.isArray(list) && list.length > 0) {
          return <div>{list.join("、")}</div>;
        }
        return <div>{emptyDefaultValue}</div>;
      }
    });
  }
  return { columnsConfig };
}
