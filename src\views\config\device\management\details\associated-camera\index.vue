<template>
  <div class="flex justify-end pb-4 bg-bg_color">
    <ElButton v-auth="PermissionKey.meta.metaDeviceCamera" type="primary" :icon="Plus" @click="onCameraAssociated()">
      <template #icon>
        <FontIcon icon="icon-plus" />
      </template>
      关联摄像头
    </ElButton>
  </div>
  <PureTable
    class="flex-1 overflow-hidden pagination tooltip-max-w"
    row-key="id"
    :data="associatedCameraStore.cameralist"
    :columns="columns"
    :pagination="pagination"
    showOverflowTooltip
    :loading="associatedCameraStore.loading"
    @page-size-change="onPageSizeChange()"
    @page-current-change="onCurrentPageChange()"
  >
    <template #operation="data">
      <div>
        <ElButton type="primary" link @click="onviewLiveStream(data.row)"> 查看直播 </ElButton>
        <ElButton v-auth="PermissionKey.meta.metaDeviceCamera" link type="danger" @click="onDelete(data.row.id)">
          移除
        </ElButton>
      </div>
    </template>
    <template #empty>
      <el-empty :image-size="120">
        <template #image>
          <EmptyData />
        </template>
      </el-empty>
    </template>
  </PureTable>

  <!-- 关联摄像头 -->
  <el-dialog
    v-model="state.cameraRelationVid"
    title="关联摄像头"
    class="middle"
    align-center
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    @close="onCloseCameraRelation()"
  >
    <CameraRelation ref="cameraRelationRef" @onSelectCameraIds="handleSelectCameraIds($event)" />
    <template #footer>
      <el-button @click="onCloseCameraRelation()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSaveCameraRelation()">保存</el-button>
    </template>
  </el-dialog>

  <el-dialog
    align-center
    destroy-on-close
    v-model="state.viewLiveStreamVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="flex">
        <span>{{ state.viewLiveStreamDeviceName }}</span>
        <el-tag class="mx-2" v-if="state.monitorStatus === MonitorDeviceStatusEnum.START">开启</el-tag>
        <el-tag class="mx-2" v-else type="danger">关闭</el-tag>
      </div>
    </template>
    <CXPlayer :url="state.viewLiveStreamUrl" :isLive="true" :volume="0" :autoplay="true" />
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, watchEffect } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { ElMessage } from "element-plus";
import CameraRelation from "./camera-relation.vue";
import { useConfirm } from "@/utils/useConfirm";
import { Plus } from "@element-plus/icons-vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useRoute } from "vue-router";
import { useAssociatedCameraStore } from "@/store/modules/device";
import { IMonitorDevice } from "@/models";
import { useMonitorDeviceStore } from "@/store/modules";
import CXPlayer from "@/components/cx-player";
import { MonitorDeviceStatusEnum } from "@/enums/monitor-device/monitor-device-status.enum";
import { PermissionKey } from "@/consts";

const route = useRoute();
const associatedCameraStore = useAssociatedCameraStore();
const monitorDeviceStore = useMonitorDeviceStore();

const deviceId: string = route.params.id as string;
let selectedCameraIds: Array<string> = [];

const state = reactive<{
  cameraRelationVid: boolean;
  monitorStatus: MonitorDeviceStatusEnum;
  viewLiveStreamVisible?: boolean;
  viewLiveStreamDeviceName?: string;
  viewLiveStreamUrl?: string;
}>({
  cameraRelationVid: false,
  // 摄像头状态：开启/关闭
  monitorStatus: MonitorDeviceStatusEnum.DISABLE
});

const { columns } = useColumns();
const { pagination } = useTableConfig();
const saveLoading = ref<boolean>(false);
const cameraRelationRef = ref<InstanceType<typeof CameraRelation>>();

watchEffect(() => {
  pagination.total = associatedCameraStore.total;
});
const onSaveCameraRelation = useLoadingFn(saveRelationCamera, saveLoading);
associatedCameraStore.queryCamera(queryParams());

const onviewLiveStream = async (data: IMonitorDevice) => {
  const res = await monitorDeviceStore.getMonitorDeviceWatchUrlById(data.id);
  state.viewLiveStreamDeviceName = data.name;
  state.viewLiveStreamUrl = res.data;
  state.viewLiveStreamVisible = true;
  state.monitorStatus = data.status;
};

// 关联摄像头
const onCameraAssociated = () => {
  state.cameraRelationVid = true;
};

const onCloseCameraRelation = () => {
  state.cameraRelationVid = false;
};

async function saveRelationCamera() {
  if (!Array.isArray(selectedCameraIds) || selectedCameraIds.length === 0) {
    ElMessage.warning("请选择关联摄像头");
    return;
  }

  await associatedCameraStore.deviceRelationCamera({
    monitorIdList: selectedCameraIds,
    deviceId
  });
  state.cameraRelationVid = false;
  ElMessage.success("关联成功");
  pagination.currentPage = 1;
  associatedCameraStore.queryCamera(queryParams());
}

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认移除后，摄像头失去关联", "确认移除"))) {
    return;
  }
  await associatedCameraStore.deleteAssociatedCamera(deviceId, id);
  ElMessage.success("移除成功");
  pagination.currentPage = 1;
  associatedCameraStore.queryCamera(queryParams());
};

const onPageSizeChange = () => {
  associatedCameraStore.queryCamera(queryParams());
};

const handleSelectCameraIds = (cameraIds: Array<string>) => {
  selectedCameraIds = cameraIds;
};

const onCurrentPageChange = () => {
  associatedCameraStore.queryCamera(queryParams());
};

function queryParams() {
  return { deviceId, relationFlag: true, pageNo: pagination.currentPage, pageSize: pagination.pageSize };
}
</script>
<style scoped lang="scss"></style>
