import * as api from "@/api";
import { ISalesOrderLineProductLinkParams } from "@/models";

/**
 * @description: 请求销售订单关联的销售订单行/请求采购订单关联的销售订单行
 */
export function querySalesOrderLines(params: ISalesOrderLineProductLinkParams) {
  trimKeyword(params);
  return api.queryProductToBeLinkSalesOrderLines(params);
}

function trimKeyword(params: ISalesOrderLineProductLinkParams) {
  if (params.keyWords) {
    params.keyWords = params.keyWords.trim();
  }
}
