import { InjectionKey } from "vue";
import { SaleOrderLineStateEnum } from "@/enums";
import { PaginationProps } from "@pureadmin/table";
import { ISalesOrderLineExt } from "@/models";
import { PropParams } from "./types";

export interface ISalesOrderLineSelectCtx {
  keyword: string;
  multiple: boolean;
  /** 是否跨销售订单 */
  crossOrder: boolean;
  pagination: PaginationProps;
  salesOrderLineStatus: SaleOrderLineStateEnum;
  selectedLines: Array<ISalesOrderLineExt>;
  propParams: PropParams;
  refreshLines?(): void;
  /** 启用跨销售订单按钮 **/
  requiredCrossOrderBtn: boolean;
}

export const salesOrderLineSelectKey: InjectionKey<ISalesOrderLineSelectCtx> = Symbol("sales order line select");
