import {
  ColumnWidth,
  CoolingMethodEnumMapDesc,
  ComponentBasicMapDesc,
  GisComponentBasicMapDesc,
  DomesticOrImportedEnumMapDesc
} from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { FileInfoModel } from "@/models";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 原材料基本信息
 */
export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig = [];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "原材料组部件类型",
        prop: "gisComponentType",
        width: ColumnWidth.Char12,
        cellRenderer: (data: TableColumnRenderer) => {
          return GisComponentBasicMapDesc[data.row.gisComponentType];
        }
      },
      {
        label: "供应商统一社会信用代码",
        prop: "supplierUscc",
        width: ColumnWidth.Char12
      },
      {
        label: "供应商全称",
        prop: "supplier",
        width: ColumnWidth.Char8
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        width: ColumnWidth.Char8
      },
      {
        label: "子实物ID(单元)",
        prop: "subPhysicalItemId",
        width: ColumnWidth.Char10
      },
      {
        label: "批次编号",
        prop: "batchNumber",
        width: ColumnWidth.Char15
      },
      {
        label: "钢印号/字头号",
        prop: "stampingNumber",
        width: ColumnWidth.Char8
      },
      {
        label: "同步状态",
        prop: "pullStatus",
        minWidth: ColumnWidth.Char10,
        slot: "pullStatus"
      },
      {
        label: "创建时间",
        prop: "createTime",
        width: ColumnWidth.Char11,
        formatter: dateFormatter()
      },
      {
        label: "操作",
        prop: "operation",
        fixed: "right",
        slot: "operation",
        width: ColumnWidth.Char11
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "原材料组部件类型",
        prop: "componentType",
        width: ColumnWidth.Char12,
        cellRenderer: (data: TableColumnRenderer) => {
          return ComponentBasicMapDesc[data.row.componentType];
        }
      },
      {
        label: "冷却方式",
        prop: "coolingMethod",
        width: ColumnWidth.Char12,
        cellRenderer: (data: TableColumnRenderer) => {
          return CoolingMethodEnumMapDesc[data.row.coolingMethod];
        }
      },
      {
        label: "供应商统一社会信用代码",
        prop: "supplierUscc",
        width: ColumnWidth.Char12
      },
      {
        label: "供应商全称",
        prop: "supplier",
        width: ColumnWidth.Char8
      },
      {
        label: "实物ID(原材料组部件)",
        prop: "utcNum",
        width: ColumnWidth.Char15
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        width: ColumnWidth.Char8
      },
      {
        label: "批次编号",
        prop: "batchNumber",
        width: ColumnWidth.Char15
      },
      {
        label: "规格型号",
        prop: "specificationModel",
        width: ColumnWidth.Char8
      },
      {
        label: "产地类型",
        prop: "domesticOrImported",
        width: ColumnWidth.Char8,
        cellRenderer: (data: TableColumnRenderer) => {
          return DomesticOrImportedEnumMapDesc[data.row.domesticOrImported];
        }
      },
      {
        label: "监测类型",
        prop: "monitoringTypes",
        width: ColumnWidth.Char10
      },
      {
        label: "送检报告",
        prop: "fileName",
        width: ColumnWidth.Char11,
        cellRenderer: (data: TableColumnRenderer) => {
          if (data.row.testFileId) {
            return (
              <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.testFileInfo)}>
                {data.row.testFileInfo?.name}
              </div>
            );
          } else {
            return <span>--</span>;
          }
        }
      },
      {
        label: "出厂报告",
        prop: "fileName",
        width: ColumnWidth.Char11,
        cellRenderer: (data: TableColumnRenderer) => {
          if (data.row.factoryFileId) {
            return (
              <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.factoryFileInfo)}>
                {data.row.factoryFileInfo?.name}
              </div>
            );
          } else {
            return <span>--</span>;
          }
        }
      },
      {
        label: "同步状态",
        prop: "pullStatus",
        minWidth: ColumnWidth.Char10,
        slot: "pullStatus"
      },
      {
        label: "创建时间",
        prop: "createTime",
        width: ColumnWidth.Char11,
        formatter: dateFormatter()
      },
      {
        label: "操作",
        prop: "operation",
        fixed: "right",
        slot: "operation",
        width: ColumnWidth.Char11
      }
    ];
  }

  async function downLoad(fileInfo: FileInfoModel) {
    const blob = await downLoadFile(fileInfo.id);
    downloadByData(blob, fileInfo.name, blob.type);
  }
  return columnsConfig;
}
