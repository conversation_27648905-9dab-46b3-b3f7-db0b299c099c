<template>
  <div class="flex-1 flex overflow-hidden">
    <BaseList :columns="columns" v-bind="$attrs" />
    <DetailProcessQualityAutoDialog ref="detailDialog" />
  </div>
</template>

<script setup lang="ts">
import BaseList from "./base-list.vue";
import DetailProcessQualityAutoDialog from "@/views/components/state-grid-order-sync/dialogs/detail-process-quality-auto/index.vue";
import { TableColumns } from "@pureadmin/table";
import { ColumnWidth, StateGridOrderSyncType, TableWidth } from "@/enums";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { h, provide, reactive, ref } from "vue";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { ElMessage, ElMessageBox, ElProgress } from "element-plus";
import { checkAutoCollectProdData } from "@/api/data-integrity-check/data-integrity-check";
import { hasAuth } from "@/router/utils";
import { PermissionKey } from "@/consts/permission-key";

const type = StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT;
const { sync, cancelSync, syncByDataId, getSyncHistoryByDataId, prioritySync } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  operatorColumnWith: hasAuth(PermissionKey.form.formPurchaseProcessCheckProdData)
    ? ColumnWidth.Char29
    : ColumnWidth.Char22,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  cancelSyncFn: cancelSync,
  prioritySyncFn: prioritySync,
  checkProductionDataFn: checkProductionData
});
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);
const detailDialog = ref<InstanceType<typeof DetailProcessQualityAutoDialog>>();

const columns: Array<TableColumns> = reactive([
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order
  },
  {
    label: "报工批次号",
    prop: "productionBatchNo",
    minWidth: TableWidth.largeOrder,
    formatter: row => {
      const value = row.productBatchNo || row.productionBatchNo;
      return h(
        "a",
        {
          class: "text-primary",
          onClick: () => detailDialog.value.openDetailDialog(row.productReportId, value)
        },
        value
      );
    }
  },
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: "同步进度",
    prop: "percentage",
    cellRenderer: data => {
      const totalCount = +data.row.totalCount || 0;
      const syncedCount = +data.row.syncedCount || 0;
      const percentage = totalCount === 0 || syncedCount === 0 ? 0 : Math.floor((syncedCount / totalCount) * 100);
      return h(ElProgress, {
        percentage
      });
    },
    minWidth: ColumnWidth.Char13
  },
  {
    label: "数据总量",
    prop: "totalCount",
    minWidth: ColumnWidth.Char5
  },
  {
    label: "已同步",
    prop: "syncedCount",
    minWidth: ColumnWidth.Char4
  },
  ...normalColumns,
  operatorColumn
]);

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

/**
 * @description: 检查生产数据
 */
async function checkProductionData(data: { dataId: string }) {
  const action = await ElMessageBox.alert("是否确认检查生产数据？", "检查数据", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  if (action !== "confirm") {
    return;
  }
  const { dataId } = data;
  const { data: result } = await checkAutoCollectProdData({
    reportIds: [dataId]
  });
  if (result) {
    ElMessage.success("操作成功，已加入检查队列");
  }
}
</script>

<style scoped>
:deep(.base-list) {
  padding-top: 12px !important;
}
</style>
