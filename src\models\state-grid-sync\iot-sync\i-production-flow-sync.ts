import { IIotSyncCommon } from "@/models";

export interface IProductionFlowSync extends IIotSyncCommon {
  ipoNo: string;
  /** 过程检编号 */
  code: string;
  /** 过程检Code（工序code） */
  processCode: string;
  /** 工序（工序名称） */
  processName: string;
  /** 工序id */
  processId: string;
}

/** 金具 */
export interface IArmourClampProductionFlowSync extends IProductionFlowSync {
  /** 产品型号 */
  model?: string;
  /** 产品名称 */
  productName?: string;
  /** 质量追溯码 */
  qualityTraceCode?: string;
  /** 生产批次号 */
  productBatchNo?: string;
  /** 生产完成时间 */
  productFinishTime?: string;
  /** 加工方式 */
  processMethod?: string;
  /** 加工件数 */
  processNumber?: string;
}
