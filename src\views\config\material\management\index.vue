<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="ml-6 flex-1">
      <ElFormItem label="物资种类：">
        <SubclassSelect v-model="state.params.subClassCode" placeholder="请选择物资种类" clearable />
      </ElFormItem>
      <ElFormItem label="物料编号：">
        <ElInput
          class="w-full"
          clearable
          v-model="state.params.materialCode"
          placeholder="请输入物料编号"
          @clear="onConfirmQuery"
        />
      </ElFormItem>

      <ElFormItem label="物料描述：">
        <ElInput
          class="w-full"
          clearable
          v-model="state.params.materialDescribe"
          placeholder="请输入物料描述"
          @clear="onConfirmQuery"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton
      size="large"
      type="primary"
      :icon="Plus"
      v-auth="PermissionKey.meta.metaMaterialCreate"
      v-track="TrackPointKey.META_MATERIAL_CREATE"
      @click="onAddMaterialModalVis()"
      >新增物料
    </ElButton>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="materialStore.materials"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="materialStore.loading"
      @page-size-change="queryMaterial"
      @page-current-change="queryMaterial"
    >
      <template #operation="data">
        <div>
          <ElButton
            type="primary"
            v-auth="PermissionKey.meta.metaMaterialEdit"
            v-track="TrackPointKey.META_MATERIAL_EDIT"
            link
            @click="onEditMaterialModalVis(data.row)"
          >
            编辑
          </ElButton>
          <ElButton
            link
            type="danger"
            v-auth="PermissionKey.meta.metaMaterialDelete"
            v-track="TrackPointKey.META_MATERIAL_DELETE"
            @click="onDeleteMaterial(data.row)"
          >
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getMaterialFormModalTitle()"
      align-center
      class="default"
      destroy-on-close
      v-model="state.materialFormModalVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <MaterialForm ref="materialFormRef" />
      <template #footer>
        <el-button @click="onCancelMaterialFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveDevice()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { reactive, ref, watch } from "vue";
import { useMaterialStore } from "@/store/modules/material";
import { IMaterial, IMaterialDto, IMaterialQueryParams, IResponse } from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import MaterialForm from "@/views/components/material-form";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { PermissionKey, TrackPointKey } from "@/consts";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";

usePageStoreHook().setTitle("物料管理");

const { columns } = useColumns();
const { pagination } = useTableConfig();
const materialStore = useMaterialStore();

pagination.pageSize = 20;

const state = reactive<{
  materialFormModalVis: boolean;
  isAddMaterial: boolean;
  params: IMaterialQueryParams;
}>({
  materialFormModalVis: false,
  isAddMaterial: false,
  params: {}
});

watch(
  () => materialStore.total,
  () => {
    pagination.total = materialStore.total;
  },
  {
    immediate: true
  }
);

const saveLoading = ref<boolean>(false);
const materialFormRef = ref<InstanceType<typeof MaterialForm>>();

queryMaterial();
const getMaterialFormModalTitle = () => (state.isAddMaterial ? "新增物料" : "编辑物料");
const handleSaveDevice = useLoadingFn(onAddMaterial, saveLoading);

const onAddMaterialModalVis = () => {
  state.isAddMaterial = true;
  state.materialFormModalVis = true;
};

async function onAddMaterial() {
  const formValue: IMaterialDto | false = await materialFormRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.id) {
    await materialStore.createMaterial(formValue);
  } else {
    await materialStore.editMaterial(formValue);
  }

  state.materialFormModalVis = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  queryMaterial();
}

const onEditMaterialModalVis = async (material: IMaterial) => {
  state.materialFormModalVis = true;
  state.isAddMaterial = false;
  const materialRes: IResponse<IMaterial> = await materialStore.getMaterialDetailById(material.id);
  if (!materialRes.data) {
    ElMessage.warning(materialRes.msg);
    return;
  }
  materialFormRef.value.initializeForm(materialRes.data, ["materialCode"]);
};

const onDeleteMaterial = async (device: IMaterial) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await materialStore.deleteMaterial(device.id);
  ElMessage.success("删除成功");
  queryMaterial();
};

const onResetQuery = () => {
  state.params = {};
  pagination.currentPage = 1;
  queryMaterial();
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  queryMaterial();
};

const onCancelMaterialFormModal = () => {
  state.materialFormModalVis = false;
  materialFormRef.value.initializeForm({});
};

function queryMaterial() {
  materialStore.queryMaterialsPaging({
    ...state.params,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
