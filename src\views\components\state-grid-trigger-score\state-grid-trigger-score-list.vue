<template>
  <pure-table
    class="flex-1 overflow-hidden pagination"
    show-overflow-tooltip
    :columns="props.columns"
    :data="props.data"
    :loading="props.loading"
    :pagination="pagination"
    row-key="id"
    v-bind="$attrs"
    @page-current-change="pageChange"
    @page-size-change="pageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
  <el-dialog
    v-model="ctx.editVisible"
    :title="dialogTitle"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <ProductOrder
      v-if="isCable"
      ref="productOrderRef"
      v-loading="fetchLoading"
      :actualStartDateRules="actualStartDateRules"
      :actualFinishDateRules="actualFinishDateRules"
    />
    <WorkOrderForm
      v-else
      ref="workOrderFormRef"
      v-loading="fetchLoading"
      :actualStartDateRules="actualStartDateRules"
      :actualFinishDateRules="actualFinishDateRules"
    />
    <template #footer>
      <el-button @click="ctx.editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleUpdateAndTriggerScore" :loading="updateAndTriggerScoreLoading">
        保存，并触发质量评分
      </el-button>
      <el-button type="primary" @click="handleUpdate" :loading="updateLoading">保存</el-button>
    </template>
  </el-dialog>
  <el-dialog
    title="触发评分确认"
    v-model="ctx.syncAuditVisible"
    destroy-on-close
    :close-on-press-escape="false"
    fullscreen
    class="sync-audit-full-screen"
  >
    <SyncAudit :base-info="ctx.syncAuditBaseInfo" :iotAuditItems="IOT_STATE_GRID_CARD_IDS" />
    <template #footer>
      <el-button @click="ctx.syncAuditVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmTrigger" :loading="syncLoading">确认触发</el-button>
    </template>
  </el-dialog>
  <TriggerScoreCheckDialog v-model="checkVisible" :checkText="checkText" @trigger-score="confirmTriggerScore" />
</template>

<script setup lang="ts">
import ProductOrder from "@/views/order/purchase-order/detail/src/components/production-order/add-product-order/product-order/index.vue";
import { PureTable } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { ICreateProductOrder, IPagingReq, IStateGridRateTrigger } from "@/models";
import { FormItemRule } from "element-plus/es/components/form/src/types";
import { requiredMessage } from "@/utils/form";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { computed, inject, ref, watch, watchEffect } from "vue";
import { triggerScoreProductOrderKey } from "@/views/components/state-grid-trigger-score/tokens";
import {
  allowTriggerStateGridScore,
  triggerScore,
  updateProductionOrderByTrigger,
  updateWorkOrderByTrigger
} from "@/views/components/state-grid-trigger-score/tools";
import { ElMessage, ElNotification } from "element-plus";
import WorkOrderForm from "@/views/order/purchase-order/detail/src/production-data/non-cable/work-order/forms/work-order-form.vue";
import { useNonCableWorkOrderStore, useProductOrderStore } from "@/store/modules";
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import TriggerScoreCheckDialog from "@/views/components/state-grid-trigger-score/trigger-score-check-dialog.vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useTableConfig } from "@/utils/useTableConfig";
import { useStateGriRateDetailStore } from "@/store/modules/state-grid-rate";
import { IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";

const props = defineProps<{
  columns: TableColumnList;
  data: Array<IStateGridRateTrigger>;
  loading: boolean;
  modelValue?: IPagingReq;
}>();

const emit = defineEmits(["update:modelValue"]);

const productStore = useProductOrderStore();
const nonCableWorkOrderStore = useNonCableWorkOrderStore();
const syncAuditStore = useStateGridSyncAuditStore();
const ctx = inject(triggerScoreProductOrderKey);

const fetchLoading = ref(false);
const updateLoading = ref(false);
const syncLoading = ref(false);
const updateAndTriggerScoreLoading = ref(false);
const productOrderRef = ref<InstanceType<typeof ProductOrder> | null>();
const workOrderFormRef = ref<InstanceType<typeof WorkOrderForm> | null>();

const handleTriggerScoreAfterSyncAudit = useLoadingFn(triggerScoreAfterSyncAudit, syncLoading);

const isCable = computed(() => ctx.editData?.isCable);
const dialogTitle = computed(() => (isCable.value ? "编辑生产订单" : "编辑生产工单"));
const checkVisible = ref<boolean>(false);
const checkText = ref();

const handleUpdate = useLoadingFn(update, updateLoading);
const handleUpdateAndTriggerScore = useLoadingFn(updateAndTriggerScore, updateAndTriggerScoreLoading);

const actualStartDateRules: Array<FormItemRule> = [
  { required: true, message: requiredMessage("实际开始时间"), trigger: "change" }
];
const actualFinishDateRules: Array<FormItemRule> = [
  { required: true, message: requiredMessage("实际结束时间"), trigger: "change" }
];

const store = useStateGriRateDetailStore();
const { pagination } = useTableConfig();

const pageInfo = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

watchEffect(() => {
  pagination.total = store.triggerTotal;
});

watch(
  () => ctx.editVisible,
  visible => {
    if (!visible) {
      return;
    }
    const { dataId, isCable } = ctx.editData;
    isCable ? fillingProductionOrderForm(dataId) : fillingWorkOrderForm(dataId);
  }
);

watch(
  () => ctx.syncAuditVisible,
  visible => {
    if (!visible) {
      return;
    }
    const { isCable, woNo, ipoNo } = ctx.editData;
    const orderNo = isCable ? ipoNo : woNo;
    syncAuditStore.setOrderNo(orderNo);
  }
);

function fillingProductionOrderForm(productionOrderId: string) {
  fetchLoading.value = true;
  productStore
    .getProductOrderDetailById(productionOrderId)
    .then(productionOrder => productStore.setCreateProductOrder(productionOrder))
    .finally(() => (fetchLoading.value = false));
}

function fillingWorkOrderForm(workOrderId: string) {
  fetchLoading.value = true;
  nonCableWorkOrderStore
    .getWorkOrderById(workOrderId)
    .then(workOrder => workOrderFormRef.value.initializeValue(workOrder))
    .finally(() => (fetchLoading.value = false));
}

async function update() {
  await updateData();
  ctx.editVisible = false;
  ctx.refresh(pageInfo.value);
  ElMessage.success("编辑成功");
}

async function updateAndTriggerScore() {
  await updateData();
  const { purchaseId, purchaseLineId, productionId, workOrderId, dataId } = ctx.editData;
  await triggerScore(purchaseId, purchaseLineId, dataId, productionId, workOrderId);
  ctx.editVisible = false;
  ctx.refresh(pageInfo.value);
  showNotification();
}

function updateData(): Promise<void> {
  return ctx.editData.isCable ? updateProductionOrder() : updateWorkOrder();
}

async function updateProductionOrder(): Promise<void> {
  const data: ICreateProductOrder | boolean = await productOrderRef.value.getFormValue();
  if (typeof data === "boolean") {
    return Promise.reject("invalid");
  }
  if (data.salesLineDetails) {
    delete data.salesLineDetails;
  }
  await updateProductionOrderByTrigger(data, ctx.editData.id);
}

async function updateWorkOrder(): Promise<void> {
  const workOrder = await workOrderFormRef.value.getValidValue();
  await updateWorkOrderByTrigger(workOrder, ctx.editData.id);
}

/** 触发评分 */
async function confirmTrigger() {
  const { purchaseLineId } = ctx.editData;
  const checkRes = (await allowTriggerStateGridScore(purchaseLineId)).data;
  const { verify, message } = checkRes;
  if (!verify) {
    checkText.value = message;
    checkVisible.value = true;
    return;
  }
  await confirmTriggerScore();
}

async function confirmTriggerScore() {
  await handleTriggerScoreAfterSyncAudit();
}

async function triggerScoreAfterSyncAudit() {
  const { purchaseId, purchaseLineId, dataId, isCable } = ctx.editData;
  const productionId = isCable ? dataId : undefined;
  const workOrderId = isCable ? undefined : dataId;
  await triggerScore(purchaseId, purchaseLineId, dataId, productionId, workOrderId);
  checkVisible.value = false;
  ctx.syncAuditVisible = false;
  showNotification();
}

function showNotification() {
  const { woNo, ipoNo, isCable } = ctx.editData;
  const tag = isCable ? "生产订单" : "生产工单";
  const no = isCable ? ipoNo : woNo;
  ElNotification.info({
    title: "触发评分",
    message: `正在触发${tag}【${no}】下生产数据的质量评分`,
    duration: 3000
  });
}

/**
 * 切换页面
 */
function pageChange(pageNo: number) {
  pageInfo.value = { pageNo, pageSize: pagination.pageSize };
  ctx.refresh({ pageNo, pageSize: pagination.pageSize });
}

/**
 * 切换页码数据
 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  pageInfo.value = { pageNo: 1, pageSize };
  ctx.refresh({ pageNo: 1, pageSize });
}
</script>

<style lang="scss">
@import "@/views/order/purchase-order/detail/styles/mixin";

.sync-audit-full-screen {
  @include full-screen-dialog;
}
</style>
