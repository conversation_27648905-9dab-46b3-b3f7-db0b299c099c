<template>
  <BaseList
    :columns="columns"
    type="work-order"
    :check-data="!store.isCable"
    v-bind="$attrs"
    ref="baseList"
    @dataChange="dataChange"
  />
  <EditWorkOrderDialog :isCable="store.isCable" ref="editDialog" />
  <DetailWorkOrderDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { provide, ref } from "vue";
import { OperatorCell } from "@/components/TableCells";
import { infoHeader } from "@/components/TableHeader";
import { StateGridOrderSyncResult, TableWidth } from "@/enums";
import { IWorkOrderSync } from "@/models";
import { formatDecimal } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import EditWorkOrderDialog from "@/views/components/state-grid-order-sync/dialogs/edit-work-order-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import DetailWorkOrderDialog from "@/views/components/state-grid-order-sync/dialogs/detail-work-order-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import BaseList from "../base-list.vue";
import { missingDataFormatter } from "../formatters/missing-data-formatter";
import { useAuditItemStatus } from "@/views/components/sync-audit/hooks/audit-item-status";

const props = defineProps<{
  cardId: string;
}>();

const store = useStateGridSyncAuditStore();

const { updateItemStatus } = useAuditItemStatus();
const { enumFormatter, dateRangeFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditWorkOrderDialog>,
  InstanceType<typeof DetailWorkOrderDialog>,
  IWorkOrderSync
>();

provide(stateGridOrderSyncEditKey, {
  refreshFn: pageInfo => baseList.value?.refresh(pageInfo)
});

const columns: TableColumnList = [
  {
    label: "工单编号",
    prop: "woNo",
    width: TableWidth.suborder,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.largeName
  },
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: "生产数量",
    prop: "amount",
    minWidth: TableWidth.number,
    formatter: row => {
      const { amount, unitName } = row;
      return amount ? `${formatDecimal(amount)} ${unitName}` : null;
    }
  },
  {
    label: "计划日期",
    width: TableWidth.dateRanger,
    formatter: dateRangeFormatter("planStartDate", "planEndDate")
  },
  {
    label: "数据检查",
    prop: "missingData",
    width: TableWidth.type,
    fixed: "right",
    hide: () => store.isCable,
    formatter: missingDataFormatter(),
    headerRenderer: infoHeader("未显示有缺失时，建议人工检查相关数据，请在系统中维护所有生产数据数据")
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    width: TableWidth.operation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        }
      ])
  }
];

function dataChange(data: Array<IWorkOrderSync>) {
  if (!store.isCable || !props.cardId) {
    return;
  }
  updateItemStatus(
    props.cardId,
    data.some(datum => datum.missingData)
  );
}
</script>

<style scoped></style>
