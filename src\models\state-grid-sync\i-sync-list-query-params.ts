import { ISyncQueryParams } from "@/models";

export type BusinessSyncListType = "work-report" | "work-order" | "production-plan" | "production" | "sales";

export type IotSyncListType =
  | "raw-material"
  | "production-flow"
  | "process-data"
  | "process-data-result"
  | "experiment"
  | "finished-production";

export type SyncListType = BusinessSyncListType | IotSyncListType;

export interface ISyncListQueryData extends ISyncQueryParams {
  purchaseLineId: string;
}

export interface ISyncListQueryParams {
  type: SyncListType;
  data: ISyncListQueryData;
  /** 是否检查数据缺失 */
  checkData?: boolean;
  /** 是否取消上一次请求 */
  cancelLast?: boolean;
}
