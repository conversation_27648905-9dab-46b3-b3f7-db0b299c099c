import { ETestSensingType } from "@/enums";

/** 试验感知 */
export interface ITestSensing {
  title: string;
  key: ETestSensingType;
  message: string;
  hasData?: boolean;
  tip?: string;
}

export const TEST_SENSING_STEP_LIST: ITestSensing[] = [
  {
    title: "第一步",
    key: ETestSensingType.RawMaterialGroupUnitCheck,
    message: "原材料、组部件检测",
    hasData: false,
    tip: "缺少原材料检验信息"
  },
  {
    title: "第二步",
    key: ETestSensingType.ProductionProcessInspection,
    message: "生产工艺及过程检测",
    hasData: false,
    tip: "缺少生产过程检验信息"
  },
  {
    title: "第三步",
    key: ETestSensingType.ExFactoryExperiment,
    message: "出厂试验",
    hasData: false,
    tip: "缺少出厂试验信息"
  },
  {
    title: "第四步",
    key: ETestSensingType.FinishingWarehousing,
    message: "成品入库",
    hasData: false,
    tip: "缺少成品入库信息"
  }
];
