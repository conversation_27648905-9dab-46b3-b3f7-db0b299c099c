import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { ISupervisionPlan, ISupervisionPlanReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const querySupervisionPlan = (data: ISupervisionPlanReq) => {
  const url: string = withApiGateway("admin-api/southgrid/supervisionPlans/list");
  return http.post<ISupervisionPlanReq, IListResponse<ISupervisionPlan>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getSupervisionPlanById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/supervisionPlans/${id}`);
  return http.get<string, IResponse<ISupervisionPlan>>(url);
};

/** 删除根据Id */
export const deleteSupervisionPlanById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/supervisionPlans/${id}`));
};

/** 拉取南网监造计划 */
export const pullSupervisionPlan = () => {
  const url: string = withApiGateway("admin-api/southgrid/supervisionPlans/pull");
  return http.get<void, IResponse<boolean>>(url);
};

/** 推送南网监造计划 */
export const syncSupervisionPlan = (data: { idList: Array<string> }) => {
  const url: string = withApiGateway("admin-api/southgrid/supervisionPlans/syncPlan");
  return http.post<{ idList: Array<string> }, IResponse<boolean>>(url, { data });
};
