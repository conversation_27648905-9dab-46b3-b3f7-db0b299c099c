import { OrderSyncPriorityEnum } from "@/enums";

export interface IPurchaseOrderLine {
  id: string;
  /** 采购订单行项目号*/
  poItemNo: string;
  /** 物料编码*/
  materialCode: string;
  /** 物料描述*/
  materialDesc: string;
  /** 采购数量*/
  amount: number;
  /** 物资小类名称*/
  matMinName: string;
  /** 关联销售行个数 */
  linkedCount: number;
  /** 物资种类名称 */
  subClassCode: string;
  subClassName: string;
  /** 前端添加颜色标记 */
  color?: string;

  /** 同步优先级 */
  priority: OrderSyncPriorityEnum;
}

export interface IPurchaseOrderLineRes extends IPurchaseOrderLine {
  /** 采购订单Id */
  purchaseId: string;
  /** 采购订单行项目ID */
  poItemId: string;
  /** 采购号 */
  poNo: string;
  /** 合同编号 */
  conCode: string;
  /** 合同名称 */
  conName: string;
  /** 采购方公司名称 */
  buyerName: string;
  /** 合同编号（国网经法号） */
  sellerConCode: string;
  /** 项目名称 */
  prjName: string;
  /** 合同签订日期 */
  sellerSignTime: string;
  /** 拉取时间 */
  pullingTime: string;
  /** 同步状态 */
  syncResult: boolean;
  /** 触发结果 */
  isTrigger: boolean;
  /** 合同最终交货期 */
  cfmDlvTime: string;
  /** 合同计划交货期 */
  dlvTime: string;
}

export interface IPurchaseLineCount {
  /** 订单总数 */
  total: number;
  /** 优先级数量*/
  priorityCount: number;
}
