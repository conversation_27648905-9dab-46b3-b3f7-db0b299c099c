<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="80px">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物资品类" prop="categoryCode">
          <category-selector v-model="form.categoryCode" :disabled="isEditMode || isCopyMode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <sub-class-selector
            limit-by-category-code
            :category-code="form.categoryCode"
            v-model="form.subClassCode"
            :placeholder="subClassSelectorPlaceholder"
            :disabled="isEditMode || isCopyMode"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="规范名称" prop="qualityName">
          <el-input clearable v-model="form.qualityName" :maxlength="100" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规范编号" prop="qualityCode">
          <el-input clearable v-model="form.qualityCode" :maxlength="100" :disabled="isEditMode" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="启用状态" prop="enabled">
          <el-switch v-model="form.enabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="适用类型" prop="matchType">
          <el-radio-group v-model="form.matchType" :disabled="isEditMode">
            <el-radio :label="1" border>物料</el-radio>
            <el-radio :label="2" border>销售订单行</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            clearable
            type="textarea"
            placeholder="请输入备注"
            resize="none"
            :maxlength="100"
            :rows="3"
            v-model="form.remark"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { watch, reactive, ref, computed } from "vue";
import { FormInstance, FormRules } from "element-plus";
import categorySelector from "@/views/components/category-select/category-select.vue";
import SubClassSelector from "@/views/components/subclass-select/subclass-select.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genQualitySpecificationNumber } from "@/api/quality-tracing";
import { QualitySpecificationMatchTypeEnum } from "@/enums/quality-specification";

/**
 * 新增/编辑/复制 质量规范 的基础信息表单
 */

interface FormType {
  /** 物资品类编码 */
  categoryCode: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 编号 */
  qualityCode: string;
  /** 规范名称 */
  qualityName: string;
  /** 使用状态 */
  enabled: boolean;
  /** 备注 */
  remark: string;
  /** 适用类型 */
  matchType: number;
}

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add" | "copy";
}>();

const form = reactive({
  categoryCode: "",
  subClassCode: "",
  qualityCode: "",
  qualityName: "",
  enabled: true,
  remark: "",
  matchType: QualitySpecificationMatchTypeEnum.Material
});
const formRef = ref<FormInstance>();
const genNumberLoading = ref(false);

const subClassSelectorPlaceholder = computed(() => {
  return form.categoryCode ? "请选择" : "请先选择物资品类";
});

const isAddMode = computed(() => props.mode === "add");
const isEditMode = computed(() => props.mode === "edit");
const isCopyMode = computed(() => props.mode === "copy");

const rules: FormRules = {
  categoryCode: [{ required: true, message: "请选择物资品类", trigger: "change" }],
  subClassCode: [{ required: true, message: "请选择物资种类", trigger: "change" }],
  qualityCode: [{ required: true, message: "请输入编号", trigger: "change" }],
  qualityName: [{ required: true, message: "请输入规范名称", trigger: "change" }],
  enabled: [{ required: true, message: "请选择使用状态", trigger: "change" }],
  matchType: [{ required: true, message: "请选择适用类型", trigger: "change" }]
};

/**
 * @description: 请求生成一条编号
 */
const requestQualitySpecificationNumber = useLoadingFn(async (code: string) => {
  const res = await genQualitySpecificationNumber(code);
  form.qualityCode = res.data;
}, genNumberLoading);

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  form.categoryCode = v.categoryCode;
  form.subClassCode = v.subClassCode;
  form.qualityCode = v.qualityCode;
  form.qualityName = v.qualityName;
  form.enabled = v.enabled;
  form.remark = v.remark;
  form.matchType = v.matchType;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

/**
 * 仅在新增、复制时自动生成编号
 */
(isAddMode.value || isCopyMode.value) &&
  watch(
    () => form.subClassCode,
    code => {
      if (code) {
        console.log(code);
        requestQualitySpecificationNumber(code);
      }
    },
    {
      immediate: true
    }
  );

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped lang="scss"></style>
