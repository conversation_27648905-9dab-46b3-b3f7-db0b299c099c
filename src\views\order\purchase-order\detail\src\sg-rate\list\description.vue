<template>
  <div class="flex flex-col w-full justify-around">
    <div class="title font-medium">
      <span class="label">采购订单行项目号</span>
      <span class="content">{{ rate.poItemNo }}</span>
    </div>
    <div class="descriptions">
      <el-row>
        <el-col :span="6">
          <div class="description-item">
            <span class="label">物料编码</span>
            <span class="content">{{ rate.materialCode }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="description-item">
            <span class="label">采购数量</span>
            <span class="content">{{ rate.amount }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="description-item">
            <span class="label">物料描述</span>
            <span class="content">
              <ShowTooltip class-name="max-w-[15em] lg:max-w-[20em] xl:max-w-[30em]" :content="rate.materialDesc" />
            </span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IStateGridRate } from "@/models";
import { computed } from "vue";
import ShowTooltip from "@/components/ShowTooltip";

const props = defineProps<{
  rate: IStateGridRate;
}>();

const rate = computed(() => props.rate);
</script>

<style scoped lang="scss">
.descriptions {
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-primary);

  .description-item {
    .label {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-secondary);
      padding-right: 8px;
    }

    .content {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-primary);
    }
  }
}

.title {
  @apply text-lg mb-2;

  .label {
    padding-right: 12px;
  }
}
</style>
