<!-- 第三步：填报生产数据 - 非线缆 -->
<template>
  <div class="flex flex-col flex-1">
    <QuickSolver title="需填工单的生产单" :items="store.noWorkProductOrders">
      <template v-slot="{ item }">
        <div class="text-lg">
          <span class="mr-1">生产订单</span>
          <span>{{ item?.ipoNo }}</span>
        </div>
        <div class="flex-bc">
          <div class="w-[200px] xl:w-[240px] text-secondary text-base" />
          <el-button
            v-auth="PermissionKey.form.formPurchaseWorkOrderBtnQuickCreate"
            v-track="TrackPointKey.FORM_PURCHASE_PWO_FAST_CREATE"
            size="small"
            type="warning"
            @click="openQuickCreateDialog(item)"
            >快速创建</el-button
          >
        </div>
      </template>
    </QuickSolver>
    <div class="card flex-1 py-3">
      <div class="mb-4">
        <SearchBar @onCreateWorkOrderDialogVisible="createVisible = true" @searchForm="searchForm" />
      </div>
      <pure-table
        row-key="id"
        size="large"
        class="flex-1 overflow-hidden pagination"
        show-overflow-tooltip
        :loading="loading"
        :columns="columns"
        :data="store.workOrders"
        :pagination="pagination"
        @page-size-change="onPageSizeChange()"
        @page-current-change="onPageCurrentPage()"
      >
        <template #empty>
          <CxEmpty />
        </template>
      </pure-table>
    </div>
    <CreateWorkOrderDialog v-model="createVisible" @shouldRefresh="refreshListAndStepStatus" />
    <UpdateWorkOrderDialog v-model="editVisible" :work-order-id="editId" @shouldRefresh="handleRefresh" />
    <QuickCreateWorkOrderDialog
      v-model="quickCreateVisible"
      :product-order="quickCreateProductOrder"
      @shouldRefresh="refreshListAndStepStatus"
    />
    <el-dialog title="数据检查" class="default" align-center v-model="dataMissingVisible">
      <DataMissing :data-missing="dataMissing" />
    </el-dialog>
    <FillInDataDialog v-model="fillInDataVisible" @close="handleRefresh" />
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import {
  ColumnWidth,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc,
  ProductionStateEnum,
  TableWidth,
  VoltageClassesEnumMapDesc
} from "@/enums";
import {
  useSalesFillInDataStore,
  useNonCableWorkOrderStore,
  useSystemAuthStore,
  useReportWorkStore,
  useSalesOrderDetailStore
} from "@/store/modules";
import { computed, h, onMounted, ref, watchEffect } from "vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { formatDate } from "@/utils/format";
import { dateFormat, TrackPointKey } from "@/consts";
import { TableColumnCtx } from "element-plus";
import { OperatorCell } from "@/components/TableCells";
import CreateWorkOrderDialog from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/dialogs/create-work-order-dialog.vue";
import { useConfirm } from "@/utils/useConfirm";
import UpdateWorkOrderDialog from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/dialogs/update-work-order-dialog.vue";
import QuickSolver from "@/views/order/sales-order/detail/src/components/quick-solver/quick-solver.vue";
import QuickCreateWorkOrderDialog from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/dialogs/quick-create-work-order-dialog.vue";
import { IDataCheck, IProductOrder, IWorkOrderReq, IWorkOrder } from "@/models";
import DataMissing from "@/views/order/sales-order/detail/src/components/data-missing/data-missing.vue";
import FillInDataDialog from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/dialogs/fill-in-data-dialog.vue";
import { PermissionKey } from "@/consts";
import { infoHeader } from "@/components/TableHeader";
import { useTableConfig } from "@/utils/useTableConfig";
import SearchBar from "./search-bar.vue";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";
const { pagination } = useTableConfig();

const store = useNonCableWorkOrderStore();
const salesOrderStore = useSalesOrderDetailStore();
const systemAuthStore = useSystemAuthStore();
const fillInDataStore = useSalesFillInDataStore();
const reportWorkStore = useReportWorkStore();
const authDataCheckCMP = computed(() => systemAuthStore.businessLicenseAuth?.authorization?.dataCheckFlag);
const { mapFormatter, dateFormatter, enumFormatter } = useTableCellFormatter();

const createVisible = ref(false);
const quickCreateVisible = ref(false);
const quickCreateProductOrder = ref<IProductOrder>(undefined);
const editVisible = ref(false);
const editId = ref<string>(undefined);
const dataMissingVisible = ref(false);
const dataMissing = ref<IDataCheck>(undefined);
const fillInDataVisible = ref(false);
let searchFormValue: IWorkOrderReq;

const loading = computed(() => store.loading);
const isArmourClamp = computed(() => fillInDataStore.isArmourClamp);

const columns: Array<TableColumns> = [
  {
    label: "工单编号",
    prop: "woNo",
    minWidth: ColumnWidth.Char14,
    fixed: "left",
    cellRenderer(data) {
      return h(
        "span",
        { onClick: () => showFillInData(data.row), class: "text-primary cursor-pointer" },
        data.row.woNo
      );
    }
  },
  {
    label: "生产订单号",
    prop: "ipoNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),
    minWidth: ColumnWidth.Char14
  },
  {
    label: "销售订单行项目",
    prop: "soItemNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "子种类名称",
    prop: "minClassName",
    width: TableWidth.name,
    hide: () => {
      return !isArmourClamp.value;
    }
  },
  {
    label: "物料名称",
    prop: "materialName",
    width: TableWidth.name
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    width: TableWidth.number,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "需求数量",
    prop: "amount",
    width: TableWidth.number,
    formatter: row => {
      const { amount, unitDictionary } = row;
      if (!amount) {
        return null;
      }
      return unitDictionary ? `${amount} ${unitDictionary?.name}` : amount;
    }
  },
  {
    label: "计划日期",
    prop: "planStartDate",
    width: TableWidth.dateRanger,
    formatter: row => {
      const { planStartDate, planFinishDate } = row;
      if (!planStartDate && !planFinishDate) {
        return null;
      }
      return `${formatDate(planStartDate) || ""} ～ ${formatDate(planFinishDate) || ""}`;
    }
  },
  {
    label: "工单状态",
    prop: "woStatus",
    width: TableWidth.largeType,
    formatter: enumFormatter(ProductionStateEnum, "productionStateEnum")
  },
  {
    label: "实物Id",
    prop: "entityId"
  },
  {
    label: "实际开始日期",
    prop: "actualStartDate",
    width: TableWidth.date,
    fixed: "right",
    className: "actual-start-date",
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "实际结束日期",
    prop: "actualFinishDate",
    width: TableWidth.date,
    fixed: "right",
    className: "actual-finish-date",
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "数据检查",
    prop: "missingData",
    width: TableWidth.type,
    fixed: "right",
    headerRenderer: infoHeader("未显示有缺失时，建议人工检查相关数据，请在系统中维护所有生产数据数据"),
    formatter: (row: any, column: TableColumnCtx<any>, cellValue: boolean) => {
      if (cellValue) {
        return h(
          "a",
          {
            style: { color: "var(--el-color-danger)" },
            class: authDataCheckCMP.value ? "cursor-pointer" : null,
            onClick: authDataCheckCMP.value ? () => showDataMissDialog(row.id) : null
          },
          "有缺失"
        );
      }
      return null;
    }
  },
  {
    label: "操作",
    fixed: "right",
    minWidth: TableWidth.operation,
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row.id),
          props: { type: "primary" },
          permissionKey: PermissionKey.form.formPurchaseWorkOrderEdit,
          trackKey: TrackPointKey.FORM_PURCHASE_PWO_EDIT
        },
        {
          name: "删除",
          action: () => deleteWorkOrder(data.row.id),
          props: { type: "danger" },
          permissionKey: PermissionKey.form.formPurchaseWorkOrderDelete,
          trackKey: TrackPointKey.FORM_PURCHASE_PWO_DELETE
        }
      ])
  }
];

onMounted(async () => {
  queryNoWorkAndNonCableWorkOrders();
});

watchEffect(() => {
  pagination.total = store.total;
});

async function deleteWorkOrder(id: string) {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await store.deleteWorkOrder(id);
  refreshListAndStepStatus();
}

function openEditDialog(id: string) {
  editId.value = id;
  editVisible.value = true;
}

function openQuickCreateDialog(productOrder: IProductOrder) {
  quickCreateProductOrder.value = productOrder;
  quickCreateVisible.value = true;
}

async function showDataMissDialog(id: string) {
  dataMissingVisible.value = true;
  dataMissing.value = undefined;
  dataMissing.value = await store.checkData(id);
}

async function showFillInData(workOrder: IWorkOrder) {
  fillInDataVisible.value = true;
  await fillInDataStore.refreshData(workOrder.id, workOrder.productionId);
  const { subclassCode, processIds, minClassCode } = fillInDataStore.data as IWorkOrder;

  reportWorkStore.setReportWorkProps(subclassCode, processIds, minClassCode);
}

function refreshListAndStepStatus() {
  pagination.currentPage = 1;
  queryNoWorkAndNonCableWorkOrders();
  salesOrderStore.refreshStepStatus();
}

function handleRefresh() {
  queryNoWorkAndNonCableWorkOrders();
}

/**
 * 查询没有创建工单的生产订单
 * 查询 非线缆的工单列表
 */
async function queryNoWorkAndNonCableWorkOrders() {
  store.loading = true;
  await queryNonCableWorkOrdersPaging();
  await store.queryNoWorkProductOrders(true);
  store.loading = false;
}

async function queryNonCableWorkOrdersPaging() {
  let params: IWorkOrderReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    saleId: salesOrderStore.saleOrderId
  };
  params = { ...params, ...searchFormValue };
  return store.queryNonCableWorkOrderBySalesOrder(params);
}

const onPageSizeChange = () => {
  pagination.currentPage = 1;
  queryNonCableWorkOrdersPaging();
};

const onPageCurrentPage = () => {
  queryNonCableWorkOrdersPaging();
};

// 搜索的时候重置页码

function searchForm(formValue: IWorkOrderReq) {
  searchFormValue = formValue;
  pagination.currentPage = 1;
  queryNonCableWorkOrdersPaging();
}
</script>

<style scoped lang="scss">
@import "@/style/mixin";

.card {
  @include productCard;
}

:deep(.actual-start-date),
:deep(.actual-finish-date) {
  --default-color: var(--el-text-color-secondary);
  --default-value: "待补全";
}
</style>
