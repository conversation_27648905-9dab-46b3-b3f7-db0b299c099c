<template>
  <div class="detail-header-info flex items-center">
    <span>采购订单号: {{ info?.poNo }}</span>
    <span class="ml-4">采购订单行号: {{ info?.poItemNo }}</span>
    <span class="mx-4">物料编码: {{ info?.materialCode }}</span>
    <span class="flex-1 flex items-center overflow-hidden">
      <span class="w-[80px]">物料描述:</span>
      <ShowTooltip class-name="w-[80%]" :content="info?.materialDesc" />
    </span>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { IPurchaseOrderLineRes } from "@/models";

defineProps<{
  info: IPurchaseOrderLineRes | null;
}>();
</script>

<style scoped lang="scss"></style>
