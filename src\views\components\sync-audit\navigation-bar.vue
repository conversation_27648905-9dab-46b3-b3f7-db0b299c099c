<template>
  <aside>
    <nav class="pl-2.5 relative" ref="nav">
      <ul>
        <li v-for="item in items" :key="item.id" :class="getClass(item.id)" @click="scrollTo(item.id)">
          {{ item.title }}
          <el-icon :class="item.hasError ? 'visible' : 'invisible'">
            <WarningFilled />
          </el-icon>
        </li>
      </ul>
      <div class="ink-bar" ref="inkBar" />
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { computed, inject, nextTick, onMounted, ref, unref, watch } from "vue";
import { syncAuditAnchorKey, syncAuditStateKey } from "./tokens";
import { WarningFilled } from "@element-plus/icons-vue";
import { ScrollbarInstance } from "element-plus";

const ctx = inject(syncAuditAnchorKey);
const syncAuditStateCtx = inject(syncAuditStateKey);

const nav = ref<HTMLElement>();
const inkBar = ref<HTMLDivElement>();

let pauseListenHandle: ReturnType<typeof setTimeout>;

const items = computed(() => syncAuditStateCtx.navigationItems);

onMounted(() => {
  watch(
    () => ctx.id,
    async () => {
      await nextTick();
      const active = nav.value.getElementsByClassName("active")[0] as HTMLLIElement | undefined;
      if (!active) {
        return;
      }
      inkBar.value.style.transform = `translateY(${active.offsetTop + 2}px)`;
    },
    { immediate: true }
  );
});

function getClass(id: string): string {
  return ctx.id === id ? "active" : "";
}

async function scrollTo(id: string) {
  const scrollBar = unref(ctx.scrollBar);
  const wrap = scrollBar.wrapRef;
  const element: HTMLElement = wrap.querySelector(`#${id}`);
  if (!element) {
    return;
  }
  clearTimeout(pauseListenHandle);
  ctx.pauseListenScroll = true;
  ctx.id = id;
  await scrollToTarget(wrap.scrollTop, element.offsetTop, scrollBar);
  // set pause false after scroll end
  pauseListenHandle = setTimeout(() => (ctx.pauseListenScroll = false), 100);
  // add active class for card title
  addActiveClass(element.querySelector(".card-title"));
}

function scrollToTarget(initial: number, target: number, scrollBar: ScrollbarInstance): Promise<void> {
  return new Promise(resolve => {
    const delta = (target - initial) / 15;
    const scrollToTop = (top: number) => {
      scrollBar.setScrollTop(top);
      if (Math.abs(top - target) < 1) {
        resolve(undefined);
        return;
      }
      const next = top + delta;
      requestAnimationFrame(() => scrollToTop(next));
    };
    scrollToTop(initial + delta);
  });
}

function addActiveClass(element: HTMLElement) {
  if (!element) {
    return;
  }
  element.classList.add("active-card");
  setTimeout(() => element.classList.remove("active-card"), 600);
}
</script>

<style scoped lang="scss">
$inkBarWidth: 2px;

nav {
  border-left: $inkBarWidth solid var(--el-color-info-light-8);

  li {
    @apply text-primaryText text-base cursor-pointer;

    .el-icon {
      color: var(--el-color-warning);
    }

    &.active,
    &:hover {
      color: var(--el-color-primary);
    }
  }

  li + li {
    @apply mt-2;
  }

  .ink-bar {
    @apply absolute top-0 transition-transform;
    left: -$inkBarWidth;
    width: $inkBarWidth;
    height: 18px;
    background: var(--el-color-primary);
  }
}
</style>
