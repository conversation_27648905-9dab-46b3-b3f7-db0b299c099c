import { Ref, computed } from "vue";

/**
 * @description: 生成echarts配置
 */
export const genEchartsConfig = (
  categoryList: Ref<Array<string>>,
  countList: Ref<Array<number>>,
  probabilityList: Ref<Array<number>>
) =>
  computed(() => {
    const isEmpty = !!categoryList.value.length;
    return {
      animation: false,
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999"
          }
        }
      },
      grid: {
        left: "12%",
        top: 50,
        right: "10%",
        bottom: "10%"
      },
      legend: {
        data: ["数量", "概率"]
      },
      xAxis: [
        {
          type: "category",
          data: categoryList.value,
          axisPointer: {
            type: "shadow"
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        }
      ],
      yAxis: [
        {
          type: "value",
          name: "数量(个)",
          axisLabel: {
            formatter: "{value}"
          },
          splitLine: {
            show: false
          },
          nameTextStyle: {
            align: "right"
          }
        },
        {
          type: "value",
          name: "概率",
          axisLabel: {
            formatter: "{value}"
          },
          splitLine: {
            show: false
          },
          nameTextStyle: {
            align: "left"
          }
        }
      ],
      series: [
        {
          name: "数量",
          type: "bar",
          tooltip: {
            valueFormatter: function (value) {
              return (value as number) + "个";
            }
          },
          data: countList.value
        },
        {
          name: "概率",
          type: "line",
          yAxisIndex: 1,
          tooltip: {
            valueFormatter: function (value) {
              return value as number;
            }
          },
          data: probabilityList.value
        }
      ],
      graphic: {
        type: "text",
        left: "center",
        top: "middle",
        silent: true,
        invisible: isEmpty,
        style: {
          text: "暂无正态分布数据",
          fontSize: "16px",
          fill: "#909399"
        }
      }
    };
  });
