<template>
  <div class="bg-bg_color p-5 mx-6 overflow-hidden mt-3">
    <div class="header">
      <FontIcon icon="icon-classify-fill" />
      <span class="title font-medium">供货单信息详情</span>
    </div>
    <el-descriptions>
      <el-descriptions-item label="采购供货单编号">
        {{ state.baseInfoDetail?.supplyNo }}
      </el-descriptions-item>

      <el-descriptions-item label="合同类型">
        {{ ContractTypeEnumMapDisplayName[state.baseInfoDetail?.conType] }}
      </el-descriptions-item>

      <el-descriptions-item label="合同编号">
        {{ state.baseInfoDetail?.conCode }}
      </el-descriptions-item>
      <el-descriptions-item label="货物名称">
        {{ state.baseInfoDetail?.cargoName }}
      </el-descriptions-item>
      <el-descriptions-item label="采购方公司名称">
        {{ state.baseInfoDetail?.purchaseName }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ formatDate(state.baseInfoDetail?.createTime, fullDateFormat) }}
      </el-descriptions-item>
      <el-descriptions-item label="备注">
        {{ state.baseInfoDetail?.remark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>

  <div class="bg-bg_color p-5 my-3 mx-6 flex flex-col flex-1 overflow-hidden">
    <div class="w-full pb-4 px-2 flex justify-between">
      <TitleBar title="供货单信息明细" class="mb-2" />
      <ElButton size="large" type="primary" :icon="Plus" @click="onAddDeliveryOrderInfo()">新增明细 </ElButton>
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.infoTableData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
    >
      <template #operation="data">
        <div>
          <ElButton v-auth="PermissionKey.form.formSupplyOrderEdit" type="primary" link @click="onEdit(data.row.id)">
            编辑
          </ElButton>
          <ElButton v-auth="PermissionKey.form.formSupplyOrderDelete" link type="danger" @click="onDelete(data.row.id)">
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>

  <el-dialog
    :title="getDetailFormModalTitle()"
    align-center
    class="default"
    destroy-on-close
    v-model="state.detailFormModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCancelDetailFormModal"
  >
    <OrderDetail ref="orderDetailRef" />
    <template #footer>
      <el-button @click="onCancelDetailFormModal()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSaveDetailFormModal()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { IDeliveryOrderDetailListData, IDeliveryOrderDetailReqParams, IDeliveryOrderListData } from "@/models";
import { ref, reactive, onMounted, watch } from "vue";
import TitleBar from "@/components/TitleBar";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { useDeliveryOrderHook } from "../hooks/delivery-order-hook";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import OrderDetail from "./detail/index.vue";
import { ContractTypeEnumMapDisplayName } from "@/enums";
import { useDeliveryOrderStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { fullDateFormat, PermissionKey } from "@/consts";

usePageStoreHook().setTitle("供货单明细");
const { pagination } = useTableConfig();
const {
  createDeliveryOrderDetail,
  editDeliveryOrderDetail,
  queryDeliveryOrderDetailDataList,
  deleteDeliveryOrderDetailById,
  getDeliveryOrderDetailById,
  queryDeliveryOrderDetailDataListById,
  setDeliveryOrderDetailStorage,
  clearDeliveryOrderDetailStorage
} = useDeliveryOrderHook();
const route = useRoute();
const state = reactive<{
  detailFormModalVis: boolean;
  infoTableData: Array<IDeliveryOrderDetailListData>;
  selectId: string;
  params: IDeliveryOrderDetailReqParams;
  baseInfoDetail: IDeliveryOrderListData;
}>({
  detailFormModalVis: false,
  infoTableData: [],
  selectId: "",
  params: { deliveryOrderId: `${route.params.id}` },
  baseInfoDetail: {}
});

const { columns } = useColumns();
const getDetailFormModalTitle = () => (!state.selectId ? "新增供货单明细" : "编辑供货单明细");
const loading = ref<boolean>(false);
const saveLoading = ref<boolean>(false);
const orderDetailRef = ref<InstanceType<typeof OrderDetail>>();
const deliveryOrderStore = useDeliveryOrderStore();
async function onAddDeliveryOrderInfo() {
  state.selectId = "";
  state.detailFormModalVis = true;
}

onMounted(async () => {
  state.baseInfoDetail = await getTableData(state.params.deliveryOrderId);
  state.infoTableData = await queryDeliveryOrderDetailDataList(queryParams());
});

const onEdit = async (id: string) => {
  const deliveryOrderDetail: IDeliveryOrderDetailListData = await queryDeliveryOrderDetailDataListById(id);
  if (!deliveryOrderDetail) {
    return;
  }
  state.selectId = id;
  state.detailFormModalVis = true;
  setDeliveryOrderDetailStorage(deliveryOrderDetail);
};

const onCancelDetailFormModal = () => {
  clearForm();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteDeliveryOrderDetailById(id);
  ElMessage.success("删除成功");
  state.infoTableData = await queryDeliveryOrderDetailDataList(queryParams());
};

const onSave = async () => {
  const formValue: IDeliveryOrderDetailListData | false = await orderDetailRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }
  formValue.deliveryOrderId = `${route.params.id}`;
  if (!formValue.id) {
    await createDeliveryOrderDetail(formValue);
  } else {
    await editDeliveryOrderDetail(formValue);
  }

  clearForm();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  state.infoTableData = await queryDeliveryOrderDetailDataList(queryParams());
};

const onSaveDetailFormModal = useLoadingFn(onSave, saveLoading);
const getTableData = useLoadingFn(getDeliveryOrderDetailById, loading);
const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  return state.params;
};

watch(
  () => deliveryOrderStore.detailTotal,
  () => {
    pagination.total = deliveryOrderStore.detailTotal;
  },
  {
    immediate: true
  }
);

function clearForm() {
  clearDeliveryOrderDetailStorage();
  state.detailFormModalVis = false;
}
</script>

<style scoped lang="scss">
.header {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;

  .iconfont {
    color: var(--el-color-primary);
  }

  .title {
    @apply font-medium;
  }
}
</style>
