import { defineStore } from "pinia";
import * as api from "@/api";
import { ISynchronousFlag } from "@/models";

export const useSynchronousFlagStore = defineStore({
  id: "cx-synchronous-flag",
  state: () => ({
    synchronousFlag: [] as Array<ISynchronousFlag>,
    flag: false as boolean
  }),
  getters: {
    /** 获取数据 */
    async getSynchronousFlagList() {
      return this.synchronousFlag;
    }
  },
  actions: {
    async querySynchronousFlag() {
      this.synchronousFlag = (await api.querySynchronousFlag()).data;
      return this.synchronousFlag;
    },
    async getSynchronousFlagNameById(id: string) {
      if (!Array.isArray(this.synchronousFlag) || !this.synchronousFlag.length) {
        this.synchronousFlag = (await api.querySynchronousFlag()).data;
      }
      const synchronousFlag: ISynchronousFlag = this.synchronousFlag.find(
        (synchronousFlag: ISynchronousFlag) => synchronousFlag.id == id
      );
      return synchronousFlag ? synchronousFlag.flagName : undefined;
    }
  }
});
