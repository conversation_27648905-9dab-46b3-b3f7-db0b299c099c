<template>
  <div class="flex-bc py-2">
    <div class="flex-bc flex-1 overflow-hidden mr-2">
      <div>
        <div class="label">采购方公司名称</div>
        <CxTag type="custom" icon="icon-company-fill">{{ purchaseOrder?.buyerName || "--" }}</CxTag>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">订单来源</div>
        <div class="flex text-middle">
          <FontIcon icon="icon-orderdata-fill" class="!text-primary_light_5" />
          <span>{{ channel }}</span>
        </div>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">物资种类</div>
        <div class="flex-bc text-middle">
          <SubclassIcon :subclassCode="purchaseOrder?.subClassCode" />
          <span>{{ purchaseOrder?.subClassName }}</span>
        </div>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">项目名称</div>
        <div class="flex-bc text-middle">
          <FontIcon icon="icon-pm-fill" class="!text-primary_light_5" />
          <ShowTooltip className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl" :content="purchaseOrder?.prjName" />
        </div>
      </div>
      <el-divider direction="vertical" />
      <div class="flex-1 overflow-hidden">
        <div class="label">合同名称</div>
        <div class="flex text-middle">
          <FontIcon icon="icon-seal" class="!text-primary_light_5" />
          <ShowTooltip className="md:max-w-[25em] lg:max-w-[30em] xl:max-w-full " :content="purchaseOrder?.conName" />
        </div>
      </div>
    </div>

    <el-tooltip
      class="box-item"
      v-if="!isFiling"
      effect="dark"
      content="归档后的数据将不会自动同步和触发评分"
      placement="top-start"
    >
      <el-button
        size="large"
        type="primary"
        :icon="MessageBox"
        @click="filing"
        v-auth="PermissionKey.form.formPurchaseArchived"
        >归档</el-button
      >
    </el-tooltip>

    <el-tooltip
      v-else
      class="box-item"
      effect="dark"
      content="归档后，关联数据不会自动同步，默认列表隐藏，同步问题需手动处理。"
      placement="top-start"
    >
      <el-button size="large" @click="cancelFiling" v-auth="PermissionKey.form.formPurchaseArchived"
        >取消归档</el-button
      >
    </el-tooltip>
  </div>
  <!-- 归档审核 -->
  <FilingDialog v-model="visible" @fillingSuccess="fillingSuccess" />
</template>

<script setup lang="ts">
import CxTag from "@/components/CxTag/index.vue";
import ShowTooltip from "@/components/ShowTooltip";
import FilingDialog from "../components/filing-dialog/filing-dialog.vue";
import { usePurchaseOrderDetailStore, usePurchaseOrderStore } from "@/store/modules";
import { computed, ref } from "vue";
// import { TrackPointKey } from "@/consts";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";
import { MessageBox } from "@element-plus/icons-vue";
import SubclassIcon from "@/views/components/subclass-icon";
import { PermissionKey, emptyDefaultValue } from "@/consts";
import { PurchaseChannel } from "@/enums";

const purchaseOrderStore = usePurchaseOrderStore();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const purchaseOrder = computed(() => purchaseOrderDetailStore.purchaseOrder);
const channel = computed(() => {
  if (!purchaseOrder.value) {
    return emptyDefaultValue;
  }
  const channel = purchaseOrder.value.syncChannel;
  if (channel === PurchaseChannel.CSG_GuangZhou) {
    return "南网";
  } else if (channel === PurchaseChannel.EIP) {
    return "国网";
  }
  return emptyDefaultValue;
});

const isFiling = computed(() => {
  return purchaseOrder.value?.documentation;
});
const visible = ref<boolean>(false);

function filing() {
  visible.value = true;
}

/** 归档成功 */
function fillingSuccess() {
  purchaseOrderDetailStore.refreshPurchaseOrder();
}

async function cancelFiling() {
  if (!(await useConfirm("正在执行取消归档，是否继续？", "确认取消"))) {
    return;
  }
  const id = purchaseOrderDetailStore.purchaseOrderId;
  await purchaseOrderStore.purchaseOrderFilling(id);
  ElMessage.success("取消成功");
  purchaseOrderDetailStore.refreshPurchaseOrder();
}
</script>

<style scoped lang="scss">
.subclass-icons {
  @apply flex-bc gap-1 pl-2.5 pr-4 py-2;
  border-radius: 3px;
  border: 1px solid var(--el-color-primary-light-8);
  background: var(--el-color-primary-light-9);

  .value {
    @apply text-primary font-medium;
  }
}

.label {
  @apply text-base text-secondary mb-2;
}

.iconfont {
  @apply mr-2 text-primary_light_5 text-xxl leading-none;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
