import { IOption } from "@/models";
import { mapDescToOptions } from "@/utils/enum";

/**
 * 生产相关业务阶段枚举
 */
export enum ProductionStageEnum {
  /**
   * 生产单据
   */
  PRODUCTION_DOCUMENT = "PRODUCTION_DOCUMENT",

  /**
   * 开工
   */
  START_WORK = "START_WORK",

  /**
   * 排产计划
   */
  SCHEDULING_PLAN = "SCHEDULING_PLAN",

  /**
   * 原材料组部件检测
   */
  MATERIAL_INSPECTION = "MATERIAL_INSPECTION",

  /**
   * 出厂试验
   */
  FACTORY_TEST = "FACTORY_TEST",

  /**
   * 生产工艺及过程检测
   */
  PROCESS_MONITORING = "PROCESS_MONITORING",

  /**
   * 设备信息
   */
  EQUIPMENT_INFO = "EQUIPMENT_INFO"
}

/**
 * 枚举值到中文描述的映射
 */
export const ProductionStageEnumMapDesc: Record<ProductionStageEnum, string> = {
  [ProductionStageEnum.PRODUCTION_DOCUMENT]: "生产单据",
  [ProductionStageEnum.START_WORK]: "开工完工",
  [ProductionStageEnum.EQUIPMENT_INFO]: "设备信息",
  [ProductionStageEnum.SCHEDULING_PLAN]: "排产计划",
  [ProductionStageEnum.MATERIAL_INSPECTION]: "原材料组部件检测",
  [ProductionStageEnum.PROCESS_MONITORING]: "生产工艺及过程检测",
  [ProductionStageEnum.FACTORY_TEST]: "出厂试验"
};

export const ProductionStageOptions: Array<IOption> = mapDescToOptions(ProductionStageEnumMapDesc);
