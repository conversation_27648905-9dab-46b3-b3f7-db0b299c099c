export class Point {
  public x: number;
  public y: number;
  public cur: number;

  private fixedY: number;
  private readonly speed = 0.035;
  private readonly max = Math.random() * 4 + 6;

  constructor(index: number, x: number) {
    this.x = x;
    this.cur = index;
  }

  update() {
    this.cur += this.speed;
    this.y = this.fixedY + Math.sin(this.cur) * this.max;
  }

  setOffsetY(offsetY: number) {
    this.fixedY = offsetY;
  }
}
