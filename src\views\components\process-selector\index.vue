<template>
  <el-select v-if="loaded" class="w-full" v-model="modelValue" multiple filterable clearable placeholder="请选择工序">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <el-select v-else class="w-full" multiple filterable clearable placeholder="请选择工序" />
</template>

<script setup lang="ts">
import { WatchStopHandle, computed, nextTick, onMounted, onUnmounted, ref, watch, withDefaults } from "vue";
import { useVModels } from "@vueuse/core";
import * as processService from "@/api/process";

interface IOption {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    /** 物资种类 */
    subClassCode: string;
    /** 工艺路线 */
    processRoute?: string;
    /** 已选中id列表 */
    modelValue: Array<string>;
  }>(),
  {
    modelValue: () => [],
    subClassCode: ""
  }
);

const emits = defineEmits(["update:modelValue"]);

const { modelValue } = useVModels(props, emits);

const isEmpty = computed(() => !modelValue.value.length);

const options = ref<Array<IOption>>([]);

const loaded = ref(false);

let watchStopHandle: WatchStopHandle | null = null;

/** 工艺路线 工序配置映射 */
const processRouteOptionsMap: Record<string, Array<IOption>> = {};

/** 根据物资种类查出来的默认配置项 */
const defaultOptions: Array<IOption> = [];

/**
 * @description: 根据物资种类查询，全部工艺路线下的工序列表
 */
async function requestAllProcessListByRoute() {
  // 获取路线列表
  const { data: routeList } = await processService.getProcessRouteList(props.subClassCode);
  // 遍历路线列表，构建工序映射关系
  routeList.forEach(({ code: routeCode, processInfos: processList }) => {
    processRouteOptionsMap[routeCode] = processList.map(({ id, processName }) => {
      return {
        label: processName,
        value: id
      };
    });
  });
}

/**
 * @description: 根据物资种类查询默认工序,并设置默认选中
 */
async function requestDefaultProcessBySubclassCode() {
  const { data } = await processService.getProductionProcessListByCategoryCodeAndProductionStage(props.subClassCode);
  if (!data) {
    return;
  }
  data.forEach(({ id, processName }) => {
    defaultOptions.push({
      label: processName,
      value: id
    });
  });
  options.value = defaultOptions;
}

/**
 * @description: 初始化选中列表
 */
function initSelectedIdList() {
  nextTick(() => {
    if (isEmpty.value) {
      modelValue.value = defaultOptions.map(({ value }) => value);
    }
    if (props.processRoute) {
      options.value = processRouteOptionsMap[props.processRoute];
    }
  });
}

onMounted(async () => {
  await Promise.all([requestAllProcessListByRoute(), requestDefaultProcessBySubclassCode()]);
  initSelectedIdList();
  loaded.value = true;

  // 所有的观察者，都需要在初始化完列表选中项后才能开启，防止相互干扰

  // 订阅工艺路线，有工艺路线时使用工艺路线下的工序作为选择项；没有时，使用物资种类的默认工序列表作为选择项
  watchStopHandle = watch(
    () => props.processRoute,
    route => {
      if (route) {
        options.value = processRouteOptionsMap[route];
        modelValue.value = options.value.map(({ value }) => value);
      } else {
        options.value = defaultOptions;
        modelValue.value = defaultOptions.map(({ value }) => value);
      }
    }
  );
});

onUnmounted(() => {
  watchStopHandle && watchStopHandle();
});
</script>

<style scoped></style>
