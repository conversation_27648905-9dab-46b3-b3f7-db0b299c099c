<template>
  <section class="sync-order flex-1 overflow-hidden w-full flex flex-col">
    <!-- 同步订单  -->
    <el-radio-group class="mx-2 mb-4" v-model="currentChannel">
      <el-radio-button v-for="radio in orderSyncState.orderSyncChannelOption" :key="radio.value" :label="radio.value">
        {{ radio.label }}
      </el-radio-button>
    </el-radio-group>
    <Component :is="currentList" />
  </section>
</template>

<script setup lang="ts">
import EipSyncList from "./state-grid/state-grid.vue";
import IotSyncList from "./shanghai-platform/shanghai-platform.vue";
import CsgGuangzhouSyncList from "./csg-guangzhou-sync-list/index.vue";
import { computed, onMounted, ref } from "vue";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { useSalesOrderDetailStore, useSalesOrderSyncInfo } from "@/store/modules";
import { OrderType, PurchaseChannel } from "@/enums";
import { useOrderSyncChannelHook } from "@/views/order/hooks";

type EipSyncListComponent = typeof EipSyncList;
type IotSyncListComponent = typeof IotSyncList;
type CsgGuangzhouSyncListComponent = typeof CsgGuangzhouSyncList;
type ListComponent = EipSyncListComponent | IotSyncListComponent | CsgGuangzhouSyncListComponent | undefined;

const salesOrderDetailStore = useSalesOrderDetailStore();
const { getOrderSyncChannel, orderSyncState } = useOrderSyncChannelHook();

/** 当前所选渠道 */
const currentChannel = ref<SyncOrderTabEnum>();

const salesOrderSyncInfoStore = useSalesOrderSyncInfo();

// 获取当前所选择的平台
const currentList = computed(() => {
  const channelIdentify = currentChannel.value;
  let listComponent: ListComponent = undefined;
  switch (channelIdentify) {
    case SyncOrderTabEnum.SYNC_STATE__GRID_ORDER:
      listComponent = EipSyncList;
      salesOrderSyncInfoStore.setChannel(PurchaseChannel.EIP);
      break;
    case SyncOrderTabEnum.SYNC_SHANGHAI_IOT:
      listComponent = IotSyncList;
      salesOrderSyncInfoStore.setChannel(PurchaseChannel.IOT);
      break;
    case SyncOrderTabEnum.SYNC_CSG_GUANGZHOU:
      listComponent = CsgGuangzhouSyncList;
      salesOrderSyncInfoStore.setChannel(PurchaseChannel.CSG_GuangZhou);
      break;
  }
  if (channelIdentify) {
    salesOrderSyncInfoStore.setSyncDetailType(channelIdentify);
  }
  return listComponent;
});

onMounted(async () => {
  // 获取订单同步渠道
  const orderSyncChannel = await salesOrderSyncInfoStore.getOrderSyncChannel({
    orderId: salesOrderDetailStore.saleOrderId,
    viewMode: OrderType.SALES
  });
  getOrderSyncChannel(orderSyncChannel);
  currentChannel.value = orderSyncState.orderSyncChannelOption[0].value as SyncOrderTabEnum;
});
</script>

<style lang="scss">
@import "@/views/order/sales-order/detail/styles/mixin";

.state-grid-order-sync-detail-dialog {
  @include full-screen-dialog;
}

.order-list {
  width: calc(100% - 16px);
  margin-left: 8px;
}
</style>
