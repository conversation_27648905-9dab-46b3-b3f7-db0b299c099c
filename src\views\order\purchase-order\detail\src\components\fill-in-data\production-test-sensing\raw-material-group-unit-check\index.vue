<template>
  <div class="raw-material h-full flex flex-col overflow-hidden">
    <Component :is="rawMaterialInspectComp" />
  </div>
</template>

<script setup lang="ts">
import NormalRawMaterialInspect from "./normal/index.vue";
import ACRawMaterialInspect from "./armour-clamp/index.vue";
import { inject, onMounted, shallowRef } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const rawMaterialInspectComp = shallowRef();

onMounted(() => {
  // 处理显示的组件
  handleShowComponent();
});

/**
 * 根据传入参数显示不同的组件
 */
function handleShowComponent() {
  switch (prodCtx?.detailMaterialCategory) {
    case EMaterialCategory.Normal:
      rawMaterialInspectComp.value = NormalRawMaterialInspect;
      break;
    case EMaterialCategory.ArmourClamp:
      rawMaterialInspectComp.value = ACRawMaterialInspect;
      break;
    default:
      break;
  }
}
</script>

<style scoped lang="scss"></style>
