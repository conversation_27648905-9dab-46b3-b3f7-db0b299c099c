import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { ISchedulingPlan, ISchedulingPlanForm, ISchedulingPlanReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const querySchedulingPlan = (data: ISchedulingPlanReq) => {
  const url: string = withApiGateway("admin-api/southgrid/productionPlans/pageList");
  return http.post<ISchedulingPlanReq, IListResponse<ISchedulingPlan>>(url, {
    data
  });
};

/** 查询 列表  */
export const querySchedulingPlanList = (data: ISchedulingPlanReq) => {
  const url: string = withApiGateway("admin-api/southgrid/productionPlans/list");
  return http.post<ISchedulingPlanReq, IResponse<Array<ISchedulingPlan>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getSchedulingPlanById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/productionPlans/${id}`);
  return http.get<string, IResponse<ISchedulingPlan>>(url);
};

/** 新增 */
export const createSchedulingPlan = (data: ISchedulingPlanForm) => {
  return http.post<ISchedulingPlanForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/productionPlans/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateSchedulingPlan = (data: ISchedulingPlanForm) => {
  return http.put<ISchedulingPlanForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/productionPlans/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteSchedulingPlanById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/productionPlans/${id}`));
};
