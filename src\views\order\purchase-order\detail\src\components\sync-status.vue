<template>
  <div class="flex-c flex-col px-4" :class="info.clazz">
    <FontIcon :icon="info.icon" class="icon mb-2.5" />
    <span class="text-center whitespace-nowrap w-[5em]">{{ info.text }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  success?: boolean;
}>();

const info = computed(() => {
  if (props.success == null) {
    return {
      icon: "icon-warning-fill",
      text: "未同步",
      clazz: "info"
    };
  }
  if (props.success) {
    return {
      icon: "icon-success-fill",
      text: "已触发同步",
      clazz: "success"
    };
  }
  return {
    icon: "icon-fail-fill",
    text: "同步失败",
    clazz: "fail"
  };
});
</script>

<style scoped>
.icon {
  font-size: 30px;
}

.success {
  color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.fail {
  color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

.info {
  color: var(--el-color-info);
  background: var(--el-color-info-light-9);
}
</style>
