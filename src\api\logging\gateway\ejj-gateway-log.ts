import { withApiGateway } from "@/api/util";
import { http } from "@/utils/http";
import {
  IEJJGatewayList,
  IEJJInterface,
  IListResponse,
  IResponse,
  ISearchEJJGatewayReq,
  ISearchGatewayReq
} from "@/models";

export function getEjjInterfaceOptions(categoryCode: string) {
  const url = withApiGateway(`admin-api/ejj/equipment/queryInterfaceByCategory`);
  return http.get<void, IResponse<IEJJInterface>>(url, {
    params: {
      categoryCode
    }
  });
}

export function getEjjGatewayLogList(params: ISearchEJJGatewayReq) {
  const url = withApiGateway(`admin-api/infra/ejj-gateway/page`);
  return http.post<ISearchGatewayReq, IListResponse<IEJJGatewayList>>(url, { data: params });
}

export function getResponseContentApi(id: string) {
  const url = withApiGateway(`admin-api/infra/ejj-gateway/resp/${id}`);
  return http.get<void, IResponse<string>>(url);
}
