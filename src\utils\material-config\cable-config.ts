import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 中压电缆
 */
export const mediumVoltageCableConfig: MaterialSubClassConfig = {
  name: "中压电缆",
  subClassCode: MaterialSubclassCode.MEDIUM_VOLTAGE_CABLE,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 高压电缆
 */
export const highVoltageCableConfig: MaterialSubClassConfig = {
  name: "高压电缆",
  subClassCode: MaterialSubclassCode.HIGH_VOLTAGE_CABLE,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 导、地线
 */
export const wireGroundConfig: MaterialSubClassConfig = {
  name: "导、地线",
  subClassCode: MaterialSubclassCode.WIRE_GROUND,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * OPGW-OPPC光缆
 */
export const opgwOppcConfig: MaterialSubClassConfig = {
  name: "OPGW-OPPC光缆",
  subClassCode: MaterialSubclassCode.OPGW_OPPC,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * ADSS光缆
 */
export const adssConfig: MaterialSubClassConfig = {
  name: "ADSS光缆",
  subClassCode: MaterialSubclassCode.ADSS,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 电缆中间接头
 */
export const cableJointConfig: MaterialSubClassConfig = {
  name: "电缆中间接头",
  subClassCode: MaterialSubclassCode.CABLE_JOINT,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 电缆终端
 */
export const cableTerminalConfig: MaterialSubClassConfig = {
  name: "电缆终端",
  subClassCode: MaterialSubclassCode.CABLE_TERMINAL,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 物资品类-线缆
 */
export const cableConfig: MaterialCategoryConfig = {
  name: "线缆",
  categoryCode: MaterialCategoryCode.CABLE,
  subClassMap: {
    mediumVoltageCableConfig,
    [MaterialSubclassCode.MEDIUM_VOLTAGE_CABLE]: mediumVoltageCableConfig,
    highVoltageCableConfig,
    [MaterialSubclassCode.HIGH_VOLTAGE_CABLE]: highVoltageCableConfig,
    wireGroundConfig,
    [MaterialSubclassCode.WIRE_GROUND]: wireGroundConfig,
    opgwOppcConfig,
    [MaterialSubclassCode.OPGW_OPPC]: opgwOppcConfig,
    adssConfig,
    [MaterialSubclassCode.ADSS]: adssConfig,
    cableJointConfig,
    [MaterialSubclassCode.CABLE_JOINT]: cableJointConfig,
    cableTerminalConfig,
    [MaterialSubclassCode.CABLE_TERMINAL]: cableTerminalConfig
  }
};

export default cableConfig;
