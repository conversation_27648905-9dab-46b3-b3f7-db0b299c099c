<template>
  <el-dialog
    title="编辑报工"
    v-model="editVisible"
    align-center
    class="default"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <ReportWorkForm
      mode="edit"
      ref="editFormRef"
      :subClassCode="subClassCode"
      :processIds="processIds"
      :minClassCode="minClassCode"
      v-loading="loading"
    />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import ReportWorkForm from "@/views/components/work-order-and-report-work/add-edit-report-work-dialog/report-work-form/index.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ICreateReportWork, IWorkReportSync } from "@/models";
import { nextTick, ref } from "vue";
import { getReportWorkById, editReportWork } from "@/api/report-work";

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof ReportWorkForm>>(updateReportWork);
const { loading, handlePatchValue } = usePatchValue(patchValue);

const subClassCode = ref<string>();
const processIds = ref<string>();
const minClassCode = ref<string>();
const workReportId = ref<string>();

function openEditDialog(data: IWorkReportSync) {
  editVisible.value = true;
  subClassCode.value = data.subClassCode || data.subclassCode;
  processIds.value = data.processIds;
  minClassCode.value = data.minClassCode;
  // 注意：EIP平台接口里返回的报工ID是workReportId， IOT、CSG的是productReportId
  workReportId.value = data.workReportId || data.productReportId;
  stateGridOrderSyncEditCtx.editData = {
    id: data.workReportId
  };
  handlePatchValue(workReportId.value);
}

async function patchValue(workReportId: string) {
  const { data } = await getReportWorkById(workReportId);
  nextTick(() => {
    editFormRef.value.initFormValue(data);
  });
}

async function updateReportWork() {
  if (!editFormRef.value) {
    return;
  }
  const verified = await editFormRef.value.validateForm();
  if (!verified) {
    return;
  }
  const formValue = editFormRef.value.getFormValue();
  const params: ICreateReportWork = { id: workReportId.value, ...formValue };
  const { data: result } = await editReportWork(params);
  return result;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
