import { TableColumnRenderer } from "@pureadmin/table";
import { SalesOrderStatus, TableWidth, ColumnWidth, SalesLinkStepColorDesc, SalesLinkStepMapDesc } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { dateFormat, fullDateFormat } from "@/consts";
import { EnumCell } from "@/components/TableCells";
import CxTag from "@/components/CxTag/index.vue";
import { RouterLink } from "vue-router";

export function useColumns(t) {
  const { dateFormatter } = useTableCellFormatter();

  const columns: TableColumnList = [
    {
      label: "选择",
      prop: "select",
      type: "selection",
      fixed: "left",
      width: TableWidth.check
    },
    {
      label: "关注",
      prop: "follow",
      fixed: "left",
      width: TableWidth.check,
      align: "center",
      slot: "follow"
    },
    {
      label: "销售订单号",
      prop: "soNo",
      fixed: "left",
      sortable: "custom",
      minWidth: ColumnWidth.Char14,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <RouterLink class="text-primary" to={{ name: "salesOrderDetail", params: { id: data.row.id } }}>
            {data.row.soNo}
          </RouterLink>
        );
      }
    },
    {
      label: "采购订单号",
      prop: "poNo",
      minWidth: 150,
      slot: "poNo"
    },
    {
      label: "合同编号",
      prop: "conCode",
      sortable: "custom",
      minWidth: TableWidth.largeName
    },
    {
      label: "物资品类",
      slot: "categoryName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "采购方公司名称",
      prop: "buyerName",
      minWidth: TableWidth.largeName
    },
    {
      label: "项目名称",
      prop: "prjName",
      minWidth: TableWidth.largeXgOperation
    },
    {
      label: "标识",
      minWidth: TableWidth.name,
      slot: "matSyncFlagId"
    },
    {
      label: "订单进度",
      prop: "orderProgress",
      minWidth: ColumnWidth.Char10,
      cellRenderer(data: TableColumnRenderer) {
        return <div>{EnumCell(data, SalesOrderStatus, "SalesOrderStatus")}</div>;
      }
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: TableWidth.largeName
    },
    {
      label: "交货日期",
      prop: "deliveryDate",
      sortable: "custom",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter(dateFormat)
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "当前填报环节",
      prop: "linkStep",
      fixed: "right",
      minWidth: TableWidth.suborder,
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.linkStep ? (
          <CxTag class={SalesLinkStepColorDesc[data.row.linkStep]}>{t(SalesLinkStepMapDesc[data.row.linkStep])}</CxTag>
        ) : null;
      }
    },
    // {
    //   label: "同步状态",
    //   prop: "syncResult",
    //   fixed: "right",
    //   width: TableWidth.suborder,
    //   formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
    // },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
