<template>
  <el-form ref="dynamicFormRef" class="p-0" :model="formModel" :rules="formRules">
    <el-table :data="props.fields" style="width: 100%" border>
      <el-table-column prop="measureItemName" label="检测项目" width="180">
        <template #default="{ row }">
          <div class="item-name">
            {{ row.measureItemName }}<span v-if="row.required" class="isRequire">*</span>
          </div></template
        >
      </el-table-column>
      <el-table-column prop="dataValue" align="center" label="检测值">
        <template #default="{ row, $index }">
          <el-form-item :prop="row.measureItemCode">
            <!-- 文本输入 -->
            <el-input
              v-if="row[props.fieldType] === 'string' || row[props.fieldType] === 'text'"
              v-model="formModel[row[props.fieldKey as string]]"
              type="text"
              :placeholder="`请输入${row.measureItemName}`"
            />

            <!-- 数字输入 -->
            <el-input-number
              v-else-if="row[props.fieldType] === 'number'"
              controls-position="right"
              v-model="formModel[row[props.fieldKey as string]]"
              class="!w-full"
              :placeholder="`请输入${row.measureItemName}`"
            />

            <!-- 下拉选择 -->
            <el-select
              v-else-if="row[props.fieldType] === 'select'"
              v-model="formModel[row[props.fieldKey as string]]"
              :placeholder="`请选择${row.measureItemName}`"
              style="width: 100%"
            >
              <el-option
                v-for="item in JSON.parse(row.conConfig)"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>

            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="row[props.fieldType] === 'date' || row[props.fieldType] === 'datetime'"
              v-model="formModel[row[props.fieldKey as string]]"
              type="date"
              :placeholder="`选择${row.measureItemName}`"
              style="width: 100%"
            />

            <!-- 开关 -->
            <el-radio-group
              v-else-if="row[props.fieldType] === 'switch'"
              v-model="formModel[row[props.fieldKey as string]]"
            >
              <el-radio v-for="item in JSON.parse(row.conConfig)" :key="item.value" :label="item.value">{{
                item.name
              }}</el-radio>
            </el-radio-group>

            <!-- 附件上传 -->
            <dynamic-table-file
              v-else-if="row[props.fieldType] === 'file'"
              :ref="`uploadRef${$index}`"
              v-model="formModel[row[props.fieldKey as string]]"
            />

            <!-- 默认文本输入 -->
            <el-input
              v-else
              v-model="formModel[row[props.fieldKey as string]]"
              :placeholder="`请输入${row.measureItemName}`"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="measureUnit" label="单位" width="100" />
    </el-table>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { DataValueObjModel } from "@/models";
import DynamicTableFile from "../dynamic-table-file/index.vue";

// 暴露给父组件的方法
defineExpose({
  validateForm,
  resetFields,
  getFormValue
});

const props = defineProps<{
  fields: Array<DataValueObjModel>;
  modelValue: Record<string, any>;
  fieldKey: string;
  fieldType: string;
}>();

const dynamicFormRef = ref<FormInstance>();
const formModel = ref<Record<string, any>>({});

// 初始化表单模型
const initFormModel = () => {
  formModel.value = props.modelValue;
};

// 监听props变化，初始化表单
watch(() => props.fields, initFormModel, { immediate: true });

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const params = [];
  props.fields.forEach(item => {
    if (formModel.value[item[props.fieldKey as string]] != "") {
      const newItem = {
        ...item,
        dataValue: formModel.value[item[props.fieldKey as string]]
      };
      if (item[props.fieldType] == "file") {
        newItem.fileInfo = formModel.value[item[`${props.fieldKey}`]];
        newItem.dataValue = newItem.fileInfo?.id;
      }
      params.push(newItem);
    }
  });
  return Object.assign([], params as Array<DataValueObjModel>);
}

// 表单验证规则
const formRules = computed<FormRules>(() => {
  const rules: FormRules = {};
  props.fields.forEach(item => {
    rules[item.measureItemCode] = getFieldRules(item);
  });
  return rules;
});
// 获取单个字段的验证规则
const getFieldRules = (field: DataValueObjModel) => {
  const rules = [];

  if (field.required) {
    rules.push({
      required: true,
      message: `${field.measureItemAlias}不能为空`,
      trigger: ["blur", "change"]
    });
  }

  return rules;
};

// 暴露验证方法
async function validateForm() {
  return await dynamicFormRef.value.validate(valid => valid);
}

// 暴露重置方法
function resetFields() {
  dynamicFormRef.value?.resetFields();
  initFormModel();
}
</script>

<style lang="scss" scoped>
.isRequire {
  color: #f56c6c;
}

.el-form-item {
  margin-bottom: 0;
}
</style>
