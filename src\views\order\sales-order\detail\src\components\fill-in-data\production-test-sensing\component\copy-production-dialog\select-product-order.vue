<template>
  <div class="select-product-order flex-1 flex flex-col">
    <div class="add-raw-material flex justify-between items-center mb-4">
      <div class="search flex items-center w-2/3">
        <el-input class="mr-4 flex-1" v-model="keywords" clearable :placeholder="placeholderText" />
        <el-checkbox
          class="!mr-2"
          v-model="withSameMaterial"
          label="相同物料"
          size="large"
          @change="searchRawMaterial"
        />
        <el-button type="primary" @click="searchRawMaterial">搜索</el-button>
      </div>
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="selectOrderStore.dataList"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      showOverflowTooltip
      @row-click="rowClick"
      @current-change="currentChange"
      @page-current-change="pageNoChange"
      @page-size-change="pageSizeChange"
    >
      <template #radioGroup="{ row }">
        <el-radio-group class="text-center" v-model="currentCheckId">
          <el-radio class="text-center" :label="row.id">{{ "" }}</el-radio>
        </el-radio-group>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { h, inject, onMounted, onUnmounted, ref, watchEffect } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useTableConfig } from "@/utils/useTableConfig";
import { IProductOrder } from "@/models/product-order";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, TableWidth } from "@/enums";
import {
  useSalesProductionTestSensingStore,
  useSelectProductOrderStore
} from "@/store/modules/production-test-sensing";
import { IOrderListReq } from "@/models/production-test-sensing";
import { COPY_FROM_ORDER_TOKEN } from "./tokens";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { computedAsync } from "@vueuse/core";

const keyWordAliasHook = usekeyWordAliasHook();
const props = withDefaults(
  defineProps<{
    currentSelect?: IProductOrder;
    dataType?: string;
  }>(),
  {}
);

const emit = defineEmits<{
  (e: "currentChange", data: IProductOrder): void;
}>();

const copyCtx = inject(COPY_FROM_ORDER_TOKEN);
const selectOrderStore = useSelectProductOrderStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const keywords = ref<string>();
const withSameMaterial = ref<boolean>(true);
const placeholderText = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    copyCtx.isCable ? `请输入${KeywordAliasEnum.IPO_NO}/物料名称` : "请输入工单号/物料名称",
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const { pagination } = useTableConfig();
const queryParams = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize
};
const currentCheckId = ref();

const cableColumn = [
  {
    label: "生产订单号",
    prop: "ipoNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      })
  },
  {
    label: "物料名称",
    prop: "materialsName"
  }
];
const notCableColumn = [
  {
    label: "生产工单号",
    prop: "woNo"
  },
  {
    label: "物料名称",
    prop: "materialName"
  }
];
const columns: TableColumnList = [
  {
    type: "index",
    label: "选择",
    width: TableWidth.check,
    slot: "radioGroup"
  },
  ...(copyCtx.isCable ? cableColumn : notCableColumn)
];
const loading = ref<boolean>(false);
const initProductOrderList = useLoadingFn(getProductOrderData, loading);

onMounted(async () => {
  Object.assign(queryParams, { dataType: props.dataType, withSameMaterial: withSameMaterial.value });
  await initProductOrderList(queryParams);
  currentCheckId.value = props.currentSelect?.id;
});

watchEffect(() => {
  pagination.total = selectOrderStore.total;
});

/** 获取生产订单数据 */
async function getProductOrderData(queryParams?: IOrderListReq) {
  copyCtx.isCable
    ? await selectOrderStore.getProductOrderList(productionTestSensingStore.dataId, queryParams)
    : await selectOrderStore.getWorkOrderList(productionTestSensingStore.dataId, queryParams);
}
/** 搜索生产订单数据 */
async function searchRawMaterial() {
  Object.assign(queryParams, { keywords: keywords.value, withSameMaterial: withSameMaterial.value });
  await initProductOrderList(queryParams);
}
/** 单行点击 */
function rowClick(row: IProductOrder) {
  currentCheckId.value = row.id;
}

/** 选择行改变 */
function currentChange(data: IProductOrder) {
  emit("currentChange", data);
}

/** 页码改变 */
function pageNoChange(pageNo: number) {
  Object.assign(queryParams, { pageNo });
  initProductOrderList(queryParams);
}

/** 页码数量改 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  Object.assign(queryParams, { pageNo: pagination.currentPage, pageSize });
  initProductOrderList(queryParams);
}

onUnmounted(() => {
  currentCheckId.value = null;
});
</script>

<style scoped lang="scss"></style>
