<template>
  <div class="select-raw-material">
    <div class="add-raw-material flex justify-between items-center mb-4">
      <div class="search flex w-1/3">
        <el-input class="mr-4 flex-1" v-model="keywords" clearable placeholder="请输入原材料编号/名称" />
        <el-button type="primary" @click="searchRawMaterial">查询</el-button>
      </div>
      <el-button type="primary" @click="addRawMateril()">
        <FontIcon class="mr-2" icon="icon-plus" />
        新增原材料
      </el-button>
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="rawMaterialGroupStore.rawMaterialList"
      :height="400"
      :max-height="400"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      @row-click="rowClickChange"
      @current-change="handleCurrentChange"
      @page-current-change="pageCurrentChange"
      @page-size-change="pageSizeChange"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
  </div>

  <!-- 新增原材料 -->
  <el-dialog
    v-model="selectAddRawMaterialRef"
    title="新增原材料"
    class="default"
    top="5vh"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    @close="closeSelectAddRawMaterial()"
  >
    <div class="add-raw-material-inspec">
      <SelectAddRawMaterial ref="selectAddRawMaterialIns" @saveSuccess="saveSuccess" />
    </div>
    <template #footer>
      <el-button @click="closeSelectAddRawMaterial()">取消</el-button>
      <el-button type="primary" @click="saveSelectAddRawMaterial()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import SelectAddRawMaterial from "../select-add-raw-material/index.vue";
import { TableWidth } from "@/enums";
import { ref, onMounted, inject } from "vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { useTableConfig } from "@/utils/useTableConfig";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import { ISearchRawMaterialListReq, IRawMaterialList } from "@/models/raw-material/i-raw-material-res-v2";
import { ElMessage } from "element-plus";
import { dateFormat } from "@/consts";
import { watchEffect } from "vue";

const ctx = inject<{
  processInfo: IProductionMaterialProcess;
}>("addRawMaterialToken");

const { singleSelectFormatter } = useTableCellFormatter();
const rawMaterialGroupStore = useRawMaterialGroupUnitStore();
const selectRawMaterial = ref<IRawMaterialList>();
const selectedId = ref<string>();
const { dateFormatter } = useTableCellFormatter();
const columns: TableColumnList = [
  {
    label: "选择",
    prop: "id",
    width: TableWidth.check,
    fixed: "left",
    formatter: singleSelectFormatter(selectedId)
  },
  {
    label: "原材料编号",
    prop: "code",
    minWidth: TableWidth.order
  },
  {
    label: "原材料名称",
    prop: "name",
    minWidth: TableWidth.name
  },
  {
    label: "规格型号",
    prop: "modelCode",
    width: TableWidth.type
  },
  {
    label: "计量单位",
    prop: "partUnit",
    width: TableWidth.unit
  },
  {
    label: "原材料批次号",
    prop: "materialBatchNo",
    width: TableWidth.order
  },
  {
    label: "生产日期",
    prop: "productionDate",
    minWidth: TableWidth.date,
    formatter: dateFormatter(dateFormat)
  }
];
const { pagination } = useTableConfig();
const selectAddRawMaterialRef = ref<boolean>(false);
const keywords = ref();
const pageQuery = {
  pageNo: 1,
  pageSize: pagination.pageSize,
  processCode: ctx.processInfo?.processCode
};
const selectAddRawMaterialIns = ref();

// 获取新增的原材料数据
onMounted(() => {
  getRawMaterialList();
});

watchEffect(() => {
  pagination.total = rawMaterialGroupStore.rawMaterialTotal;
});

/**
 * 点击行动作
 */
const rowClickChange = (row: any) => {
  selectedId.value = row.id;
  selectRawMaterial.value = row;
  rawMaterialGroupStore.selectRawMaterialList = selectRawMaterial.value;
};
/**
 * 单选
 */
const handleCurrentChange = (val: any) => {
  selectedId.value = val?.id;
  selectRawMaterial.value = val;
  rawMaterialGroupStore.selectRawMaterialList = selectRawMaterial.value;
};
/** 单选 */
const closeSelectAddRawMaterial = () => {
  selectAddRawMaterialRef.value = false;
};

/** 获取原材料列表数据 */
async function getRawMaterialList() {
  const params = {
    ...pageQuery
  };
  await queryRawMaterilListPage(params);
}

/**
 * 查询
 */
const searchRawMaterial = () => {
  pagination.currentPage = 1;
  const params = {
    ...pageQuery,
    pageSize: pagination.pageSize,
    keyWords: keywords.value
  };
  queryRawMaterilListPage(params);
};
/**
 * 页面数量改变
 */
const pageSizeChange = (pageSize: number) => {
  pagination.currentPage = 1;
  const params = {
    ...pageQuery,
    pageSize
  };
  queryRawMaterilListPage(params);
};
/**
 * 页码改变
 */
const pageCurrentChange = (pageNo: number) => {
  const params = {
    ...pageQuery,
    pageNo
  };
  queryRawMaterilListPage(params);
};
/**
 * 查询数据
 */
const queryRawMaterilListPage = async (params: ISearchRawMaterialListReq) => {
  await rawMaterialGroupStore.getConfigOfRawMaterialList(params);
};

/**
 * 点击新增原材料
 */
const addRawMateril = () => {
  selectAddRawMaterialRef.value = true;
};

/**
 * 保存新增的原材料信息
 */
const saveSelectAddRawMaterial = () => {
  selectAddRawMaterialIns.value.saveAddRawMaterial();
};
/**
 * 新增原材料数据成功
 */
const saveSuccess = () => {
  ElMessage.success("新增成功");
  closeSelectAddRawMaterial();
  getRawMaterialList();
  selectAddRawMaterialIns.value.resetForm();
};
</script>

<style scoped lang="scss"></style>
