import { defineStore } from "pinia";
import { IDictionaryOption } from "@/models";
import * as api from "@/api/platform/dictionary";

export const useUserDictionaryStore = defineStore("cx-user-dictionary", () => {
  const _retryMap = new Map<string, number>();
  const _cbMap = new Map<string, Array<(options: Array<IDictionaryOption>) => void>>();
  const _optionMap = new Map<string, Array<IDictionaryOption>>();

  async function getOptions(parentCode: string, subClassCode: string): Promise<Array<IDictionaryOption>> {
    const key = _getUniqKey(parentCode, subClassCode);
    const options = _getCacheOptions(key);
    if (options) {
      return options;
    }
    return new Promise(resolve => {
      if (_cbMap.has(key)) {
        _cbMap.get(key).push(resolve);
        return;
      }
      _cbMap.set(key, [resolve]);
      _fetchOptions(parentCode, subClassCode);
    });
  }

  async function getName(parentCode: string, subClassCode: string, code: string | number): Promise<string> {
    const options = await getOptions(parentCode, subClassCode);
    code = String(code);
    return options.find(option => option.code === code)?.name ?? "";
  }

  function $reset() {
    _cbMap.clear();
    _retryMap.clear();
    _optionMap.clear();
  }

  function _getCacheOptions(key: string) {
    if (!key) {
      return [];
    }
    return _optionMap.get(key);
  }

  function _fetchOptions(parentCode: string, subClassCode: string) {
    const key = _getUniqKey(parentCode, subClassCode);
    api
      .getDictionaryOptions({ subClassCode, parentCode })
      .then(res => {
        const options = res.data;
        _optionMap.set(key, options);
        _retryMap.delete(key);
        _cbMap.get(key).forEach(cb => cb(options));
        _cbMap.delete(key);
      })
      .catch(() => _retry(parentCode, subClassCode));
  }

  function _retry(parentCode: string, subClassCode: string) {
    const key = _getUniqKey(parentCode, subClassCode);
    let retryCount = _retryMap.get(key) || 0;
    retryCount += 1;
    _retryMap.set(key, retryCount);
    setTimeout(() => _fetchOptions(parentCode, subClassCode), retryCount * 1000);
  }

  function _getUniqKey(parentCode: string, subClassCode: string): string {
    if (!parentCode || !subClassCode) {
      return "";
    }
    return `${parentCode}${subClassCode}`;
  }

  return {
    getOptions,
    getName,
    $reset
  };
});
