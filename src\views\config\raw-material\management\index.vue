<template>
  <!-- 原材料检 -->
  <div class="flex h-full py-5 px-6">
    <!-- 原材料类型列表 -->
    <div
      class="raw-material-type w-1/5 bg-bg_color mr-4 overflow-hidden"
      v-loading="rawMaterialStore.$state.rawMaterialTypeOptionsLoading"
    >
      <div class="p-2 h-full">
        <div class="m-2 mb-3">
          <el-input
            class="mr-2 input-group"
            v-model="keywords"
            clearable
            placeholder="原材料类型"
            @input="queryRawMaterialType"
            :prefix-icon="Search"
          />
        </div>
        <el-scrollbar height="calc(100% - 40px)">
          <div class="flex flex-col px-2 pb-3">
            <template v-for="(process, index) in rawMaterialTypeLists" :key="index">
              <span
                class="info-item"
                :class="index === currenIndex ? 'bg_primary' : ''"
                @click="clickProcessInfo(process, index)"
                >{{ process.processName }}</span
              >
            </template>
            <div class="list" v-if="!rawMaterialTypeLists.length">
              <el-empty :image-size="120">
                <template #image> <EmptyData /> </template>
              </el-empty>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="raw-material-container flex-1 flex overflow-hidden">
      <!-- 原材料列表  -->
      <div
        class="raw-material-info w-1/4 py-4 pl-4 bg-bg_color mr-4"
        v-loading="rawMaterialStore.$state.rawMaterialListLoading"
      >
        <RawMaterialList
          :activeRawMaterial="rawMaterialStore.currentClickRawMaterial"
          ref="rawMaterialDetailInstance"
          @addRawMaterial="addRawMaterial"
          :processInfo="currentProcessInfo"
        />
      </div>

      <!-- 原材料详情 -->
      <div class="raw-material-detail flex-1 overflow-hidden">
        <RawMaterialDetailAndCheck @editRawMaterial="editRawMaterial" />
      </div>
    </div>
  </div>

  <!-- 新增原材料 -->
  <el-dialog
    v-model="addRawMaterialRef"
    :title="addDiagTitle"
    class="default"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeRawMaterialDialog()"
  >
    <AddRawMaterial ref="addRawMaterialInstance" :processInfo="currentProcessInfo" @saveSuccess="saveSuccess($event)" />
    <template #footer>
      <div class="footer">
        <el-button @click="closeRawMaterialDialog()">取消</el-button>
        <el-button type="primary" @click="saveRawMaterial()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import RawMaterialList from "../raw-material/index.vue";
import RawMaterialDetailAndCheck from "../raw-material-detail/index.vue";
import AddRawMaterial from "../add-raw-material/index.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { IProductionStageProcessCode } from "@/models/raw-material/i-raw-material-res-v2";
import { usePageStoreHook } from "@/store/modules/page";
import { Search } from "@element-plus/icons-vue";
import { useDebounceFn, watchOnce } from "@vueuse/core";
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";

// 设置标题
usePageStoreHook().setTitle("原材料");

const rawMaterialStore = useRawMaterialV2Store();
const rawMaterialTypeList = computed(() => rawMaterialStore.rawMaterialTypeOptions);
const currenIndex = ref(0);
const currentProcessInfo = ref<IProductionStageProcessCode>(null);
// 新增或者编辑原材料
const addRawMaterialInstance = ref<InstanceType<typeof AddRawMaterial>>();
const addRawMaterialRef = ref<boolean>(false);
const addDiagTitle = ref<string>("新增原材料");
const rawMaterialDetailInstance = ref();
const keywords = ref("");
const rawMaterialTypeLists = ref([]);

const queryRawMaterialType = useDebounceFn(getRawMaterialType, 200);
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

/** 查询原材料类型数据 */
onMounted(() => {
  rawMaterialStore.getRawMaterialTypeOptions();
});

watchOnce(rawMaterialTypeList, (newVal: IProductionStageProcessCode[]) => {
  if (newVal.length) {
    clickProcessInfo(newVal[0], 0);
    rawMaterialTypeLists.value = newVal;
  }
});

/**
 * 搜索原材料类型数据
 */
function getRawMaterialType() {
  rawMaterialTypeLists.value = rawMaterialTypeList.value.filter(
    item => item.processName.includes(keywords.value) || item.processCode.includes(keywords.value)
  );
  if (!rawMaterialTypeLists.value.length) {
    rawMaterialStore.rawMaterialList = [];
    rawMaterialStore.initRawMaterInspectDetail();
    return;
  }
  clickProcessInfo(rawMaterialTypeLists.value[0], 0);
}

/**
 * 点击原材料
 */
function clickProcessInfo(processInfo: IProductionStageProcessCode, index: number) {
  currentProcessInfo.value = processInfo;
  currenIndex.value = index;
  // 获取当前工序下原材料信息
  rawMaterialStore.initCurrentRawMaterialData();
  searchRawMaterialList(processInfo);
  // 设置是否是金具
  rawMaterialStore.setIsArmourClamp(processInfo.subclassCode);
}

/**
 * 查询原材料数据
 */
const searchRawMaterialList = (processInfo: IProductionStageProcessCode) => {
  const { processCode } = processInfo;
  rawMaterialStore.queryRawMaterialList({ processCode, ...pageInfo });
};

/**
 * 新增原材料弹框
 */
const addRawMaterial = () => {
  addRawMaterialRef.value = true;
  addDiagTitle.value = "新增原材料";
  rawMaterialStore.setAddOrEditRawMaterial(true);
};

/**
 * 关闭新增原材料弹框
 */
const closeRawMaterialDialog = () => {
  addRawMaterialRef.value = false;
};

/**
 * 编辑原材料
 */
const editRawMaterial = () => {
  addRawMaterialRef.value = true;
  addDiagTitle.value = "编辑原材料";
  rawMaterialStore.setAddOrEditRawMaterial(false);
};

/**
 * 保存新增的原材料
 */
const saveRawMaterial = () => {
  addRawMaterialInstance.value.saveAddRawMaterial();
};

/**
 * 保存新增的原材料成功
 */
const saveSuccess = (formMode: boolean) => {
  ElMessage.success(formMode ? "新增成功" : "编辑成功");
  closeRawMaterialDialog();
  refreshRawMaterialList();
};

/**
 * 刷新原材料列表
 */
const refreshRawMaterialList = () => {
  const { processCode } = currentProcessInfo.value;
  rawMaterialStore.queryRawMaterialList({ ...pageInfo, processCode });
};

/**
 * 刷新
 */

onUnmounted(() => {
  rawMaterialStore.rawMaterialTypeOptions = [];
  rawMaterialStore.initCurrentRawMaterialData();
  rawMaterialStore.rawMaterialList = [];
});
</script>

<style scoped lang="scss">
.raw-material-type {
  .tags {
    @apply mb-2 cursor-pointer;
    box-shadow: 2px 2px 5px 0px rgba(0, 0, 0, 0.1);

    &:hover {
      color: var(--el-color-primary);
    }

    &.el-tag--primary {
      border-color: var(--el-color-primary-light-5);
      box-shadow: 2px 2px 5px 0px var(--el-color-primary-light-3);
    }
  }

  .info-item {
    @apply text-base text-regular p-0.5 pl-2;

    &:hover {
      @apply cursor-pointer;
      background: var(--el-fill-color-light);
    }
  }

  .bg_primary {
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);

    &:hover {
      color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }
  }
}
</style>
