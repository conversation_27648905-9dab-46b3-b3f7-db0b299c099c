import { emptyDefaultValue } from "@/consts";
import { ColumnWidth, EControlType, QMXExperimentResultsEnum, QMXExperimentStatusEnum } from "@/enums";
import { useSalesFillInDataStore } from "@/store/modules";
import { formatEnum } from "@/utils/format/enum";
import { useMaterial } from "@/utils/material";
import { useDynamicFormLabel } from "@/utils/useDynamicFormSelect";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 出厂试验表格配置
 */
export function genOutGoingFactoryExperimentTableColumnsConfig() {
  const fillInDataStore = useSalesFillInDataStore();
  const materialTool = useMaterial();
  const { dateFormatter } = useTableCellFormatter();
  const { getSelectLabel } = useDynamicFormLabel();

  // 通用列配置
  const universalColumnsConfig: TableColumnList = [
    {
      label: "试验编号",
      prop: "experimentNo",
      minWidth: ColumnWidth.Char8,
      fixed: "left"
    },
    {
      label: "成品编号",
      prop: "finProNo",
      width: ColumnWidth.Char8
    },
    {
      label: "需求数量",
      prop: "account",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "线芯名称",
      prop: "wireName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "开始时间",
      prop: "startTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "结束时间",
      prop: "endTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "检测项",
      prop: "rawMetadataValue",
      minWidth: ColumnWidth.Char10,
      formatter(row) {
        if (!row.rawMetadataValue) {
          return emptyDefaultValue;
        }
        return row.rawMetadataValue
          .filter(item => !item.collectionType)
          .map(item => {
            // 波形图
            if (item.dataTypeIdentityDetail.identityCode === EControlType.WaveRoseControl) {
              return "";
            }
            // 常规字段
            return `${item.targetName}：${getSelectLabel(item)} ${item.unit};`;
          });
      }
    },
    {
      label: "试验状态",
      prop: "experimentStatus",
      minWidth: ColumnWidth.Char10,
      formatter: row => {
        return formatEnum(row.experimentStatus, QMXExperimentStatusEnum, "QMXExperimentStatusEnum");
      }
    },
    {
      label: "试验结果",
      prop: "experimentResult",
      minWidth: ColumnWidth.Char10,
      formatter: row => {
        return formatEnum(row.experimentResult, QMXExperimentResultsEnum, "QMXExperimentResultsEnum");
      }
    },
    {
      label: "操作",
      prop: "operation",
      slot: "operation",
      width: ColumnWidth.Char10,
      fixed: "right"
    }
  ];

  // 线圈列配置
  const coilColumnsConfig: TableColumnList = [
    {
      label: "试验编号",
      prop: "experimentNo",
      width: ColumnWidth.Char12,
      fixed: "left"
    },
    {
      label: "检测项",
      prop: "rawMetadataValue",
      minWidth: ColumnWidth.Char10,
      formatter(row) {
        if (!row.rawMetadataValue) {
          return emptyDefaultValue;
        }
        return row.rawMetadataValue
          .filter(item => !item.collectionType)
          .map(item => {
            // 波形图
            if (item.dataTypeIdentityDetail.identityCode === EControlType.WaveRoseControl) {
              return "";
            }
            // 常规字段
            return `${item.targetName}：${getSelectLabel(item)} ${item.unit};`;
          });
      }
    },
    {
      label: "操作",
      prop: "operation",
      slot: "operation",
      width: ColumnWidth.Char18,
      fixed: "right"
    }
  ];

  return {
    columnsConfig: materialTool.isCoilBySubClassCode(
      fillInDataStore.data.subclassCode || fillInDataStore.data.subClassCode
    )
      ? coilColumnsConfig
      : universalColumnsConfig
  };
}
