<template>
  <div class="overflow-hidden w-full flex flex-col flex-1 h-[410px] px-5 py-4 relative">
    <PureTable
      row-key="id"
      :data="list"
      :columns="columnsConfig"
      size="large"
      :loading="loading"
      showOverflowTooltip
      v-model:pagination="pagination"
      @sort-change="handleSortChange"
      @page-current-change="requestList"
      @page-size-change="reloadList"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, onMounted, onUnmounted } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQualityTracingCategoryRecordList } from "@/api/quality-tracing/quality-tracing-record";
import { PureTable } from "@pureadmin/table";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";
import { genQualityTracingRecordTableColumnsConfig } from "./column-config";
import { useTableSort } from "@/utils/use-table-sort";
import { QualityTracingCategoryRecordListItem } from "@/models/quality-tracing";
import { emitter } from "@/utils/mitt";

/**
 * 质量追溯详情分类列表
 */

const props = defineProps<{
  productionStageId: string;
  /** 质量规范id */
  recordId: string;
  /** 分类code */
  categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum;
}>();

const { pagination } = useTableConfig();
const { sortParams, handleSortChange } = useTableSort(reloadList);
const { columnsConfig } = genQualityTracingRecordTableColumnsConfig(props.categoryCode);
const loading = ref(false);
const list = ref<Array<QualityTracingCategoryRecordListItem>>([]);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await queryQualityTracingCategoryRecordList(props.recordId, {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    orderByField: sortParams.value.orderByField,
    orderByType: sortParams.value.orderByType,
    category: props.categoryCode
  });
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

watch(
  () => props.recordId,
  id => {
    if (!id) {
      return;
    }
    requestList();
  },
  {
    immediate: true
  }
);

onMounted(() => {
  emitter.on("refreshQualityTracingRecordDetailCategoryList", requestList);
});

onUnmounted(() => {
  emitter.off("refreshQualityTracingRecordDetailCategoryList", requestList);
});
</script>

<style scoped lang="scss"></style>
