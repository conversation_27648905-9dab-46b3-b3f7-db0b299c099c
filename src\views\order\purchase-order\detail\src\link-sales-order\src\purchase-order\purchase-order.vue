<template>
  <Card margin="12px 0" :fontWeight="700" :title="`采购订单号：${purchaseOrder?.poNo ?? '--'}`">
    <el-row>
      <el-col :span="24">
        <label class="label">采购方公司名称</label>
        <span class="value">
          <CxTag type="custom" icon="icon-company-fill">{{ purchaseOrder?.buyerName || "--" }}</CxTag>
        </span>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <label class="label">物资种类</label>
        <span class="value">
          {{ purchaseOrder?.subClassName || "--" }}
        </span>
      </el-col>
      <el-col :span="12">
        <label class="label">项目名称</label>
        <ShowTooltip className="max-w-[20em] text-base" :content="purchaseOrder?.prjName" />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <label class="label">合同编号</label>
        <span class="value">{{ purchaseOrder?.conCode }}</span>
      </el-col>
      <el-col :span="12">
        <label class="label">合同名称</label>
        <ShowTooltip className="max-w-[20em] text-base" :content="purchaseOrder?.conName" />
      </el-col>
    </el-row>
    <ExtraInfo :data="purchaseOrder" :fields="extraFields" :collapse="collapsed" />
  </Card>
</template>

<script setup lang="ts">
import Card from "../card.vue";
import ShowTooltip from "@/components/ShowTooltip";
import CxTag from "@/components/CxTag/index.vue";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { computed } from "vue";
import { formatDate, formatEnum } from "@/utils/format";
import { ContractTypeEnum, VirtualFlag } from "@/enums";
import ExtraInfo from "@/views/order/purchase-order/detail/src/link-sales-order/src/extra-info.vue";

const props = defineProps<{
  collapsed: boolean;
}>();

const extraFields = [
  { title: "合同编号(国网经法)", key: "sellerConCode" },
  { title: "技术规范流水号", key: "serialNumber" },
  { title: "合同类型", key: "conType" },
  { title: "合同签订日期", key: "sellerSignTime" },
  { title: "物资大类名称", key: "matMaxName" },
  { title: "物资中类名称", key: "matMedName" },
  { title: "物资小类名称", key: "matMinName" },
  { title: "虚拟订单标识", key: "virFlag" }
];
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const purchaseOrder = computed(() => {
  const order = purchaseOrderDetailStore.purchaseOrder;
  if (!order) {
    return order;
  }
  return {
    ...order,
    conType: formatEnum(order.conType, ContractTypeEnum, "ContractTypeEnum"),
    virFlag: formatEnum(order.virFlag, VirtualFlag, "VirtualFlag"),
    sellerSignTime: formatDate(order.sellerSignTime)
  };
});
const collapsed = computed(() => props.collapsed);
</script>

<style lang="scss" scoped>
@import "../../../../styles/mixin";
@include card-label-value;
</style>
