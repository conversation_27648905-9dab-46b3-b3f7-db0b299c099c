<template>
  <search-form
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    :placeholder="placeholder"
    @search="onSearch()"
    @reset="emits('handleReset')"
  >
    <slot />
  </search-form>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm";
import { IKeywordField, ISearchItem } from "@/components/SearchForm";
import { ElDatePicker, ElOption, ElSelect, dayjs } from "element-plus";
import { reactive, defineComponent, h } from "vue";
import { computedAsync, useVModels } from "@vueuse/core";
import { IProductOrderReq } from "@/models";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateoption } from "@/enums";
import { omitBy } from "lodash-unified";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import { useI18n } from "vue-i18n";
import { SalesOrderStatusOption } from "@/consts";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);
const keyWordAliasHook = usekeyWordAliasHook();
const props = defineProps<{
  modelValue: IProductOrderReq;
}>();

const emits = defineEmits<{
  (event: "operateChange"): void;
  (event: "searchForm", params: IProductOrderReq): void;
  (event: "update:modelValue", params: IProductOrderReq): any;
  (event: "handleReset"): void;
}>();

const { modelValue } = useVModels(props, emits);
const placeholder = computedAsync(async () => {
  const placeholder = await keyWordAliasHook.getReplaceAlias(
    `请输入${KeywordAliasEnum.IPO_NO}/销售订单号/销售订单行号`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  );
  console.log("placeholder", placeholder);
  return placeholder || "请输入生产订单号/销售订单号/销售订单行号";
});

// 订单进度
const SalesOrderStatusComponents = defineComponent({
  name: "SalesOrderStatus",
  render() {
    // 生产状态
    const options = SalesOrderStatusOption.map(option => {
      return h(ElOption, { label: useI18n().t(option.label), value: option.value }, () => useI18n().t(option.label));
    });
    return h(ElSelect, { clearable: true }, () => options);
  }
});

const WoStatusComponents = defineComponent({
  name: "woStatus",
  render() {
    // 品类下拉框数据
    const options = ProductionStateoption.map(option =>
      h(ElOption, { label: option.label, value: option.value }, () => option.label)
    );
    return h(ElSelect, { clearable: true, filterable: true }, () => options);
  }
});

const form = reactive({
  /** 物料名称 */
  materialName: undefined,

  /** 计划结束时间 */
  plantStartTime: [],

  /** 计划结束时间 */
  plantEndTime: [],

  /** 工单状态 */
  ipoStatus: undefined,

  /** 物资种类 */
  subClassCode: undefined,

  /** 销售订单进度 */
  soOrderProgress: undefined,

  orFilters: []
});

const items: Array<ISearchItem> = [
  {
    key: "ipoStatus",
    label: "生产状态：",
    component: WoStatusComponents
  },
  {
    key: "subClassCode",
    label: "物资种类：",
    component: SubclassSelect,
    componentProps: {
      placeholder: "请选择",
      clearable: true
    }
  },
  {
    key: "soOrderProgress",
    label: "销售订单进度",
    component: SalesOrderStatusComponents
  },
  {
    key: "plantStartTime",
    label: "计划开始日期：",
    component: ElDatePicker,
    componentProps: {
      startPlaceholder: "开始日期",
      endPlaceholder: "开始日期",
      type: "daterange",
      dateFormat: "YYYY-MM-DD"
    }
  },
  {
    key: "plantEndTime",
    label: "计划结束日期：",
    component: ElDatePicker,
    componentProps: {
      startPlaceholder: "结束日期",
      endPlaceholder: "结束日期",
      type: "daterange",
      dateormat: "YYYY-MM-DD"
    }
  }
];

const keywordFields: Array<IKeywordField> = [
  { key: "prod.ipo_no", title: "生产订单号" },
  { key: "sale.so_no", title: "销售订单号" },
  { key: "line.so_item_no", title: "销售订单行号" }
];

const onSearch = () => {
  const params = omitBy(
    {
      orFilters: form.orFilters,
      subClassCode: form.subClassCode,
      materialName: form.materialName,
      ipoStatus: form.ipoStatus,
      orderProgress: form.soOrderProgress,
      planStartDateFrom:
        form.plantStartTime && form.plantStartTime[0] && dayjs(form.plantStartTime[0]).format("YYYY-MM-DD"),
      planStartDateTo:
        form.plantStartTime && form.plantStartTime[1] && dayjs(form.plantStartTime[1]).format("YYYY-MM-DD"),
      planFinishDateFrom: form.plantEndTime && form.plantEndTime[0] && dayjs(form.plantEndTime[0]).format("YYYY-MM-DD"),
      planFinishDateTo: form.plantEndTime && form.plantEndTime[1] && dayjs(form.plantEndTime[1]).format("YYYY-MM-DD")
    },
    v => v == null
  );
  modelValue.value = params;
  emits("searchForm", params);
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;
}
</style>
