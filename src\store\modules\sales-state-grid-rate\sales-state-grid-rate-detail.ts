import { defineStore } from "pinia";
import { IPagingReq, IStateGridOrderRateParams, IStateGridRate, IStateGridRateTrigger } from "@/models";
import * as api from "@/api/state-grid-rate";
import * as syncApi from "@/api/state-grid-order-sync";
import { StateGridOrderSyncType } from "@/enums";

type StateGridRateDetailType = {
  rate: IStateGridRate | null;
  dialogVisible: boolean;
  title: string;
  triggerList: Array<IStateGridRateTrigger>;
  triggerTotal: number;
};

export const useSalesStateGriRateDetailStore = defineStore({
  id: "cx-sales-state-grid-rate-detail",
  state: (): StateGridRateDetailType => ({
    rate: null,
    dialogVisible: false,
    title: "",
    triggerList: [],
    triggerTotal: 0
  }),
  actions: {
    openDialog(rate: IStateGridRate) {
      this.setRate(rate);
      this.dialogVisible = true;
      this.title = `采购订单号：${rate?.poNo} \u00a0 采购订单行项目号：${rate.poItemNo}`;
    },
    async refreshTriggerList(pageInfo?: IPagingReq) {
      const res = (await api.getStateGridRateTriggerList(this.rate.purchaseId, this.rate.purchaseLineId, pageInfo))
        .data;
      this.triggerList = res?.list || [];
      this.triggerTotal = res?.total;
    },
    async triggerScore(rate?: IStateGridRate) {
      const { purchaseId, purchaseLineId } = rate || this.rate;
      const params: IStateGridOrderRateParams = {
        purchaseId,
        purchaseLineId,
        dataType: StateGridOrderSyncType.TRIGGER_SCORE
      };
      return syncApi.syncStateGridOrderAndValidate(params);
    },
    async triggerScoreByDataId(dataId: string) {
      const { purchaseId, purchaseLineId } = this.rate;
      const params: IStateGridOrderRateParams = {
        purchaseId,
        purchaseLineId,
        dataType: StateGridOrderSyncType.TRIGGER_SCORE,
        dataId
      };
      return syncApi.syncStateGridOrderAndValidate(params);
    },
    async allowTriggerStateGridScore(purchaseLineId: string) {
      return api.allowTriggerStateGridScore(purchaseLineId).then(res => res.data);
    },
    setRate(rate: IStateGridRate) {
      this.rate = rate;
    }
  }
});
