<template>
  <div class="h-full flex flex-col">
    <base-info ref="baseInfoRef" :id="id" />
    <div class="my-5 mx-6 flex-1 overflow-hidden flex flex-col">
      <el-row :gutter="20" class="mb-2 pr-2 bg-white pt-2 shadow-sm">
        <el-col :span="16">
          <cx-switch :operate-modules="tabConfig" v-model:current-tab-index="currentTabIndex" />
        </el-col>
        <el-col v-show="activeParticularDetail" :span="8" class="text-right pr-2">
          <el-button @click="handleExpandAll"> 全部展开 </el-button>
          <el-button @click="handleCollapseAll"> 全部收起 </el-button>
        </el-col>
        <el-col :span="8" class="text-right pr-2">
          <add-application-material-dialog
            v-show="activeApplicationMaterials"
            :quality-id="id"
            @post-save-success="reloadApplicationMaterials"
          />
          <add-application-sales-order-line-dialog
            v-show="activeApplicationSalesOrderLines"
            :quality-id="id"
            @post-save-success="reloadApplicationSalesOrderLines"
          />
        </el-col>
      </el-row>

      <!-- 质量规范明细 -->
      <particular-detail
        ref="particularDetailRef"
        v-show="activeParticularDetail"
        :quality-id="id"
        :sub-class-code="subClassCode"
      />
      <!-- 适用物料 -->
      <application-materials ref="applicationMaterialsRef" v-show="activeApplicationMaterials" :quality-id="id" />
      <!-- 适用销售订单行 -->
      <application-sales-order-line
        ref="applicationSalesOrderLineRef"
        v-show="activeApplicationSalesOrderLines"
        :quality-id="id"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from "vue";
import { useRoute } from "vue-router";
import CxSwitch from "@/components/cx-tab-switch/index.vue";
import BaseInfo from "./base-info.vue";
import { genTabConfig } from "./tab-config";
import ParticularDetail from "./particular-detail/index.vue";
import ApplicationMaterials from "./application-materials/index.vue";
import AddApplicationMaterialDialog from "./application-materials/add-application-material-dailog/index.vue";
import ApplicationSalesOrderLine from "./application-sales-order-line/index.vue";
import AddApplicationSalesOrderLineDialog from "./application-sales-order-line/add-application-sales-oder-line-dialog/index.vue";
import { QualitySpecificationMatchTypeEnum } from "@/enums/quality-specification";
/**
 * 质量规范详情页面
 * 组成：
 * 1. 基础信息
 * 2. tab切换
 *    (1). 质量规范明细 页面
 *    (2). 适用物料 页面
 */

const route = useRoute();
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>();
const tabConfig = genTabConfig(baseInfoRef);
const currentTabIndex = ref(initTabIndex());
const id = route.params.id as string;
const particularDetailRef = ref<InstanceType<typeof ParticularDetail>>();
const applicationMaterialsRef = ref<InstanceType<typeof ApplicationMaterials>>();
const applicationSalesOrderLineRef = ref<InstanceType<typeof ApplicationSalesOrderLine>>();
const subClassCode = computed(() => {
  return baseInfoRef.value?.specificationDetail.subClassCode || "";
});

const reloadApplicationMaterials = computed(() => {
  if (applicationMaterialsRef.value) {
    return applicationMaterialsRef.value.requestApplicationMaterialList;
  }
  return () => {};
});

const reloadApplicationSalesOrderLines = computed(() => {
  if (applicationSalesOrderLineRef.value) {
    return applicationSalesOrderLineRef.value.requestApplicationSalesOrderLineList;
  }
  return () => {};
});

/**
 * 激活质量规范明细tab
 */
const activeParticularDetail = computed(() => {
  return currentTabIndex.value === 0;
});

/**
 * 激活适用物料tab
 */
const activeApplicationMaterials = computed(() => {
  return (
    baseInfoRef.value?.specificationDetail.matchType === QualitySpecificationMatchTypeEnum.Material &&
    currentTabIndex.value === 1
  );
});

/**
 * 激活适用销售订单行tab
 */
const activeApplicationSalesOrderLines = computed(() => {
  return (
    baseInfoRef.value?.specificationDetail.matchType === QualitySpecificationMatchTypeEnum.SalesOrderLine &&
    currentTabIndex.value === 1
  );
});

/**
 * @description: 初始化tabIndex
 */
function initTabIndex() {
  // 初始化时从路由同步 tab 索引
  const initIndex = route.query.tab;
  if (initIndex && typeof initIndex === "string") {
    return parseInt(initIndex, 10);
  }
  return 0;
}

/**
 * @description: 展开全部卡片
 */
function handleExpandAll() {
  particularDetailRef.value?.handleExpandAll();
}

/**
 * @description: 收起全部卡片
 */
function handleCollapseAll() {
  particularDetailRef.value?.handleCollapseAll();
}

// 更新url地址状态
watch(currentTabIndex, newIndex => {
  const urlObj = new URL(window.location.href);
  urlObj.searchParams.set("tab", newIndex.toString());
  history.pushState(null, "", urlObj);
});
</script>
<style lang="scss" scoped></style>
