/**
 * @description: 质量规范详情明细
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse } from "@/models";
import {
  QualitySpecificationParticularDetailListParams,
  QualitySpecificationParticularDetailListItem,
  EditQualitySpecificationParticularDetailParams,
  QualitySpecificationParticularDetailScore,
  QualitySpecificationParticularDetailInsepectionItem
} from "@/models/quality-tracing";

//#region 质量规范详情
/**
 * @description: 获取质量规范详情明细列表
 */
export const getQualitySpecificationParticularDetailList = (
  params: QualitySpecificationParticularDetailListParams,
  id: string
) => {
  return http.post<
    QualitySpecificationParticularDetailListParams,
    IListResponse<QualitySpecificationParticularDetailListItem>
  >(withApiGateway(`admin-api/system/quality-specification/detail/page/${id}`), {
    data: params
  });
};

/**
 * @description: 获取质量规范明细各阶段的合计分值映射
 */
export const getQualitySpecificationParticularDetailScoreMap = (id: string) => {
  return http.get<void, IResponse<QualitySpecificationParticularDetailScore>>(
    withApiGateway(`admin-api/system/quality-specification/detail/score/${id}`)
  );
};

/**
 * @description: 获取质量规范明细(检测标准)
 */
export const getQualitySpecificationParticularDetail = (id: string) => {
  return http.get<void, IResponse<QualitySpecificationParticularDetailListItem>>(
    withApiGateway(`admin-api/system/quality-specification/detail/info/${id}`)
  );
};

/**
 * @description: 获取质量规范明细(检测标准)的检测项
 */
export const getQualitySpecificationParticularDetailInsepectionItemList = (
  id: string,
  category: number,
  qualityId: string
) => {
  return http.get<void, IResponse<Array<QualitySpecificationParticularDetailInsepectionItem>>>(
    withApiGateway(
      `admin-api/system/quality-specification/detail/model-list-by-process?processId=${id}&category=${category}&qualityId=${qualityId}`
    )
  );
};

/**
 * @description: 新增质量规范明细(检测标准)
 */
export const addQualitySpecificationParticularDetail = (params: EditQualitySpecificationParticularDetailParams) => {
  return http.post<EditQualitySpecificationParticularDetailParams, IResponse<boolean>>(
    withApiGateway("admin-api/system/quality-specification/detail/add"),
    {
      data: params
    }
  );
};

/**
 * @description: 编辑质量规范明细(检测标准)
 */
export const editQualitySpecificationParticularDetail = (
  id: string,
  params: EditQualitySpecificationParticularDetailParams
) => {
  return http.put<EditQualitySpecificationParticularDetailParams, IResponse<boolean>>(
    withApiGateway(`admin-api/system/quality-specification/detail/update/${id}`),
    {
      data: params
    }
  );
};

/**
 * @description: 删除质量规范明细(检测标准)
 */
export const deleteQualitySpecificationParticularDetail = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/system/quality-specification/detail/delete/${id}`)
  );
};
//#endregion
