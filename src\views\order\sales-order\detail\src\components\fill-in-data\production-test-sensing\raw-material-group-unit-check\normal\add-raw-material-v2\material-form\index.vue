<template>
  <div class="pl-5" v-loading="loading">
    <div class="mb-5">
      <TitleBar title="原材料检测" class="mb-3" />
      <DynamicForm
        v-if="resFormData.length"
        ref="dynamicTableFormRef"
        :dynamic-form-data="resFormData"
        :edit-mode="true"
      />
      <div class="empty text-center" v-else>
        <el-empty :image-size="120">
          <template #image> <EmptyData /></template>
        </el-empty>
      </div>
    </div>
    <div>
      <div class="flex items-center my-3">
        <TitleBar title="原材料报告" />
        <div class="text-sm text-secondary ml-2">
          <span class="whitespace-nowrap ml-1">{{ FILE_TYPE_TEXT }}</span>
        </div>
      </div>
      <div class="mx-5" v-if="resFileData.length">
        <UploadFrom ref="dynamicFileRef" :dynamic-file-data="resFileData" />
      </div>
      <div class="empty text-center" v-else>
        <el-empty :image-size="120">
          <template #image> <EmptyData /></template>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import DynamicForm from "@/components/DynamicForm";
import UploadFrom from "@/components/Upload";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { useRawMaterialCheckInfoHook } from "@/views/config/raw-material/hook/useRawMaterialCheck";
import { ref, computed, watch } from "vue";
import { IRawMaterialInspectCollectionItem } from "@/models/raw-material/i-raw-material-res-v2";
import { FILE_TYPE_TEXT } from "@/consts";

const props = withDefaults(
  defineProps<{
    collectionData: IRawMaterialInspectCollectionItem[];
    collectionFormData?: any;
  }>(),
  {
    collectionFormData: {}
  }
);
const emits = defineEmits(["update:modelValue"]);

const rawMaterialStore = useRawMaterialGroupUnitStore();
const loading = computed(() => rawMaterialStore.collectionItemLoading);

// 获取采集项目信息
const collectionData = computed(() => props.collectionData);

// 原材料检测项数据
const resFormData = ref([]);
const resFileData = ref([]);
const dynamicTableFormRef = ref<InstanceType<typeof DynamicForm>>();
const dynamicFileRef = ref<InstanceType<typeof UploadFrom>>();

watch(
  collectionData,
  newVal => {
    if (newVal.length) {
      handleCollectionItemsData();
    }
  },
  {
    immediate: true
  }
);

// 获取表单采集项信息
function handleCollectionItemsData() {
  const { getRawMaterialCheckFormData, getRawMaterialCheckFileData } = useRawMaterialCheckInfoHook();
  resFormData.value = getRawMaterialCheckFormData(collectionData.value) || [];
  resFileData.value = getRawMaterialCheckFileData(collectionData.value) || [];
}

// 验证表单信息
const validRawMaterialInspect = async () => {
  const dynamicFormIns = dynamicTableFormRef.value;
  const formData = await dynamicFormIns.payloadFormData();
  const dynamicFileIns = dynamicFileRef.value;
  const uploadData = dynamicFileIns.payloadUploadData();
  if (formData && uploadData) {
    // 处理表单数据
    return updateMaterialTestFormStore(formData, uploadData);
  }
};

// 更新store里面的表单数据
const updateMaterialTestFormStore = (formData, uploadData) => {
  const saveData = {
    ...formData,
    ...uploadData
  };
  emits("update:modelValue", saveData);
};

defineExpose({
  validRawMaterialInspect
});
</script>

<style scoped lang="scss"></style>
