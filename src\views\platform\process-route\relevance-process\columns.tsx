import { TableWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "序号",
      type: "index",
      align: "center",
      width: TableWidth.indexName
    },
    {
      label: "工艺路线",
      prop: "name"
    },
    {
      label: "类型",
      prop: "productionStageName"
    },
    {
      label: "工序名称",
      prop: "processName",
      width: TableWidth.unit
    },
    {
      label: "工序编码",
      prop: "processCode",
      width: TableWidth.unit
    },
    {
      label: "2.0工序编码",
      prop: "processCodeV2",
      width: TableWidth.date
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.simpleOperation
    }
  ];
  return { columns };
}
