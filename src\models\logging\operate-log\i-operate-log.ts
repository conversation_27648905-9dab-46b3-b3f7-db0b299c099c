/**
 * 配置的模块信息
 */
export interface IConfigModule {
  id: string;
}

/** 配置的页面信息 */
export interface IConfigPageFrame {
  id: string;
}

export interface IOperateLog {
  id: string;
  tenantId: string;
  pointId: string;

  /** 操作人 todo */
  operator: string;

  /** 模块 todo */
  module: string;

  /** 页面 */
  pageFrame: string;

  /** 菜单 */
  menu: string;

  /** 功能点 */
  functionPoint: string;

  /** 点击统计 */
  clickNum: number;
  /** 定义  */
  definition: string;
}

export interface IOperateLogDetail extends IOperateLog {
  /** 操作时间 */
  operationTime: string;
  /** Ip */
  ip: string;
  /** 客户端 */
  analysisUserAgent: string;
}
