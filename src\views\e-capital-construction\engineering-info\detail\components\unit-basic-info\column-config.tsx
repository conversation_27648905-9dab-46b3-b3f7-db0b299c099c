import { ColumnWidth, UnitNameEnum, TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 单元基础信息
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "所属间隔名称",
      prop: "bayName",
      formType: "text",
      canEdit: false,
      isFormShow: true,
      isRequire: true,
      minWidth: ColumnWidth.Char10
    },
    {
      label: "所属间隔ID",
      prop: "bayId",
      formType: "text",
      canEdit: false,
      isRequire: true,
      isFormShow: true,
      minWidth: ColumnWidth.Char8
    },
    {
      label: "子实物编码",
      prop: "subPhysicalItemCode",
      formType: "text",
      canEdit: true,
      isRequire: true,
      isFormShow: true,
      minWidth: ColumnWidth.Char10
    },
    {
      label: "子实物ID（单元）",
      prop: "subPhysicalItemId",
      formType: "text",
      canEdit: true,
      isFormShow: true,
      minWidth: ColumnWidth.Char10
    },
    {
      label: "单元名称",
      prop: "unitName",
      formType: "select",
      options: UnitNameEnum,
      canEdit: true,
      isFormShow: true,
      minWidth: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return UnitNameEnum[data.row.unitName];
      }
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char10,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      canEdit: false,
      isFormShow: false,
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      isFormShow: false,
      slot: "operation",
      canEdit: false,
      width: TableWidth.operation
    }
  ];

  return columnsConfig;
}
