<template>
  <el-form
    ref="addCollectionFormRef"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
    :model="collectionForm"
    :rules="rules"
    :validate-on-rule-change="false"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="数据项" prop="targetName">
          <el-input v-model="collectionForm.targetName" placeholder="请输入数据项" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="数据项编码" prop="targetCode">
          <el-input v-model="collectionForm.targetCode" placeholder="请输入数据项编码" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="数据项编码2.0" prop="targetCodeV2">
          <el-input v-model="collectionForm.targetCodeV2" placeholder="请输入数据项编码" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="别名" prop="targetNameAlias">
          <el-input v-model="collectionForm.targetNameAlias" placeholder="请输入别名" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="数据采集方式" prop="collectionType">
          <el-radio-group v-model="collectionForm.collectionType">
            <el-radio border :label="0">系统推送</el-radio>
            <el-radio border :label="1">自动采集</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否必传" prop="required">
          <el-radio-group v-model="collectionForm.required">
            <el-radio border :label="true">是</el-radio>
            <el-radio border :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="单位" prop="unit">
          <el-input v-model="collectionForm.unit" placeholder="请输入单位" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="数据格式要求" prop="tips">
          <el-input v-model="collectionForm.datumOrganization" placeholder="请输入数据格式要求" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="表单控件类型" prop="identityCode">
          <el-select
            class="w-full"
            v-model="collectionForm.identityCode"
            placeholder="请选择表单控件类型"
            filterable
            @change="identityChange"
          >
            <el-option
              v-for="item in collectionItemsStore.identifyTypeList"
              :key="item.id"
              :label="item.identityName"
              :value="item.identityCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="collectionForm.sort"
            class="!w-full"
            :min="0"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            placeholder="请输入排序数字"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 控件列表 -->
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item
          label="选项列表"
          class="identity-list-content"
          :prop="collectionForm.identityCode === EControlType.WaveRoseControl ? 'waveFormConfig' : 'identityDetailList'"
          v-if="isShowAddOptions"
        >
          <div class="add-identityList">
            <el-button type="primary" @click="handleAddTableItem(collectionForm)">添加</el-button>
          </div>
          <PureTable
            class="flex-1 overflow-hidden"
            row-key="id"
            :data="getTableData(collectionForm)"
            :columns="columns"
            showOverflowTooltip
            :max-height="200"
          >
            <template #valueType="{ row }">
              <el-select v-model="row.valueType">
                <el-option :disabled="xAxisOptionDisabled" label="X轴" :value="WaveRoseAxisEnum.XAxis" />
                <el-option label="Y轴" :value="WaveRoseAxisEnum.YAxis" />
              </el-select>
            </template>
            <template #name="{ row }">
              <div class="input-code">
                <el-input v-model.trim="row.name" placeholder="请输入字段" />
              </div>
            </template>
            <template #display="{ row }">
              <div class="input-code">
                <el-input v-model.trim="row.display" placeholder="请输入名称" />
              </div>
            </template>
            <template #identityCode="data">
              <div class="input-code">
                <el-input v-model.trim="data.row.identityLabel" placeholder="请输入编码" />
              </div>
            </template>
            <template #identityValue="data">
              <div class="input-value">
                <el-input v-model.trim="data.row.identityValue" placeholder="请输入值" />
              </div>
            </template>
            <!-- 操作 -->
            <template #operate="{ index }">
              <div class="move-to">
                <el-button type="primary" @click="handleRemoveTableItem(collectionForm, index)">移除</el-button>
              </div>
            </template>
          </PureTable>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12" v-if="isShowNumberFormItem">
        <el-form-item label="保留小数位" prop="decimalDigits">
          <el-input-number
            v-model="collectionForm.decimalDigits"
            class="!w-full"
            :min="0"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            placeholder="请输入保留小数位"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="isShowDateTypeFormItem">
        <el-form-item label="日期/时间格式化" prop="format">
          <el-select class="w-full" v-model="collectionForm.format" filterable placeholder="请选择日期/时间格式化">
            <el-option
              v-for="(item, index) in DATE_TYPE_OPTIONS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <!-- 文本 -->
      <el-col :span="12" v-if="isShowTextFormItem">
        <el-form-item label="文本最大长度" prop="maxStrLength">
          <el-input-number
            v-model="collectionForm.maxStrLength"
            class="!w-full"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            :max="500"
            :min="0"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 文本 -->
    <el-row :gutter="40" v-if="isShowTextFormItem">
      <el-col :span="12">
        <el-form-item label="是否使用正则" prop="useRegular">
          <el-radio-group v-model="collectionForm.useRegular">
            <el-radio border :label="true">是</el-radio>
            <el-radio border :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="正则表达式" prop="regular">
          <el-input v-model="collectionForm.regular" placeholder="请输入正则" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 数字 -->
    <el-row :gutter="40" v-if="isShowNumberFormItem">
      <el-col :span="12">
        <div class="line-grid flex justify-between items-center">
          <el-form-item label="是否限制最大值" prop="validMaxValue">
            <el-radio-group v-model="collectionForm.validMaxValue">
              <el-radio border :label="true">是</el-radio>
              <el-radio border :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否包含最大值" prop="includeMaxValue">
            <el-radio-group v-model="collectionForm.includeMaxValue">
              <el-radio border :label="true">是</el-radio>
              <el-radio border :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="12" v-if="isShowMaxValueInput">
        <el-form-item label="最大值" prop="maxValue">
          <el-input-number
            v-model="collectionForm.maxValue"
            class="!w-full"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40" v-if="isShowNumberFormItem">
      <el-col :span="12">
        <div class="line-grid flex justify-between items-center">
          <el-form-item label="是否限制最小值" prop="validMinValue">
            <el-radio-group v-model="collectionForm.validMinValue">
              <el-radio border :label="true">是</el-radio>
              <el-radio border :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否包含最小值" prop="includeMinValue">
            <el-radio-group v-model="collectionForm.includeMinValue">
              <el-radio border :label="true">是</el-radio>
              <el-radio border :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
      </el-col>

      <el-col :span="12" v-if="isShowMinValueInput">
        <el-form-item label="最小值" prop="minValue">
          <el-input-number
            v-model="collectionForm.minValue"
            class="!w-full"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 文件类型 -->
    <el-row :gutter="40" v-if="isShowFileTypeFormItem">
      <el-col :span="12">
        <el-form-item label="文件类型" prop="fileType">
          <el-select class="w-full" v-model="collectionForm.fileType" filterable multiple placeholder="请选择文件类型">
            <el-option
              v-for="(item, index) in FILE_TYPE_OPTIONS"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="文件大小" prop="fileSize">
          <el-input-number
            v-model="collectionForm.fileSize"
            class="!w-full"
            controls-position="right"
            :step-strictly="true"
            :precision="0"
            :max="20"
            :min="0"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input
            type="textarea"
            placeholder="请输入"
            v-model="collectionForm.remarks"
            show-word-limit
            clearable
            :rows="2"
            :maxlength="100"
            resize="none"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { reactive, ref, watchEffect } from "vue";
import { ISaveCollectionItemsReq } from "@/models/collection-items";
import { useCollectionItemsStore } from "@/store/modules/collection-items";
import { DATE_TYPE_OPTIONS, FILE_TYPE_OPTIONS } from "../types";
import { EControlType, WaveRoseAxisEnum } from "@/enums";
import { genRules } from "./rules-config";
import { genColumns, handleAddTableItem, handleRemoveTableItem, getTableData } from "./table-config";
import { getItemDispalyController } from "./item-display-controller";

const collectionItemsStore = useCollectionItemsStore();
const addCollectionFormRef = ref<FormInstance>();
const collectionForm = reactive<ISaveCollectionItemsReq>({
  targetName: "",
  targetNameAlias: "",
  targetCode: "",
  targetCodeV2: "",
  collectionType: null,
  collectionTypeName: "",
  required: false,
  unit: "",
  datumOrganization: "",
  identityId: "",
  identityCode: "",

  dateTypeCode: "",
  identityDetailList: [],
  sort: null,
  // 校验信息
  // 文本
  maxStrLength: 500,
  useRegular: null,
  regular: null,
  // 数字
  validMaxValue: null,
  maxValue: null,
  includeMaxValue: null,
  validMinValue: null,
  minValue: null,
  includeMinValue: null,
  decimalDigits: null,
  // 日期格式化
  format: null,
  // 文件
  fileType: [],
  fileSize: null,
  remarks: ""
});
const rules = genRules(collectionForm);
const columns = genColumns(collectionForm);
const {
  xAxisOptionDisabled,
  isShowAddOptions,
  isShowTextFormItem,
  isShowNumberFormItem,
  isShowDateTypeFormItem,
  isShowFileTypeFormItem,
  isShowMaxValueInput,
  isShowMinValueInput
} = getItemDispalyController(collectionForm);

// 获取表单的下拉框值
collectionItemsStore.getCollectionIdentifyList();

watchEffect(() => {
  const id = collectionItemsStore.detailOfCollectionItems;
  if (id) {
    Object.assign(collectionForm, { ...collectionItemsStore.detailOfCollectionItems });
  }
});

/** 表单控件类型改变 */
const identityChange = (identityCode: string) => {
  if (!identityCode) return;
  const currentIdentity = collectionItemsStore.identifyTypeList.find(item => item.identityCode === identityCode);
  collectionForm.identityId = currentIdentity?.id;
  // 重置部分表单数据
  Object.assign(collectionForm, {
    useRegular: null,
    regular: null,
    validMaxValue: null,
    maxValue: null,
    includeMaxValue: null,
    validMinValue: null,
    minValue: null,
    includeMinValue: null,
    decimalDigits: null,
    format: null,
    fileType: [],
    fileSize: null,
    identityDetailList: [],
    waveFormConfig: []
  });
};

/** 验证表单数据 */
const validForm = async fn => {
  if (!addCollectionFormRef.value) return;
  const valid = await addCollectionFormRef.value.validate(() => {});
  if (!valid) return;

  // 校验下拉框或者单选框是否有值
  if (
    collectionForm.identityCode === EControlType.SelectControl ||
    collectionForm.identityCode === EControlType.RadioControl
  ) {
    const validate = collectionForm.identityDetailList.filter(item => !item.identityValue);
    if (validate.length) {
      ElMessage.warning("请输入列表值");
      return;
    }
  }

  if (typeof fn === "function") {
    fn(collectionForm);
  }
};

defineExpose({
  validForm
});
</script>

<style scoped lang="scss">
.identity-list-content {
  position: relative;
}

.add-identityList {
  position: absolute;
  top: -36px;
  right: 0;
  z-index: 2;
}
</style>
