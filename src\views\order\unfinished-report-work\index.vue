<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 搜索条 -->
    <search-bar class="bg-bg_color pt-2 -ml-2" v-model="searchParams" @search="handleSearch" @reset="handleSearch" />
    <!-- 表格 -->
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="text-slate-500 text-xs mb-1">仅显示“结束时间”为空的报工数据</div>
      <PureTable
        row-key="id"
        size="large"
        show-overflow-tooltip
        :data="list"
        :columns="columnsConfig"
        :loading="loading"
        v-model:pagination="pagination"
        @page-size-change="requestList"
        @page-current-change="requestList"
        @sort-change="sortChange"
      >
        <template #ipoNo="{ row }">
          <fill-in-production-data
            v-if="isCableBySubClassCode(row.subClassCode)"
            :sale-order-id="calcSaleOrderId(row.soIds)"
            :production-id="row.productionId"
            @post-dialog-close="requestList"
            mode="SALE"
          >
            <el-button link type="primary">{{ row.ipoNo }}</el-button>
          </fill-in-production-data>
          <production-order-detail v-else :production-id="row.productionId" :sale-order-id="calcSaleOrderId(row.soIds)">
            <el-button link type="primary">{{ row.ipoNo }}</el-button>
          </production-order-detail>
        </template>
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="{ row }">
          <add-edit-report-work-dialog
            mode="edit"
            :report-work-id="row.id"
            :sub-class-code="row.subClassCode || ''"
            :production-id="row.productionId"
            @post-save-success="requestList"
          >
            <template #default="{ openDialog }">
              <el-button v-auth="PermissionKey.form.formPurchaseWorkReportEdit" link type="primary" @click="openDialog">
                编辑
              </el-button>
            </template>
          </add-edit-report-work-dialog>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey } from "@/consts";
import { getUnfinishedReportWorkList } from "@/api/unfinished-report-work";
import { UnfinishedReportWorkItem, UnfinishedReportWorkListParams } from "@/models";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import SearchBar from "./search-bar.vue";
import { genProductionTableColumnsConfig } from "./column-config";
import FillInProductionData from "@/views/components/fill-in-production-data/index.vue";
import ProductionOrderDetail from "@/views/order/production-order/production-order-detail/index.vue";
import AddEditReportWorkDialog from "@/views/components/work-order-and-report-work/add-edit-report-work-dialog/index.vue";
import { useMaterial } from "@/utils/material";
import { handleOrderType } from "@/utils/sortByOrderType";

const route = useRoute();

const { isCableBySubClassCode } = useMaterial();

usePageStoreHook().setTitle(route.meta.title as string);

const { pagination } = useTableConfig();
const { columnsConfig } = genProductionTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<UnfinishedReportWorkItem>>([]);
const searchParams = ref<{
  keyword: string;
  startTime: [string, string];
}>({
  keyword: "",
  startTime: [undefined, undefined]
});

const sortParams = ref({
  orderByField: "",
  orderByType: ""
});

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: UnfinishedReportWorkListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    keyWords: searchParams.value.keyword,
    timeFrom: searchParams.value.startTime?.[0],
    timeTo: searchParams.value.startTime?.[1],
    ...sortParams.value
  };

  const { data } = await getUnfinishedReportWorkList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

function calcSaleOrderId(ids = "") {
  return ids.split(",")[0];
}

function handleSearch() {
  pagination.currentPage = 1;
  requestList();
}

async function sortChange({ prop, order }) {
  if (order) {
    sortParams.value = {
      orderByField: prop,
      orderByType: handleOrderType(order)
    };
  } else {
    sortParams.value = {
      orderByField: "",
      orderByType: ""
    };
  }

  handleSearch();
}

onMounted(requestList);
</script>

<style scoped lang="scss"></style>
