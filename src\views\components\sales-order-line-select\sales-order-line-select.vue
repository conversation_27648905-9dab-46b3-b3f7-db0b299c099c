<template>
  <slot>
    <el-button v-bind="props" :type="type" :disabled="disabled" @click="openSelectSalesOrderLineDialog">
      <FontIcon class="mr-2" icon="icon-link" />
      关联销售订单行
    </el-button>
  </slot>
  <el-dialog
    v-model="visible"
    title="选择销售订单行"
    class="middle"
    align-center
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <SelectHeader />
    <SelectTable :class="{ 'error-only-other-order': showSelectSelfOrderError }" />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSave()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { buttonProps, ElMessage } from "element-plus";
import { computed, provide, reactive, ref } from "vue";
import { ISalesOrderLineSelectCtx, salesOrderLineSelectKey } from "./tokens";
import SelectHeader from "./select-header/select-header.vue";
import SelectTable from "./select-table/select-table.vue";
import { PropParams } from "./types";
import { useTableConfig } from "@/utils/useTableConfig";
import { SaleOrderLineStateEnum } from "@/enums";
import { ISalesOrderLineExt } from "@/models";
import { isObject } from "lodash-unified";

/** 采购订单id、销售订单id 二选一必填 */
const props = defineProps({
  ...buttonProps,
  salesOrderId: String,
  purchaseOrderId: String,
  subclassCode: {
    type: String,
    required: true
  },
  modelValue: Object as PropType<Array<ISalesOrderLineExt>>
});
const emits = defineEmits<{
  (e: "update:modelValue", value: Array<ISalesOrderLineExt>): void;
  (e: "postSave", value: Array<ISalesOrderLineExt>): void;
}>();

const visible = ref(false);
const showSelectSelfOrderError = ref(false);
const subclassCode = computed(() => props.subclassCode);
const type = computed(() => props.type || "primary");
const disabled = computed(() => !subclassCode.value || props.disabled);
const propParams = computed<PropParams>(() => ({
  subclassCode: props.subclassCode,
  purchaseId: props.purchaseOrderId,
  salesId: props.salesOrderId
}));

const ctx = reactive({
  requiredCrossOrderBtn: Boolean(props.salesOrderId && props.purchaseOrderId)
} as ISalesOrderLineSelectCtx);
provide(salesOrderLineSelectKey, ctx);

/**
 * @description: 开启销售订单行选择
 */
function openSelectSalesOrderLineDialog(): void {
  checkProps();
  initializeCtx();
  showSelectSelfOrderError.value = false;
  visible.value = true;
}

function handleSave() {
  catchError(save);
}

function save(): void {
  checkSelectedLines();
  emits("update:modelValue", ctx.selectedLines.slice(0, ctx.multiple ? undefined : 1));
  emits("postSave", ctx.selectedLines.slice(0, ctx.multiple ? undefined : 1));
  visible.value = false;
}

function initializeCtx() {
  Object.assign(ctx, getDefaultCtx(), { propParams });
}

function getDefaultCtx() {
  const selectedLines = getSelectedLinesFromModelValue();
  return {
    keyword: "",
    multiple: selectedLines.length > 1,
    crossOrder: false,
    pagination: useTableConfig().pagination,
    salesOrderLineStatus: SaleOrderLineStateEnum.all,
    selectedLines
  };
}

function getSelectedLinesFromModelValue() {
  return !isObject(props.modelValue)
    ? []
    : Array.isArray(props.modelValue)
    ? [...props.modelValue]
    : [props.modelValue];
}

/**
 * @description: 检查传入props
 */
function checkProps() {
  if (!props.subclassCode) {
    throw new Error("required subclassCode");
  }
}

function checkSelectedLines() {
  if (!ctx.selectedLines.length) {
    throw "请选择一个销售订单行";
  }

  // 如果显示跨订单开关，则说明，现在是查询 销售订单或者采购订单的关联订单模式
  if (ctx.requiredCrossOrderBtn) {
    showSelectSelfOrderError.value = ctx.selectedLines.every(line => !line.orderField);
    if (showSelectSelfOrderError.value) {
      throw "请至少选择当前订单下的一个销售订单行信息";
    }
  }
}

function catchError(fn: Function) {
  try {
    fn();
  } catch (e) {
    ElMessage.warning(e);
  }
}

defineExpose({
  openSelectSalesOrderLineDialog
});
</script>
