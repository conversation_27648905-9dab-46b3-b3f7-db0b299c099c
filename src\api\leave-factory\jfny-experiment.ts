import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models";
import {
  DuplicationJfnyExperimentParams,
  IEditJfnyExperimentForm,
  IJfnyExperimentList,
  ILinkStatusCount,
  IProductionOrderList,
  ISearchJfnyListReq,
  ISearchProductionOrderReq
} from "@/models/leave-factory";

/**
 * 获取日期的数据状态
 */
export function getExperimentCheckData(params: { startDate: string; endDate: string }) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/check-date`);
  return http.get<void, IResponse<Array<string>>>(url, { params });
}

/**
 * 获取关联状态
 */
export function getExperimentListStatus(params: ISearchJfnyListReq) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/list-status`);
  return http.post<ISearchJfnyListReq, IResponse<ILinkStatusCount>>(url, { data: params });
}

/**
 * 获取局放耐压试验列表
 */
export function getJfnyExperimentList(params: ISearchJfnyListReq) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/list`);
  return http.post<ISearchJfnyListReq, IListResponse<IJfnyExperimentList>>(
    url,
    { data: params },
    {
      duplicateKey: "jfnyExperimentList"
    }
  );
}

/**
 * 获取单个试验信息
 */
export function getSingleJfnyInfo(id: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/detail/${id}`);
  return http.get<void, IResponse<IJfnyExperimentList>>(url);
}

/**
 * 删除试验
 */
export function deleteExperimentById(experimentId: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/${experimentId}`);
  return http.delete<void, IResponse<boolean>>(url, {
    params: { experimentId }
  });
}

/**
 * 删除报告
 */
export function deleteReport(id: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny/remove-upload/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}

/**
 * 获取关联的生产订单
 */
export function getLinkProductionOrder(pramsData: ISearchProductionOrderReq) {
  const url = withApiGateway(`admin-api/business/production/getProductionByVoltageType`);
  return http.get<ISearchProductionOrderReq, IListResponse<IProductionOrderList>>(url, { params: pramsData });
}

/**
 * 保存关联的生产订单
 */
export function saveSelectLinkProductionOrder(params: { experimentId: string; productionIds: string[] }) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/link-experiment`);
  return http.post<{ experimentId: string; productionIds: string[] }, IResponse<boolean>>(url, { data: params });
}

/**
 * @description: 复制局放耐压试验
 */
export function duplicationJfnyExperiment(params: DuplicationJfnyExperimentParams) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/Duplication`);
  return http.post<DuplicationJfnyExperimentParams, IResponse<boolean>>(url, { data: params });
}

/**
 * @description: 编辑局放耐压试验
 */
export function editJfnyExperimentById(id: string, data: IEditJfnyExperimentForm) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/edit/${id}`);
  return http.post<IEditJfnyExperimentForm, IResponse<boolean>>(url, { data });
}
