import { usePurchaseOrderDetailStore } from "@/store/modules";
import { usePurchaseOrderDetailSalesOrderLineStore } from "@/store/modules/purchase-order-detail";

function refreshLink() {
  const detailStore = usePurchaseOrderDetailStore();
  const salesOrderLineStore = usePurchaseOrderDetailSalesOrderLineStore();
  return Promise.all([
    detailStore.refreshPurchaseOrder(),
    detailStore.refreshStepStatus(),
    salesOrderLineStore.refreshSalesOrderLines()
  ]);
}

export function usePurchaseOrderLink() {
  return { refreshLink };
}
