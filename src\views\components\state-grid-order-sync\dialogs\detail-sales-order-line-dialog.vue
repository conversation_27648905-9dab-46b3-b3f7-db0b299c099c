<template>
  <SalesOrderLineDetailDialog />
</template>

<script setup lang="ts">
import SalesOrderLineDetailDialog from "@/views/order/purchase-order/detail/src/link-sales-order/src/sales-order-line/sales-order-line-detail-dialog.vue";
import { usePurchaseOrderDetailSalesOrderLineDetailStore } from "@/store/modules/purchase-order-detail";

const salesOrderLineDetailStore = usePurchaseOrderDetailSalesOrderLineDetailStore();

function openDetailDialog(id: string) {
  salesOrderLineDetailStore.showDetail(id);
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
