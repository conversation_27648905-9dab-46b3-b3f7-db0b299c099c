import { defineStore } from "pinia";
import { IProductOrder, IWorkOrder } from "@/models";
import * as workOrderApi from "@/api/work-order";
import * as productionOrderApi from "@/api/production-order";
import { usePurchaseOrderDetailStore } from "@/store/modules";

type State = {
  dataId: string;
  productionId?: string;
  data: IWorkOrder | IProductOrder;
};

export const useFillInDataStore = defineStore({
  id: "cx-fill-in-data",
  state: (): State => ({
    /**  生产订单Id */
    dataId: undefined,
    data: undefined,
    /** 非线缆--工单进入填报详情--需要传入 productionId */
    productionId: undefined
  }),
  actions: {
    async refreshData(id: string, productionId?: string) {
      this.dataId = id;
      this.productionId = productionId;
      this.data = await (usePurchaseOrderDetailStore().isCable
        ? productionOrderApi.getProductionOrderDetailById(id).then(res => res.data)
        : workOrderApi.getWorkOrderById(id).then(res => res.data));
    }
  }
});
