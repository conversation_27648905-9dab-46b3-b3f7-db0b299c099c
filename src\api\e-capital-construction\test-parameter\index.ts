/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IEJJInterface, IListResponse } from "@/models";
import {
  EParamsStandard,
  EquipmentModel,
  CollectionItemModel,
  EParamsStandardReport,
  CollectionModel,
  CollectionModelReport,
  IResponse
} from "@/models";

/**
 * @description: 获取参数标准-分页
 */
export const getTestParameterReportList = (params: EParamsStandardReport) => {
  return http.post<EParamsStandardReport, IListResponse<EParamsStandard>>(
    withApiGateway(`admin-api/ejj/equipment/list`),
    {
      data: params
    }
  );
};

/**
 * @description: 获取物资种类列表
 */
export const getEquipmentApi = () => {
  return http.get<EParamsStandardReport, IResponse<Array<EquipmentModel>>>(withApiGateway(`admin-api/ejj/equipment`));
};

export const getInterfaceListApi = (code: string) => {
  return http.get<EParamsStandardReport, IResponse<Array<IEJJInterface>>>(
    withApiGateway(`admin-api/ejj/equipment/interface/${code}`)
  );
};

/**
 * @description: 创建参数标准
 */
export const createStandardApi = (params: EParamsStandardReport) => {
  return http.post<EParamsStandardReport, IListResponse<EParamsStandard>>(withApiGateway(`admin-api/ejj/equipment`), {
    data: params
  });
};

/**
 * @description: 编辑参数标准
 */
export const updateStandardApi = (params: EParamsStandardReport) => {
  return http.put<EParamsStandardReport, IListResponse<EParamsStandard>>(
    withApiGateway(`admin-api/ejj/equipment/${params.id}`),
    {
      data: params
    }
  );
};

/**
 * @description: 删除参数标准
 */
export const deleteStandardApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/equipment/${id}`));
};

/**
 * @description: 获取参数标准ById
 */
export const equipmentInfoApi = (id: String) => {
  return http.get<EParamsStandardReport, IResponse<EParamsStandard>>(withApiGateway(`admin-api/ejj/equipment/${id}`));
};

/**
 * @description: 获取参数标准明细-试验采集类型
 */
export const getCollectionListApi = (id: String) => {
  return http.get<EParamsStandardReport, IResponse<Array<CollectionModel>>>(
    withApiGateway(`admin-api/ejj/equipment/items/collection-list/${id}`)
  );
};

/**
 * @description: 获取参数标准明细-试验明细列表
 */
export const getCollectionItemListApi = (data: CollectionModelReport) => {
  return http.get<EParamsStandardReport, IResponse<Array<CollectionItemModel>>>(
    withApiGateway(
      `admin-api/ejj/equipment/items/collection-item-list/${data.id}?collectionCode=${data.collectionCode}`
    )
  );
};

/**
 * @description: 获取参数标准明细-批量编辑保存
 */
export const saveCollectionForm = (id: string, list: CollectionItemModel) => {
  return http.put<CollectionItemModel, IResponse<boolean>>(
    withApiGateway(`admin-api/ejj/equipment/items/collection-list/update/${id}`),
    {
      data: list
    }
  );
};
