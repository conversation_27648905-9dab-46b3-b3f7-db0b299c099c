<template>
  <div class="relative" :style="{ width: containerWidth + 'px', height: containerHeight + 'px' }">
    <img
      v-for="(image, index) in props.images"
      :id="image.width ? '' : 'main-screenshot'"
      :key="index"
      :style="{
        position: 'absolute',
        top: image.top + 'px',
        left: image.left + 'px',
        width: image.width + 'px',
        height: image.height + 'px'
      }"
      :src="image.src"
    />
  </div>
</template>

<script setup lang="ts">
// Bases
import { ref, watch } from "vue";

// Types
import type { IExcelEditorScreenshot } from "./model";

const props = defineProps<{
  images: IExcelEditorScreenshot[];
}>();
const containerWidth = ref<number>(0);
const containerHeight = ref<number>(0);

watch(
  () => props.images,
  () => {
    const mainScreenshotElement = document.getElementById("main-screenshot") as HTMLImageElement;
    if (mainScreenshotElement) {
      mainScreenshotElement.onload = () => {
        containerWidth.value = mainScreenshotElement.naturalWidth;
        containerHeight.value = mainScreenshotElement.naturalHeight;
      };
    }
  },
  { deep: true, flush: "post" }
);
</script>

<style scoped lang="scss"></style>
