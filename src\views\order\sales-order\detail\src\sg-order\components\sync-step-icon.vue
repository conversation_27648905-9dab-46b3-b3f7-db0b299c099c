<template>
  <FontIcon icon="icon-gou" v-if="isSuccess" />
  <FontIcon icon="icon-cuo" v-else-if="isFail" />
  <div class="flex" :class="isSyncing ? 'syncing' : ''" v-else>
    <div class="dot" />
    <div class="dot" />
    <div class="dot" />
  </div>
</template>

<script setup lang="ts">
import { SyncStepType } from "@/views/order/sales-order/detail/src/sg-order/sync-step-tool";
import { computed } from "vue";

const props = defineProps<{
  type: SyncStepType;
}>();

const isSuccess = computed(() => props.type === "success");
const isFail = computed(() => props.type === "fail");
const isSyncing = computed(() => props.type === "syncing");
</script>

<style scoped>
.dot {
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: var(--el-text-color-regular);
  transform: scale(0.5);
}

.dot + .dot {
  margin-left: 2px;
}

.syncing .dot {
  animation: scale 0.6s infinite alternate linear;
}

.syncing .dot:nth-child(2) {
  animation-delay: 200ms;
}

.syncing .dot:nth-child(3) {
  animation-delay: 400ms;
}

@keyframes scale {
  0% {
    transform: scale(0.5);
  }

  100% {
    transform: scale(1);
  }
}
</style>
