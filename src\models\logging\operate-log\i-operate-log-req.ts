import { IPagingReq, ISortReq } from "@/models/i-paging-req";

export interface IOperateLogReq extends IPagingReq, ISortReq {
  statisticalTimeStart?: string;
  statisticalTimeEnd?: string;

  module?: string;
  menu?: string;
  pageFrame?: string;
  /** 	功能点关键字(支持模糊),示例值(功能点关键字) */
  functionPointLike?: string;

  operator?: string;

  pointIdList?: Array<string>;

  /** 统计时间 */
  statisticalTime?: [Date, Date];
  /** 操作时间 */
  operateDate?: [Date, Date];
  /** ipAddress 地址 */
  ip?: string;
}
