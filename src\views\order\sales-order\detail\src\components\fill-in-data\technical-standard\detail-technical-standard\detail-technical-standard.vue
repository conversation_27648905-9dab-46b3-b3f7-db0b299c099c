<template>
  <el-dialog
    v-model="visible"
    :title="title"
    align-center
    class="large"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="closedChange"
  >
    <DetailOfTechnicalStandard :isEditBtn="isEditBtn" :standardInfo="standardInfo" :processOptions="processOptions" />
  </el-dialog>
</template>

<script setup lang="ts">
import DetailOfTechnicalStandard from "../components/detail-info/technical-standard-info.vue";
import { useTechnicalStandardStore } from "@/store/modules";
import { computed, ref, watch } from "vue";
import { ITechnicalStandardInfo } from "@/models/technical-standard";

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    standardInfo?: ITechnicalStandardInfo;
    isEditBtn?: boolean;
  }>(),
  {
    isEditBtn: true
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const title = computed(() => {
  return props.standardInfo?.standardName;
});
const store = useTechnicalStandardStore();
const processOptions = ref([]);

watch(
  visible,
  newVal => {
    if (newVal) {
      getProcessOfStandard();
    }
  },
  {
    immediate: true
  }
);

// 获取工序列表
async function getProcessOfStandard() {
  const res = await store.getProcessFromStandard(props.standardInfo?.standardId);
  processOptions.value = res || [];
}

/** 关闭弹框 */
function closedChange() {
  processOptions.value = [];
}
</script>

<style scoped lang="scss"></style>
