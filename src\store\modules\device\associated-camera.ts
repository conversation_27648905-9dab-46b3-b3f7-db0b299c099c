import { defineStore } from "pinia";
import { IAssociatedCamera, IAssociatedCameraReq, IAssociatedCameraForm } from "@/models/device";
import * as api from "@/api/device/associated-camera";

/** 关联摄像头 */
export const useAssociatedCameraStore = defineStore({
  id: "cx-camera-store",
  state: () => ({
    cameralist: [] as Array<IAssociatedCamera>,
    cameraRelationList: [] as Array<IAssociatedCamera>,
    total: 0,
    loading: false
  }),
  actions: {
    /** 查询摄像头列表 */
    async queryCamera(data?: IAssociatedCameraReq) {
      this.loading = true;
      const res = await api.queryAssociatedCamera(data);
      this.loading = false;
      this.total = res.data.total;
      if (data.relationFlag) {
        this.cameralist = res.data.list;
      } else {
        this.cameraRelationList = res.data.list;
      }
      return res.data.list;
    },
    /** 关联摄像头 */
    async deviceRelationCamera(data?: IAssociatedCameraForm) {
      return api.deviceRelationCamera(data);
    },
    /**
     * 删除关联摄像头
     * @param deviceId 设备ID
     * @param monitorId 监控ID
     * @returns
     */
    async deleteAssociatedCamera(deviceId: string, monitorId: string) {
      return api.deleteAssociatedCamera(deviceId, monitorId);
    }
  }
});
