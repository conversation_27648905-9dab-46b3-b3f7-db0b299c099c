import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IFactoryTest, IFactoryTestForm, IFactoryTestReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryFactoryTest = (data: IFactoryTestReq) => {
  const url: string = withApiGateway("admin-api/southgrid/experiments/pageList");
  return http.post<IFactoryTestReq, IListResponse<IFactoryTest>>(url, {
    data
  });
};

/** 查询分页  */
export const queryFactoryTestList = (data: IFactoryTestReq) => {
  const url: string = withApiGateway("admin-api/southgrid/experiments/list");
  return http.post<IFactoryTestReq, IResponse<Array<IFactoryTest>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getFactoryTestById = (id: string, collectionTypeId: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/experiments/${id}/${collectionTypeId}`);
  return http.get<string, IResponse<IFactoryTest>>(url);
};

/** 新增 */
export const createFactoryTest = (data: IFactoryTestForm) => {
  return http.post<IFactoryTestForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/experiments/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateFactoryTest = (data: IFactoryTestForm) => {
  return http.put<IFactoryTestForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/experiments/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteFactoryTestById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/experiments/${id}`));
};
