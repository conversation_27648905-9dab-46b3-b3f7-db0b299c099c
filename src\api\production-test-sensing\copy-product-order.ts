import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import {
  IListResponse,
  IOrderListReq,
  IOrdersForChooseReq,
  IOutFactoryFromOrderRes,
  IProcessInspectFromOrderRes,
  IProductionPageRes,
  IRawMaterialFromOrderRes
} from "@/models";
import { IPerceptionProcessStatus } from "@/models/production-test-sensing/i-common";

/** 获取同物资种类的生成订单列表 */
export function getProductionOrderList(productionId: string, params: IOrderListReq) {
  const url = withApiGateway(`admin-api/business/production/production/page/${productionId}`);
  return http.get<void, IListResponse<IProductionPageRes>>(url, { params });
}

/** 获取同物资种类的工单列表 */
export function getWorkOrderList(workOrderId: string, params: IOrderListReq) {
  const url = withApiGateway(`admin-api/business/workOrder/workOrder/page/${workOrderId}`);
  return http.get<void, IListResponse<IPerceptionProcessStatus>>(url, { params });
}

/**
 * 获取某一个生产订单或者工单下的原材检数据
 */
export function getRawMaterialInspectFromOrder(params: IOrdersForChooseReq) {
  const url = withApiGateway(`admin-api/business/productionMaterialProcess/v2/page-for-choose`);
  return http.post<IOrdersForChooseReq, IListResponse<IRawMaterialFromOrderRes>>(url, { data: params });
}

/**
 * 获取某一个生产订单或者工单下的过程检数据
 */
export function getProcessInspectFromOrder(params: IOrdersForChooseReq) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/page-for-choose`);
  return http.post<IOrdersForChooseReq, IListResponse<IProcessInspectFromOrderRes>>(url, { data: params });
}

/**
 * 获取某一个生产订单或者工单下的出厂试验数据
 */
export function getOutFactoryExperimentFromOrder(params: IOrdersForChooseReq) {
  const url = withApiGateway(`admin-api/business/outgoing/page-for-choose`);
  return http.post<IOrdersForChooseReq, IListResponse<IOutFactoryFromOrderRes>>(url, { data: params });
}
