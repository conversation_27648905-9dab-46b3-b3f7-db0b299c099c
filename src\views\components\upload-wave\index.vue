<template>
  <el-dialog
    v-model="visible"
    title="上传波形数据"
    class="default"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="flex">
      <div class="w-full">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right">
          <el-form-item label="文件类型">
            <el-radio-group v-model="form.waveFormFileModel">
              <el-radio :label="WaveFormFileModelEnum.lvm" border>lvm</el-radio>
              <el-radio :label="WaveFormFileModelEnum.txt" border>txt</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="form.waveFormFileModel === WaveFormFileModelEnum.lvm" label="LVM文件">
            <el-upload
              class="w-full"
              ref="lvmUploadRef"
              :auto-upload="false"
              :limit="1"
              :on-change="onLvmFileChange"
              :on-remove="onRemoveLVM"
              accept=".lvm"
            >
              <el-button :icon="UploadFilled" type="primary">文件上传</el-button>
              <template #tip>
                <span class="el-upload__tip ml-2">支持LVM格式文件</span>
              </template>
            </el-upload>
          </el-form-item>

          <template v-else>
            <el-form-item label="电压文件">
              <el-upload
                class="w-full"
                ref="volUploadRef"
                :auto-upload="false"
                :limit="1"
                :on-change="onVolFileChange"
                :on-remove="onRemoveVol"
                accept=".txt"
              >
                <el-button :icon="UploadFilled" type="primary">文件上传</el-button>
                <template #tip>
                  <span class="el-upload__tip ml-2">支持txt格式文件</span>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="电流文件">
              <el-upload
                class="w-full"
                ref="ampUploadRef"
                :auto-upload="false"
                :limit="1"
                :on-change="onAmpFileChange"
                :on-remove="onRemoveAmp"
                accept=".txt"
              >
                <el-button :icon="UploadFilled" type="primary">文件上传</el-button>
                <template #tip>
                  <span class="el-upload__tip ml-2">支持txt格式文件</span>
                </template>
              </el-upload>
            </el-form-item>
          </template>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleUpload" :loading="loading">上传</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules, UploadFile, UploadInstance, UploadRawFile } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { UploadFilled } from "@element-plus/icons-vue";
import { uploadExperimentWave } from "@/api/production-test-sensing/out-going-factory";

enum WaveFormFileModelEnum {
  lvm = " lvm",
  txt = "txt"
}

interface IUploadWate {
  waveFormFileModel: WaveFormFileModelEnum;
  experimentId?: string;
  /** 电压名称 */
  volFileName?: string;
  /** 电流 */
  ampFileName?: string;
}

const emits = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "uploadSuccess"): void;
}>();
const props = defineProps<{
  modelValue: boolean;
  id?: string;
}>();
const loading = ref(false);
const lvmUploadRef = ref<UploadInstance>();
const volUploadRef = ref<UploadInstance>();
const ampUploadRef = ref<UploadInstance>();

let lvmFile: UploadRawFile = null;
let volFile: UploadRawFile = null;
let ampFile: UploadRawFile = null;
const handleUpload = useLoadingFn(onUpload, loading);
const formRef = ref<FormInstance>();
const form = reactive<IUploadWate>({
  waveFormFileModel: WaveFormFileModelEnum.lvm
});
const rules: FormRules = {
  waveFormFileModel: [{ required: true, message: requiredMessage("文件类型"), trigger: "change" }]
};

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

const onLvmFileChange = async (file: UploadFile) => {
  if (!validationLVMFile(file)) {
    onRemoveLVM();
    return;
  }
  lvmFile = file.raw;
};

const onRemoveLVM = () => {
  lvmFile = null;
  lvmUploadRef.value.clearFiles();
};

const onVolFileChange = async (file: UploadFile) => {
  if (!validationTxtFile(file)) {
    onRemoveVol();
    return;
  }
  volFile = file.raw;
};
const onRemoveVol = () => {
  volFile = null;
  volUploadRef.value.clearFiles();
};

const onAmpFileChange = async (file: UploadFile) => {
  if (!validationTxtFile(file)) {
    onRemoveAmp();
    return;
  }
  ampFile = file.raw;
};
const onRemoveAmp = () => {
  ampFile = null;
  ampUploadRef.value.clearFiles();
};

function validationLVMFile(file: UploadFile) {
  if (!file.raw.name.endsWith(".lvm")) {
    ElMessage.warning("只能上传LVM格式的文件");
    return false;
  }
  return true;
}

function validationTxtFile(file: UploadFile) {
  if (file.raw.type != "text/plain") {
    ElMessage.warning("只能上传txt格式的文件");
    return false;
  }
  return true;
}

async function onUpload() {
  if (form.waveFormFileModel == WaveFormFileModelEnum.lvm) {
    onUploadLVM();
    return;
  }
  onUploadTxt();
}

async function onUploadLVM() {
  if (!lvmFile) {
    ElMessage.warning("LVM文件不能为空");
    return;
  }
  const formData = new FormData();
  formData.append("waveFormFileModel", form.waveFormFileModel);
  formData.append("experimentId", props.id);
  formData.append("filels", lvmFile);
  handleUploadWave(formData);
}

async function onUploadTxt() {
  if (!volFile) {
    ElMessage.warning("电压文件不能为空");
    return;
  }
  if (!ampFile) {
    ElMessage.warning("电流文件不能为空");
    return;
  }
  const formData = new FormData();
  formData.append("waveFormFileModel", form.waveFormFileModel);
  formData.append("experimentId", props.id);
  formData.append("filels", volFile);
  formData.append("filels", ampFile);
  formData.append("volFileName", volFile.name);
  formData.append("ampFileName", ampFile.name);
  handleUploadWave(formData);
}

async function handleUploadWave(data: FormData) {
  await uploadExperimentWave(data);
  closeDialog();
  ElMessage.success("导入成功");
  emits("uploadSuccess");
}

function closeDialog() {
  lvmFile = null;
  volFile = null;
  ampFile = null;
  visible.value = false;
}
</script>

<style scoped></style>
