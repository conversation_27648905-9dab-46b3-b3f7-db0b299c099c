import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { ITypeExperimen, ITypeExperimenForm, ITypeExperimenReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryTypeExperimen = (data: ITypeExperimenReq) => {
  const url: string = withApiGateway("admin-api/southgrid/typeExperiments/pageList");
  return http.post<ITypeExperimenReq, IListResponse<ITypeExperimen>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getTypeExperimenById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/typeExperiments/${id}`);
  return http.get<string, IResponse<ITypeExperimen>>(url);
};

/** 新增 */
export const createTypeExperimen = (data: ITypeExperimenForm) => {
  return http.post<ITypeExperimenForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/typeExperiments/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateTypeExperimen = (data: ITypeExperimenForm) => {
  return http.put<ITypeExperimenForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/typeExperiments/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteTypeExperimenById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/typeExperiments/${id}`));
};
