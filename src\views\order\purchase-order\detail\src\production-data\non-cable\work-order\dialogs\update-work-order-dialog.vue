<template>
  <el-dialog
    title="编辑工单"
    class="middle"
    v-model="visible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="dialogOpen"
  >
    <WorkOrderForm ref="workOrderForm" v-loading="fetchLoading" />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import WorkOrderForm from "@/views/order/purchase-order/detail/src/production-data/non-cable/work-order/forms/work-order-form.vue";
import { computed, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useNonCableWorkOrderStore } from "@/store/modules";
import { ElMessage } from "element-plus";

const props = defineProps<{
  modelValue: boolean;
  workOrderId?: string;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", visible: boolean): void;
  (e: "shouldRefresh"): void;
}>();

const store = useNonCableWorkOrderStore();

const workOrderForm = ref<InstanceType<typeof WorkOrderForm>>();
const fetchLoading = ref(false);
const loading = ref(false);
const handleSave = useLoadingFn(save, loading);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(visible: boolean) {
    emit("update:modelValue", visible);
  }
});

async function dialogOpen() {
  fetchLoading.value = true;
  store
    .getWorkOrderById(props.workOrderId)
    .then(workOrder => workOrderForm.value.initializeValue(workOrder))
    .finally(() => (fetchLoading.value = false));
}

async function save() {
  const workOrder = await workOrderForm.value.getValidValue();
  await store.updateWorkOrder(workOrder);
  ElMessage.success("编辑成功");
  visible.value = false;
  emit("shouldRefresh");
}
</script>

<style scoped></style>
