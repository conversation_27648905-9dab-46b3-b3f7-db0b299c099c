import { inject, Ref } from "vue";
import { ScrollbarInstance } from "element-plus";
import { tryOnMounted, useThrottleFn } from "@vueuse/core";
import { syncAuditAnchorKey, syncAuditStateKey } from "../tokens";

export function useScroll(scrollBar: Ref<ScrollbarInstance>) {
  const ctx = inject(syncAuditAnchorKey);
  const syncAuditStateCtx = inject(syncAuditStateKey);
  ctx.scrollBar = scrollBar;

  let ids = syncAuditStateCtx.navigationItems.map(item => item.id);
  let idCount: number;
  const elementMap = new Map<string, HTMLElement>();

  const handleScroll = useThrottleFn(scroll, 100, true);

  tryOnMounted(() => {
    const wrap = scrollBar.value.wrapRef;
    ids.forEach(id => elementMap.set(id, wrap.querySelector(`#${id}`)));
    ids = ids.filter(id => !!elementMap.get(id));
    idCount = ids.length;
  });

  function scroll({ scrollTop }) {
    if (ctx.pauseListenScroll || elementMap.get(ctx.id)?.offsetTop === scrollTop) {
      return;
    }
    for (let i = 0; i < idCount; i++) {
      const id = ids[i];
      const current = elementMap.get(id);
      const { offsetTop: cTop, clientHeight: cHeight } = current;
      if (cTop + cHeight > scrollTop) {
        ctx.id = id;
        break;
      }
    }
  }

  return {
    handleScroll
  };
}
