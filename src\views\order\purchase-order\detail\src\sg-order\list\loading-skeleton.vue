<template>
  <el-skeleton :loading="props.loading" :count="props.count" animated>
    <template #template>
      <div class="bg-bg_color gap-5 flex mb-5">
        <el-skeleton-item variant="rect" style="width: 96px; height: 180px" />
        <div class="flex flex-col flex-1 justify-between my-2.5">
          <div class="flex-bc">
            <div class="flex flex-col w-full gap-3">
              <el-skeleton-item variant="h1" style="width: 50%" />
              <div class="flex gap-5">
                <el-skeleton-item variant="h1" style="width: 20%" />
                <el-skeleton-item variant="h1" style="width: 20%" />
                <el-skeleton-item variant="h1" style="width: 40%" />
              </div>
            </div>
            <div class="flex flex-col gap-2 mr-5">
              <el-skeleton-item variant="button" style="width: 88px; height: 32px" />
              <el-skeleton-item variant="button" style="width: 88px; height: 32px" />
            </div>
          </div>
          <div class="flex-c mr-5">
            <el-skeleton-item variant="rect" style="height: 70px" />
          </div>
        </div>
      </div>
    </template>
    <template #default>
      <slot />
    </template>
  </el-skeleton>
</template>

<script setup lang="ts">
const props = defineProps<{
  loading?: boolean;
  count?: number;
}>();
</script>

<style scoped lang="scss"></style>
