<template>
  <div class="overflow-hidden w-full flex flex-col">
    <!-- 表格 -->
    <PureTable
      ref="tableRef"
      row-key="id"
      :data="list"
      :columns="columnsConfig"
      :loading="loading"
      max-height="250px"
      showOverflowTooltip
      size="small"
      @page-current-change="requestList"
      @page-size-change="reloadList"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
      <template #radio="{ row }">
        <div class="flex justify-center items-center">
          <el-radio v-model="selectedId" size="large" :label="row.id" @change="handleRadioChange">&nbsp;</el-radio>
        </div>
      </template>
      <template #printerPhoto="{ row }">
        <el-button v-if="row.printerPhoto" type="primary" link @click="handlePreviewImage(row.printerPhoto)">
          查看
        </el-button>
        <span v-else>{{ emptyDefaultValue }}</span>
      </template>
    </PureTable>
    <div class="flex h-8 items-center">
      <div v-if="selectedRow">
        已选择： <el-tag @close="selectedId = ''">{{ selectedRow.printerName }}</el-tag>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClosePreview"
    >
      <div class="flex justify-center mt-2">
        <img w-full :src="dialogImageUrl" alt="喷码机照片" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useVModels } from "@vueuse/core";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PureTable } from "@pureadmin/table";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import { getPrintMachineList } from "@/views/ecode/api";
import { PrintMachine } from "@/views/ecode/models/i-print-machine-manage";
import { emptyDefaultValue } from "@/consts";

/**
 * 喷码机列表
 */

const props = defineProps<{
  /** 已选中的质量规范 */
  modelValue: string;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", value: string): void;
}>();

const { modelValue: selectedId } = useVModels(props, emits);

const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<PrintMachine>>([]);
const tableRef = ref<PureTableInstance>();
const dialogImageUrl = ref("");
const dialogVisible = ref(false);

const selectedRow = computed<null | PrintMachine>(() => {
  if (!selectedId.value) {
    return null;
  }
  return list.value.find(item => item.id === selectedId.value);
});

/**
 * @description: 选择事件
 */
function handleRadioChange(id: string) {
  selectedId.value = id;
}

/**
 * @description: 预览图片
 */
function handlePreviewImage(url: string) {
  dialogImageUrl.value = url;
  dialogVisible.value = true;
}

/**
 * @description: 关闭预览
 */
function handleClosePreview() {
  dialogImageUrl.value = "";
  dialogVisible.value = false;
}

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params = {
    pageNo: 1,
    pageSize: 10000
  };
  const { data } = await getPrintMachineList(params);
  list.value = data.list;
  pagination.total = data.total;
  if (!props.modelValue && list.value.length > 0) {
    selectedId.value = list.value[0].id;
  }
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(requestList);

defineExpose({
  getSelectedId: () => selectedId.value
});
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper) {
  .el-checkbox {
    display: none;
  }
}
</style>
