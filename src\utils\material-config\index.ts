import { MaterialCategoryCode } from "@/enums";
import coilConfig from "./coli-config";
import switchConfig from "./switch-config";
import cableConfig from "./cable-config";
import distributionCableConfig from "./distribution-cable-config";
import switchgearConfig from "./switchgear-config";
import distributionTransformerConfig from "./distribution-transformer-config";
import { MaterialCategoryConfig } from "./types";

export const materialConfig = {
  coilConfig,
  [MaterialCategoryCode.COIL]: coilConfig,
  switchConfig,
  [MaterialCategoryCode.SWITCH]: switchConfig,
  cableConfig,
  [MaterialCategoryCode.CABLE]: cableConfig,
  distributionCableConfig,
  [MaterialCategoryCode.DISTRIBUTION_CABLE]: distributionCableConfig,
  switchgearConfig,
  [MaterialCategoryCode.SWITCHGEAR]: switchgearConfig,
  distributionTransformerConfig,
  [MaterialCategoryCode.DISTRIBUTION_TRANSFORMER]: distributionTransformerConfig
};

/**
 * @description: 获取物资品类的配置
 */
export const getCategoryConfig = (categoryCode: string) => materialConfig[categoryCode] as MaterialCategoryConfig;

/**
 * @description: 获取物资种类的配置
 */
export const getSubClassConfig = (subClassCode: string) => {
  const subClassConfigMap = {
    ...coilConfig.subClassMap,
    ...switchConfig.subClassMap,
    ...cableConfig.subClassMap,
    ...distributionCableConfig.subClassMap,
    ...switchgearConfig.subClassMap,
    ...distributionTransformerConfig.subClassMap
  };
  return subClassConfigMap[subClassCode];
};

export default materialConfig;
