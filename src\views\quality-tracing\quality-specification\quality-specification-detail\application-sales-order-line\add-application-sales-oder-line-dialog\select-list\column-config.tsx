import { MEASURE_UNIT } from "@/consts";
import { ColumnWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const { withUnitFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "选择",
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      width: ColumnWidth.Char1,
      headerRenderer: () => {
        return <div>选择</div>;
      }
    },
    {
      label: "销售订单号",
      prop: "soNo",
      width: ColumnWidth.Char8
    },
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      width: ColumnWidth.Char5,
      formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
    },
    {
      label: "规格型号",
      prop: "specificationType",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      minWidth: ColumnWidth.Char4
    }
  ];

  return { columnsConfig };
}
