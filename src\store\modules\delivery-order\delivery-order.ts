import {
  IListResponse,
  IDeliveryOrderListData,
  IDeliveryOrderReqParams,
  IResponse,
  IDeliveryOrderDetailListData,
  IDeliveryOrderDetailReqParams
} from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useDeliveryOrderStore = defineStore({
  id: "cx-delivery-order",
  state: () => ({
    total: 0,
    deliveryOrder: {} as IDeliveryOrderListData,
    detailTotal: 0,
    deliveryOrderDetail: {} as IDeliveryOrderDetailListData
  }),
  actions: {
    async queryDeliveryOrderDataList(params: IDeliveryOrderReqParams) {
      // 查询供货单列表
      const res: IListResponse<IDeliveryOrderListData> = await api.queryDeliveryOrderDataList(params);
      this.total = res.data.total;
      return res.data.list;
    },

    async getDeliveryOrderDetailById(id: string) {
      // 根据ID查询供货单详情
      const res: IResponse<IDeliveryOrderListData> = await api.getDeliveryOrderDetailById(id);
      return res.data;
    },

    async createDeliveryOrder(params: IDeliveryOrderListData) {
      // 创建供货单
      const res: IResponse<string> = await api.createDeliveryOrder(params);
      return res.data;
    },
    async editDeliveryOrder(params: IDeliveryOrderListData) {
      // 编辑供货单
      const res: IResponse<boolean> = await api.editDeliveryOrder(params);
      return res.data;
    },
    async deleteDeliveryOrder(id: string) {
      // 删除供货单
      const res: IResponse<boolean> = await api.deleteDeliveryOrder(id);
      return res.data;
    },
    async queryDeliveryOrderDetailDataList(params: IDeliveryOrderDetailReqParams) {
      // 根据ID查询供货单明细详情
      const res: IListResponse<IDeliveryOrderDetailListData> = await api.queryDeliveryOrderDetailDataList(params);
      this.detailTotal = res.data.total;
      return res.data.list;
    },
    async createDeliveryOrderDetail(params: IDeliveryOrderDetailListData) {
      // 创建供货单明细
      await api.createDeliveryOrderDetail(params);
    },
    async editDeliveryOrderDetail(params: IDeliveryOrderDetailListData) {
      // 编辑供货单明细
      await api.editDeliveryOrderDetail(params);
    },
    async deleteDeliveryOrderDetailById(id: string) {
      const res: IResponse<boolean> = await api.deleteDeliveryOrderDetailById(id);
      return res.data;
    },

    async queryDeliveryOrderDetailDataListById(id: string) {
      const res: IResponse<IDeliveryOrderDetailListData> = await api.queryDeliveryOrderDetailDataListById(id);
      return res.data;
    },

    // 供货单详情赋值
    setDeliveryOrderStorage(deliveryOrder: IDeliveryOrderListData) {
      this.deliveryOrder = deliveryOrder;
    },
    // 供货单详情清空
    clearDeliveryOrderStorage() {
      this.deliveryOrder = {};
    },

    // 供货单明细详情赋值
    setDeliveryOrderDetailStorage(deliveryOrderDetail: IDeliveryOrderListData) {
      this.deliveryOrderDetail = deliveryOrderDetail;
    },
    // 供货单明细详情清空
    clearDeliveryOrderDetailStorage() {
      this.deliveryOrderDetail = {};
    }
  }
});
