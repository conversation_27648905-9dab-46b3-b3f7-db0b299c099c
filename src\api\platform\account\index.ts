import { IAccount, IAccountForm, IAccountReq, IListResponse, IResetPassword, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

export const queryAccount = (params: IAccountReq) => {
  return http.post<IAccountReq, IListResponse<IAccount>>(withApiGateway("admin-api/system/account/page"), {
    data: params
  });
};

export const resetPassword = (data: IResetPassword) => {
  return http.put<IResetPassword, IResponse<boolean>>(withApiGateway("admin-api/system/user/update-password"), {
    data
  });
};

export const addAccount = (account: IAccountForm) => {
  return http.post(withApiGateway("admin-api/system/account/create"), { data: account }, { showErrorInDialog: true });
};

export const editAccount = (account: Partial<IAccountForm>) => {
  return http.post(withApiGateway("admin-api/system/account/update"), { data: account });
};
