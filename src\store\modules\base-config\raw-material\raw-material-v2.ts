import {
  addRawmaterialInspecInfo,
  delAddRawmaterialInfo,
  delRawmaterialInspecInfo,
  getDetailRawMaterialById,
  getDetailRawMaterialInspectInfoById,
  getRawMaterialCheckInfoByProductStePro,
  getRawMaterialInspecListByCode,
  getRawMaterialListByProcessCode,
  putAddRawmaterialInfo,
  putRawmaterialInspecInfo,
  saveAddRawmaterialInfo,
  getRawMaterialKindsV2
} from "@/api/base-config/raw-material";
import { ARMOUR_CLAMP_CODE, dateFormat } from "@/consts";
import { IResponse } from "@/models";
import { IRawMaterialReq } from "@/models/raw-material/i-raw-material-res";
import {
  IProductionStageProcessCode,
  IAddRawMaterialInspection,
  IRawMaterialInspectCollectionItem,
  IRawMaterialList,
  ISaveAddRawMaterial,
  ISearchRawMaterialInspecReq,
  ISearchRawMaterialListReq
} from "@/models/raw-material/i-raw-material-res-v2";
import { formatDate } from "@/utils/format";
import { defineStore } from "pinia";

export const useRawMaterialV2Store = defineStore({
  id: "cx-raw-material-v2-store",
  state: () => ({
    rawMaterialTypeOptions: [] as Array<IProductionStageProcessCode>,
    rawMaterialTypeOptionsLoading: false,
    isAddRaw: true, // 新增/编辑原材料
    isAddInspect: true, // 新增/编辑原材料检
    /** 原材料列表查询参数 */
    queryRawMaterialParams: {} as ISearchRawMaterialListReq,
    rawMaterialTotal: 0 as number,
    rawMaterialList: [] as Array<IRawMaterialList>, // 原材料列表
    rawMaterialListLoading: false,
    currentClickRawMaterial: null as IRawMaterialList,
    rawMaterialBaseInfo: {} as IRawMaterialReq,
    rawMaterialBaseInfoLoading: false,
    editRawMaterialBaseInfo: {} as IRawMaterialReq,
    /** 检验批次表格查询参数 */
    queryRMTestParams: {} as ISearchRawMaterialInspecReq,
    /** 检验批次表格数据 */
    rawMaterialTestList: [] as Array<IAddRawMaterialInspection>,
    rawMaterialTestListLoading: false,
    rawMaterialTestTotal: 0,
    rawMaterialInspectCollectionItem: [] as Array<IRawMaterialInspectCollectionItem>,
    collectionItemLoading: false, // 检测项的loading
    editRawMaterialInspect: {} as IAddRawMaterialInspection,
    detailRawMaterialCheckItem: [] as Array<IRawMaterialInspectCollectionItem>,
    isArmourClamp: false as boolean
  }),
  actions: {
    /**
     * 设置新增或者编辑原材料
     */
    setAddOrEditRawMaterial(flag: boolean) {
      this.isAddRaw = flag;
    },
    /**
     * 设置当前点击的原材料列表
     */
    setCurrentClickRawMaterial(currentClickRawMaterial: IRawMaterialList) {
      this.currentClickRawMaterial = currentClickRawMaterial;
    },
    /**
     * 设置新增或者编辑原材料检
     */
    setAddOrEditInspect(flag: boolean) {
      this.isAddInspect = flag;
    },

    /**
     * 设置当前点击的原材料是否是金具
     */
    setIsArmourClamp(subclassCode: string) {
      return (this.isArmourClamp = subclassCode.includes(ARMOUR_CLAMP_CODE));
    },

    /**
     * @description: 设置检验批次表格参数
     */
    setQueryRMTestParams(params: ISearchRawMaterialInspecReq) {
      this.queryRMTestParams = params;
    },

    /**
     * 获取原材料类型下拉框数据
     */
    async getRawMaterialTypeOptions() {
      this.rawMaterialTypeOptionsLoading = true;
      const kindRes = await getRawMaterialKindsV2().finally(() => {
        this.rawMaterialTypeOptionsLoading = false;
      });
      this.rawMaterialTypeOptions = kindRes.data || [];
    },

    /**
     * 查询原材料列表
     */
    async queryRawMaterialList(params: ISearchRawMaterialListReq) {
      this.queryRawMaterialParams = { ...this.queryRawMaterialParams, ...params };
      await this.getRawMaterialByProcessCode();
    },
    /**
     * 根据工序信息获取原材料列表
     */
    async getRawMaterialByProcessCode() {
      this.rawMaterialListLoading = true;
      const res = await getRawMaterialListByProcessCode(this.queryRawMaterialParams).finally(() => {
        this.rawMaterialListLoading = false;
      });
      if (Array.isArray(res.data?.list) && res.data.list?.length) {
        this.rawMaterialList = res.data.list;
        this.rawMaterialTotal = res.data.total;
      } else {
        this.rawMaterialList = [];
        this.rawMaterialTotal = 0;
      }
    },

    /**
     * 获取原材料类型的基础信息
     */
    async getDetailBaseInfoOfRawMaterial(id: string) {
      this.rawMaterialBaseInfoLoading = true;
      const res = await getDetailRawMaterialById(id).finally(() => {
        this.rawMaterialBaseInfoLoading = false;
      });
      // 编辑的原材料信息
      this.editRawMaterialBaseInfo = res.data;
      // 原材料信息基本信息
      this.rawMaterialBaseInfo = res.data || {};
      this.rawMaterialBaseInfo.productionDate = formatDate(res.data?.productionDate, dateFormat);
    },

    /**
     * 删除原材料信息
     */
    async delRawMaterialInfo(id: string): Promise<IResponse<boolean>> {
      return await delAddRawmaterialInfo(id);
    },

    /**
     * 新增原材料
     */
    async saveAddRawMaterialList(params: ISaveAddRawMaterial): Promise<IResponse<boolean>> {
      return await saveAddRawmaterialInfo(params);
    },
    /**
     * 更新原材料
     */
    async putAddRawMaterialList(params: ISaveAddRawMaterial): Promise<IResponse<boolean>> {
      return await putAddRawmaterialInfo(params);
    },

    /**
     * 查询检测批次列表
     */
    queryRawMaterialInspectList(params: ISearchRawMaterialInspecReq) {
      this.setQueryRMTestParams({ ...this.queryRawMaterialParams, ...params });
      this.getRawMaterialTestListByProcessCode();
    },

    /**
     * 获取检验批次信息
     */
    async getRawMaterialTestListByProcessCode() {
      this.rawMaterialTestListLoading = true;
      const res = await getRawMaterialInspecListByCode(this.queryRMTestParams).finally(() => {
        this.rawMaterialTestListLoading = false;
      });
      if (Array.isArray(res.data?.list) && res.data.list?.length) {
        this.rawMaterialTestList = res.data.list;
        this.rawMaterialTestTotal = res.data.total;
      } else {
        this.rawMaterialTestList = [];
        this.rawMaterialTestTotal = 0;
      }
    },

    /**
     * 删除检验批次信息
     */
    async delRawmaterialByIdAction(id: string): Promise<IResponse<boolean>> {
      return await delRawmaterialInspecInfo(id);
    },

    /**
     * 获取原材料检测项数据
     */
    async getRawMaterialCheckInfoByProcessCode(processCode: string, specificationModel: string) {
      if (this.isAddInspect) {
        this.rawMaterialInspectCollectionItem = [];
        this.collectionItemLoading = true;
        const res = await getRawMaterialCheckInfoByProductStePro(processCode, specificationModel);
        this.collectionItemLoading = false;
        if (res.data?.length) {
          this.rawMaterialInspectCollectionItem = res.data;
        }
      }
    },

    /**
     * 保存原材料检测配置
     */
    async addRawMaterialInspect(saveData: IAddRawMaterialInspection) {
      if (Object.keys(saveData).length) {
        return await addRawmaterialInspecInfo(saveData);
      }
    },

    /**
     * 编辑原材料检测数据
     */
    async updateRawMaterialInspect(updateData: IAddRawMaterialInspection) {
      if (Object.keys(updateData).length) {
        return await putRawmaterialInspecInfo(updateData);
      }
    },

    /**
     * 获取原材料检测的详情数据
     */
    async getDetailOfRawMaterialInspect(id: string) {
      this.collectionItemLoading = true;
      const res = await getDetailRawMaterialInspectInfoById(id);
      this.collectionItemLoading = false;
      this.initRawMaterInspectDetail();
      if (res.data.id) {
        this.editRawMaterialInspect = res.data;
        this.editRawMaterialInspect.inspectDate = formatDate(res.data.inspectDate, dateFormat);
        this.rawMaterialInspectCollectionItem = res.data.rawMetadataValue || [];
        // 详情
        this.detailRawMaterialCheckItem = res.data.rawMetadataValue || [];
      } else {
        this.editRawMaterialInspect = {};
        this.rawMaterialInspectCollectionItem = [];
        this.detailRawMaterialCheckItem = [];
      }
    },

    /**
     * 删除原材料检
     */
    async delRawMaterialInspectById(id: string): Promise<IResponse<boolean>> {
      return await delRawmaterialInspecInfo(id);
    },

    /**
     * 重置当前点击的原材料数据数据
     */
    initCurrentRawMaterialData() {
      this.currentClickRawMaterial = null;
    },
    /**
     * 重置原材料基础详情信息
     */
    initRawMaterialBaseInfo() {
      this.rawMaterialBaseInfo = {};
    },
    initRawMaterialInspecList() {
      this.rawMaterialTestList = [];
    },
    /**
     * 重置单个原材料检测的详情数据
     */
    initRawMaterInspectDetail() {
      this.editRawMaterialInspect = {};
      this.detailRawMaterialCheckItem = [];
      this.rawMaterialInspectCollectionItem = [];
    }
  }
});
