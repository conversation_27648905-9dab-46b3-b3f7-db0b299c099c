<template>
  <PureTable
    class="flex-1 overflow-hidden pagination"
    row-key="id"
    ref="multipleTableRef"
    :data="state.dataTypes"
    :columns="columns"
    showOverflowTooltip
    :loading="state.loading"
  >
    <template #operation="data">
      <div>
        <ElButton type="primary" link @click="onImportData(data.row)">导入</ElButton>
        <ElButton type="primary" link @click="onDownloadTemplate(data.row)">模板下载</ElButton>
        <ElButton type="primary" link @click="onViewImportData(data.row)">查看数据</ElButton>
        <ElButton v-if="data.row.errorExcelUrl" type="primary" link @click="onDownloadErrorExcel(data.row)"
          >错误数据下载
        </ElButton>
      </div>
    </template>
  </PureTable>

  <ElDialog
    :title="getDialogTitle()"
    align-center
    class="middle"
    destroy-on-close
    v-model="state.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseImportData()"
  >
    <ImportDataDialog
      :percentage="state.percentage"
      :importDataProcess="state.importDataProcess"
      :taskId="state.taskId"
      :importDataType="state.choosedDataType"
      ref="importDataDialogRef"
      @downloadTemplate="handleDownloadTemplate()"
      @downloadErrorExcel="handleDownloadErrorExcel()"
      @uploadFileChange="handleUploadFileChange()"
    />
    <template #footer>
      <el-button @click="onCloseImportData()">取消</el-button>
      <el-button type="primary" :disabled="!!state.taskId" :loading="loadingRef" @click="onUploadFile()"
        >导入
      </el-button>
    </template>
  </ElDialog>

  <ElDialog
    :title="getUploadTemplateDialogTitle()"
    align-center
    class="middle"
    destroy-on-close
    v-model="state.uploadTemplateModalVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseUploadTemplateDialog()"
  >
    <el-upload
      drag
      ref="uploadExcelTemplateRef"
      v-model:file-list="state.uploadTemplatefileList"
      :auto-upload="false"
      :accept="excelAccept"
      :on-change="onUploadTemplateFileChange"
      :limit="1"
      :on-exceed="onUploadTemplateExceed"
    >
      <el-icon size="24"><UploadFilled /></el-icon>
      <div class="el-upload__text">
        <div class="upload-text"><span>将导入Excel数据模板拖到此处，或</span><em>点击上传</em></div>
        <div class="upload-tips">
          <span>仅支持Excel格式文件.</span>
        </div>
      </div>
    </el-upload>
    <template #footer>
      <el-button @click="onCloseUploadTemplateDialog()">取消</el-button>
      <el-button type="primary" :loading="uploadTemplateLoadingRef" @click="onUploadExcelTemplate()">导入 </el-button>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { useColumns } from "./columns";
import ImportDataDialog from "../import-data-dialog/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import * as importDataService from "@/api/import";
import { IImportDataType } from "@/models";
import { ImportDataTypeMapName, ImportDataTypeMapEnumName } from "@/enums";
import { downloadByUrl } from "@/utils/file";
import { useLoop } from "@/utils/useLoop";
import { useRouter } from "vue-router";
import { ElMessage, UploadUserFile, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { downloadByData } from "@pureadmin/utils";

const { startLoop, stopLoop } = useLoop(loopImportLoop, 2000);
const { columns } = useColumns();
const router = useRouter();
const importDataDialogRef = ref<InstanceType<typeof ImportDataDialog>>();
const loadingRef = ref();
const uploadTemplateLoadingRef = ref();
const uploadExcelTemplateRef = ref();

const onUploadFile = useLoadingFn(handleConfirmImportData, loadingRef);
const onUploadExcelTemplate = useLoadingFn(handleUploadExcelTemplate, uploadTemplateLoadingRef);
const excelTypes = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"];
const excelAccept: string = excelTypes.join(",");

const state = reactive<{
  dataTypes: Array<IImportDataType>;
  visible: boolean;
  loading: boolean;
  fileId: string;
  dataTypeName: string;
  choosedDataType: IImportDataType;
  percentage: number;
  importDataProcess?: IImportDataType;
  taskId?: string;
  uploadTemplateModalVisible: boolean;
  uploadTemplatefileList: Array<UploadUserFile>;
}>({
  fileId: undefined,
  loading: false,
  dataTypeName: undefined,
  dataTypes: [],
  visible: false,
  choosedDataType: undefined,
  percentage: 0,
  importDataProcess: undefined,
  taskId: undefined,
  uploadTemplateModalVisible: false,
  uploadTemplatefileList: []
});

onMounted(async () => {
  queryImportDataType();
});

const getDialogTitle = () => {
  return `${state.dataTypeName}批量导入`;
};

const onImportData = (importData: IImportDataType) => {
  state.dataTypeName = ImportDataTypeMapName[importData.dataType];
  state.choosedDataType = importData;
  state.visible = true;
};

const onDownloadTemplate = (importData: IImportDataType) => {
  state.choosedDataType = importData;
  handleDownloadTemplate();
};

const onCloseImportData = () => {
  state.visible = false;
  stopLoop();
  if (state.importDataProcess?.resultStatus) {
    queryImportDataType();
  }
  resetUploadState();
};

async function handleConfirmImportData() {
  try {
    const uploadRes = await importDataDialogRef.value.handleUploadFile(state.choosedDataType);
    state.fileId = uploadRes?.id;
    await handleSubmitImportTask();
    startLoop();
  } catch (ex) {
    //Todo
  }
}

/** 提交当前导入任务 */
const handleSubmitImportTask = async () => {
  state.taskId = (
    await importDataService.submitImportTask({ fileId: state.fileId, dataType: state.choosedDataType.dataType })
  ).data;
};

const handleDownloadTemplate = async () => {
  if (!state.choosedDataType) {
    return;
  }

  const downloadBlod = await importDataService.downloadImportTemplate(
    ImportDataTypeMapEnumName[state.choosedDataType.dataType]
  );
  downloadByData(downloadBlod, ImportDataTypeMapName[state.choosedDataType.dataType], downloadBlod.type);
};

const handleDownloadErrorExcel = () => {
  if (!state.importDataProcess || !state.importDataProcess.errorExcelUrl) {
    return;
  }
  downloadByUrl(state.importDataProcess.errorExcelUrl, ImportDataTypeMapName[state.choosedDataType.dataType]);
};

const handleUploadFileChange = () => {
  resetUploadState();
};

async function loopImportLoop() {
  try {
    const importRes = (await importDataService.getImportTaskResult(state.taskId)).data;
    state.importDataProcess = importRes;
    if (importRes.count === importRes.successCount + importRes.errorCount) {
      state.percentage = 100;

      if (importRes.errorCount > 0 && !importRes.errorExcelUrl) {
        return;
      }

      stopLoop();
      state.taskId = undefined;
      if (importRes.errorCount === 0) {
        ElMessage({ message: "数据导入成功", type: "success" });
        onCloseImportData();
      }
      return;
    }
    const percentage: number = Math.ceil(Number((importRes.successCount / importRes.count).toFixed(2)) * 100);
    state.percentage = isNaN(percentage) ? 0 : percentage;
  } catch (ex) {
    resetUploadState();
  }
}

const onViewImportData = (data: IImportDataType) => {
  router.push(`/import-data-view/${data.batchCode}/${data.dataType}`);
};

const queryImportDataType = async () => {
  state.dataTypes = (await importDataService.queryImportDataType()).data;
};

const onDownloadErrorExcel = (importData: IImportDataType) => {
  downloadByUrl(importData.errorExcelUrl, ImportDataTypeMapName[importData.dataType]);
};

const getUploadTemplateDialogTitle = () => {
  return `${state.dataTypeName}导入模板上传`;
};

const onUploadTemplateFileChange = (file: UploadFile) => {
  if (!excelTypes.includes(file.raw.type)) {
    state.uploadTemplatefileList = [];
    uploadExcelTemplateRef.value?.clearFiles();
    ElMessage.warning("上传模板仅支持Excel类型");
    return;
  }
};

const onUploadTemplateExceed = (files: Array<any>) => {
  const file = files?.[0];
  if (!file) {
    return;
  }
  if (!excelTypes.includes(file.type)) {
    state.uploadTemplatefileList = [];
    uploadExcelTemplateRef.value?.clearFiles();
    ElMessage.warning("上传模板仅支持Excel类型");
    return;
  }

  state.uploadTemplatefileList = files;
};

const onCloseUploadTemplateDialog = () => {
  state.uploadTemplateModalVisible = false;
};

// 上传导入exceel模板
async function handleUploadExcelTemplate() {
  if (state.uploadTemplatefileList.length === 0) {
    return;
  }
  const formData = new FormData();
  formData.append("file", state.uploadTemplatefileList[0].raw);
  formData.append("dataType", ImportDataTypeMapEnumName[state.choosedDataType.dataType]);
  await importDataService.uploadExcelTemplate(formData);
  ElMessage.success("模板导入成功");
  state.uploadTemplateModalVisible = false;
  queryImportDataType();
}

function resetUploadState() {
  state.importDataProcess = undefined;
  state.taskId = undefined;
  state.percentage = 0;
}
</script>

<style scoped lang="scss"></style>
