<template>
  <!-- 原材料组部件检验 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="原材料信息" />
      </el-col>
      <el-col v-if="$route.query.type == EquipmentTypeEnumExt.Combiner.toString()" :span="12">
        <el-form-item
          label="采集类型"
          prop="collectionCode"
          :rules="{ required: true, message: '请选择采集类型ID', trigger: 'change' }"
        >
          <el-select
            v-model="formData.collectionCode"
            class="w-full"
            disabled
            placeholder="请选择采集类型ID"
            clearable
            filterable
          >
            <el-option v-for="item in collectionList" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: true, message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder=" 请输入子实物编码" />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="子实物ID"
          prop="utcNum"
          :rules="{ required: false, message: '请输入子实物ID', trigger: 'change' }"
        >
          <el-input v-model="formData.utcNum" placeholder=" 请输入子实物ID" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物ID(单元)"
          prop="subPhysicalItemId"
          :rules="{ required: false, message: '请输入子实物ID(单元)', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemId" placeholder=" 请输入子实物ID(单元)" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="批次检验结果"
          prop="batchResult"
          :rules="{ required: true, message: '请选择批次检验结果', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.batchResult">
            <el-radio v-for="item in BatchResultEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="原材料组件检验结果"
          prop="materialComponentResult"
          :rules="{ required: true, message: '请选择原材料组件检验结果', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.materialComponentResult">
            <el-radio v-for="item in MaterialResultEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="钢印号/字头号"
          prop="stampingNumber"
          :rules="{ required: true, message: '请输入钢印号/字头号', trigger: 'change' }"
        >
          <el-input
            v-model="formData.stampingNumber"
            placeholder=" 请输入钢印号/字头号
"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="主体变/调补变类型"
          prop="mainRegulatingTransformer"
          :rules="{
            required: $route.query.equipmentName == EquipmentNameEnum.Transformer.toString(),
            message: '请选择主体变/调补变类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mainRegulatingTransformer">
            <el-radio v-for="item in MainRegulatEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="原材料检测" />
      </el-col>
      <el-col :span="24">
        <DynamicTable
          ref="dynamicTableRef"
          :fields="tableField"
          field-type="conType"
          field-key="measureItemCode"
          v-model="state.dataValueObj"
        />
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import TitleBar from "@/components/TitleBar/index";
import DynamicTable from "../dynamic-table/index.vue";
import { CollectionModel, InspectRawModel, DataValueObjModel, FileInfoModel } from "@/models";
import {
  MaterialResultEnumOptions,
  EquipmentNameEnum,
  BatchResultEnumOptions,
  MainRegulatEnumOptions,
  EquipmentTypeEnumExt
} from "@/enums";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    detail: InspectRawModel; // 表格表单数据
    isEdit: boolean;
    collection: Array<CollectionModel>;
    tableField: Array<DataValueObjModel>;
  }>(),
  {
    detail: () => {
      return {} as InspectRawModel;
    },
    collection: () => {
      return [];
    },
    collectionItem: () => {
      return [];
    },
    isEdit: false
  }
);
const formData = reactive({
  dataValueObj: []
} as InspectRawModel);
const collectionList = ref([] as Array<CollectionModel>);
const dynamicTableRef = ref();
const tableField = ref([] as Array<DataValueObjModel>);
const route = useRoute();

const state = reactive<{
  dataValueObj: { [key: string]: string | number | FileInfoModel };
}>({
  dataValueObj: {}
});

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

watchEffect(() => {
  Object.assign(formData, props.detail);
  collectionList.value = props.collection;
  tableField.value = props.tableField;
  if (props.isEdit) {
    formData.dataValueObj.forEach(item => {
      if (item.conType == "file") {
        state.dataValueObj[item.measureItemCode] = item.fileInfo || ({} as FileInfoModel);
      } else if (item.conType == "number") {
        state.dataValueObj[item.measureItemCode] = item.dataValue ? Number(item.dataValue) : null;
      } else {
        state.dataValueObj[item.measureItemCode] = item.dataValue;
      }
    });
  } else {
    formData.equipmentType = route.query.type.toString();
  }
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  if (!dynamicTableRef.value) {
    return await formRef.value.validate(valid => valid);
  } else {
    return (await formRef.value.validate(valid => valid)) && (await dynamicTableRef.value.validateForm());
  }
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const dynamicTableValue = dynamicTableRef.value.getFormValue();
  const params = {
    ...formData,
    dataValueObj: dynamicTableValue.map((item: DataValueObjModel) => {
      return {
        measureItemCode: item.measureItemCode,
        dataValue: item.dataValue,
        conType: item.conType,
        conConfig: item.conConfig,
        fileInfo: item.fileInfo,
        validated: false
      };
    })
  };
  return Object.assign({}, params as InspectRawModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  dynamicTableRef.value?.resetFields();
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
