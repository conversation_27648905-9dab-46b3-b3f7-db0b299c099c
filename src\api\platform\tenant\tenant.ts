import { IListResponse, IResponse, ITenant, ITenantForm, ITenantReq } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../../util";

export const queryTenant = (params: ITenantReq) => {
  return http.get<ITenantReq, IListResponse<ITenant>>(withApiGateway("admin-api/system/tenant/page"), { params });
};

export const addTenant = (data: ITenantForm) => {
  return http.post<ITenantForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/tenant/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editTenant = (data: ITenantForm) => {
  return http.put<ITenantForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/tenant/update"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editEnterpriseInfo = (data: ITenantForm) => {
  return http.put<ITenantForm, IResponse<boolean>>(
    withApiGateway("admin-api/system/tenant/enterprise/update"),
    { data },
    { showErrorInDialog: true }
  );
};

export const getTenantDetailById = (id: string) => {
  return http.get<string, IResponse<ITenant>>(withApiGateway("admin-api/system/tenant/get"), { params: { id } });
};

/** 获取登录账号所属租户信息 */
export const queryTenantsByCurrentUser = () => {
  return http.get<void, IResponse<Array<ITenant>>>(withApiGateway("admin-api/system/tenant/get-by-current-user"));
};

export const deleteTenantById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway("admin-api/system/tenant/delete"), { params: { id } });
};

export const bindUserToTenant = (userIds: Array<string>, tenantId: string): Promise<IResponse<boolean>> => {
  return http.post(withApiGateway("admin-api/system/tenant/bind-user-to-tenant"), { data: { userIds, tenantId } });
};

export const downloadGatewayInterface = (tenantId: string) => {
  const url = withApiGateway(`admin-api/reporting/eipGateway/export/${tenantId}`);
  return http.get<void, IResponse<{ name: string; downloadUrl: string }>>(url);
};
