<template>
  <div class="mx-6 my-5 bg-white flex-1 flex flex-col overflow-hidden">
    <div class="flex items-center">
      <input ref="fileRef" type="file" accept=".xlsx, .xls" @change="onFileChange" />
      <el-button type="primary" @click="onGetData()">获取数据</el-button>
      <el-button type="primary" @click="onSetData()">设置数据</el-button>
      <el-button type="primary" @click="onGetScreenshot()">截屏</el-button>
    </div>
    <div class="flex justify-center items-center py-4">
      <Screenshot :images="screenshots" />
    </div>
    <!-- <div id="luckysheet" class="w-full h-full fixed top-[9999px] left-[9999px] z-[9999px]" /> -->
    <div id="luckysheet" class="w-full h-full" />
  </div>
</template>

<script setup lang="ts">
// Bases
import { onMounted, ref } from "vue";

// Utils
import ExcelEditor from "../components/excel-editor/index";

// Types
import type { IExcelEditorScreenshot, IExcelEditorValue } from "../components/excel-editor/model";

// Components
import Screenshot from "../components/excel-editor/preview.vue";

const fileRef = ref<HTMLInputElement>();
const excelEditor = ref();
const screenshots = ref<IExcelEditorScreenshot[]>([]);

onMounted(() => {
  excelEditor.value = new ExcelEditor();
});

function onFileChange(event: Event): void {
  console.log("onFileChange ", event);
  const file: File = (event.target as HTMLInputElement).files[0];
  const blob: Blob = new Blob([file], { type: file.type });
  console.log(blob);
  excelEditor.value.loadExcel(blob);
}

function onGetData(): void {
  const data: IExcelEditorValue[] = excelEditor.value.getDefineNameValues();
  console.log("data", data);
}

function onSetData(): void {
  excelEditor.value.setDefineNameValues({
    customerName: "customerName",
    prodType: "prodType",
    lineName: "lineName"
  });
}

function onGetScreenshot(): void {
  screenshots.value = excelEditor.value.getScreenshots();
  console.log("screenshots", screenshots.value);
}
</script>

<style scoped lang="scss"></style>
