<template>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    :size="size"
    placeholder="请输入工单编号"
    @search="onSearch()"
  />
</template>

<script setup lang="ts">
import { reactive, defineComponent, h } from "vue";
import { ElDatePicker, ElInput, ElOption, ElSelect } from "element-plus";
import { omitBy } from "lodash-unified";
import SearchForm from "@/components/SearchForm";
import { IKeywordField, ISearchItem, SizeType } from "@/components/SearchForm";
import { IWorkOrderReq } from "@/models";
import { ProductionStateoption } from "@/enums";
import { isAllEmpty } from "@pureadmin/utils";

const size = SizeType.DEFAULT;

const form = reactive({
  materialName: undefined,
  plantStartTime: undefined,
  plantEndTime: undefined,
  woStatus: undefined
});

const WoStatusComponents = defineComponent({
  name: "woStatus",
  render() {
    // 品类下拉框数据
    const options = ProductionStateoption.map(option =>
      h(ElOption, { label: option.label, value: option.value }, () => option.label)
    );
    return h(ElSelect, { clearable: true, filterable: true }, () => options);
  }
});

const items: Array<ISearchItem> = [
  {
    key: "materialName",
    label: "物料名称：",
    component: ElInput,
    componentProps: {
      placeholder: "请输入物料名称"
    }
  },
  {
    key: "plantStartTime",
    label: "计划开始日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划开始日期"
    }
  },
  {
    key: "plantEndTime",
    label: "计划结束日期：",
    component: ElDatePicker,
    componentProps: {
      placeholder: "请选择计划结束日期"
    }
  },
  {
    key: "woStatus",
    label: "工单状态：",
    component: WoStatusComponents
  }
];

const emits = defineEmits<{
  (event: "onCreateWorkOrderDialogVisible", params: boolean): void;
  (event: "searchForm", value: IWorkOrderReq): void;
}>();

const keywordFields: Array<IKeywordField> = [{ key: "woNo", title: "工单编号" }];

const onSearch = () => {
  const params = omitBy(form, v => isAllEmpty(v));
  emits("searchForm", params);
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;

  .title {
    color: var(--el-text-color-primary);
    font-size: 1.125rem;
    font-weight: normal;
    line-height: 1.625rem;
    letter-spacing: 0;
    padding-bottom: 0.75rem;
  }
}
</style>
