import { TableWidth, ReworkStatusEnumMapDesc, DataTypeEnumEjjMapDesc, TestConclusionEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 出厂试验
 */
export function useColumns(isCombiner: Boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig: TableColumnList = [];
  const commonColumn: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      minWidth: TableWidth.operation
    }
  ];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "试验项目",
        prop: "testItemName",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "子实物ID(单元)",
        prop: "subPhysicalItemId",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "联合试验单元",
        prop: "jointExperimentalUnit",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "出厂试验贯通次数",
        prop: "penetrationsNum",
        minWidth: TableWidth.largeOrder,
        cellRenderer: (data: TableColumnRenderer) => {
          return `第${data.row.penetrationsNum || "--"}次`;
        }
      },
      {
        label: "试验是否通过",
        prop: "testConclusion",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return TestConclusionEnumMapDesc[data.row.testConclusion];
        }
      },
      {
        label: "试验人员",
        prop: "testPerson",
        minWidth: TableWidth.largeName
      },
      {
        label: "不通过原因",
        prop: "failureReason",
        minWidth: TableWidth.reason
      },
      {
        label: "返修状态",
        prop: "reworkStatus",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return ReworkStatusEnumMapDesc[data.row.reworkStatus];
        }
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "试验项目",
        prop: "testItemName",
        minWidth: TableWidth.largeName
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "出厂试验贯通次数",
        prop: "penetrationsNum",
        minWidth: TableWidth.largeOrder,
        cellRenderer: (data: TableColumnRenderer) => {
          return `第${data.row.penetrationsNum || "--"}次`;
        }
      },
      {
        label: "试验是否通过",
        prop: "testConclusion",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return TestConclusionEnumMapDesc[data.row.testConclusion];
        }
      },
      {
        label: "试验人员",
        prop: "testPerson",
        minWidth: TableWidth.largeName
      },
      {
        label: "不通过原因",
        prop: "failureReason",
        minWidth: TableWidth.reason
      },
      {
        label: "返修状态",
        prop: "reworkStatus",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return ReworkStatusEnumMapDesc[data.row.reworkStatus];
        }
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      }
    ];
  }

  return [...columnsConfig, ...commonColumn];
}
