<template>
  <!-- 附件上传 -->
  <el-upload
    ref="uploadRef"
    accept=".jpg, .jpeg, .png, .pdf"
    v-model:file-list="fileList"
    :auto-upload="true"
    :limit="1"
    :before-upload="beforeUpload"
    :on-success="uploadSuccess"
    :on-error="uploadError"
    :on-exceed="onExceed"
    :on-remove="uploadRemove"
    :on-preview="uploadReview"
    :http-request="uploadReportHttp"
  >
    <el-button type="primary"> 选择文件 </el-button>
    <template #tip>
      <span class="ml-2">仅支持20M内pdf，png，jpg，jpeg格式文件</span>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { ref, reactive, watchEffect } from "vue";
import { ElMessage } from "element-plus";
import {
  genFileId,
  UploadProps,
  UploadRawFile,
  UploadInstance,
  UploadRequestOptions,
  UploadFile,
  FormInstance
} from "element-plus";
import { FileInfoModel } from "@/models";
import { uploadFile } from "@/api/upload-file";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

const props = withDefaults(
  defineProps<{
    modelValue?: FileInfoModel;
  }>(),
  {
    modelValue: () => {
      return {} as FileInfoModel;
    }
  }
);

const uploadRef = ref<UploadInstance>();
const fileList = ref([]);

const state = reactive<{
  fileId: String;
}>({
  fileId: ""
});

watchEffect(() => {
  fileList.value = JSON.stringify(props.modelValue) == "{}" ? [] : props.modelValue?.id ? [props.modelValue] : [];
});

const emits = defineEmits<{
  (e: "update:modelValue", value: FileInfoModel): void;
}>();

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
  uploadRef.value!.submit();
};

/**
 * 预览
 */
const uploadReview: UploadProps["onPreview"] = uploadFile => {
  downLoad(uploadFile as FileInfoModel);
};

/**
 *  下载
 */
async function downLoad(fileInfo: FileInfoModel) {
  const blob = await downLoadFile(fileInfo.id);
  downloadByData(blob, fileInfo.name, blob.type);
}

/**
 * 删除附件
 */
const uploadRemove = () => {
  state.fileId = "";
  emits("update:modelValue", {} as FileInfoModel);
};

/**
 *  上传前
 */

const beforeUpload = (rawFile: UploadRawFile) => {
  const fileSize = rawFile.size / 1024 / 1024 < 20;
  let fileName = "";
  if (rawFile.name) {
    fileName = rawFile.name.substring(rawFile.name.lastIndexOf(".") + 1);
  }
  if (!fileSize) {
    ElMessage.error("上传附件大小不能超过20M");
    return false;
  } else if (fileName !== "pdf" && fileName !== "png" && fileName !== "jpg" && fileName !== "jpeg") {
    ElMessage.error("仅支持.pdf, .png, .jpg, .jpeg文件扩展名");
    return false;
  } else if (rawFile.size === 0) {
    ElMessage.error("上传附件大小不能为空");
    return false;
  } else {
    return true;
  }
};

/** 上传成功时执行的钩子函数 */
const uploadSuccess = async (res: any) => {
  if (!res.code) {
    state.fileId = res.data.id;
    emits("update:modelValue", res.data);
  } else {
    ElMessage.error(`${res?.msg}`);
  }
};

/**
 * 上传失败的时候钩子函数
 */
const uploadError = (error: Error, file: UploadFile) => {
  // 请求失败
  const statusStr = file.status;
  if (statusStr === "fail") {
    ElMessage.error("文件上传失败");
    state.fileId = "";
  }
};

/**
 * 自定义上传接口
 */
const uploadReportHttp = async (options: UploadRequestOptions) => {
  const formData = new FormData();
  formData.append("file", options.file);
  return await uploadFile(formData);
};

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, fileList.value[0] as FileInfoModel);
}

defineExpose({
  validateForm,
  getFormValue
});
</script>

<style scoped lang="scss">
:deep(.el-icon--close-tip) {
  display: none !important;
}
</style>
