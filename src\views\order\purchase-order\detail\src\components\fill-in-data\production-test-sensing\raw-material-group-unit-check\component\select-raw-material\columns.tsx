import { dateFormat } from "@/consts";
import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "原材料编号",
      prop: "code",
      minWidth: TableWidth.order
    },
    {
      label: "原材料名称",
      prop: "name",
      minWidth: TableWidth.name
    },
    {
      label: "规格型号",
      prop: "modelCode",
      minWidth: TableWidth.largeOrder
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: TableWidth.name
    },
    {
      label: "原材料类型",
      prop: "processName",
      minWidth: TableWidth.type
    },
    {
      label: "检验批次号",
      prop: "inspectBatchNo",
      minWidth: TableWidth.largeOrder,
      slot: "inspectBatchNo"
    },
    {
      label: "检验日期",
      prop: "inspectDate",
      minWidth: TableWidth.date,
      formatter: dateFormatter(dateFormat)
    },
    {
      label: "电压等级",
      prop: "voltageGrade",
      minWidth: TableWidth.number
    },
    {
      label: "计量单位",
      prop: "partUnit",
      minWidth: TableWidth.unit
    }
  ];

  return {
    columns
  };
}
