<template>
  <el-select
    v-if="loaded"
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  >
    <el-option
      v-for="item in options"
      :disabled="item.disabled"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
  <!-- 数据未加载完成时显示空白选择器 -->
  <el-select
    v-else
    class="w-full"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  />
</template>

<script setup lang="ts">
import { watch, ref, computed } from "vue";
import { useVModels } from "@vueuse/core";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";
import { getQualitySpecificationParticularDetailInsepectionItemList } from "@/api/quality-tracing";
import { useSelectedItem } from "./use-selected-item";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";
/**
 * 检测类别选择器
 */

interface IOption {
  label: string;
  value: string;
  disabled: boolean;
  /** 采集类型 （自动采集，系统推送） */
  collectionType: number;
}

const props = defineProps<{
  /** 工序Id */
  processId: string;
  /** 已选中类别的id */
  modelValue: string;
  /** 质量Id */
  qualityId: string;
  /** 生产阶段分类编码 */
  categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);

const { seletectItem } = useSelectedItem(null);

const options = ref<Array<IOption>>([]);
const loading = ref(false);
const loaded = ref(false);

const placeholder = computed(() => {
  return props.processId ? `请选择检测项` : "请先选择检测类别";
});

/**
 * @description: 根据检测类型Id获取检测类别
 */
const requestInspectionItemsByInspectionTypeId = useLoadingFn(async () => {
  const { data } = await getQualitySpecificationParticularDetailInsepectionItemList(
    props.processId,
    props.categoryCode,
    props.qualityId
  );
  if (!data) {
    return;
  }
  options.value = data.map(({ id, name, disabled, collectionType }) => ({
    label: name,
    value: id,
    disabled,
    collectionType: collectionType
  }));
  loaded.value = true;
  return options.value;
}, loading);

watch(
  () => props.processId,
  async id => {
    if (id) {
      await requestInspectionItemsByInspectionTypeId();
      // 当检测类别发生变化时，如果当前选中项不存在于新的检测类别中，则清空选中项
      const selectedItem = options.value.find(item => item.value === modelValue.value);
      if (!selectedItem) {
        modelValue.value = "";
        seletectItem.value = null;
      } else {
        seletectItem.value = selectedItem;
      }
    }
  },
  {
    immediate: true
  }
);

watch(
  modelValue,
  value => {
    const item = options.value.find(item => item.value === value);
    if (item) {
      seletectItem.value = item;
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped></style>
