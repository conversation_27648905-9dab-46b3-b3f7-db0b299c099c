@import "./responsive";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-plus.scss";
@import "./sidebar.scss";
@import "./dark.scss";
@import "./reset-element.scss";
@import "./font-misans-regular-result.css";
@import "./font-misans-medium-result.css";
@import "./font.scss";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

/* icon-point样式 */
.icon-point {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--el-color-danger);
}

body {
  font-family: "MiSans", "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", "微软雅黑", "PingFang SC",
    "Hiragino Sans GB", sans-serif;
}
