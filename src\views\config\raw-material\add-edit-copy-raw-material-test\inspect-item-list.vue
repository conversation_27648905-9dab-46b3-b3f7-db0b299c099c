<template>
  <DynamicForm v-if="list.length" :key="key" ref="formRef" :dynamic-form-data="list" :edit-mode="true" />
  <div class="empty text-center" v-else>
    <el-empty :image-size="120">
      <template #image> <EmptyData /> </template>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import DynamicForm from "@/components/DynamicForm";
import { IDynamicFormItem } from "@/models/dynamic-form/i-dynamic-form";
import EmptyData from "@/assets/svg/empty_data.svg?component";

/**
 * 原材料检检测项表单
 */

const formRef = ref<InstanceType<typeof DynamicForm>>();
const list = ref<IDynamicFormItem[]>([]);
const key = ref(Date.now());
/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validDynamicForm();
}

/**
 * @description: 初始化表单
 */
function initFormValue(data: Array<IDynamicFormItem>) {
  list.value = data;
  key.value = Date.now();
}

/**
 * @description: 获取表单值
 */
async function getFormValue() {
  const { submitData } = await formRef.value.payloadFormData();
  return submitData.map(item => {
    const { targetCode, targetValue, dataTypeIdentityDetail } = item;
    const { identityCode } = dataTypeIdentityDetail;
    return {
      dataCode: targetCode,
      dataValue: targetValue,
      identityCode
    };
  });
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>
