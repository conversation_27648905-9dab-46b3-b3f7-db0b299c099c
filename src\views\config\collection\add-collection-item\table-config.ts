import { EControlType, TableWidth } from "@/enums";
import { computed } from "vue";
import { ISaveCollectionItemsReq } from "@/models/collection-items";

/**
 * @description: 生成列配置
 */
export function genColumns(collectionFormInfo: ISaveCollectionItemsReq) {
  /** 选项列表下拉框值 */
  const normalColumns: TableColumnList = [
    {
      label: "编号",
      type: "index",
      width: TableWidth.number
    },
    {
      label: "名称",
      prop: "identityLabel",
      slot: "identityCode"
    },
    {
      label: "值",
      prop: "identityValue",
      slot: "identityValue"
    },
    {
      label: "操作",
      slot: "operate",
      width: TableWidth.operation
    }
  ];

  const waveRoseColumns: TableColumnList = [
    {
      label: "波形数据类型",
      prop: "valueType",
      width: TableWidth.operation,
      slot: "valueType"
    },
    {
      label: "波形数据字段",
      prop: "name",
      slot: "name"
    },
    {
      label: "显示名称",
      prop: "display",
      slot: "display"
    },
    {
      label: "操作",
      slot: "operate",
      width: TableWidth.operation
    }
  ];

  return computed(() => {
    if (collectionFormInfo.identityCode === EControlType.WaveRoseControl) {
      return waveRoseColumns;
    }
    return normalColumns;
  });
}

/**
 * @description: 根据表单控件类型添加表格数据
 */
export function handleAddTableItem(collectionFormInfo: ISaveCollectionItemsReq) {
  if (collectionFormInfo.identityCode !== EControlType.WaveRoseControl) {
    collectionFormInfo.identityDetailList.push({
      id: null,
      identityLabel: "",
      identityValue: ""
    });
  } else {
    if (!collectionFormInfo.waveFormConfig) {
      collectionFormInfo.waveFormConfig = [];
    }
    collectionFormInfo.waveFormConfig.push({
      id: null,
      valueType: null,
      name: "",
      display: ""
    });
  }
}

/**
 * @description: 切换表格数据
 */
export function getTableData(collectionFormInfo: ISaveCollectionItemsReq) {
  if (collectionFormInfo.identityCode !== EControlType.WaveRoseControl) {
    return collectionFormInfo.identityDetailList;
  } else {
    return collectionFormInfo.waveFormConfig;
  }
}

/**
 * @description: 移除表格数据
 */
export function handleRemoveTableItem(collectionFormInfo: ISaveCollectionItemsReq, index: number) {
  if (collectionFormInfo.identityCode !== EControlType.WaveRoseControl) {
    collectionFormInfo.identityDetailList.splice(index, 1);
  } else {
    collectionFormInfo.waveFormConfig.splice(index, 1);
  }
}
