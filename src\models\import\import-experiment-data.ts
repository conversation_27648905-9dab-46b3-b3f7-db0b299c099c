export interface ExperimentDataImportItem {
  id: string;
  subClassCode: string;
  subClassName: string;
  processId: string;
  processCode: string;
  processName: string;
  /** 导入时间 */
  importTime: string;
  /** 总计条数 */
  totalCount: number;
  /** 成功条数 */
  successCount: number;
  /** 错误条数 */
  faultCount: number;
  /** 操作人 */
  opUserName: string;
  /** 错误数据 */
  faultFileInfo: FaultFileInfo;
  remark: string;
}

export interface FaultFileInfo {
  id: string;
  name: string;
  path: string;
  url: string;
  downloadUrl: string;
  bucket: string;
  fileSize: number;
}

export interface GetExperimentDataImportTemplateParams {
  subClassCode: string;
  processCode: string;
}
