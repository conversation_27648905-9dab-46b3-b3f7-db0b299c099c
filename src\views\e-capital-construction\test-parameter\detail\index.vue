<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="px-6 pb-2 bg-bg_color">
      <Header />
    </div>
    <div v-loading="loading" class="bg-bg_color p-5 pt-3 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="flex-bc mb-4">
        <ProjectRadio class="flex-1" @change="handleChangeSelect" />
        <div>
          <el-button v-if="!isEdit" type="primary" @click="handleEdit">编辑</el-button>
          <el-button v-if="isEdit" @click="handleReset">重置</el-button>
          <el-button v-if="isEdit" @click="handleCancle">取消</el-button>
          <el-button v-if="isEdit" type="primary" @click="handleSave">保存</el-button>
        </div>
      </div>
      <TableForm
        v-if="tableData.length"
        ref="tableFormRef"
        :table-data="tableData"
        class="h-full"
        :edit-mode="isEdit"
      />
      <div class="empty text-center" v-else>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { CollectionItemModel } from "@/models/e-capital-construction";
// components
import Header from "./components/header/header.vue";
import ProjectRadio from "./components/project-radio/project-radio.vue";
import TableForm from "./components/table-form/table-form.vue";
// utils
import { getCollectionItemListApi, saveCollectionForm } from "@/api/e-capital-construction/test-parameter/index";
/**
 * E基建-试验参数标准
 */

// 设置标题
const route = useRoute();
usePageStoreHook().setTitle("出厂试验参数标准" as string);

// const { columnsConfig } = genLoginReportTableColumnsConfig();
const loading = ref(false);
const tableData = ref<CollectionItemModel[]>([]);
const isEdit = ref(false); /** 编辑状态 */
const collectionCode = ref("");
const tableFormRef = ref();

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await getCollectionItemListApi({
    id: route.query.id as string,
    collectionCode: collectionCode.value
  });
  tableData.value = data;
}, loading);

/**
 * @description: 编辑
 */
const handleEdit = async () => {
  isEdit.value = true;
};
/**
 * @description: 重置
 */
const handleReset = async () => {
  requestList();
};
/**
 * @description: 切换
 */
const handleChangeSelect = async (val: string) => {
  collectionCode.value = val;
  isEdit.value = false;
  requestList();
};

/**
 * @description: 取消
 */
const handleCancle = async () => {
  isEdit.value = false;
};
/**
 * @description: 保存
 */
const handleSave = async () => {
  const params = tableFormRef.value.getFormValue();
  const { data } = await saveCollectionForm(route.query.id as string, params);
  if (data) {
    ElMessage.success("保存成功 ！");
    requestList();
    isEdit.value = false;
  }
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.label {
  @apply text-base text-secondary mb-2;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
