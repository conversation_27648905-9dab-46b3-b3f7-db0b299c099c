import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 变压器
 */
export const transformerConfig: MaterialSubClassConfig = {
  name: "变压器",
  subClassCode: MaterialSubclassCode.TRANSFORMER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 电抗器
 */
export const reactorConfig: MaterialSubClassConfig = {
  name: "电抗器",
  subClassCode: MaterialSubclassCode.REACTOR,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 电流互感器
 */
export const currentTransformerConfig: MaterialSubClassConfig = {
  name: "电流互感器",
  subClassCode: MaterialSubclassCode.CURRENT_TRANSFORMER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 电压互感器
 */
export const valtageTransformerConfig: MaterialSubClassConfig = {
  name: "电压互感器",
  subClassCode: MaterialSubclassCode.VOLTAGE_TRANSFORMER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 避雷器
 */
export const lightningArresterConfig: MaterialSubClassConfig = {
  name: "避雷器",
  subClassCode: MaterialSubclassCode.LIGHTNING_ARRESTER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 物资品类-线圈配置
 */
export const coil: MaterialCategoryConfig = {
  name: "线圈",
  categoryCode: MaterialCategoryCode.COIL,
  subClassMap: {
    transformerConfig,
    [MaterialSubclassCode.TRANSFORMER]: transformerConfig,
    reactorConfig,
    [MaterialSubclassCode.REACTOR]: reactorConfig,
    currentTransformerConfig,
    [MaterialSubclassCode.CURRENT_TRANSFORMER]: currentTransformerConfig,
    valtageTransformerConfig,
    [MaterialSubclassCode.VOLTAGE_TRANSFORMER]: valtageTransformerConfig,
    lightningArresterConfig,
    [MaterialSubclassCode.LIGHTNING_ARRESTER]: lightningArresterConfig
  }
};

export default coil;
