import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { ICollectionItem, IMaterial, IMaterialReq } from "@/models/south-grid-access";
import { IResponse } from "@/models";

/** 查询南网信息类别列表-原材料、生产过程、出厂试验通用  */
export const queryMaterialInfoList = (data: IMaterialReq) => {
  const url: string = withApiGateway("admin-api/southgrid/materials/getInfoList");
  return http.post<IMaterialReq, IResponse<Array<IMaterial>>>(url, {
    data
  });
};

export const getMaterialCollections = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/materials/${id}`);
  return http.get<void, IResponse<Array<ICollectionItem>>>(url);
};
