import { defineStore } from "pinia";
import * as api from "@/api/workbench";
import { AchievementPeriodEnum } from "@/enums";
import { IAchievementStatistics } from "@/models";

export const useAchievementStatisticsStore = defineStore({
  id: "cx-achievement-statistics",
  state: () => ({
    loading: false,
    period: AchievementPeriodEnum.MONTH,
    statistics: {} as IAchievementStatistics
  }),
  getters: {
    purchaseCount: state => state.statistics.purchaseCount || 0,
    purchaseProportion: state => state.statistics.purchaseProportion || 0,
    purchaseData: state => state.statistics.purchaseData?.map(({ count }) => count) || [],
    salesCount: state => state.statistics.salesCount || 0,
    salesProportion: state => state.statistics.salesProportion || 0,
    salesData: state => state.statistics.salesData?.map(({ count }) => count) || []
  },
  actions: {
    refresh(loading = false) {
      this.loading = loading;
      api
        .getAchievementStatistics(this.period)
        .then(res => res.data)
        .then(statistics => (this.statistics = statistics))
        .finally(() => (this.loading = false));
    },
    changePeriod(period: AchievementPeriodEnum) {
      this.period = period;
      this.refresh(true);
    }
  }
});
