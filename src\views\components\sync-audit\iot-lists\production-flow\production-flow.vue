<template>
  <BaseList :columns="columns" type="production-flow" v-bind="$attrs" ref="baseList" />
  <EditProcessInspectionDialog ref="editDialog" :subclass-code="subclassCode" />
</template>

<script setup lang="ts">
import { KeywordAliasEnum, StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import BaseList from "../base-list.vue";
import { computed, h, provide, ref } from "vue";
import { syncAuditCellSpanKey } from "../../tokens";
import { OperatorCell } from "@/components/TableCells";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditProcessInspectionDialog from "@/views/components/state-grid-order-sync/dialogs/edit-process-inspection-dialog.vue";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const store = useStateGridSyncAuditStore();
const { dateFormatter, enumFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const editDialog = ref<InstanceType<typeof EditProcessInspectionDialog>>();

const subclassCode = computed(() => store.subClassCode);

provide(stateGridOrderSyncEditKey, {
  refreshFn: () => baseList.value?.refresh()
});

const normalColumns: TableColumnList = [
  {
    label: "检测项",
    prop: "inspectionName",
    minWidth: TableWidth.name,
    fixed: "right"
  },
  {
    label: "检测值",
    prop: "inspectionValue",
    minWidth: TableWidth.order,
    fixed: "right",
    className: "error-mark"
  },
  {
    label: "数据格式要求",
    prop: "inspectionRule",
    minWidth: TableWidth.name,
    fixed: "right",
    className: "error-mark"
  }
];

const armourClampColumns: TableColumnList = [
  {
    label: "产品型号",
    prop: "model",
    width: TableWidth.order
  },
  {
    label: "生产批次号",
    prop: "productBatchNo",
    width: TableWidth.order
  },
  {
    label: "加工方式(本体)",
    prop: "processMethod",
    width: TableWidth.order
  },
  {
    label: "加工件数(本体)",
    prop: "processNumber",
    width: TableWidth.order
  },
  {
    label: "生产完成时间",
    prop: "productFinishTime",
    formatter: dateFormatter(),
    width: TableWidth.dateTime
  }
];

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: store.isCable ? "生产订单号" : "生产工单号",
    prop: "orderNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: store.isCable ? KeywordAliasEnum.IPO_NO : "product_work_order_no",
        defaultText: store.isCable ? "生产订单号" : "生产工单号"
      }),
    width: TableWidth.order
  },
  {
    label: "过程检编号",
    prop: "code",
    minWidth: TableWidth.largeOrder
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  ...(store.isArmourClamp ? armourClampColumns : normalColumns),
  {
    label: "操作",
    prop: "operator",
    width: TableWidth.operation,
    fixed: "right",
    className: "no-default",
    cellRenderer: data => {
      if (!data.row.editable) {
        return null;
      }
      return OperatorCell([
        {
          name: "编辑",
          action: () => editDialog.value.openEditDialog(data.row),
          props: { type: "primary" }
        }
      ]);
    }
  }
];
provide(syncAuditCellSpanKey, {
  uniqKeys: ["processCode", "processCode_orderNo", "id"],
  spanTypes: {
    processName: "processCode",
    orderNo: "processCode_orderNo",
    code: "id",
    syncResult: "id",
    operator: "id"
  }
});
</script>

<style scoped></style>
