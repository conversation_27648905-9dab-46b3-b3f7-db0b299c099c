import { type Component, InjectionKey, Ref } from "vue";
import { ScrollbarInstance } from "element-plus";

export interface IAnchor {
  id: string;
  pauseListenScroll?: boolean;
  scrollBar?: Ref<ScrollbarInstance>;
}

export const syncAuditAnchorKey: InjectionKey<IAnchor> = Symbol("sync audit anchor");

export interface ICellSpan {
  uniqKeys: Array<string>;
  spanTypes: Record<string, string>;
}

export const syncAuditCellSpanKey: InjectionKey<ICellSpan> = Symbol("sync audit cell span");

export interface ISyncAuditItem {
  id: string;
  title: string;
  component: Component<{ cardId: string }>;
  collapsed?: boolean;
}

export interface ISyncAuditNavigationItem {
  id: string;
  title: string;
  hasError?: boolean;
}

export interface ISyncAuditState {
  allowFilterByOrderNo: boolean;
  hasBusinessItem: boolean;
  hasIotItem: boolean;
  navigationItems: Array<ISyncAuditNavigationItem>;
  businessItems: Array<ISyncAuditItem>;
  iotItems: Array<ISyncAuditItem>;
  subClassCode?: string;
}

export const syncAuditStateKey: InjectionKey<ISyncAuditState> = Symbol("sync audit state");
