<template>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    placeholder="输入销售订单号/采购订单号/采购方公司名称/项目名称/备注"
    @search="onSearch()"
  >
    <ElButton
      v-auth="PermissionKey.form.formPurchaseSalesCreate"
      type="primary"
      size="large"
      @click="onCreateSalesOrder()"
      :icon="Plus"
      >新增销售订单</ElButton
    >
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm/src/search-form.vue";
import { IKeywordField, ISearchItem } from "@/components/SearchForm";
import { defineComponent, h, onMounted, reactive } from "vue";
import { ElDatePicker, ElSelect, ElOption } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";
import { PermissionKey, SalesOrderStatusOption } from "@/consts";
import { ISalesOrderParams } from "@/models";
import SynchronousFlagSelect from "@/views/components/synchronous-flag";
import CategorySelect from "@/views/components/category-select";
import { useRoute } from "vue-router";
const route = useRoute();

const emits = defineEmits<{
  (e: "onCreateSalesOrder");
  (e: "onSearch", params: ISalesOrderParams);
}>();

const form = reactive({
  createTime: [],
  subclassCode: undefined,
  orderProgress: undefined,
  orFilters: [] as Array<Record<string, string>>
});

onMounted(() => {
  const { start, end } = route.query;
  if (start && end) {
    form.createTime = [start as string, end as string];
  }
});

// 定义物资品类下拉框数据
const SyncCategoryCodeComponents = defineComponent({
  name: "CategoryCode",
  render() {
    return h(CategorySelect, { clearable: true });
  }
});

// 订单进度
const SalesOrderStatusComponents = defineComponent({
  name: "SalesOrderStatus",
  render() {
    // 生产状态
    const options = SalesOrderStatusOption.map(option => {
      return h(ElOption, { label: useI18n().t(option.label), value: option.value }, () => useI18n().t(option.label));
    });
    return h(ElSelect, { clearable: true }, () => options);
  }
});

// 标识
const FlagComponents = defineComponent({
  name: "matSyncFlagId",
  render() {
    return h(SynchronousFlagSelect, { clearable: true });
  }
});

const items: Array<ISearchItem> = [
  { full: false, key: "CategoryCode", label: "物资品类：", component: SyncCategoryCodeComponents },
  { full: false, key: "orderProgress", label: "订单进度：", component: SalesOrderStatusComponents },
  { full: false, key: "matSyncFlagId", label: "标识：", component: FlagComponents },
  {
    key: "createTime",
    label: "创建时间：",
    component: ElDatePicker,
    componentProps: {
      type: "daterange",
      rangeSeparator: "～",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  }
];

const keywordFields: Array<IKeywordField> = [
  { key: "salesOrder.so_no", title: "销售订单号" },
  { key: "purchaseSalesLink.po_no", title: "采购订单号" },
  { key: "salesOrder.buyer_name", title: "采购方公司名称" },
  { key: "salesOrder.prj_name", title: "项目名称" },
  { key: "salesOrder.remark", title: "备注" }
];

/** 创建销售订单 */
const onCreateSalesOrder = () => {
  emits("onCreateSalesOrder");
};

const onSearch = () => {
  emits("onSearch", form);
};
</script>

<style scoped>
:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}
</style>
