import { IOption, IProcess, IResponse, IProcessReq, IProcessRouteList } from "@/models";
import { defineStore } from "pinia";
import * as processService from "@/api/process";
import { getProcessRouteList } from "@/api/process";

/** 工序 */
export const useProcessStore = defineStore({
  id: "cx-process-store",
  state: () => ({
    process: [] as Array<IProcess>,
    options: [] as Array<IOption>,
    boundProcess: [] as Array<IProcess>,
    processRouteOptions: [] as Array<IProcessRouteList>
  }),
  actions: {
    async queryProcess(categoryCode: string) {
      const processRes: IResponse<Array<IProcess>> = await processService.queryProcess(categoryCode);
      if (!Array.isArray(processRes.data) || processRes.data.length === 0) {
        this.process = [];
        this.options = [];
        return;
      }

      this.process = processRes.data;
      this.options = processRes.data.map(item => ({ label: item.processName, value: item.id }));
    },
    async getProductionProcessListByCategoryCodeAndProductionStage(categoryCode: string) {
      if (!categoryCode) {
        this.process = [];
        this.options = [];
        return;
      }
      const processRes: IResponse<Array<IProcess>> =
        await processService.getProductionProcessListByCategoryCodeAndProductionStage(categoryCode);
      if (!Array.isArray(processRes.data) || processRes.data.length === 0) {
        this.process = [];
        this.options = [];
        return;
      }

      this.process = processRes.data;
      this.options = processRes.data.map(item => ({ label: item.processName, value: item.id }));
    },

    /** 查询工艺路线 */
    async getProcessRouteList(subClassCode: string) {
      const res = await getProcessRouteList(subClassCode);
      const processList = res.data.filter(item => item.status);
      this.processRouteOptions = processList;
      return processList;
    },

    /**
     * 根据工艺路径id获取已绑定的工序(分页)
     */
    async queryBoundProcessPage(data: IProcessReq) {
      const res = await processService.queryBoundProcessPage(data);
      this.boundProcess = res.data.list;
    },
    /**
     * 根据物资种类编码获取指定工艺路线下未绑定的工序
     * categoryCode 物资种类
     * processRouteId 工艺路线ID
     */
    async queryNotBoundProcess(categoryCode: string, processRouteId: string) {
      const res = await processService.queryNotBoundProcess(categoryCode, processRouteId);
      this.process = res.data;
    },
    /** 工艺路线绑定工序 */
    async bindProcess(data: IProcessReq) {
      return await processService.bindProcess(data);
    },
    /** 工艺路线解绑工序 */
    async unbindProcess(data: IProcessReq) {
      return await processService.unbindProcess(data);
    }
  }
});
