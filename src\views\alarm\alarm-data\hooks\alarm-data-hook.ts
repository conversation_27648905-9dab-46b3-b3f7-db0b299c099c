import { AlarmTypeEnum } from "@/enums";
import { IAlarmData, IAlarmDataDetail, IAlarmDataParam, IAlarmSolveReq } from "@/models";
import { useAlarmDataStore } from "@/store/modules";

/**
 * 告警的hook
 */

export const useAlarmDataHook = () => {
  const alarmDataStore = useAlarmDataStore();

  // 获取告警的列表数据
  async function getAlarm(params: IAlarmDataParam) {
    const tableData: Array<IAlarmData> = await alarmDataStore.queryAlarm(params);
    return tableData;
  }

  // 根据Id获取告警详情
  async function getAlarmDataById(id: string, alarmType: AlarmTypeEnum) {
    const alarmData: IAlarmDataDetail = await alarmDataStore.getAlarmDataById(id, alarmType);
    return alarmData;
  }

  // 根据ID解决告警
  async function solveAlarmById(params: IAlarmSolveReq, alarmType: AlarmTypeEnum) {
    const res: boolean = await alarmDataStore.solveAlarmById(params, alarmType);
    return res;
  }

  //给选中的告警明细详情赋值
  function setAlarmDataStorage(alarmData: IAlarmData) {
    alarmDataStore.setAlarmDataStorage(alarmData);
  }

  // 告警数据明细详情清空
  function clearAlarmDataStorage() {
    alarmDataStore.clearAlarmDataStorage();
  }

  // 给表单赋值key
  function getRowKeyOfTarget(row: IAlarmData) {
    return `${row.id}`;
  }

  return {
    getAlarm,
    getAlarmDataById,
    solveAlarmById,
    setAlarmDataStorage,
    clearAlarmDataStorage,
    getRowKeyOfTarget
  };
};
