<template>
  <el-dialog
    v-model="editVisible"
    title="编辑过程检"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <AddProcessInspection ref="editFormRef" :inspectId="orderId" :subClassCode="subclassCode" :processId="processId" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import AddProcessInspection from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/edit-production-process-inspect/index.vue";
import { useEdit } from "@/views/components/state-grid-order-sync/hooks";
import { IProductionFlowSync } from "@/models";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { provide, reactive, watch } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";

const props = defineProps<{
  subclassCode: string;
}>();

const productionProcessInspecStore = useProductionProcessInspecStore();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof AddProcessInspection>>(updateProcessInspection);

const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

/** 订单 */
let orderId: string;
/** 工序id */
let processId: string;

async function openEditDialog(data: IProductionFlowSync) {
  productionProcessInspecStore.mode = "edit";
  await productionProcessInspecStore.getDetailOfProdProcessInfo(data.dataId);
  editVisible.value = true;
  orderId = data.dataId;
  processId = data.processId;
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.code
  };
}

async function updateProcessInspection() {
  const saveFormData = await editFormRef.value.getFormValue();
  if (!saveFormData?.code) {
    return;
  }
  await productionProcessInspecStore.saveProductionProcessCheck({
    ...saveFormData,
    id: orderId
  });
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
