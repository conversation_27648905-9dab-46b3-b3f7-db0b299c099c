<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" :close-dialog="closeDialog">
      <el-button v-if="isAddMode" type="primary" link @click="openDialog"> 新增 </el-button>
      <el-button v-if="isEditMode" link type="primary" @click="openDialog"> 编辑 </el-button>
    </slot>
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <div v-loading="loading">
        <base-info-form
          ref="baseInfoFormRef"
          :category-code="categoryCode"
          :production-stage-id="productionStageId"
          :quality-id="qualityId"
        />
        <reach-standard-form v-if="isSelectedReachingRate" ref="reachStandardFormRef" />
        <range-list-form ref="rangeListFormRef" :judgment-type="judgeType" :limit-score="currentScore" />
      </div>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            v-if="isAddMode"
            type="warning"
            @click="handleClickSaveBtn('save-and-continue')"
            :loading="saveloading"
            >保存, 并继续新增</el-button
          >
          <el-button type="primary" @click="handleClickSaveBtn('only-save')" :loading="saveloading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";
import BaseInfoForm from "./base-info-form/index.vue";
import ReachStandardForm from "./reach-standard-form.vue";
import RangeListForm from "./range-list-form/index.vue";
import { EditQualitySpecificationParticularDetailParams } from "@/models/quality-tracing";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  addQualitySpecificationParticularDetail,
  editQualitySpecificationParticularDetail,
  getQualitySpecificationParticularDetail
} from "@/api/quality-tracing";
import {
  baseInfoFormValueToDTO,
  judgementStandardListToDTO,
  reachStandardFormValueToDTO,
  rawDataToJudgementStandardList,
  rawDatoToReachStandardFormValue,
  rawDatoToBaseInfoFormValue
} from "./converter";

/**
 * 新增/编辑 检测标准（原材料检、生产过程、出厂试验、工艺稳定性）
 */

const saveloading = ref(false);
const loading = ref(false);

const props = withDefaults(
  defineProps<{
    /** 模式 */
    mode: "edit" | "add";
    /** 生产阶段Id */
    productionStageId: string;
    /** 质量规范Id */
    qualityId: string;
    /** 类型编码 */
    categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum;
    /** 标题 */
    title?: string;
    /** 检测标准Id */
    id?: string;
  }>(),
  {
    title: "检测标准"
  }
);

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const baseInfoFormRef = ref<InstanceType<typeof BaseInfoForm>>();
const reachStandardFormRef = ref<InstanceType<typeof ReachStandardForm>>();
const rangeListFormRef = ref<InstanceType<typeof RangeListForm>>();

/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");

/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

/** 当前判断方式 */
const judgeType = computed(() => {
  if (!baseInfoFormRef.value) {
    return 0;
  }
  return baseInfoFormRef.value.getFormValue().judgeType;
});

/** 当前分值 */
const currentScore = computed(() => {
  if (!baseInfoFormRef.value) {
    return "";
  }
  return baseInfoFormRef.value.getFormValue().score;
});

/**
 * 是否选择了 达标率
 */
const isSelectedReachingRate = computed(() => {
  if (judgeType.value === 1) {
    return true;
  }
  return false;
});

/**
 * @description: 表单校验函数
 */
async function validateForm() {
  if (isSelectedReachingRate.value) {
    const formResSet = await Promise.all([
      baseInfoFormRef.value?.validateForm(),
      reachStandardFormRef.value?.validateForm(),
      rangeListFormRef.value?.validateForm()
    ]);
    return formResSet.every(res => res === true);
  }
  const formResSet = await Promise.all([baseInfoFormRef.value?.validateForm(), rangeListFormRef.value?.validateForm()]);
  return formResSet.every(res => res === true);
}

/**
 * @description: 获取检测标准详情
 */
const requestQualitySpecificationParticularDetail = useLoadingFn(async () => {
  const res = await getQualitySpecificationParticularDetail(props.id);
  return res;
}, loading);

/**
 * @description: 请求保存
 */
const requestSaveQualitySpecificationParticularDetail = useLoadingFn(
  async (params: EditQualitySpecificationParticularDetailParams) => {
    if (isEditMode.value) {
      return await editQualitySpecificationParticularDetail(props.id, params);
    }
    return await addQualitySpecificationParticularDetail(params);
  },
  saveloading
);

/**
 * @description: 保存按钮点击事件
 * @param type 保存类型 save-and-continue 保存并继续新增 | only-save 仅保存
 */
const handleClickSaveBtn = async (type: "save-and-continue" | "only-save") => {
  const isValidate = await validateForm();
  if (!isValidate) {
    return;
  }

  const baseInfoFormValue = baseInfoFormValueToDTO(baseInfoFormRef.value?.getFormValue());
  const reachStandardFormValue = reachStandardFormValueToDTO(reachStandardFormRef.value?.getFormValue());
  const rangeListFormValue = judgementStandardListToDTO(rangeListFormRef.value?.getFormValue());

  const params: EditQualitySpecificationParticularDetailParams = {
    qualityId: props.qualityId,
    category: props.categoryCode,
    judgeStandard: rangeListFormValue,
    ...baseInfoFormValue,
    ...reachStandardFormValue
  };

  const { data } = await requestSaveQualitySpecificationParticularDetail(params);

  if (!data) {
    return;
  }

  emits("postSaveSuccess");
  ElMessage({
    message: isEditMode.value ? "编辑成功" : "新增成功",
    type: "success"
  });

  switch (type) {
    case "save-and-continue":
      baseInfoFormRef.value?.resetForm();
      if (isSelectedReachingRate.value) {
        reachStandardFormRef.value?.resetForm();
      }
      rangeListFormRef.value?.resetForm();
      break;
    case "only-save":
      closeDialog();
      break;
  }
};

/**
 * @description: 编辑模式初始化数据
 */
async function initEditData() {
  const { data } = await requestQualitySpecificationParticularDetail();
  if (!data) {
    return;
  }
  const baseInfoFormValue = rawDatoToBaseInfoFormValue(data);
  const reachStandardFormValue = rawDatoToReachStandardFormValue(data);
  const judgementStandardList = rawDataToJudgementStandardList(data);
  baseInfoFormRef.value?.initFormValue(baseInfoFormValue);
  // 达标率 是否显示取决于，基础表单，需要放在nextTick中等待基础表单初始化完成
  nextTick(() => {
    reachStandardFormRef.value?.initFormValue(reachStandardFormValue);
  });
  rangeListFormRef.value?.initFormValue(judgementStandardList);
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, visible => {
  if (visible) {
    if (isEditMode.value) {
      initEditData();
    }
  }
});
</script>

<style scoped></style>
