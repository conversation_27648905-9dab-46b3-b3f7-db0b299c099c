<template>
  <div v-if="props.pointList.length" class="miss-item">
    <FillInProductionData
      :production-id="props.productionId"
      :sale-order-id="calcSaleOrderId"
      :purchase-order-id="calcPurchaseOrderId"
      :mode="calcSaleOrderId ? 'SALE' : 'PURCHASE'"
      :navigate="navigateParams"
      loadingTarget="#data-integrity-check-table"
    >
      <span class="miss-text">{{ props.text }} </span> <span>（{{ props.pointList.length }}）</span>
      <div class="navigateTo">
        <div v-for="(items, index) of state.pointList" class="item" :key="index">
          <div class="marjor-icon" v-if="items[1]">*</div>
          {{ items[0] }}
        </div>
      </div>
    </FillInProductionData>
    <div v-if="props.pointList.length > number && state.isMorePoint" class="more" @click="morePointList()">...更多</div>
    <div v-if="props.pointList.length > number && !state.isMorePoint" class="more" @click="lessPointList()">
      ...收起
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watchEffect, computed } from "vue";
import FillInProductionData from "@/views/components/fill-in-production-data/index.vue";
import { ETestSensingType } from "@/enums/purchase-order/production-test-sensing";

const props = defineProps<{
  text: string;
  pointList: Array<string>;
  requiredPointList: Array<string>;
  productionId: string;
  purchaseOrderId: string;
  saleOrderId: string;
  productionTestKey: ETestSensingType;
  productionTestIndex: number;
}>();

//每次展示多少个数据，再多就是more
const number = 3;
const state = reactive<{
  isMorePoint: boolean;
  pointList: Array<Array<string | boolean>>;
}>({
  isMorePoint: false,
  pointList: []
});

const calcPurchaseOrderId = computed(() => {
  const tempArr = props.purchaseOrderId.split(",");
  if (tempArr.length) {
    return tempArr[0];
  }
  return props.purchaseOrderId;
});

const calcSaleOrderId = computed(() => {
  const tempArr = props.saleOrderId.split(",");
  if (tempArr.length) {
    return tempArr[0];
  }
  return props.saleOrderId;
});

/**
 * @description: 导航到生产数据——生产试验感知的参数
 */
const navigateParams = computed(() => ({
  index: props.productionTestIndex,
  key: props.productionTestKey
}));

watchEffect(() => {
  setPointList(number);
});

function setRequired(item: string) {
  return props.requiredPointList.includes(item);
}

function morePointList(): void {
  setPointList();
  state.isMorePoint = false;
}

function lessPointList(): void {
  setPointList(number);
  state.isMorePoint = true;
}

function setPointList(cutNum?: number) {
  const pointList = props.pointList?.map(item => [item, setRequired(item)]) ?? [];
  state.pointList = cutNum ? pointList.slice(0, cutNum) : pointList;
  state.isMorePoint = cutNum ? pointList.length > cutNum : false;
}
</script>

<style scoped lang="scss">
.miss-item {
  font-weight: 600;
  margin-left: 12px;
  cursor: pointer;

  &:hover {
    color: rgb(85, 136, 238);

    .miss-text {
      text-decoration: underline;
    }

    .item {
      text-decoration: underline;
    }
  }
}

.item {
  font-weight: normal;
  font-size: 13.5px;
  margin-left: 18px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;

  &:hover {
    color: rgb(85, 136, 238);
  }
}

.more {
  font-size: 13.5px;
  margin-left: 18px;
  font-weight: 600;
  cursor: pointer;

  &:hover {
    color: rgb(85, 136, 238);
  }
}

.marjor-icon {
  color: rgb(85, 136, 238);
  display: inline-block;
}
</style>
