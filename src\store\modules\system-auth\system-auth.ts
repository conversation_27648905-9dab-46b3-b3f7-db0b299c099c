import { defineStore } from "pinia";
import * as api from "@/api/business-license";
import { IBusinessLicense, ILicenseVerify } from "@/models";
import { LicenseEnum } from "@/enums";
import { CHANNEL_CSG, CHANNEL_EIP, CHANNEL_IOT, CHANNEL_EJJ } from "@/consts";

export const useSystemAuthStore = defineStore({
  id: "cx-system-auth-store",
  state: () => ({
    /** license 授权信息，品类 功能列表格式化具体信息 用于展示授权具体信息 */
    businessInfoLicense: {} as IBusinessLicense,

    /** license 授权信息，品类 功能列表没有格式化 用于系统判断权限 */
    businessLicenseAuth: {} as IBusinessLicense
  }),

  getters: {
    /** 授权 是否为企业版本 */
    async getIsEnterprise(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth?.authorization?.edition === LicenseEnum.Enterprise;
    },

    /** 授权是否包含IOT */
    async checkLicenseAuthIncludeIOT(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_IOT);
    },

    /** 授权是否包含EIP */
    async checkLicenseAuthIncludeEIP(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_EIP);
    },

    /** 授权是否包含EIP */
    async checkLicenseAuthIncludeCSG(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_CSG);
    },

    /** 授权是否包含EJJ */
    async checkLicenseAuthIncludeEJJ(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_EJJ);
    },

    /** 授权是否仅仅包含EIP */
    async checkLicenseAuthOnlyIncludeEIP(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return (
        !state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_IOT) &&
        state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_EIP)
      );
    },
    /** 授权是否仅仅包含IOT */
    async checkLicenseAuthOnlyIncludeIOT(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return (
        state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_IOT) &&
        !state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_EIP)
      );
    },
    /** 授权是否同时具有IOT和EIP */
    async checkLicenseAuthBothIncludeIOTAndEIP(state) {
      if (!state.businessLicenseAuth || Object.keys(state.businessLicenseAuth).length === 0) {
        state.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
      }
      return (
        state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_IOT) &&
        state.businessLicenseAuth.authorization.integrationChannel.includes(CHANNEL_EIP)
      );
    }
  },

  actions: {
    /** 查询以及授权的租户列表  */
    async queryAuthTenantList() {
      return await api.queryAuthTenantList();
    },

    async queryUnAuthTenant() {
      return await api.queryUnAuthTenant();
    },

    /** 获取单个租户授权信息 */
    async getTenantAuthInfo(id: string) {
      return await api.getTenantAuthInfo(id);
    },

    /** 获取单个租户授权信息[详细信息，包含物质，品类等] */
    async getTenantAuthDisplay(id: string) {
      return await api.getTenantAuthDisplay(id);
    },

    /** license 授权信息，品类 功能列表格式化具体信息 用于展示授权具体信息 */
    async getBusinessLicenseInfo() {
      this.businessInfoLicense = (await api.getBusinessLicenseInfo()).data;
    },

    /** license 授权信息，品类 功能列表没有格式化 用于系统判断权限 */
    async getBusinessLicenseAuth() {
      this.businessLicenseAuth = (await api.getBusinessLicenseAuth()).data;
    },

    /** 授权 */
    businessLicenseVerify(license: ILicenseVerify) {
      return api.businessLicenseVerify(license);
    },

    setBusinessLicenseAuth(businessLicenseAuth?: IBusinessLicense) {
      this.businessLicenseAuth = businessLicenseAuth;
    }
  }
});
