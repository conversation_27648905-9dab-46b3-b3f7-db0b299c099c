<template>
  <el-select
    v-if="loaded"
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    placeholder="请选择检测类别"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <!-- 数据未加载完成时显示空白选择器 -->
  <el-select
    v-else
    class="w-full"
    filterable
    clearable
    placeholder="请选择检测类别"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  />
</template>

<script setup lang="ts">
import { watch, ref } from "vue";
import { useVModels } from "@vueuse/core";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";
import { getProcessInfoByProductProcess } from "@/api";

/**
 * 检测类别选择器
 */

interface IOption {
  label: string;
  value: string;
  code: string;
}

const props = defineProps<{
  /** 生产阶段Id（对应检测规范明细中的不同分类） */
  productionStageId: string;
  /** 已选中类别的id */
  modelValue: string;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);

const options = ref<Array<IOption>>([]);
const loading = ref(false);
const loaded = ref(false);

/**
 * @description: 根据生产阶段Id获取检测类别
 */
const requestInspectionTypeByProductionStageId = useLoadingFn(async () => {
  const { data } = await getProcessInfoByProductProcess(props.productionStageId);
  if (!data) {
    return;
  }
  options.value = data.map(({ id, processName, processCode }) => ({
    label: processName,
    value: id,
    code: processCode
  }));
  loaded.value = true;
}, loading);

watch(
  () => props.productionStageId,
  id => {
    if (id) {
      requestInspectionTypeByProductionStageId();
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped></style>
