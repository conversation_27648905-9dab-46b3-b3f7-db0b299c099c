<template>
  <v-chart :option="option" autoresize class="min-w-[300px] w-full h-full" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import VChart from "vue-echarts";
import { IExperimentData } from "@/models";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";

const props = defineProps<{
  data: IExperimentData;
  dataZoom?: boolean;
}>();

const option = computed(() => {
  const yDataArr = props.data.values.map(({ value }) => value);
  const maxValue = Math.max(...yDataArr);
  const minValue = Math.min(...yDataArr);
  return {
    animation: false,
    grid: {
      left: "10%",
      top: "2%",
      right: "5%",
      bottom: props.dataZoom ? 80 : "20%"
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLabel: {
        show: true,
        color: "#909399",
        formatter: (oldLabel: string) => {
          let label = "";
          if (oldLabel) {
            const strArr = oldLabel.split(" ");
            label = strArr[0] + "\n" + strArr[1];
          }
          return label;
        }
      },
      data: props.data.values.map(value => formatDate(value.timestamp, fullDateFormat))
    },
    yAxis: {
      type: "value",
      axisLabel: {
        show: true,
        color: "#909399"
      },
      splitLine: {
        show: false
      },
      min: Math.floor(minValue - minValue * 0.1),
      max: Math.ceil(maxValue + maxValue * 0.1)
    },
    tooltip: {
      trigger: "axis"
    },
    series: [
      {
        type: "line",
        smooth: true,
        sampling: "lttb",
        data: props.data.values
      }
    ],
    dataZoom: props.dataZoom
      ? [
          {
            type: "inside"
          },
          {
            type: "slider",
            left: "15%",
            right: "15%"
          }
        ]
      : null,
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: !!props.data.values.length,
      style: {
        text: "暂无采集点数据",
        fontSize: "16px",
        fill: "#909399"
      }
    }
  };
});
</script>

<style scoped>
.not-data {
  text-align: center;
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-secondary);
}
</style>
