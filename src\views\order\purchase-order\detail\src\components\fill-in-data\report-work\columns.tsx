import { TableColumnRenderer } from "@pureadmin/table";
import { TableWidth } from "@/enums";
import { ElButton, ElMessage } from "element-plus";
import { IReportWork, IWorkOrder } from "@/models";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useReportWorkStore,
  useWorkOrderStore
} from "@/store/modules";
import { useConfirm } from "@/utils/useConfirm";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { PermissionKey } from "@/consts";

export function useColumns() {
  const reportWorkStore = useReportWorkStore();
  const workOrderStore = useWorkOrderStore();
  const purchaseStore = usePurchaseOrderDetailStore();
  const fillInDataStore = useFillInDataStore();
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "工序",
      prop: "processName",
      width: TableWidth.largeName
    },
    {
      label: "报工批次号",
      prop: "productBatchNo",
      width: TableWidth.order
    },
    {
      label: "设备",
      prop: "deviceName",
      width: TableWidth.largeName
    },
    {
      label: "开始时间",
      prop: "workStartTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "结束时间",
      prop: "workEndTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "报工地址",
      prop: "buyerProvince"
    },
    {
      label: "操作",
      prop: "op",
      fixed: "right",
      width: TableWidth.operation,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseWorkReportEdit}
              type="primary"
              link
              onClick={() => onEditReportOrder(data.row)}
            >
              编辑
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseWorkReportDelete}
              link
              type="danger"
              onClick={() => onDeleteReportOrder(data.row)}
            >
              删除
            </ElButton>
          </div>
        );
      }
    }
  ];

  const onEditReportOrder = (data: IReportWork) => {
    const workOrder = getWorkOrder();
    reportWorkStore.setReportWorkProps(workOrder.subclassCode, workOrder.processIds, workOrder.minClassCode);
    reportWorkStore.setIsAddReportWork(false);
    reportWorkStore.clearReportWorkDetailAndShowReportWorkFormModal();
    reportWorkStore.getReportWorkById(data.id);
  };

  const onDeleteReportOrder = async (data: IReportWork) => {
    if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
      return;
    }
    await reportWorkStore.deleteReportWork(data.id);
    ElMessage({ type: "success", message: "删除成功" });
    reportWorkStore.setFreshReportWorkTag(true);
    if (purchaseStore.isCable) {
      workOrderStore.refreshWorkOrders();
    }
  };

  function getWorkOrder() {
    if (purchaseStore.isCable) {
      return workOrderStore.workOrderDetail;
    }
    return fillInDataStore.data as IWorkOrder;
  }
  return { columns };
}
