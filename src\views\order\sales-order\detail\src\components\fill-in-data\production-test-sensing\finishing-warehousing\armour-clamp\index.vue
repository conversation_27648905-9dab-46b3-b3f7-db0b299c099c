<template>
  <div class="h-full flex flex-col">
    <div class="w-full text-right mb-5">
      <ElButton
        v-auth="PermissionKey.form.formPurchaseFinishProductCreate"
        type="primary"
        :icon="Plus"
        @click="onAddFinishingWarehousingModalVis()"
        >新增成品信息</ElButton
      >
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="finishedProductStorageStore.finishedProductStorages"
      :columns="columns"
      :height="300"
      showOverflowTooltip
      :pagination="pagination"
      :loading="loading"
      @page-current-change="pageChange"
      @page-size-change="pageSizeChange"
    >
      <template #operation="data">
        <div>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFinishProductEdit"
            type="primary"
            link
            @click="onEditFinishingWarehousingModalVis(data.row)"
          >
            编辑
          </ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFinishProductDelete"
            link
            type="danger"
            @click="onDeleteFinishingWarehousing(data.row)"
          >
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>

    <el-dialog
      :title="getFinishingWarehousingFormModalTitle()"
      align-center
      class="default"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-model="state.finishingWarehousingFormModalVis"
    >
      <FinishingWarehousingForm ref="finishingWarehousingFormRef" />

      <template #footer>
        <el-button @click="onCancelCreateFinishingWarehousing()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleOnCreateFinishingWarehousing()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useSalesFillInDataStore, useFinishedProductStorageStore, usePurchaseOrderDetailStore } from "@/store/modules";
import { useFinishedWarehousingHook } from "../hooks/useFinishingWarehousing";
import { onMounted, reactive, ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import FinishingWarehousingForm, { IFinishingWarehousingForm } from "./finishing-warehousing-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  ICreateFinishedProductStorage,
  IFinishedProductStorage,
  IFinishedProductStorageReq,
  IProductOrder,
  IWorkOrder
} from "@/models";
import { Plus } from "@element-plus/icons-vue";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { PermissionKey } from "@/consts";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useTableConfig } from "@/utils/useTableConfig";

const { columns } = useFinishedWarehousingHook();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const state = reactive<{
  finishingWarehousingFormModalVis: boolean;
  isAddFinishingWarehousing: boolean;
}>({
  finishingWarehousingFormModalVis: false,
  isAddFinishingWarehousing: false
});

const finishingWarehousingFormRef = ref<IFinishingWarehousingForm | null>();
const saveLoading = ref<boolean>(false);

const handleOnCreateFinishingWarehousing = useLoadingFn(onCreateFinishingWarehousing, saveLoading);
const getFinishingWarehousingFormModalTitle = () => (state.isAddFinishingWarehousing ? "新增成品信息" : "编辑成品信息");

const finishedProductStorageStore = useFinishedProductStorageStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const purchaseStore = usePurchaseOrderDetailStore();
const fillInDataStore = useSalesFillInDataStore();
const loading = ref<boolean>(false);
const initFinishingWarehousingData = useLoadingFn(getFinishedProductList, loading);
const defaultPageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

onMounted(() => {
  initFinishingWarehousingData(defaultPageInfo);
});

async function getFinishedProductList(params: IFinishedProductStorageReq) {
  await finishedProductStorageStore.queryFinishedProductStorage(params);
}

function getFinishingWarehousingDefaultValue() {
  if (purchaseStore.isCable) {
    const productOrder = fillInDataStore.data as IProductOrder;
    return {
      specificationModel: productOrder.specificationModel,
      voltageClasses: productOrder.voltageClasses,
      unit: productOrder.unit,
      productionId: productOrder.id,
      purchaseId: productOrder.purchaseId
    };
  }
  const workOrder = fillInDataStore.data as IWorkOrder;
  return {
    specificationModel: workOrder.specificationType,
    voltageClasses: workOrder.voltageLevel,
    unit: workOrder.unit,
    workOrderId: workOrder.id,
    purchaseId: workOrder.purchaseId
  };
}

/**
 * 页码改变
 */
const pageChange = (pageNo: number) => {
  initFinishingWarehousingData({ pageNo, pageSize: pagination.pageSize });
};

/**
 * 页码数量改变
 */
const pageSizeChange = (pageSize: number) => {
  pagination.currentPage = 1;
  initFinishingWarehousingData({ pageNo: 1, pageSize });
};

const onAddFinishingWarehousingModalVis = () => {
  state.isAddFinishingWarehousing = true;
  state.finishingWarehousingFormModalVis = true;

  const { specificationModel, voltageClasses, unit, productionId, workOrderId, purchaseId } =
    getFinishingWarehousingDefaultValue();
  finishedProductStorageStore.setCreateFinishedProductStorageDetail({
    specificationModel,
    voltageLevel: voltageClasses,
    unit,
    id: undefined,
    amount: undefined,
    storageTime: undefined,
    shipmentTime: undefined,
    shipmentStatus: undefined,
    isQualified: undefined,
    productionId,
    workOrderId,
    productNo: undefined,
    productBatchNo: undefined,
    purchaseId: purchaseId
  });
};

const onEditFinishingWarehousingModalVis = async (data: IFinishedProductStorage) => {
  state.isAddFinishingWarehousing = false;
  state.finishingWarehousingFormModalVis = true;
  const res = await finishedProductStorageStore.getFinishedProductStorageDetailById(data.id);
  if (res.code !== 0) {
    ElMessage.error(res.msg || "网络异常");
    return;
  }

  finishedProductStorageStore.setFinishedProductStorageDetail(res.data);
};

const onCancelCreateFinishingWarehousing = () => {
  state.finishingWarehousingFormModalVis = false;
  finishingWarehousingFormRef.value.resetFormValue();
};

/** 保存或者修改成品信息 */
async function onCreateFinishingWarehousing() {
  const formValue: ICreateFinishedProductStorage | boolean = await finishingWarehousingFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }

  // 新增/编辑
  if (!formValue.id) {
    await finishedProductStorageStore.createFinishedProductStorage(formValue);
  } else {
    await finishedProductStorageStore.editFinishedProductStorage(formValue);
  }

  state.finishingWarehousingFormModalVis = false;
  finishingWarehousingFormRef.value.resetFormValue();
  ElMessage.success(!formValue.id ? "新增成功" : "编辑成功");
  initFinishingWarehousingData(defaultPageInfo);
  productionTestSensingStore.refreshProductionProcessStatus();
}

const onDeleteFinishingWarehousing = (data: IFinishedProductStorage) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const delRes = await finishedProductStorageStore.deleteFinishedProductStorage(data.id);
      if (!delRes.data) {
        ElMessage.error(delRes.msg || "网络异常");
        return;
      }
      ElMessage.success("删除成功");
      initFinishingWarehousingData(defaultPageInfo);
      productionTestSensingStore.refreshProductionProcessStatus();
    })
    .catch(() => {});
};
</script>

<style scoped lang="scss"></style>
