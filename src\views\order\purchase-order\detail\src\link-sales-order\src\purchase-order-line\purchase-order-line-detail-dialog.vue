<template>
  <el-dialog
    title="采购订单行项目"
    v-model="visible"
    class="middle"
    :close-on-click-modal="false"
    align-center
    @open="dialogOpen"
    @close="dialogClose"
  >
    <Card margin="0 0 10px 0" title="基础信息" class="mb-5">
      <el-descriptions>
        <el-descriptions-item label="采购订单行项目号"> {{ line.poItemNo }}</el-descriptions-item>
        <el-descriptions-item label="采购订单行项目ID">{{ line.poItemId }}</el-descriptions-item>
        <el-descriptions-item label="物料编码">{{ line.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="采购数量">{{ line.amount }}</el-descriptions-item>
        <el-descriptions-item :span="2" label="物资小类名称">{{ line.matMinName }}</el-descriptions-item>
        <el-descriptions-item :span="3" label="物料描述">{{ line.materialDesc }}</el-descriptions-item>
      </el-descriptions>
    </Card>
    <Card margin="0 0 10px 0" title="销售订单行项目" class="mb-4">
      <template #action>
        <el-button
          v-auth="PermissionKey.form.formPurchaseSalesEdit"
          v-track="TrackPointKey.FORM_PURCHASE_LINE_SALES_REF"
          type="primary"
          @click="linkVisible = true"
        >
          <FontIcon class="mr-2" icon="icon-link" />关联销售订单行
        </el-button>
      </template>
      <pure-table
        show-overflow-tooltip
        :loading="loading"
        :columns="columns"
        :data="salesOrderLines"
        :max-height="300"
        row-key="id"
      >
        <template #empty>
          <CxEmpty />
        </template>
      </pure-table>
    </Card>

    <LinkSalesOrderLineDialog :purchaseLineId="line.id" v-model="linkVisible" @link-success="afterLink" />
  </el-dialog>
</template>

<script setup lang="ts">
import Card from "../card.vue";
import { computed, ref } from "vue";
import { IPurchaseOrderLine, ISalesOrderLine } from "@/models";
import { PureTable, TableColumns } from "@pureadmin/table";
import { OperatorCell } from "@/components/TableCells";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { usePurchaseOrderDetailPurchaseOrderLineStore } from "@/store/modules/purchase-order-detail";
import { ElMessage } from "element-plus";
import LinkSalesOrderLineDialog from "./link-sales-order-line-dialog.vue";
import { TableWidth } from "@/enums";
import { usePurchaseOrderLink } from "@/views/order/purchase-order/detail/src/hooks/usePurchaseOrderLink";
import { useConfirm } from "@/utils/useConfirm";
import { MEASURE_UNIT, PermissionKey, TrackPointKey } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";

const props = defineProps(["modelValue", "line"]);
const emit = defineEmits(["update:modelValue"]);

let shouldRefresh = false;

const store = usePurchaseOrderDetailPurchaseOrderLineStore();
const { refreshLink } = usePurchaseOrderLink();
const { withUnitFormatter } = useTableCellFormatter();

const linkVisible = ref(false);
const loading = ref(false);
const salesOrderLines = ref<Array<ISalesOrderLine>>([]);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});
const line = computed<IPurchaseOrderLine>(() => props.line);
const handleCancelLink = useLoadingFn(cancelLink, loading);
const columns: Array<TableColumns> = [
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    fixed: "left",
    minWidth: TableWidth.order
  },
  {
    label: "物资种类",
    prop: "subClassCode",
    width: TableWidth.type
  },
  {
    label: "物料编码",
    prop: "materialCode",
    width: TableWidth.order
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.name
  },
  {
    label: "物料数量",
    prop: "materialNumber",
    align: "right",
    minWidth: TableWidth.number,
    formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
  },
  {
    label: "电压等级",
    prop: "voltageLevel"
  },
  {
    label: "操作",
    width: TableWidth.simpleOperation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "取消关联",
          action: () => cancelLinkConfirm(data.row),
          props: { type: "danger" },
          permissionKey: PermissionKey.form.formPurchaseSalesEdit,
          trackKey: TrackPointKey.FORM_PURCHASE_LINE_SALES_REF_CANCEL
        }
      ])
  }
];

function dialogOpen() {
  shouldRefresh = false;
  refresh();
}

function dialogClose() {
  if (shouldRefresh) {
    refreshLink();
  }
}

async function refresh() {
  salesOrderLines.value = await store.queryLinesByPurchaseOrderLine(line.value.id);
}

async function cancelLinkConfirm(salesOrderLine: ISalesOrderLine) {
  if (!(await useConfirm("确认取消关联"))) {
    return;
  }
  await handleCancelLink(salesOrderLine);
}

async function cancelLink(salesOrderLine: ISalesOrderLine) {
  await store.cancelSalesOrderLineLink(line.value.id, salesOrderLine.id);
  await refresh();
  ElMessage.success("取消关联成功");
  shouldRefresh = true;
}

function afterLink() {
  refresh();
  shouldRefresh = true;
}
</script>

<style scoped lang="scss">
:deep(.el-descriptions) {
  .el-descriptions__label:not(.is-bordered-label) {
    color: var(--el-text-color-secondary);
  }

  .el-descriptions__content:not(.is-bordered-label) {
    color: var(--el-text-color-primary);
  }
}
</style>
