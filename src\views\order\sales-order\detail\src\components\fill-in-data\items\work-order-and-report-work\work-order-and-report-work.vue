<template>
  <el-collapse-item :name="name">
    <template #title>
      <CollapseItemTitle
        class="order-report-work"
        :title="title"
        :name="name"
        v-track="TrackPointKey.FORM_PURCHASE_PD_UPDOWN_2"
      >
        <el-tooltip effect="dark" content="缺少报工信息" placement="top" v-if="showMissingTip">
          <el-icon size="18" class="status-icon">
            <WarningFilled />
          </el-icon>
        </el-tooltip>

        <template #actions>
          <el-button
            v-auth="permissionKey"
            v-track="trackPointKey"
            class="mr-5"
            :icon="Plus"
            @click.stop="openCreateDialog"
            type="primary"
            >{{ actionTitle }}</el-button
          >
        </template>
      </CollapseItemTitle>
    </template>
    <div class="m-5 flex-1">
      <WorkOrderTable v-if="purchaseStore.isCable" />
      <ReportWorkTable v-else />
    </div>

    <el-dialog
      :title="reportWorkStore.isAddReportWork ? '新增报工' : '编辑报工'"
      align-center
      class="default"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-model="reportWorkStore.reportWorkFormModalVis"
      @close="onCancelCreateReportWork"
    >
      <ReportWorkForm
        ref="reportWorkFormRef"
        :subclassCode="reportWorkStore.reportWorkFormProps.subclassCode"
        :processIds="reportWorkStore.reportWorkFormProps.processIds"
        :minClassCode="reportWorkStore.reportWorkFormProps.minClassCode"
      />
      <template #footer>
        <el-button @click="onCancelCreateReportWork()">取消</el-button>
        <el-button type="primary" :loading="reportWorkSaveLoading" @click="handleOnSaveReportWork()">保存</el-button>
      </template>
    </el-dialog>
  </el-collapse-item>
</template>

<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import {
  useReportWorkStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore,
  useWorkOrderStore
} from "@/store/modules";
import { computed, ref, watch } from "vue";
import ReportWorkForm from "@/views/order/sales-order/detail/src/components/fill-in-data/report-work/report-work-form/index.vue";
import ReportWorkTable from "@/views/order/sales-order/detail/src/components/fill-in-data/report-work/index.vue";
import WorkOrderTable from "@/views/order/sales-order/detail/src/components/fill-in-data/work-order/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ICreateReportWork, IProductOrder, IReportWork, IWorkOrder } from "@/models";
import { ElMessage } from "element-plus";
import { PermissionKey, TrackPointKey } from "@/consts";
import { WarningFilled } from "@element-plus/icons-vue";
import { useDataMissingCheck } from "@/views/order/sales-order/detail/src/components/fill-in-data/items/work-order-and-report-work/useDataMissingCheck";
import { FillInDataOfCollapseNameEnum } from "../../fill-in-data/types";

const name = FillInDataOfCollapseNameEnum.WORK_ORDER_LIST;

const purchaseStore = useSalesOrderDetailStore();
const workOrderStore = useWorkOrderStore();
const reportWorkStore = useReportWorkStore();
const fillInDataStore = useSalesFillInDataStore();

const reportWorkFormRef = ref<InstanceType<typeof ReportWorkForm>>();
const reportWorkSaveLoading = ref<boolean>(false);
const showMissingTip = ref<boolean>(false);
const handleOnSaveReportWork = useLoadingFn(saveReportWork, reportWorkSaveLoading);
const { checkCableReportWorkMissingStatus, checkNonCableDataMissing } = useDataMissingCheck();

const title = computed(() => (purchaseStore.isCable ? "工单与报工" : "报工"));
const actionTitle = computed(() => (purchaseStore.isCable ? "新增工单" : "新增报工"));
const permissionKey = computed(() =>
  purchaseStore.isCable
    ? PermissionKey.form.formPurchaseWorkOrderCreate
    : PermissionKey.form.formPurchaseWorkReportCreate
);
// Todo: Add work report `trackPointKey`.
const trackPointKey = computed(() => (purchaseStore.isCable ? TrackPointKey.FORM_PURCHASE_PWO_CREATE : ""));

// 订阅工单列表数据，工单列表发生变化时，检查报工完整性
watch(
  () => workOrderStore.workOrders,
  async () => {
    showMissingTip.value = purchaseStore.isCable
      ? await checkCableReportWorkMissingStatus()
      : checkNonCableDataMissing();
  },
  {
    immediate: true
  }
);

function openCreateDialog() {
  purchaseStore.isCable ? openCreateWorkOrderDialog() : openCreateReportWorkDialog();
}

function openCreateWorkOrderDialog() {
  workOrderStore.setWorkOrderFormModalVis(true, true);
  const {
    amount,
    unit,
    ipoStatus,
    voltageClasses,
    specificationModel,
    planStartDate,
    planFinishDate,
    actualStartDate,
    actualFinishDate,
    materialsCode,
    materialsName,
    materialsDesc,
    materialsUnit,
    ipoNo,
    salesLineId,
    id,
    purchaseId,
    subClassCode
  } = fillInDataStore.data as IProductOrder;
  workOrderStore.setCreateWorkOrderDetail({
    amount,
    unit,
    ipoNo,
    purchaseId,
    subclassCode: subClassCode,
    productionId: id,
    salesLineId,
    woStatus: ipoStatus,
    voltageLevel: voltageClasses,
    specificationType: specificationModel,
    planStartDate,
    planFinishDate,
    actualStartDate,
    actualFinishDate,
    materialsCode,
    materialName: materialsName,
    materialDesc: materialsDesc,
    materialUnit: materialsUnit
  });
}

async function openCreateReportWorkDialog() {
  const { subclassCode, processIds, id: workOrderId, minClassCode } = fillInDataStore.data as IWorkOrder;
  reportWorkStore.setReportWorkProps(subclassCode, processIds, minClassCode);
  reportWorkStore.setIsAddReportWork(true);
  reportWorkStore.clearReportWorkDetailAndShowReportWorkFormModal();
  const reportWork: IReportWork = await reportWorkStore.getLatestReportWorkById(workOrderId);
  if (reportWork && Object.keys(reportWork).length) {
    reportWorkStore.setReportWorkDetail({ buyerProvince: reportWork.buyerProvince });
  }
}

function onCancelCreateReportWork() {
  reportWorkStore.setReportWorkFormModalVis(false);
  reportWorkStore.setReportWorkDetail();
}

async function saveReportWork() {
  const formValue: ICreateReportWork | boolean = await reportWorkFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  reportWorkStore.isAddReportWork
    ? await reportWorkStore.createReportWork({ ...formValue, ...getCreateReportWorkAppendInfo() })
    : await reportWorkStore.editorReportWork(formValue);
  reportWorkStore.setReportWorkFormModalVis(false);
  ElMessage.success(reportWorkStore.isAddReportWork ? "新增成功" : "编辑成功");

  if (reportWorkStore.isAddReportWork) {
    reportWorkStore.setFreshReportWorkTag(true);
  } else {
    reportWorkStore.refreshReportWorks(reportWorkStore.reportWorkParams.pageNo);
  }

  if (purchaseStore.isCable) {
    workOrderStore.refreshWorkOrders(workOrderStore.workOrderParams?.pageNo);
  }
}

function getCreateReportWorkAppendInfo() {
  const workOrder = purchaseStore.isCable ? workOrderStore.workOrderDetail : (fillInDataStore.data as IWorkOrder);
  return {
    workId: workOrder.id,
    purchaseId: workOrder.purchaseId
  };
}
</script>

<style scoped lang="scss">
:deep(.order-report-work) {
  .mx-4 {
    margin-right: 0.3rem;
  }
}

:deep(.status-icon) {
  color: var(--el-color-warning);
}
</style>
