import { EditQualitySpecificationParticularDetailParams, JudgeStandard } from "@/models/quality-tracing";
import { InputFormualModelVal } from "@/components/input-formual/types";

/**
 * @description: 格式化判定标准列表为请求参数列表
 */
export function judgementStandardListToDTO(
  list: Array<{
    /** 判断公式 */
    range: InputFormualModelVal;
    /** 分值 */
    score: string;
  }>
): Array<JudgeStandard> {
  return list.map(item => {
    const { leftValue, leftSymbol, rightSymbol, rightValue } = item.range;
    return {
      minValue: leftValue ? Number(leftValue) : null,
      minOp: leftValue ? leftSymbol : null,
      maxValue: rightValue ? Number(rightValue) : null,
      maxOp: rightValue ? rightSymbol : null,
      score: Number(item.score)
    };
  });
}

/**
 * @description: 响应数据转为判定标准列表
 */
export function rawDataToJudgementStandardList(value: EditQualitySpecificationParticularDetailParams) {
  const { judgeStandard: list, judgeType } = value;
  return list.map(item => {
    const { minValue, minOp, maxValue, maxOp, score } = item;
    return {
      range: {
        leftValue: typeof minValue === "number" ? String(minValue) : "",
        leftSymbol: minOp,
        midValue: getMidValue(judgeType),
        rightValue: typeof maxValue === "number" ? String(maxValue) : "",
        rightSymbol: maxOp
      },
      score: String(score)
    };
  });
}

/**
 * @description: 格式化数值达标范围为请求参数
 */
export function reachStandardFormValueToDTO(value?: InputFormualModelVal) {
  if (value) {
    return {
      standardMin: value.leftValue ? Number(value.leftValue) : null,
      minOp: value.leftValue ? value.leftSymbol : null,
      standardMax: value.rightValue ? Number(value.rightValue) : null,
      maxOp: value.rightValue ? value.rightSymbol : null
    };
  }
  return {
    standardMin: null,
    minOp: null,
    standardMax: null,
    maxOp: null
  };
}

/**
 * @description: 响应数据数据转为达标范围表单value
 */
export function rawDatoToReachStandardFormValue(value: EditQualitySpecificationParticularDetailParams) {
  return {
    leftValue: typeof value.standardMin === "number" ? String(value.standardMin) : "",
    leftSymbol: value.minOp,
    rightValue: typeof value.standardMax === "number" ? String(value.standardMax) : "",
    rightSymbol: value.maxOp
  };
}

/**
 * @description: 格式化基础信息表单值为请求参数
 */
export function baseInfoFormValueToDTO(value: {
  processId: string;
  modelId: string;
  judgeType: number;
  score: string;
}) {
  return {
    ...value,
    score: Number(value.score)
  };
}

/**
 * @description: 响应数据数据转为基础表单value
 */
export function rawDatoToBaseInfoFormValue(value: EditQualitySpecificationParticularDetailParams) {
  return {
    ...value,
    score: value.score + ""
  };
}

function getMidValue(judgeType: number) {
  switch (judgeType) {
    case 0:
      return "x";
    case 1:
      return "D(x)";
    case 2:
      return "q";
    default:
      return "";
  }
}
