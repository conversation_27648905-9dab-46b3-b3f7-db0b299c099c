@mixin card-label-value {
  .el-row + .el-row {
    @apply mt-3;
  }
  .el-col {
    @apply flex flex-col;
    .label {
      @apply text-sm text-regular;
    }
    .value {
      @apply text-base;
    }
  }
}

@mixin state-grid-item-description {
  .descriptions {
    display: flex;
    gap: 2rem;
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-primary);
    @apply md:gap-[3rem] lg:gap-[4rem];

    .description-item {
      @apply flex items-center;

      .label {
        @apply truncate;
        color: var(--el-text-color-secondary);
        padding-right: 12px;
      }
      .content {
        @apply truncate flex items-center;
        color: var(--el-text-color-primary);
      }
    }
  }
}

@mixin full-screen-dialog {
  background-color: var(--fullscreen-bg);
  display: flex;
  flex-direction: column;

  & > .el-dialog__header {
    margin: 0;
    background-color: var(--el-bg-color);
  }
  & > .el-dialog__body {
    flex: 1;
    overflow: hidden;
  }
  & > .el-dialog__footer {
    @apply bg-bg_color pt-2.5;
  }
}
