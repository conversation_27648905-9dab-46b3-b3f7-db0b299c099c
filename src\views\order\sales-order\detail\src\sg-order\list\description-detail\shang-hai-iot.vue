<template>
  <div class="title font-medium">
    <span class="label">销售订单行项目号</span>
    <span class="content">{{ sync.soItemNo }}</span>
  </div>
  <div class="descriptions">
    <el-row>
      <el-col :span="6">
        <div class="description-item">
          <div class="label">销售订单号</div>
          <div class="content">{{ sync.soNo }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="description-item">
          <div class="label">合同编号</div>
          <div class="content">{{ sync.conCode }}</div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="description-item">
          <div class="label">采购方公司名称</div>
          <div class="content">
            <ShowTooltip class-name="max-w-[15em] lg:max-w-[20em] xl:max-w-[30em]" :content="sync.buyerName" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { IIOTStateGridOrderSync } from "@/models/state-grid-sync";
import { computed } from "vue";

const props = defineProps<{
  sync: IIOTStateGridOrderSync;
}>();

const sync = computed(() => props.sync);
</script>
