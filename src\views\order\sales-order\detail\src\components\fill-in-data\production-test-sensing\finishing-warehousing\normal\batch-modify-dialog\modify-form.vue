<template>
  <el-form ref="formRef" :model="form" :rules="rules" :validate-on-rule-change="false" label-position="top">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="检验结果" prop="isQualified">
          <el-radio-group v-model="form.isQualified">
            <el-radio :label="QualifiedEnum.QUALIFIED">合格</el-radio>
            <el-radio :label="QualifiedEnum.NOT_QUALIFIED">不合格</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="入库日期" prop="storageTime">
          <el-date-picker
            v-model="form.storageTime"
            class="!w-full"
            type="date"
            clearable
            placeholder="请输入入库日期"
            @change="handleDateChange($event, 'storageTime')"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="发货状态" prop="shipmentStatus">
          <el-radio-group v-model="form.shipmentStatus">
            <el-radio :label="ShipmentStatusEnum.NOT_SHIPMENT">未发货</el-radio>
            <el-radio :label="ShipmentStatusEnum.SHIPMENT">已发货</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发货日期" prop="shipmentTime">
          <el-date-picker
            v-model="form.shipmentTime"
            class="!w-full"
            type="date"
            clearable
            placeholder="请输入发货日期"
            @change="handleDateChange($event, 'shipmentTime')"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { QualifiedEnum, ShipmentStatusEnum } from "@/enums";

interface Form {
  isQualified: string;
  storageTime: string;
  shipmentStatus: string;
  shipmentTime: string;
}

const form = reactive<any>({
  isQualified: "",
  storageTime: "",
  shipmentStatus: "",
  shipmentTime: ""
});
const formRef = ref<FormInstance>();

const rules = computed<FormRules>(() => {
  if (form.shipmentStatus === ShipmentStatusEnum.SHIPMENT) {
    return {
      isQualified: [{ required: true, message: "请输入检验结果", trigger: "change" }],
      storageTime: [{ required: true, message: "请输入入库日期", trigger: "change" }],
      shipmentStatus: [{ required: true, message: "请输入发货状态", trigger: "change" }],
      shipmentTime: [{ required: true, message: "请输入发货日期", trigger: "change" }]
    };
  }
  return {
    isQualified: [{ required: true, message: "请输入检验结果", trigger: "change" }],
    storageTime: [{ required: true, message: "请输入入库日期", trigger: "change" }],
    shipmentStatus: [{ required: true, message: "请输入发货状态", trigger: "change" }]
  };
});

function handleDateChange(value: string, key: string) {
  if (value) {
    const date = new Date(value);
    form[key] = date.toISOString();
  } else {
    form[key] = null;
  }
  console.log(form);
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: Partial<Form>) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style lang="scss" scoped>
:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
