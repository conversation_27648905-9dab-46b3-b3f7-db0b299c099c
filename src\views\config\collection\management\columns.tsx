import { TableWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "信息类别(工序)",
      prop: "name",
      width: TableWidth.largeOrder
    },
    {
      label: "信息类别(工序)编码",
      prop: "code",
      width: TableWidth.order
    },
    {
      label: "编号",
      type: "index",
      width: TableWidth.index
    },
    {
      label: "数据项",
      prop: "targetName",
      width: TableWidth.name
    },
    {
      label: "数据项编码",
      prop: "targetCode",
      width: TableWidth.order
    },
    {
      label: "数据项编码2.0",
      prop: "targetCodeV2",
      width: TableWidth.order
    },
    {
      label: "是否必传",
      prop: "requiredDesc",
      width: TableWidth.unit
    },
    {
      label: "单位",
      prop: "unit",
      width: TableWidth.unit
    },
    {
      label: "数据格式要求",
      prop: "datumOrganization",
      width: TableWidth.name
    },
    {
      label: "控件类型",
      prop: "collectionTypeName",
      width: TableWidth.type,
      formatter: row => {
        return row?.dataTypeIdentityDetail?.identityName || "--";
      }
    },
    {
      label: "备注",
      prop: "remarks",
      width: TableWidth.largeName
    },
    {
      label: "数据采集方式",
      prop: "collectionTypeName",
      width: TableWidth.type,
      fixed: "right"
    },
    {
      label: "操作",
      prop: "op",
      fixed: "right",
      width: TableWidth.operations,
      slot: "operates"
    }
  ];
  return {
    columns
  };
}
