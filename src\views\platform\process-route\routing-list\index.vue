<template>
  <div class="flex flex-col h-full overflow-hidden raw-material-list">
    <div class="justify-between mb-3 search flex-c">
      <TitleBar title="工艺路线" />
      <el-button
        v-auth="PermissionKey.meta.metaRawMaterialCreate"
        class="square-btn"
        @click="onAddRoutingForm"
        :icon="Plus"
      />
    </div>
    <el-scrollbar>
      <div class="flex-1 raw-material-type-list" v-loading="processRouteStore.loading">
        <div class="list" v-if="processRouteStore.processRouteList?.length">
          <template v-for="item in processRouteStore.processRouteList" :key="item.id">
            <CardList
              class="p-3 mb-2 border card"
              :class="{ active: item.id === selProcessRouteId }"
              :processRouteInfo="item"
              @click="routingDetail(item)"
              @onEditProcessRoute="onEditProcessRoute"
              @onDelProcessRoute="onDelProcessRoute"
            />
          </template>
        </div>
        <div class="list" v-else>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </div>
      </div>
    </el-scrollbar>
    <el-dialog
      :title="state.dialogTitle"
      align-center
      class="small"
      destroy-on-close
      v-model="state.routingFormVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="onCloseRoutingForm"
    >
      <RoutingForm ref="routingFormRef" />
      <template #footer>
        <el-button @click="onCloseRoutingForm()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveRoutingForm()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import CardList from "./routing-list.vue";
import { Plus } from "@element-plus/icons-vue";
import TitleBar from "@/components/TitleBar";
import { ref, reactive, watchEffect } from "vue";
import { PermissionKey } from "@/consts";
import RoutingForm from "./routing-form.vue";
import { useProcessRouteStore } from "@/store/modules";
import { IProcessRouteForm, IProcessRouteReq, IProcessRoute } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";

const processRouteStore = useProcessRouteStore();

const props = defineProps<{
  currentCategoryCode?: string;
  currentCategoryName?: string;
}>();

const state = reactive<{
  routingFormVis: boolean;
  dialogTitle: string;
}>({
  routingFormVis: false,
  dialogTitle: undefined
});

const selProcessRouteId = ref();
const saveLoading = ref<boolean>(false);
const routingFormRef = ref<InstanceType<typeof RoutingForm>>();
const onSaveRoutingForm = useLoadingFn(saveRoutingForm, saveLoading);

watchEffect(async () => {
  const { currentCategoryCode } = props;
  if (currentCategoryCode) {
    selProcessRouteId.value = undefined;
    queryProcessRoute();
  }
});

const onAddRoutingForm = () => {
  state.dialogTitle = "新增工艺路线";
  state.routingFormVis = true;
  processRouteStore.emptyForm();
};

const onCloseRoutingForm = () => {
  state.routingFormVis = false;
};

const onEditProcessRoute = (id: string) => {
  state.dialogTitle = "编辑工艺路线";
  processRouteStore.queryProcessRouteDeteil(id);
  state.routingFormVis = true;
};

const onDelProcessRoute = async (id: string) => {
  if (!(await useConfirm("确认删除当前工艺路线", "确认删除"))) {
    return;
  }
  await processRouteStore.deleteProcessRoute(id);
  if (selProcessRouteId.value == id) {
    selProcessRouteId.value = undefined;
  }
  ElMessage.success("删除成功");
  queryProcessRoute();
};

async function saveRoutingForm() {
  const formValue: IProcessRouteForm | boolean = await routingFormRef.value.getFormValue();

  if (typeof formValue === "boolean") {
    return;
  }

  const _formValue: IProcessRouteReq = {
    ...formValue,
    categoryCode: props.currentCategoryCode,
    categoryName: props.currentCategoryName
  };
  if (!formValue.id) {
    await processRouteStore.createProcessRoute(_formValue);
  } else {
    await processRouteStore.editProcessRoute(_formValue);
  }
  state.routingFormVis = false;
  ElMessage.success(formValue.id ? "修改成功" : "新增成功");
  queryProcessRoute();
}

/** 工艺路线详情 */
function routingDetail(item: IProcessRoute) {
  selProcessRouteId.value = item.id;
  processRouteStore.currentProcessRoute(item);
}

async function queryProcessRoute() {
  await processRouteStore.queryProcessRoute(props.currentCategoryCode);
  if (processRouteStore.processRouteList && processRouteStore.processRouteList.length > 0) {
    if (!selProcessRouteId.value) {
      const item = processRouteStore.processRouteList[0];
      selProcessRouteId.value = item.id;
      processRouteStore.currentProcessRoute(item);
    }
  } else {
    processRouteStore.currentProcessRoute();
  }
}
</script>

<style scoped lang="scss">
.search {
  .input-group {
    :deep(.el-input-group__append) {
      width: 40px;
    }

    :deep(.el-button) {
      padding: 8px;
    }
  }

  .square-btn {
    padding: 5px 8px;
  }
}

.raw-material-type-list {
  .list {
    .card {
      box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      border-color: rgba(0, 0, 0, 0.1);

      &.active {
        color: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }

      &:hover {
        cursor: pointer;
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
}

.raw-material-list {
  .list-footer {
    :deep(.el-pagination .btn-prev) {
      margin-left: 0;
    }
  }
}
</style>
