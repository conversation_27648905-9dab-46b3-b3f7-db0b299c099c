import { IRawMaterialExperimentValue } from "@/models/raw-material/i-raw-material-res";
import { IOutGoingFactoryQmxExperiment } from "./i-out-going-factory-qmx-experiment";

export interface IOutGoingFactoryQMXExperimentForm extends Omit<IOutGoingFactoryQmxExperiment, "rawMetadataValue"> {
  /** 生产订单ID */
  productionId: string;
  /** 采购订单ID */
  purchaseId?: string;
  /** 工序ID */
  processId: string;
  /** 工序Code */
  processCode: string;

  /** 工序名称 */
  processName: string;

  /** 原材料检信息 */
  rawMetadataValue?: Array<IRawMaterialExperimentValue>;
  workOrderId?: string;

  /** 盘号 */
  reelNo?: string;
  /** 线芯名称 */
  wireName?: string;
}

export interface IArmourClampExperimentForm {
  id?: string;
  /** 试验编号 */
  experimentNo: string;
  /** 产品名称 */
  productName?: string;
  /** 产品型号 */
  productModel?: string;
  /** 生产批次号 */
  productBatchNo?: string;
  /** 抽检批次号 */
  inspectBatchNo?: string;
  /** 样品编号 */
  sampleNo: string;
  /** 检验日期 */
  inspectDateTime?: string;
  /** 检测人员 */
  inspectOperate?: string;
  /** 审核人员 */
  auditor?: string;
  /** 备注 */
  remark?: string;

  /** 原材料检信息 */
  rawMetadataValue?: Array<IRawMaterialExperimentValue>;

  workOrderId?: string;
}
