<template>
  <div class="w-full h-full overflow-hidden flex">
    <Content class="flex-1" />
    <NavigationBar class="ml-5" />
  </div>
</template>

<script setup lang="ts">
import { onUnmounted, provide, reactive, watch, watchEffect } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../state-grid-order-sync/tokens";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { IStateGridSyncAuditBaseInfo } from "@/models";
import { AUTO_COLLECTION_IN_PROCESS, BASE_INFO_ID } from "./consts";
import Content from "./content.vue";
import NavigationBar from "./navigation-bar.vue";
import { ISyncAuditState, syncAuditAnchorKey, syncAuditStateKey } from "./tokens";
import { useAuditItem } from "./hooks/audit-item";

const props = defineProps<{
  baseInfo: IStateGridSyncAuditBaseInfo;
  businessAuditItems?: Array<string>;
  iotAuditItems?: Array<string>;
  allowFilterByOrderNo?: boolean;
  subClassCode?: string;
}>();

const store = useStateGridSyncAuditStore();
const { getTitleById, generateItem } = useAuditItem();

const syncAuditState = reactive<ISyncAuditState>({
  hasBusinessItem: false,
  hasIotItem: false,
  navigationItems: [],
  businessItems: [],
  iotItems: [],
  allowFilterByOrderNo: false,
  subClassCode: props.subClassCode
});

provide(syncAuditStateKey, syncAuditState);
provide(syncAuditAnchorKey, reactive({ id: BASE_INFO_ID }));
provide(PROVIDE_PROCESS_INSPECT_TOKEN, { isArmourClamp: store.isArmourClamp });

watchEffect(() => store.setBaseInfo(props.baseInfo));

// region syncAuditState watch
watchEffect(() => (syncAuditState.allowFilterByOrderNo = props.allowFilterByOrderNo ?? false));
watchEffect(() => {
  syncAuditState.hasBusinessItem = false;
  props.businessAuditItems?.forEach(id => {
    syncAuditState.hasBusinessItem = true;
    const item = generateItem(id);
    syncAuditState.businessItems.push(item);
  });
});
watch(
  () => store.hasCollectData,
  () => {
    genIotComponentList();
    genNavigationList();
  }
);
// endregion

/**
 * @description: 生成物联数据组件列表
 */
function genIotComponentList() {
  if (typeof store.hasCollectData !== "boolean") {
    return;
  }
  syncAuditState.hasIotItem = false;
  props.iotAuditItems?.forEach(id => {
    syncAuditState.hasIotItem = true;
    const item = generateItem(id);
    if (id === AUTO_COLLECTION_IN_PROCESS) {
      if (store.hasCollectData) {
        syncAuditState.iotItems.push(item);
      }
    } else {
      syncAuditState.iotItems.push(item);
    }
  });
}

/**
 * @description: 生成右侧导航列表
 */
function genNavigationList() {
  syncAuditState.navigationItems = [{ id: BASE_INFO_ID, title: getTitleById(BASE_INFO_ID) }];
  const businessAuditItems = props.businessAuditItems;
  const iotAuditItems = props.iotAuditItems;
  businessAuditItems?.forEach(id => syncAuditState.navigationItems.push({ id, title: getTitleById(id) }));
  iotAuditItems?.forEach(id => {
    if (id === AUTO_COLLECTION_IN_PROCESS) {
      if (store.hasCollectData) {
        syncAuditState.navigationItems.push({ id, title: getTitleById(id) });
      }
    } else {
      syncAuditState.navigationItems.push({ id, title: getTitleById(id) });
    }
  });
}

onUnmounted(() => store.$reset());

defineExpose({
  isEmpty: () => store.empty
});
</script>

<style scoped></style>
