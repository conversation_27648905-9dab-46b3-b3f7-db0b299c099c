import {
  IListResponse,
  IMaterial,
  IMaterialQueryParams,
  IPurchaseOderLine,
  IResponse,
  ISaleLineLinkPurchaseLineParams,
  ISalesOrder,
  ISalesOrderLineDetail,
  ISalesOrderLineLinkPurchase,
  ISalesOrderLineParams
} from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useSalesOrderLineManagementStore = defineStore({
  id: "cx-sales-order-line-management-store",
  state: () => ({
    total: 0,
    materialTotal: 0,
    salesOrderLineFaultCount: 0,
    saleOrderLineDetail: {} as ISalesOrderLineDetail,
    salesOrderDetail: {} as ISalesOrder,
    saleLineLinkPurchaseOrderLineTotal: 0
  }),
  actions: {
    /**
     * 根据ID查询销售订单详情数据
     * @deprecated 根据ID查询销售订单详情数据 这个方法将会被启用，请使用销售订单stor 【sales-order】
     */
    async getSalesOrderDetailById(id: string) {
      const res: IResponse<ISalesOrder> = await api.getSalesOrderDetailById(id);
      this.salesOrderDetail = res.data;
      return res.data;
    },

    // 根据 销售订单Id 采购订单行是否缺失 获取销售订单行的列表数据
    async querySalesOrderLine(params: ISalesOrderLineParams) {
      const res: IListResponse<ISalesOrderLineLinkPurchase> = await api.querySalesOrderLine(params);
      this.salesOrderLineFaultCount = await this.querySalesOrderLineFaultCount(params.salesId);
      this.total = res.data.total;
      return res.data.list;
    },

    async querySalesOrderLineFaultCount(id: string) {
      return (await api.querySalesOrderLineFaultCount(id)).data;
    },
    // 查询销售订单详情销售订单行
    async querySaleLineLinkPurchaseOrderLine(saleId: string, params: ISaleLineLinkPurchaseLineParams) {
      const res: IListResponse<IPurchaseOderLine> = await api.querySaleLineLinkPurchaseOrderLine(saleId, params);
      this.saleLineLinkPurchaseOrderLineTotal = res.data.total || 0;
      return res.data.list;
    },
    // 创建销售订单行信息
    async createSalesOrderLineDetail(salesOrderLineDetail: ISalesOrderLineDetail) {
      const res: IResponse<string> = await api.createSalesOrderLineDetail(salesOrderLineDetail);
      return res.data;
    },

    // 查询销售订单行详情数据
    async getSalesOrderLineDetailById(id: string) {
      const res: IResponse<ISalesOrderLineDetail> = await api.getSalesOrderLineDetailById(id);
      return res.data;
    },

    // 编辑销售订单行信息
    async editSalesOrderLineDetail(salesOrderLineDetail: ISalesOrderLineDetail) {
      const res: IResponse<string> = await api.editSalesOrderLineDetail(salesOrderLineDetail);
      return res.data;
    },

    // 删除销售订单行信息
    async deleteSaleOrderLineById(id: string) {
      await api.deleteSaleOrderLineById(id);
    },
    // 销售订单行详情赋值
    setSalesOrderLineDetailStorage(saleOrderLineDetail: ISalesOrderLineDetail) {
      this.saleOrderLineDetail = saleOrderLineDetail;
    },
    // 销售订单行详情清空
    clearSalesOrderLineDetailStorage() {
      this.saleOrderLineDetail = {};
    },
    async queryMaterialTableData(params: IMaterialQueryParams) {
      const res: IListResponse<IMaterial> = await api.queryMaterials(params);
      this.materialTotal = res.data.total;
      return res.data.list;
    }
  }
});
