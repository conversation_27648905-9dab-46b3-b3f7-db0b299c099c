<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :validate-on-rule-change="false"
    class="cx-form"
    label-width="7rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="试验参数标准" prop="name">
          <el-input v-model="formData.name" placeholder="请输入试验参数标准" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="物资种类" prop="equipmentCode">
          <el-select
            v-model="formData.equipmentCode"
            class="w-full"
            placeholder="请选择物资种类"
            :disabled="editMode"
            clearable
            filterable
          >
            <el-option v-for="item in equipmentAll" :key="item.code" :label="item.name" :value="Number(item.code)" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="设备名称" prop="speciType">
          <el-select
            v-model="formData.speciType"
            class="w-full"
            placeholder="请选择设备名称"
            :disabled="editMode"
            clearable
            filterable
          >
            <el-option v-for="(val, key) in speciList" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="formData.remarks"
            placeholder="请输入备注"
            type="textarea"
            :rows="3"
            :maxlength="100"
            :show-word-limit="true"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { SpeciTypeEnum } from "@/enums";
import { EParamsStandard, EquipmentModel } from "@/models";

const props = withDefaults(
  defineProps<{
    detail: EParamsStandard; // 表格表单数据
    equipmentList: Array<EquipmentModel>; // 物资种类
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as EParamsStandard;
    },
    equipmentList: () => [],
    isEdit: false
  }
);
const formData = reactive({} as EParamsStandard);
const equipmentAll = ref<EquipmentModel[]>([]);
const editMode = ref(false);
watchEffect(() => {
  Object.assign(formData, props.detail);
  equipmentAll.value = props.equipmentList;
  editMode.value = props.isEdit;
});
const speciList = SpeciTypeEnum;

const rules: FormRules = {
  name: [{ required: true, message: "试验参数标准不能为空", trigger: "change" }],
  equipmentCode: [{ required: true, message: "物资种类不能为空", trigger: "change" }],
  speciType: [{ required: true, message: "设备名称不能为空", trigger: "change" }]
};

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as EParamsStandard);
}

defineExpose({
  validateForm,
  getFormValue
});
</script>

<style scoped></style>
