export interface IPurchaseOrderProcess {
  /** 采购订单行 总行数 **/
  purchaseLineCount: number;
  /** 已关联采购订单行 总行数 **/
  purchaseLineLinkedCount: number;
  /** 已同步采购订单行 总行数 **/
  purchaseLineSyncCount: number;
  /** 已评分采购订单行 总行数 **/
  purchaseLineGradeCount: number;
  /** 销售订单行 总行数 **/
  salesLineCount: number;
  /** 已排产的销售订单行 总行数 **/
  salesLinePlanCount: number;
  /** 已生产订单行 总行数 **/
  salesLineProductCount: number;
  /** 有工单的生产订单数 */
  workOrderHasProductionCount: number;
  /** 生产订单总数 */
  productionCount: number;
}

export interface ISalesOrderProcess {
  /** 销售订单行 总行数 **/
  salesLineCount: number;
  /** 已关联销售订单行 总行数 **/
  salesLineLinkedCount: number;
  /** 已排产的销售订单行 总行数 **/
  salesLinePlanCount: number;
  /** 已生产订单行 总行数 **/
  salesLineProductCount: number;
  /** 已同步销售订单行 总行数 **/
  salesLineSyncCount: number;
  /** 已评分销售订单行 总行数 **/
  salesLineGradeCount: number;
  /** 已有工单的订单数 **/
  workOrderHasProductionCount: number;
  /** 生产订单 总行数 **/
  productionCount: number;
}
