<template>
  <el-dialog
    v-model="editVisible"
    title="编辑排产计划"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <SchedulingPlanForm ref="editFormRef" v-loading="loading" />
    <template #footer>
      <span>
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
          >保存，并重新同步</el-button
        >
        <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SchedulingPlanForm from "@/views/order/purchase-order/detail/src/components/scheduling-plan/add-scheduling-plan/scheduling-plan-form/index.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ICreateSchedulingPlan, IProductionPlanSync, IResponse, ISchedulingPlanDetail } from "@/models";
import { useSchedulingPlanStore } from "@/store/modules";

const schedulingPlanStore = useSchedulingPlanStore();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof SchedulingPlanForm>>(updateSchedulingPlan);
const { loading, handlePatchValue } = usePatchValue(patchValue);

function openEditDialog(data: IProductionPlanSync) {
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.ppNo
  };
  handlePatchValue(data.dataId);
}

async function patchValue(dataId: string) {
  const res: IResponse<ISchedulingPlanDetail> = await schedulingPlanStore.getSchedulingPlanDetailById(dataId);
  schedulingPlanStore.setSchedulingPlanFormValue(res.data);
}

async function updateSchedulingPlan() {
  const formValue: ICreateSchedulingPlan | boolean = await editFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  await schedulingPlanStore.editProductionPlan(formValue);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
