import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import { isNil } from "lodash-unified";
import { emptyDefaultValue } from "@/consts";
import { TableColumnCtx } from "element-plus";

/**
 * @description  将持续时长转化为日时分秒
 * @param duration 持续时长
 * @returns 日时分秒
 */

export function convertTimeFormatter(row: any, column: TableColumnCtx<any>, cellValue: string | number) {
  return convertTime(cellValue);
}

export function convertTime(duration: string | number) {
  if (isNil(duration)) {
    return emptyDefaultValue;
  }
  const date = Number(duration);
  if (isNaN(date)) {
    return emptyDefaultValue;
  }
  return formatOfDayjs(date, "DD日HH时mm分ss秒");
}
function formatOfDayjs(durationTime: number, format: string) {
  // 扩展插件
  dayjs.extend(duration);
  return dayjs.duration(durationTime, "second").format(format);
}
