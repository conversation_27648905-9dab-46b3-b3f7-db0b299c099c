<template>
  <div>
    <form-title-bar title="基础信息" />
    <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="80px">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="检测类别" prop="processId">
            <inspection-type-selector :production-stage-id="productionStageId" v-model="form.processId" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测项" prop="modelId">
            <inspection-item-selector
              :category-code="categoryCode"
              :process-id="form.processId"
              :quality-id="qualityId"
              v-model="form.modelId"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="判定方式" prop="judgeType">
            <judgement-type-selector v-model="form.judgeType" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分值" prop="score">
            <el-input type="number" clearable v-model="form.score" @input="handleInput" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import FormTitleBar from "../form-title-bar.vue";
import InspectionTypeSelector from "./inspection-type-selector.vue";
import InspectionItemSelector from "./insepection-item-selector.vue";
import JudgementTypeSelector from "./jugment-type-selector.vue";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";

interface FormType {
  processId: string;
  modelId: string;
  judgeType: number;
  score: string;
}

defineProps<{
  /** 生产阶段Id */
  productionStageId: string;
  /** 质量Id */
  qualityId: string;
  /** 生产阶段分类编码 */
  categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum;
}>();

const maxLength = 20;

const form = reactive({
  processId: "",
  modelId: "",
  judgeType: undefined,
  score: ""
});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  processId: [{ required: true, message: "请选择检测类别", trigger: "change" }],
  modelId: [{ required: true, message: "请选择检测项", trigger: "change" }],
  judgeType: [{ required: true, message: "请选择判定方式", trigger: "change" }],
  score: [{ required: true, message: "请输入分值", trigger: "change" }]
};

/**
 * @description: 输入时矫正长度
 */
function handleInput(s: string) {
  if (s.length > maxLength) {
    form.score = s.slice(0, maxLength);
  }
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  form.processId = v.processId;
  form.modelId = v.modelId;
  form.judgeType = v.judgeType;
  form.score = v.score;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  resetForm: () => {
    formRef.value?.resetFields();
  }
});
</script>

<style lang="scss" scoped>
:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
