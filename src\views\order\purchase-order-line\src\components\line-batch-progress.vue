<template>
  <el-dialog
    v-model="visible"
    :title="title"
    class="default"
    align-center
    destroy-on-close
    close-on-press-escape
    @closed="closeDialog"
  >
    <el-progress :percentage="percentage" :stroke-width="15" striped striped-flow :duration="10" />
    <section class="tip-info flex items-center justify-center p-4" v-if="errorList?.length">
      <div class="tip-icon flex items-center mr-2">
        <el-icon size="large"><WarningFilled /></el-icon>
      </div>
      <div class="tip-text flex items-center">
        <span>成功{{ successCount }}条，</span>
        <span>失败{{ errorList?.length }}条；</span>
        <span>共{{ batchList?.length }}条。</span>
        <el-button type="primary" size="small" @click="openErrorDialog" link>查看失败数据</el-button>
      </div>
    </section>
  </el-dialog>
</template>

<script setup lang="ts">
import { PurchaseOrderLineDetailEnum } from "@/enums/purchase-order";
import { computed } from "vue";
import { WarningFilled } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    percentage?: number;
    type?: string;
    batchList?: Array<Record<string, string>>;
  }>(),
  {
    modelValue: false,
    percentage: 0
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "closeDialog"): void;
  (e: "openErrorDialog"): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});

const title = computed(() => {
  return props.type === PurchaseOrderLineDetailEnum.DATA_SYNC ? "批量同步" : "批量触发评分";
});

const errorList = computed(() => {
  return props.batchList.filter(item => !item.success);
});
const successCount = computed(() => {
  return props.batchList?.length - errorList.value?.length;
});

function closeDialog() {
  emit("closeDialog");
}

function openErrorDialog() {
  emit("openErrorDialog");
}
</script>

<style scoped lang="scss">
.tip-icon {
  color: var(--el-color-warning);
}
</style>
