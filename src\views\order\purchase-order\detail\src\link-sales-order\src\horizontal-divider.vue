<template>
  <div class="divider relative flex-c bg-bg_color">
    <el-button
      class="relative z-10 !h-4"
      :class="collapsed ? '!cursor-[s-resize]' : '!cursor-[n-resize]'"
      size="small"
      @click="collapsed = !collapsed"
      v-track="TrackPointKey.FORM_PURCHASE_SALES_VIEW_MORE"
    >
      <IconifyIconOffline :icon="ArrowUp" class="transition-transform" :class="{ 'rotate-180': collapsed }" />
    </el-button>
    <div class="line" />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import ArrowUp from "@iconify-icons/ep/arrow-up";
import { TrackPointKey } from "@/consts";

const props = defineProps<{
  modelValue: boolean;
}>();
const emits = defineEmits<{
  (e: "update:modelValue", collapsed: boolean): void;
}>();

const collapsed = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
</script>

<style scoped>
.divider {
  width: calc(100% - 40px);
  margin-left: 20px;
}

.line {
  @apply absolute w-full h-px hidden;
  border-top: 1px var(--el-border-color) var(--el-border-style);
}

.el-button:hover + .line {
  @apply block;
}
</style>
