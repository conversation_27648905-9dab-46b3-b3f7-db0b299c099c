<template>
  <el-form
    ref="rawMaterialFormRef"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
    :model="rawMaterialForm"
    :rules="rules"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="原材料类型" prop="processName">
          <el-input v-model="rawMaterialForm.processName" placeholder="请输入原材料类型" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="原材料编号" prop="code">
          <SerialNumber
            v-if="showSerialNumber"
            :code="RAW_MATERIAL_NO_CODE"
            :create="isAddRawFlag"
            v-model="rawMaterialForm.code"
            placeholder="请输入原材料编号"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="原材料名称" prop="name">
          <el-input v-model="rawMaterialForm.name" placeholder="请输入原材料名称" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageGrade">
          <EnumSelect
            class="w-full"
            v-model="rawMaterialForm.voltageGrade"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="modelCode">
          <el-input v-model="rawMaterialForm.modelCode" placeholder="请输入规格型号" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="品牌" prop="borMaterials">
          <el-input v-model="rawMaterialForm.borMaterials" placeholder="请输入品牌" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产地" prop="oorMaterials">
          <el-input v-model="rawMaterialForm.oorMaterials" placeholder="请输入产地" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="制造商" prop="rawmManufacturer">
          <el-input v-model="rawMaterialForm.rawmManufacturer" placeholder="请输入制造商" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="partUnit">
          <el-input v-model="rawMaterialForm.partUnit" placeholder="请输入计量单位" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="原材料批次号" prop="materialBatchNo">
          <el-input v-model="rawMaterialForm.materialBatchNo" placeholder="请输入原材料批次号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产日期" prop="productionDate">
          <el-date-picker
            class="!w-full"
            v-model="rawMaterialForm.productionDate"
            type="date"
            placeholder="请选择日期"
            :disabled-date="disabledNowAfterDate"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="rawMaterialForm.remark" :rows="2" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import EnumSelect from "@/components/EnumSelect";
import SerialNumber from "@/components/SerialNumber";
import { reactive, ref, inject, computed } from "vue";
import { ISaveAddRawMaterial } from "@/models/raw-material/i-raw-material-res-v2";
import { FormInstance, FormRules } from "element-plus";
import { VoltageClassesEnum } from "@/enums";
import { RAW_MATERIAL_NO_CODE } from "@/consts/serial-number-code";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import { useSalesFillInDataStore } from "@/store/modules";
import { voltageGardeRequireRawMaterial } from "@/views/order/utils";

const ctx = inject<{
  processInfo: IProductionMaterialProcess;
}>("addRawMaterialToken");

const emits = defineEmits<{
  (event: "saveSuccess"): void;
}>();

const fillInDataStore = useSalesFillInDataStore();
const subClassCode = fillInDataStore.data.subClassCode || fillInDataStore.data.subclassCode;
const voltageGradeRequire = computed(() => voltageGardeRequireRawMaterial(subClassCode));
const rawMaterialFormRef = ref();
const rawMaterialForm = reactive<ISaveAddRawMaterial>({
  name: "",
  code: "",
  processCode: ctx.processInfo.processUnionCode,
  processName: ctx.processInfo.processName,
  voltageGrade: null,
  modelCode: "",
  borMaterials: "",
  rawmManufacturer: "",
  oorMaterials: "",
  materialBatchNo: "",
  productionDate: "",
  partUnit: "",
  materialStandard: null,
  remark: ""
});
const rules = reactive<FormRules>({
  processName: [{ required: true, message: "请输入原材料类型", trigger: "change" }],
  code: [{ required: true, message: "请输入原材料编号", trigger: "change" }],
  name: [{ required: true, message: "请输入原材料名称", trigger: "change" }],
  voltageGrade: [{ required: voltageGradeRequire.value, message: "请选择/输入电压等级", trigger: "change" }],
  partUnit: [{ required: true, message: "请输入计量单位", trigger: "change" }],
  modelCode: [{ required: true, message: "请输入规格型号", trigger: "change" }],
  borMaterials: [{ required: true, message: "请输入品牌名称", trigger: "change" }],
  rawmManufacturer: [{ required: true, message: "请输入制造商", trigger: "change" }],
  materialBatchNo: [{ required: true, message: "请输入原材料批次号", trigger: "change" }],
  productionDate: [{ required: true, message: "请选择生产日期", trigger: "change" }]
});

const rawMaterialStore = useRawMaterialV2Store();
const isAddRawFlag = ref(true);
const showSerialNumber = ref(true);

// 返回表单数据之前校验表单
const submitForm = async () => {
  const formEl: FormInstance | undefined = rawMaterialFormRef.value;
  if (!formEl) return;
  return await formEl.validate(() => {});
};

/**
 * 新增原材料数据
 */
const saveAddRawMaterial = async () => {
  if (!(await submitForm())) return;
  // 保存数据
  await rawMaterialStore.saveAddRawMaterialList(rawMaterialForm);
  emits("saveSuccess");
};

/**
 * 重置数据
 */
const resetForm = () => {
  Object.assign(rawMaterialForm, {
    name: "",
    code: "",
    processCode: ctx.processInfo.processUnionCode,
    processName: ctx.processInfo.processName,
    voltageGrade: null,
    modelCode: "",
    borMaterials: "",
    rawmManufacturer: "",
    oorMaterials: "",
    materialBatchNo: "",
    productionDate: "",
    partUnit: "",
    remark: ""
  });
};

defineExpose({
  saveAddRawMaterial,
  resetForm
});
</script>

<style scoped lang="scss">
.raw-material-info {
  border-color: #dcdfe6;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);
}
</style>
