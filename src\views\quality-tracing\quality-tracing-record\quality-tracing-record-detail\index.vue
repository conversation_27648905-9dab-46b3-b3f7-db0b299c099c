<template>
  <div class="h-full flex flex-col">
    <base-info ref="baseInfoRef" :id="id" />
    <div class="mb-3 mx-6 flex-1 overflow-hidden flex flex-col">
      <el-row :gutter="20" class="pr-2 my-1.5">
        <el-col :span="20">
          <div class="text-sm my-1.5 flex items-center">
            <el-icon size="14">
              <Warning />
            </el-icon>
            <span class="ml-3">
              {{ calcScoreDes }}
            </span>
          </div>
        </el-col>
        <el-col :span="4" class="text-right pr-2">
          <el-button @click="handleExpandAll"> 全部展开 </el-button>
          <el-button @click="handleCollapseAll"> 全部收起 </el-button>
        </el-col>
      </el-row>
      <el-scrollbar class="flex-1">
        <el-collapse v-model="expandNames">
          <el-collapse-item v-for="item in stageListConfigArr" :key="item.key" :name="item.key" class="mb-5">
            <template #title>
              <collapse-item-title :title="item.title" :name="item.key" class="!py-0">
                <el-divider direction="vertical" />
                <div class="text-right">
                  合计得分：<span class="text-primary score-text">{{ scoreMap[item.scoreKey] }}</span>
                </div>
              </collapse-item-title>
            </template>
            <universal-list
              :category-code="item.category"
              :production-stage-id="scoreMap[item.collectionCode] || ''"
              :record-id="id"
            />
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { useRoute } from "vue-router";
import { Warning } from "@element-plus/icons-vue";
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import UniversalList from "./universal-list/index.vue";
import { QualityStageCategoryConfig } from "@/enums/quality-specification";
import { emptyDefaultValue } from "@/consts";
import BaseInfo from "./base-info.vue";
import { stageListConfigArr } from "./list-config";

/**
 * 质量追溯记录详情页面
 */

const route = useRoute();
const baseInfoRef = ref<InstanceType<typeof BaseInfo>>();
const id = route.params.id as string;
const expandNames = ref(genAllExpandNames());

const calcScoreDes = computed(() => {
  if (!baseInfoRef.value) {
    return "";
  }
  const { recordDetail } = baseInfoRef.value;
  return recordDetail.scoreDescription ? recordDetail.scoreDescription : emptyDefaultValue;
});

const scoreMap = computed(() => {
  if (!baseInfoRef.value) {
    return {
      rawMaterialTotalScore: "",
      processTotalScore: "",
      experimentTotalScore: "",
      processStabilitysTotalScore: ""
    };
  }
  const { rawMaterialScore, processScore, experimentScore, processStabilityScore } = baseInfoRef.value.recordDetail;
  return {
    rawMaterialTotalScore: rawMaterialScore,
    processTotalScore: processScore,
    experimentTotalScore: experimentScore,
    processStabilitysTotalScore: processStabilityScore
  };
});

/**
 * @description: 生成所有展开项名称
 */
function genAllExpandNames() {
  return Object.values(QualityStageCategoryConfig).map(({ name }) => name);
}

/**
 * @description: 展开全部卡片
 */
function handleExpandAll() {
  expandNames.value = genAllExpandNames();
}

/**
 * @description: 收起全部卡片
 */
function handleCollapseAll() {
  expandNames.value = [];
}
</script>
<style lang="scss" scoped>
:deep(.el-collapse-item__arrow) {
  display: none;
}

.score-text:empty:after {
  content: var(--default-value) !important;
}
</style>
