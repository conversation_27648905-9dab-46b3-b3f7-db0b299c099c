<template>
  <div class="flex flex-col flex-1 overflow-hidden">
    <TabSwitch
      :operate-modules="alarmModules"
      @switchModuleEvent="clickModuleChange"
      v-model:currentTabIndex="currentIndex"
    />
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
      <Component :is="currentComp" />
    </div>
  </div>
</template>

<script setup lang="ts">
import QualityAlarm from "./quality-alarm/index.vue";
import ProgressAlarm from "./progress-alarm/index.vue";
import OperationsAlarm from "./operations-alarm/index.vue";
import { onMounted, ref } from "vue";
import { reactive, shallowRef } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import TabSwitch from "@/components/cx-tab-switch";
import { IModule } from "@/components/cx-tab-switch/types";
// 设置标题
usePageStoreHook().setTitle("告警数据");

const currentComp = shallowRef();
const currentIndex = ref(0);

enum EModuleType {
  /** 质量告警 */
  QualityAlarm = "quality-alarm",
  /** 进度告警 */
  ProgressAlarm = "progress-alarm",
  /** 运维告警 */
  OperationsAlarm = "operations-alarm"
}

const alarmModules = reactive<Array<IModule>>([
  {
    moduleCode: EModuleType.QualityAlarm,
    moduleName: "质量告警"
  },
  {
    moduleCode: EModuleType.ProgressAlarm,
    moduleName: "进度告警"
  },
  {
    moduleCode: EModuleType.OperationsAlarm,
    moduleName: "运维告警"
  }
]);

onMounted(() => {
  clickModuleChange(alarmModules[0]);
});

/**
 * 点击不同的模块
 */
const clickModuleChange = (item: IModule) => {
  switch (item.moduleCode) {
    case EModuleType.QualityAlarm:
      currentComp.value = QualityAlarm;
      break;
    case EModuleType.ProgressAlarm:
      currentComp.value = ProgressAlarm;
      break;
    case EModuleType.OperationsAlarm:
      currentComp.value = OperationsAlarm;
      break;
    default:
      currentComp.value = QualityAlarm;
      break;
  }
};
</script>

<style scoped lang="scss"></style>
