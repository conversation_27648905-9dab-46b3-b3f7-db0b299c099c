import { IPagingReq, ISortReq } from "../i-paging-req";

export interface IDownLoadDetailLog {
  fileName: string;
  fileUrl: string;
}

/** 搜索E基建网关日志 */
export interface ISearchEJJGatewayReq extends IPagingReq, ISortReq {
  /** 请求开始时间 */
  requestTimeStart?: string;
  /** 请求结束时间 */
  requestTimeEnd?: string;
  /**  */
  subClassCode?: string;
  interfaceName?: string;
}

/** 搜索网关日志 */
export interface ISearchGatewayReq extends IPagingReq, ISortReq {
  statisticalTimeStart?: string;
  statisticalTimeEnd?: string;
  categoryCode?: string;
  /**  */
  matStageCode?: string;
  matProcessCode?: string;
}

export interface IOptionsChildren {
  matProcessCode: string;
  matProcessName: string;
}

export interface IEJJInterface {
  interfaceName: string;
  interfaceDesc: string;
}
export interface ISearchOptionList {
  matStageCode: string;
  matStageName: string;
  childrens: IOptionsChildren[];
}

/**
 * 网关日志数据
 */
export interface IGatewayList {
  id: string;
  /** 物资种类 */
  categoryCode: string;
  categoryName: string;
  /** 生产阶段信息 */
  matStageCode: string;
  matStageName: string;
  /** 工序 */
  matProcessCode: string;
  matProcessName: string;
  /** 统计时间 */
  statisticalTime: string;
  /** 拉取次数 */
  fetchCount: number;
  /** 拉取异常 */
  awaitSize: number;
  /** 拉取条数 */
  fetchSize: number;
  /** interfaceSceneId */
  interfaceSceneId: number;
}

/**
 * E基建网关日志数据
 */
export interface IEJJGatewayList {
  id: string;
  /** 物资种类 */
  equipmentType: number;
  subClassCode: string;
  /** 生产阶段信息 */
  interfaceName: string;
  interfaceDesc: string;
  /** 工序 */
  matProcessCode: string;
  matProcessName: string;
  /** 统计时间 */
  requestTime: string;
  responseTime: string;
  elapsedTime: number;
  /** 拉取条数 */
  resultSize: number;
  requestMethod: string;
  statusCode: string;
  amount: number;
}

/** 日志详情搜索 */
export interface ISearchDetailReq extends IPagingReq, ISortReq {
  requestTimeStart?: string;
  requestTimeEnd?: string;
  interfaceSceneId?: string | unknown;
  statusCodeQuery?: number;
  querySizeNotEmpty?: boolean;
  ip?: string;
}

/** */
export enum EStatusCode {
  /** 成功 */
  Success = 200
}

/** 日志详情数据 */
export interface IGatewayDetailList {
  id?: string;
  tenantId: string;
  interfaceSceneId: string;
  /** IP地址 */
  ip: string;
  /** 请求时间 */
  requestTime: string;
  /**  */
  requestContent: string;
  /** 响应时间  */
  responseTime: string;
  /** 响应返回  */
  responseContent: string;
  /** 请求耗时(毫秒) */
  elapsedTime: number;
  /**  */
  requestMethod: string;
  /** 响应状态码,200表示成功,其他都为异常 */
  statusCode: number;
  /**  */
  category: number;
  /** 上报:响应条数/拉取:拉取条数 */
  resultSize: number;
  /** 失败数量 */
  awaitSize: number;
  /** 报错日志 */
  errorMessage: string;
  /** eip上报,请求的条数 */
  amount: number;
}

/** 当日记录查询 */
export interface IGatewayTodayListReq extends IPagingReq {
  /** 排序字段 */
  orderByField?: string;
  /** 排序方式,传入desc/asc */
  orderByType?: string;
  /** 请求开始时间 */
  requestTimeStart?: string;
  /** 请求结束时间 */
  requestTimeEnd?: string;
  /** 接口id */
  interfaceSceneId?: number;
  /** 接口状态 */
  statusCodeQuery?: number;
  /** 是否查询拉取数量不为空 */
  querySizeNotEmpty?: boolean;
  /** 物资种类编码 */
  categoryCode?: string;
  /** 生产阶段编码 */
  matStageCode?: string;
  /** 工序 */
  matProcessCode?: string;
  /** IP */
  ip?: string;
}

export interface IGatewayTodayList extends IGatewayDetailList {
  /** 物资种类名称 */
  categoryName: string;
  /** 物资种类编码 */
  categoryCode: string;
  /** 生产阶段编码 */
  matStageCode: string;
  /** 生产阶段名称 */
  matStageName: string;
  /** 工序 */
  matProcessCode: string;
  /** 工序名称 */
  matProcessName: string;
}
