<template>
  <div>
    <el-scrollbar :height="348" v-if="activeTab === 'card'">
      <div
        class="grid gap-4 overflow-hidden"
        v-infinite-scroll="load"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="false"
      >
        <div v-for="item in productionProcessInspecStore.processInspectionTableData" :key="item.id">
          <div class="info-content">
            <div class="header flex-bc">
              <TitleBar title="过程检测信息" />
              <div class="operate">
                <el-button
                  v-auth="PermissionKey.form.formPurchaseProcessDelete"
                  type="danger"
                  @click="delProductionInspection(item)"
                >
                  <el-icon class="mr-2"><Delete /></el-icon>
                  删除
                </el-button>
                <el-button
                  v-auth="PermissionKey.form.formPurchaseProcessCreate"
                  type="primary"
                  :icon="CopyDocument"
                  v-if="isCanOperateAddAndEdit"
                  @click="copyProcessInspection(item)"
                >
                  复制
                </el-button>
                <el-button
                  v-auth="PermissionKey.form.formPurchaseProcessEdit"
                  type="primary"
                  v-if="isCanOperateAddAndEdit"
                  @click="editProductionInspection(item)"
                >
                  <FontIcon class="mr-2" icon="icon-edit" />
                  编辑
                </el-button>
              </div>
            </div>
            <div>
              <el-descriptions class="mb-4">
                <el-descriptions-item label-align="right" label="过程检测编号">
                  {{ item.code }}
                </el-descriptions-item>
                <el-descriptions-item label-align="right" label="设备">
                  {{ item.deviceName }}
                </el-descriptions-item>
                <el-descriptions-item label-align="right" label="检测时间">
                  {{ item.checkTime }}
                </el-descriptions-item>
              </el-descriptions>
              <PureTable
                class="flex-1 overflow-hidden"
                row-key="id"
                :data="item.processDetailDataValue"
                :columns="columns"
                :max-height="300"
                showOverflowTooltip
                :row-class-name="getRowClassName"
              >
                <template #empty>
                  <el-empty :image-size="120">
                    <template #image> <emptyData /> </template>
                  </el-empty>
                </template>
              </PureTable>
            </div>
          </div>
        </div>
      </div>
      <div v-if="emptyShow" class="flex flex-c">
        <div class="text-center p-4">
          <emptyData />
          <div class="not-data">暂无数据</div>
        </div>
      </div>
    </el-scrollbar>
    <List
      class="h-96"
      v-if="activeTab === 'list'"
      :editProdInspect="editProductionInspection"
      :delProdInspect="delProductionInspection"
      :copyProcessInspection="copyProcessInspection"
    />
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { Delete } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import emptyData from "@/assets/svg/empty_data.svg?component";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { IProductionProcessList } from "@/models/production-test-sensing/i-production-process-inspection";
import { PermissionKey } from "@/consts";
import { computed } from "vue";
import { CopyDocument } from "@element-plus/icons-vue";
import List from "./list/index.vue";

defineProps<{
  activeTab: "card" | "list";
}>();

const emits = defineEmits<{
  (event: "editProdInspect", processInfo: IProductionProcessList): void;
  (event: "delProdInspect", processInfo: IProductionProcessList): void;
  (event: "copyProcessInspection", processInfo: IProductionProcessList): void;
  (event: "loadMore"): void;
}>();

const { columns } = useColumns();
const productionProcessInspecStore = useProductionProcessInspecStore();
/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => productionProcessInspecStore.getIsCanOperateAddAndEdit);

const emptyShow = computed(() => {
  return (
    !productionProcessInspecStore.loadingInspect && !productionProcessInspecStore.processInspectionTableData?.length
  );
});

/**
 * 加载数据信息
 */
const load = () => {
  emits("loadMore");
};

// 删除过程检测信息
const delProductionInspection = (processInfo: IProductionProcessList) => {
  emits("delProdInspect", processInfo);
};

// 编辑过程检测信息
const editProductionInspection = (processInfo: IProductionProcessList) => {
  emits("editProdInspect", processInfo);
};

// 复制过程检测信息
const copyProcessInspection = (processInfo: IProductionProcessList) => {
  emits("copyProcessInspection", processInfo);
};

// 设置行类名
function getRowClassName({ row }) {
  const valid = row.validated;
  return valid ? null : "validated-waring";
}
</script>

<style scoped lang="scss">
.info-content {
  padding: 20px;
  border-radius: 10px;
  border: 1px solid var(--el-border-color);
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);

  &:not(:last-of-type) {
    margin-bottom: 20px;
  }

  .header {
    @apply mb-3 pb-3;
    border-bottom: 1px solid var(--el-border-color);
  }
}

.text-center {
  .not-data {
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-secondary);
  }
}

:deep(.validated-waring) {
  .valid-waring {
    color: var(--el-color-warning);
  }
}
</style>
