import { SupervisionStatusEnum, SyncStatusEnum } from "@/enums/south-grid-access";
import { IPagingReq } from "@/models";

export interface ISupervisionPlanReq extends IPagingReq {
  id?: string;
  keywords?: string;

  /** 监造状态 */
  supervisionStatus?: SupervisionStatusEnum;

  /** 同步状态 */
  isSync?: SyncStatusEnum;

  planStartDate?: string;

  planEndDate?: string;

  /** 是否关注 */
  follow?: boolean;

  /** 未读 */
  unRead?: boolean;
}
