import { SyncStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";

export interface IProductionDocument extends IBase {
  /**
   * 监造计划编号
   */
  supervisionNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 物料名称
   */
  productionMaterialName?: string;
  /**
   * 物资编码
   */
  categoryCode?: string;
  /**
   * 物料编码
   */
  productionMaterialCode?: string;
  /**
   * 物料描述
   */
  productionMaterialDesc?: string;
  /**
   * 数量
   */
  productionMaterialNumber?: number;
  /**
   * 单位
   */
  productionMaterialUnit?: string;
  /**
   * 电压等级
   */
  voltageClasses?: number;
  /**
   * 计划开始日期,格式:yyyy-MM-dd
   */
  planStartDate?: string;
  /**
   * 计划完成日期
   */
  planEndDate?: string;
  /**
   * 是否同步,0-未同步，1-已同步
   */
  isSync?: SyncStatusEnum;

  /**
   * 删除说明
   */
  reason?: string;
}
