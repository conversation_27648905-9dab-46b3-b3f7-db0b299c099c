<template>
  <el-button
    v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationDeleteStandard"
    link
    type="danger"
    @click="handleDelete"
  >
    删除
  </el-button>
</template>
<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { PermissionKey } from "@/consts";
import { deleteQualitySpecificationParticularDetail } from "@/api/quality-tracing";

/**
 * 删除 检测标准
 */

const props = defineProps<{
  /** 检测标准id */
  id: string;
}>();

const emits = defineEmits<{
  /** 删除后置操作 */
  (event: "postDeleteSuccess"): void;
}>();

/**
 * @description: 删除行为
 */
async function handleDelete() {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  const res = await deleteQualitySpecificationParticularDetail(props.id);

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  emits("postDeleteSuccess");
}
</script>
