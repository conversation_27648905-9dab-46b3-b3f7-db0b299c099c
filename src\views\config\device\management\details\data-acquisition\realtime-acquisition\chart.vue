<template>
  <v-chart :option="option" autoresize class="min-w-[280px] w-full h-full" />
</template>

<script setup lang="ts">
import { PropType, computed } from "vue";
import VChart from "vue-echarts";

const props = defineProps({
  xAxisData: {
    type: Array as PropType<Array<string>>,
    default: () => []
  },
  yAxisData: {
    type: Array as PropType<Array<string>>,
    default: () => []
  }
});

const option = computed(() => {
  const yDataArr = props.yAxisData.map(value => +value);
  const maxValue = Math.max(...yDataArr);
  const minValue = Math.min(...yDataArr);
  return {
    grid: {
      left: "10%",
      top: "10%",
      right: "5%",
      bottom: 18
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLabel: {
        show: true,
        color: "#909399"
      },
      splitNumber: 5,
      data: props.xAxisData
    },
    yAxis: {
      type: "value",
      axisLabel: {
        show: true,
        color: "#909399"
      },
      splitNumber: 10,
      min: Math.floor(minValue - Math.abs(minValue * 0.1)),
      max: Math.ceil(maxValue + Math.abs(maxValue * 0.1))
    },
    tooltip: {
      trigger: "axis"
    },
    series: [
      {
        type: "line",
        smooth: true,
        sampling: "lttb",
        data: props.yAxisData
      }
    ],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: !!props.xAxisData.length,
      style: {
        text: "暂无采集点数据",
        fontSize: "16px",
        fill: "#909399"
      }
    }
  };
});
</script>

<style scoped></style>
