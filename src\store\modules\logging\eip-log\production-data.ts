import { defineStore } from "pinia";
import * as api from "@/api/logging/eip";
import { IEipLog, IEipLogReq } from "@/models";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

export const useProductionDataStore = defineStore({
  id: "production-data-store",
  state: () => ({
    total: 0 as number,
    loading: false as boolean,
    list: [] as Array<IEipLog>
  }),
  actions: {
    async querySgOrder(params: IEipLogReq) {
      this.loading = true;
      const res = await api.queryEIPLog(omitBy(params, val => isAllEmpty(val))).finally(() => {
        this.loading = false;
      });
      this.total = res.data.total;
      this.list = res.data.list;
    },

    async getDownloadFileUrl(id: string) {
      return (await api.getDownloadFileUrl(id)).data;
    }
  }
});
