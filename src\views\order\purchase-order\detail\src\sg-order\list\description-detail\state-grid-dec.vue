<template>
  <div class="title font-medium">
    <span class="label">采购订单行项目号</span>
    <span class="content">{{ sync.poItemNo }}</span>
  </div>
  <div class="descriptions">
    <el-row>
      <el-col :span="6">
        <div class="description-item">
          <div class="label">物料编码</div>
          <div class="content">{{ sync.materialCode }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="description-item">
          <div class="label">采购数量</div>
          <div class="content">{{ sync.amount }}</div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="description-item">
          <div class="label">物料描述</div>
          <div class="content">
            <ShowTooltip class-name="max-w-[15em] lg:max-w-[20em] xl:max-w-[30em]" :content="sync.materialDesc" />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { IStateGridOrderSync } from "@/models/state-grid-sync";
import { computed } from "vue";

const props = defineProps<{
  sync: IStateGridOrderSync;
}>();

const sync = computed(() => props.sync);
</script>
