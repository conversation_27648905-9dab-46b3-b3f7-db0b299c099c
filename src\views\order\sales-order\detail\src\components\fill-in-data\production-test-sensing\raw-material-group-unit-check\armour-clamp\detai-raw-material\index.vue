<template>
  <div class="add-raw-material">
    <el-dialog
      v-model="detailDigVisible"
      title="原材料检详情"
      class="middle"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="onClose()"
    >
      <div class="base-info">
        <RawMaterialInfo :baseInfoDetail="baseInfoDetail" />
      </div>

      <div class="material-test">
        <MaterialForm :editMode="false" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import RawMaterialInfo from "./raw-material-info/index.vue";
import MaterialForm from "./material-form/index.vue";
import { ref, watchEffect } from "vue";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";

defineOptions({
  name: "raw-material-detail"
});

// 定义抛出的事件
const emits = defineEmits<{
  (event: "detailCloseDiag"): void;
}>();

// 控制详情的弹框是否展示
const detailDigVisible = ref<boolean>();
const baseInfoDetail = ref();
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
watchEffect(() => {
  detailDigVisible.value = rawMaterialGroupUnitStore.detailRawMaterialVisible;
  baseInfoDetail.value = rawMaterialGroupUnitStore.detailRawMaterialGroupUnit;
});

// 关闭弹框
const onClose = () => {
  emits("detailCloseDiag");
};
</script>

<style scoped lang="scss">
.hidden {
  display: none;
}

.block {
  display: block;
}
</style>
