import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "过程检编号",
      prop: "code"
    },
    {
      label: "产品名称",
      prop: "productName"
    },
    {
      label: "产品型号",
      prop: "model"
    },
    {
      label: "生产批次号",
      prop: "productBatchNo"
    },
    {
      label: "加工方式(本体)",
      prop: "processMethod"
    },
    {
      label: "加工件数(本体)",
      prop: "processNumber"
    },
    {
      label: "生产完成时间",
      prop: "productFinishTime",
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      width: TableWidth.operation,
      fixed: "right",
      slot: "operation"
    }
  ];
  return { columns };
}
