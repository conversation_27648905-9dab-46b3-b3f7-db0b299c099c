<template>
  <div class="mb-5">
    <div class="text-right pb-3">
      <ElButton link type="primary" v-if="props.importDataType?.templateUrl" @click="onDownloadTemplate()"
        >下载模板</ElButton
      >
    </div>
    <el-upload
      ref="uploadRef"
      drag
      v-model:file-list="fileList"
      :auto-upload="false"
      :disabled="!!taskId"
      accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
      :on-change="onFileChange"
      :limit="1"
      :on-exceed="onExceed"
    >
      <el-icon size="24"><UploadFilled /></el-icon>
      <div class="el-upload__text">
        <div class="upload-text"><span>将导入文件拖到此处，或</span><em>点击上传</em></div>
        <div class="upload-tips">
          <span>仅支持excel格式文件.</span>
        </div>
      </div>
    </el-upload>
    <div class="p-2" v-if="importDataProcess">
      <el-progress :percentage="props.percentage" />
    </div>
    <div class="rule">
      <div>1.仅支持上传excel，文件大小不能超过20M</div>
      <div>2.请基于模板维护数据，请勿修改单元格</div>
      <div>3.每次只允许上传一个文件</div>
    </div>
    <template v-if="importDataProcess">
      <el-divider />
      <div class="import-result">
        <div class="result">导入结果</div>
        <div class="metric">
          <div v-if="importDataProcess?.count > 0">
            <span>总条数：</span>
            <span class="font-semibold">{{ importDataProcess.count }}条</span>
          </div>
          <div v-if="importDataProcess?.successCount > 0">
            <span>导入成功：</span>
            <span class="success">{{ importDataProcess.successCount }}条</span>
          </div>
          <div v-if="importDataProcess?.errorCount > 0">
            <span>导入失败：</span>
            <span class="fail">{{ importDataProcess.errorCount }}条</span>
          </div>
          <ElButton
            v-if="uploadErrorExcel && props.importDataProcess?.errorExcelUrl"
            type="primary"
            link
            @click="onDownloadErrorExcel()"
            >下载错误Excel</ElButton
          >
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import * as importService from "@/api/import";
import { IUpLoadFileRes, IImportDataType } from "@/models";
import { ImportDataTypeMapEnumName } from "@/enums";

const fileList = ref();
const uploadRef = ref();
const uploadFile = ref();

defineExpose({
  handleUploadFile
});

const props = withDefaults(
  defineProps<{
    percentage: number;
    importDataType?: IImportDataType;
    importDataProcess?: IImportDataType;
    taskId?: string;
  }>(),
  {
    percentage: 0,
    importDataProcess: undefined,
    taskId: undefined,
    importDataType: undefined
  }
);

const uploadErrorExcel = computed(
  () =>
    props.importDataProcess?.count > 0 &&
    props.importDataProcess?.errorCount > 0 &&
    (props.importDataProcess?.count || 0) ===
      (props.importDataProcess?.successCount || 0) + (props.importDataProcess?.errorCount || 0)
);

const emits = defineEmits<{
  (e: "downloadTemplate"): void;
  (e: "downloadErrorExcel"): void;
  (e: "uploadFileChange"): void;
}>();

const onExceed = (files: Array<any>) => {
  const file = files?.[0];
  if (!file) {
    return;
  }

  if (!verifyFile(file)) {
    return;
  }

  fileList.value = [file];
  uploadFile.value = file;
  uploadFileChange();
};

const onFileChange = async (file: UploadFile) => {
  if (!verifyFile(file)) {
    return;
  }
  uploadFile.value = file.raw;
  uploadFileChange();
};

async function handleUploadFile(importDataType: IImportDataType): Promise<IUpLoadFileRes> {
  const formData = new FormData();
  formData.append("file", uploadFile.value);
  formData.append("dataType", ImportDataTypeMapEnumName[importDataType.dataType]);
  return (await importService.fileUpolad(formData)).data;
}

const verifyFile = (file: UploadFile): boolean => {
  if (file.size / 1024 / 1024 > 20) {
    fileList.value = [];
    uploadFile.value = undefined;
    uploadRef.value?.clearFiles();
    ElMessage.warning("请选择上传文件小于20M");
    return false;
  }
  return true;
};

const onDownloadTemplate = () => {
  emits("downloadTemplate");
};

const onDownloadErrorExcel = () => {
  emits("downloadErrorExcel");
};

function uploadFileChange() {
  emits("uploadFileChange");
}
</script>

<style scoped lang="scss">
.metric {
  display: flex;
  align-items: center;
  line-height: 30px;

  div {
    margin-right: 10px;
  }

  .success {
    color: var(--el-color-primary);
  }

  .fail {
    color: var(--el-color-danger);
  }
}
</style>
