import { PaginationProps, TableColumnRenderer } from "@pureadmin/table";
import { reactive, watchEffect } from "vue";
import { RouterLink } from "vue-router";
import { LinkStepColorDesc, LinkStepMapDesc, TableWidth, ColumnWidth, PurchaseChannel } from "@/enums";
import { usePurchaseOrderStore } from "@/store/modules";
import CxTag from "@/components/CxTag/index.vue";
import { IPurchaseOrder } from "@/models";
import { formatDate } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { fullDateFormat, TrackPointKey } from "@/consts";
import { StarFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";

export function useColumns(t) {
  const { statusFormatter } = useTableCellFormatter();
  const purchaseOrderStore = usePurchaseOrderStore();
  const columns: TableColumnList = [
    {
      label: "选择",
      prop: "select",
      type: "selection",
      fixed: "left",
      width: TableWidth.radio
    },
    {
      label: "关注",
      prop: "follow",
      fixed: "left",
      width: TableWidth.check,
      align: "center",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div
            class="flex justify-center"
            onClick={() => onToggleFollow(data.row)}
            v-track={TrackPointKey.FORM_PURCHASE_PAY_ATTENTION}
          >
            <StarFilled style={{ width: "20", cursor: "pointer", color: data.row.follow ? "#E6A23C" : "#DEDFE0" }} />
          </div>
        );
      }
    },
    {
      label: "",
      prop: "readed",
      width: "24",
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return data?.row.readed ? <span /> : <span class="icon-point mb-0.5" />;
      }
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.poNo"),
      prop: "poNo",
      fixed: "left",
      sortable: "custom",
      minWidth: ColumnWidth.Char7,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <RouterLink class="text-primary" to={{ name: "purchaseOrderDetail", params: { id: data.row.id } }}>
            {data.row.poNo}
          </RouterLink>
        );
      }
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.conCode"),
      prop: "conCode",
      sortable: "custom",
      minWidth: TableWidth.order
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.conName"),
      prop: "conName",
      minWidth: TableWidth.largeName
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.subClassName"),
      prop: "subClassName",
      width: ColumnWidth.Char9
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.buyerName"),
      prop: "buyerName",
      width: ColumnWidth.Char14,
      cellRenderer: (data: TableColumnRenderer) => {
        if (data.row.buyerName) {
          return (
            <CxTag icon="icon-company-fill" type="custom">
              {data.row.buyerName}
            </CxTag>
          );
        }
      }
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.sellerConCode"),
      prop: "sellerConCode",
      sortable: "custom",
      width: ColumnWidth.Char11
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.prjName"),
      prop: "prjName",
      minWidth: TableWidth.largeName
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.sellerSignTime"),
      prop: "sellerSignTime",
      width: ColumnWidth.Char7,
      formatter: (row: IPurchaseOrder) => formatDate(row.sellerSignTime)
    },
    {
      label: "拉取时间",
      prop: "pullingTime",
      minWidth: TableWidth.dateTime,
      sortable: "custom",
      formatter: (row: IPurchaseOrder) => formatDate(row.pullingTime, fullDateFormat)
    },
    {
      label: "来源",
      prop: "syncChannel",
      minWidth: ColumnWidth.Char3,
      formatter: (row: IPurchaseOrder) => {
        const { syncChannel } = row;
        if (syncChannel === PurchaseChannel.EIP) {
          return "国网";
        } else if (syncChannel === PurchaseChannel.CSG_GuangZhou) {
          return "南网";
        }
        return "";
      }
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.linkStep"),
      prop: "linkStep",
      fixed: "right",
      width: ColumnWidth.Char7,
      cellRenderer: (data: TableColumnRenderer) => {
        return <CxTag class={LinkStepColorDesc[data.row.linkStep]}>{t(LinkStepMapDesc[data.row.linkStep])}</CxTag>;
      }
    },
    {
      label: t("purchaseOrder.column.purchaseOrder.synType"),
      prop: "synType",
      fixed: "right",
      width: ColumnWidth.Char5,
      formatter: statusFormatter("已触发同步", "同步失败")
    },
    {
      label: "触发结果",
      prop: "triggerType",
      fixed: "right",
      width: ColumnWidth.Char8,
      formatter: statusFormatter("已触发评分", "评分触发失败")
    }
  ];
  /** 分页配置 */
  const pagination = reactive<PaginationProps>({
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 30, 50, 100],
    total: 0,
    background: true,
    small: false
  });

  const onToggleFollow = async (data: IPurchaseOrder) => {
    if (data.follow) {
      if (!(await useConfirm("是否确认取消关注该采购订单", "取消关注"))) {
        return;
      }
    }

    await purchaseOrderStore.purchaseOrderFollow(data.id);
    ElMessage.success(data.follow ? "取消关注订单成功" : "关注订单成功");
    //  purchaseOrderStore.queryPurchaseOrdersByPageIndex();
    purchaseOrderStore.getStatisticsPurchaseOrders();
    data.follow = !data.follow;
  };

  watchEffect(() => {
    pagination.total = purchaseOrderStore.total;
  });

  watchEffect(() => {
    pagination.currentPage = purchaseOrderStore.queryParams?.pageNo || 1;
  });

  return { columns, pagination };
}
