import { TableColumnRenderer } from "@pureadmin/table";
import dayjs from "dayjs";
import { useSalesOrderDetailStore, useSalesSchedulingPlanStore } from "@/store/modules";
import { ElMessage, ElMessageBox } from "element-plus";
import { IResponse, ISchedulingPlan } from "@/models";
import { PermissionKey, dateFormat, TrackPointKey, MEASURE_UNIT } from "@/consts";
import { ColumnWidth, TableWidth } from "@/enums";
import { ref } from "vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const schedulingPlanStore = useSalesSchedulingPlanStore();
  const salesOrderDetailStore = useSalesOrderDetailStore();
  const { withUnitFormatter } = useTableCellFormatter();
  const editSchedulingPlanModalVisible = ref<boolean>();
  const columns: TableColumnList = [
    {
      label: "排产计划编码",
      prop: "ppNo",
      width: TableWidth.order,
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div class="text-primary cursor-pointer" onClick={() => onViewSchedulingPlan(data.row)}>
            {data.row?.ppNo}
          </div>
        );
      }
    },
    {
      label: "销售订单号",
      prop: "soNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "销售订单行项目",
      prop: "soItemNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "生产工号",
      prop: "productionWorkNo",
      minWidth: TableWidth.order
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "排产进度",
      prop: "schedule",
      slot: "progress",
      width: TableWidth.name
    },
    {
      label: "排产数量",
      prop: "amount",
      align: "right",
      width: TableWidth.number,
      formatter: withUnitFormatter("measUnit", "subClassCode", MEASURE_UNIT)
    },
    {
      label: "计划日期",
      prop: "planDate",
      width: TableWidth.dateRanger,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <span>
            {data.row.planDate?.[0] ? dayjs(data.row.planDate[0]).format(dateFormat) : null}～
            {data.row.planDate?.[1] ? dayjs(data.row.planDate[1]).format(dateFormat) : null}
          </span>
        );
      }
    },
    {
      label: "计划工期",
      prop: "planWorkDuration",
      width: TableWidth.unit,
      align: "right",
      formatter: (row: ISchedulingPlan) => `${row.planWorkDuration} 天`
    },
    {
      label: "最晚交付日期",
      prop: "deliveryDate",
      width: TableWidth.date,
      formatter: (row: ISchedulingPlan) => dayjs(row.deliveryDate).format(dateFormat)
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            <el-button
              v-auth={PermissionKey.form.formPurchasePlanEdit}
              v-track={TrackPointKey.FORM_PURCHASE_PLAN_EDIT}
              link
              type="primary"
              onClick={() => onEditSchedulingPlan(data.row)}
            >
              编辑
            </el-button>
            <el-button
              v-auth={PermissionKey.form.formPurchasePlanDelete}
              v-track={TrackPointKey.FORM_PURCHASE_PLAN_DELETE}
              link
              type="danger"
              onClick={() => onDeleteSchedulingPlan(data.row.id)}
            >
              删除
            </el-button>
          </div>
        );
      }
    }
  ];

  const onDeleteSchedulingPlan = (id: string) => {
    ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        const res: IResponse<boolean> = await schedulingPlanStore.deleteSchedulingPlan(id);
        if (!res.data) {
          ElMessage({ type: "warning", message: res.msg || "网络异常" });
          return;
        }
        ElMessage({ type: "success", message: "删除成功" });
        schedulingPlanStore.setDeleteSchedulingPlanStatus(true);
        schedulingPlanStore.getSchedulingPlanStatistics(salesOrderDetailStore.salesOrder.id);
        salesOrderDetailStore.refreshStepStatus();
      })
      .catch(() => {});
  };

  const onEditSchedulingPlan = async (data: ISchedulingPlan) => {
    editSchedulingPlanModalVisible.value = true;
    schedulingPlanStore.setSchedulingPlanFormAddMode(false);
    const res = await schedulingPlanStore.getSchedulingPlanDetailById(data.id);
    const {
      id,
      purchaseId,
      ppNo,
      salesLineId,
      amount,
      measUnit,
      planDate,
      planWorkDuration,
      deliveryDate,
      actStartDate,
      actEndDate,
      schedule,
      subClassCode
    } = res.data;
    schedulingPlanStore.setSchedulingPlanFormValue({
      id,
      purchaseId,
      ppNo,
      salesLineId,
      amount,
      measUnit,
      planDate,
      planWorkDuration,
      deliveryDate,
      actStartDate,
      actEndDate,
      schedule,
      subClassCode: data.subClassCode || subClassCode
    });
  };

  const onViewSchedulingPlan = async (data: ISchedulingPlan) => {
    schedulingPlanStore.setSchedulingPlanDetailVisible(true);
    const res = await schedulingPlanStore.getSchedulingPlanDetailById(data.id);
    schedulingPlanStore.setSchedulingPlanDetail(res.data);
  };

  return { columns, editSchedulingPlanModalVisible };
}
