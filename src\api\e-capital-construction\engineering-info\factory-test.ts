/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, FactoryTestModel, FactoryTableModel } from "@/models";
/**
 * @description: 出厂试验列表
 */
export const FactoryTestApi = (id: String) => {
  return http.get<string, IResponse<Array<FactoryTestModel>>>(
    withApiGateway(`admin-api/ejj/project/experiment/base/list/${id}`)
  );
};
/**
 * @description: 创建出厂试验
 */
export const FactoryTestCreateApi = (params: FactoryTestModel) => {
  return http.post<FactoryTestModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/experiment/base`), {
    data: params
  });
};

/**
 * @description: 编辑出厂试验
 */
export const FactoryTestEditApi = (params: FactoryTestModel) => {
  return http.put<FactoryTestModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/experiment/base`), {
    data: params
  });
};

/**
 * @description: 删除出厂试验
 */
export const FactoryTestDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/experiment/base/${id}`));
};

/**
 * @description: 获取表单参数
 */
export const FactoryTableApi = (id: string, collectionCode: string) => {
  return http.get<string, IResponse<FactoryTableModel>>(
    withApiGateway(`admin-api/ejj/project/experiment/base/${id}/${collectionCode}`)
  );
};

/**
 * @description: 查看 出厂试验 信息
 */
export const FactoryTableDetailApi = (id: string) => {
  return http.get<string, IResponse<FactoryTestModel>>(withApiGateway(`/admin-api/ejj/project/experiment/base/${id}`));
};
