import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { IStockSync, ISyncStockStatus } from "@/models/stock-data-center/stock-sync/i-sync-stock";
import { http } from "@/utils/http";

/**
 * 获取同步数据的状态
 */
export function getSyncStockStatus() {
  const url = withApiGateway(`admin-api/business/inventory/getInventoryResult`);
  return http.get<void, IResponse<ISyncStockStatus>>(url);
}

/**
 * 一键同步 / 重新同步
 */
export function syncAllByOneKey(paramsData: IStockSync) {
  const url = withApiGateway(`admin-api/reporting/scheduling/task/inventory-create`);
  return http.post<IStockSync, IResponse<string>>(url, { data: paramsData });
}

/**
 * 重新同步
 */
// export function againSyncStock(paramsData: IStockSync) {
//   const url: string = withApiGateway(`admin-api/reporting/scheduling/task/create`);
//   return http.post<IStockSync, IResponse<string>>(url, { data: paramsData });
// }
