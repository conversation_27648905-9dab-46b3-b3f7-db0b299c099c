<template>
  <el-form
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="成品编号" prop="productNo">
          <el-input v-model="formValue.productNo" placeholder="请输入成品编号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="生产批次号"
          prop="productBatchNo"
          :rules="{
            required: productBatchNoRequired,
            message: '生产批次号不能为空',
            trigger: 'change'
          }"
        >
          <el-input v-model="formValue.productBatchNo" placeholder="请输入生产批次号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input v-model="formValue.specificationModel" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageLevel">
          <EnumSelect
            class="flex-1"
            v-model="formValue.voltageLevel"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="数量" prop="amount">
          <el-input-number
            v-model="formValue.amount"
            controls-position="right"
            placeholder="请输入成品数量"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="unit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="dictionarySubclassCode"
            class="flex-1"
            v-model="formValue.unit"
            placeholder="请选择计量单位"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="检验结果" prop="isQualified">
          <EnumRadioGroup
            class="flex-1"
            v-model="formValue.isQualified"
            placeholder="请选择检验结果"
            :enum="QualifiedEnum"
            enumName="QualifiedEnum"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="入库日期" prop="storageTime">
          <el-date-picker
            v-model="formValue.storageTime"
            label="入库日期"
            placeholder="请选择入库日期"
            class="flex-1"
            :disabled-date="disabledNowAfterDate"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发货状态" prop="shipmentStatus">
          <EnumRadioGroup
            class="flex-1"
            v-model="formValue.shipmentStatus"
            placeholder="请选择发货状态"
            :enum="ShipmentStatusEnum"
            enumName="ShipmentStatusEnum"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="发货日期"
          prop="shipmentTime"
          :rules="{
            required: shipmentTimeRequired,
            message: '发货日期不能为空',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formValue.shipmentTime"
            label="发货日期"
            placeholder="请选择发货日期"
            class="flex-1"
            :disabled-date="disabledNowAfterDate"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item class="flex-1 gap-y-10" label="备注" prop="remark">
          <el-input
            v-model="formValue.remark"
            placeholder="请输入备注"
            type="textarea"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import EnumSelect from "@/components/EnumSelect";
import Dictionary from "@/components/Dictionary";
import EnumRadioGroup from "@/components/EnumRadioGroup/index";
import { computed, onMounted, reactive, ref, watch, watchEffect } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ICreateFinishedProductStorage } from "@/models";
import { QualifiedEnum, VoltageClassesEnum, ShipmentStatusEnum } from "@/enums";
import { useSalesFillInDataStore, useFinishedProductStorageStore } from "@/store/modules";
import { CABLE_TERMINAL_ADAPTER, MEASURE_UNIT } from "@/consts";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { Validators } from "@/utils/form";
import { getSubClassConfig } from "@/utils/material-config/index";

export interface IFinishingWarehousingForm {
  getFormValue: () => ICreateFinishedProductStorage | boolean;
  resetFormValue: () => {};
}

const props = defineProps<{
  mode: "add" | "edit" | "copy";
}>();

const fillInDataStore = useSalesFillInDataStore();
const finishedProductStorageStore = useFinishedProductStorageStore();
const dictionarySubclassCode = computed(() => fillInDataStore.data?.subClassCode || fillInDataStore.data?.subclassCode);
const subClassConfig = getSubClassConfig(dictionarySubclassCode.value);

const ruleFormRef = ref<FormInstance>();
const disabledEdit = ref<boolean>(false);
const shipmentTimeRequired = ref<boolean>(false);
const productBatchNoRequired = ref<boolean>(false);

const formValue = reactive<ICreateFinishedProductStorage>({
  id: undefined,
  productNo: undefined,
  productBatchNo: undefined,
  specificationModel: undefined,
  voltageLevel: undefined,
  amount: undefined,
  unit: undefined,
  storageTime: undefined,
  isQualified: undefined,
  shipmentStatus: undefined,
  shipmentTime: undefined,
  productionId: undefined,
  purchaseId: undefined,
  workOrderId: undefined,
  receiptBatchNo: undefined,
  productName: undefined
});
const rules = reactive<FormRules>({
  productNo: [{ required: true, message: "成品编号不能为空", trigger: "blur" }],
  // productBatchNo: [{ required: true, message: "生产批次号不能为空", trigger: "blur" }],
  specificationModel: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  voltageLevel: [
    {
      required:
        subClassConfig.productionDataFormConfig.productionTestSensingConfig.finishedProductStorage
          .voltageLevelNecessity,
      message: "电压等级不能为空",
      trigger: "change"
    }
  ],
  amount: [
    { message: "数量不能为空", trigger: "change", required: true },
    { message: "数量必须大于0", trigger: "change", validator: Validators.positiveNumber }
  ],
  unit: [{ required: true, message: "计量单位不能为空", trigger: "change" }],
  isQualified: [{ required: true, message: "检验结果不能为空", trigger: "change" }],
  storageTime: [{ required: true, message: "入库日期不能为空", trigger: "change" }],
  shipmentStatus: [{ required: true, message: "发货状态不能为空", trigger: "change" }],
  productName: [{ required: true, message: "产品名称不能为空", trigger: "change" }],
  receiptBatchNo: [{ required: true, message: "入库批次号不能为空", trigger: "change" }]
});

watch(
  () => formValue.shipmentStatus,
  status => {
    shipmentTimeRequired.value = status === ShipmentStatusEnum.SHIPMENT;
    // 清除发货时间检验状态和值
    if (!shipmentTimeRequired.value) {
      ruleFormRef.value?.clearValidate(["shipmentTime"]);
      formValue.shipmentTime = undefined;
    }
  }
);

watchEffect(() => {
  const finishedProductStorageDetail = finishedProductStorageStore.finishedProductStorageDetail;
  if (finishedProductStorageDetail && Object.keys(finishedProductStorageDetail)) {
    Object.assign(formValue, finishedProductStorageDetail);
  }

  // 如果是新增
  if (!finishedProductStorageDetail?.id && props.mode === "add") {
    if (CABLE_TERMINAL_ADAPTER.includes(dictionarySubclassCode.value)) {
      formValue.amount = 1;
    } else {
      formValue.amount = undefined;
    }
  }

  disabledEdit.value = !!finishedProductStorageDetail.id;
});

onMounted(() => {
  productBatchNoRequired.value = CABLE_TERMINAL_ADAPTER.includes(dictionarySubclassCode.value);
});

const clearProductNo = () => {
  formValue.productNo = undefined;
};

const resetFormValue = () => {
  shipmentTimeRequired.value = false;
  ruleFormRef.value?.resetFields();
};

const getFormValue = async (): Promise<boolean | ICreateFinishedProductStorage> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});
  if (!valid) {
    return valid;
  }
  return formValue;
};

defineExpose({
  getFormValue,
  resetFormValue,
  clearProductNo
});
</script>

<style scoped lang="scss"></style>
