export interface IStateGridRateTrigger {
  id: string;
  poItemNo: string;
  soItemNo: string;
  ipoNo: string;
  actualStartDate: string;
  actualFinishDate: string;
  lastUpdateTime: string;
  lastTriggerTime: string;
  syncResult: boolean;
  scoreResult: boolean;
  reason: string;
  purchaseId: string;
  purchaseLineId: string;
  productionId: string;
  dataId: string;
  workOrderId: string;
  woNo: string;
  isCable: boolean;
  materialCode: string;
  materialDesc: string;
  subClassCode: string;
}
