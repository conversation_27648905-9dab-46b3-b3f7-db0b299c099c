<template>
  <Card margin="6px 0 12px" :fontWeight="700" title="采购订单行项目">
    <template #action>
      <SwitchAction v-track="TrackPointKey.FORM_PURCHASE_SALES_VIEW_CHANGE" />
    </template>
    <div v-show="cardVisible" class="card-list flex-1 overflow-hidden">
      <el-scrollbar>
        <div
          v-for="item in store.lines"
          :key="item.id"
          class="card-item"
          :class="{ 'item-active': isActive(item) }"
          :style="{ borderColor: item.color }"
          @click="select(item)"
        >
          <div class="w-full flex-wrap flex-bc">
            <div class="item-left flex-1">
              <div class="w-1/4 item-text">
                <span class="icon">购</span>
                <ShowTooltip className="text-sm max-w-[100px]" :content="item.poItemNo" />
              </div>
              <div class="w-1/3 item-text">
                <span class="icon">料</span>
                <ShowTooltip className="text-sm max-w-[120px]" :content="item.materialCode" />
              </div>
              <div class="w-1/3 item-text">
                <span class="icon">
                  <IconifyIconOffline :icon="BarChat" />
                </span>
                <span class="text">× {{ item.amount }}</span>
              </div>
            </div>
            <div class="item-right w-[120px]">
              <CxTag v-if="item.linkedCount" type="success" size="small">{{ `已关联 ${item.linkedCount}` }}</CxTag>
              <CxTag v-else type="info" size="small">{{ `未关联` }}</CxTag>
            </div>
          </div>
          <div>
            <span class="item-title font-medium" @click="openDialog(item)">{{ item.materialDesc }}</span>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <pure-table
      v-show="!cardVisible"
      class="flex-1 overflow-hidden"
      show-overflow-tooltip
      :columns="columns"
      :data="store.lines"
      row-key="id"
      :row-class-name="({ row }) => (isActive(row) ? 'item-active' : '')"
      @row-click="select"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
    <PurchaseOrderLineDetailDialog v-model="dialogVisible" :line="activeLine" />
  </Card>
</template>

<script setup lang="ts">
import Card from "../card.vue";
import SwitchAction from "../switch-action.vue";
import BarChat from "@iconify-icons/ri/bar-chart-fill";
import { PureTable, TableColumns } from "@pureadmin/table";
import { computed, h, ref } from "vue";
import PurchaseOrderLineDetailDialog from "./purchase-order-line-detail-dialog.vue";
import { IPurchaseOrderLine } from "@/models";
import { usePurchaseOrderDetailPurchaseOrderLineStore } from "@/store/modules/purchase-order-detail";
import { TableWidth, ToggleStyleEnum } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import ShowTooltip from "@/components/ShowTooltip";
import { TrackPointKey } from "@/consts";
import { useTrack } from "@/utils/useTrack";

const store = usePurchaseOrderDetailPurchaseOrderLineStore();
const { sendUserOperation } = useTrack();
const dialogVisible = ref(false);
const activeLine = ref<IPurchaseOrderLine>();

const cardVisible = computed(() => store.toggleStyle === ToggleStyleEnum.MENU);

const columns: Array<TableColumns> = [
  {
    label: "采购订单行项目号",
    prop: "poItemNo",
    minWidth: TableWidth.order,
    cellRenderer(data) {
      return h(
        "a",
        {
          class: "text-primary",
          onClick: () => openDialog(data.row)
        },
        data.row.poItemNo
      );
    }
  },
  {
    label: "采购订单行项目ID",
    prop: "poItemId",
    width: TableWidth.order
  },
  {
    label: "物料编码",
    prop: "materialCode",
    width: TableWidth.order
  },
  {
    label: "物料描述",
    prop: "materialDesc",
    minWidth: TableWidth.name
  },
  {
    label: "采购数量",
    prop: "amount",
    width: TableWidth.number,
    align: "center"
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "关联销售行状态",
    prop: "linkedCount",
    width: TableWidth.type,
    fixed: "right",
    cellRenderer(data) {
      const linkedCount: boolean = data.row.linkedCount;
      if (linkedCount) {
        return h(CxTag, { type: "success" }, () => `已关联${linkedCount}条`);
      }
      return h(CxTag, { type: "warning" }, () => "未关联");
    }
  }
];

function isActive(line: IPurchaseOrderLine): boolean {
  return store.activePurchaseOrderLineNo === line.poItemNo;
}

function openDialog(line: IPurchaseOrderLine) {
  dialogVisible.value = true;
  activeLine.value = line;
  sendShowDetailOperation();
}

function select(purchaseOrderLine: IPurchaseOrderLine) {
  store.setActivePurchaseOrderLine(purchaseOrderLine.poItemNo);
}

function sendShowDetailOperation() {
  sendUserOperation(TrackPointKey.FORM_PURCHASE_LINE_SALES_DETAIL);
}
</script>

<style scoped lang="scss">
.card-item {
  padding: 12px;
  border-radius: 3px;
  border-width: 0 4px 0 0;
  border-color: transparent;
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 2px 2px 12px 2px;

  .item-left {
    @apply flex gap-4;
  }

  .item-right {
    @apply text-right;
  }

  .item-title {
    @apply inline-block leading-5 text-base cursor-pointer;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .item-text {
    @apply flex items-center text-secondary;

    .icon {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      width: 16px;
      height: 16px;
      border-radius: 1px;
      margin-right: 4px;
      color: var(--el-color-primary-light-3);
      background: var(--el-color-primary-light-9);
      border: 0.5px solid var(--el-color-primary-light-7);
    }

    .text {
      @apply text-sm;
    }
  }
}

.item-active {
  background: var(--el-color-warning-light-9);

  .item-title {
    // color: var(--el-color-primary);
  }
}

.pure-table {
  :deep(.item-active) {
    --el-bg-color: var(--el-color-warning-light-9);
    background-color: var(--el-color-warning-light-9);
  }
}

@media screen and (max-width: 1439px) {
  .card-item {
    flex-direction: column;

    .item-right {
      @apply text-left flex-row items-center gap-1 w-auto;
    }
  }
}
</style>
