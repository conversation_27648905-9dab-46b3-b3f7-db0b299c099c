<template>
  <div class="flex rounded item-container" v-bind="$attrs">
    <SyncStatus :success="syncResult" />
    <div class="flex flex-col flex-1 px-6 py-3 bg-bg_color">
      <div class="flex items-center mb-2.5">
        <Description :sync="sync" class="flex-1" />
        <div class="flex flex-col text-right gap-2">
          <el-badge :value="errorCount" :hidden="!errorCount">
            <el-button @click="openDetailDialog" v-track="TrackPointKey.FORM_PURCHASE_SYNC_BTN_DETAIL"
              >同步详情</el-button
            >
          </el-badge>
          <el-tooltip class="hidden" content="仅同步全部未同步、同步失败的数据">
            <el-button
              v-auth="PermissionKey.form.formPurchaseSyncBtnOneKey"
              v-track="TrackPointKey.FORM_PURCHASE_SYNC_BTN_ONE_KEY"
              type="primary"
              @click="showSyncAuditDialog"
            >
              一键同步
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <Steps :steps="steps" />
    </div>
  </div>
  <el-dialog
    title="同步确认"
    v-model="syncAuditVisible"
    :close-on-press-escape="false"
    destroy-on-close
    fullscreen
    class="sync-audit-full-screen"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" class="flex justify-between">
        <div class="flex gap-3 items-center">
          <el-button link @click="close">
            <el-icon size="20"><Back /></el-icon>
          </el-button>
          <div :class="titleClass">同步确认</div>
        </div>
        <el-button type="danger" @click="close">
          <el-icon size="20"><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <SyncAudit
      :base-info="syncAuditBaseInfo"
      :business-audit-items="BUSINESS_CARD_IDS"
      :iot-audit-items="IOT_STATE_GRID_CARD_IDS"
      allow-filter-by-order-no
      ref="syncAudit"
    />
    <template #footer>
      <el-button @click="syncAuditVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSyncAll" :loading="loading">确认同步</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SyncStatus from "@/views/order/purchase-order/detail/src/components/sync-status.vue";
import Description from "@/views/order/purchase-order/detail/src/sg-order/list/description.vue";
import Steps from "@/views/order/purchase-order/detail/src/sg-order/list/steps.vue";
import { IIOTStateGridOrderSync, IStateGridOrderSync, IStateGridSyncAuditBaseInfo } from "@/models";
import { computed, ref } from "vue";
import { formatSyncSteps } from "@/views/order/purchase-order/detail/src/sg-order/sync-step-tool";
import {
  usePurchaseOrderDetailStore,
  useStateGridOrderSyncDetailStore,
  useStateGridOrderSyncListStore
} from "@/store/modules";
import { ElMessage, ElNotification } from "element-plus";
import { PermissionKey, TrackPointKey } from "@/consts";
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { BUSINESS_CARD_IDS, IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";
import { useConfirm } from "@/utils/useConfirm";
import { PurchaseChannel, OrderType } from "@/enums";
import { Close, Back } from "@element-plus/icons-vue";

const props = defineProps<{
  sync?: IStateGridOrderSync;
  iotSyncs?: IIOTStateGridOrderSync;
}>();

const store = useStateGridOrderSyncListStore();
const detailStore = useStateGridOrderSyncDetailStore();
const purchaseDetailStore = usePurchaseOrderDetailStore();
const syncAuditVisible = ref(false);
const syncAuditBaseInfo = ref<IStateGridSyncAuditBaseInfo>();
const syncAudit = ref<InstanceType<typeof SyncAudit>>();
const loading = ref(false);
const handleSyncAll = useLoadingFn(syncAll, loading);
const handleSyncIotAll = useLoadingFn(syncIotAll, loading);

const sync = computed(() => (detailStore.channel === PurchaseChannel.EIP ? props.sync : props.iotSyncs));
const syncResult = computed(() => {
  return detailStore.channel === PurchaseChannel.EIP ? props.sync.syncResult : props.iotSyncs.synType;
});
const steps = computed(() => formatSyncSteps(sync.value.detail));
const errorCount = computed(() => steps.value.reduce((prev, cur) => prev + (cur.errorCount ?? 0), 0));

function openDetailDialog() {
  if (detailStore.channel === PurchaseChannel.EIP) {
    detailStore.openDialog(sync.value);
  } else if (detailStore.channel === PurchaseChannel.CSG_GuangZhou) {
    detailStore.openCsgGuangzhouDialog(sync.value as IIOTStateGridOrderSync);
  } else {
    detailStore.openIOTDialog(sync.value as IIOTStateGridOrderSync);
  }
  detailStore.subClassCode = sync.value.subClassCode;
}

async function showSyncAuditDialog() {
  if (detailStore.channel === PurchaseChannel.EIP) {
    syncAuditBaseInfo.value = {
      isCable: purchaseDetailStore.isCable,
      isArmourClamp: purchaseDetailStore.isArmourClamp,
      subClassCode: purchaseDetailStore.purchaseOrder.subClassCode,
      purchaseLineId: sync.value.id,
      orderType: OrderType.PURCHASE,
      orderId: purchaseDetailStore.purchaseOrderId,
      ...sync.value
    };
    syncAuditVisible.value = true;
  } else {
    const channelName = detailStore.channel === PurchaseChannel.CSG_GuangZhou ? "广州供电局" : "上海平台";
    const text = `同步【${props.iotSyncs.soItemNo}】下所有生产数据到${channelName}`;
    if (!(await useConfirm(text, "确认同步"))) {
      return;
    }
    handleSyncIotAll();
  }
}

async function syncAll() {
  if (syncAudit.value.isEmpty()) {
    ElMessage.warning("当前采购订单行下没有数据可以同步，请先维护相关数据");
    return;
  }
  await store.syncAll(props.sync.id);
  ElNotification.info({
    title: "数据同步",
    message: `正在同步采购订单行项目【${props.sync.poItemNo}】下的相关数据`,
    duration: 3000
  });
  syncAuditVisible.value = false;
}

async function syncIotAll() {
  const { id, salesId } = props.iotSyncs;
  await store.syncAll(id, salesId);
  ElNotification.info({
    title: "数据同步",
    message: `正在同步销售订单行项目【${props.iotSyncs.soItemNo}】下的相关数据`,
    duration: 3000
  });
}
</script>

<style scoped>
.item-container {
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
}
</style>

<style lang="scss">
@import "@/views/order/purchase-order/detail/styles/mixin";

.sync-audit-full-screen {
  @include full-screen-dialog;
}
</style>
