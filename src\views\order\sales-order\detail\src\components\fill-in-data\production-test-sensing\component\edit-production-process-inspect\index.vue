<template>
  <div class="add-edit-process-inspect">
    <AddProductionProcessInspect v-if="isNormal" ref="addProcessInspectRef" />
    <AddAcProductionProcessInspect v-else ref="addAcProcessInspectRef" :isAddOrEdit="isAddOrEdit" />
  </div>
</template>

<script setup lang="ts">
import AddProductionProcessInspect from "../../production-process-inspection/normal/add-production-process-inspec/index.vue";
import AddAcProductionProcessInspect from "../../production-process-inspection/armour-clamp/add-Armour-clamp-inspect/index.vue";
import { ref, computed, onMounted, inject } from "vue";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { useArmourClampProdProcessInspectStore } from "@/store/modules/production-test-sensing";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

// 物资种类判断
const props = withDefaults(
  defineProps<{
    inspectId?: string;
  }>(),
  {
    inspectId: null
  }
);

const addProcessInspectRef = ref<InstanceType<typeof AddProductionProcessInspect>>();
const addAcProcessInspectRef = ref<InstanceType<typeof AddAcProductionProcessInspect>>();
const productionProcessInspecStore = useProductionProcessInspecStore();
const prodProcessInspecStore = useArmourClampProdProcessInspectStore();
const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const isNormal = ref<boolean>(true);

onMounted(() => {
  if (!props.inspectId) return;
  isNormal.value = prodCtx?.detailMaterialCategory === EMaterialCategory.Normal;
  if (isNormal.value) {
    productionProcessInspecStore.getDetailOfProdProcessInfo(props.inspectId);
  } else {
    prodProcessInspecStore.getAcDetailOfProdProcessInfo(props.inspectId);
  }
});

// 编辑还是新增
const isAddOrEdit = computed(() => {
  return !props.inspectId;
});

/**
 * 获取表单数据
 */
const getFormValue = () => {
  if (isNormal.value) {
    return addProcessInspectRef.value?.getProcessInspectionForm();
  } else {
    return addAcProcessInspectRef.value?.getProcessInspectForm();
  }
};

defineExpose({
  getFormValue
});
</script>

<style scoped lang="scss"></style>
