import { defineStore } from "pinia";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { ISalesOrder, ISalesOrderDto } from "@/models";
import * as api from "@/api/sales-order";
import { computed, ref, watch } from "vue";

export const usePurchaseOrderDetailSalesOrderStore = defineStore("cx-purchase-order-detail-sales-order", () => {
  const _purchaseOrderDetailStore = usePurchaseOrderDetailStore();
  const topOrderCount = 3;
  const orders = ref<Array<ISalesOrder>>([]);
  const activeOrder = ref<ISalesOrder>();

  const activeOrderId = computed(() => activeOrder.value?.id);
  const topOrders = computed(() => orders.value.slice(0, topOrderCount));
  const dropdownOrders = computed(() => orders.value.slice(topOrderCount));

  watch(() => _purchaseOrderDetailStore.purchaseOrderId, refreshSalesOrders, { immediate: true });
  watch(activeOrderId, (cur, pre) => {
    if (topOrders.value.some(order => order.id === cur)) {
      return;
    }
    _swapOrder(cur, pre);
  });

  async function getSalesOrderById(id: string) {
    const purchaseOrderId = _purchaseOrderDetailStore.purchaseOrderId;
    return (await api.getSalesOrderById(purchaseOrderId, id)).data;
  }

  async function refreshSalesOrders() {
    const id = _purchaseOrderDetailStore.purchaseOrderId;
    const data = id ? (await api.getSalesOrdersByPurchaseOrderId(id)).data : [];
    orders.value = Array.isArray(data) ? data : [];
    _updateActiveSalesOrder(orders.value);
  }

  function setActiveSalesOrder(id: string) {
    if (!id || activeOrderId.value === id) {
      return;
    }
    activeOrder.value = orders.value.find(order => order.id === id);
  }

  async function deleteSalesOrder(id: string) {
    return api.deleteSalesOrder(id, _purchaseOrderDetailStore.purchaseOrderId);
  }

  async function createSalesOrder(order: ISalesOrderDto) {
    return api.createSalesOrder(order);
  }

  async function createSalesOrderAndLines(order: ISalesOrderDto) {
    return api.createSalesOrderAndLines(order);
  }

  async function updateSalesOrder(order: ISalesOrderDto) {
    return api.updateSalesOrder(order);
  }

  function _updateActiveSalesOrder(orders: Array<ISalesOrder>) {
    let newOrder: ISalesOrder;
    const activeId: string = activeOrder.value?.id;
    if (activeId) {
      newOrder = orders.find(order => order.id === activeId);
    }
    activeOrder.value = newOrder || orders[0];
  }

  function _swapOrder(id1: string, id2: string) {
    const index1: number = orders.value.findIndex(o => o.id === id1);
    const index2: number = orders.value.findIndex(o => o.id === id2);
    if (index1 < 0 || index2 < 0) {
      return;
    }
    [orders.value[index1], orders.value[index2]] = [orders.value[index2], orders.value[index1]];
  }

  return {
    orders,
    topOrders,
    dropdownOrders,
    activeOrder,
    activeOrderId,
    getSalesOrderById,
    refreshSalesOrders,
    setActiveSalesOrder,
    deleteSalesOrder,
    createSalesOrder,
    createSalesOrderAndLines,
    updateSalesOrder
  };
});
