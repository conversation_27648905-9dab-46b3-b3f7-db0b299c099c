import { ISalesOrderLineExt, ISalesOrderLineLinkDto, ISalesOrderLineLinkParams, ISalesOrderLineReq } from "@/models";
import { defineStore } from "pinia";
import * as salesOrderLineService from "@/api";
import { getSalesOrderLinesByPurchaseOrderId } from "@/api";
import { useRowspan } from "@/utils/useRowspan";

export const useSalesOrderLineStore = defineStore({
  id: "cx-sale-order-line-store",
  state: () => ({
    salesOrderLines: [] as Array<ISalesOrderLineExt>,
    total: 0,
    salesOrderLinesProduct: [] as Array<ISalesOrderLineExt>,
    productTotal: 0
  }),
  actions: {
    async querySaleOrderLinesByState(params: ISalesOrderLineReq) {
      const data = (await salesOrderLineService.querySaleOrderLines(params)).data;
      this.salesOrderLines = useRowspan(data, "soNo");
    },
    async queryLinesByLink(params: ISalesOrderLineLinkParams) {
      const data = (await salesOrderLineService.querySaleOrderLinesByPurchaseLine(params)).data;
      this.total = data.total;
      this.salesOrderLines = useRowspan(data.list, "soNo");
    },
    async queryLinesByPurchaseOrderLine(purchaseOrderLineId: string) {
      return getSalesOrderLinesByPurchaseOrderId(purchaseOrderLineId).then(res => res.data);
    },
    async linkSalesOrderLine(data: ISalesOrderLineLinkDto) {
      return salesOrderLineService.linkSalesOrderLine(data);
    },
    async cancelSalesOrderLineLink(purchaseOrderLineId: string, salesOrderLineId: string) {
      return salesOrderLineService.cancelSalesOrderLineLink(purchaseOrderLineId, salesOrderLineId);
    },
    /** 通过销售订单Id获取销售订单行 */
    async querySaleOrderLinesBySaleId(params: ISalesOrderLineReq) {
      const data = (await salesOrderLineService.querySaleOrderLinesMerge(params)).data;
      this.salesOrderLines = useRowspan(data, "soNo");
    }
  }
});
