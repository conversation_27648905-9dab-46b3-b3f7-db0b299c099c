import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IProcessStandarModel, IProcessStandarModelForm, IProcessStandarModelReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryProcessStandarModel = (data: IProcessStandarModelReq) => {
  const url: string = withApiGateway("admin-api/southgrid/models/pageList");
  return http.post<IProcessStandarModelReq, IListResponse<IProcessStandarModel>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getProcessStandarModelById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/models/${id}`);
  return http.get<string, IResponse<IProcessStandarModel>>(url);
};

/** 新增 */
export const createProcessStandarModel = (data: IProcessStandarModelForm) => {
  return http.post<IProcessStandarModelForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/models"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateProcessStandarModel = (data: IProcessStandarModelForm) => {
  return http.put<IProcessStandarModelForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/models/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteProcessStandarModelById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/models/${id}`));
};
