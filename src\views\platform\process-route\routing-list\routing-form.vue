<template>
  <div class="px-5">
    <el-form ref="formRef" :model="formValue" :rules="rules" label-width="110px">
      <el-row>
        <el-col>
          <el-form-item label="工艺路线名称" prop="name">
            <el-input placeholder="请输入工艺路线名称" v-model="formValue.name" clearable />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="工艺路线编码" prop="code">
            <el-input placeholder="请输入工艺路线编码" v-model="formValue.code" clearable />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formValue.status">
              <el-radio :label="true" border>启用</el-radio>
              <el-radio :label="false" border>禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="虚拟订单标识" prop="status">
            <el-switch
              v-model="formValue.virtualOrderFlag"
              :active-value="true"
              :inactive-value="false"
              active-text="启用"
              inactive-text="禁用"
              inline-prompt
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watchEffect, ref } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IProcessRouteForm } from "@/models";
import { useProcessRouteStore } from "@/store/modules";

const processRouteStore = useProcessRouteStore();

const formRef = ref<FormInstance>();
const formValue = reactive<IProcessRouteForm>({
  id: undefined,
  name: undefined,
  code: undefined,
  status: true,
  virtualOrderFlag: false
});
const rules = reactive<FormRules>({
  name: [{ required: true, message: requiredMessage("工艺路线名称"), trigger: "change" }],
  code: [{ required: true, message: requiredMessage("工艺路线编码"), trigger: "change" }]
});

watchEffect(() => {
  if (processRouteStore.processRouteForm && Object.keys(processRouteStore.processRouteForm).length) {
    Object.assign(formValue, processRouteStore.processRouteForm);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getFormValue(): Promise<IProcessRouteForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return formValue;
}

defineExpose({
  getFormValue
});
</script>

<style scoped></style>
