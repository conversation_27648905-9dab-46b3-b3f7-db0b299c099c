import {
  delEmphasisRawMaterilaById,
  getDetailAddEmphasisRawMaterial,
  getEmphasisRawMaterialList,
  getEmphasisRawMaterilKinds,
  saveAddEmphasisRawMaterial
} from "@/api/stock-data-center/emphasis-raw-material";
import { MAIN_MATERIAL_TYPE, dateFormat } from "@/consts";
import { IDictionaryOption, IResponse } from "@/models";
import {
  IEmphasisRawmaterialList,
  IEmphasisRawmaterialReq,
  IKeyRawMaterialOptions,
  ISearchEmphasisRawMaterial
} from "@/models/stock-data-center/i-emphasis-raw-material";
import { formatDate } from "@/utils/format";
import { defineStore } from "pinia";
import * as api from "@/api/platform/dictionary";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

export const useEmphasisRawMaterialStore = defineStore({
  id: "emphasis-rawMaterial-store",
  state: () => ({
    rawMaterialKindOptions: [] as Array<IKeyRawMaterialOptions>,
    allRawMaterialKindOptions: [] as Array<IDictionaryOption>,
    emphasisRawMaterialTableData: [] as Array<IEmphasisRawmaterialList>,
    queryParams: {
      pageNo: 1,
      pageSize: 20
    } as ISearchEmphasisRawMaterial,
    tableTotal: 0,
    editDetailRawMaterialInfo: {} as IEmphasisRawmaterialList,
    detailRawMaterialBaseInfo: {} as IEmphasisRawmaterialList
  }),
  actions: {
    /**
     * 获取重点原材料原材料类型下拉框数据
     */
    async getRawMaterialKindOptions() {
      const res = await api.getTenantDictionaryOptions({ parentCode: MAIN_MATERIAL_TYPE });
      this.allRawMaterialKindOptions = res.data?.length ? res.data : [];
    },

    /**
     * 根据选择的物资种类获取重点原材料原材料类型下拉框数据
     */
    async getRawMaterialKindOptionsByCategoryCode(paramsData?: { categoryCode?: string; subClassCode?: string }) {
      const kindRes = await getEmphasisRawMaterilKinds(paramsData);
      this.rawMaterialKindOptions = kindRes.data?.length ? kindRes.data : [];
    },

    /**
     * 查询重点原材料
     */
    queryEmphasisRawMaterial(queryParams: ISearchEmphasisRawMaterial) {
      this.queryParams = { ...this.queryParams, ...queryParams };
      this.getEmphasisRawMaterialListAction();
    },

    /**
     * 获取重点原材料的数据
     */
    async getEmphasisRawMaterialListAction() {
      const res = await getEmphasisRawMaterialList(omitBy(this.queryParams, val => isAllEmpty(val)));
      if (Array.isArray(res.data?.list) && res.data.list?.length) {
        this.emphasisRawMaterialTableData = res.data.list;
        this.tableTotal = res.data?.total;
      } else {
        this.emphasisRawMaterialTableData = [];
      }
    },

    /**
     * 保存原材料的数据
     */
    async saveEmphasisRawMaterialAction(paramsData: IEmphasisRawmaterialReq) {
      return await saveAddEmphasisRawMaterial(paramsData);
    },

    /**
     * 获取详情的数据
     */
    async getDetailEmphasisRawMaterialAction(id: string) {
      const detailRes = await getDetailAddEmphasisRawMaterial(id);
      this.detailRawMaterialBaseInfo = detailRes.data || {};
      this.detailRawMaterialBaseInfo.storageTime = formatDate(detailRes.data?.storageTime, dateFormat);
      this.detailRawMaterialBaseInfo.inventoryTime = formatDate(detailRes.data?.inventoryTime, dateFormat);
      this.editDetailRawMaterialInfo = detailRes.data;
    },

    /**
     * 删除重点原材料
     */
    async delEmphasisRawMaterialAction(id: string): Promise<IResponse<boolean>> {
      return await delEmphasisRawMaterilaById(id);
    },

    /**
     * 根据页码数量改变查询重点原材料
     */
    queryEmphasisRawMaterialListByPageSize(pageSize: number) {
      this.queryParams.pageSize = pageSize;
      this.queryParams.pageNo = 1;
      this.getEmphasisRawMaterialListAction();
    },

    /**
     * 根据页码改变查询重点原材料数据
     */
    queryEmphasisRawMaterialListByPageNo(pageNo: number) {
      this.queryParams.pageNo = pageNo;
      this.getEmphasisRawMaterialListAction();
    },

    /**
     * 重置数据状态
     */
    setDetailEmphasisRawMaterial() {
      this.editDetailRawMaterialInfo = {};
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
    },
    initQueryData() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
      this.emphasisRawMaterialTableData = [];
    }
  }
});
