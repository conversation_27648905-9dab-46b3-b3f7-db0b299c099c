<template>
  <div class="inspec-check-item">
    <DynamicForm
      v-if="resFormData.length"
      ref="dynamicTableFormRef"
      :dynamic-form-data="resFormData"
      :edit-mode="true"
      @formControlValueChange="formControlValueChange"
    />
  </div>
</template>

<script setup lang="ts">
import DynamicForm from "@/components/DynamicForm";
import { useRawMaterialCheckInfoHook } from "@/views/config/raw-material/hook/useRawMaterialCheck";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { ref, watchEffect } from "vue";
import { EControlType } from "@/enums";
import { IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";

// store 数据
const productionProcessInspecStore = useProductionProcessInspecStore();

const dynamicTableFormRef = ref();
// 原材料检测项数据
const { getRawMaterialCheckFormData } = useRawMaterialCheckInfoHook();
const resFormData = ref([]);

watchEffect(() => {
  resFormData.value = getRawMaterialCheckFormData(productionProcessInspecStore.processCheckCollectionItem);
  if (resFormData.value) {
    // 编辑的时候重置二次硫化时间/温度的必填项
    const rawTypeData = resFormData.value?.find(data => data.targetCode === "rawType");
    if (rawTypeData?.id) {
      const key = rawTypeData.dataTypeIdentityDetail?.identityCode;
      if (key === EControlType.SelectControl || key === EControlType.RadioControl) {
        const value = rawTypeData.targetValue;
        handelCollectionRequired(resFormData.value, value);
      }
    }
  }
});

/**
 * 根据材料类型表单选择的值，重置 二次硫化时间和二次硫化温度 的必填项
 */
const formControlValueChange = rowData => {
  const processCode =
    productionProcessInspecStore.currentTagInfo?.processCode ||
    productionProcessInspecStore.detailProductionCheckInfo?.processCode;
  if (!Object.keys(rowData).length) return;
  const { key, value, config } = rowData;
  // 硫化
  if (processCode && processCode === "LH") {
    // 是选择框并且是材料类型
    if ((key === EControlType.SelectControl || key === EControlType.RadioControl) && config.targetCode === "rawType") {
      handelCollectionRequired(productionProcessInspecStore.processCheckCollectionItem, value);
    }
  }
};

function handelCollectionRequired(collectionItems: IRawMaterialCheckCollectionItem[], value: string) {
  // 选择的值是 三元乙丙橡胶
  if (value === "SYYBXJ") {
    collectionItems.forEach(item => {
      // 二次硫化时间和二次硫化温度
      if (item.targetCode === "ECLHSJ" || item.targetCode === "ECLHWD") {
        item.required = false;
      }
    });
  } else if (value === "GXJ") {
    collectionItems.forEach(item => {
      // 二次硫化时间和二次硫化温度
      if (item.targetCode === "ECLHSJ" || item.targetCode === "ECLHWD") {
        item.required = true;
      }
    });
  }
}

// 验证表单信息
const validProcessCheckInfo = async () => {
  const dynamicFormIns = dynamicTableFormRef.value;
  const formData = await dynamicFormIns?.payloadFormData();
  // 验证信息
  if (formData) {
    return {
      valid: true,
      formData
    };
  }
  return {
    valid: false,
    formData
  };
};

// 导出的数据
defineExpose({
  validProcessCheckInfo
});
</script>

<style scoped lang="scss">
.inspec-check-item {
  min-height: 140px;
}
</style>
