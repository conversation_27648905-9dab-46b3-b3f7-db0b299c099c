import { QMXExperimentResultsEnum, QMXExperimentStatusEnum } from "@/enums";
import { IRawMaterialCheckCollectionItem } from "../../raw-material/i-raw-material-res";
import { IPagingReq } from "@/models/i-paging-req";

/**
 * 非局放耐压
 */
export interface IOutGoingFactoryNotJfnyReq extends IPagingReq {
  productionId?: string;
  processId?: string;
  workOrderId?: string;
}

/** 气密性模型 */
export interface IOutGoingFactoryQmxExperiment {
  id: string;
  /** 试验编号 */
  experimentNo: string;

  /**成品编号  */
  finProNo: string;
  /** 需求数量 */
  account: number;
  /** 需求单位 */
  measUnit: string;

  /** 盘号 */
  reelNo?: string;
  /** 线芯名称 */
  wireName?: string;
  /** 开始时间 */
  startTime: Date;

  /** 结束时间 */
  endTime: Date;

  /** 试验状态? */
  experimentStatus: QMXExperimentStatusEnum;

  /** 试验结果? */
  experimentResult: QMXExperimentResultsEnum;

  /** 产品名称 */
  productName?: string;
  /** 产品型号 */
  model?: string;
  /** 生产批次号 */
  productBatchNo?: string;
  /** 抽检批次号 */
  inspectBatchNo?: string;
  /** 样品编号 */
  sampleNo?: string;
  /** 检验时间 */
  inspectDate?: string;
  /** 审核人员 */
  inspectOperate?: string;
  /** 审核人员 */
  auditor?: string;
  /** 备注 */
  remark?: string;

  /** 原材料检信息 */
  rawMetadataValue?: Array<IRawMaterialCheckCollectionItem>;

  /** 波形图配置 */
  waveFormConfig?: Array<IWaveFormItem>;

  /** 试验时间 */
  testTime?: string;
}

export interface IWaveFormItem {
  name: string;
  valueType: number;
  display: string;
}
