<template>
  <div class="mb-5">
    <TitleBar title="基本信息" class="mb-3" />
    <ProcessInspecBaseInfo
      ref="processInspecRef"
      :subClassCode="fillInDataStore.data.subClassCode || fillInDataStore.data.subclassCode"
      :processId="productionProcessInspecStore.currentTagInfo?.processId"
    />
  </div>
  <div>
    <TitleBar title="过程检检测" class="mb-3" />
    <ProcessInspecCheck ref="dynamicTableFormRef" />
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import ProcessInspecBaseInfo from "./process-inspec-info/index.vue";
import ProcessInspecCheck from "./process-inspec-check/index.vue";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { ref } from "vue";
import { useDeviceStore } from "@/store/modules/device";
import { useSalesFillInDataStore } from "@/store/modules/fill-in-data/sales-fill-in-data";
import { IProductOrder } from "@/models";

const fillInDataStore = useSalesFillInDataStore();
// 新增时获取生产工艺的检测项目信息
const productionProcessInspecStore = useProductionProcessInspecStore();
productionProcessInspecStore.getProductionProcessCheckInfo(
  productionProcessInspecStore.currentTagInfo?.processId,
  (fillInDataStore.data as IProductOrder).specificationModel || fillInDataStore.data.specificationType
);

const deviceStore = useDeviceStore();
deviceStore.queryDeviceList();

// 保存数据
const processInspecRef = ref();
const dynamicTableFormRef = ref();
const getProcessInspectionForm = async () => {
  // 基本信息
  const vaildProcForm = await processInspecRef.value.vaildProcessForm();
  // 过程检测信息
  const vaildDynamicForm = await dynamicTableFormRef.value.validProcessCheckInfo();
  const { valid, formData } = vaildDynamicForm;
  if (vaildProcForm && valid) {
    const baseInfo = processInspecRef.value.form;
    const {
      checkTime,
      devicedId: deviceId,
      devicedName: deviceName,
      devicedCode: deviceCode,
      deviceIds,
      code
    } = baseInfo;
    const processCheckInfo = (formData?.submitData || []).map(item => {
      const { targetCode, targetValue } = item;
      const { identityCode } = item.dataTypeIdentityDetail;
      return {
        dataCode: targetCode,
        identityCode: identityCode,
        dataValue: targetValue
      };
    });
    // 保存数据
    productionProcessInspecStore.saveFormData = {
      code,
      deviceId,
      deviceName,
      deviceCode,
      checkTime,
      deviceIds,
      processDetailDataValue: processCheckInfo
    };
    return productionProcessInspecStore.saveFormData;
  } else {
    return Promise.reject();
  }
};

const refreshNo = async () => {
  await processInspecRef.value.refreshNo();
};

defineExpose({
  getProcessInspectionForm,
  refreshNo
});
</script>

<style scoped lang="scss"></style>
