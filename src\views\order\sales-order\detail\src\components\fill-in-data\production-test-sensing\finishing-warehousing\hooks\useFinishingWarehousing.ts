import { emptyDefaultValue } from "@/consts";
import { ColumnWidth, QualifiedEnum, ShipmentStatusEnum, TableWidth, VoltageClassesEnum } from "@/enums";
import { IFinishedProductStorage } from "@/models";
import { formatDate, formatEnum } from "@/utils/format";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { inject } from "vue";

/** 公共数据或方法 */
export function useFinishedWarehousingHook() {
  const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);

  const armourClampColumns = [
    {
      label: "产品名称",
      prop: "productName"
    }
  ];

  const normalColumns = [
    {
      type: "selection",
      width: TableWidth.check
    },
    {
      label: "成品编号",
      prop: "productNo"
    },
    {
      label: "生产批次号",
      prop: "productBatchNo"
    }
  ];

  const columns = [
    ...(prodCtx?.isArmourClamp ? armourClampColumns : normalColumns),
    {
      label: "规格型号",
      prop: "specificationModel"
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      formatter: (row: IFinishedProductStorage) =>
        formatEnum(row.voltageLevel, VoltageClassesEnum, "voltageClassesEnum")
    },
    {
      label: "数量",
      prop: "amount",
      align: "right",
      formatter: (row: IFinishedProductStorage) =>
        row.amount ? `${row.amount} ${row?.unitDictionary?.name || ""}` : emptyDefaultValue
    },
    {
      label: "入库日期",
      prop: "storageTime",
      formatter: (row: IFinishedProductStorage) => formatDate(row.storageTime)
    },
    {
      label: "检验结果",
      prop: "isQualified",
      formatter: (row: IFinishedProductStorage) => formatEnum(row.isQualified, QualifiedEnum, "QualifiedEnum")
    },
    {
      label: "发货状态",
      prop: "shipmentStatus",
      formatter: (row: IFinishedProductStorage) =>
        formatEnum(row.shipmentStatus, ShipmentStatusEnum, "ShipmentStatusEnum")
    },
    {
      label: "发货时间",
      prop: "shipmentTime",
      formatter: (row: IFinishedProductStorage) => formatDate(row.shipmentTime)
    },
    {
      label: "备注",
      prop: "remark",
      formatter: (row: IFinishedProductStorage) => row.remark
    },
    {
      label: "操作",
      prop: "operation",
      width: ColumnWidth.Char10,
      fixed: "right",
      slot: "operation"
    }
  ];

  return { columns };
}
