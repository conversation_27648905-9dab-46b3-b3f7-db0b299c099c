import { IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IAttachDocumentRes, ISaveDocumentReq } from "@/models/attached-report";

/** 查询工序文档列表 */
export const getProcessDocumentList = (id: string) => {
  const url = withApiGateway(`admin-api/business/process/document/list/${id}`);
  return http.get<void, IResponse<Array<IAttachDocumentRes>>>(url);
};

/** 新增工序文档 编辑工序文档*/
export const saveProcessDocument = (params: ISaveDocumentReq) => {
  const url = withApiGateway(`admin-api/business/process/document/save`);
  return http.post<ISaveDocumentReq, IResponse<boolean>>(url, { data: params });
};

/** 删除工序文档 */
export const delProcessDocument = (id: string) => {
  const url = withApiGateway(`admin-api/business/process/document/delete/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
};
