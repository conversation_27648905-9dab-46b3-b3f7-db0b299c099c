import { ContractTypeEnum, PurchaseChannel, SalesLinkStepEnum, SalesOrderStatus, VoltageClassesEnum } from "@/enums";
import { IPagingReq, IPurchaseOrderReq, ISortReq } from "@/models";

export interface ISalesOrder {
  id: string;
  /** 销售订单号 **/
  soNo: string;
  /** 采购订单号 **/
  poNo: string;

  /** 采购订单ID */
  purchaseIds: string[];

  /** 品类 No **/
  categoryCode: string;
  /** 品类名称 **/
  categoryName: string;
  /** 项目名称 **/
  prjName: string;
  /** 采购方公司名称 **/
  buyerName: string;
  /** 合同编号 **/
  conCode: string;
  /** 交货日期 **/
  deliveryDate: string;
  /** 订单进度 **/
  orderProgress: SalesOrderStatus;
  /** 备注 **/
  remark: string;
  /** 是否关注 **/
  follow: boolean;
  /**当前填报环节 */
  linkStep: SalesLinkStepEnum;
  /**同步结果 */
  synType: boolean;
  /** 同步标识 */
  matSyncFlagId?: string;
  /** 创建时间 */
  createTime: string;
  /** 合同签订日期 暂无使用**/
  sellerSignTime?: string;

  /**物资种类code */
  subClassCode: string;
  /**物资种类名称 */
  subClassName: string;

  /** 销售订单归档 true: 归档 */
  documentation?: boolean;

  /** 同步--采购订单行数量 */
  purchaseLineCount?: number;
  /** 同步--销售订单行数量 */
  saleLineCount?: number;
  /** 是否同步到iot */
  syncIotFlag?: boolean;

  /** 同步采购订单号详情 */
  purchaseList?: Array<ISalesLinkPurchaseOrder>;
}

export interface ISalesOrderParams extends IPagingReq, ISortReq {
  /** 模糊查询 包括：销售订单号/采购订单号/采购方公司名称/项目名称/备注*/
  orFilters?: Array<Record<string, string>>;
  /** 品类 **/
  categoryCode?: string;
  /** 同步标识  **/
  matSyncFlagId?: string;
  /** 订单进度  **/
  orderProgress?: SalesOrderStatus;
  /** 创建时间 */
  createTime?: Array<string>;
  /** 采购订单是否缺失*/
  isFaultPoNo?: boolean;
  /** 仅看已关注*/
  follow?: boolean;
  /** 步骤*/
  linkStep?: SalesLinkStepEnum;
  /** 是否归档 */
  documentation?: boolean;
}

export type ISalesOrderDetail = Pick<
  ISalesOrder,
  | "id"
  | "soNo"
  | "categoryCode"
  | "categoryName"
  | "conCode"
  | "prjName"
  | "orderProgress"
  | "deliveryDate"
  | "buyerName"
  | "remark"
  | "categoryName"
  | "purchaseList"
> & {
  poNo?: Array<string> | string;
  /** 同步标识 */
  matSyncFlagIdList: Array<string>;
};

export interface ISalesLinkPurchaseOrder {
  // 关联采购订单号
  poNo?: string;
  // 关联采购订单ID
  id?: string;
  // 项目名称
  prjName?: string;
  // 采购方公司名称
  buyerName?: string;
  /** */
  syncChannel?: PurchaseChannel;
}
export type ISalesLinkPurchaseOrderParams = Pick<IPurchaseOrderReq, "orFilters" | "pageNo" | "pageSize"> & {
  categoryCode?: string;
  poNo?: Array<string>;
  channel?: PurchaseChannel;
};

export type IStepValue = {
  /** 维护销售订单行 */
  salesLine?: number;

  /** 制定排产计划 */
  productionPlan?: number;

  /** 填报生产订单 */
  productionOrder?: number;

  /** 填报生产数据 */
  productionData?: number;

  /** 同步订单数据 */
  syncOrder?: number;

  /**触发质量评分 */
  qualityEval?: number;
} & Record<string, string | number>;

export interface ISalesOrderLineLinkPurchase {
  /**销售订单行数据主键ID */
  id: string;
  /**销售订单ID */
  salesId: string;
  /**销售订单行项目号 */
  soItemNo: string;
  /**项目号 */
  prjCode?: string;
  /**采购订单行项目号 */
  poItemNos: string;
  /**采购订单行ID */
  poItemIds: string;
  /**品类code */
  categoryCode: string;
  /**物资种类code */
  subClassCode: string;
  /**物资种类名称 */
  subClassName: string;
  /**物料名称 */
  materialName: string;
  /**物料编码 */
  materialCode: string;
  /**物料描述 */
  materialDesc: string;
  /**物料单位 */
  materialUnit: string;
  /**物料单位名称 */
  materialUnitName: string;
  /**物料数量 */
  materialNumber: number;
  /**物料ID */
  materialId: string;
  /**电压等级 */
  voltageLevel?: VoltageClassesEnum;
  /**规格型号 */
  specificationType?: string;
  /**是否生产 */
  isProduction: boolean;
  /**生产订单号 */
  ipoNo: string;
  /**有无工单 */
  isWork: boolean;
  /**销售订单号 */
  soNo: string;
  /**是否排产 */
  schduled: boolean;
  /**采购方公司名称 */
  buyerName?: string;
  /** 生产工号 */
  productionWorkNo?: string;
  /** 实物Id */
  entityId?: string;
}

export interface ISalesOrderLineParams extends IPagingReq {
  /**销售订单ID */
  salesId: string;
  /**采购订单行号是否缺失 */
  isFaultPoItem?: boolean;
}

export type ISalesOrderLineDetail = Pick<
  ISalesOrderLineLinkPurchase,
  | "id"
  | "soItemNo"
  | "subClassCode"
  | "subClassName"
  | "materialId"
  | "materialName"
  | "materialCode"
  | "materialDesc"
  | "materialUnit"
  | "materialUnitName"
  | "materialNumber"
  | "specificationType"
  | "voltageLevel"
  | "prjCode"
  | "poItemNos"
  | "poItemIds"
  | "salesId"
  | "soNo"
  | "buyerName"
  | "productionWorkNo"
  | "entityId"
> & {
  purchaseLines?: Array<IPurchaseLines>;
};

/**关联采购订单行号查询条件 */
export interface ISaleLineLinkPurchaseLineParams extends IPagingReq {
  /** 模糊查询 包括：物资编码/物料描述*/
  orFilters?: Array<Record<string, string>>;

  /**物资种类code */
  subClassCode: string;
  /**采购订单行ID */
  purchaseItemId?: Array<string>;
}

/**关联采购订单行号 */
export interface IPurchaseOderLine {
  /**采购订单id */
  purchaseId: string;
  /**采购订单号 */
  poNo: string;
  /**采购订单行项目号 */
  poItemNo: string;
  /** 采购订单行ID */
  poItemId: string;
  /**采购订单行Id */
  id: string;
  /**物资种类code */
  subClassCode: string;
  /**物资种类名称 */
  subClassName: string;
  /**物料描述 */
  materialDesc: string;
  /**合同类型*/
  conType: ContractTypeEnum;
  /**采购数量*/
  amount: number;
  /**物料编码 */
  materialCode: string;
  /**合同名称*/
  conName: string;
}

export interface IPurchaseLines {
  /**采购订单行Id */
  purchaseLineId?: string;
  /**采购订单id */
  purchaseId?: string;
  /**采购订单行项目号 */
  poItemNo?: string;
  /**采购订单号 */
  poNo?: string;
  /** 合同名称 */
  conName?: string;
}
