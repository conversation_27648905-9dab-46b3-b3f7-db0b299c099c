<template>
  <div class="w-full border-box overflow-hidden">
    <pure-table
      ref="tableRef"
      class="flex-1 overflow-hidden"
      row-key="id"
      show-overflow-tooltip
      :data="data"
      :columns="columns"
      border
      :maxHeight="300"
    >
      <template #operate="{ row }">
        <el-button type="danger" link @click="cancelLink(row.id)">取消关联</el-button>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { useColumns } from "./columns";
import { computed } from "vue";
import { ISalesOrderLineExt } from "@/models";

const props = defineProps<{
  modelValue: ISalesOrderLineExt[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: ISalesOrderLineExt[]): void;
}>();

const data = computed({
  get() {
    return props.modelValue || [];
  },
  set(val: ISalesOrderLineExt[]) {
    emit("update:modelValue", val);
  }
});

const { columns } = useColumns();

/** 取消关联 */
function cancelLink(id: string) {
  const index = data.value.findIndex(item => item.id === id);
  data.value.splice(index, 1);
}
</script>

<style scoped lang="scss"></style>
