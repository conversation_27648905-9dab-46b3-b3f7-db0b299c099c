import { ColumnWidth } from "@/enums";
import { ICollectionItem } from "@/models";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const baseColumn: TableColumnList = [
    {
      label: "工序",
      prop: "processName",
      width: ColumnWidth.Char8
    },
    {
      label: "标准采集点",
      prop: "metadataModelName",
      minWidth: ColumnWidth.Char10
    },

    {
      label: "关键采集点",
      prop: "required",
      width: ColumnWidth.Char5,
      formatter: (row: ICollectionItem) => {
        const required = row.required;
        return required ? <CxTag type="success">是</CxTag> : <CxTag type="danger">否</CxTag>;
      }
    },
    {
      label: "计量单位",
      prop: "unit",
      width: ColumnWidth.Char6
    }
  ];

  /** 展示列 */
  const showColumns: TableColumnList = [
    ...baseColumn,
    {
      label: "采集范围",
      prop: "collectRange",
      slot: "collectRange",
      width: ColumnWidth.Char10
    }
  ];

  /** 编辑列 */
  const editColumns: TableColumnList = [
    ...baseColumn,
    {
      label: "下限",
      prop: "minValue",
      slot: "minValue",
      fixed: "right",
      width: ColumnWidth.Char13
    },
    {
      label: "上限",
      prop: "maxValue",
      slot: "maxValue",
      fixed: "right",
      width: ColumnWidth.Char13
    }
  ];
  return { showColumns, editColumns };
}
