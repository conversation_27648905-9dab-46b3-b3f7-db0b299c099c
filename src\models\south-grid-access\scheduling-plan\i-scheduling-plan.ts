import { SyncStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";

export interface ISchedulingPlan extends IBase {
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 排产计划编号
   */
  productionPlanningNo?: string;
  /**
   * 重点阶段编码
   */
  phaseCode?: string;

  /**
   * 车间编码
   */
  workshopCode?: string;
  /**
   * 设备编码
   */
  deviceCode?: string;
  /**
   * 数量
   */
  productionMaterialNumber?: number;
  /**
   * 单位
   */
  productionMaterialUnit?: string;
  /**
   * 计划开始日期
   */
  planStartDate?: string;
  /**
   * 计划完成日期
   */
  planEndDate?: string;
  /**
   * 同步状态
   */
  isSync?: SyncStatusEnum;
}
