import { SupervisionStatusEnum, SyncStatusEnum } from "@/enums";
import { IBase } from "@/models/i-base";

/**
 * 南网监造计划视图对象
 */
export interface Supervision extends IBase {
  /**
   * 平台侧监造计划唯一编码
   */
  supervisionPlanId?: string;

  /**
   * 监造计划编号
   */
  supervisionNo?: string;

  /**
   * 计划制定时间,格式?:yyyy年MM月dd日
   */
  planCreatedDate?: string;

  /**
   * 分省公司
   */
  provinceCompany?: string;

  /**
   * 地市局
   */
  cityCompany?: string;

  /**
   * 物资合同编号
   */
  contractNo?: string;

  /**
   * 是否网省重点工程物资
   */
  isVipProject?: string;

  /**
   * 项目编码
   */
  projectNo?: string;

  /**
   * 项目名称
   */
  projectName?: string;

  /**
   * 品类名称
   */
  categoryName?: string;

  /**
   * 物资品类(品控策略二级)
   */
  secondCategory?: string;

  /**
   * 物资品类(品控策略三级)
   */
  thirdCategory?: string;

  /**
   * 物资名称(品控策略四级)
   */
  fourthCategory?: string;

  /**
   * 物资编码
   */
  materialCode?: string;

  /**
   * 物资名称
   */
  materialName?: string;

  /**
   * 电压等级,单位kV
   */
  voltageClasses?: number;

  /**
   * 监造数量
   */
  supervisionNum?: number;

  /**
   * 计量单位
   */
  unit?: string;

  /**
   * 计划开始监造时间,格式?:yyyy年MM月dd日
   */
  planStartDate?: string;

  /**
   * 计划完成监造时间,格式?:yyyy年MM月dd日
   */
  planEndDate?: string;

  /**
   * 调整后计划完成监造时间,格式?:yyyy年MM月dd日
   */
  modifiedPlanEndDate?: string;

  /**
   * 远程监造下达状态?:20-已下达,30-已撤回
   */
  status?: string;

  /**
   * 监造状态?:10-未开始,20-监造中,30-监造完成
   */
  supervisionStatus?: SupervisionStatusEnum;

  /**
   * 监造计划在平台侧创建时间,格式?:yyyy-MM-dd HH?:mm?:ss
   */
  createdTime?: string;

  /**
   * 是否同步,0-未同步，1-已同步，2-同步失败
   */
  isSync?: SyncStatusEnum;
}
