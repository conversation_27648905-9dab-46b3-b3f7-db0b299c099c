/**
 * @description: 生产工艺及过程检测
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { InspectProcessModel } from "@/models";
/**
 * @description: 生产工艺及过程检测列表
 */
export const InspectProcessInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<InspectProcessModel>>>(
    withApiGateway(`admin-api/ejj/project/process/inspect/list/${id}`)
  );
};

/**
 * @description: 生产工艺及过程检测创建
 */
export const InspectProcessCreateApi = (params: InspectProcessModel) => {
  return http.post<InspectProcessModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/process/inspect`), {
    data: params
  });
};

/**
 * @description: 生产工艺及过程检测编辑
 */
export const InspectProcessEditApi = (params: InspectProcessModel) => {
  return http.put<InspectProcessModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/process/inspect`), {
    data: params
  });
};

/**
 * @description: 生产工艺及过程检测删除
 */
export const InspectProcessDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/process/inspect/${id}`));
};

/**
 * @description: 查看 过程检 信息
 */
export const InspectProcessDetailApi = (id: string) => {
  return http.get<string, IResponse<InspectProcessModel>>(
    withApiGateway(`admin-api/ejj/project/process/inspect/${id}`)
  );
};
