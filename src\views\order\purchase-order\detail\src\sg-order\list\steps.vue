<template>
  <div class="flex gap-[5px]">
    <div v-for="(step, index) in steps" :key="step.title" class="flex-1 flex-ac flex-col relative" :class="step.type">
      <div class="flex-c">
        <div class="line" v-if="index !== steps.length - 1" />
        <div class="relative bg-bg_color px-4">
          <div class="icon flex-c text-sm">
            <SyncStepIcon :type="step.type" />
          </div>
        </div>
      </div>
      <div class="mt-1.5 text-secondary text-base">{{ step.successCount }}/{{ step.totalCount }}</div>
      <div class="whitespace-nowrap text-primaryText text-base text-bold max-w-[6em] lg:max-w-none">
        <ShowTooltip class-name="w-full text-center" :content="step.title" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ISyncStep } from "../sync-step-tool";
import ShowTooltip from "@/components/ShowTooltip";
import SyncStepIcon from "@/views/order/purchase-order/detail/src/sg-order/components/sync-step-icon.vue";

const props = defineProps<{
  steps: Array<ISyncStep>;
}>();

const steps = computed(() => props.steps);
</script>

<style scoped lang="scss">
.icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.success .icon {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
  border: 1px solid var(--el-color-success-light-8);
}

.fail .icon {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
  border: 1px solid var(--el-color-danger-light-8);
}

.info,
.syncing {
  .icon {
    color: var(--disabled-text-color);
    background-color: var(--el-color-info-light-9);
    border: 1px solid var(--el-color-info-light-8);
  }

  .text-base {
    color: var(--disabled-text-color);
  }
}

.line {
  position: absolute;
  height: 1px;
  width: 100%;
  left: 50%;
  background-color: var(--el-border-color);
}
</style>
