import { IAlarmRuleSetting, IResponse } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api";

export const useAlarmRulesStore = defineStore({
  id: "cx-alarm-rules",
  state: () => ({
    alarmRulesSetting: {} as IAlarmRuleSetting
  }),
  actions: {
    // 查询告警规则
    async queryAlarmRules() {
      const res: IResponse<IAlarmRuleSetting> = await api.getAlarmRules();
      this.alarmRulesSetting = res.data;
    },

    // 保存告警规则
    async saveAlarmRules(alarmRulesSetting: IAlarmRuleSetting) {
      const res: IResponse<boolean> = await api.saveAlarmRules(alarmRulesSetting);
      return res.data;
    }
  }
});
