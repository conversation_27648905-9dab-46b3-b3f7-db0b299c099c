import { http } from "@/utils/http";
import { withApiGateway } from "../../util";
import {
  IExperimentJfnyDetail,
  IExperimentJfnyEChartDetail,
  IExperimentUploadFile,
  IJfnyExperimentList,
  IJfnyExperimentListReq,
  IListResponse,
  IResponse,
  IWaitExperimentJfnyListReq
} from "@/models";
import { IUpLoadFile } from "@/models/production-test-sensing/i-common";

/**
 * 获取局放耐压的列表
 * @param productionId
 */
export function getExFactoryJfnyTableData(params: IJfnyExperimentListReq) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny/page`);
  return http.post<IJfnyExperimentListReq, IListResponse<IJfnyExperimentList>>(url, { data: params });
}

/**
 * 局放耐压上传报告
 * @param params
 */
export function upLoadReportOfJfny(params: IExperimentUploadFile) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny/report-upload`);
  return http.post<any, IResponse<IUpLoadFile>>(url, {
    data: { ...params },
    headers: { "Content-type": "multipart/form-data" }
  });
}

/**
 * 根据Id获取局放耐压的详情数据
 * @param id
 */
export function getDetailExperimentJfny(id: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny/detail/${id}`);
  return http.get<void, IResponse<IExperimentJfnyDetail>>(url);
}

/**
 * 根据Id获取局放耐压的详情图表数据
 * @param id
 */
export function getDetailExperimentJfnyCharts(id: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny/detail-data/${id}`);
  return http.get<void, IResponse<IExperimentJfnyEChartDetail>>(url);
}

/**
 * 删除局放耐压的详情数据
 * @param id
 */
export function delExFactoryExperiment(id: string, productionId: string) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/jfny`);
  return http.delete<
    {
      experimentId: string;
      productionId: string;
    },
    IResponse<number>
  >(url, {
    params: {
      experimentId: id,
      productionId
    }
  });
}

/**
 * 获取局放耐压的待关联列表
 */
export function getExperimentLinkList(searchParam?: IWaitExperimentJfnyListReq) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/choose-list/${searchParam?.productionId}`);
  return http.post<IWaitExperimentJfnyListReq, IListResponse<IJfnyExperimentList>>(url, { data: { ...searchParam } });
}

/**
 * 关联局放耐压试验列表
 */
export function linkProductionAndExperiment(productionId: string, experimentIds: string[]) {
  const url = withApiGateway(`admin-api/business/outgoing/experiment/link-production`);
  return http.post<
    {
      productionId: string;
      experimentIds: string[];
    },
    IResponse<boolean>
  >(url, { data: { productionId, experimentIds } });
}

/**
 * 模拟局放耐压试验数据
 */
export function createJfnyData(id: string) {
  const url = withApiGateway(`admin-api/business/outgoing/create/jfny-data/${id}?minute=2`);
  return http.get<void, IResponse<boolean>>(url);
}

export function uploadExperimentWave(data: FormData) {
  const url = withApiGateway("admin-api/business/import/experiment/wave-form/bb");
  return http.post<FormData, IResponse<boolean>>(url, {
    data,
    headers: { "Content-type": "multipart/form-data" }
  });
}
