<template>
  <div class="flex-1 flex flex-col overflow-hidden">
    <div class="w-full flex-bc pt-6 pb-3">
      <SearchBar ref="searchbarRef" @search="search" />
      <div>
        <el-button @click="store.refresh(false)" :loading="store.loading">
          <template #icon>
            <FontIcon icon="icon-refresh" />
          </template>
          刷新
        </el-button>

        <el-tooltip
          v-if="syncInfoStore.activeStepKey === StateGridOrderSyncStep.PROCESS_QUALITY_AUTO_COLLECT"
          content="自动检查没有采集数据和同步报错的工序数据"
        >
          <el-button
            type="primary"
            :loading="syncLoading"
            @click="handleBatchCheckProdData"
            v-auth="PermissionKey.form.formPurchaseProcessCheckProdData"
            v-track="store.syncActionTrackPointKey"
          >
            批量检查生产数据
          </el-button>
        </el-tooltip>

        <select-sync-way @on-confirm="handleSync" class="ml-2">
          <template #default="{ openDialog }">
            <el-button
              type="primary"
              :loading="syncLoading"
              @click="openDialog"
              v-auth="PermissionKey.form.formPurchaseSyncBtnSync"
              v-track="store.syncActionTrackPointKey"
            >
              <template #icon>
                <FontIcon icon="icon-sync" />
              </template>
              {{ store.syncActionName }}
            </el-button>
          </template>
        </select-sync-way>
      </div>
    </div>
    <pure-table
      show-overflow-tooltip
      :loading="store.loading"
      :columns="props.columns"
      :data="store.data"
      :pagination="store.pagination"
      row-key="id"
      @page-current-change="currentPageChange"
      @page-size-change="pageSizeChange"
      class="flex-1 overflow-hidden pagination"
    >
      <template #empty>
        <CxEmpty />
      </template>

      <template v-if="props.type === StateGridOrderSyncType.REPORT_WORK" #report-work-batch-no="{ row }">
        <slot name="report-work-batch-no" :value="row" />
      </template>
    </pure-table>
  </div>
</template>

<script setup lang="ts">
import SearchBar from "./search-bar.vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import { useSalesStateGridOrderSyncDetailListStore, useSalesOrderSyncInfo } from "@/store/modules";
import { onMounted, ref } from "vue";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { PermissionKey } from "@/consts/permission-key";
import { getStateGridOrderSyncTypeTitle, StateGridOrderSyncStep, StateGridOrderSyncType } from "@/enums";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { ISyncSearchParams, SyncOrderBySearchParams } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { checkAutoCollectProdData } from "@/api/data-integrity-check/data-integrity-check";
import selectSyncWay from "./select-sync-way.vue";
import { syncStateGridOrderBySearch } from "@/api/state-grid-order-sync";

const props = defineProps<{
  columns: Array<TableColumns>;
  type?: StateGridOrderSyncType;
}>();

const store = useSalesStateGridOrderSyncDetailListStore();
const syncInfoStore = useSalesOrderSyncInfo();
const searchbarRef = ref<InstanceType<typeof SearchBar>>();
const syncLoading = ref(false);
const handleSync = useLoadingFn(sync, syncLoading);

onMounted(() => store.refresh(false));

async function sync(onCondition?: boolean) {
  // 不根据搜索条件同步
  if (!onCondition) {
    await store.syncStateGridOrder();
  } else {
    // 根据搜索条件同步
    const searchState = searchbarRef.value.getSearchState();
    const params: SyncOrderBySearchParams = Object.assign(
      { dataType: store.type },
      store.getStateGridOrderSyncParams,
      searchState
    );
    if (syncInfoStore.isShanghaiIotChannel || syncInfoStore.isGuangzhouChannel) {
      params.syncProcess = syncInfoStore.activeStepKey;
    }
    await syncStateGridOrderBySearch(params);
  }
  ElNotification.info({
    title: "数据同步",
    message: `正在同步${getTitle()}数据`,
    duration: 3000
  });
}

function getTitle() {
  return getStateGridOrderSyncTypeTitle(store.type);
}

/**
 * 切换页面
 */
function currentPageChange(currentPage: number) {
  store.updatePagination({ currentPage });
  store.refresh();
}

/**
 * 切换页码数量
 */
function pageSizeChange(pageSize: number) {
  store.updatePagination({ currentPage: 1, pageSize });
  store.refresh();
}

/**
 * 搜索
 */
function search(params: ISyncSearchParams) {
  store.updateSearchParams(params);
  store.updatePagination({ currentPage: 1 });
  store.refresh();
}

/**
 * @description: 批量检查该订单行下的生产数据
 */
async function handleBatchCheckProdData() {
  const action = await ElMessageBox.alert("是否确认批量检查该订单行下的生产数据？", "批量检查", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  if (action !== "confirm") {
    return;
  }
  // syncInfoStore.sync 在国网订单时存的是采购订单行数据，上海平台、广州供电局存的是
  const lineId = syncInfoStore.sync.id;
  const params = syncInfoStore.isEipChannel ? { purchaseLineId: lineId } : { salesLineId: lineId };
  const { data: result } = await checkAutoCollectProdData(params);
  if (result) {
    ElMessage.success("批量操作成功，已加入检查队列");
  }
}
</script>

<style scoped lang="scss">
:deep(.pure-table) {
  .iconfont.icon-refresh {
    animation: loading-rotate 2s linear infinite;
  }
}
</style>
