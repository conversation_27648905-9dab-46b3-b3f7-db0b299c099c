<template>
  <div class="bg-bg_color flex justify-between items-center">
    <SearchForm @onSearch="onSearch($event)" />
    <CXBatchAction :count="state.count" @cancel="batchCancel" class="mb-4">
      <div v-for="(item, index) in batchButton" :key="index" class="flex flex-wrap ml-4">
        <CXBatchButton :button="item" />
      </div>
    </CXBatchAction>
  </div>
  <div class="bg-bg_color flex flex-col flex-1 overflow-hidden" :class="batch">
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      :row-key="getRowKeyOfTarget"
      size="large"
      ref="tableRef"
      :data="state.progressAlarmTableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      showOverflowTooltip
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
      @selection-change="handleSelectionChange"
    >
      <template #content="data">
        <span v-html="highLight(data.row.message)" />
      </template>
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click.stop="onQuery(data.row.id)" :disabled="isSolving"> 详情 </ElButton>
          <ElButton
            v-auth="PermissionKey.alarm.alarmDataSolve"
            type="primary"
            link
            @click.stop="onSolveFormDiglog(data.row.id)"
            :disabled="isSolving"
          >
            解决
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>

  <el-dialog
    title="解决告警"
    align-center
    class="small"
    destroy-on-close
    v-model="state.solveFormModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCancelSolveFormModal"
  >
    <AlarmSolveForm ref="solveRef" />
    <template #footer>
      <el-button @click="onCancelSolveFormModal()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="solveAlarm()">保存</el-button>
    </template>
  </el-dialog>

  <el-dialog
    title="告警详情"
    align-center
    destroy-on-close
    v-model="state.viewFormModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCancelViewFormModal"
  >
    <ProgressAlarmDetail :loading="detailLoading" />
  </el-dialog>
</template>

<script setup lang="ts">
import SearchForm from "../component/search-form.vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { IAlarmData, IAlarmDataParam, IAlarmSolveReq } from "@/models";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { reactive, watch, onMounted, ref, computed } from "vue";
import { useAlarmDataStore } from "@/store/modules";
import { useAlarmDataHook } from "../hooks/alarm-data-hook";
import { AlarmSolveStatusEnum, AlarmTypeEnum } from "@/enums";
import AlarmSolveForm from "../alarm-solve-form/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage, ElButton } from "element-plus";
import ProgressAlarmDetail from "./progress-alarm-detail-form/index.vue";
import { pickBy, merge, isEmpty, omit, isNumber } from "lodash-unified";
import CXBatchAction from "@/components/cx-batch-action";
import CXBatchButton from "@/components/cx-batch-button";
import { highLight } from "../untils/high-light-alarm-message";
import { PermissionKey } from "@/consts";

const { columns } = useColumns();
const { pagination } = useTableConfig();
const alarmDataStore = useAlarmDataStore();
const tableRef = ref<PureTableInstance>();

const { getAlarm, getAlarmDataById, setAlarmDataStorage, solveAlarmById, clearAlarmDataStorage, getRowKeyOfTarget } =
  useAlarmDataHook();
const state = reactive<{
  solveFormModalVis: boolean;
  viewFormModalVis: boolean;
  params: IAlarmDataParam;
  progressAlarmTableData: Array<IAlarmData>;
  selectIds: Array<string>;
  singleResoledId: Array<string>;
  count: number;
}>({
  solveFormModalVis: false,
  viewFormModalVis: false,
  params: {
    alarmType: AlarmTypeEnum.progressAlarm
  },
  progressAlarmTableData: [],
  selectIds: [],
  singleResoledId: [],
  count: 0
});
const saveLoading = ref<boolean>(false);
const loading = ref<boolean>(false);
const detailLoading = ref<boolean>(false);
const solveRef = ref<InstanceType<typeof AlarmSolveForm>>();
const isSolving = computed(() => !!state.selectIds.length);

const batchButton = computed(() => [
  {
    name: "批量解决",
    action: batchClick,
    disabled: !state.selectIds.length,
    permission: PermissionKey.alarm.alarmDataSolve
  }
]);
watch(
  () => alarmDataStore.total,
  () => {
    pagination.total = alarmDataStore.total;
  },
  {
    immediate: true
  }
);
onMounted(async () => {
  state.progressAlarmTableData = await getAlarmData(queryParams());
});

const onPageSizeChange = async () => {
  state.progressAlarmTableData = await getAlarmData(queryParams());
};
const onCurrentPageChange = async () => {
  state.progressAlarmTableData = await getAlarmData(queryParams());
};

const onSearch = async (params: IAlarmDataParam) => {
  pagination.currentPage = 1;
  state.params = isEmpty(params)
    ? omit(state.params, ["keyWords", "alarmTimes", "alarmSolveStatus"])
    : merge({}, state.params, params);
  state.progressAlarmTableData = await getAlarmData(queryParams());
  if (state.selectIds.length) {
    batchCancel();
  }
};

const onSolveFormDiglog = async (id: string) => {
  state.singleResoledId.push(id);
  const alarmDataDetail: IAlarmData = await getAlarmDataById(id, AlarmTypeEnum.progressAlarm);
  if (!alarmDataDetail && alarmDataDetail.solveStatus == AlarmSolveStatusEnum.UNRESOLVED) {
    return;
  }
  state.solveFormModalVis = true;
};

const onQuery = async (id: string) => {
  state.viewFormModalVis = true;
  const alarmDataDetail: IAlarmData = await getAlarmById(id, AlarmTypeEnum.progressAlarm);
  setAlarmDataStorage(alarmDataDetail);
};

const onCancelSolveFormModal = () => {
  state.singleResoledId = [];
  state.solveFormModalVis = false;
};

const onCancelViewFormModal = () => {
  state.viewFormModalVis = false;
  clearAlarmDataStorage();
};

const solveAlarm = async () => {
  const formValue: IAlarmSolveReq | false = await solveRef.value.getValidValue().catch(() => false);

  if (!formValue) {
    return;
  }
  const ids = state.singleResoledId?.length ? state.singleResoledId : state.selectIds;
  await onSolve(merge(formValue, { ids }), AlarmTypeEnum.progressAlarm);
  state.solveFormModalVis = false;
  ElMessage.success("解决成功");
  state.selectIds.length = 0;
  state.singleResoledId.length = 0;
  state.progressAlarmTableData = await getAlarmData(queryParams());
  batchCancel();
};

function queryParams() {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  const params: IAlarmDataParam = pickBy(state.params, value => !!value || isNumber(value));
  if (params.alarmSolveStatus) {
    params.alarmSolveStatus = Number(params.alarmSolveStatus);
  }
  return params;
}

function handleSelectionChange(rows: Array<IAlarmData>) {
  state.selectIds = rows.map(row => row.id);
  state.count = rows.length;
}

function batchCancel() {
  state.selectIds.length = 0;
  tableRef.value?.getTableRef()?.clearSelection();
}

function batchClick() {
  if (state.selectIds.length) {
    state.solveFormModalVis = true;
  }
}
/** 解决告警 */
const onSolve = useLoadingFn(solveAlarmById, saveLoading);
/** 告警列表 */
const getAlarmData = useLoadingFn(getAlarm, loading);
/** 查询告警详情 */
const getAlarmById = useLoadingFn(getAlarmDataById, detailLoading);

const batch = computed(() => (state.selectIds.length ? `batch` : ``));
</script>

<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

.batch {
  ::v-deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  ::v-deep(.el-input__suffix-inner) {
    pointer-events: none;
  }
}
</style>
