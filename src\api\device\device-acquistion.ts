import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IDeviceAcquisition, IDeviceAcquisitionForm, IDeviceAcquisitionReq, IListResponse, IResponse } from "@/models";
// 设备采集点列表
export const queryDeviceAcquisition = (data: IDeviceAcquisitionReq) => {
  const url: string = withApiGateway("admin-api/business/deviceCollectionPoint/getList");
  return http.post<IDeviceAcquisitionReq, IListResponse<IDeviceAcquisition>>(url, { data });
};

// 设备采集点列表 -- 不分页
export const queryDeviceAcquisitionAll = (deviceId: string) => {
  const url: string = withApiGateway(`admin-api/business/deviceCollectionPoint/getList/${deviceId}`);
  return http.get<void, IResponse<Array<IDeviceAcquisition>>>(url);
};

// 设备采集点新增
export const createDeviceAcquisition = (data: IDeviceAcquisitionForm) => {
  return http.post<IDeviceAcquisitionForm, IResponse<string>>(
    withApiGateway("admin-api/business/deviceCollectionPoint/create"),
    { data },
    { showErrorInDialog: true }
  );
};

// 设备采集点编辑
export const editDeviceAcquisition = (data: IDeviceAcquisitionForm) => {
  return http.post<IDeviceAcquisitionForm, IResponse<boolean>>(
    withApiGateway(`admin-api/business/deviceCollectionPoint/edit/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

// 设备采集点列表-删除
export const deleteDeviceAcquisition = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/deviceCollectionPoint/delete/${id}`)
  );
};
