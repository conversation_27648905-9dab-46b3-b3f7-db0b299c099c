<template>
  <el-dialog
    v-model="editVisible"
    title="编辑工单"
    class="default"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="min-h-80" v-loading="loading">
      <WorkOrderForm v-if="isCable && subClassCode" mode="edit" :subClassCode="subClassCode" ref="editFormRef" />
      <NonCableWorkOrderForm
        v-if="!isCable && subClassCode"
        mode="edit"
        :subClassCode="subClassCode"
        ref="editFormRef"
      />
    </div>
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import WorkOrderForm from "@/views/components/work-order-and-report-work/add-edit-work-order-dialog/work-order-form/index.vue";
import NonCableWorkOrderForm from "@/views/order/production-order/add-edit-work-order/work-order-form/index.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ICreateWorkOrder, IWorkOrderSync } from "@/models";
import { computed, nextTick, ref } from "vue";
import { getWorkOrderById, editWorkOrder as requestEditWorkOrder } from "@/api/work-order";

type FormComponent = typeof WorkOrderForm | typeof NonCableWorkOrderForm;

const props = defineProps<{
  isCable: boolean;
}>();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<FormComponent>>(editWorkOrder);
const { loading, handlePatchValue } = usePatchValue(patchValue);

const subClassCode = ref<string>();

const isCable = computed(() => props.isCable);

function openEditDialog(data: IWorkOrderSync) {
  editVisible.value = true;
  subClassCode.value = "";
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.woNo
  };
  handlePatchValue(data.dataId);
}

async function patchValue(dataId: string) {
  await nextTick();
  return initWorkOrderForm(dataId);
}

async function initWorkOrderForm(dataId: string) {
  const { data } = await getWorkOrderById(dataId);
  const {
    planFinishDate,
    planStartDate,
    processIds,
    subClassCode: detailSubClassCode,
    subclassCode: detailSubclassCode
  } = data;
  subClassCode.value = detailSubClassCode || detailSubclassCode;
  const formValue = {
    ...data,
    planDateArray: planFinishDate && planStartDate ? [planStartDate, planFinishDate] : [],
    processIds: processIds.split("-")
  };
  nextTick(() => {
    (editFormRef.value as InstanceType<typeof WorkOrderForm>).initFormValue(formValue);
  });
}

async function editWorkOrder(): Promise<boolean> {
  const formRef = editFormRef.value as InstanceType<typeof WorkOrderForm>;
  if (!formRef) {
    return;
  }
  const verified = await formRef.validateForm();
  if (!verified) {
    return;
  }
  const formValue = formRef.getFormValue();
  const params: ICreateWorkOrder = {
    ...formValue,
    planStartDate: formValue.planDateArray[0],
    planFinishDate: formValue.planDateArray[1],
    processIds: formValue.processIds.join("-")
  };
  const workId = await requestEditWorkOrder(params);
  return Boolean(workId);
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
