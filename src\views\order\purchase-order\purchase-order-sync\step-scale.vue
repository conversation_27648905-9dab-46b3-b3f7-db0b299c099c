<template>
  <div class="flex w-full pt-5">
    <div
      class="step"
      v-for="step in sync?.steps"
      :key="step.name"
      :style="{ width: `${step.percent}%` }"
      :class="step.status"
    >
      <span class="text-sm whitespace-nowrap block translate-y-[-100%]">{{ step.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePurchaseOrderSyncStore } from "@/store/modules";
import { computed } from "vue";

const store = usePurchaseOrderSyncStore();
const sync = computed(() => store.sync);
</script>

<style scoped lang="scss">
$scaleColor: #d8d8d8;

.step {
  @apply text-center;
  color: var(--el-text-color-disabled);
  background: repeating-linear-gradient(to right, $scaleColor 0, $scaleColor 2px, transparent 2px, transparent 100%)
      no-repeat bottom,
    repeating-linear-gradient(to right, $scaleColor 0, $scaleColor 1px, transparent 1px, transparent 5px) no-repeat
      bottom;
  background-size: 100% 100%, 100% 7px;
}

.step:last-child {
  background: repeating-linear-gradient(to right, $scaleColor 0, $scaleColor 2px, transparent 2px, transparent 100%)
      no-repeat bottom,
    repeating-linear-gradient(to right, $scaleColor 0, $scaleColor 1px, transparent 1px, transparent 5px) no-repeat
      bottom,
    repeating-linear-gradient(
        to right,
        transparent 0,
        transparent calc(100% - 2px),
        $scaleColor calc(100% - 2px),
        $scaleColor 100%
      )
      no-repeat bottom;
  background-size: 100% 100%, 100% 7px;
}

.step.success {
  color: var(--el-color-primary);
}

.step.fail {
  color: var(--el-color-danger);
}
</style>
