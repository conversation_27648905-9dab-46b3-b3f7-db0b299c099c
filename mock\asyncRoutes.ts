// 模拟后端动态生成路由
import { MockMethod } from "vite-plugin-mock";

const purchaseOrderRouter: DynamicRouteConfigsTable = {
  path: "/purchase",
  meta: {
    title: "订单数据中心",
    icon: "list",
    rank: 10
  },
  children: [
    {
      name: "purchaseOrder",
      path: "/purchase-order",
      component: "purchase-order/management/index",
      meta: {
        title: "采购订单",
        menuKey: "purchaseOrder"
      },
      children: [
        {
          name: "purchaseOrderDetail",
          path: "/purchase-order/:id",
          component: "order/purchase-order/detail/index",
          meta: {
            showLink: false,
            title: "采购订单详情",
            menuKey: "purchaseOrder"
          }
        }
      ]
    },
    {
      name: "purchaseOrderLine",
      path: "/purchase-order-line",
      component: "order/purchase-order-line/index",
      meta: {
        // todo: route add style support
        title: "\u00a0\u00a0采购订单行",
        menuKey: "purchaseOrderLine"
      }
    },
    {
      name: "salesOrder",
      path: "/sales-order",
      component: "order/sales-order/index",
      meta: {
        title: "销售订单",
        menuKey: "salesOrder"
      },
      children: [
        {
          name: "salesOrderDetail",
          path: "/sales-order/:id",
          component: "order/sales-order/detail/index",
          meta: {
            showLink: false,
            title: "销售订单详情",
            menuKey: "salesOrder"
          }
        }
      ]
    },
    {
      name: "productionOrder",
      path: "/production-order",
      component: "order/production-order/index",
      meta: {
        title: "生产订单",
        menuKey: "productionOrder"
      }
    },
    {
      name: "unfinishedReportWork",
      path: "/unfinished-report-work",
      component: "order/unfinished-report-work/index",
      meta: {
        title: "未完成报工",
        menuKey: "unfinishedReportWork"
      }
    },
    {
      path: "/supply-order",
      name: "supply-order",
      component: "delivery-order/index",
      meta: {
        title: "供货单",
        menuKey: "supplyOrder"
      },
      children: [
        {
          name: "supply-order-info",
          path: "/supply-order/:id",
          component: "delivery-order/delivery-order-info/index",
          meta: {
            showLink: false,
            title: "供货单信息详情",
            menuKey: "supplyOrder"
          }
        }
      ]
    }
  ]
};

const baseConfigRouter: DynamicRouteConfigsTable = {
  path: "/config",
  meta: {
    title: "基础数据",
    icon: "setting",
    rank: 10
  },
  children: [
    // {
    //   name: "rawMaterial",
    //   path: "/raw-material",
    //   component: "config/raw-material/management/index",
    //   meta: {
    //     title: "原材料检",
    //     showParent: true,
    //     menuKey: "rawMaterialInspect"
    //   }
    // }, // v1
    {
      name: "rawMaterialV2",
      path: "/raw-material",
      component: "config/raw-material/management/index",
      meta: {
        title: "原材料",
        showParent: true,
        showLink: true,
        menuKey: "rawMaterialInspect"
      }
    },
    {
      path: "/material",
      component: "config/material/management/index",
      name: "material",
      meta: {
        title: "物料管理"
      }
    },
    {
      path: "/device",
      name: "deviceManagement",
      component: "config/device/management/index",
      meta: {
        rank: 3,
        title: "生产设备",
        menuKey: "device"
      },
      children: [
        {
          name: "deviceManagementDetail",
          path: "/device/:id",
          component: "config/device/management/details/index",
          meta: {
            showLink: false,
            title: "设备管理详情",
            menuKey: "device"
          }
        }
      ]
    },
    {
      path: "/monitor-device",
      name: "monitor-device",
      component: "config/monitor-device/index",
      meta: {
        title: "监控设备"
      }
    },
    {
      path: "/technical-standard-library",
      name: "technical-standard-library",
      component: "config/technical-standard-library/management/index",
      meta: {
        title: "技术标准库"
      },
      children: [
        {
          name: "technical-standard-library-detail",
          path: "/technical-standard-library/:id",
          component: "config/technical-standard-library/detail/index",
          meta: {
            showLink: false,
            title: "技术标准库参数详情",
            menuKey: "technicalStandardLibrary"
          }
        }
      ]
    },
    {
      path: "/import",
      name: "import",
      component: "config/import/index",
      meta: {
        title: "数据导入",
        menuKey: "import"
      },
      children: [
        {
          name: "data-view",
          path: "/import-data-view/:bathNo/:type",
          component: "config/import/import-data-view/index",
          meta: {
            showLink: false,
            title: "导入数据列表",
            menuKey: "import"
          }
        }
      ]
    }
  ]
};

const enterpriseRouter: DynamicRouteConfigsTable = {
  path: "/enterprise",
  meta: {
    title: "企业管理",
    icon: "building",
    rank: 10
  },
  children: [
    {
      path: "/enterprise-info",
      name: "enterprise-info",
      component: "enterprise/enterprise-info/index",
      meta: {
        title: "企业信息"
      }
    },
    {
      path: "/sync",
      component: "enterprise/sync/index",
      name: "sync",
      meta: {
        title: "数据同步开关"
      }
    },
    {
      path: "/employee",
      name: "employee",
      component: "enterprise/employee/index",
      meta: {
        title: "员工管理"
      }
    },
    {
      path: "/department",
      name: "department",
      component: "enterprise/department/index",
      meta: {
        title: "部门管理"
      }
    },
    {
      path: "/role",
      component: "enterprise/role/index",
      name: "role",
      meta: {
        title: "角色权限"
      }
    },
    {
      path: "/workshop",
      component: "enterprise/workshop-manage/index",
      name: "workshop",
      meta: {
        title: "车间管理",
        menuKey: "workshopManage"
      }
    }
  ]
};

const stockRouter: DynamicRouteConfigsTable = {
  path: "/stock-data-center",
  meta: {
    title: "库存数据中心",
    icon: "shield",
    rank: 10
  },
  children: [
    {
      path: "/emphasis-raw-material",
      name: "emphasisRawMaterial",
      component: "stock-data-center/emphasis-raw-material/index",
      meta: {
        title: "重点原材料"
      }
    },
    {
      path: "/finished-product",
      name: "finishedProduct",
      component: "stock-data-center/finished-product/index",
      meta: {
        title: "产成品库存"
      }
    },
    {
      path: "/reserve-part-stock",
      name: "reservePartStock",
      component: "stock-data-center/reserve-part-stock/index",
      meta: {
        title: "备品备件"
      }
    },
    {
      path: "/stock-sync",
      name: "stockSync",
      component: "stock-data-center/stock-sync/index",
      meta: {
        title: "库存同步"
      }
    }
  ]
};

/** 系统内置表单 */
const tenantRouter: DynamicRouteConfigsTable = {
  path: "/platform",
  meta: {
    title: "平台管理",
    icon: "admin",
    rank: 10
  },
  children: [
    {
      path: "/auth",
      name: "auth",
      component: "platform/system-auth/index",
      meta: {
        title: "系统授权",
        rank: 10
      }
    },
    {
      path: "/tenant",
      name: "tenant",
      component: "platform/tenant/index",
      meta: {
        title: "租户管理",
        menuKey: "tenant",
        rank: 10
      },
      children: [
        {
          path: "/platform/token/:id",
          name: "密钥",
          component: "open-platform/token/index",
          meta: {
            showLink: false,
            title: "OpenAPI凭证管理",
            menuKey: "tenant"
          }
        }
      ]
    },
    {
      path: "/account",
      name: "account",
      component: "platform/account/index",
      meta: {
        title: "账号管理"
      }
    },
    {
      path: "/dictionary",
      name: "用户字典",
      component: "platform/dictionary/index",
      meta: {
        title: "用户字典"
      }
    },
    {
      path: "/process-route",
      name: "工艺路线",
      component: "platform/process-route/index",
      meta: {
        title: "工艺路线"
      }
    },
    {
      path: "/collection",
      name: "collectionManagement",
      component: "config/collection/management/index",
      meta: {
        title: "EIP数据采集规范",
        showParent: true,
        showLink: false
      }
    },
    {
      path: "/synchronization",
      name: "synchronizationFlag",
      component: "platform/synchronization-flag/index",
      meta: {
        title: "同步标识"
      }
    }
  ]
};

// 出厂试验
const leaveFactoryExperimentRouter: DynamicRouteConfigsTable = {
  path: "/leave-factory",
  meta: {
    title: "出厂试验",
    icon: "flask",
    rank: 10
  },
  children: [
    {
      path: "/jfny-experiment",
      name: "jfnyExperiment",
      component: "leave-factory/jfny-experiment/index",
      meta: {
        title: "局放耐压",
        showParent: true
      }
    }
  ]
};

const qualityTracingRouter: DynamicRouteConfigsTable = {
  path: "/quality-tracing",
  meta: {
    title: "质量追溯",
    icon: "guide",
    rank: 10
  },
  children: [
    {
      path: "/quality-specification",
      name: "qualitySpecification",
      component: "quality-tracing/quality-specification/index",
      meta: {
        title: "质量规范",
        menuKey: "qualityTracing"
      }
    },
    {
      path: "/quality-specification/:id",
      name: "qualitySpecificationDetail",
      component: "quality-tracing/quality-specification/quality-specification-detail/index",
      meta: {
        title: "质量规范详情",
        menuKey: "qualityTracing",
        showLink: false
      }
    },
    {
      path: "/quality-tracing-record",
      name: "qualityTracingRecord",
      component: "quality-tracing/quality-tracing-record/index",
      meta: {
        title: "质量追溯记录",
        menuKey: "qualityTracingRecord"
      }
    },
    {
      path: "/quality-tracing-record/:id",
      name: "qualityTracingRecordDetail",
      component: "quality-tracing/quality-tracing-record/quality-tracing-record-detail/index",
      meta: {
        title: "质量追溯记录详情",
        menuKey: "qualityTracingRecord",
        showLink: false
      }
    },
    {
      path: "/quality-level",
      name: "qualityLevel",
      component: "quality-tracing/quality-level/index",
      meta: {
        title: "质量等级"
      }
    }
  ]
};

// 个人中心
const accountRouter: DynamicRouteConfigsTable = {
  path: "/account",
  meta: {
    title: "账号",
    icon: "service",
    rank: 10,
    showLink: false
  },
  children: [
    {
      path: "/account-profile",
      name: "个人中心",
      component: "profile/index",
      meta: {
        showParent: false,
        showLink: false,
        title: "个人中心"
      }
    },
    {
      path: "/report-export-record",
      name: "report-export-record",
      component: "report-center/export-record/index",
      meta: {
        title: "导出记录",
        showLink: false
      }
    }
  ]
};

// 日志监控
const logMonitoring: DynamicRouteConfigsTable = {
  path: "/logging",
  meta: {
    title: "日志监控",
    icon: "calendar",
    rank: 10
  },
  children: [
    {
      path: "/gateway-warning-rule",
      name: "gatewayWarningRule",
      component: "logging/gateway-warning-rule/index",
      meta: {
        title: "网关告警规则"
      }
    },
    {
      path: "/gateway",
      name: "gatewayDevice",
      component: "logging/gateway/index",
      meta: {
        title: "EIP网关拉取日志",
        menuKey: "gatewayDevice"
      },
      children: [
        {
          path: "/gateway-detail/:id",
          name: "gatewayDetail",
          component: "logging/gateway/detail/index.vue",
          meta: {
            title: "网关设备详情",
            showLink: false,
            menuKey: "gatewayDevice"
          }
        }
      ]
    },

    {
      path: "/eip-interface",
      name: "eipInterface",
      component: "logging/eip-interface/index",
      meta: {
        title: "EIP日志"
      }
    },
    {
      path: "/operation",
      name: "operation",
      component: "logging/operation/index",
      meta: {
        title: "操作日志"
      }
    },
    {
      path: "/data-logging",
      name: "dataLogging",
      component: "logging/data-logging/index",
      meta: {
        title: "数据日志"
      }
    },
    {
      path: "/login-logging",
      name: "loginLogging",
      component: "logging/login/index",
      meta: {
        title: "登录日志"
      }
    },
    {
      path: "/open-api-logging",
      name: "openApiLogging",
      component: "logging/open-api-logging/index",
      meta: {
        title: "OpenAPI日志"
      }
    }
  ]
};

// 告警管理
const alarmRouter: DynamicRouteConfigsTable = {
  path: "/alarm",
  meta: {
    title: "告警中心",
    icon: "alarm",
    rank: 10
  },
  children: [
    {
      path: "/alarm-data",
      name: "alarm-data",
      component: "alarm/alarm-data/index",
      meta: {
        title: "告警数据"
      }
    },
    {
      path: "/alarm-rules",
      name: "alarm-rules",
      component: "alarm/alarm-rules/index",
      meta: {
        title: "告警规则"
      }
    },
    {
      path: "/data-check",
      name: "data-check",
      component: "data-integrity-check/index",
      meta: {
        title: "生产数据完整性检查"
      }
    }
  ]
};

// 报表中心
const reportCenterRouter: DynamicRouteConfigsTable = {
  path: "/report-center",
  meta: {
    title: "报表中心",
    icon: "lineChart",
    rank: 11
  },
  children: [
    {
      path: "/purchase-report",
      name: "purchase-report",
      component: "report-center/purchase-report/index",
      meta: {
        title: "采购订单"
      }
    },
    {
      path: "/login-report",
      name: "login-report",
      component: "report-center/login-report/index",
      meta: {
        title: "登录报表"
      }
    }
  ]
};

// 导出记录
const reportExportRecord: DynamicRouteConfigsTable = {
  path: "/report-export-record",
  name: "report-export-record",
  component: "report-center/export-record/index",
  meta: {
    title: "导出记录",
    showLink: false
  }
};

// E-基建
const capitalConstructionRouter: DynamicRouteConfigsTable = {
  path: "/e-capital-construction",
  meta: {
    title: "E基建",
    icon: "lineChart",
    rank: 11
  },
  children: [
    {
      path: "/engineering-info/list",
      name: "engineering-info",
      component: "e-capital-construction/engineering-info/list/index",
      meta: {
        title: "工程信息",
        menuKey: "engineeringInfo"
      },
      children: [
        {
          name: "engineeringInfoDetail",
          path: "/engineering-info/detail",
          component: "e-capital-construction/engineering-info/detail/index",
          meta: {
            showLink: false,
            title: "工程信息详情",
            menuKey: "engineeringInfo"
          }
        }
      ]
    },
    {
      path: "/test-parameter/list",
      name: "test-parameter",
      component: "e-capital-construction/test-parameter/list/index",
      meta: {
        title: "试验参数标准",
        menuKey: "testParameter"
      },
      children: [
        {
          name: "testParameterDetail",
          path: "/test-parameter/detail",
          component: "e-capital-construction/test-parameter/detail/index",
          meta: {
            showLink: false,
            title: "试验参数标准详情",
            menuKey: "testParameter"
          }
        }
      ]
    }
  ]
};

// 南网接入
const southGridAccessRouter: DynamicRouteConfigsTable = {
  path: "/south-grid-access",
  meta: {
    title: "南网接入",
    icon: "lineChart",
    rank: 11
  },
  children: [
    {
      path: "/supervision-plan",
      name: "supervision-plan",
      component: "south-grid-access/supervision-plan/index",
      meta: {
        title: "监造计划",
        menuKey: "supervision-plan"
      },
      children: [
        {
          name: "supervision-plan-detail",
          path: "/supervision-plan/:id",
          component: "south-grid-access/supervision-plan/detail/index",
          meta: {
            showLink: false,
            title: "监造计划详情",
            menuKey: "supervision-plan"
          }
        }
      ]
    },
    {
      path: "/type-experimen",
      name: "type-experimen",
      component: "south-grid-access/type-experimen/index",
      meta: {
        title: "型式实验",
        menuKey: "type-experimen"
      },
      children: [
        {
          name: "type-experimen-item",
          path: "/type-experimen/:id",
          component: "south-grid-access/type-experimen/type-experimen-item/index",
          meta: {
            showLink: false,
            title: "技术参数",
            menuKey: "type-experimen"
          }
        }
      ]
    },
    {
      path: "/process-standar",
      name: "process-standar",
      component: "south-grid-access/process-standar/index",
      meta: {
        title: "工艺标准",
        menuKey: "process-standar"
      },
      children: [
        {
          name: "process-standar-technical-specification",
          path: "/process-standar/:id",
          component: "south-grid-access/process-standar/process-standar-technical-spec/index",
          meta: {
            showLink: false,
            title: "技术参数",
            menuKey: "process-standar"
          }
        }
      ]
    },
    {
      path: "/event-notification",
      name: "event-notification",
      component: "south-grid-access/event-notification/index",
      meta: {
        title: "事件通知",
        menuKey: "event-notification"
      }
    }
  ]
};
export default [
  {
    url: "/getAsyncRoutes",
    method: "get",
    response: () => {
      return {
        success: true,
        data: [
          purchaseOrderRouter,
          stockRouter,
          alarmRouter,
          baseConfigRouter,
          qualityTracingRouter,
          leaveFactoryExperimentRouter,
          logMonitoring,
          enterpriseRouter,
          tenantRouter,
          accountRouter,
          reportCenterRouter,
          reportExportRecord,
          capitalConstructionRouter,
          southGridAccessRouter
        ]
      };
    }
  }
] as MockMethod[];
