import { computed, ref, watch, ComputedRef } from "vue";
import { IDataIntegrityCheck } from "@/models/data-integrity-check/i-data-integrity-check";

/**
 * @description: 表格选中
 */
export function useTableSelect(subClassCode: ComputedRef<string>) {
  const selectedMap = new Map<string, IDataIntegrityCheck>();
  const selectedList = ref<IDataIntegrityCheck[]>([]);
  const selectedCount = computed(() => selectedList.value.length);

  function addItem(id: string, item: IDataIntegrityCheck) {
    selectedMap.set(id, item);
  }

  function clearMap() {
    selectedMap.clear();
  }

  function selectChange(selection: Array<IDataIntegrityCheck>) {
    selectedList.value = selection;
  }

  function clearSelectedList() {
    selectedList.value = [];
  }

  watch(subClassCode, () => {
    clearMap();
    selectedList.value = [];
  });

  // 更新映射关系
  watch(selectedList, list => {
    clearMap();
    list.forEach(item => {
      addItem(item.productionId, item);
    });
  });

  return {
    selectChange,
    clearSelectedList,
    clearMap,
    selectedCount,
    selectedList
  };
}
