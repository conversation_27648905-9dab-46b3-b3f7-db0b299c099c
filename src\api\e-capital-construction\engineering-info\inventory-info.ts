/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { InventoryInfoModel } from "@/models";
/**
 * @description: 原材料库存信息列表
 */
export const InventoryInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<InventoryInfoModel>>>(
    withApiGateway(`admin-api/ejj/project/raw-material/store/list/${id}`)
  );
};
/**
 * @description: 原材料库存信息创建
 */
export const InventoryCreateApi = (params: InventoryInfoModel) => {
  return http.post<InventoryInfoModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/store`), {
    data: params
  });
};

/**
 * @description: 原材料库存信息编辑
 */
export const InventoryEditApi = (params: InventoryInfoModel) => {
  return http.put<InventoryInfoModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/store`), {
    data: params
  });
};

/**
 * @description: 原材料库存信息删除
 */
export const InventoryDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/store/${id}`));
};
