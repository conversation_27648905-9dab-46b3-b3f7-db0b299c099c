<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 搜索条 -->
    <div class="bg-bg_color flex justify-end p-2 pr-7">
      <el-button type="primary" @click="handleAdd" :loading="loading">新增</el-button>
    </div>
    <!-- 表格 -->
    <div class="bg-bg_color p-5 pt-3 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="data">
          <ElButton type="primary" link @click="handleEdit(data.row)"> 编辑 </ElButton>
          <ElButton type="danger" link @click="handleDelete(data.row.id)"> 删除 </ElButton>
        </template>
      </PureTable>
    </div>
    <el-dialog
      v-model="addTestParameterVisibleRef"
      :title="isEditDialog ? '编辑' : '新增'"
      width="500px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCloseDialog()"
    >
      <add-form ref="formRef" :detail="selectRow" :equipmentList="equipmentList" :isEdit="isEditDialog" />
      <template #footer>
        <span>
          <el-button @click="handleCloseDialog()">取消</el-button>
          <el-button type="primary" @click="handleCreate()">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import { useColumns } from "./column-config";
import AddForm from "./components/add-form.vue";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useTableConfig } from "@/utils/useTableConfig";
import {
  getTestParameterReportList,
  getEquipmentApi,
  createStandardApi,
  updateStandardApi,
  deleteStandardApi
} from "@/api/e-capital-construction/test-parameter/index";
import { EParamsStandard, EParamsStandardReport, EquipmentModel } from "@/models";
/**
 * E基建-试验参数标准
 */

// 设置标题
// const router = useRouter();

usePageStoreHook().setTitle("出厂试验参数标准" as string);

const { pagination } = useTableConfig();
const { columnsConfig } = useColumns();
const loading = ref(false);
const list = ref<EParamsStandard[]>([]);
const equipmentList = ref<EquipmentModel[]>([]);
const addTestParameterVisibleRef = ref<boolean>();
let selectRow = reactive({} as EParamsStandard);
const isEditDialog = ref(false);
const formRef = ref();

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: EParamsStandardReport = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
  const { data } = await getTestParameterReportList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 请求物资分类列表
 */
const getEquipmentList = useLoadingFn(async () => {
  const { data } = await getEquipmentApi();
  equipmentList.value = data;
}, loading);
/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

/**
 * @description: 新增
 */
const handleAdd = async () => {
  addTestParameterVisibleRef.value = true;
  isEditDialog.value = false;
};

/**
 * @description: 取消
 */
const handleCloseDialog = async () => {
  addTestParameterVisibleRef.value = false;
  selectRow = {} as EParamsStandard;
};

/**
 * @description: 新增标准
 */
const handleCreate = async () => {
  const verificationResult = await formRef.value.validateForm();
  if (!verificationResult) {
    return;
  }
  const formVal = formRef.value.getFormValue();
  addTestParameterVisibleRef.value = false;
  onSave(formVal);
};

/**
 * @description: 保存标准
 */
const onSave = useLoadingFn(async (formVal: EParamsStandard) => {
  isEditDialog.value ? await updateStandardApi(formVal) : await createStandardApi(formVal);
  requestList();
  ElMessage({ type: "success", message: "保存成功" });
}, loading);
/**
 * @description: 删除
 */
const handleDelete = async (id: string) => {
  if (!(await useConfirm("是否确认删除", "删除确认"))) {
    return;
  }
  const { data } = await deleteStandardApi(id);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
};

/**
 * @description: 编辑
 */
const handleEdit = async (row: EParamsStandard) => {
  addTestParameterVisibleRef.value = true;
  selectRow = row;
  isEditDialog.value = true;
};

onMounted(() => {
  requestList();
  getEquipmentList();
});
</script>

<style scoped></style>
