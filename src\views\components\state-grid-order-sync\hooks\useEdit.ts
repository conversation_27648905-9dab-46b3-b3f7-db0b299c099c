import { computed, inject, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { EMaterialCategory } from "@/enums/purchase-order/production-test-sensing";
import { ARMOUR_CLAMP_CODE, CABLE_CATEGORY_CODE } from "@/consts";

export function useEdit<T>(editFn: () => Promise<boolean>) {
  const editVisible = ref(false);
  const editFormRef = ref<T>();
  const editLoading = ref(false);
  const editAndSyncLoading = ref(false);
  const handleEdit = useLoadingFn(edit, editLoading);
  const handleEditAndSync = useLoadingFn(editAndSync, editAndSyncLoading);

  const ctx = inject(stateGridOrderSyncEditKey);

  const hasSync = computed(() => typeof ctx.syncFn === "function");

  async function edit() {
    (await editFn()) && closeAndRefresh("编辑成功");
  }

  async function editAndSync() {
    if (!(await editFn())) {
      return;
    }
    const { id, no } = ctx.editData;
    if (!id) {
      return;
    }
    await ctx.syncFn(id, no);
    closeAndRefresh();
  }

  function closeAndRefresh(message?: string) {
    if (message) {
      ElMessage.success(message);
    }
    editVisible.value = false;
    ctx.refreshFn();
  }

  /** 不同物资种类 */
  function handleMaterialCategory(subClassCode?: string): string {
    switch (subClassCode) {
      case ARMOUR_CLAMP_CODE:
        return EMaterialCategory.ArmourClamp;
      case CABLE_CATEGORY_CODE:
        return EMaterialCategory.Normal;
      default:
        return EMaterialCategory.Normal;
    }
  }

  return {
    hasSync,
    editVisible,
    editFormRef,
    editLoading,
    editAndSyncLoading,
    handleEdit,
    handleEditAndSync,
    handleMaterialCategory,
    stateGridOrderSyncEditCtx: ctx
  };
}
