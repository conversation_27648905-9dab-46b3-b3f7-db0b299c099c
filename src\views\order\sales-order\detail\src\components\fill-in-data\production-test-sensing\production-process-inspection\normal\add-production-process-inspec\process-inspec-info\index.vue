<template>
  <div>
    <el-form
      v-loading="loading"
      ref="processInspecRef"
      :validate-on-rule-change="false"
      label-position="top"
      require-asterisk-position="right"
      :model="form"
      :rules="rules"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item class="form-item" label="过程检编号" prop="code">
            <SerialNumber
              ref="serialNumberRef"
              :code="PROCESS_INSPECT_NO_CODE"
              :create="isAddForm"
              v-model="form.code"
              :forcedEditable="isCopyForm"
              placeholder="请输入过程检编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item class="form-item" v-if="isCable" label="设备" prop="devicedCode">
            <el-select
              v-model="form.devicedCode"
              placeholder="请选择设备"
              filterable
              class="!w-full"
              @change="deviceChange"
            >
              <el-option
                v-for="(device, index) in deviceStore.devices"
                :key="index"
                :label="device.deviceName"
                :value="device.deviceCode"
            /></el-select>
          </el-form-item>
          <el-form-item class="form-item" v-if="!isCable" label="设备S" prop="deviceIds">
            <el-select v-model="form.deviceIds" placeholder="请选择设备" filterable multiple class="!w-full">
              <el-option
                v-for="(device, index) in deviceStore.devices"
                :key="index"
                :label="device.deviceName"
                :value="device.id"
            /></el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item class="form-item" label="检验时间" prop="checkTime">
            <el-date-picker
              v-model="form.checkTime"
              type="datetime"
              placeholder="请选择时间"
              class="!w-full"
              :disabled-date="disabledNowAfterDate"
              :disabled-hours="disabledHours"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import SerialNumber from "@/components/SerialNumber";
import { reactive, ref, computed, watchEffect, onMounted, nextTick, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { useDeviceStore } from "@/store/modules/device";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { requiredMessage } from "@/utils/form";
import { PROCESS_INSPECT_NO_CODE, SPECIAL_CHARACTER_REGEXP } from "@/consts";
import { disabledNowAfterDate, disabledTimeHours } from "@/utils/disabledDate";
import { getProductionProcessListByCategoryCodeAndProductionStage } from "@/api/process";
import { useLoadingFn } from "@/utils/useLoadingFn";

const props = defineProps<{
  subClassCode: string;
  processId: string;
}>();

// store数据
const productionProcessInspecStore = useProductionProcessInspecStore();
// 设备信息
const deviceStore = useDeviceStore();
// 表单数据
const processInspecRef = ref<FormInstance>();
const form = reactive({
  code: "",
  devicedId: "",
  devicedName: "",
  devicedCode: "",
  checkTime: "",
  deviceIds: []
});
const isAddForm = computed(() => productionProcessInspecStore.mode === "add");
const isCopyForm = computed(() => productionProcessInspecStore.mode === "copy");
const isEditForm = computed(() => productionProcessInspecStore.mode === "edit");
const serialNumberRef = ref<InstanceType<typeof SerialNumber>>();

const rules = reactive<FormRules>({
  code: [
    { message: requiredMessage("过程检测编号"), trigger: "change", required: true },
    { message: "过程检测编号 不允许输入符号", trigger: "change", pattern: SPECIAL_CHARACTER_REGEXP }
  ],
  // devicedCode: [{ required: false, message: "请选择设备", trigger: "change" }],
  checkTime: [{ required: false, message: "请选择时间", trigger: "change" }]
});

const loading = ref(false);

watchEffect(() => {
  // 编辑
  if (isEditForm.value) {
    Object.assign(form, { ...productionProcessInspecStore.detailProductionCheckInfo });
  }
});

watch(
  [isCopyForm, () => productionProcessInspecStore.detailProductionCheckInfo],
  () => {
    if (isCopyForm.value && productionProcessInspecStore.detailProductionCheckInfo.id) {
      Object.assign(form, { ...productionProcessInspecStore.detailProductionCheckInfo }, { code: "" });
      nextTick(() => {
        refreshNo();
      });
    }
  },
  {
    immediate: true
  }
);

const refreshNo = async () => {
  await serialNumberRef.value?.refreshNo();
};

// 设备改变
const deviceChange = (deviceCode: string) => {
  const selectDevice = deviceStore.devices?.filter(item => item.deviceCode === deviceCode);
  const { deviceName, id } = selectDevice[0];
  form.devicedName = deviceName;
  form.devicedId = id;
};

// 返回表单数据之前校验表单
const vaildProcessForm = async () => {
  const formEl: FormInstance | undefined = processInspecRef.value;
  if (!formEl) return;
  return await formEl.validate(() => {});
};

const isCable = props.subClassCode.startsWith("10");

// 重置表单
const resetForm = () => {
  processInspecRef.value.resetFields();
};

const disabledHours = () => {
  const day = new Date(form.checkTime).getDay();
  return disabledTimeHours(day);
};

/**
 * @description: 查询设备是否必填
 */
const getRequireDevice = useLoadingFn(async () => {
  if (!props.subClassCode) {
    return;
  }
  const { data } = await getProductionProcessListByCategoryCodeAndProductionStage(props.subClassCode);
  // 获取当前工序
  const curProcess = data.find(({ id }) => id === props.processId);
  rules.devicedCode[0].required = curProcess ? curProcess.requireDevice : false;
}, loading);

onMounted(getRequireDevice);

defineExpose({
  processInspecRef,
  form,
  resetForm,
  vaildProcessForm,
  refreshNo
});
</script>

<style scoped lang="scss"></style>
