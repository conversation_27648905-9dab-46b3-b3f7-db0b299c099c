import * as api from "@/api/technical-standard";
import { ICollectionParams, IDetailBatchCollectItem, ISaveStandardReq, ITechnicalStandardStockReq } from "@/models";
import { isEmpty } from "@pureadmin/utils";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";

export const useTechnicalStandardStore = defineStore({
  id: "technical-standard-store",
  state: () => ({
    standardList: []
  }),
  actions: {
    /** 填报--获取技术标准信息 */
    async getTechnicalStandardInfo(productionId: string) {
      return (await api.getTechnicalStandardList(productionId)).data;
    },
    /** 选用技术标准库列表 */
    async getTechnicalStandardStock(productionId: string, params: ITechnicalStandardStockReq) {
      const queryParams = omitBy(params, val => isEmpty(val));
      return await api.getTechnicalStandardStock(productionId, queryParams);
    },
    /** 从订单中复制--订单列表 */
    async getTechnicalStandardFromOrder(productionId: string, params: ITechnicalStandardStockReq) {
      const queryParams = omitBy(params, val => isEmpty(val));
      return await api.getTechnicalStandardFromOrder(productionId, queryParams);
    },

    /** 从标准库中选用 / 订单复制 */
    async saveTechnicalStandardStock(params: { productionOrderId: string; standardId: string }) {
      return await api.saveSelectedStandard(params);
    },

    /** 编辑--技术标准 */
    async saveEditStandardInfo(id: string, params: ISaveStandardReq) {
      return await api.saveEditStandard(id, params);
    },

    /** 工序列表 */
    async getProcessFromStandard(standardId: string) {
      return (await api.getStandardDetailProcess(standardId)).data;
    },
    /** 标准库详情信息列表可编辑 */
    async getStandardDetailInfoList(params: ICollectionParams) {
      const queryParams = omitBy(params, val => isEmpty(val));
      return await api.getStandardDetailList(queryParams);
    },

    /** 详情--保存技术标准详情信息 */
    async saveStandardDetailInfo(params: IDetailBatchCollectItem[]) {
      return await api.saveStandardDetailInfo(params);
    },

    /** 删除技术标准*/
    async delTechnicalStandard(id: string) {
      return await api.delStandard(id);
    }
  }
});
