import { IEmployee, IEmployeeForm, IEmployeeReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/enterprise";

export const useEmployeeStore = defineStore({
  id: "cx-enterprise-employee",
  state: () => ({
    employees: [] as Array<IEmployee>,
    total: 0 as number,
    employee: {} as IEmployee,
    employeeForm: {} as IEmployeeForm,
    loading: false as boolean
  }),
  actions: {
    async queryEmployee(params?: IEmployeeReq) {
      this.loading = true;
      const res = await api.queryEmployee(params);
      this.employees = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async searchEmployee(params?: IEmployeeReq) {
      return await api.queryEmployee(params);
    },

    async addEmployee(data: IEmployeeForm) {
      return api.addEmployee(data);
    },

    async editEmployee(data: IEmployeeForm) {
      return api.editEmployee(data);
    },

    async deleteEmployee(id: string) {
      return api.deleteEmployee(id);
    },

    async getEmployeeDetailById(id: string) {
      const res = await api.getEmployeeDetail(id);
      this.employee = res.data;
      return this.employee;
    },

    setAddEmployeeDetail(data?: IEmployee) {
      this.employee = data;
    },

    setEmployeeForm(data: Partial<IEmployeeForm>) {
      this.employeeForm = data;
    }
  }
});
