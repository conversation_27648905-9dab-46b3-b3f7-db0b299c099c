import { AggModelEnum } from "@/enums/device/device.enum";
import { IPagingReq } from "../i-paging-req";
import { IRawMaterialCheckCollectionItem } from "../raw-material/i-raw-material-res";

/**
 * 获取生产过程工艺列表
 */
export interface ISearchProductionProcessList extends IPagingReq {
  productionId?: string;
  workOrderId?: string;
  processId?: string;
  processCode?: string;
  deviceId?: string;
  deviceCode?: string;
  purchaseOrderLineId?: string;
  purchaseOrderId?: string;
}

/** 工序数据 */
export interface IProductionProcessDetail {
  dataCode: string;
  identityCode: string;
  dataValue: string;
}

/**
 * 生产过程工艺检测
 */
export interface IProductionProcess {
  id?: string;
  code?: string;
  deviceId?: string;
  checkTime?: string;
  uploadTime?: string;
  testingPersonnel?: string;
  information?: string;
  remarks?: string;
  processId?: string;
  productionId?: string;
  productionOrderNo?: string;
  deviceCode?: string;
  deviceName?: string;
  workOrderId?: string;
  purchaseOrderId?: string;
  purchaseOrderLineId?: string;
  tenantId?: string;
  processCode?: string;
  processName?: string;
  deviceIds?: string[];
}

/**
 * 保存生产过程工艺检测
 */
export interface IAddProductionProcess extends IProductionProcess {
  processDetailDataValue?: IProductionProcessDetail;

  /** 产品型号  */
  model?: string;
  /** 质量追溯码  */
  qualityTraceCode?: string;
  /** 生产批次号  */
  productBatchNo?: string;
  /** 生产完成时间  */
  productFinishTime?: string;
  /** 加工方式  */
  processMethod?: string;
  /** 加工件数  */
  processNumber?: number;
}

/** 生产过程工艺列表 */
export interface IProductionProcessList extends IProductionProcess {
  processDetailDataValue?: IRawMaterialCheckCollectionItem[];
  /** 产品型号  */
  model?: string;
  /** 质量追溯码  */
  qualityTraceCode?: string;
  /** 生产批次号  */
  productBatchNo?: string;
  /** 生产完成时间  */
  productFinishTime?: string;
  /** 加工方式  */
  processMethod?: string;
  /** 加工件数  */
  processNumber?: number;
}

/** 自动采集实时数据 */
export interface IAutoCollectChartReq {
  deviceCode: string;
  deviceId?: string;
  processId?: string;
  timeFrom?: string;
  timeEnd?: string;
  /** 码点 */
  points?: string[];
  /** 统计方式 */
  aggModel?: AggModelEnum;
  /** 统计函数 陈翼要求传的 固定值 */
  func?: "MEAN";
}

export interface IPoints {
  [key: string]: string;
}

export interface IItem {
  deviceNo: string;
  time: string;
  points: IPoints;
}

export interface IAutoCollectChartRes {
  items: IItem[];
  points: string[];
}
