<template>
  <div class="h-full flex flex-col items-center p-8">
    <div>
      <range-list ref="rangeListRef" :mode="mode" />
      <div class="flex justify-end mt-2">
        <el-button
          v-if="mode === 'browse'"
          v-auth="PermissionKey.qualityTracing.qualityTracingLevelEdit"
          type="primary"
          @click="mode = 'edit'"
        >
          编辑
        </el-button>
        <div v-if="mode === 'edit'">
          <el-button type="primary" :loading="saveLoading" @click="handleSave"> 保存 </el-button>
          <el-button :loading="saveLoading" @click="handleCancel"> 取消 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { PermissionKey } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { usePageStoreHook } from "@/store/modules/page";
import RangeList from "./range-list/index.vue";
import { queryQualityLevelList, editQualityLevelList } from "@/api/quality-tracing";
import { responseDataToRangeList, rangeListToEditDTO } from "./converter";
import { EditQualityLevelParams, QualityLevelListItem } from "@/models/quality-tracing";
import { ElMessage } from "element-plus";
import { cloneDeep } from "@pureadmin/utils";

/**
 * 质量等级配置页
 */

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);

const mode = ref<"browse" | "edit">("browse");
const loading = ref(false);
const saveLoading = ref(false);
const rangeListRef = ref<InstanceType<typeof RangeList>>();
const initList = ref<Array<QualityLevelListItem>>([]);

/**
 * @description: 请求列表
 */
const requestList = useLoadingFn(async () => {
  const res = await queryQualityLevelList();
  initList.value = cloneDeep(res.data);
  return res.data;
}, loading);

/**
 * @description: 请求保存
 */
const requestSave = useLoadingFn(async (params: EditQualityLevelParams) => {
  const res = await editQualityLevelList(params);
  return res.data;
}, saveLoading);

async function handleSave() {
  const valid = await rangeListRef.value?.validateForm();
  if (!valid) {
    return;
  }
  const list = rangeListRef.value?.getFormValue();
  const params: EditQualityLevelParams = {
    data: rangeListToEditDTO(list)
  };
  const res = await requestSave(params);
  if (res) {
    ElMessage.success("保存成功");
  }
  requestList();
  mode.value = "browse";
}

/**
 * @description: 取消编辑
 */
function handleCancel() {
  mode.value = "browse";
  rangeListRef.value?.initFormValue(responseDataToRangeList(initList.value));
}

onMounted(async () => {
  const List = await requestList();
  const data = responseDataToRangeList(List);
  nextTick(() => {
    rangeListRef.value?.initFormValue(data);
  });
});
</script>

<style scoped></style>
