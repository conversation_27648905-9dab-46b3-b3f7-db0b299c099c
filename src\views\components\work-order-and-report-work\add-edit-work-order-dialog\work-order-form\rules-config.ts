import { computed } from "vue";
import type { FormRules } from "element-plus";
import type { ICreateWorkOrder } from "@/models";

export interface ICreateWorkOrderExe extends Omit<ICreateWorkOrder, "processIds"> {
  planDateArray?: any;
  subclassCode: string;
  processIds: Array<string>;
}

/**
 * @description: 生成工单的动态表单验证规则
 */
export function genWorkOrderFormRules(formValue: ICreateWorkOrderExe) {
  return computed<FormRules>(() => {
    return {
      woNo: [{ message: "工单编号不能为空", trigger: "change" }],
      processIds: [{ required: true, type: "array", message: "工序不能为空", trigger: "change" }],
      amount: [{ required: true, message: "生产数量不能为空", trigger: "change" }],
      unit: [{ required: true, message: "计量单位不能为空", trigger: "change" }],
      materialsCode: [{ required: true, message: "物料编号不能为空", trigger: "change" }],
      materialName: [{ required: true, message: "物料名称不能为空", trigger: "change" }],
      materialDesc: [{ required: true, message: "物料描述不能为空", trigger: "change" }],
      materialUnit: [{ required: true, message: "物料单位不能为空", trigger: "change" }],
      specificationType: [{ required: true, message: "规格型号不能为空", trigger: "change" }],
      voltageLevel: [{ message: "电压等级不能为空", trigger: "change" }],
      woStatus: [{ required: true, message: "工单状态不能为空", trigger: "change" }],
      planDateArray: [{ type: "array", required: true, message: "计划日期不能为空", trigger: "change" }],
      actualStartDate: [
        {
          required: Boolean(formValue.actualFinishDate),
          message: "实际开始日期不能为空",
          trigger: "change"
        }
      ],
      actualFinishDate: [{ required: false }],
      processRouteNo: [{ required: false }]
    };
  });
}
