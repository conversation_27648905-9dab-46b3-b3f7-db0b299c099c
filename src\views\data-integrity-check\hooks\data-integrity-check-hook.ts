import { IDataIntegrityCheckParams, IDataIntegrityCheckTable } from "@/models";
import { useDataIntegrityCheckStore } from "@/store/modules";

/**
 * 数据完整性检查的hook
 */

export const useDataIntegrityCheckHook = () => {
  const dataIntegrityCheckStore = useDataIntegrityCheckStore();

  // 根据物资品类的ID获取数据完整性检测的列表数据
  async function queryDataIntegrityCheckDataList(params: IDataIntegrityCheckParams) {
    const dataIntegrityCheckTable: IDataIntegrityCheckTable =
      await dataIntegrityCheckStore.queryDataIntegrityCheckDataList(params);
    return dataIntegrityCheckTable;
  }

  return {
    queryDataIntegrityCheckDataList
  };
};
