<template>
  <el-descriptions class="describe-content half" :column="2">
    <el-descriptions-item label="摄像头编号">
      {{ monitorDevice.no }}
    </el-descriptions-item>
    <el-descriptions-item label="摄像头名称">
      {{ monitorDevice.name }}
    </el-descriptions-item>
    <el-descriptions-item label="NVR IP地址">
      {{ monitorDevice.nvrIp }}
    </el-descriptions-item>
    <el-descriptions-item label="状态">
      {{ formatEnum(monitorDevice.status, MonitorDeviceStatusEnum, "MonitorDeviceStatusEnum") }}
    </el-descriptions-item>
    <el-descriptions-item label="品牌">
      {{ formatEnum(monitorDevice.brandCode, MonitorDeviceBrandEnum, "MonitorDeviceBrandEnum") }}
    </el-descriptions-item>
    <el-descriptions-item label="通道号">
      {{ monitorDevice.channel }}
    </el-descriptions-item>
    <el-descriptions-item label="账号">
      {{ monitorDevice.account }}
    </el-descriptions-item>
    <el-descriptions-item label="密码">
      {{ monitorDevice.password }}
    </el-descriptions-item>
    <el-descriptions-item label="端口号">
      {{ monitorDevice.port }}
    </el-descriptions-item>
    <el-descriptions-item label="码流">
      {{ formatEnum(monitorDevice.subType, MonitorDeviceSubTypeEnum, "MonitorDeviceSubTypeEnum") }}
    </el-descriptions-item>
  </el-descriptions>
</template>

<script setup lang="ts">
import { IMonitorDevice } from "@/models";
import { formatEnum } from "@/utils/format";
import { MonitorDeviceStatusEnum, MonitorDeviceBrandEnum, MonitorDeviceSubTypeEnum } from "@/enums";
withDefaults(
  defineProps<{
    monitorDevice: Partial<IMonitorDevice>;
  }>(),
  {
    monitorDevice: () => ({
      id: undefined,
      no: undefined,
      name: undefined,
      nvrIp: undefined,
      status: undefined,
      brandCode: undefined,
      channel: undefined,
      account: undefined,
      password: undefined,
      port: undefined,
      subType: undefined
    })
  }
);
</script>

<style scoped lang="scss"></style>
