<template>
  <el-dialog
    v-model="visible"
    title="关联销售订单"
    destroy-on-close
    class="middle"
    :close-on-click-modal="false"
    @open="dialogOpen"
    @close="dialogClose"
  >
    <el-steps :active="active" class="mb-5">
      <el-step title="选择采购订单行" />
      <el-step title="关联销售订单行" />
      <el-step title="完成" />
    </el-steps>

    <PurchaseOrderLineSelectTable v-show="isFirstStep" v-model="purchaseLineId" :height="500" />
    <SalesOrderLineSelectTable
      :purchaseLineId="purchaseLineId"
      v-if="isSecondStep"
      :only-same-sub-class-and-buyer="true"
      v-model="salesLines"
    />
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="next" :disabled="!purchaseLineId" v-show="isFirstStep">下一步</el-button>
      <el-button @click="prev" :disabled="!purchaseLineId" v-show="isSecondStep">上一步</el-button>
      <el-button
        type="warning"
        @click="handleSaveAndContinue"
        :loading="continueLoading"
        :disabled="!selectedSalesOrderLine"
        v-show="isSecondStep"
        >保存，并继续关联</el-button
      >
      <el-button
        type="primary"
        @click="handleSaveAndClose"
        :loading="saveLoading"
        :disabled="!selectedSalesOrderLine"
        v-show="isSecondStep"
        >保存</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import PurchaseOrderLineSelectTable from "../purchase-order-line/purchase-order-line-select-table.vue";
import SalesOrderLineSelectTable from "../sales-order-line/sales-order-line-select-table.vue";
import { ILinkSalesOrderLines } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import {
  usePurchaseOrderDetailSalesOrderLineStore,
  usePurchaseOrderDetailSalesOrderStore
} from "@/store/modules/purchase-order-detail";
import { usePurchaseOrderLink } from "@/views/order/purchase-order/detail/src/hooks/usePurchaseOrderLink";
import { usePurchaseOrderDetailStore } from "@/store/modules";

const props = defineProps(["modelValue"]);
const emit = defineEmits(["update:modelValue"]);

let shouldRefresh = false;
const salesOrderLineStore = usePurchaseOrderDetailSalesOrderLineStore();
const salesOrderStore = usePurchaseOrderDetailSalesOrderStore();
const purchaseOrderStore = usePurchaseOrderDetailStore();
const { refreshLink } = usePurchaseOrderLink();
const active = ref(0);
const purchaseLineId = ref<string>();
const salesLines = ref<Array<ILinkSalesOrderLines>>([]);
const saveLoading = ref(false);
const continueLoading = ref(false);
const handleSaveAndClose = useLoadingFn(saveAndClose, saveLoading);
const handleSaveAndContinue = useLoadingFn(saveAndContinue, continueLoading);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});
const isFirstStep = computed(() => active.value === 0);
const isSecondStep = computed(() => active.value === 1);
const selectedSalesOrderLine = computed(() => salesLines.value.length);

function dialogOpen() {
  shouldRefresh = false;
  reset();
}

function dialogClose() {
  if (shouldRefresh) {
    refreshLink();
    salesOrderStore.refreshSalesOrders();
  }
}

function cancel() {
  visible.value = false;
}

function next() {
  active.value = 1;
}

function prev() {
  active.value = 0;
  salesLines.value = [];
}

async function saveAndContinue() {
  await save();
  await purchaseOrderStore.refreshPurchaseOrder();
  reset();
}

async function saveAndClose() {
  await save();
  ElMessage.success("关联成功");
  cancel();
}

async function save() {
  await salesOrderLineStore.linkSalesOrderLine(purchaseLineId.value, salesLines.value);
  shouldRefresh = true;
}

function reset() {
  active.value = 0;
  purchaseLineId.value = null;
  salesLines.value = [];
}
</script>

<style scoped></style>
