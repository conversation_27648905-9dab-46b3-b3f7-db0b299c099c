<template>
  <div class="inline-block">
    <el-button
      v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationAddMaterial"
      type="primary"
      :icon="Plus"
      @click="openDialog"
    >
      新增物料
    </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="选择物料"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <select-list ref="selectListRef" :quality-id="qualityId" />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { addApplicationMaterial } from "@/api/quality-tracing/application-material";
import SelectList from "./select-list/index.vue";
import { AddApplicationMaterialParams } from "@/models/quality-tracing";
import { useLoadingFn } from "@/utils/useLoadingFn";

const loading = ref(false);

const props = defineProps<{
  /** 质量规范id */
  qualityId: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const saveLoading = ref(false);

const selectListRef = ref<InstanceType<typeof SelectList>>();

const requestAddApplicationMaterial = useLoadingFn(async () => {
  const selectedList = selectListRef.value?.getSelectedList();
  if (!selectedList || selectedList.length === 0) {
    ElMessage.warning("请选择物料");
    return false;
  }
  const params: AddApplicationMaterialParams = {
    qualitySpecificationId: props.qualityId,
    materialIdList: selectedList.map(item => item.id)
  };
  const res = await addApplicationMaterial(params);
  return res.data;
}, saveLoading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const res = await requestAddApplicationMaterial();

  if (!res) {
    return;
  }

  // 处理保存后续事件
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({
    message: "新增成功",
    type: "success"
  });
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
