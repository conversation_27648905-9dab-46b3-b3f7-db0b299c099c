import { useTableConfig } from "@/utils/useTableConfig";
import { usePurchaseOrderLineStore } from "@/store/modules";
import { reactive, ref, shallowRef } from "vue";
import { IPurchaseLineCount, IPurchaseOrderLineQueryParams, IPurchaseOrderLineRes, ISortReq } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IPurchaseOrderLineDataCtx } from "../tokens/purchase-order-line-data-token";
import { ISearchForm } from "../models";
import { OrderSyncPriorityEnum } from "@/enums";
import { handleOrderType } from "@/utils/sortByOrderType";

export function usePurchaseOrderLineData(): IPurchaseOrderLineDataCtx {
  const lineStore = usePurchaseOrderLineStore();
  const loading = ref(false);
  const data = shallowRef<Array<IPurchaseOrderLineRes>>([]);
  const selections = ref<Array<IPurchaseOrderLineRes>>([]);
  const { pagination } = useTableConfig();
  const searchForm = reactive<ISearchForm>({});
  const sortParams = ref<ISortReq>({});
  const purchaseLineCount = ref<IPurchaseLineCount>({
    total: 0,
    priorityCount: 0
  });
  const syncPriority = ref<OrderSyncPriorityEnum>(OrderSyncPriorityEnum.COMMON_PRIORITY);

  const handleQueryPurchaseOrderLines = useLoadingFn(_queryPurchaseOrderLines, loading);

  function disablePagination() {
    pagination.disabled = true;
  }
  function enablePagination() {
    pagination.disabled = false;
  }
  function updateCurrentPage(currentPage: number) {
    pagination.currentPage = currentPage;
    void refreshPurchaseOrderLines();
  }
  function updatePageSize(pageSize: number) {
    pagination.pageSize = pageSize;
    pagination.currentPage = 1;
    void refreshPurchaseOrderLines();
  }

  function refreshPurchaseOrderLines() {
    const params: IPurchaseOrderLineQueryParams = {
      ...searchForm,
      ...sortParams.value,
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      priority: syncPriority.value
    };
    lineStore.queryPurchaseCount(params).then(res => {
      purchaseLineCount.value = res.data;
    });
    return handleQueryPurchaseOrderLines(params);
  }

  async function _queryPurchaseOrderLines(params: IPurchaseOrderLineQueryParams) {
    selections.value.length = 0;
    const { list, total } = await lineStore
      .queryPurchaseOrderLines(params)
      .then(res => res.data || { list: [], total: 0 });
    pagination.total = total;
    data.value = list;
  }

  function sortChange({ prop, order }) {
    if (order) {
      sortParams.value = {
        orderByField: prop,
        orderByType: handleOrderType(order)
      };
    } else {
      sortParams.value = {
        orderByField: "",
        orderByType: ""
      };
    }

    pagination.currentPage = 1;
    refreshPurchaseOrderLines();
  }

  return {
    data,
    selections,
    loading,
    pagination,
    searchForm,
    syncPriority,
    purchaseLineCount,
    enablePagination,
    disablePagination,
    updateCurrentPage,
    updatePageSize,
    refreshPurchaseOrderLines,
    sortChange
  };
}
