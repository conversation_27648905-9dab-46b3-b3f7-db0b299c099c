import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IProductionDocument, IProductionDocumentForm, IProductionDocumentReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryProductionDocument = (data: IProductionDocumentReq) => {
  const url: string = withApiGateway("admin-api/southgrid/productions/pageList");
  return http.post<IProductionDocumentReq, IListResponse<IProductionDocument>>(url, {
    data
  });
};

/** 查询 列表  */
export const queryProductionDocumentList = (data: IProductionDocumentReq) => {
  const url: string = withApiGateway("admin-api/southgrid/productions/list");
  return http.post<IProductionDocumentReq, IResponse<Array<IProductionDocument>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getProductionDocumentById = (id: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/productions/${id}`);
  return http.get<string, IResponse<IProductionDocument>>(url);
};

/** 新增 */
export const createProductionDocument = (data: IProductionDocumentForm) => {
  return http.post<IProductionDocumentForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/productions/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateProductionDocument = (data: IProductionDocumentForm) => {
  return http.put<IProductionDocumentForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/productions/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteProductionDocumentById = (data: IProductionDocumentForm) => {
  return http.post<{ ids: string; reason: string }, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/productions/deleteProductions`),
    {
      data: {
        ids: data.id,
        reason: data.reason
      }
    }
  );
};
