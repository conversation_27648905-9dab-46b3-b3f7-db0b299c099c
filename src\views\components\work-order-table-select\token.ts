import { PaginationProps } from "@pureadmin/table";
import { IPagingReq, IWorkOrder } from "@/models";
import { InjectionKey } from "vue";
import * as api from "@/api/work-order";

export interface IWorkOrderTableSelectCtx {
  pagination?: PaginationProps;
  keyword?: string;
  data: Array<IWorkOrder>;
  loading: boolean;
  selectedId?: string;
  selectedWorkOrder?: IWorkOrder;
  refresh(): void;
}

export interface IWorkOrderQueryParams extends IPagingReq {
  id?: string;
  keyWords?: string;
  subClassCode?: string;
}

export function queryWorkOrder(param: IWorkOrderQueryParams) {
  if (param.keyWords) {
    param.keyWords = param.keyWords.trim();
  }
  return api.queryWorkOrderForEJJ(param);
}

export const workOrderSelectKey: InjectionKey<IWorkOrderTableSelectCtx> = Symbol("Work Order Table Select");
