<template>
  <el-dialog
    v-model="drawerVisible"
    title="同步明细"
    width="700px"
    align-center
    @close="closeDialog"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-timeline v-if="histories?.length" class="pl-2">
      <el-timeline-item hollow v-for="history in histories" :key="history.id" type="primary">
        <p class="text-middle text-primaryText">{{ history.interfaceName || "--" }}</p>
        <div class="mt-2 text-secondary text-sm flex items-center">
          <span class="text-[13px]"> 数据修改时间 :</span>
          <span class="mr-5"> {{ formatDate(history.addTime, fullDateFormat) }} </span>
          <template v-if="history.pullTime">
            <span class="text-[13px]">网关拉取时间 : </span>
            <span> {{ formatDate(history.pullTime, fullDateFormat) }}</span>
          </template>
          <el-button link size="small" type="primary" class="ml-2.5" @click="showMessage(history.pullData)">
            查看报文
          </el-button>
        </div>
      </el-timeline-item>
    </el-timeline>
    <CxEmpty v-else />
    <SynchronousDetailsMessage
      :dialog-show="dialogMessage"
      :pull-data="selectPullData"
      @close="dialogMessage = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { formatDate } from "@/utils/format";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import SynchronousDetailsMessage from "./synchronous-details-message.vue";
import { fullDateFormat } from "@/consts";
import { IntermediateApi } from "@/api/e-capital-construction/engineering-info";

// const loading = ref(false);

const drawerVisible = ref(false);
const emits = defineEmits(["close"]);
const selectPullData = ref("");

const props = defineProps<{
  dialogShow: boolean;
  batchId?: string;
}>();

const histories = ref([]);
const dialogMessage = ref(false);

// 订阅弹窗开启状态，请求数据
watch(
  () => props.dialogShow,
  newValue => {
    if (newValue) {
      drawerVisible.value = newValue;
      initList();
    }
  }
);

async function initList() {
  const { data } = await IntermediateApi(props.batchId || "1926946822441336832");
  histories.value = data;
}

const showMessage = (data: string) => {
  dialogMessage.value = true;
  selectPullData.value = data;
};

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
  emits("close");
}
</script>

<style scoped></style>
