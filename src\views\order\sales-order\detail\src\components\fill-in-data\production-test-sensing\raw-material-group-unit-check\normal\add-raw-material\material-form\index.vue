<template>
  <div class="cx-form">
    <div class="inspect-info">
      <TitleBar title="原材料检信息" class="mb-2" />
      <div class="form">
        <el-form
          ref="inspectFormInstance"
          label-width="110px"
          label-position="right"
          require-asterisk-position="right"
          :model="inspectForm"
          :rules="rules"
        >
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="检验批次号" prop="inspectBatchNo">
                <el-input v-model="inspectForm.inspectBatchNo" placeholder="请输入检验批次号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验日期" prop="inspectDate">
                <el-date-picker
                  class="!w-full"
                  v-model="inspectForm.inspectDate"
                  type="date"
                  placeholder="请选择日期"
                  :disabled-date="disabledNowAfterDate"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="24">
              <el-form-item label="备注" prop="inspectRemark">
                <el-input v-model="inspectForm.inspectRemark" :rows="2" type="textarea" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="inspect-collection-items">
      <div class="mb-5">
        <TitleBar title="原材料检测" class="mb-3" />
        <DynamicForm ref="dynamicTableFormRef" :dynamic-form-data="resFormData" :edit-mode="true" />
      </div>
      <div>
        <div class="flex items-center my-3">
          <TitleBar title="原材料报告" />
          <div class="text-sm text-secondary ml-2">
            <span class="whitespace-nowrap ml-1">{{ FILE_TYPE_TEXT }}</span>
          </div>
        </div>
        <div class="mx-5">
          <UploadFrom ref="dynamicFileRef" :dynamic-file-data="resFileData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import DynamicForm from "@/components/DynamicForm";
import UploadFrom from "@/components/Upload";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { useRawMaterialCheckInfoHook } from "@/views/config/raw-material/hook/useRawMaterialCheck";
import { ref, reactive, watchEffect } from "vue";
import { FormRules, FormInstance } from "element-plus";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { FILE_TYPE_TEXT } from "@/consts";

const rawMaterialInspectStore = useRawMaterialGroupUnitStore();
// 检验信息
const inspectFormInstance = ref();
const inspectForm = reactive<{
  inspectBatchNo: string;
  inspectDate: string;
  inspectRemark: string;
}>({
  inspectBatchNo: null,
  inspectDate: null,
  inspectRemark: null
});
const rules = reactive<FormRules>({
  inspectBatchNo: [{ required: true, message: "请输入检验批次号", trigger: "change" }],
  inspectDate: [{ required: true, message: "请选择检验日期", trigger: "change" }]
});

watchEffect(() => {
  if (!rawMaterialInspectStore.isAdd) {
    Object.assign(inspectForm, { ...rawMaterialInspectStore.selectRawMaterialBaseInfo });
  }
});

// 获取采集项目信息
const collectionData = rawMaterialInspectStore.rawMaterialCheckCollectionItem;
// 原材料检测项数据
const { getRawMaterialCheckFormData, getRawMaterialCheckFileData } = useRawMaterialCheckInfoHook();
const resFormData = getRawMaterialCheckFormData(collectionData);
const resFileData = getRawMaterialCheckFileData(collectionData);
const dynamicTableFormRef = ref();
const dynamicFileRef = ref();

/** 校验原材料检信息 */
const submitForm = async () => {
  const formEl: FormInstance | undefined = inspectFormInstance.value;
  if (!formEl) return;
  return await formEl.validate(() => {});
};

// 验证表单信息
const validRawMaterialTest = async () => {
  // 校验原材料检信息
  if (!(await submitForm())) {
    rawMaterialInspectStore.materialTestFromValid = false;
    return;
  }
  const dynamicFormIns = dynamicTableFormRef.value;
  const formData = await dynamicFormIns.payloadFormData();
  const dynamicFileIns = dynamicFileRef.value;
  const uploadData = dynamicFileIns.payloadUploadData();
  if (formData && uploadData) {
    // 更新是否可以保存并继续的标识
    rawMaterialInspectStore.materialTestFromValid = true;
    // 更新store里面的表单数据
    updateMaterialTestFormStore(formData, uploadData);
  } else {
    rawMaterialInspectStore.materialTestFromValid = false;
  }
};

// 更新store里面的表单数据
const updateMaterialTestFormStore = (formData, uploadData) => {
  const saveData = {
    ...formData,
    ...uploadData,
    inspectForm: inspectForm
  };
  rawMaterialInspectStore.selectRawMaterialTestInfo = saveData;
};

defineExpose({
  validRawMaterialTest
});
</script>

<style scoped lang="scss"></style>
