<template>
  <Step :entryOrder="entryOrder" :steps="steps" @gotoEvent="goto" />
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import Step from "@/views/order/components/detail/step.vue";
import { OrderType } from "@/enums/order";
import { useSalesOrderDetailStore, useSalesStateGridOrderSyncListStore } from "@/store/modules";
import { useSocket } from "@/utils/useSocket";
import { RefreshSceneEnum, SocketEventEnum } from "@/enums";

defineOptions({
  name: "PurchaseOrderStep"
});

const socket = useSocket();

const salesOrderDetailStore = useSalesOrderDetailStore();

const salesStateGridOrderSyncList = useSalesStateGridOrderSyncListStore();

const steps = computed(() => {
  return salesOrderDetailStore.steps || [];
});
const entryOrder = OrderType.SALES;

function listenStepStatusChange() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const matching = salesStateGridOrderSyncList.syncs.find(item => item.id === event.purchaseOrderItemId);
    if (event.type !== RefreshSceneEnum.SYNC_DATA_LIST || !matching) {
      return;
    }
    salesOrderDetailStore.refreshStepStatusHandle();
  });
}

function goto(key) {
  salesOrderDetailStore.goto(key);
}

onMounted(listenStepStatusChange);
</script>
