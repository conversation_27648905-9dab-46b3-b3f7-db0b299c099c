import { getSyncStockStatus, syncAllByOneKey } from "@/api/stock-data-center/sync-stock/sync-stock";
import { PermissionKey } from "@/consts";
import { IResponse } from "@/models";
import { IStockSync, ISyncStockTagType } from "@/models/stock-data-center/stock-sync/i-sync-stock";
import { ESyncModuleKey } from "@/views/stock-data-center/stock-sync/component/type";
import { defineStore } from "pinia";

export const useSyncStockStore = defineStore({
  id: "sync-stock-store",
  state: () => ({
    activeStepKey: null as string,
    syncStockStatus: [] as Array<ISyncStockTagType>
  }),
  actions: {
    /** 获取模块的同步状态 */
    async getSyncModuleStatus() {
      const resData = await getSyncStockStatus();
      if (Array.isArray(resData.data) && resData.data?.length) {
        this.syncStockStatus = resData.data.map((item: ISyncStockTagType) => {
          item.processCode = item.syncInventoryEnum;
          switch (item.syncInventoryEnum) {
            case ESyncModuleKey.RawMaterial: {
              item.processName = "原材料库存信息";
              item.permission = PermissionKey.store.storeKeyRawMaterialView;
              break;
            }

            case ESyncModuleKey.FinishedProduct: {
              item.processName = "产成品库存信息";
              item.permission = PermissionKey.store.storeProductionView;
              break;
            }
            default: {
              item.processName = "备品备件库存信息";
              item.permission = PermissionKey.store.storeSpareProductView;
            }
          }
          return item;
        });
      }
    },

    /** 一键同步 */
    async allSyncByOneKey(paramsData: IStockSync): Promise<IResponse<string>> {
      return await syncAllByOneKey(paramsData);
    },
    /**
     * 重新同步
     */
    async againSyncStock(paramsData: IStockSync): Promise<IResponse<string>> {
      return await syncAllByOneKey(paramsData);
    }
  }
});
