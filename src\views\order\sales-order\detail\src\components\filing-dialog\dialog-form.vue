<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="cx-form">
    <el-form-item label="" prop="remark">
      <el-input
        v-model="form.remark"
        type="textarea"
        :autosize="{ minRows: 3, maxRows: 6 }"
        placeholder="请输入"
        show-word-limit
        maxlength="200"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";

const formRef = ref<FormInstance>();
const form = reactive({
  remark: undefined
});

const rules = reactive<FormRules>({
  remark: [{ required: true, message: requiredMessage("归档备注"), trigger: "change" }]
});

const validateForm = async () => {
  if (!formRef.value) return;
  return await formRef.value.validate();
};

async function getFormValue() {
  if (!(await validateForm())) {
    return;
  }
  return form;
}

defineExpose({
  getFormValue
});
</script>

<style scoped lang="scss"></style>
