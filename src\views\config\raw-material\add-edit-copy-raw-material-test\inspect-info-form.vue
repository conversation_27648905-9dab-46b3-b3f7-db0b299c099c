<template>
  <el-form
    ref="formRef"
    label-width="110px"
    label-position="right"
    require-asterisk-position="left"
    :model="form"
    :rules="rules"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <raw-material-sub-class-selector
            ref="selectorRef"
            v-model="form.subClassCode"
            :raw-material-code="rawMaterialDetail.processCode"
            @loaded="onLoaded"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="检验批次号" prop="inspectBatchNo">
          <el-input v-model="form.inspectBatchNo" placeholder="请输入检验批次号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="检验日期" prop="inspectDate">
          <el-date-picker
            class="!w-full"
            v-model="form.inspectDate"
            type="date"
            placeholder="请选择日期"
            :disabled-date="disabledNowAfterDate"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            :rows="2"
            type="textarea"
            placeholder="请输入"
            show-word-limit
            maxlength="100"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import RawMaterialSubClassSelector from "../raw-material-sub-class-selector.vue";
import { getAddRawMaterialCheckInfoByProcessId } from "@/api/production-test-sensing/raw-material-group-unit-check";
import { getRawMaterialCheckInfoByProductStePro } from "@/api/base-config/raw-material";
import { useRawMaterialCheckInfoHook } from "../hook/useRawMaterialCheck";

/**
 * 原材料检信息表单
 */

interface FormType {
  /** 物资种类 */
  subClassCode?: string;
  /** 检测批次号 */
  inspectBatchNo: string;
  /** 检测日期 */
  inspectDate: string;
  /** 备注 */
  remark: string;
}

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add" | "copy";
  /** 原材料详情 */
  rawMaterialDetail: {
    name: string;
    processName: string;
    code: string;
    processCode: string;
    modelCode: string;
  };
  /** 原材料检测详情是否加载完成 */
  rawMaterialInsectDetailLoaded: boolean;
}>();

const emits = defineEmits<{
  // 设置原材料检测项列表
  (e: "setInspectItemList", value: any): void;
  // 初始化原材料检测项列表数据
  (e: "initInspectItemList"): void;
}>();

const { getRawMaterialCheckFormData } = useRawMaterialCheckInfoHook();
const selectorRef = ref<InstanceType<typeof RawMaterialSubClassSelector>>();
const form = reactive({
  subClassCode: "",
  inspectBatchNo: "",
  inspectDate: "",
  remark: ""
});
const formRef = ref<FormInstance>();

const subClassLoaded = ref(false);

const rules: FormRules = {
  inspectBatchNo: [{ required: true, message: "请输入检验批次号", trigger: "change" }],
  inspectDate: [{ required: true, message: "请选择检验日期", trigger: "change" }],
  enabled: [{ required: true, message: "请选择使用状态", trigger: "change" }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  form.subClassCode = v.subClassCode;
  form.inspectBatchNo = v.inspectBatchNo;
  form.inspectDate = v.inspectDate;
  form.remark = v.remark;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

/**
 * @description: 物资种类选择器加载完成
 */
function onLoaded() {
  subClassLoaded.value = true;
}

// 当物资种类发生切换的时候，更新检测项
// 编辑/复制模式 首次填充：如果没有物资种类，检测详情已经加载完成，适用默认检测项 + 详情数据进行填充
watch(
  () => form.subClassCode,
  async s => {
    if (s) {
      // 获取该物资种类该工序对应的采集项信息
      const InpectItemProcessId = selectorRef.value?.getInpectItemProcessId(s);
      if (InpectItemProcessId) {
        const { data } = await getAddRawMaterialCheckInfoByProcessId(
          InpectItemProcessId,
          props.rawMaterialDetail.modelCode
        );
        const inspectItemListData = getRawMaterialCheckFormData(data);
        emits("setInspectItemList", inspectItemListData);
      }
      // 详情数据填充物资种类时，使用物资种类检测项 + 详情数据进行填充
      emits("initInspectItemList");
    } else {
      // 获取默认检测项
      const { data } = await getRawMaterialCheckInfoByProductStePro(
        props.rawMaterialDetail.processCode,
        props.rawMaterialDetail.modelCode
      );
      // 获取原材料检测项列表数据
      const inspectItemListData = getRawMaterialCheckFormData(data);
      emits("setInspectItemList", inspectItemListData);
    }
  }
);

// 编辑/复制模式 首次填充：如果没有物资种类，检测详情已经加载完成，适用默认检测项 + 详情数据进行填充
watch(
  () => props.rawMaterialInsectDetailLoaded,
  async v => {
    if (v && !form.subClassCode) {
      // 获取默认检测项
      const { data } = await getRawMaterialCheckInfoByProductStePro(
        props.rawMaterialDetail.processCode,
        props.rawMaterialDetail.modelCode
      );
      // 获取原材料检测项列表数据
      const inspectItemListData = getRawMaterialCheckFormData(data);
      emits("setInspectItemList", inspectItemListData);
      // 初始化检测项列表
      emits("initInspectItemList");
    }
  },
  { immediate: true }
);

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped lang="scss"></style>
