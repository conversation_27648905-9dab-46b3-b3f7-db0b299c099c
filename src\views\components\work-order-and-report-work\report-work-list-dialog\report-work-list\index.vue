<template>
  <div>
    <div class="mb-4 flex justify-between">
      <search-bar :subclass-code="subClassCode" @search-form="onFilterParamsChange" />
    </div>
    <PureTable
      :height="300"
      :loading="loading"
      show-over-flow-tooltip
      :pagination="pagination"
      :data="dataList"
      :columns="columns"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <empty-data /> </template>
        </el-empty>
      </template>
      <template #operation="{ row }">
        <add-edit-report-work-dialog
          mode="edit"
          work-order-selector-disabled
          :work-id="workId"
          :production-id="productionId"
          :report-work-id="row.id"
          :sub-class-code="subClassCode"
          :min-class-code="row.minClassCode"
          @post-save-success="requestReportWorkList"
        />
        <el-button
          class="ml-2"
          link
          type="danger"
          v-auth="PermissionKey.form.formPurchaseWorkReportDelete"
          @click="handleDeleteReportWork(row.id)"
        >
          删除
        </el-button>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genReportWorkColumns } from "./column-config";
import { ElMessage, ElMessageBox } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";
import { queryReportWorkPaging, deleteReportWork } from "@/api/report-work";
import { PermissionKey } from "@/consts";
import { IReportWorkReq } from "@/models/report-work/i-report-work-req";
import { IReportWork } from "@/models/report-work/i-report-work";
import SearchBar from "./search-bar.vue";
import AddEditReportWorkDialog from "../../add-edit-report-work-dialog/index.vue";
import { emitter as eventBus } from "@/utils/mitt";

const props = defineProps<{
  /** 工单编号 */
  workId: string;
  /** 物资种类code */
  subClassCode: string;
  productionId: string;
}>();

const { pagination } = useTableConfig();
pagination.pageSize = 10;
const { columns } = genReportWorkColumns();
const loading = ref(false);
const dataList = ref<Array<IReportWork>>([]);
const filterParams = ref<IReportWorkReq>({} as IReportWorkReq);

function onFilterParamsChange(params: IReportWorkReq) {
  filterParams.value = params;
  pagination.currentPage = 1;
}

function genRequestParams(): IReportWorkReq {
  return {
    workId: props.workId,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    ...filterParams.value
  };
}

const requestReportWorkList = useLoadingFn(async () => {
  const params = genRequestParams();
  const { data } = await queryReportWorkPaging(params);
  dataList.value = data.list;
  pagination.total = data.total;
}, loading);

async function handleDeleteReportWork(id: string) {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  loading.value = true;
  const res = await deleteReportWork(id).finally(() => {
    loading.value = false;
  });

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  requestReportWorkList();
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
}

watch([() => pagination.currentPage, () => pagination.pageSize, filterParams], requestReportWorkList);

onMounted(requestReportWorkList);

defineExpose({
  /** 请求工单列表 */
  requestReportWorkList
});
</script>

<style scoped lang="scss"></style>
