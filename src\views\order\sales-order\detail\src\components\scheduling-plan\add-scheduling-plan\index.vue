<template>
  <el-dialog
    v-model="schedulingPlanStore.addSchedulingPlanModalVisible"
    title="新增排产计划"
    class="middle"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseSchedulingPlanDialog()"
  >
    <el-steps
      :active="schedulingPlanStore.activeCreateSchedulingPlanStep"
      class="mb-5"
      v-if="!schedulingPlanStore.isQuickCreateSchedulingPlan"
    >
      <el-step title="选择销售订单行" />
      <el-step title="填写排产计划" />
      <el-step title="完成" />
    </el-steps>
    <div
      :class="{
        hidden: schedulingPlanStore.activeCreateSchedulingPlanStep !== CreateSchedulingPlanStepEnum.selectSaleOrderLine
      }"
    >
      <SaleOrderLine />
    </div>

    <template v-if="createSchedulingPlanVisibleRef">
      <div
        :class="[
          'hidden',
          {
            block:
              schedulingPlanStore.activeCreateSchedulingPlanStep === CreateSchedulingPlanStepEnum.createSchedulingPlan
          }
        ]"
      >
        <SchedulingPlanForm ref="schedulingPlanFormRef" />
      </div>
    </template>

    <template #footer>
      <span
        :class="{
          hidden:
            schedulingPlanStore.activeCreateSchedulingPlanStep !== CreateSchedulingPlanStepEnum.selectSaleOrderLine
        }"
      >
        <el-button @click="onCancelAddOrder()">取消</el-button>
        <el-button type="primary" :disabled="!schedulingPlanStore.schedulingPlanForm?.salesLineId" @click="onNextStep()"
          >下一步</el-button
        >
      </span>

      <span
        :class="[
          'hidden',
          {
            block:
              schedulingPlanStore.activeCreateSchedulingPlanStep === CreateSchedulingPlanStepEnum.createSchedulingPlan
          }
        ]"
      >
        <el-button @click="onCancelAddOrder()">取消</el-button>
        <template v-if="!schedulingPlanStore.isQuickCreateSchedulingPlan">
          <el-button @click="onPreStep()">上一步</el-button>
          <el-button type="warning" :loading="saveAndAddLoading" @click="handleOnSaveAndAdd()"
            >保存并继续新增</el-button
          >
        </template>
        <el-button type="primary" :loading="saveLoading" @click="handleOnSave()">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { ICreateSchedulingPlan } from "@/models";
import { useSalesOrderDetailStore, useSalesSchedulingPlanStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { CreateSchedulingPlanStepEnum } from "@/enums";
import { ISchedulingPlanForm } from "./scheduling-plan-form/index.vue";
import SchedulingPlanForm from "./scheduling-plan-form/index.vue";
import SaleOrderLine from "./sale-order-line/index.vue";

let alreadyOpenCreateSchedulingPlanForm = false;
let continuousAddTag = false;
const schedulingPlanStore = useSalesSchedulingPlanStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const schedulingPlanFormRef = ref<ISchedulingPlanForm | null>();
const saveLoading = ref<boolean>(false);
const saveAndAddLoading = ref<boolean>(false);
const handleOnSave = useLoadingFn(saveSchedulingPlan, saveLoading);
const handleOnSaveAndAdd = useLoadingFn(saveAndAddSchedulingPlan, saveAndAddLoading);

const emits = defineEmits<{
  (event: "cancel"): void;
  (event: "success"): void;
}>();

const createSchedulingPlanVisibleRef = computed(() => {
  if (
    schedulingPlanStore.activeCreateSchedulingPlanStep === CreateSchedulingPlanStepEnum.createSchedulingPlan &&
    !alreadyOpenCreateSchedulingPlanForm
  ) {
    return true;
  }

  return alreadyOpenCreateSchedulingPlanForm;
});

const onCloseSchedulingPlanDialog = () => {
  resetValue();
  if (continuousAddTag) {
    continuousAddTag = false;
    emits("success");
  }

  emits("cancel");
};

const onNextStep = async () => {
  if (!schedulingPlanStore.schedulingPlanForm?.salesLineId) {
    ElMessage.error("请选择销售订单行");
    return;
  }

  schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.createSchedulingPlan);
};

async function saveAndAddSchedulingPlan() {
  const formValue: ICreateSchedulingPlan | boolean = await schedulingPlanFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  continuousAddTag = true;
  const res = await schedulingPlanStore.createSchedulingPlan({
    ...formValue,
    purchaseId: salesOrderDetailStore.salesOrder.id
  });

  if (!res.data) {
    ElMessage.success(res.msg || "网络异常");
    return;
  }

  resetValue();
  schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.selectSaleOrderLine);
  ElMessage.success("新增成功");
}

async function saveSchedulingPlan() {
  const formValue: ICreateSchedulingPlan | boolean = await schedulingPlanFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  const res = await schedulingPlanStore.createSchedulingPlan({
    ...(formValue as ICreateSchedulingPlan)
  });

  if (!res.data) {
    ElMessage.success(res.msg || "网络异常");
    return;
  }
  resetValue();
  ElMessage.success("新增成功");
  emits("success");
}

const onCancelAddOrder = () => {
  if (continuousAddTag) {
    continuousAddTag = false;
    emits("success");
  }
  emits("cancel");
  resetValue();
};

const resetValue = () => {
  alreadyOpenCreateSchedulingPlanForm = false;
  schedulingPlanStore.setSchedulingPlanFormValue();
  schedulingPlanFormRef?.value?.resetFormValue();
};

const onPreStep = () => {
  alreadyOpenCreateSchedulingPlanForm = true;
  schedulingPlanFormRef.value.patchFormValue();
  schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.selectSaleOrderLine);
};
</script>

<style scoped lang="scss">
.hidden {
  display: none;
}

.block {
  display: block;
}
</style>
