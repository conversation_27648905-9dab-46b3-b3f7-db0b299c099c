import { TableWidth, CameraStatusEnum } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "摄像头编号",
      prop: "no",
      width: TableWidth.largeOrder
    },
    {
      label: "摄像头名称",
      prop: "name",
      minWidth: TableWidth.name
    },
    {
      label: "状态",
      prop: "status",
      cellRenderer(data: TableColumnRenderer) {
        const status: CameraStatusEnum = data.row.status;
        if (!status) {
          return null;
        }
        return status === CameraStatusEnum.DISABLE ? (
          <CxTag type="danger">关闭</CxTag>
        ) : (
          <CxTag type="success">开启</CxTag>
        );
      }
    },
    {
      label: "NVR IP地址",
      prop: "nvrIp",
      minWidth: TableWidth.largeName
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
