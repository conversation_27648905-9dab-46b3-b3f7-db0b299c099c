<template>
  <el-button type="primary" link @click="onToggleeditExperimentDialogbVisible(true)">编辑</el-button>
  <el-dialog
    v-model="editExperimentState.editExperimentDialogbvisible"
    title="编辑"
    align-center
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <EditJfnyExperiment ref="editJfnyExperimentRef" :ijfnyExperiment="props.ijfnyExperiment" />
    <template #footer>
      <span>
        <el-button @click="onToggleeditExperimentDialogbVisible(false)">取消</el-button>
        <el-button
          type="primary"
          @click="saveEditExperimentLoading(props.ijfnyExperiment.id, props.saveSuccess)"
          :loading="saveLoading"
          >保存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { IEditJfnyExperimentForm, IJfnyExperimentList } from "@/models/leave-factory/i-jfny-experiment";
import EditJfnyExperiment from "./index.vue";
import { useJfnyExperimentStore } from "@/store/modules/leave-factory/jfny-experiment";
import { reactive, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";

const props = withDefaults(
  defineProps<{
    ijfnyExperiment: IJfnyExperimentList;
    saveSuccess?: Function;
  }>(),
  {}
);

const jfnyExperimentStore = useJfnyExperimentStore();
const saveLoading = ref(false);
const editJfnyExperimentRef = ref<InstanceType<typeof EditJfnyExperiment>>();

const editExperimentState = reactive<{
  editExperimentDialogbvisible: boolean;
}>({
  editExperimentDialogbvisible: false
});

const onToggleeditExperimentDialogbVisible = (Visible: boolean) => {
  editExperimentState.editExperimentDialogbvisible = Visible;
};

const saveEditExperiment = async (id: string, callBack: Function) => {
  const editJfnyExperimentForm: IEditJfnyExperimentForm = editJfnyExperimentRef.value.getEditExperimentValue();

  if (!Array.isArray(editJfnyExperimentForm?.ipoNos) || editJfnyExperimentForm?.ipoNos.length === 0) {
    ElMessage.warning("生产订单不能为空");
    return;
  }

  if ((editJfnyExperimentForm?.reelNos?.join(",")?.length || 0) > 40) {
    ElMessage.warning("盘号总长度不大于40位");
    return;
  }

  await jfnyExperimentStore.editJfnyExperimentById(id, editJfnyExperimentForm);
  editExperimentState.editExperimentDialogbvisible = false;
  callBack && callBack();
  ElMessage.success("编辑成功");
};

const saveEditExperimentLoading = useLoadingFn(saveEditExperiment, saveLoading);
</script>

<style scoped lang="scss"></style>
