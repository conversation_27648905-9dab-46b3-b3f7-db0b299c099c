import { MEASURE_UNIT, emptyDefaultValue } from "@/consts";
import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";

export function useColumns() {
  const { dictionaryFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      fixed: "left",
      width: TableWidth.largeType
    },
    {
      label: "项目号",
      prop: "prjCode",
      width: TableWidth.name
    },
    {
      label: "物料种类",
      slot: "subClassCode",
      minWidth: TableWidth.order
    },
    {
      label: "物料编码",
      prop: "materialCode",
      minWidth: TableWidth.order
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      minWidth: TableWidth.number
    },
    {
      label: "物料单位",
      prop: "materialUnit",
      minWidth: TableWidth.number,
      formatter: dictionaryFormatter(MEASURE_UNIT, "subClassCode")
    },
    {
      label: "采购行项目号",
      prop: "poItemNos",
      minWidth: TableWidth.largeMgOperation,
      cellRenderer: (data: TableColumnRenderer) => {
        return <span>{data.row.poItemNos.match(/[^,]+/) ? data.row.poItemNos : emptyDefaultValue}</span>;
      }
    },
    {
      label: "采购行项目ID",
      prop: "poItemIds",
      minWidth: TableWidth.largeMgOperation,
      cellRenderer: (data: TableColumnRenderer) => {
        return <span>{data.row.poItemIds.match(/[^,]+/) ? data.row.poItemIds : emptyDefaultValue}</span>;
      }
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  const columnsLink: TableColumnList = [
    {
      label: "采购订单行项目号",
      slot: "poItemNo",
      minWidth: TableWidth.largeType
    },
    {
      label: "采购订单号",
      slot: "poNo",
      minWidth: TableWidth.order
    },
    {
      label: "采购订单行项目ID",
      prop: "poItemId",
      minWidth: TableWidth.suborder
    },
    {
      label: "合同名称",
      prop: "conName",
      width: TableWidth.largeType
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      width: TableWidth.simpleOperation
    }
  ];
  return { columns, columnsLink };
}
