import { IWorkOrder } from "./i-work-order";

// &后 非线缆需要的字段
export type ICreateWorkOrder = Pick<
  IWorkOrder,
  | "id"
  | "woNo"
  | "ipoNo"
  | "amount"
  | "unit"
  | "materialsCode"
  | "materialName"
  | "materialDesc"
  | "materialUnit"
  | "specificationType"
  | "voltageLevel"
  | "woStatus"
  | "planStartDate"
  | "planFinishDate"
  | "actualStartDate"
  | "actualFinishDate"
  | "processIds"
  | "salesLineId"
  | "productionId"
  | "purchaseId"
  | "minClassCode"
  | "productName"
  | "qualityTraceCode"
  | "entityId"
> &
  Partial<{
    productCode: string;
    soNo: string;
    soItemNo: string;
    processRouteNo?: string;
    processRouteId?: string;
    subclassCode?: string;
    subClassCode?: string;
  }>;
