import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, IEipLog, IEipLogReq, IFileInfo } from "@/models";
import { http } from "@/utils/http";

export const queryEIPLog = (data: IEipLogReq) => {
  return http.post<IEipLogReq, IListResponse<IEipLog>>(
    withApiGateway("admin-api/infra/eipInterface/getEIPBusinessDataPage"),
    { data }
  );
};

export const getDownloadFileUrl = (id: string) => {
  return http.get<string, IResponse<IFileInfo>>(
    withApiGateway(`admin-api/infra/eipInterface/getEIPBusinessDataFile/${id}`)
  );
};

export const getDownloadIOTFileUrl = (id: string) => {
  return http.get<string, IResponse<IFileInfo>>(withApiGateway(`admin-api/infra/iotLog/getIOTDataFile/${id}`));
};

export const getLogDetailById = (id: number) => {
  return http.get<number, IResponse<IEipLog>>(
    withApiGateway(`admin-api/infra/eipInterface/getEIpBusinessDetailById/${id}`)
  );
};
