import { ColumnWidth } from "@/enums/table-width.enum";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 合格证列表配置
 */
export function genCertificateTableColumnsConfig() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "合格证名称",
      prop: "templateName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "创建人",
      prop: "createName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      width: ColumnWidth.Char14,
      slot: "operation",
      fixed: "right"
    }
  ];

  return { columnsConfig };
}
