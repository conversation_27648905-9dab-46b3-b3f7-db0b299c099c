import { PurchaseChannel, StateGridOrderSyncResult } from "@/enums";
import { IPagingReq } from "@/models";

export interface ISyncCommon {
  id: string;
  purchaseId: string;
  purchaseLineId: string;
  dataId: string;
  lastUpdateTime: string;
  lastSyncTime: string;
  syncResult: StateGridOrderSyncResult;
  reason: string;
  /** 重试次数 */
  retryTimes?: number;
}

export interface ISyncSearchParams {
  searchNo?: string;
  syncResult?: string;
  processId?: string;
}

export type ISyncQueryParams = ISyncSearchParams & IPagingReq;

/** 同步-国网平台EIP-生产过程自动采集列表项 */
export interface IEIPSyncProductionProcessAutoItem {
  id: string;
  ipoNo: string;
  lastUpdateTime: string;
  lastSyncTime: string;
  queueTime: string;
  gatewayTime: string;
  syncResult: string;
  reason: string;
  dataId: string;
  editable: boolean;
  productionStage: string;
  purchaseId: string;
  purchaseLineId: string;
  woNo: string;
  productReportId: string;
  processId: string;
  processCode: string;
  processName: string;
  deviceId: string;
  deviceCode: string;
  deviceName: string;
  productBatchNo: string;
  dataPrepared: boolean;
  totalCount: number;
  syncedCount: number;
  processIndex: number;
}

export interface IIOTSyncCommon {
  id: string;
  saleId?: string;
  saleNo: string;
  dataId: string;
  saleLineId: string;
  saleLineNo: string;
  lastUpdateTime: string;
  lastSyncTime: string;
  syncResult: StateGridOrderSyncResult;
  reason: string;
}

/** 同步-上海平台IOT-生产过程自动采集列表项 */
export interface IIOTSyncProductionProcessAutoItem {
  code: string;
  dataId: string;
  experimentNo: string;
  fileList: any[];
  fileSyncResult: string;
  id: string;
  ipoNo: string;
  lastEipTime: string;
  lastSyncTime: string;
  lastUpdateTime: string;
  name: string;
  ppNo: string;
  processCode: string;
  processIds: string;
  processName: string;
  productBatchNo: string;
  productNo: string;
  productReportId: string;
  productionBatchNo: string;
  productionId: string;
  productionPlanId: string;
  purchaseId: string;
  purchaseLineId: string;
  reason: string;
  salesId: string;
  salesLineId: string;
  soItemNo: string;
  soNo: string;
  standardId: string;
  standardName: string;
  subClassCode: string;
  syncProcess: string;
  syncResult: StateGridOrderSyncResult;
  woNo: string;
  workReportId: string;
}

export interface IProductionProcessAutoSyncParams {
  orderId: string;
  orderItemId: string;
  productReportId: string;
  processCode: string;
  channel: PurchaseChannel;
}
