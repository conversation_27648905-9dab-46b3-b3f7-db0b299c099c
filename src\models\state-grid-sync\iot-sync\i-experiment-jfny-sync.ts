import { IIotSyncCommon } from "@/models";

export interface IExperimentJfnySync extends IIotSyncCommon {
  /** 生产订单号 */
  ipoNo: string;
  /** 试验ID */
  experimentId: string;
  /** 试验编号 */
  experimentNo: string;
  /** 试验名称（工序名称） */
  processName: string;
  /** 工序Code */
  processCode: string;
  /** 生产订（工）单号 */
  orderNo: string;
  /** 成品编号 */
  finProNo: string;
  /** 电压等级 */
  finishedVoltageLevel: string;
  /** 电压等级类型 */
  finishedVoltageLevelType: string;
  /** 试验结果 */
  experimentResult: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
}
