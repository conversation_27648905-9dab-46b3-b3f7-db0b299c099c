<template>
  <div v-loading="loading" class="flex-bc py-2">
    <div class="flex-bc flex-1 overflow-hidden mr-2">
      <div>
        <div class="label">物资种类</div>
        <div class="flex-bc text-middle">
          <span>{{ detailInfo?.categoryName }}</span>
        </div>
      </div>
      <el-divider direction="vertical" />
      <div>
        <div class="label">设备名称</div>
        <div class="flex-bc text-middle">
          <FontIcon icon="icon-pm-fill" class="!text-primary_light_5" />
          <ShowTooltip className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl" :content="detailInfo?.speciTypeName" />
        </div>
      </div>
      <el-divider direction="vertical" />
      <div class="flex-1 overflow-hidden">
        <div class="label">备注</div>
        <div class="flex text-middle">
          <FontIcon icon="icon-seal" class="!text-primary_light_5" />
          <ShowTooltip className="md:max-w-[25em] lg:max-w-[30em] xl:max-w-full " :content="detailInfo?.remarks" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import { useLoadingFn } from "@/utils/useLoadingFn";
import ShowTooltip from "@/components/ShowTooltip";
import { equipmentInfoApi } from "@/api/e-capital-construction/test-parameter/index";
import { EParamsStandard } from "@/models";

let detailInfo = reactive({} as EParamsStandard);
const route = useRoute();
const loading = ref(false);

/**
 * @description: 请求详情数据
 */
const initDetail = useLoadingFn(async () => {
  const { data } = await equipmentInfoApi(route.query.id as string);
  detailInfo = data;
}, loading);

onMounted(() => {
  initDetail();
});
</script>

<style scoped lang="scss">
.subclass-icons {
  @apply flex-bc gap-1 pl-2.5 pr-4 py-2;
  border-radius: 3px;
  border: 1px solid var(--el-color-primary-light-8);
  background: var(--el-color-primary-light-9);

  .value {
    @apply text-primary font-medium;
  }
}

.label {
  @apply text-base text-secondary mb-2;
}

.iconfont {
  @apply mr-2 text-primary_light_5 text-xxl leading-none;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
