import {
  IMaterial,
  IMaterialQueryParams,
  ISalesOrderLineParams,
  ISalesOrder,
  ISaleLineLinkPurchaseLineParams,
  IPurchaseOderLine,
  ISalesOrderLineDetail,
  ISalesOrderLineLinkPurchase
} from "@/models";
import { useSalesOrderLineManagementStore } from "@/store/modules";

/**
 * 销售订单行的hook
 */
export const useSalesOrderLineHook = () => {
  const salesOrderLineManagementStore = useSalesOrderLineManagementStore();

  // 获取销售订单行的列表数据
  async function querySalesOrderLine(params: ISalesOrderLineParams) {
    const tableData: Array<ISalesOrderLineLinkPurchase> = await salesOrderLineManagementStore.querySalesOrderLine(
      params
    );
    return tableData;
  }

  //给选中的销售订单行详情赋值
  function setSalesOrderLineDetailStorage(saleOrderLineDetail: ISalesOrderLineDetail) {
    salesOrderLineManagementStore.setSalesOrderLineDetailStorage(saleOrderLineDetail);
  }

  //清空选中的销售订单行详情
  function clearSalesOrderLineDetail() {
    salesOrderLineManagementStore.clearSalesOrderLineDetailStorage();
  }

  // 查询物料列表
  async function queryMaterialTableData(params: IMaterialQueryParams) {
    const materialTableData: Array<IMaterial> = await salesOrderLineManagementStore.queryMaterialTableData(params);
    return materialTableData;
  }

  // 根据ID查询销售订单详情
  async function getSalesOrderDetailById(id: string) {
    const salesOrderDetail: ISalesOrder = await salesOrderLineManagementStore.getSalesOrderDetailById(id);
    return salesOrderDetail;
  }

  // 查询销售订单详情 销售订单行列表数据
  async function querySaleLineLinkPurchaseOrderLine(saleId: string, params: ISaleLineLinkPurchaseLineParams) {
    const saleLineLinkPurchaseOrderLine: Array<IPurchaseOderLine> =
      await salesOrderLineManagementStore.querySaleLineLinkPurchaseOrderLine(saleId, params);
    return saleLineLinkPurchaseOrderLine;
  }

  // 创建销售订单详情销售订单行
  async function createSalesOrderLineDetail(salesOrderLineDetail: ISalesOrderLineDetail) {
    await salesOrderLineManagementStore.createSalesOrderLineDetail(salesOrderLineDetail);
  }

  // 删除销售订单详情销售订单行
  async function deleteSaleOrderLineById(id: string) {
    await salesOrderLineManagementStore.deleteSaleOrderLineById(id);
  }

  // 查询销售订单行详情数据
  async function getSalesOrderLineDetailById(id: string) {
    const salesOrderLineDetail: ISalesOrderLineDetail = await salesOrderLineManagementStore.getSalesOrderLineDetailById(
      id
    );
    return salesOrderLineDetail;
  }

  // 编辑销售订单详情销售订单行
  async function editSalesOrderLineDetail(salesOrderLineDetail: ISalesOrderLineDetail) {
    await salesOrderLineManagementStore.editSalesOrderLineDetail(salesOrderLineDetail);
  }
  return {
    querySalesOrderLine,
    setSalesOrderLineDetailStorage,
    clearSalesOrderLineDetail,
    queryMaterialTableData,
    getSalesOrderDetailById,
    querySaleLineLinkPurchaseOrderLine,
    createSalesOrderLineDetail,
    deleteSaleOrderLineById,
    getSalesOrderLineDetailById,
    editSalesOrderLineDetail
  };
};
