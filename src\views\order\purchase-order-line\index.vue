<template>
  <div class="bg-bg_color pt-[8px] pb-4 px-6">
    <PurchaseOrderLineHeader />
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
    <PurchaseOrderLine />
  </div>
</template>

<script setup lang="ts">
import { provide } from "vue";
import PurchaseOrderLineHeader from "./src/components/purchase-order-line-header.vue";
import PurchaseOrderLine from "./src/components/purchase-order-line.vue";
import { usePurchaseOrderLineData } from "./src/hooks/purchase-order-line-data";
import { purchaseOrderLineDataKey } from "./src/tokens/purchase-order-line-data-token";

provide(purchaseOrderLineDataKey, usePurchaseOrderLineData());
</script>
