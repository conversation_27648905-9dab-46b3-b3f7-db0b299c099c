/**
 * @description: 原材料组部件检验
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { InspectRawModel } from "@/models";
/**
 * @description: 原材料组部件检验列表
 */
export const InspectRawInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<InspectRawModel>>>(
    withApiGateway(`admin-api/ejj/project/raw-material/inspect/list/${id}`)
  );
};

/**
 * @description: 原材料组部件检验创建
 */
export const InspectRawCreateApi = (params: InspectRawModel) => {
  return http.post<InspectRawModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/inspect`), {
    data: params
  });
};

/**
 * @description: 原材料组部件检验编辑
 */
export const InspectRawEditApi = (params: InspectRawModel) => {
  return http.put<InspectRawModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/inspect`), {
    data: params
  });
};

/**
 * @description: 原材料组部件检验删除
 */
export const InspectRawDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/inspect/${id}`));
};

/**
 * @description: 查看 原材料检验信息
 */
export const InspectRawDetailApi = (id: string) => {
  return http.get<string, IResponse<InspectRawModel>>(
    withApiGateway(`admin-api/ejj/project/raw-material/inspect/${id}`)
  );
};
