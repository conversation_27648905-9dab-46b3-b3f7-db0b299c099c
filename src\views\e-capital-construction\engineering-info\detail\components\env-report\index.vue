<template>
  <div class="overflow-hidden w-full flex flex-col h-full">
    <div class="bg-bg_color flex justify-end">
      <el-button
        :loading="loading"
        v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
        type="primary"
        @click="handleSynchronization"
        >一键同步</el-button
      >
      <AddEditEnvReportDialog mode="add" @post-save-success="requestList()">
        <template #trigger="{ openDialog }">
          <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增</el-button>
        </template>
      </AddEditEnvReportDialog>
    </div>

    <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
      >
        <template #pullStatus="data">
          <el-tag
            :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
            @click="handleShowDetail(data)"
            class="cursor-pointer"
          >
            {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
          </el-tag>
        </template>
        <template #operation="data">
          <div>
            <AddEditEnvReportDialog mode="edit" :id="data.row.id" @post-save-success="requestList()">
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditEnvReportDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
    <!-- 同步明细 -->
    <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
  </div>
</template>

<script setup lang="ts" name="env-report">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  queryEnvReport,
  deleteEnvReportById,
  IntermediateSyncByVoucherTypeApi
} from "@/api/e-capital-construction/engineering-info";
import { IEnvReport } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { useRoute } from "vue-router";
import AddEditEnvReportDialog from "./add-edit-env-report/dialog.vue";
import { PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
import { PermissionKey } from "@/consts";

const { columns } = useColumns();
const loading = ref(false);
const state = reactive<{
  list: Array<IEnvReport>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {}
});

const route = useRoute();
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
onMounted(() => {
  requestList();
});
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("EnvReport", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteEnvReportById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  const { data } = await queryEnvReport(route.query.id as string);
  state.list = data;
}, loading);
</script>
