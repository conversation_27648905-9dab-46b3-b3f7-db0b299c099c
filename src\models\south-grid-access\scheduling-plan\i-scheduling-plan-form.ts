import { ProcessPhaseCodeEnum } from "@/enums/south-grid-access";

export interface ISchedulingPlanForm {
  id?: string;
  /**
   * 排产计划编号
   */
  productionPlanningNo?: string;
  /**
   * 车间编码
   */
  workshopCode?: string;

  /** 监造计划Id */

  supervisionPlanId?: string;

  /**
   * 生产单据编码
   */
  productionOrderNo?: string;

  /**
   * 重点阶段编码
   */
  phaseCode?: ProcessPhaseCodeEnum;
  /**
   * 设备编码
   */
  deviceCode?: string;
  /**
   * 数量
   */
  productionMaterialNumber?: number;
  /**
   * 单位
   */
  productionMaterialUnit?: string;
  /**
   * 计划开始日期
   */
  planStartDate?: string;
  /**
   * 计划完成日期
   */
  planEndDate?: string;
}
