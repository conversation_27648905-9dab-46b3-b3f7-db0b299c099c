import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { ITypeExperimenItem, ITypeExperimenItemForm, ITypeExperimenItemReq } from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryTypeExperimenItem = (params: ITypeExperimenItemReq) => {
  const url: string = withApiGateway(`admin-api/business/type-experimen-item/type-experimen-item`);
  return http.get<ITypeExperimenItemReq, IListResponse<ITypeExperimenItem>>(url, {
    params
  });
};

/** 根据id 查询详情 */
export const getTypeExperimenItemById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/type-experimen-item/type-experimen-item-detail-by-id/${id}`);
  return http.get<string, IResponse<ITypeExperimenItem>>(url);
};

/** 新增 */
export const createTypeExperimenItem = (data: ITypeExperimenItemForm) => {
  return http.post<ITypeExperimenItemForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/type-experimen-item/type-experimen-item"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateTypeExperimenItem = (data: ITypeExperimenItemForm) => {
  return http.put<ITypeExperimenItemForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/type-experimen-item/type-experimen-item"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteTypeExperimenItemById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(
    withApiGateway(`admin-api/business/type-experimen-item/type-experimen-item-by-id/${id}`)
  );
};
