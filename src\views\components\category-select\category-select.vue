<template>
  <el-select class="w-full" v-model="categoryCode" filterable placeholder="请选择" :disabled="props.disabled">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { IOption, ICategory } from "@/models";
import { useCategoryStore } from "@/store/modules";

const props = defineProps<{
  modelValue?: string | Array<string>;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string | Array<string>): void;
}>();

const categoryStore = useCategoryStore();

const options = ref<Array<IOption>>([]);

const categoryCode = computed({
  get() {
    return props.modelValue;
  },
  set(value: string | Array<string>) {
    emit("update:modelValue", value);
  }
});

onMounted(() => getOptions());

async function getOptions() {
  const res: Array<ICategory> = await categoryStore.getCategories();
  options.value = res.map(category => ({ label: category.categoryName, value: category.categoryCode }));
}
</script>

<style scoped></style>
