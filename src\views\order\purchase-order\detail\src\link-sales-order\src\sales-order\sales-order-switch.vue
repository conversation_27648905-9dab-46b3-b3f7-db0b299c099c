<template>
  <div class="flex-bc">
    <div class="flex flex-1 gap-2.5">
      <div
        v-for="order in topOrders"
        :key="order.id"
        :class="{ active: order.id === activeSalesOrder.id }"
        class="item"
        @click="switchSalesOrder(order)"
      >
        <IconifyIconOffline class="icon-prefix" :icon="IconDocument" />
        {{ order.soNo }}
        <IconifyIconOffline
          v-auth="PermissionKey.form.formPurchaseSalesDelete"
          v-track="TrackPointKey.FORM_PURCHASE_SALES_DELETE"
          class="icon-suffix hidden"
          :icon="IconClose"
          @click.stop="deleteSalesOrder(order.id)"
        />
      </div>
    </div>

    <el-dropdown v-if="dropdownOrders.length" max-height="235px">
      <el-button class="!w-8">
        <FontIcon icon="icon-arrow-down" />
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="order in dropdownOrders" :key="order.id" @click="switchSalesOrder(order)">
            <IconifyIconOffline class="icon-prefix" :icon="IconDocument" />
            {{ order.soNo }}
            <IconifyIconOffline class="icon-suffix hidden" :icon="IconClose" @click.stop />
            <!-- <FontIcon class="unlink ml-3 hidden" icon="icon-unlink" @click.stop /> -->
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ISalesOrder } from "@/models";
import { ElMessage } from "element-plus";
import IconDocument from "@iconify-icons/ep/document";
import IconClose from "@iconify-icons/ep/close";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail";
import { useConfirm } from "@/utils/useConfirm";
import { PermissionKey, TrackPointKey } from "@/consts";

const store = usePurchaseOrderDetailSalesOrderStore();
const activeSalesOrder = computed(() => store.activeOrder);
const topOrders = computed(() => store.topOrders);
const dropdownOrders = computed(() => store.dropdownOrders);

function switchSalesOrder(order: ISalesOrder) {
  store.setActiveSalesOrder(order.id);
}

async function deleteSalesOrder(id: string) {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await store.deleteSalesOrder(id);
  ElMessage.success("删除成功");
  void store.refreshSalesOrders();
}
</script>

<style lang="scss" scoped>
.item {
  @apply flex-ac py-1 px-2.5 text-base;
  cursor: pointer;
  border: 1px solid var(--el-color-info-light-8);
  border-radius: 3px;
  color: var(--el-text-color-regular);
  box-shadow: 2px 2px 5px 0 rgba(0, 0, 0, 0.1);
}

.item.active {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary-light-5);
  background: var(--el-color-primary-light-9);
  box-shadow: 2px 2px 5px 0 var(--el-color-primary-light-9);

  .icon-suffix:hover {
    background-color: var(--el-color-primary-light-8);
  }
}

.item:hover .icon-suffix {
  display: block;
}

.icon-prefix {
  @apply mr-1 text-middle;
}

.icon-suffix {
  @apply ml-1 text-sm;

  &:hover {
    border-radius: 50%;
    background-color: var(--el-color-info-light-9);
  }
}
</style>
