import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 配电变压器
 */
export const distributionTransformerSubConfig: MaterialSubClassConfig = {
  name: "配电变压器",
  subClassCode: MaterialSubclassCode.DISTRIBUTION_TRANSFORMER,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: false
      }
    }
  }
};

/**
 * 物资品类-配电变压器
 */
export const distributionTransformerConfig: MaterialCategoryConfig = {
  name: "配电变压器",
  categoryCode: MaterialCategoryCode.DISTRIBUTION_TRANSFORMER,
  subClassMap: {
    distributionTransformerSubConfig,
    [MaterialSubclassCode.DISTRIBUTION_TRANSFORMER]: distributionTransformerSubConfig
  }
};

export default distributionTransformerConfig;
