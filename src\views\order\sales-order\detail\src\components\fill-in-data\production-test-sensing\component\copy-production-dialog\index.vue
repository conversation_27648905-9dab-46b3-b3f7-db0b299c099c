<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    class="middle"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
    draggable
    @close="cancel"
    @closed="closed"
  >
    <el-steps :active="activeStepKey" class="mb-5">
      <el-step :title="firstStepTitle" />
      <el-step :title="stepTwoTitle" />
      <el-step title="完成" />
    </el-steps>
    <SelectProductOrder
      v-if="isFirstStep"
      class="h-[490px] overflow-hidden"
      :currentSelect="selectProduct"
      :dataType="dataType"
      @currentChange="currentChange"
    />
    <template v-if="!isFirstStep">
      <component class="h-[490px] overflow-hidden" :is="currentCmp" :productInfo="selectProduct" v-model="selectData" />
    </template>

    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" v-if="isFirstStep" :disabled="disabledNextStep" @click="onNextStep()">下一步</el-button>

      <template v-if="!isFirstStep">
        <el-button @click="onPreStep()">上一步</el-button>
        <el-button type="primary" :loading="saveLoading" :disabled="disabledSave" @click="handleOnSave()"
          >保存</el-button
        >
      </template>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SelectProductOrder from "./select-product-order.vue";
import SelectRawMaterialInProductOrder from "./step-second/select-raw-material/select-raw-material.vue";
import SelectProductionProcess from "./step-second/select-production-process/select-production-process.vue";
import SelectPredeliveryTest from "./step-second/select-predelivery-test/select-predelivery-test.vue";
import { computed, ref, ComputedRef, provide } from "vue";
import { CopyProductOrderStepEnum, CopyTestSensingDateType, ETestSensingType } from "@/enums";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IProductOrder } from "@/models/product-order";
import { COPY_FROM_ORDER_TOKEN } from "./tokens";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";
import { useEXFactoryExperimentStore, useSalesFillInDataStore, useSalesOrderDetailStore } from "@/store/modules";
import { emitter } from "@/utils/mitt";

const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isStepKey?: ComputedRef;
  }>(),
  {
    modelValue: false
  }
);
const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "saveSuccess"): void;
}>();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const salesOrderDetailStore = useSalesOrderDetailStore();
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const productionProcessInspecStore = useProductionProcessInspecStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();
const dialogTitle = computed(() => {
  return salesOrderDetailStore.isCable ? "从生产订单中选用" : "从生产工单中选用";
});
const firstStepTitle = computed(() => {
  return salesOrderDetailStore.isCable ? "选择生产订单" : "选择生产工单";
});
const stepTwoTitle = computed(() => {
  return props.isStepKey.value === ETestSensingType.RawMaterialGroupUnitCheck
    ? "选择原材料检"
    : props.isStepKey.value === ETestSensingType.ProductionProcessInspection
    ? "过程检工序"
    : "出厂试验";
});
const activeStepKey = ref();
activeStepKey.value = CopyProductOrderStepEnum.SELECT_PRODUCT_ORDER;
const selectProduct = ref();
const isFirstStep = computed(() => activeStepKey.value === CopyProductOrderStepEnum.SELECT_PRODUCT_ORDER);
const currentCmp = computed(() => {
  return getStepComponent(props.isStepKey.value);
});
const dataType = computed(() => {
  // 判断历史生产订单的类型
  return props.isStepKey.value === ETestSensingType.RawMaterialGroupUnitCheck
    ? CopyTestSensingDateType.MATERIAL
    : props.isStepKey.value === ETestSensingType.ProductionProcessInspection
    ? CopyTestSensingDateType.PROCESS
    : props.isStepKey.value === ETestSensingType.ExFactoryExperiment
    ? CopyTestSensingDateType.EXPERIMENT
    : "";
});
const disabledNextStep = computed(() => {
  return !selectProduct.value?.id;
});
const selectData = ref([]);
const disabledSave = computed(() => {
  return !selectData.value?.length;
});
const saveLoading = ref<boolean>(false);
const handleOnSave = useLoadingFn(save, saveLoading);
provide(COPY_FROM_ORDER_TOKEN, {
  isCable: salesOrderDetailStore.isCable
});

const onNextStep = () => {
  activeStepKey.value = CopyProductOrderStepEnum.SELECT_TWO_STEP;
};

const onPreStep = () => {
  activeStepKey.value = CopyProductOrderStepEnum.SELECT_PRODUCT_ORDER;
};

const cancel = () => {
  visible.value = false;
};

const closed = () => {
  activeStepKey.value = CopyProductOrderStepEnum.SELECT_PRODUCT_ORDER;
  selectProduct.value = null;
  selectData.value = [];
};

function currentChange(data: IProductOrder) {
  selectProduct.value = data;
}

function getStepComponent(key?: string) {
  switch (key) {
    case ETestSensingType.RawMaterialGroupUnitCheck:
      return SelectRawMaterialInProductOrder;
    case ETestSensingType.ProductionProcessInspection:
      return SelectProductionProcess;
    case ETestSensingType.ExFactoryExperiment:
      return SelectPredeliveryTest;
    default:
      break;
  }
}

async function save() {
  const iOtDataIds = selectData.value.map(item => item.id);
  const dataId = useSalesFillInDataStore().dataId;
  const params = {
    orderId: dataId,
    iOtDataIds
  };
  switch (props.isStepKey.value) {
    case ETestSensingType.RawMaterialGroupUnitCheck:
      await rawMaterialGroupUnitStore.copyRawMaterialFromOrder(params, true);
      break;
    case ETestSensingType.ProductionProcessInspection:
      await productionProcessInspecStore.copyProcessInspectFromOrder(params, true);
      break;
    case ETestSensingType.ExFactoryExperiment:
      await exFactoryExperimentStore.copyOutFactoryExperimentFromOrder(params, true);
      break;
    default:
      break;
  }
  emit("saveSuccess");
  emitter.emit("refreshOutGoingFactoryExperimentList");
}
</script>
