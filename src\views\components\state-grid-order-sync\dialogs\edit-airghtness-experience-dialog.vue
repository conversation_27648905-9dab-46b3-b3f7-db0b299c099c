<template>
  <el-dialog
    v-model="editVisible"
    title="编辑试验"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <AddAirTightnessExperiment ref="editFormRef" :subClassCode="subclassCode" :isCable="isCable" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
          >保存，并重新同步</el-button
        >
        <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import AddAirTightnessExperiment from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/edit-delivery-test/index.vue";
import { useEdit } from "@/views/components/state-grid-order-sync/hooks";
import { IExperimentSync, IOutGoingFactoryQMXExperimentForm } from "@/models";
import { useEXFactoryExperimentStore, useEXFactoryQMXStore } from "@/store/modules";
import { provide, reactive, watch } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";

const props = defineProps<{
  subclassCode: string;
  isCable?: boolean;
}>();

const exFactoryQMXStore = useEXFactoryQMXStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof AddAirTightnessExperiment>>(updateExperiment);

const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

async function openEditDialog(data: IExperimentSync) {
  editVisible.value = true;
  exFactoryQMXStore.isEditTightness = true;
  await exFactoryQMXStore.getOutGoingFactoryQMXExperimentDetailById(data.dataId);
  exFactoryExperimentStore.setMetadataModelInfo(exFactoryQMXStore.outGoingFactoryQmxExperimentDetail.rawMetadataValue);
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.experimentNo
  };
}

async function updateExperiment() {
  const formData = await editFormRef.value.getDeliveryTestFormValue();
  if (!formData) return;
  const { qmxExperimentBaseValue, qmxAirTightnessExpersValue } = formData;

  const factoryQMXExperimentForm: IOutGoingFactoryQMXExperimentForm = {
    ...(qmxExperimentBaseValue as IOutGoingFactoryQMXExperimentForm),
    rawMetadataValue: qmxAirTightnessExpersValue.submitData.map(item => ({
      dataCode: item.targetCode,
      identityCode: item.dataTypeIdentityDetail.identityCode,
      dataValue: item.targetValue
    }))
  };
  await exFactoryQMXStore.editOutGoingFactoryQMXExperiment(factoryQMXExperimentForm);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
