<template>
  <div class="flex">
    <transition name="fade">
      <div class="flex justify-between items-center mr-6 text-base" v-if="Boolean(selectedCount)">
        已选中:
        <span class="text-primary mx-4">
          {{ selectedCount }}
        </span>
        项
        <el-icon class="close ml-4" @click="clearSelection"><CircleCloseFilled /></el-icon>
      </div>
    </transition>

    <el-tooltip effect="dark" content="归档后的数据将不会自动同步和触发评分" placement="top-start">
      <batch-archive-dialog :selected-list="selectedList" @post-archive-success="handlePostArchiveSuccess">
        <template #trigger="{ openDialog }">
          <el-button :disabled="!isBatching" @click="openDialog" v-auth="PermissionKey.form.formPurchaseSalesArchived"
            >批量归档</el-button
          >
        </template>
      </batch-archive-dialog>
    </el-tooltip>

    <el-tooltip effect="dark" content="归档后的数据将不会自动同步和触发评分" placement="top-start">
      <el-button
        class="ml-4"
        :disabled="!isBatching"
        @click="requestBatchCancelArchive"
        v-auth="PermissionKey.form.formPurchaseSalesArchived"
      >
        批量取消归档
      </el-button>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useVModels } from "@vueuse/core";
import { CircleCloseFilled } from "@element-plus/icons-vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey } from "@/consts";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";
import BatchArchiveDialog from "./batch-archive-dialog/index.vue";
import { batchArchiveSalesOrder } from "@/api/sales-order";
import { emitter } from "@/utils/mitt";

/**
 * 批量归档
 */
const props = defineProps<{
  modelValue: Array<string>;
}>();

const emits = defineEmits<{
  (e: "update:modelValue", value: Array<string>): void;
  (e: "postClear"): void;
}>();

const { modelValue: selectedList } = useVModels(props, emits);

const loading = ref(false);
const selectedCount = computed(() => selectedList.value.length);
const isBatching = computed(() => selectedCount.value > 0);

function clearSelection() {
  selectedList.value = [];
  emits("postClear");
}

const requestBatchCancelArchive = useLoadingFn(async () => {
  if (!(await useConfirm("正在执行批量取消归档，是否继续？", "确认取消"))) {
    return;
  }
  const params = {
    ids: selectedList.value,
    documentation: false
  };
  const { data } = await batchArchiveSalesOrder(params);
  emitter.emit("refreshSalesOrderList");
  if (!data) {
    return;
  }
  ElMessage.success("取消成功");
}, loading);

function handlePostArchiveSuccess() {
  emitter.emit("refreshSalesOrderList");
}
</script>

<style lang="scss" scoped>
.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}
</style>
