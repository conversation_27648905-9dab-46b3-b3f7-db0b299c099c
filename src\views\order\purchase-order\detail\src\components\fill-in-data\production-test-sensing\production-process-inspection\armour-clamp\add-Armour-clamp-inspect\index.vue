<template>
  <div class="add-armour-clamp-inspect">
    <el-form
      class="cx-form"
      ref="processInspecRef"
      label-position="top"
      require-asterisk-position="right"
      :model="form"
      :rules="rules"
    >
      <div class="baseInfo mb-4">
        <TitleBar title="基本信息" class="mb-3" />
        <div class="form-item">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item" label="过程检编号" prop="code">
                <SerialNumber
                  :code="PROCESS_INSPECT_NO_CODE"
                  :create="isAddOrEdit"
                  v-model="form.code"
                  placeholder="请输入过程检编号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="form-item" label="产品名称" prop="productName">
                <el-input v-model="form.productName" class="!w-full" placeholder="请输入产品名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item" label="生产批次号" prop="productBatchNo">
                <el-input v-model="form.productBatchNo" class="!w-full" placeholder="请输入生产批次号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="form-item" label="生产完成时间" prop="productFinishTime">
                <el-date-picker
                  v-model="form.productFinishTime"
                  type="datetime"
                  placeholder="请选择时间"
                  class="!w-full"
                  :disabled-date="disabledNowAfterDate"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item" label="产品型号" prop="model">
                <el-input v-model="form.model" class="!w-full" placeholder="请输入产品型号" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="process">
        <TitleBar title="加工信息" class="mb-3" />
        <div class="form-item">
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item class="form-item" label="加工方式(本体)" prop="processMethod">
                <el-input v-model="form.processMethod" class="!w-full" placeholder="请输入加工方式" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="form-item" label="加工件数(本体)" prop="processNumber">
                <el-input-number
                  :min="0"
                  v-model="form.processNumber"
                  class="!w-full"
                  controls-position="right"
                  placeholder="请输入加工件数"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar/index";
import SerialNumber from "@/components/SerialNumber";
import { reactive, ref, watchEffect } from "vue";
import { FormRules, FormInstance } from "element-plus";
import { useArmourClampProdProcessInspectStore } from "@/store/modules/production-test-sensing/armour-clamp/prod-process-inspect";
import { useFillInDataStore, usePurchaseOrderDetailStore } from "@/store/modules";
import { onMounted } from "vue";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { IWorkOrder } from "@/models";
import { PROCESS_INSPECT_NO_CODE } from "@/consts";

const processInspecRef = ref<FormInstance>();
const props = withDefaults(
  defineProps<{
    isAddOrEdit?: boolean;
  }>(),
  {
    isAddOrEdit: true
  }
);
// const emits = defineEmits<{
//   (event: "saveSuccess"): void;
// }>();
const fillInDataStore = useFillInDataStore();
const purchaseOrderStore = usePurchaseOrderDetailStore();
const armourClampProdProcessInspectStore = useArmourClampProdProcessInspectStore();
// 表单信息
const form = reactive({
  id: null,
  code: null,
  productName: null,
  productBatchNo: null,
  productFinishTime: null,
  model: null,
  processMethod: null,
  processNumber: null
});
const rules: FormRules = {
  code: [{ required: true, message: "请输入过程检测编码", trigger: "change" }],
  productName: [{ required: true, message: "请输入产品名称", trigger: "change" }],
  productBatchNo: [{ required: true, message: "请输入生产批次号", trigger: "change" }],
  productFinishTime: [{ required: true, message: "请选择时间", trigger: "change" }],
  model: [{ required: true, message: "请输入产品型号", trigger: "change" }],
  processMethod: [{ required: true, message: "请输入加工方式", trigger: "change" }],
  processNumber: [{ required: true, message: "请输入加工件数", trigger: "change" }]
};
const saveData = ref();

// 编辑的时候更新初始值
onMounted(() => {
  watchEffect(() => {
    const workOrder = fillInDataStore.data as IWorkOrder;
    if (props.isAddOrEdit && workOrder) {
      // 从工单带入数据
      const { specificationType: model, productName } = workOrder;
      Object.assign(form, { model, productName });
    } else {
      // 编辑的时候赋值
      Object.assign(form, armourClampProdProcessInspectStore.detailOfProcessInspect);
    }
  });
});

/**
 * 验证表单
 */
const validForm = async () => {
  const formEle = processInspecRef.value;
  if (!formEle) return;
  return await formEle.validate(() => {});
};

/**
 * 获取表单数据
 */
const getProcessInspectForm = async () => {
  const valid = await validForm();
  if (valid) {
    const { processId, processCode, processName } = armourClampProdProcessInspectStore.currentTagInfo || {};
    const idStr = form?.id || null;
    const saveFormData = {
      ...form,
      workOrderId: fillInDataStore.dataId,
      purchaseOrderId: purchaseOrderStore.purchaseOrderId,
      processId,
      processName,
      processCode,
      id: props.isAddOrEdit ? null : idStr
    };
    return saveFormData;
  } else {
    return Promise.reject();
  }
};

defineExpose({
  getProcessInspectForm,
  saveData
});
</script>

<style scoped lang="scss"></style>
