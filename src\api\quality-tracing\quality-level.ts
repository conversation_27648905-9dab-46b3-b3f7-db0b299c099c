/**
 * @description: 质量等级
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { QualityLevelListItem, EditQualityLevelParams } from "@/models/quality-tracing";

/**
 * @description: 编辑质量等级
 */
export const editQualityLevelList = (params: EditQualityLevelParams) => {
  return http.post<EditQualityLevelParams, IResponse<boolean>>(withApiGateway("admin-api/system/quality-level/save"), {
    data: params
  });
};

/**
 * @description: 查询质量等级列表
 */
export const queryQualityLevelList = () => {
  return http.get<void, IResponse<Array<QualityLevelListItem>>>(withApiGateway("admin-api/system/quality-level/list"));
};
