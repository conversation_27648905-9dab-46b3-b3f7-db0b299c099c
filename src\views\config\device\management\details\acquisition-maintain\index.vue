<template>
  <div class="flex my-3">
    <ElRadioGroup v-model="state.processId" @change="onAcquisitionProcessChange()">
      <ElRadioButton
        :label="item.processId"
        v-for="item of deviceStore.deviceDetail?.processList || []"
        :key="item.processId"
      >
        {{ item.processName }}
      </ElRadioButton>
    </ElRadioGroup>
  </div>
  <PureTable
    class="flex-1 overflow-hidden pagination tooltip-max-w"
    row-key="id"
    :data="acquisitionMaintainStore.processList"
    :columns="columns"
    showOverflowTooltip
    :loading="acquisitionMaintainStore.loading"
  >
    <template #operation="data">
      <div>
        <ElButton
          v-auth="PermissionKey.meta.metaDeviceStandardCollect"
          v-if="data.row.bindFlag"
          type="danger"
          link
          @click="onUnBinding(data.row)"
        >
          解绑
        </ElButton>
        <ElButton
          v-auth="PermissionKey.meta.metaDeviceStandardCollect"
          v-else
          type="primary"
          link
          @click="onBinding(data.row)"
        >
          绑定
        </ElButton>
      </div>
    </template>
    <template #empty>
      <el-empty :image-size="120">
        <template #image>
          <EmptyData />
        </template>
      </el-empty>
    </template>
  </PureTable>
  <el-dialog
    :title="'绑定采集点'"
    destroy-on-close
    align-center
    class="small"
    v-model="state.bindingFormVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseBindingForm()"
  >
    <BindingForm
      ref="bindingFormRef"
      :standardPointId="state.standardPointId"
      :standardPointName="state.standardPointName"
      :processId="processId"
    />
    <template #footer>
      <el-button @click="onCloseBindingForm()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSaveBindAcquisition()">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, watchEffect, toRef } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { ElMessage } from "element-plus";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import BindingForm from "./binding-form.vue";
import { useConfirm } from "@/utils/useConfirm";
import { AcquisitionMaintain, IAcquisitionMaintainForm } from "@/models/device";
import { useRoute } from "vue-router";
import { useDeviceStore } from "@/store/modules";
import { useAcquisitionMaintainStore } from "@/store/modules/device";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey } from "@/consts";

const route = useRoute();
const deviceStore = useDeviceStore();
const acquisitionMaintainStore = useAcquisitionMaintainStore();
const deviceId: string = route.params.id as string;

const bindingFormRef = ref<InstanceType<typeof BindingForm>>();

const state = reactive<{
  bindingFormVis: boolean;
  processId: string;
  standardPointId: string;
  standardPointName: string;
}>({
  bindingFormVis: false,
  processId: undefined,
  standardPointId: undefined,
  standardPointName: undefined
});

const processId = toRef(state, "processId");

const { columns } = useColumns();
const { pagination } = useTableConfig();
const saveLoading = ref<boolean>(false);
const onSaveBindAcquisition = useLoadingFn(saveBindAcquisition, saveLoading);

const queryAutomaticCollection = () => {
  acquisitionMaintainStore.queryAutomaticCollection(deviceId, state.processId);
};

watchEffect(() => {
  if (deviceStore.deviceDetail && deviceStore.deviceDetail.id) {
    if (!state.processId && deviceStore.deviceDetail.processList.length) {
      state.processId = deviceStore.deviceDetail.processList[0].processId;
      queryAutomaticCollection();
    } else {
      acquisitionMaintainStore.resetProcessList();
    }
  }
});

// 绑定
const onBinding = async (item: AcquisitionMaintain) => {
  state.bindingFormVis = true;
  state.standardPointId = item.standardPointId;
  state.standardPointName = item.standardPointName;
};

const onUnBinding = async (item: AcquisitionMaintain) => {
  if (!(await useConfirm("确认解绑后，数据将无法维护", "确认解绑"))) {
    return;
  }
  const _dataReq: IAcquisitionMaintainForm = {
    standardPointId: item.standardPointId,
    deviceId
  };
  await acquisitionMaintainStore.unbindAcquisitionPoint(_dataReq);
  ElMessage.success("解绑成功");
  pagination.currentPage = 1;
  queryAutomaticCollection();
};

const onCloseBindingForm = () => {
  state.bindingFormVis = false;
};

// 采集工序tab切换
const onAcquisitionProcessChange = () => {
  queryAutomaticCollection();
};

async function saveBindAcquisition() {
  const formValue: IAcquisitionMaintainForm | boolean = await bindingFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  const _formValue: IAcquisitionMaintainForm = { ...formValue, deviceId };
  await acquisitionMaintainStore.bindAcquisitionPoint(_formValue);

  state.bindingFormVis = false;
  ElMessage.success("绑定成功");
  pagination.currentPage = 1;
  queryAutomaticCollection();
}
</script>

<style scoped lang="scss"></style>
