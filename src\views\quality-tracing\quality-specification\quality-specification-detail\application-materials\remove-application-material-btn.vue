<template>
  <el-button
    v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationDeleteMaterial"
    link
    type="danger"
    @click="handleDelete"
  >
    删除
  </el-button>
</template>
<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus";
import { PermissionKey } from "@/consts";
import { removeApplicationMaterial } from "@/api/quality-tracing";

/**
 * 移除 该质量规范 的使用物料
 */

const props = defineProps<{
  /** 要移除的物料的id */
  materialId: string;
  /** 质量规范id */
  qualityId: string;
}>();

const emits = defineEmits<{
  /** 删除后置操作 */
  (event: "postDeleteSuccess"): void;
}>();

/**
 * @description: 删除行为
 */
async function handleDelete() {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  const res = await removeApplicationMaterial({
    removeChooseMaterialId: props.materialId,
    qualitySpecificationId: props.qualityId
  });

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  emits("postDeleteSuccess");
}
</script>
