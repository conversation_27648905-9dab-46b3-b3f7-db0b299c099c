<template>
  <DetailRawMaterial @detailCloseDiag="detailCloseDiag" />
</template>

<script setup lang="ts">
import DetailRawMaterial from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/detail-raw-material-inspect/index.vue";
import { EDiagType } from "@/models/raw-material/i-raw-material-res";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";

const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();

async function openDetailDialog(dataId: string) {
  await rawMaterialGroupUnitStore.getDetailOfRawMaterialGroupUnit(dataId, EDiagType.Detail);
  rawMaterialGroupUnitStore.setDetailRawMaterialVisible(true);
}

function detailCloseDiag() {
  rawMaterialGroupUnitStore.setDetailRawMaterialVisible(false);
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
