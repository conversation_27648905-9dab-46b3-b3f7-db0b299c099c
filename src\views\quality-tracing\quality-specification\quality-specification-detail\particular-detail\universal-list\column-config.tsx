import { ColumnWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { JudgeTypeEnumMap } from "@/enums/quality-specification";

/**
 * @description: 生成质量规范明细通用表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig: TableColumnList = [
    {
      label: "序号",
      prop: "ipoNo",
      minWidth: ColumnWidth.Char3,
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return <div>{data.$index + 1}</div>;
      }
    },
    {
      label: "检测类型",
      prop: "processName",
      minWidth: ColumnWidth.Char10,
      sortable: "custom"
    },
    {
      label: "检测项",
      prop: "modelName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "判定方式",
      prop: "poNos",
      slot: "poNos",
      minWidth: ColumnWidth.Char5,
      cellRenderer: (data: TableColumnRenderer) => {
        return <div>{JudgeTypeEnumMap[data.row.judgeType]}</div>;
      }
    },
    {
      label: "判定标准",
      prop: "judgeStandardStr",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "分值",
      prop: "score",
      minWidth: ColumnWidth.Char5,
      sortable: "custom"
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char5,
      fixed: "right",
      slot: "opertion"
    }
  ];

  return { columnsConfig };
}
