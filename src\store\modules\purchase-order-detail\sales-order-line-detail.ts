import { defineStore } from "pinia";
import { IPurchaseOrderLine, ISalesOrderLine } from "@/models";
import { getPurchaseOrderLineBySalesOrderLine } from "@/api/purchase-order";
import { getSalesOrderLineById } from "@/api";

export const usePurchaseOrderDetailSalesOrderLineDetailStore = defineStore({
  id: "cx-purchase-order-detail-sales-order-line-detail",
  state: () => ({
    loading: false,
    visible: false,
    salesOrderLine: undefined as ISalesOrderLine,
    purchaseOrderLines: [] as Array<IPurchaseOrderLine>
  }),
  actions: {
    showDetail(id: string) {
      this.visible = true;
      this.loading = true;
      Promise.all([
        getSalesOrderLineById(id).then(res => res.data),
        getPurchaseOrderLineBySalesOrderLine(id).then(res => res.data)
      ])
        .then(([salesOrderLine, purchaseOrderLines]) => {
          this.salesOrderLine = salesOrderLine;
          this.purchaseOrderLines = purchaseOrderLines;
        })
        .finally(() => (this.loading = false));
    }
  }
});
