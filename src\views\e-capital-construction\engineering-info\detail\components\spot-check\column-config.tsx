import {
  ColumnWidth,
  SamplingResultEnumMapDesc,
  TableWidth,
  GisComponentEnumMapDesc,
  HydraulicFailureCountEnumMapDesc,
  OperatingMechanismTypeEnumMapDesc,
  MechanicalOperationEnumMapDesc
} from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 原材料组部件抽检
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "采集类型ID",
      prop: "gisComponentType",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return GisComponentEnumMapDesc[data.row.gisComponentType];
      }
    },
    {
      label: "总抽检批次",
      prop: "totalBatchesSampled",
      width: ColumnWidth.Char10
    },
    {
      label: "批次编号",
      prop: "batchNumber",
      width: ColumnWidth.Char8
    },
    {
      label: "批次数量",
      prop: "batchQuantity",
      width: ColumnWidth.Char10
    },
    {
      label: "钢印号/字头号",
      prop: "stampingNumber",
      width: ColumnWidth.Char10
    },
    {
      label: "试验时间",
      prop: "testTime",
      width: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "抽检结果",
      prop: "samplingResult",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return SamplingResultEnumMapDesc[data.row.samplingResult];
      }
    },
    {
      label: "液压失效次数",
      prop: "hydraulicFailureCount",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return HydraulicFailureCountEnumMapDesc[data.row.hydraulicFailureCount];
      }
    },
    {
      label: "抽检失效值",
      prop: "failureValueSampled",
      width: ColumnWidth.Char10
    },
    {
      label: "破坏部位/抽检失效位置",
      prop: "failureLocationSampled",
      width: ColumnWidth.Char12
    },
    {
      label: "操动机构类型",
      prop: "operatingMechanismType",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return OperatingMechanismTypeEnumMapDesc[data.row.operatingMechanismType];
      }
    },
    {
      label: "机械操作类型",
      prop: "mechanicalOperation",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return MechanicalOperationEnumMapDesc[data.row.mechanicalOperation];
      }
    },
    {
      label: "操作次数",
      prop: "operationCount",
      width: ColumnWidth.Char10,
      cellRenderer: (data: TableColumnRenderer) => {
        return MechanicalOperationEnumMapDesc[data.row.mechanicalOperation];
      }
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char10,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];

  return columnsConfig;
}
