<template>
  <div class="fill-in-data-dialog">
    <div @click="onOpen">
      <slot>
        <el-button link>填报生产数据</el-button>
      </slot>
    </div>
    <el-dialog
      title="填报生产数据"
      fullscreen
      align-center
      class="state-grid-order-sync-detail-dialog"
      v-model="visible"
      :destroy-on-close="true"
      :close-on-press-escape="false"
      :append-to-body="true"
      @close="onClose"
      :show-close="false"
    >
      <template #header="{ close, titleId, titleClass }">
        <div :id="titleId" class="flex justify-between">
          <div class="flex gap-3 items-center">
            <el-button link @click="close">
              <el-icon size="20"><Back /></el-icon>
            </el-button>
            <div :class="titleClass">【填报生产数据】</div>
            <div :class="titleClass" v-if="purchaseOrderDetailStore.purchaseOrder.poNo && isPurchaseOrderMode">
              采购订单号：{{ purchaseOrderDetailStore.purchaseOrder.poNo }}
            </div>
            <div :class="titleClass" v-if="salesOrderDetailStore.salesOrder.soNo && isSalesOrderMode">
              销售订单号：{{ salesOrderDetailStore.salesOrder.soNo }}
            </div>
          </div>
          <el-button type="danger" @click="close">
            <el-icon size="20"><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <SaleOrderFillIndata v-if="mode === 'SALE'" />
      <PurchaseOrderFillIndata v-else />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { ElLoading } from "element-plus";
import PurchaseOrderFillIndata from "@/views/order/purchase-order/detail/src/components/fill-in-data/fill-in-data/index.vue";
import SaleOrderFillIndata from "@/views/order/sales-order/detail/src/components/fill-in-data/fill-in-data/index.vue";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesOrderDetailStore,
  useSalesFillInDataStore
} from "@/store/modules";
import { ETestSensingType } from "@/enums/purchase-order/production-test-sensing";
import { emitter } from "@/utils/mitt";
import { Close, Back } from "@element-plus/icons-vue";

type Mode = "SALE" | "PURCHASE";

const props = defineProps({
  productionId: {
    type: String,
    required: true
  },
  workOrderId: {
    type: String
  },
  saleOrderId: {
    type: String,
    required: true
  },
  purchaseOrderId: {
    type: String
  },
  /* 导航到填报生产数据**/
  navigate: {
    type: Object as PropType<{
      index: number;
      key: ETestSensingType;
    }>
  },
  mode: {
    type: String as PropType<Mode>,
    default: "SALE"
  },
  /* loading插入的DOM **/
  loadingTarget: {
    type: [String, Object] as PropType<string | HTMLElement>
  }
});

const emits = defineEmits<{
  (event: "postDialogClose"): void;
}>();

const salesOrderDetailStore = useSalesOrderDetailStore();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const fillInDataStore = useFillInDataStore();
const salesOrderFillInDataStore = useSalesFillInDataStore();

const visible = ref(false);

const isWorkOrder = computed(() => Boolean(props.workOrderId));

const isPurchaseOrderMode = computed(() => props.mode === "PURCHASE");
const isSalesOrderMode = computed(() => props.mode === "SALE");

/**
 * @description: 打开dialog
 */
const onOpen = async () => {
  if (props.mode === "SALE") {
    await saleOrderModeOpen();
  } else {
    await purchaseOrderModeOpen();
  }
  if (props.navigate) {
    emitter.emit("dataIntegrity", props.navigate);
  }
};

/**
 * @description: 销售订单模式，打开执行
 */
const saleOrderModeOpen = async () => {
  const loading = ElLoading.service(
    props.loadingTarget ? { target: props.loadingTarget, background: "transparent" } : { background: "transparent" }
  );
  salesOrderDetailStore.setSaleOrderId(props.saleOrderId);
  await salesOrderDetailStore.refreshSalesOrder().finally(loading.close);
  visible.value = true;
  if (isWorkOrder.value) {
    await salesOrderFillInDataStore.refreshData(props.workOrderId, props.productionId);
  } else {
    await salesOrderFillInDataStore.refreshData(props.productionId);
  }
};

/**
 * @description: 采购订单模式，打开时执行
 */
const purchaseOrderModeOpen = async () => {
  const loading = ElLoading.service(
    props.loadingTarget ? { target: props.loadingTarget, background: "transparent" } : { background: "transparent" }
  );
  // 设定采购订单ID
  purchaseOrderDetailStore.setPurchaseOrderId(props.purchaseOrderId);
  await purchaseOrderDetailStore.refreshPurchaseOrder().finally(loading.close);
  visible.value = true;
  // 设定  生产ID
  if (isWorkOrder.value) {
    await fillInDataStore.refreshData(props.workOrderId, props.productionId);
  } else {
    await fillInDataStore.refreshData(props.productionId);
  }
};

/**
 * @description: 关闭dialog
 */
const onClose = () => {
  visible.value = false;
  emits("postDialogClose");
};
</script>

<style lang="scss">
@import "@/views/order/purchase-order/detail/styles/mixin";

.state-grid-order-sync-detail-dialog {
  @include full-screen-dialog;
}
</style>
