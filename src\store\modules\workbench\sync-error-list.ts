import { defineStore } from "pinia";
import * as api from "@/api/workbench";
import * as syncApi from "@/api/state-grid-order-sync";
import {
  ErrorLevel,
  ErrorType,
  OrderDirectionEnum,
  StateGridOrderSyncDataFormatEnum,
  StateGridOrderSyncType
} from "@/enums";
import {
  IListResponse,
  IStateGridOrderSyncParams,
  IStateGridRateTrigger,
  IWorkbenchSync,
  IWorkbenchSyncParams
} from "@/models";
import { DialogInstance } from "@/components/CxDialog/src/dialog.type";

export const useSyncErrorListStore = defineStore({
  id: "cx-sync-error-list",
  state: () => ({
    loading: false,
    type: undefined as StateGridOrderSyncType,
    data: [] as Array<IWorkbenchSync>,
    triggerData: [] as Array<IStateGridRateTrigger>,
    total: 0,
    queryParams: {
      pageNo: 1,
      pageSize: 10
    } as IWorkbenchSyncParams,
    dialog: null as DialogInstance<any>
  }),
  actions: {
    setType(type: StateGridOrderSyncType) {
      this.type = type;
      this.data = [];
    },
    refresh() {
      this.loading = true;
      let fetchFn: (params: IWorkbenchSyncParams) => Promise<IListResponse<IWorkbenchSync>>;
      switch (this.type) {
        case StateGridOrderSyncType.SALE_ORDER_ITEM:
          fetchFn = api.getSalesSyncErrorList;
          break;
        case StateGridOrderSyncType.PRODUCTION_PLAN:
          fetchFn = api.getProductionPlanSyncErrorList;
          break;
        case StateGridOrderSyncType.PRODUCTION_ORDER:
          fetchFn = api.getProductionOrderSyncErrorList;
          break;
        case StateGridOrderSyncType.WORK_ORDER:
          fetchFn = api.getWorkOrderSyncErrorList;
          break;
        case StateGridOrderSyncType.REPORT_WORK:
          fetchFn = api.getWorkReportSyncErrorList;
          break;
        case StateGridOrderSyncType.RAW_MATERIAL_INSPECTION:
          fetchFn = api.getRawMaterialSyncErrorList;
          break;
        case StateGridOrderSyncType.PRODUCTION_TECHNOLOGY:
          fetchFn = api.getProductionFlowSyncErrorList;
          break;
        case StateGridOrderSyncType.EXPERIMENT:
          fetchFn = api.getExperimentSyncErrorList;
          break;
        case StateGridOrderSyncType.FINISHED_PRODUCT:
          fetchFn = api.getFinishProductSyncErrorList;
          break;
        default:
          fetchFn = () =>
            Promise.resolve({
              code: 0,
              msg: "",
              msgLevel: ErrorLevel.NORMAL,
              msgType: ErrorType.SUCCESS,
              data: { list: [], total: 0 }
            });
          break;
      }
      fetchFn(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.data = list;
          this.total = total;
        })
        .finally(() => (this.loading = false));
    },
    refreshTriggerData() {
      this.loading = true;
      api
        .getTriggerScoreSyncErrorList(this.queryParams)
        .then(res => {
          const { list, total } = res.data;
          this.triggerData = list;
          this.total = total;
        })
        .finally(() => (this.loading = false));
    },
    syncStateGridOrder(
      purchaseOrderId: string,
      purchaseOrderItemId: string,
      dataId: string,
      dataFormat?: StateGridOrderSyncDataFormatEnum
    ) {
      const params: IStateGridOrderSyncParams = {
        orderId: purchaseOrderId,
        orderItemId: purchaseOrderItemId,
        dataType: this.type,
        dataId,
        dataFormat
      };
      return syncApi.syncStateGridOrder(params);
    },
    pageSizeChange(pageSize: number) {
      this.queryParams.pageSize = pageSize;
    },
    pageNoChange(pageNo: number) {
      this.queryParams.pageNo = pageNo;
    },
    sortChange(orderByField: string, orderByType: OrderDirectionEnum) {
      this.queryParams.orderByField = orderByField;
      this.queryParams.orderByType = orderByType;
    }
  }
});
