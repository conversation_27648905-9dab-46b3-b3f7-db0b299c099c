import { FontIcon } from "@/components/ReIcon";
import { ISalesOrderLine } from "@/models";
import { TableColumnRenderer } from "@pureadmin/table";
import { ElTooltip } from "element-plus";
import { useProductOrderStore } from "@/store/modules";
import { TableWidth } from "@/enums";

export function useColumns() {
  const productOrderStore = useProductOrderStore();
  const columns: TableColumnList = [
    {
      label: "销售订单行",
      prop: "soNo",
      width: TableWidth.order
    },
    {
      label: "销售订单行项目",
      prop: "soItemNo",
      headerRenderer: () => {
        return (
          <div class="flex items-center">
            <div class="mr-1">销售订单行项目</div>
            <ElTooltip effect="dark" content="点击销售订单行项目号可快速添加生产订单" placement="top">
              <div class="w-5 text-center cursor-pointer">
                <FontIcon class="text-base text-secondary" icon="icon-warning-fill"></FontIcon>
              </div>
            </ElTooltip>
          </div>
        );
      },
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div class={"cursor-pointer text-primary"} onClick={() => addProductOrderBySoItemNo(data.row)}>
            {data.row.soItemNo}
          </div>
        );
      }
    }
  ];

  const addProductOrderBySoItemNo = (data: ISalesOrderLine) => {
    productOrderStore.setSelectedSaleOrderLineId(data.id);
    productOrderStore.setCreateProductOrder({ unit: data.materialUnit });
    productOrderStore.setProductOrderModalVisible(true);
    productOrderStore.setCreateProductOrderFromBySoItemNo(true);
  };

  return { columns };
}
