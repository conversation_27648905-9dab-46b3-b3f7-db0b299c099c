import { ColumnWidth, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { ComputedRef, computed } from "vue";

export function genReportWorkColumns(isProductionOrder: ComputedRef<boolean>) {
  const { dateFormatter } = useTableCellFormatter();
  const columns = computed(() => {
    const baseColumns = [
      {
        label: "选择",
        prop: "selection",
        type: "selection",
        fixed: "left",
        width: ColumnWidth.Char1
      },
      {
        label: "报工批次号",
        prop: "productBatchNo",
        width: TableWidth.order,
        slot: "ProductBatchNo"
      },
      {
        label: "工序",
        prop: "processName",
        sortable: "custom",
        width: TableWidth.largeName
      },
      isProductionOrder.value
        ? {
            label: "工单编号",
            prop: "woNo",
            width: TableWidth.largeOrder,
            slot: "woNo"
          }
        : null,
      {
        label: "设备",
        prop: "deviceName",
        width: TableWidth.largeName
      },
      {
        label: "开始时间",
        prop: "workStartTime",
        sortable: "custom",
        width: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "结束时间",
        prop: "workEndTime",
        width: TableWidth.dateTime,
        formatter: dateFormatter()
      },
      {
        label: "报工地址",
        prop: "buyerProvince"
      },
      {
        label: "操作",
        prop: "operation",
        fixed: "right",
        width: TableWidth.operation,
        slot: "operation"
      }
    ];

    return baseColumns.filter(column => Boolean(column));
  });

  return { columns };
}
