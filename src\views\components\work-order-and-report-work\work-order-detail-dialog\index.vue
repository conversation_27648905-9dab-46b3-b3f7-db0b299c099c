<template>
  <div class="inline-block">
    <div @click="openDialog">
      <slot>
        <el-button link type="primary"> 详情 </el-button>
      </slot>
    </div>
    <el-dialog
      v-model="dialogVisible"
      title="工单详情"
      align-center
      draggable
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-loading="loading">
        <detail-content :detail="detail" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { getWorkOrderById } from "@/api/work-order";
import { useLoadingFn } from "@/utils/useLoadingFn";
import DetailContent from "./detail-content.vue";
import { IWorkOrder } from "@/models";

/**
 * 工单详情弹窗
 */

const props = defineProps<{
  workId: string;
}>();

const dialogVisible = ref(false);
const detail = ref<IWorkOrder>({} as IWorkOrder);
const loading = ref(false);

const requestWorkOrderDetail = useLoadingFn(async () => {
  const { data } = await getWorkOrderById(props.workId);
  detail.value = data;
}, loading);

function clearDetail() {
  detail.value = {} as IWorkOrder;
}

function openDialog() {
  dialogVisible.value = true;
}

watch(dialogVisible, visible => {
  if (visible) {
    requestWorkOrderDetail();
  } else {
    clearDetail();
  }
});
</script>

<style scoped></style>
