import { TableWidth, DataTypeEnumEjjMapDesc, FasteningPassStatusEnumMapDesc, MainRegulatEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 生产工艺及过程检测
 */
export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig: TableColumnList = [];
  const commonColumn: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      minWidth: TableWidth.operation
    }
  ];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "采集类型编码",
        prop: "collectionCode",
        slot: "collection",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "检验次数",
        prop: "checkNum",
        minWidth: TableWidth.largeNumber
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      },
      {
        label: "钢印号/字头号",
        prop: "stampingNumber",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "标准力拒要求值",
        prop: "standardForceRejectValue",
        minWidth: TableWidth.largeNumber
      },
      {
        label: "实际力拒值",
        prop: "actualForceRejectValue",
        minWidth: TableWidth.largeNumber
      },
      {
        label: "紧固合格状态",
        prop: "fasteningPassStatus",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return FasteningPassStatusEnumMapDesc[data.row.fasteningPassStatus];
        }
      },
      {
        label: "紧固操作人员",
        prop: "fasteningOperator",
        minWidth: TableWidth.name
      },
      {
        label: "互检人员",
        prop: "mutualInspector",
        minWidth: TableWidth.name
      },
      {
        label: "专检人员",
        prop: "specialInspector",
        minWidth: TableWidth.name
      },
      {
        label: "清洁操作人员",
        prop: "cleaningOperator",
        minWidth: TableWidth.name
      },
      {
        label: "子实物ID",
        prop: "subPhysicalItemId",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        minWidth: TableWidth.largeOrder
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "采集类型编码",
        prop: "collectionCode",
        slot: "collection",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "检验次数",
        prop: "checkNum",
        minWidth: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return data.row.checkNum ? `第${data.row.checkNum}次` : "--";
        }
      },
      {
        label: "检验人员姓名",
        prop: "inspectionPersonnel",
        minWidth: TableWidth.name
      },
      {
        label: "数据类型",
        prop: "dataType",
        minWidth: TableWidth.status,
        cellRenderer: (data: TableColumnRenderer) => {
          return DataTypeEnumEjjMapDesc[data.row.dataType];
        }
      },
      {
        label: "主体变/调补变类型",
        prop: "mainRegulatingTransformer",
        minWidth: TableWidth.largeName,
        cellRenderer: (data: TableColumnRenderer) => {
          return MainRegulatEnumMapDesc[data.row.mainRegulatingTransformer];
        }
      }
    ];
  }

  return [...columnsConfig, ...commonColumn];
}
