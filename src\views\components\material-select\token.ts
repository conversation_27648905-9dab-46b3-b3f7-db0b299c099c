import { InjectionKey } from "vue";
import { IMaterial } from "@/models";
import { PaginationProps } from "@pureadmin/table";

export interface IMaterialSelectCtx {
  pagination?: PaginationProps;
  keyword?: string;
  data: Array<IMaterial>;
  loading: boolean;
  selectedId?: string;
  selectedMaterial?: IMaterial;
  refresh(): void;
}

export const materialSelectKey: InjectionKey<IMaterialSelectCtx> = Symbol("material select");
