import { SyncStatusEnum } from "@/enums/south-grid-access";
import { ProcessEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models/i-base";

export interface IStartWork extends IBase {
  /**
   * 开工编码
   */
  productionStartNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 工序编码
   */
  processCode?: ProcessEnum;
  /**
   * 生产设备编码
   */
  deviceCode?: string;
  /**
   * 车间编码
   */
  workshopCode?: string;
  /**
   * 实际开始时间
   */
  actualStartTime?: string;
  /**
   * 平台返回生产开工唯一编码
   */
  productionStartId?: string;
  /**
   * 实际结束时间
   */
  actualEndTime?: string;
  /**
   * 状态：0-开工，1-完工
   */
  status?: number;
  /**
   * 是否同步,0-未同步，1-已同步
   */
  isSync?: SyncStatusEnum;
}
