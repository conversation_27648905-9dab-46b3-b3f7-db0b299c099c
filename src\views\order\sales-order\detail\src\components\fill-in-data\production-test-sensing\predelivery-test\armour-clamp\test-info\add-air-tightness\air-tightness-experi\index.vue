<template>
  <div class="air-tightness-experi-form">
    <DynamicForm
      ref="dynamicTableFormRef"
      v-if="formDataMetaDataRef?.length"
      :dynamic-form-data="formDataMetaDataRef"
      :edit-mode="true"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import DynamicForm from "@/components/DynamicForm";
import { useRawMaterialCheckInfoHook } from "@/views/config/raw-material/hook/useRawMaterialCheck";
import { useEXFactoryExperimentStore } from "@/store/modules";

const dynamicTableFormRef = ref<InstanceType<typeof DynamicForm>>();

// 原材料检测项数据
const { getRawMaterialCheckFormData } = useRawMaterialCheckInfoHook();

const formDataMetaDataRef = ref(undefined);

watchEffect(() => {
  formDataMetaDataRef.value = getRawMaterialCheckFormData(useEXFactoryExperimentStore().formMetadataModelInfos);
});

const getQMXExperimentValue = (): Promise<{ [key: string]: any }> => {
  return dynamicTableFormRef.value?.payloadFormData();
};
defineExpose({
  getQMXExperimentValue
});
</script>

<style scoped lang="scss"></style>
