import { ColumnWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { RouterLink } from "vue-router";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const { dateFormatter, percentFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "质量规范",
      prop: "qualityName",
      minWidth: ColumnWidth.Char10,
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <RouterLink to={`/quality-specification/${data.row.id}`} class="text-primary">
            {data.row.qualityName}
          </RouterLink>
        );
      }
    },
    {
      label: "启用状态",
      prop: "enabled",
      width: ColumnWidth.Char4,
      slot: "enabled",
      cellRenderer: (data: TableColumnRenderer) => {
        return <el-tag type={data.row.enabled ? "" : "danger"}>{data.row.enabled ? "启用" : "停用"}</el-tag>;
      }
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "原材料权重",
      prop: "rawMaterialWeight",
      minWidth: ColumnWidth.Char8,
      formatter: percentFormatter
    },
    {
      label: "生产过程权重",
      prop: "processWeight",
      minWidth: ColumnWidth.Char8,
      formatter: percentFormatter
    },
    {
      label: "出厂试验权重",
      prop: "experimentWeight",
      minWidth: ColumnWidth.Char8,
      formatter: percentFormatter
    },
    {
      label: "工艺稳定性权重",
      prop: "processStabilityWeight",
      minWidth: ColumnWidth.Char8,
      formatter: percentFormatter
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "更新时间",
      prop: "updateTime",
      minWidth: ColumnWidth.Char12,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "opertion",
      width: ColumnWidth.Char8,
      fixed: "right",
      slot: "opertion"
    }
  ];

  return { columnsConfig };
}
