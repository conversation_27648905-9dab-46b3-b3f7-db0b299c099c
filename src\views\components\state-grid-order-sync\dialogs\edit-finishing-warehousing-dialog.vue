<template>
  <el-dialog
    title="编辑成品信息"
    class="default"
    v-model="editVisible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <FinishingWarehousingForm ref="editFormRef" :subclassCode="subclassCode" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import FinishingWarehousingForm from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/edti-finishedWarehousing/index.vue";
import { useEdit } from "@/views/components/state-grid-order-sync/hooks";
import { ICreateFinishedProductStorage, IFinishedProductionSync, IFinishedProductStorage } from "@/models";
import { useFinishedProductStorageStore } from "@/store/modules";
import { provide, reactive, watch } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";

const props = defineProps<{
  subclassCode: string;
}>();

const finishedProductStorageStore = useFinishedProductStorageStore();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof FinishingWarehousingForm>>(updateProductionInfo);

const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

async function openEditDialog(sync: IFinishedProductionSync) {
  editVisible.value = true;
  const data: IFinishedProductStorage = await finishedProductStorageStore
    .getFinishedProductStorageDetailById(sync.dataId)
    .then(res => res.data);
  finishedProductStorageStore.setFinishedProductStorageDetail(data);
  stateGridOrderSyncEditCtx.editData = {
    id: sync.dataId,
    no: sync.productNo
  };
}

async function updateProductionInfo() {
  const formValue: ICreateFinishedProductStorage | boolean = await editFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  await finishedProductStorageStore.editFinishedProductStorage(formValue);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
