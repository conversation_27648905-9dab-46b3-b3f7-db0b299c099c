<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="form.templateName" clearable placeholder="请输入模板名称" maxlength="100" />
        </el-form-item>
        <el-form-item label="二维码尺寸" prop="size">
          <el-input v-model="form.size" type="number" clearable placeholder="请输入二维码尺寸" maxlength="100" />
        </el-form-item>
        <el-form-item label="二维码位置" prop="location">
          <el-input v-model="form.location" clearable placeholder="请输入二维码位置" maxlength="100" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row v-if="props.isAddMode">
      <el-col :span="24">
        <el-form-item required prop="excelFile" label="合格证模板">
          <el-upload
            class="w-full"
            ref="uploadRef"
            drag
            v-model:file-list="fileList"
            :auto-upload="false"
            :accept="excelAccept"
            :on-change="onFileChange"
            :limit="1"
            :on-exceed="onExceed"
            :on-remove="onRemove"
          >
            <el-icon size="24"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              <div class="upload-text"><span>将模板文件拖到此处，或</span><em>点击上传</em></div>
              <div class="upload-tips">
                <span>仅支持excel格式文件</span>
              </div>
            </div>
          </el-upload>
          <div v-show="excelFile" class="w-full h-80 overflow-hidden">
            <ExcelEditor class="block" ref="excelEditorRef" :blob="excelFile" :preview="true" />
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { FormInstance, FormRules, UploadFile } from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { ICertificateForm } from "../../models";
import ExcelEditor from "../../components/excel-editor/editor.vue";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  getExcelFile,
  emptyExcelFile
});

const excelTypes = ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"];
const excelAccept: string = excelTypes.join(",");
const excelEditorRef = ref<InstanceType<typeof ExcelEditor>>();
const props = withDefaults(defineProps<{ isAddMode?: boolean }>(), {
  isAddMode: true
});

const form = reactive<ICertificateForm>({
  templateName: ""
});
const formRef = ref<FormInstance>();
const excelFile = ref<File>();
const fileList = ref<Array<File>>([]);
const uploadRef = ref();

const rules: FormRules = {
  templateName: [{ required: true, message: "请输入模板名称", trigger: "change" }],
  excelFile: [{ required: true, message: "请选择合格证模板", validator: validateExcelFile }],
  size: [{ required: true, message: "请输入二维码尺寸", trigger: "change" }],
  location: [{ required: true, message: "请输入二维码位置", trigger: "change" }]
};

watch(excelFile, () => {
  formRef.value.clearValidate(["excelFile"]);
});

const onExceed = (files: Array<File>) => {
  uploadRef.value?.handleRemove(files);
  fileList.value = files;
  if (files && files.length) {
    excelFile.value = files[0];
  }
};

const onRemove = () => {
  fileList.value = undefined;
  excelFile.value = undefined;
};

const onFileChange = async (file: UploadFile) => {
  excelFile.value = file.raw;
  setTimeout(() => {
    excelEditorRef.value.onPreview();
  }, 500);
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: Partial<ICertificateForm>) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

function validateExcelFile(rule: any, value: any, callback: Function) {
  if (!excelFile.value) {
    return callback(new Error("请选择合格证模板"));
  }
  return callback();
}

function getExcelFile(): File {
  return excelFile.value;
}

function emptyExcelFile(): void {
  excelFile.value = null;
}
</script>

<style lang="scss" scoped>
:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
