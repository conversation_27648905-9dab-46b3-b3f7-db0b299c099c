import { TableWidth } from "@/enums";
import { IResponse } from "@/models";
import { EDiagType } from "@/models/raw-material/i-raw-material-res";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { previewUploadFile } from "@/utils/uploadFiles";
import { TableColumnRenderer } from "@pureadmin/table";
import { ElButton, ElDivider, ElMessage, ElMessageBox, ElPopover } from "element-plus";
import { PermissionKey } from "@/consts";
import { computed, inject } from "vue";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

export function useRawMaterialInspectColumns() {
  /** 根据工序判断是否可以 新增/编辑 */
  const isCanOperateAddAndEdit = computed(() => useRawMaterialGroupUnitStore().getIsCanOperateAddAndEdit);
  const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
  /** 金具字段数据 */
  const ArmourClampColumn = [
    {
      label: "检验标准",
      prop: "inspectStandard",
      width: TableWidth.largeType
    }
  ];
  const columns: TableColumnList = [
    {
      label: "原材料编号",
      prop: "code",
      minWidth: TableWidth.order
    },
    {
      label: "原材料名称",
      prop: "name",
      minWidth: TableWidth.name
    },
    {
      label: "原材料类型",
      prop: "processName",
      width: TableWidth.largeType
    },
    {
      label: "原材料批次号",
      prop: "materialBatchNo",
      width: TableWidth.largeOrder
    },
    {
      label: "检验批次号",
      prop: "inspectBatchNo",
      width: TableWidth.order
    },
    {
      label: "品牌",
      prop: "borMaterials",
      minWidth: TableWidth.order
    },
    {
      label: "计量单位",
      prop: "partUnit",
      width: TableWidth.unit
    },
    {
      label: "规格型号",
      prop: "modelCode",
      minWidth: TableWidth.largeOrder
    },
    ...(prodCtx?.isArmourClamp ? ArmourClampColumn : []),
    {
      label: "生产厂检测报告",
      prop: "inspectionReport",
      minWidth: TableWidth.largeName,
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.inspectionReport?.length > 0 ? (
          <div>
            {data.row?.inspectionReport.map(list => {
              return (
                <ElPopover width={60} placement="left" trigger="hover">
                  {{
                    default: () => (
                      <div class="flex flex-col items-center">
                        <ElButton type="primary" link onClick={() => previewFile(list)}>
                          预览
                        </ElButton>
                        <ElDivider class="!my-3" />
                        <ElButton type="primary" link onClick={() => downloadFile(list.id, list.name)}>
                          下载
                        </ElButton>
                      </div>
                    ),
                    reference: () => (
                      <ElButton type="primary" link>
                        <span class="name">{list.name}</span>
                      </ElButton>
                    )
                  }}
                </ElPopover>
              );
            })}
          </div>
        ) : (
          <span>--</span>
        );
      }
    },
    {
      label: "来料检测报告",
      prop: "spotCheckReport",
      minWidth: TableWidth.largeName,
      cellRenderer: (data: TableColumnRenderer) => {
        return data.row.spotCheckReport?.length > 0 ? (
          <div>
            {data.row?.spotCheckReport.map(list => {
              return (
                <ElPopover width={60} placement="left" trigger="hover">
                  {{
                    default: () => (
                      <div class="flex flex-col items-center">
                        <ElButton type="primary" link onClick={() => previewFile(list)}>
                          预览
                        </ElButton>
                        <ElDivider class="!my-3" />
                        <ElButton type="primary" link onClick={() => downloadFile(list.id, list.name)}>
                          下载
                        </ElButton>
                      </div>
                    ),
                    reference: () => (
                      <ElButton type="primary" link>
                        <span class="name">{list.name}</span>
                      </ElButton>
                    )
                  }}
                </ElPopover>
              );
            })}
          </div>
        ) : (
          <span>--</span>
        );
      }
    },
    {
      label: "操作",
      prop: "op",
      width: TableWidth.operations,
      fixed: "right",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            <ElButton link type="primary" onClick={() => lookRawMaterialDetail(data.row.id)}>
              详情
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseRawMaterialEdit}
              link
              type="primary"
              v-show={isCanOperateAddAndEdit.value}
              onClick={() => editRawMaterialDetail(data.row.id)}
            >
              编辑
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseRawMaterialDelete}
              link
              type="danger"
              onClick={() => onDeleteRawMaterial(data.row.id)}
            >
              删除
            </ElButton>
          </div>
        );
      }
    }
  ];

  const productionTestSensingStore = useSalesProductionTestSensingStore();
  const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
  /**
   * 详情数据
   */
  const lookRawMaterialDetail = async (rowId: string) => {
    await rawMaterialGroupUnitStore.getDetailOfRawMaterialGroupUnit(rowId, EDiagType.Detail);
    rawMaterialGroupUnitStore.setDetailRawMaterialVisible(true);
  };

  /**
   * 编辑数据
   */
  const editRawMaterialDetail = async (rowId: string) => {
    await rawMaterialGroupUnitStore.getDetailOfRawMaterialGroupUnit(rowId, EDiagType.Edit);
    rawMaterialGroupUnitStore.setEditRawMaterialVisible(true, EDiagType.Edit);
  };

  /**
   * 删除数据
   */
  const onDeleteRawMaterial = (rowId: string) => {
    ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        const res: IResponse<number> = await rawMaterialGroupUnitStore.delRawMaterialByIdAction(rowId);
        if (!res.data) {
          ElMessage({ type: "warning", message: res.msg || "网络异常" });
          return;
        }
        ElMessage({ type: "success", message: "删除成功" });
        // 更新工序数据状态
        rawMaterialGroupUnitStore.getRawMaterialGroupUnitProcessAction({}, true);
        productionTestSensingStore.refreshProductionProcessStatus();
      })
      .catch(() => {});
  };

  // 预览文件
  const previewFile = fileData => {
    const { id, name, url } = fileData;
    if (id) {
      previewUploadFile({ id, name, url });
    }
  };

  async function downloadFile(id: string, name: string) {
    const blob = await downLoadFile(id);
    downloadByData(blob, name, blob.type);
  }

  return {
    columns
  };
}
