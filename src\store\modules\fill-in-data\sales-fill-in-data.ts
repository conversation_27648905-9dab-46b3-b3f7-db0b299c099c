import { defineStore } from "pinia";
import { IProductOrder, IWorkOrder } from "@/models";
import * as workOrderApi from "@/api/work-order";
import * as productionOrderApi from "@/api/production-order";
import { useSalesOrderDetailStore } from "@/store/modules";
import { useMaterial } from "@/utils/material";

type State = {
  /** 工单ID */
  dataId: string;
  productionId?: string;
  data: IWorkOrder | IProductOrder;
  isArmourClamp?: boolean;
};

export const useSalesFillInDataStore = defineStore({
  id: "cx-sales-fill-in-data",
  state: (): State => ({
    dataId: undefined,
    data: undefined,
    /** 非线缆--工单进入填报详情--需要传入 productionId */
    productionId: undefined,
    isArmourClamp: false // 是否金具
  }),
  actions: {
    async refreshData(workOrderId: string, productionId?: string) {
      this.dataId = workOrderId;
      this.productionId = productionId;
      this.data = await (useSalesOrderDetailStore().isCable
        ? productionOrderApi.getProductionOrderDetailById(workOrderId).then(res => res.data)
        : workOrderApi.getWorkOrderById(workOrderId).then(res => res.data));

      this.setIsArmourClamp(this.data.subClassCode);
    },

    /** 金具 */
    setIsArmourClamp(subClassCode: string) {
      const materialTool = useMaterial();
      this.isArmourClamp = materialTool.isArmourClamp(subClassCode);
    }
  }
});
