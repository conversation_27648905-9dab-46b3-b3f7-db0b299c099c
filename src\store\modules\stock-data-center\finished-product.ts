import {
  delFinishedProduct,
  getDetailFinishedProductById,
  getFinishedProductList,
  getPurchaseLinePage,
  putFinishedProductInfo,
  saveFinishedProduct
} from "@/api/stock-data-center/finished-product";
import { IListResponse, IResponse } from "@/models";
import {
  IFinishedProduct,
  IFinisedProductReq,
  IPurchaseOrderList,
  ISearchFinisedProduct,
  ISearchPurchaseOrderReq
} from "@/models/stock-data-center/i-finished-product";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";
import { defineStore } from "pinia";

export const useFinishedProductionStore = defineStore({
  id: "finished-production-store",
  state: () => ({
    tableTotal: 0 as number,
    queryParams: {},
    isAddFinishedProduct: true, // 是否是新增产成品
    finishedProductionTable: [] as Array<IFinishedProduct>,
    detailFinishedProductBaseInfo: {} as IFinishedProduct,
    editFinishedProductBaseInfo: {} as IFinishedProduct,
    purchaseLineOrderList: [] as Array<IPurchaseOrderList>,
    purchaseLineTotal: 0
  }),
  actions: {
    /**
     * 查询产成品库存列表
     * @param queryParams
     */
    queryFinishedProductList(params?: ISearchFinisedProduct) {
      this.queryParams = { ...this.queryParams, ...params };
      this.getFinishedProductListAction();
    },

    /**
     * 获取产成品库存列表
     */
    async getFinishedProductListAction() {
      const resData = (await getFinishedProductList(omitBy(this.queryParams, val => isAllEmpty(val)))).data;
      if (resData && resData.list?.length) {
        this.finishedProductionTable = resData.list;
        this.tableTotal = resData.total;
      } else {
        this.finishedProductionTable = [];
        this.tableTotal = 0;
      }
    },

    /**
     * 新增产成品信息
     */
    async addFinishedProduct(paramsData: IFinisedProductReq): Promise<IResponse<boolean>> {
      return await saveFinishedProduct(paramsData);
    },

    /**
     * 根据Id获取产成品的详情信息
     */
    async getDetailFinishedProduct(id: string) {
      const detailRes = await getDetailFinishedProductById(id);
      if (detailRes.data?.id) {
        this.detailFinishedProductBaseInfo = detailRes.data;
        this.editFinishedProductBaseInfo = detailRes.data;
      }
    },

    /**
     * 修改产成品信息
     */
    async updateFinishedProduct(paramsData: IFinisedProductReq): Promise<IResponse<boolean>> {
      return await putFinishedProductInfo(paramsData);
    },

    /**
     * 删除产成品数据
     */
    async delFinishedProductById(id: string): Promise<IResponse<boolean>> {
      return await delFinishedProduct(id);
    },

    /**
     * 根据页码数量改变查询产成品数据
     */
    getFinishedProductListByPageSize(pageSize: number) {
      this.queryParams.pageSize = pageSize;
      this.queryParams.pageNo = 1;
      this.getFinishedProductListAction();
    },
    /**
     * 根据页码改变查询产成品数据
     */
    getFinishedProductListByPageNo(pageNo: number) {
      this.queryParams.pageNo = pageNo;
      this.getFinishedProductListAction();
    },

    /**
     * 获取国网采购订单
     */
    async getPurchaseLinePage(paramsData: ISearchPurchaseOrderReq): Promise<IListResponse<IPurchaseOrderList>> {
      return await getPurchaseLinePage(paramsData);
    },
    initData() {
      this.detailFinishedProductBaseInfo = {};
      this.editFinishedProductBaseInfo = {};
    },
    initQueryData() {
      this.queryParams = {};
      this.finishedProductionTable = [];
    }
  }
});
