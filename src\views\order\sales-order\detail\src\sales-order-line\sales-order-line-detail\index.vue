<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="cx-form" label-width="9rem">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="销售订单行项目号" prop="soItemNo">
          <SerialNumber
            v-model="form.soItemNo"
            :code="SALES_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :parent-no="form.soNo"
            :create="!form.id"
            placeholder="请输入销售订单行项目号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <SubclassSelect
            v-model="form.subClassCode"
            :disabled="!!form.id"
            placeholder="请选择物资种类"
            clearable
            :category-code="props.categoryCode"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="选择物料">
          <MaterialSelect
            :selectedId="form.materialId"
            :subClassCode="form.subClassCode"
            @material-change="materialChange"
            :disabled="!form.subClassCode"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input placeholder="请输入物料编码" v-model="form.materialCode" disabled />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料名称" prop="materialName">
          <el-input placeholder="请输入物料名称" v-model="form.materialName" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料单位" prop="materialUnit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="form.subClassCode"
            class="w-full"
            v-model="form.materialUnit"
            placeholder="请选择物料单位"
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input placeholder="请输入物料描述" v-model="form.materialDesc" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationType">
          <el-input placeholder="请输入" v-model="form.specificationType" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageLevel">
          <EnumSelect
            class="w-full"
            v-model="form.voltageLevel"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            allow-create
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料数量" prop="materialNumber">
          <el-input-number
            placeholder="请输入物料数量"
            v-model="form.materialNumber"
            :min="0"
            class="!w-full"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="项目号" prop="prjCode">
          <el-input placeholder="请输入项目号" v-model="form.prjCode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产工号" prop="productionWorkNo">
          <el-input placeholder="请输入 生产工号" v-model="form.productionWorkNo" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="实物ID" prop="entityId">
          <el-input placeholder="请输入 实物ID" v-model="form.entityId" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IMaterial, ISalesOrderLineDetail } from "@/models";
import { useSalesOrderDetailStore, useSalesOrderLineManagementStore } from "@/store/modules";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import EnumSelect from "@/components/EnumSelect";
import { VoltageClassesEnum } from "@/enums";
import { MATERIAL_INFO_PROPERTY, SALES_ORDER_NO_CODE } from "@/consts";
import SerialNumber from "@/components/SerialNumber";
import MaterialSelect from "@/views/components/material-select";
import { set } from "lodash-unified";
import { MEASURE_UNIT } from "@/consts";
import Dictionary from "@/components/Dictionary";
defineExpose({
  getValidValue,
  validate,
  getValidSubClassCode,
  resetFields
});
const emit = defineEmits<{
  (e: "unbindPurchaseItemId"): void;
}>();
const props = defineProps<{
  soNo: string;
  categoryCode: string;
}>();
const linkSubClassCode = ref<string>();
const formRef = ref<FormInstance>();
const salesOrderDetailStore = useSalesOrderDetailStore();
const salesOrderLineManagementStore = useSalesOrderLineManagementStore();
const form = ref<Partial<ISalesOrderLineDetail>>({});
const rules: FormRules = {
  soItemNo: [{ required: true, message: "销售订单行项目号", trigger: "change" }],
  subClassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  materialCode: [{ required: true, message: requiredMessage("物料编号"), trigger: "change" }],
  materialName: [{ required: true, message: requiredMessage("物料名称"), trigger: "change" }],
  materialUnit: [{ required: true, message: requiredMessage("物料单位"), trigger: "change" }],
  materialNumber: [{ required: true, message: requiredMessage("物料数量"), trigger: "change" }]
};

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<ISalesOrderLineDetail> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form.value as ISalesOrderLineDetail;
}

async function resetFields(): Promise<void> {
  formRef.value.resetFields();
  form.value.soNo = undefined;
  setTimeout(() => {
    form.value.soNo = props.soNo;
  }, 10);
}

async function getValidSubClassCode(): Promise<string> {
  if (
    !(await formRef.value.validateField("subClassCode", val => {
      return !val;
    }))
  ) {
    return Promise.reject("invalid");
  }
  return form.value.subClassCode;
}

onMounted(() => {
  if (salesOrderDetailStore.salesOrder) {
    const { buyerName, id, soNo } = salesOrderDetailStore.salesOrder;
    form.value.buyerName = buyerName;
    form.value.salesId = id;
    form.value.soNo = soNo;
  }
});

function materialChange(material: IMaterial) {
  const { materialDescribe, specificationModel, voltageClass, id, subClassCode } = material;
  linkSubClassCode.value = material.subClassCode;
  form.value = {
    ...form.value,
    ...MATERIAL_INFO_PROPERTY.reduce((acc, prop) => {
      acc[prop] = material[prop];
      return acc;
    }, {}),
    specificationType: specificationModel,
    voltageLevel: voltageClass,
    materialDesc: materialDescribe,
    materialId: id,
    subClassCode
  };
}

watch(
  () => form.value.subClassCode,
  (newValue, oldValue) => {
    if (newValue != linkSubClassCode.value && oldValue) {
      MATERIAL_INFO_PROPERTY.forEach(property => {
        set(form.value, property, undefined);
      });
      emit("unbindPurchaseItemId");
    } else {
      linkSubClassCode.value = undefined;
    }
  }
);

watch(
  () => salesOrderLineManagementStore.saleOrderLineDetail,
  () => {
    if (salesOrderLineManagementStore.saleOrderLineDetail) {
      Object.assign(form.value, salesOrderLineManagementStore.saleOrderLineDetail);
    }
  },
  {
    immediate: true
  }
);
</script>
