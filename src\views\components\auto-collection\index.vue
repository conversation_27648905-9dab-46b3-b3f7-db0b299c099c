<template>
  <div class="stand-press-detail p-2">
    <div class="detail-info-content flex flex-col overflow-hidden pr-1">
      <div class="experiment-base-info p-2">
        <div class="experiment-container flex py-3 px-14">
          <el-row class="w-full">
            <el-col :span="4">
              <div class="content">
                <span class="label">工序</span>
                <span class="value">{{ reportWork.processName || emptyDefaultValue }}</span>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="content">
                <span class="label">设备</span>
                <span class="value">{{ reportWork.deviceName || emptyDefaultValue }}</span>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="content">
                <span class="label">开始时间</span>
                <span class="value">
                  {{ formatDate(reportWork.workStartTime, fullDateFormat) || emptyDefaultValue }}
                </span>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="content">
                <span class="label">结束时间</span>
                <span class="value">
                  <span class="value">
                    {{ formatDate(reportWork.workEndTime, fullDateFormat) || emptyDefaultValue }}
                  </span>
                </span>
              </div>
            </el-col>
            <el-col :span="5">
              <div class="content">
                <span class="label">报工地址</span>
                <span class="value">{{ reportWork.buyerProvince || emptyDefaultValue }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div class="m-2">
      <el-scrollbar :height="450" v-loading="loading">
        <div class="flex flex-row flex-wrap" v-if="experimentData.length">
          <div v-for="(item, index) in experimentData" :key="item.collectPoint" class="basis-1/3 mt-8">
            <AutoCollectLineCharts :key="index" :data="item" />
          </div>
        </div>
        <div v-else>
          <CxEmpty />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IDeviceAcquisition, IReportWork } from "@/models";
import { onMounted, ref, watch, reactive, onUnmounted } from "vue";
import { formatDate } from "@/utils/format";
import { emptyDefaultValue, fullDateFormat } from "@/consts";
import { IAutoCollectChartReq, IExperimentData } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import AutoCollectLineCharts from "@/views/components/auto-collection-charts/auto-collection-charts.vue";
import CxEmpty from "@/components/CxEmpty";
import { merge } from "lodash-unified";
import dayjs from "dayjs";
import { useAutoCollectionHook } from "../auto-collection/hooks/auto-collection-hook";
import { useCancelHttp } from "@/utils/http/cancel-http";
const props = defineProps<{
  id: string;
}>();
const reportWork = ref<IReportWork>({} as IReportWork);
let experimentData = reactive<Array<IExperimentData>>([]);
const loading = ref<boolean>(false);
const getHistoryAutoCollect = useLoadingFn(getHistoryAutoCollectChartInfo, loading);
const { getReportWorkById, getHistoryAutoCollectCharts, queryAcquisitionAll } = useAutoCollectionHook();
const cancelHttp = useCancelHttp();

onMounted(async () => {
  // 详情数据
  reportWork.value = await getReportWorkById(props.id);
});

watch(
  () => reportWork.value.deviceCode,
  async newVal => {
    if (!newVal) {
      cancelHttp.abort();
      experimentData.length = 0;
      return;
    }
    const params: IAutoCollectChartReq = merge(
      {
        deviceCode: reportWork.value.deviceCode,
        processId: reportWork.value.processId
      },
      getWorkTime(reportWork.value.workStartTime, reportWork.value.workEndTime)
    );
    await getHistoryAutoCollect(params);
  }
);

/** 获取自动采集项数据 --历史 */
async function getHistoryAutoCollectChartInfo(params: IAutoCollectChartReq) {
  const acquisitionList: Array<IDeviceAcquisition> = (await queryAcquisitionAll(reportWork.value.deviceId)) || [];
  const historyExperiment: Array<IExperimentData> = [];
  if (!acquisitionList.length) {
    return;
  }
  acquisitionList.forEach(device =>
    historyExperiment.push({
      collectPoint: device.no,
      values: [],
      collectName: device.name
    })
  );
  const charts = (await getHistoryAutoCollectCharts(params, cancelHttp.signal.value)) || [];
  if (!charts.length) {
    experimentData = historyExperiment;
    return;
  }
  const removeIndex: Array<number> = [];
  historyExperiment.forEach((experiment, i) => {
    const index = charts.findIndex(chart => chart.collectPoint === experiment.collectPoint);
    if (index > -1) {
      experiment.values = charts[index].values;
    } else {
      removeIndex.push(i);
    }
  });
  removeIndex.reverse().forEach(i => {
    historyExperiment.splice(i, 1);
  });
  experimentData = historyExperiment;
}

/** 1.报工时间 > 90min,开始时间是 结束时间 - 90min，结束时间不变；
 *  2.报工时间 < 90min，取全部数据
 *  3.没有结束时间 ，取结束是是当前时间，开始时间是 结束时间 - 1小时
 */
function getWorkTime(startTime, endTime) {
  if (!startTime && !endTime) {
    return {
      timeFrom: "",
      timeEnd: ""
    };
  }
  if (!endTime) {
    return {
      timeFrom: dayjs().subtract(1, "hours"),
      timeEnd: dayjs()
    };
  }
  const et = dayjs(endTime).subtract(90, "minutes");
  return {
    timeFrom: et.isBefore(startTime) ? startTime : et,
    timeEnd: endTime
  };
}
onUnmounted(() => {
  experimentData.length = 0;
});
</script>

<style scoped lang="scss">
.experiment-container {
  background-color: #f4f8f9;

  .content {
    display: flex;
    flex-direction: column;

    .label {
      @apply text-secondary mb-1;
    }

    .value {
      @apply text-primaryText text-middle;
    }
  }

  :deep(.el-descriptions__body) {
    background-color: transparent;
  }

  :deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
    border: none;
  }

  :deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
    color: var(--el-text-color-secondary);
    font-weight: normal;
  }

  :deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
    font-size: var(--el-font-size-medium);
  }
}
</style>
