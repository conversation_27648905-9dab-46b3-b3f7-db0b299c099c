<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40" v-if="isArmourClamp">
      <el-col :span="12">
        <el-form-item label="子种类名称" prop="minClassCode">
          <el-select class="w-full" v-model="form.minClassCode" filterable placeholder="请选择">
            <el-option v-for="item in minClassOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工单编号" prop="woNo">
          <SerialNumber
            v-model="form.woNo"
            :code="PRODUCT_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :create="props.type === 'create'"
            :parent-no="props.parentNo"
            placeholder="请输入工单编号"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40" v-if="isArmourClamp">
      <el-col :span="12">
        <el-form-item label="产品名称" prop="productName">
          <el-input placeholder="请输入" v-model="form.productName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="质量追溯码" prop="qualityTraceCode">
          <el-input placeholder="请输入" v-model="form.qualityTraceCode" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40" v-if="!isArmourClamp">
      <el-col :span="12">
        <el-form-item label="工单编号" prop="woNo">
          <SerialNumber
            v-model="form.woNo"
            :code="PRODUCT_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :create="props.type === 'create'"
            :parent-no="props.parentNo"
            placeholder="请输入工单编号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品编号" prop="productCode">
          <el-input placeholder="请输入 产品编号" v-model="form.productCode" :maxlength="productCodeMaxlength" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="工艺路线" prop="processRouteNo">
          <ProcessRoute
            v-model="form.processRouteNo"
            :subClassCode="subClassCode"
            @change="routeNoChange"
            @clearChange="clearChange"
            :disabled="disabled"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实物Id" prop="entityId">
          <el-input v-model="form.entityId" placeholder="请输入实物Id" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="工序" prop="processIds">
          <ProcessSelect
            v-model="form.processIds"
            :processOptions="processOptions"
            :isQuery="isQuery"
            :setDefaultAllCheck="true"
            :subClassCode="subClassCode"
            :disabled="disabled"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="生产数量" prop="amount">
          <el-input-number
            :min="0"
            v-model="form.amount"
            controls-position="right"
            placeholder="请输入生产数量"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="unit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="subClassCode"
            class="w-full"
            placeholder="请选择计量单位"
            v-model="form.unit"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料编号" prop="materialsCode">
          <el-input v-model="form.materialsCode" placeholder="请输入物料编号" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" disabled />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input v-model="form.materialDesc" placeholder="请输入物料描述" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料单位" prop="materialUnit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="subClassCode"
            v-model="form.materialUnit"
            placeholder="请选择物料单位"
            disabled
            class="w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationType">
          <el-input v-model="form.specificationType" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageLevel" :rules="voltageLevelRules">
          <EnumSelect
            v-model="form.voltageLevel"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            class="w-full"
            allow-create
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="工单状态3" prop="woStatus">
          <EnumSelect
            v-model="form.woStatus"
            placeholder="请选择接工单状态"
            :enum="ProductionStateEnum"
            enumName="productionStateEnum"
            clearable
            class="w-full"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划日期" prop="planDate">
          <el-date-picker
            v-model="form.planDate"
            type="daterange"
            label="计划时间"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            clearable
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1" label="实际开始日期" prop="actualStartDate" :rules="actualStartDateRules">
          <el-date-picker
            v-model="form.actualStartDate"
            label="实际开始日期"
            placeholder="请选择实际开始日期"
            class="flex-1"
            clearable
            :disabled-date="actualStartDateDisabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1" label="实际完成日期" prop="actualFinishDate" :rules="actualFinishDateRules">
          <el-date-picker
            v-model="form.actualFinishDate"
            label="实际完成日期"
            placeholder="请选择实际完成日期"
            class="flex-1"
            clearable
            :disabled-date="actualEndDateDisabledDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ProductionStateEnum, VoltageClassesEnum } from "@/enums";
import EnumSelect from "@/components/EnumSelect";
import { reactive, ref, watch, onMounted, computed } from "vue";
import { ICreateWorkOrder, IProcessRouteList } from "@/models";
import { FormInstance, FormItemRule, FormRules } from "element-plus";
import { maxlengthMessage, requiredMessage } from "@/utils/form";
import { PRODUCT_ORDER_NO_CODE, MEASURE_UNIT } from "@/consts";
import SerialNumber from "@/components/SerialNumber";
import Dictionary from "@/components/Dictionary";
import ProcessRoute from "@/views/order/components/process-route/process-route.vue";
import ProcessSelect from "@/views/order/components/process-select/process-select.vue";
import { useCategoryStore, useProcessStore } from "@/store/modules";
import { useMaterial } from "@/utils/material";
import { voltageGardeRequireWorkOrder } from "@/views/order/utils";

const props = defineProps<{
  type?: "create" | "update";
  parentNo?: string;
  actualStartDateRules?: Array<FormItemRule>;
  actualFinishDateRules?: Array<FormItemRule>;
  subClassCode?: string;
}>();

const materialTool = useMaterial();
const productCodeMaxlength = 50;
const actualStartDateRequired = ref<boolean>();
const voltageGradeRequire = ref<boolean>();
const subClassCode = computed(() => props.subClassCode || form.subclassCode || "");
const formRef = ref<FormInstance>();
const form = reactive<ICreateWorkOrder & { planDate: [Date, Date] }>({
  id: undefined,
  woNo: undefined,
  ipoNo: undefined,
  processRouteNo: null,
  processRouteId: undefined,
  amount: undefined,
  unit: undefined,
  materialsCode: undefined,
  materialName: undefined,
  materialDesc: undefined,
  materialUnit: undefined,
  specificationType: undefined,
  voltageLevel: undefined,
  woStatus: ProductionStateEnum.CREATE,
  planStartDate: undefined,
  planFinishDate: undefined,
  actualStartDate: undefined,
  actualFinishDate: undefined,
  processIds: "",
  salesLineId: undefined,
  productionId: undefined,
  purchaseId: undefined,
  productCode: undefined,
  planDate: undefined,
  minClassCode: undefined,
  productName: undefined,
  qualityTraceCode: undefined
});
const rules = reactive<FormRules>({
  woNo: [{ required: true, message: requiredMessage("工单编号"), trigger: "change" }],
  productCode: [
    // { required: true, message: requiredMessage("产品编号"), trigger: "change" },
    { max: productCodeMaxlength, message: maxlengthMessage("产品编号", productCodeMaxlength), trigger: "change" }
  ],
  processRouteNo: [{ required: false }],
  processIds: [{ required: true, message: requiredMessage("工序"), trigger: "change" }],
  amount: [{ required: true, message: requiredMessage("生产数量"), trigger: "change" }],
  unit: [{ required: true, message: requiredMessage("计量单位"), trigger: "change" }],
  materialsCode: [{ required: true, message: requiredMessage("物料编号"), trigger: "change" }],
  materialName: [{ required: true, message: requiredMessage("物料名称"), trigger: "change" }],
  materialDesc: [{ required: true, message: requiredMessage("物料描述"), trigger: "change" }],
  materialUnit: [{ required: true, message: requiredMessage("物料单位"), trigger: "change" }],
  specificationType: [{ required: true, message: requiredMessage("规格型号"), trigger: "change" }],
  woStatus: [{ required: true, message: requiredMessage("工单状态"), trigger: "change" }],
  planDate: [{ required: true, message: requiredMessage("计划日期"), trigger: "change" }],
  minClassCode: [{ required: true, message: requiredMessage("物资子种类"), trigger: "change" }],
  productName: [{ required: true, message: requiredMessage("产品名称"), trigger: "change" }],
  qualityTraceCode: [{ required: true, message: requiredMessage("质量追溯码"), trigger: "change" }]
});
const isArmourClamp = computed(() => materialTool.isArmourClamp(subClassCode.value));
const minClassOptions = ref<Array<{ label: string; value: string }>>([]);
const categoryStore = useCategoryStore();
const disabled = computed(() => props.type === "update");

const actualStartDateRules = computed(() => [
  ...(props.actualStartDateRules || []),
  {
    required: actualStartDateRequired.value,
    message: requiredMessage("实际开始日期"),
    trigger: "change"
  }
]);
const actualFinishDateRules = computed(() => props.actualStartDateRules);
const voltageLevelRules = computed(() => [
  {
    required: voltageGradeRequire.value,
    message: requiredMessage("电压等级"),
    trigger: "change"
  }
]);
const processStore = useProcessStore();
// 工艺路线对应的工序
const processOptions = ref([]);
const isQuery = ref<boolean>(true);

onMounted(() => {
  watch(
    () => form.actualFinishDate,
    actualFinishDate => {
      if (!actualFinishDate) {
        actualStartDateRequired.value = false;
        formRef.value?.clearValidate(["actualStartDate"]);
      } else {
        actualStartDateRequired.value = true;
      }
    },
    { immediate: true }
  );

  // 请求金具的物资子种类的下拉列表
  if (isArmourClamp.value) {
    initMinClassCodeList();
  }
});

watch(
  () => form.processRouteNo,
  newVal => {
    if (newVal) {
      const selectProcessRoute = processStore.processRouteOptions.find(item => item.code === newVal);
      if (!selectProcessRoute) {
        return;
      }
      form.processRouteId = selectProcessRoute.id;
      if (selectProcessRoute.processInfos) {
        isQuery.value = false;
        formatProcessInfos(selectProcessRoute);
      }
    } else {
      isQuery.value = true;
    }
  }
);

watch(
  subClassCode,
  newVal => {
    if (newVal) {
      voltageGradeRequire.value = voltageGardeRequireWorkOrder(newVal);
    }
  },
  {
    immediate: true
  }
);

function actualStartDateDisabledDate(date: Date) {
  if (date > new Date()) {
    return true;
  }

  if (!form.actualFinishDate) {
    return false;
  }
  return date > new Date(form.actualFinishDate);
}

function actualEndDateDisabledDate(date: Date) {
  if (date > new Date()) {
    return true;
  }

  if (!form.actualStartDate) {
    return false;
  }
  return date < new Date(form.actualStartDate);
}

function initializeValue(value: Partial<ICreateWorkOrder>) {
  if (!value) {
    return;
  }
  const { planStartDate, planFinishDate } = value;
  const planDate = [];
  if (planStartDate) {
    planDate.push(planStartDate);
  }
  if (planFinishDate) {
    planDate.push(planFinishDate);
  }
  Object.assign(form, value, { planDate });
}

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue() {
  if (!(await validate())) {
    return Promise.reject();
  }
  const { planDate, ...others } = form;
  return {
    ...others,
    planStartDate: planDate[0],
    planFinishDate: planDate[1]
  };
}

async function initMinClassCodeList() {
  if (subClassCode.value) {
    const options = await categoryStore.querySubMinClassCodeList(subClassCode.value);
    minClassOptions.value = options.map(options => ({
      label: options.categoryName,
      value: options.categoryCode
    }));
  } else {
    minClassOptions.value = [];
  }
}

/** 清除工艺路线 */
async function clearChange() {
  resetProcess();
}

/** 工艺路线改变 */
function routeNoChange(processRouteInfo: IProcessRouteList) {
  // 清空工序信息
  resetProcess();
  // 赋值工序信息
  formatProcessInfos(processRouteInfo);
}

function formatProcessInfos(processRouteInfo: IProcessRouteList) {
  processOptions.value = processRouteInfo?.processInfos.map(item => {
    return {
      label: item.processName,
      value: item.id
    };
  });
  if (!processOptions.value) {
    return;
  }
  const idsArr = processOptions.value.map(({ value }) => value);
  form.processIds = idsArr.join("-");
}

function resetProcess() {
  form.processIds = "";
}

defineExpose({
  initializeValue,
  getValidValue
});
</script>

<style scoped></style>
