import { fullDateFormat } from "@/consts";
import { ContractTypeEnumMapDisplayName, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import { RouterLink } from "vue-router";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();

  const columns: TableColumnList = [
    {
      label: "采购供货单编号",
      prop: "supplyNo",
      fixed: "left",
      minWidth: TableWidth.suborder,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <RouterLink class="text-primary" to={{ name: "supply-order-info", params: { id: data.row.id } }}>
            {data.row.supplyNo}
          </RouterLink>
        );
      }
    },
    {
      label: "合同类型",
      prop: "conType",
      minWidth: TableWidth.name,
      cell<PERSON>enderer(data: TableColumnRenderer) {
        return ContractTypeEnumMapDisplayName[data.row.conType];
      }
    },
    {
      label: "合同编号",
      prop: "conCode",
      width: TableWidth.name
    },
    {
      label: "货物名称",
      prop: "cargoName",
      minWidth: TableWidth.name
    },
    {
      label: "采购方公司名称",
      prop: "purchaseName",
      minWidth: TableWidth.name
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: TableWidth.largeName
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
