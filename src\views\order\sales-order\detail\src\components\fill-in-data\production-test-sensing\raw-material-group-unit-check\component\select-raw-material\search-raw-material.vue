<template>
  <div class="search">
    <el-input class="!w-96 mr-2" v-model="keyWords" clearable placeholder="原材料编号/原材料名称/检验批次号/规格型号" />
    <el-button type="primary" @click="searchChange">搜索</el-button>
  </div>
</template>

<script setup lang="ts">
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { ref } from "vue";

const keyWords = ref("");
const emits = defineEmits<{
  (e: "search", params: { keyWords?: string; processUnionCode?: string }): void;
}>();

// 搜索数据
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const searchChange = () => {
  const { processUnionCode } = rawMaterialGroupUnitStore.currentTagInfo;
  emits("search", {
    keyWords: keyWords.value,
    processUnionCode
  });
};
</script>

<style scoped lang="scss"></style>
