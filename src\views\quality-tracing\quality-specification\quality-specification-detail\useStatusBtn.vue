<template>
  <el-switch
    class="-translate-y-0.5"
    v-if="hasAuth(PermissionKey.qualityTracing.qualityTracingSpecificationEditStatus)"
    inline-prompt
    active-text="启用"
    inactive-text="停用"
    v-model="useStatus"
    :loading="loading"
    @change="requestToggleUseStatus"
  />
  <el-tag v-else :type="status ? '' : 'danger'">{{ status ? "启用" : "停用" }}</el-tag>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { toggleQualitySpecificationUseStatus } from "@/api/quality-tracing";
import { hasAuth } from "@/router/utils";
import { PermissionKey } from "@/consts/permission-key";

const props = defineProps<{
  /** 质量规范id */
  id: string;
  /** 启用状态 */
  status: boolean;
}>();

const emits = defineEmits<{
  /** 切换启用状态成功后置事件 */
  (event: "postToggleStatusSuccess"): void;
}>();

const useStatus = ref(props.status);
const loading = ref(false);

/**
 * @description: 请求切换启用状态
 */
const requestToggleUseStatus = useLoadingFn(async () => {
  try {
    const result = await toggleQualitySpecificationUseStatus(props.id);
    if (result) {
      ElMessage.success("操作成功");
      emits("postToggleStatusSuccess");
    }
  } catch (error) {
    // 如果请求失败，还原状态
    useStatus.value = !useStatus.value;
  }
}, loading);
</script>
<style lang="scss" scoped></style>
