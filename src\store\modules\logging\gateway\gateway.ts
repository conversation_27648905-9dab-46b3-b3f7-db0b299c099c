import {
  getDownLoadUrlOfDetailLog,
  getGatewayDetailLogById,
  getGatewayDetailLogList,
  getGatewayLogList,
  getSearchOptionList
} from "@/api/logging/gateway/gateway-log";
import {
  EStatusCode,
  IDownLoadDetailLog,
  IGatewayDetailList,
  IGatewayList,
  ISearchDetailReq,
  ISearchGatewayReq,
  ISearchOptionList
} from "@/models/logging";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isNullOrUnDef } from "@pureadmin/utils";

export const useGatewayStore = defineStore({
  id: "gateway-store",
  state: () => ({
    tableTotal: 0,
    gatewayQuery: {} as ISearchGatewayReq,
    gatewayTableData: [] as Array<IGatewayList>,
    currentDetailInfo: {} as IGatewayList,
    detailTableTotal: 0 as number,
    detailQuery: {} as ISearchDetailReq,
    detailTableData: [] as Array<IGatewayDetailList>,
    matOptions: [] as Array<ISearchOptionList>
  }),
  getters: {
    getFetchErrorNumber() {
      return this.detailTableData.filter((item: IGatewayDetailList) => item.statusCode !== EStatusCode.Success)?.length;
    },
    getFetchSuccessNumber() {
      return this.detailTableData.filter((item: IGatewayDetailList) => item.statusCode === EStatusCode.Success)?.length;
    }
  },
  actions: {
    /** 获取网关设备的搜索下拉框 */
    async getGatewaySearchOptions(subClassCode: string) {
      const res = await getSearchOptionList(subClassCode);
      this.matOptions = res.data || [];
    },

    /** 查询网关设备日志列表 */
    async queryGatewayLogList(params: ISearchGatewayReq) {
      this.gatewayQuery = { ...this.gatewayQuery, ...params };
      await this.getGatewayLogList();
    },
    /** 网关设备日志 */
    async getGatewayLogList() {
      const queryParams = omitBy(this.gatewayQuery, value => isNullOrUnDef(value));
      const res = await getGatewayLogList(queryParams);
      debugger;
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.gatewayTableData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.gatewayTableData = [];
        this.tableTotal = 0;
      }
    },

    /**
     * 查询网关日志详情
     */
    async queryGatewayDetailLogList(params: ISearchDetailReq) {
      this.detailQuery = { ...this.detailQuery, ...params };
      await this.getGatewayDetailLogList();
    },

    /** 获取网关设备日志的详情 */
    async getGatewayDetailLogList() {
      const queryParams = omitBy(this.detailQuery, value => isNullOrUnDef(value));
      const res = await getGatewayDetailLogList(queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.detailTableData = res.data.list;
        this.detailTableTotal = res.data.total;
      } else {
        this.detailTableData = [];
        this.detailTableTotal = 0;
      }
    },

    /** 获取单个网关的详情数据 */
    async getDetailOfGatewayLogSingle(id: string) {
      const res = await getGatewayDetailLogById(id);
      if (res.data?.categoryCode) {
        this.currentDetailInfo = res.data;
      }
    },

    /** 获取网关日志信息-文件 */
    async getGatewayDetailLogDownloadUrl(id: string): Promise<IDownLoadDetailLog> {
      return (await getDownLoadUrlOfDetailLog(id)).data;
    }
  }
});
