<template>
  <!-- 生产进度 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物ID"
          prop="subPhysicalItemId"
          :rules="{ required: false, message: '请输入子实物ID', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemId" placeholder="请输入子实物ID" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: true, message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder="请输入子实物编码" clearable />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="所属工序"
          prop="procedureType"
          :rules="{ required: true, message: '请输入所属工序', trigger: 'change' }"
        >
          <el-select v-model="formData.procedureType" class="w-full" placeholder="请选择所属工序" clearable filterable>
            <el-option
              v-for="item in ProcedureTypeEnumOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="主体变/调补变类型"
          prop="mainRegulatingTransformer"
          :rules="{
            required: $route.query.equipmentName == EquipmentNameEnum.Transformer.toString(),
            message: '请选择主体变/调补变类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mainRegulatingTransformer">
            <el-radio v-for="item in MainRegulatEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="进度计划次数"
          prop="progressPlanNum"
          :rules="{ required: true, message: '请输入进度计划次数', trigger: 'change' }"
        >
          <el-input-number
            v-model="formData.progressPlanNum"
            :min="0"
            controls-position="right"
            placeholder="请输入进度计划次数"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="数据类型"
          prop="dataType"
          :rules="{ required: false, message: '请选择数据类型', trigger: 'change' }"
        >
          <el-select v-model="formData.dataType" class="w-full" placeholder="请选择数据类型" clearable filterable>
            <el-option
              v-for="item in DataTypeEnumEjjOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="计划开始时间"
          prop="plannedStartTime"
          :rules="{
            required: true,
            message: '请选择计划开始时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.plannedStartTime"
            placeholder="请选择计划开始时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="计划完成时间"
          prop="plannedEndTime"
          :rules="{
            required: true,
            message: '请选择计划完成时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.plannedEndTime"
            placeholder="请选择计划完成时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="实际开始时间"
          prop="actualStartTime"
          :rules="{
            required: false,
            message: '请选择实际开始时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.actualStartTime"
            placeholder="请选择实际开始时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="实际完成时间"
          prop="actualEndTime"
          :rules="{
            required: false,
            message: '请选择实际完成时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.actualEndTime"
            placeholder="请选择实际完成时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="计划具备发运时间"
          prop="plannedReadyForShipmentTime"
          :rules="{
            required: formData.procedureType == ProcedureTypeEnum.Packaging,
            message: '请选择计划具备发运时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.plannedReadyForShipmentTime"
            placeholder="请选择计划具备发运时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="实际具备发运时间"
          prop="actualReadyForShipmentTime"
          :rules="{
            required: false,
            message: '请选择实际具备发运时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.actualReadyForShipmentTime"
            placeholder="请选择实际具备发运时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="物资供应计划具备发运时间"
          prop="provisioningPlannedReadyForShipmentTime"
          :rules="{
            required: false,
            message: '请选择物资供应计划具备发运时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.provisioningPlannedReadyForShipmentTime"
            placeholder="请选择物资供应计划具备发运时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import {
  ProcedureTypeEnum,
  EquipmentTypeEnumExt,
  DataTypeEnumEjjOptions,
  ProcedureTypeEnumOptions,
  MainRegulatEnumOptions,
  EquipmentNameEnum
} from "@/enums";
import { ProductionProgressModel } from "@/models";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    detail: ProductionProgressModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as ProductionProgressModel;
    },
    isEdit: false
  }
);
const route = useRoute();
const formData = reactive({} as ProductionProgressModel);
/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

watchEffect(() => {
  Object.assign(formData, props.detail);
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as ProductionProgressModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
