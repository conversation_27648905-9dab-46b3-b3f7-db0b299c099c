<template>
  <di class="h-full overflow-hidden luckysheet-container">
    <!-- <div class="flex items-center">
       <el-button type="primary" @click="onGetData()">获取数据</el-button> 
      <el-button type="primary" @click="onSetData({})">设置数据</el-button>
      <el-button type="primary" @click="onPreview()">截屏</el-button>
      <el-button type="primary" @click="onPrint('测试')">打印</el-button>
    </div> -->
    <div class="flex items-center print-preview">
      <Preview id="print-preview" :images="screenshots" />
    </div>
    <template v-if="preview">
      <Teleport to="body">
        <div id="luckysheet" class="w-full h-full fixed top-[9999px] left-[9999px] z-[9999px]" />
      </Teleport>
    </template>
    <div v-else id="luckysheet" class="w-full h-full" />
  </di>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import ExcelEditor from "./excel-editor";
import { IExcelEditorScreenshot } from "./model";
import Preview from "./preview.vue";
// import html2canvas from "html2canvas";
import printJS from "print-js";

defineExpose({
  onPreview,
  onSetData,
  onPrint,
  onSetQrCode
});

// const emits = defineEmits(["printSuccess"]);

const props = withDefaults(
  defineProps<{
    blob?: Blob;
    preview?: boolean;
    showGridLines: 0 | 1;
    nameValue?: { [key: string]: string };
  }>(),
  {
    preview: false,
    showGridLines: 1
  }
);
const screenshots = ref<IExcelEditorScreenshot[]>([]);
const excelEditor = ref();

onMounted(async () => {
  excelEditor.value = new ExcelEditor();
});

watch(
  () => props.blob,
  blob => {
    if (blob) {
      excelEditor.value?.loadExcel(blob, props.showGridLines);
    }
  }
);

watch(
  () => props.nameValue,
  nameValue => {
    if (nameValue && Object.keys(nameValue).length) {
      onSetData(nameValue);
    }
  }
);

function onPreview(nameValue?: { [key: string]: string }): void {
  if (nameValue && Object.keys(nameValue).length) {
    onSetData(nameValue);
  }
  screenshots.value = excelEditor.value.getScreenshots();
}

function onSetData(nameValue: { [key: string]: string }): void {
  excelEditor.value.setDefineNameValues(nameValue);
}

function onSetQrCode(qrCodeOption: IExcelEditorScreenshot): void {
  screenshots.value.push(qrCodeOption);
}

async function onPrint(): Promise<void> {
  // const canvas = await html2canvas(document.getElementById("print-preview"), {
  //   // dpi: 300,
  //   scale: 2,
  //   // background: "#FFFFFF",
  //   useCORS: true
  // });

  //  const pageData = canvas.toDataURL("image/jpeg", 2.0);
  printJS({
    printable: document.getElementById("print-preview"),
    style: `body { margin: 0; padding: 0; border: 0;} img { width: 100%; display: block; } @page{size: A4 portrait; overflow: hidden;margin-bottom: 0mm; margin-top: 0mm;margin-left: 0mm;}`,
    type: "html",
    scanStyles: false,
    documentTitle: " "
  });
}
</script>

<style scoped lang="scss">
/* stylelint-disable */
.luckysheet-container {
  @import "luckysheet/dist/plugins/css/pluginsCss.css";
}
.print-preview {
  border: 1px solid #c0c4cc;
}
</style>
