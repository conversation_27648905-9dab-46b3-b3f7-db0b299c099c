<template>
  <div class="detail-container h-full flex flex-col">
    <div class="raw-material-detail mb-4" v-loading="rawMaterialStore.$state.rawMaterialBaseInfoLoading">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item :name="name" class="relative">
          <template #title>
            <div class="base-title flex-1 flex justify-between absolute left-0 w-full">
              <TitleBar title="基础信息" />
              <div class="operate mr-6">
                <el-button
                  v-auth="PermissionKey.meta.metaRawMaterialEdit"
                  class="mr-4"
                  @click.stop="editRawMaterial"
                  :icon="EditPen"
                  :disabled="!rawMaterialBaseInfo?.code"
                >
                  编辑
                </el-button>
                <span>{{ activeNames.includes(name) ? "收起" : "展开" }}</span>
              </div>
            </div>
          </template>
          <div class="p-4" v-if="rawMaterialBaseInfo.id">
            <el-scrollbar :height="160">
              <el-descriptions>
                <el-descriptions-item label="原材料编号">{{ rawMaterialBaseInfo?.code }}</el-descriptions-item>
                <el-descriptions-item label="原材料名称">
                  {{ rawMaterialBaseInfo?.name }}
                </el-descriptions-item>
                <el-descriptions-item label="原材料类型">{{ rawMaterialBaseInfo?.processName }}</el-descriptions-item>
                <el-descriptions-item label="原材料批次号">
                  {{ rawMaterialBaseInfo?.materialBatchNo }}
                </el-descriptions-item>
                <el-descriptions-item label="品牌">
                  {{ rawMaterialBaseInfo?.borMaterials }}
                </el-descriptions-item>
                <el-descriptions-item label="计量单位">
                  {{ rawMaterialBaseInfo?.partUnit }}
                </el-descriptions-item>
                <el-descriptions-item label="制造商">
                  {{ rawMaterialBaseInfo?.rawmManufacturer }}
                </el-descriptions-item>
                <el-descriptions-item label="产地">
                  {{ rawMaterialBaseInfo?.oorMaterials }}
                </el-descriptions-item>
                <el-descriptions-item label="原材料出厂日期">
                  {{ rawMaterialBaseInfo?.productionDate }}
                </el-descriptions-item>
                <el-descriptions-item label="材料标准" v-if="isArmourClamp">
                  {{ rawMaterialBaseInfo?.materialStandard }}
                </el-descriptions-item>
                <el-descriptions-item label="电压等级">
                  {{ rawMaterialBaseInfo?.voltageGrade }}
                </el-descriptions-item>
                <el-descriptions-item label="规格型号">
                  {{ rawMaterialBaseInfo?.modelCode }}
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions>
                <el-descriptions-item :span="3" label="备注">
                  {{ rawMaterialBaseInfo?.remark }}
                </el-descriptions-item>
              </el-descriptions>
            </el-scrollbar>
          </div>
          <div class="empty text-center" v-else>
            <el-empty :image-size="120">
              <template #image> <EmptyData /> </template>
            </el-empty>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="bg-bg_color raw-material-test flex-1 flex flex-col py-3 overflow-hidden">
      <RawMaterialInspect
        @addRawMaterialInspec="addRawMaterialInspec"
        @editRawMaterialInspec="editRawMaterialInspec($event)"
        @detailRawMaterialInspec="detailRawMaterialInspec($event)"
        @copyRawMaterialInspec="copyRawMaterialInspec($event)"
      />
    </div>

    <!-- 原材料检测详情 -->
    <el-dialog
      v-model="detailRawMaterialInspecRef"
      title="原材料检详情"
      class="middle"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="closeDetailInspecDialog()"
    >
      <DetailInfoInspec />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import TitleBar from "@/components/TitleBar";
import RawMaterialInspect from "../raw-material-test/index.vue";
import DetailInfoInspec from "../detail-info/index.vue";
import { ref, computed } from "vue";
import { EditPen } from "@element-plus/icons-vue";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { IAddRawMaterialInspection } from "@/models/raw-material/i-raw-material-res-v2";
import { PermissionKey } from "@/consts";

const emits = defineEmits<{
  (event: "editRawMaterial"): void;
}>();

const activeNames = ref<string[]>(["1"]);
const name = ref<string>("1");
// 原材料检
const addRawMaterialInspecRef = ref<boolean>(false);
const rawMaterialInspectTitle = ref<string>("新增原材料检");
const copyRawMaterialInspecRef = ref<boolean>(false);
const detailRawMaterialInspecRef = ref<boolean>(false);
const rawMaterialStore = useRawMaterialV2Store();
const rawMaterialBaseInfo = computed(() => rawMaterialStore.rawMaterialBaseInfo);
const isArmourClamp = computed(() => rawMaterialStore.isArmourClamp);

/** 收起折叠 */
const handleChange = (val: string[]) => {
  activeNames.value = val;
};

/**
 * 编辑原材料
 */
const editRawMaterial = () => {
  emits("editRawMaterial");
  const { id } = rawMaterialBaseInfo.value;
  // 获取原材料详情信息
  rawMaterialStore.getDetailBaseInfoOfRawMaterial(id);
};

/**
 * 新增原材料检测信息
 */
const addRawMaterialInspec = async () => {
  rawMaterialInspectTitle.value = "新增原材料检";
  addRawMaterialInspecRef.value = true;
  rawMaterialStore.setAddOrEditInspect(true);
  const { processCode, mrmSpecification } = rawMaterialBaseInfo.value;
  await rawMaterialStore.getRawMaterialCheckInfoByProcessCode(processCode, mrmSpecification);
};
/**
 * 编辑原材料检测
 */
const editRawMaterialInspec = (data: IAddRawMaterialInspection) => {
  rawMaterialInspectTitle.value = "编辑原材料检";
  rawMaterialStore.setAddOrEditInspect(false);
  addRawMaterialInspecRef.value = true;
  rawMaterialStore.getDetailOfRawMaterialInspect(data.id);
};

/**
 * 复制原材料检
 */
const copyRawMaterialInspec = (data: IAddRawMaterialInspection) => {
  copyRawMaterialInspecRef.value = true;
  rawMaterialStore.getDetailOfRawMaterialInspect(data.id);
};

/**
 * 查看详情
 */
const detailRawMaterialInspec = (id: string) => {
  detailRawMaterialInspecRef.value = true;
  rawMaterialStore.getDetailOfRawMaterialInspect(id);
};
/**
 * 关闭详情
 */
const closeDetailInspecDialog = () => {
  detailRawMaterialInspecRef.value = false;
  rawMaterialStore.initRawMaterInspectDetail();
};
</script>
<style scoped lang="scss">
.raw-material-detail {
  :deep(.el-collapse-item__header),
  .base-title {
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.08);
  }
}
</style>
