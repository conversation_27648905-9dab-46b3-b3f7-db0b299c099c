import {
  REPORT_WORK_END_TIME_GREATER_THAN_NOW,
  REPORT_WORK_END_TIME_LESS_THAN_START_DATE,
  REPORT_WORK_START_TIME_GREATER_THAN_END_DATE,
  REPORT_WORK_START_TIME_GREATER_THAN_NOW
} from "@/consts";
import { Validators } from "@/utils/form";
import { FormRules } from "element-plus";
import { computed } from "vue";

export function genFormRules(formValue: any) {
  return computed<FormRules>(() => {
    return {
      workId: [{ required: true, message: "请选择工单", trigger: "change" }],
      processId: [{ required: true, message: "请选择工序", trigger: "change" }],
      productBatchNo: [{ required: true, message: "请填写报工批次号", trigger: "blur" }],
      deviceId: [{ required: true, message: "设备不能为空", trigger: "change" }],
      workStartTime: [
        { message: "报工开始时间不能为空", trigger: "change", required: true },
        {
          message: REPORT_WORK_START_TIME_GREATER_THAN_NOW,
          trigger: "change",
          validator: Validators.maxDate(() => new Date())
        },
        {
          message: REPORT_WORK_START_TIME_GREATER_THAN_END_DATE,
          trigger: "change",
          validator: Validators.maxDateWithoutBoundary(() => formValue.workEndTime)
        }
      ],
      workEndTime: [
        {
          message: REPORT_WORK_END_TIME_GREATER_THAN_NOW,
          trigger: "change",
          validator: Validators.maxDate(() => new Date())
        },
        {
          message: REPORT_WORK_END_TIME_LESS_THAN_START_DATE,
          trigger: "change",
          validator: Validators.minDateWithoutBoundary(() => formValue.workStartTime)
        }
      ],
      buyerProvince: [{ required: true, message: "报工地址不能为空", trigger: "change" }]
    };
  });
}
