import { QMXExperimentResultsEnum, QMXExperimentStatusEnum } from "@/enums/production-experiment";
import { IPagingReq } from "../i-paging-req";

/** 步骤 */
export interface IJfnyProcess {
  timestamp: number | string;
  content: string;
}

export interface ITimeDatas {
  /** 放电量(局放) */
  amountDischarge: number;
  /** 时间戳 */
  timestamp: number | string;
  /** 电压值（耐压值） */
  voltage: number;
}

/** 设备信息 */
export interface IDatas {
  deviceNo: string; // 设备信息
  sensitivity: number; // 灵敏度
  amountDischarge?: number; // 放电量
  withstandVoltage: number; // 耐压时长
  dataType?: number; // 数据类型
  timeDatas: ITimeDatas[]; // 数据集合
}

export interface IJfnyExperimentListReq extends IPagingReq {
  productionId?: string;
}

/** 局放耐压列表 */
export interface IJfnyExperimentList {
  id?: string;
  /** 试验编号 */
  experimentNo: string;
  /** 成品编号 */
  finProNo: string;
  /** 控制室摄像头编号 */
  controlRoomCameraNo: string;
  /** 实验室摄像头编号 */
  experimentRoomCameraNo: string;
  /** 控制室摄像文件 */
  controlRoomFileId: string;
  /** 实验室摄像文件编号 */
  experimentRoomFileId: string;
  /** 试验状态 */
  experimentStatus: QMXExperimentStatusEnum;
  /** 试验过程 */
  progressStatus: string;
  /** 试验结果 */
  experimentResult: QMXExperimentResultsEnum;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 设备编码 */
  deviceNo: string;
  /** 成品电压等级类型 */
  finishedVoltageLevelType: string;
  /** 成品电压等级 */
  finishedVoltageLevel: string;
  /** 数量 */
  account: number;
  /** 计量单位 */
  measUnit: string;
  /** 产品型号 */
  productModel: string;
  /** 步骤时间，步骤描述 */
  process: IJfnyProcess[];
  /** 设备信息 */
  datas: IDatas[];
  /** 线芯名称 */
  wireName?: string;
  reportFileId?: string;
  reportFileName?: string;
  reportFileUrl?: string;
  /** 试验盘号 */
  reelNo: string;
  /** 局放设置值 */
  pdSetting: string;
  /** 局放标定值 */
  pdScaling: string;
  /** 背景干扰值 */
  backGroundInterferenceValue: string;
  /** 局部放电量 */
  partialDischargeAmount: string;
  /** 局部放大倍数 */
  pdMagnification: string;
  /** 已经关联的生产订单号 */
  ipoNos: string;
  /** 产品规格 */
  specificationModel: string;
}

/**
 * 上传报告
 */
export interface IExperimentUploadFile {
  experimentId: string;
  path: string;
  file?: File;
}

export interface IVideoInfo {
  id: string;
  url: string;
  name: string;
  downloadUrl: string;
  bucket: string;
  path: string;
}

/**
 * 试验数据详情
 */
export interface IExperimentJfnyDetail {
  id: number;
  groupNo: string; // 实验组编号
  experimentNo: string; // 试验编号
  productModel: string; // 产品型号
  finishedVoltageLevel: string; // 电压等级
  finishedVoltageLevelType: string; // 电压等级类型
  experimentStatus: QMXExperimentStatusEnum; // 试验状态
  experimentResult: QMXExperimentResultsEnum; // 实验结果
  startTime: string;
  endTime: string;
  account: number; // 数量
  measUnit: string;
  deviceNo: string; // 设备编号
  withstandVoltage: number; // 耐压时长
  /** 控制室编号 */
  controlRoomCameraNo: string;
  /** 控制室视频 */
  controlRoomFileId: IVideoInfo;
  /** 试验室编号 */
  experimentRoomCameraNo: string;
  /** 试验室视频 */
  experimentRoomFileId: IVideoInfo;
  processVOS: IJfnyProcess[];
  /** 线芯名称 */
  wireName: string;
  /** 灵敏度 */
  lmd: string;
  /**  灵敏度（实际） */
  lmdreal: number;
  /** 试验盘号 */
  reelNo: string;
  /** 局放设置值 */
  pdSetting: string;
  /** 局放标定值 */
  pdScaling: string;
  /** 背景干扰值 */
  backGroundInterferenceValue: string;
  /** 背景干扰值（实际） */
  backGroundInterferenceValueReal: number;
  /** 局部放电量 */
  partialDischargeAmount: string;
  /** 局部放电量（实际） */
  partialDischargeAmountReal: number;
  /** 局部放大倍数 */
  pdMagnification: string;
}

/**
 * 试验详情图表数据
 */
export interface IExperimentJfnyEChartDetail extends IDatas {
  id: number;
  values: ITimeDatas[]; // 数据集合
}

/**
 * 待关联的局放耐压试验列表查询
 */
export interface IWaitExperimentJfnyListReq extends IPagingReq {
  productionId?: string;
  keyWords?: string;
}
