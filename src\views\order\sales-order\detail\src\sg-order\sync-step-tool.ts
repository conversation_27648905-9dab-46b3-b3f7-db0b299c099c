import { formatEnum } from "@/utils/format";
import { StateGridOrderSyncStep, StateGridOrderSyncType } from "@/enums";
import { IStateGridOrderSyncDetailList } from "@/models";

export type SyncStepType = "success" | "fail" | "info" | "syncing";

export interface ISyncStep {
  key: StateGridOrderSyncStep;
  title: string;
  type: SyncStepType;
  errorCount: number;
  successCount: number;
  totalCount: number;
}

export function formatSyncSteps(detail: IStateGridOrderSyncDetailList[]): Array<ISyncStep> {
  if (!detail) {
    return [];
  }
  detail = formatDetail(detail);
  return detail.map(item => {
    return {
      key: item.key,
      title: getTitle(item.key),
      type: calcStepIconType(item.totalCnt, item.successCnt, item.errCnt, item.syncingCnt),
      errorCount: item.errCnt,
      successCount: item.successCnt,
      totalCount: item.totalCnt
    };
  });
}

function getTitle(value: StateGridOrderSyncStep) {
  return formatEnum(value, StateGridOrderSyncStep, "StateGridOrderSyncStep");
}

/**
 * @description: 计算步骤图标类型
 */
function calcStepIconType(total: number, successCount: number, failCount: number, syncingCount: number): SyncStepType {
  // 如果有失败的就算作失败
  if (failCount) {
    return "fail";
  }

  // 成功且全部成功
  if (successCount && successCount >= total) {
    return "success";
  }

  // 如果有正在同步的，算作同步中
  if (syncingCount) {
    return "syncing";
  }
  // 总数 = 成功数量，视为同步成功，否则算作待同步
  return "info";
}

function formatDetail(detail: IStateGridOrderSyncDetailList[]) {
  const tempDetail = [...detail];
  const techProcessIndex = detail.findIndex(item => item.key === StateGridOrderSyncStep.TECH_PROCESS_QUALITY);
  const autoCollectLastIndex = detail.findIndex(
    item => item.key === StateGridOrderSyncStep.PROCESS_QUALITY_AUTO_COLLECT
  );
  // 确保 techProcessIndex/autoCollectLastIndex 存在
  if (techProcessIndex !== -1 && autoCollectLastIndex !== -1) {
    tempDetail.splice(techProcessIndex + 1, 0, detail[autoCollectLastIndex]);
    tempDetail.splice(autoCollectLastIndex + 1, 1);
  }
  return tempDetail;
}

/**
 * @description: 是否是订单行数据的同步类型
 */
export function isOrderLineDataType(dataType: StateGridOrderSyncType) {
  if (
    dataType <= StateGridOrderSyncType.FINISHED_PRODUCT ||
    dataType === StateGridOrderSyncType.TECHNICAL_STANDARD ||
    dataType === StateGridOrderSyncType.PROCESS_DOCUMENT ||
    dataType === StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT
  ) {
    return true;
  }
  return false;
}
