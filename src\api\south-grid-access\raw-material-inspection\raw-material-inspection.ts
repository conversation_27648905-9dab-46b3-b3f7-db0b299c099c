import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IRawMaterialInspection,
  IRawMaterialInspectionForm,
  IRawMaterialInspectionReq
} from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryRawMaterialInspection = (data: IRawMaterialInspectionReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/materials/pageList`);
  return http.post<IRawMaterialInspectionReq, IListResponse<IRawMaterialInspection>>(url, {
    data
  });
};

/** 查询 列表  */
export const queryRawMaterialInspectionList = (data: IRawMaterialInspectionReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/materials/list`);
  return http.post<IRawMaterialInspectionReq, IResponse<Array<IRawMaterialInspection>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getRawMaterialInspectionById = (id: string, collectionTypeId: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/materials/${id}/${collectionTypeId}`);
  return http.get<string, IResponse<IRawMaterialInspection>>(url);
};

/** 新增 */
export const createRawMaterialInspection = (data: IRawMaterialInspectionForm) => {
  return http.post<IRawMaterialInspectionForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/materials/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateRawMaterialInspection = (data: IRawMaterialInspectionForm) => {
  return http.put<IRawMaterialInspectionForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/materials/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteRawMaterialInspectionById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/materials/${id}`));
};

/** 从生产订单中选用原材料 */
export const addRawMaterialInspectionFromProductionOrder = (data: IRawMaterialInspectionForm) => {
  return http.post<IRawMaterialInspectionForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/materials/addByProductionOrder"),
    { data }
  );
};
