import { h, inject } from "vue";
import { useConfirm } from "@/utils/useConfirm";
import { eipGuideKey } from "@/views/components/state-grid-trigger-score/tokens";

export function useTriggerTipsHook() {
  const ctx = inject(eipGuideKey);

  function showTips(): Promise<boolean> {
    return useConfirm(getMessage());
  }

  function getMessage() {
    return h("div", { class: "flex flex-col text-base" }, [
      h(
        "span",
        { class: "text-secondary" },
        "同步实际结束日期会触发EIP的质量评分，请先到EIP检查订单下的原材料、过程检、试验数据已完整且全部显示到EIP后再同步实际结束日期"
      ),
      h("span", { class: "text-primary cursor-pointer", onClick: () => (ctx.visible = true) }, "查看EIP检查数据流程")
    ]);
  }

  return {
    showTips
  };
}
