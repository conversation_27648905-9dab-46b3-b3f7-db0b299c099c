<template>
  <div class="h-full overflow-hidden flex flex-col">
    <div class="flex-bc mb-5">
      <SearchRawMaterial @search="searchRawMaterial" />
    </div>
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="rawMaterialOfSelect"
      :columns="tableColumns"
      showOverflowTooltip
      :pagination="pagination"
      :loading="loading"
      @page-size-change="onPageSizeChange($event)"
      @page-current-change="onCurrentPageChange($event)"
      @row-click="handleCurrentChange"
      @selection-change="handleSelectionChange"
    >
      <template #inspectBatchNo="{ row }">
        <div class="cursor-pointer text-primary" @click="detailOfRawMaterialInspect(row, $event)">
          <span>{{ row.inspectBatchNo || "--" }}</span>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
  <!-- 原材料检测详情 -->
  <el-dialog
    v-model="detailRawMaterialInspectRef"
    title="选用原材料检"
    class="middle"
    align-center
    :destroy-on-close="true"
    @close="closeDetail"
  >
    <div class="material-content">
      <DetailRawMaterialInspect :isArmourClamp="store.isArmourClamp" />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import SearchRawMaterial from "./search-raw-material.vue";
import DetailRawMaterialInspect from "@/views/config/raw-material/detail-info/index.vue";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check/raw-material-group-unit-check";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { ref, watchEffect, computed, onMounted } from "vue";
import { IRawMaterialInspectList } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import { useRawMaterialV2Store } from "@/store/modules/base-config/raw-material/raw-material-v2";
import { useFillInDataStore, usePurchaseOrderDetailStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ISearchRawList } from "@/models/raw-material/i-raw-material-res";
import { ProductOrWorkOrderEnum } from "@/enums";

// 获取新增原材时的列表数据
const { columns } = useColumns();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};
const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();
const { processUnionCode } = rawMaterialGroupUnitStore.currentTagInfo || {};
const tableInstance = ref<PureTableInstance>();
const rawMaterialOfSelect = ref<IRawMaterialInspectList[]>([]);
const multipleRawMaterialData = ref<IRawMaterialInspectList[]>([]);
const multiple = ref(true);
const sectionColumns = multiple.value
  ? [
      {
        prop: "checked",
        type: "selection"
      }
    ]
  : [];
const tableColumns = [...sectionColumns, ...columns];
const elTableInstance = computed(() => tableInstance.value.getTableRef());
// 查看详情
const detailRawMaterialInspectRef = ref<boolean>(false);
const rawMaterialConfigStore = useRawMaterialV2Store();
const store = usePurchaseOrderDetailStore();
const loading = ref<boolean>(false);
const initRawMaterialOfSelect = useLoadingFn(getRawMaterialOfSelectAction, loading);

watchEffect(() => {
  rawMaterialOfSelect.value = rawMaterialGroupUnitStore.selectRawMaterialTableData || [];
  pagination.total = rawMaterialGroupUnitStore.selectTotal;
});

onMounted(() => {
  const orderId = useFillInDataStore().dataId;
  const isCable = usePurchaseOrderDetailStore().isCable;
  const orderType = isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
  initRawMaterialOfSelect({ processUnionCode, ...pageInfo, orderId, orderType });
});

/** 获取原材料检的列表 */
async function getRawMaterialOfSelectAction(params: ISearchRawList) {
  await rawMaterialGroupUnitStore.getRawMaterialOfSelectAction(params);
}

// 页码数量改变
const onPageSizeChange = (pageSize: number) => {
  pagination.currentPage = 1;
  initRawMaterialOfSelect({ pageNo: pagination.currentPage, pageSize });
};

// 页码改变
const onCurrentPageChange = (page: number) => {
  initRawMaterialOfSelect({ pageNo: page, pageSize: pagination.pageSize });
};

// 单选原材料数据
const handleCurrentChange = (line: IRawMaterialInspectList) => {
  elTableInstance.value.toggleRowSelection(line, undefined);
};
// 选择原材料数据
const handleSelectionChange = (line: IRawMaterialInspectList[]) => {
  multipleRawMaterialData.value = line;
  rawMaterialGroupUnitStore.checkedRawMaterialData = multipleRawMaterialData.value;
};

/**
 * 点击表格单元格
 */
const detailOfRawMaterialInspect = (row, ev: MouseEvent) => {
  ev.stopPropagation();
  ev.preventDefault();
  if (!row.id) return;
  detailRawMaterialInspectRef.value = true;
  rawMaterialConfigStore.getDetailOfRawMaterialInspect(row.id);
};
/**
 * 关闭详情弹框
 */
const closeDetail = () => {
  detailRawMaterialInspectRef.value = false;
  rawMaterialConfigStore.initRawMaterInspectDetail();
};

/**
 * 搜索
 */
function searchRawMaterial(params: { keyWords?: string; processUnionCode?: string }) {
  initRawMaterialOfSelect({ ...params, ...pageInfo });
}
</script>

<style scoped lang="scss"></style>
