/**
 * @description: 合格证管理
 */

import { http } from "@/utils/http";
import { IListResponse, IPagingReq, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { ICertificateSelectItem } from "../models/i-certificate-manage";
import { ICertificate, ICertificateForm } from "../models";

/**
 * @description: 获取合格证列表
 */
export const getCertificateList = (params: IPagingReq) => {
  const url: string = withApiGateway(`admin-api/ecode/certificate/list`);
  return http.get<IPagingReq, IListResponse<ICertificate>>(url, { params });
};

/**
 * @description: 获取合格证详情
 */
export const getCertificateDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/certificate/detail/${id}`);
  return http.get<string, IResponse<ICertificate>>(url);
};

/**
 * @description: 新增合格证
 */
export const addCertificate = (data: ICertificateForm) => {
  const formData = new FormData();
  Object.keys(data).forEach(key => {
    formData.append(key, data[key]);
  });
  const url: string = withApiGateway(`admin-api/ecode/certificate/create`);
  return http.post<FormData, IResponse<boolean>>(
    url,
    { data: formData },
    { headers: { "Content-Type": "multipart/form-data" } }
  );
};

/**
 * @description: 编辑合格证
 */
export const editCertificate = (data: ICertificateForm) => {
  const formData = new FormData();
  Object.keys(data).forEach(key => {
    formData.append(key, data[key]);
  });
  const url: string = withApiGateway("admin-api/ecode/certificate/update");
  return http.put<FormData, IResponse<boolean>>(
    url,
    { data: formData },
    { headers: { "Content-Type": "multipart/form-data" } }
  );
};

/**
 * @description: 删除合格证
 */
export const deleteCertificate = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/certificate/delete/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/**
 * @description: 获取所有合格证
 */
export const getAllCertificate = () => {
  const url: string = withApiGateway(`admin-api/ecode/certificate/nameAll`);
  return http.get<void, IResponse<Array<ICertificateSelectItem>>>(url);
};
