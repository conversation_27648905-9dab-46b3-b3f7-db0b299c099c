import { CategoryCodeEnum, SyncStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";

export interface IProcessStandarModel extends IBase {
  /**
   * 规格型号
   */
  model?: string;
  /**
   * 设备型号规格名称
   */
  modelName?: string;
  /**
   * 状态
   */
  isSync?: SyncStatusEnum;

  /** 物资分类 */
  categoryCode?: CategoryCodeEnum;

  dataCodeDatas?: Array<IProcessStandarModelItem>;
}

export interface IProcessStandarModelItem {
  /**
   * 物资编码名称
   */
  categoryName?: string;
  /**
   * 采集类别名称
   */
  collectionName?: string;
  /**
   * 采集类别
   */
  collectionType?: string;
  /**
   * 信息类别编码
   */
  infoCode?: string;
  /**
   * 信息类别名称
   */
  infoName?: string;
  /**
   * 数据项编码
   */
  itemCode?: string;
  /**
   * 数据项名称
   */
  itemName?: string;
  /**
   * 工艺参数标准值
   */
  standardValue?: string;
  /**
   * 判定条件
   */
  verdictCondition?: string;
}
