<template>
  <el-button
    type="primary"
    @click="requestExport"
    :loading="loading"
    v-auth="PermissionKey.alarm.alarmProductionDataCheckExport"
    >导出</el-button
  >
</template>

<script setup lang="ts">
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ref } from "vue";
import { PermissionKey } from "@/consts/permission-key";
import { exportDataInterity } from "@/api";
import { IDataIntegrityCheckParams } from "@/models/data-integrity-check/i-data-integrity-check";
import { emitter } from "@/utils/mitt";

/**
 * 导出数据完整性检查报表
 */

const props = defineProps<{
  condition: IDataIntegrityCheckParams;
}>();

const loading = ref(false);

const requestExport = useLoadingFn(async () => {
  const res = await exportDataInterity(props.condition);
  if (res.data) {
    emitter.emit("exportRecordAddTip");
  }
}, loading);
</script>

<style scoped></style>
