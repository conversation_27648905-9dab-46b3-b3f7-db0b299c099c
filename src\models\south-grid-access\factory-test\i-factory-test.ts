import { InspectResultEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";
import { ICollectionItem } from "../material";

export interface IFactoryTest extends IBase {
  /**
   * 试验批次号
   */
  testBatchNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;

  /** 监造计划Id */

  supervisionPlanId?: string;

  /** 试验项目编码 */
  testProjectCode?: string;
  /**
   * 设备唯一编码
   */
  entityId?: string;
  /**
   * 试验日期
   */
  testDate?: string;
  /**
   * 试验结果
   */
  testResult?: InspectResultEnum;
  /**
   * 设备编码
   */
  deviceCode?: string;

  testDataList?: Array<ICollectionItem>;
}
