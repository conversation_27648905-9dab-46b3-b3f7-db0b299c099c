<template>
  <div class="flex items-center" v-if="foregroundOrder">
    <span>{{ foregroundOrder }}</span>
    <div class="flex items-center" v-if="hasEllipsis">
      <span>...</span>
      <span class="text-secondary mr-2">(+{{ ellipsisOrderCount }})</span>
      <el-popover
        placement="bottom-end"
        trigger="hover"
        width="auto"
        :popper-style="{ padding: 0 }"
        v-model:visible="visible"
      >
        <template #reference>
          <el-button :icon="ArrowDown" circle size="small" :class="[visible ? 'active' : '']" />
        </template>
        <div class="w-72 flex-bc flex-wrap gap-2 px-5 py-2 text-regular text-middle">
          <span v-for="order in ellipsisOrders" :key="order" class="order-item">{{ order }}</span>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";

const props = defineProps<{
  orderNoes?: string;
}>();

const MAX_FOREGROUND_NO_COUNT = 2;

const purchaseOrders = computed(() => props.orderNoes?.split(",").filter(no => !!no) || []);
const foregroundOrder = computed(() => purchaseOrders.value.slice(0, MAX_FOREGROUND_NO_COUNT).join(", "));
const ellipsisOrders = computed(() => purchaseOrders.value.slice(MAX_FOREGROUND_NO_COUNT));
const ellipsisOrderCount = computed(() => ellipsisOrders.value.length);
const hasEllipsis = computed(() => ellipsisOrderCount.value > 0);

const visible = ref(false);
</script>

<style lang="scss" scoped>
.order-item {
  width: 48%;
}

.el-button.active {
  color: var(--el-button-hover-text-color);
  border-color: var(--el-button-hover-border-color);
  background-color: var(--el-button-hover-bg-color);
  outline: 0;

  ::v-deep(.el-icon) {
    transform: rotate(180deg);
  }
}

::v-deep(.el-icon) {
  transition: transform 0.3s;
}
</style>
