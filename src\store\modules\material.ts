import { defineStore } from "pinia";
import { IListResponse, IMaterial, IMaterialDto, IMaterialQueryParams, IResponse } from "@/models";
import * as api from "@/api/material";

export const useMaterialStore = defineStore({
  id: "cx-material",
  state: () => ({
    materialDetail: {} as IMaterial,
    materials: [] as Array<IMaterial>,
    total: 0 as number,
    loading: false as boolean
  }),
  actions: {
    async queryMaterialsPaging(params?: IMaterialQueryParams) {
      this.loading = true;
      const res = await api.queryMaterials(params);
      this.materials = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async queryMaterials(params: IMaterialQueryParams): Promise<IListResponse<IMaterial>> {
      if (params.keyWords) {
        params.keyWords = params.keyWords.trim();
      }
      return api.queryMaterials(params);
    },
    async createMaterial(material: IMaterialDto): Promise<IMaterial> {
      return api.createMaterial(material).then(res => res.data);
    },

    async editMaterial(material: IMaterialDto): Promise<boolean> {
      return api.editMaterial(material).then(res => res.data);
    },

    async deleteMaterial(id: string): Promise<boolean> {
      return api.deleteMaterial(id).then(res => res.data);
    },

    async getMaterialDetailById(id: string): Promise<IResponse<IMaterial>> {
      return api.getMaterialDetailById(id);
    },
    async getMaterialDetailByCode(subClassCode: string, materialCode: string): Promise<IMaterial> {
      return api.getMaterialDetailByCode(subClassCode, materialCode).then(res => res.data);
    }
  }
});
