<template>
  <ProductionOrder v-if="purchaseStore.isCable" />
  <WorkOrder v-else />
</template>

<script setup lang="ts">
import ProductionOrder from "@/views/order/purchase-order/detail/src/components/production-order/management/index.vue";
import WorkOrder from "./non-cable/work-order/index.vue";
import { usePurchaseOrderDetailStore } from "@/store/modules";

const purchaseStore = usePurchaseOrderDetailStore();
</script>

<style scoped></style>
