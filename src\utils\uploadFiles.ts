import { isEmpty } from "lodash-unified";
import * as api from "@/api/upload-file";
import { IFileList } from "@/components/Upload/src/types";

/**
 * 预览文件
 * @param fileUrl
 */
export async function previewUploadFile(fileInfo: IFileList) {
  // 获取文件外链
  const shareUrl = (await api.getFileShareUrl(fileInfo?.id)).data;

  window.open(shareUrl, "_blank");
}

/**
 * 文件上传时校验文件类型
 */
export function validUploadFileType(currentFileType: string, ruleFileType: string[]): boolean {
  // 没有类型的文件
  if (!currentFileType) {
    return true;
  }
  const pos = currentFileType?.lastIndexOf("/");
  const fileType = currentFileType?.substring(pos + 1);
  return !isEmpty(ruleFileType) && !ruleFileType.some(item => item.includes(fileType));
}
