import { ColumnWidth, StateGridOrderSyncResult, StateGridOrderSyncType } from "@/enums";
import { ITableOperator, OperatorCell } from "@/components/TableCells";
import { TableColumns } from "@pureadmin/table/dist";
import {
  IEIPSyncProductionProcessAutoItem,
  IIOTSyncCommon,
  IIOTSyncProductionProcessAutoItem,
  ISyncCommon
} from "@/models";
import { PermissionKey, TrackPointKey } from "@/consts";
import { hasAuth } from "@/router/utils";

interface OperatorColumnParams {
  type: StateGridOrderSyncType;
  /** 同步/重新同步 */
  syncFn: (data: ISyncCommon) => any;
  /** 同步明细 */
  syncDetailFn: (data: ISyncCommon | IIOTSyncCommon) => any;
  /** 编辑 */
  editFn?: (data: ISyncCommon) => any;
  /** 取消同步 */
  cancelSyncFn?: (data: IEIPSyncProductionProcessAutoItem | IIOTSyncProductionProcessAutoItem) => any;
  /** 优先同步 */
  prioritySyncFn?: (data: IEIPSyncProductionProcessAutoItem | IIOTSyncProductionProcessAutoItem) => void;
  /** 检查生产数据 */
  checkProductionDataFn?: (data: { dataId: string }) => any;
  operatorColumnWith?: number;
}

export function useOperatorColumn(params: OperatorColumnParams) {
  const {
    type,
    operatorColumnWith,
    syncFn,
    syncDetailFn,
    editFn,
    cancelSyncFn,
    prioritySyncFn,
    checkProductionDataFn
  } = params;
  const hasEditFn: boolean = typeof editFn === "function";
  const hasSyncDetailFn: boolean = typeof syncDetailFn === "function";
  const hasCancelSync: boolean = typeof cancelSyncFn === "function";
  const hasPrioritySync: boolean = typeof prioritySyncFn === "function";
  const hasCheckProductionDataFn: boolean = typeof checkProductionDataFn === "function";

  const operatorColumn: TableColumns = {
    label: "操作",
    width: operatorColumnWith ? operatorColumnWith : ColumnWidth.Char15,
    fixed: "right",
    cellRenderer: data => {
      const { syncResult } = data.row;
      const actions: Array<ITableOperator> = [
        {
          name: _isSyncWaiting(syncResult) ? "同步" : "重新同步",
          action: () => syncFn(data.row),
          props: { type: "primary" },
          permissionKey: PermissionKey.form.formPurchaseSyncBtnSync,
          trackKey: _getSyncTrackPointKey()
        }
      ];

      // 是否显示取消同步
      if (hasCancelSync && syncResult === StateGridOrderSyncResult.TRANSFERRING) {
        actions.push({
          name: "取消同步",
          props: {
            type: "primary"
          },
          action: () => {
            cancelSyncFn(data.row);
          },
          permissionKey: PermissionKey.form.formPurchaseSyncBtnSync
        });
      }

      // 是否显示同步明细
      if (hasSyncDetailFn) {
        actions.push({
          name: "同步明细",
          action: () => _showTimeline(data.row, syncDetailFn),
          props: { type: "primary" },
          trackKey: _getSyncDetailTrackPointKey()
        });
      }

      const hasEditAction: boolean = hasEditFn && _isSyncFail(syncResult);
      if (hasEditAction) {
        const editable = data.row.editable ?? true;
        if (editable) {
          actions.unshift({
            name: "修改数据",
            action: () => editFn(data.row),
            props: { type: "primary" },
            permissionKey: PermissionKey.form.formPurchaseSyncEdit,
            trackKey: _getEditTrackPointKey()
          });
        }
      }

      // 添加检查生产数据按钮
      if (hasCheckProductionDataFn && hasAuth(PermissionKey.form.formPurchaseProcessCheckProdData)) {
        actions.push({
          name: "检查生产数据",
          action: () => checkProductionDataFn(data.row),
          props: { type: "primary" },
          tooltip: "检查并更新自动采集数据",
          permissionKey: PermissionKey.form.formPurchaseProcessCheckProdData
        });
      }

      // 优先同步
      if (hasPrioritySync && syncResult === StateGridOrderSyncResult.TRANSFERRING) {
        actions.push({
          name: "优先同步",
          props: {
            type: "primary"
          },
          action: () => {
            prioritySyncFn(data.row);
          },
          permissionKey: PermissionKey.form.formPurchaseSyncBtnSync,
          class: "!ml-8"
        });
      }

      return OperatorCell(actions);
    }
  };

  function _isSyncFail(syncResult: StateGridOrderSyncResult): boolean {
    return syncResult === StateGridOrderSyncResult.FAULT || syncResult === StateGridOrderSyncResult.GATEWAY_FAULT;
  }

  function _isSyncWaiting(syncResult: StateGridOrderSyncResult): boolean {
    return syncResult === StateGridOrderSyncResult.NO_SYNC;
  }

  function _getSyncTrackPointKey(): string {
    return {
      [StateGridOrderSyncType.SALE_ORDER_ITEM]: TrackPointKey.FORM_PURCHASE_SYNC_SALE_ITEM,
      [StateGridOrderSyncType.PRODUCTION_PLAN]: TrackPointKey.FORM_PURCHASE_SYNC_PLAN_ITEM,
      [StateGridOrderSyncType.PRODUCTION_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PO_ITEM,
      [StateGridOrderSyncType.WORK_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PWO_ITEM,
      [StateGridOrderSyncType.REPORT_WORK]: TrackPointKey.FORM_PURCHASE_SYNC_WO_ITEM,
      [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: TrackPointKey.FORM_PURCHASE_SYNC_RAWMATERIAL_ITEM,
      [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_ITEM,
      [StateGridOrderSyncType.EXPERIMENT]: TrackPointKey.FORM_PURCHASE_SYNC_EXP_ITEM,
      [StateGridOrderSyncType.FINISHED_PRODUCT]: TrackPointKey.FORM_PURCHASE_SYNC_PRO_ITEM
    }[type];
  }

  function _getSyncDetailTrackPointKey(): string {
    return {
      [StateGridOrderSyncType.SALE_ORDER_ITEM]: TrackPointKey.FORM_PURCHASE_SYNC_SALE_DETAIL,
      [StateGridOrderSyncType.PRODUCTION_PLAN]: TrackPointKey.FORM_PURCHASE_SYNC_PLAN_DETAIL,
      [StateGridOrderSyncType.PRODUCTION_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PO_DETAIL,
      [StateGridOrderSyncType.WORK_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PWO_DETAIL,
      [StateGridOrderSyncType.REPORT_WORK]: TrackPointKey.FORM_PURCHASE_SYNC_WO_DETAIL,
      [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: TrackPointKey.FORM_PURCHASE_SYNC_RAWMATERIAL_DETAIL,
      [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_DETAIL,
      [StateGridOrderSyncType.EXPERIMENT]: TrackPointKey.FORM_PURCHASE_SYNC_EXP_DETAIL,
      [StateGridOrderSyncType.FINISHED_PRODUCT]: TrackPointKey.FORM_PURCHASE_SYNC_PRO_DETAIL
    }[type];
  }

  function _getEditTrackPointKey(): string {
    return {
      [StateGridOrderSyncType.SALE_ORDER_ITEM]: TrackPointKey.FORM_PURCHASE_SYNC_SALE_EDIT,
      [StateGridOrderSyncType.PRODUCTION_PLAN]: TrackPointKey.FORM_PURCHASE_SYNC_PLAN_EDIT,
      [StateGridOrderSyncType.PRODUCTION_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PO_EDIT,
      [StateGridOrderSyncType.WORK_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PWO_EDIT,
      [StateGridOrderSyncType.REPORT_WORK]: TrackPointKey.FORM_PURCHASE_SYNC_WO_EDIT,
      [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: TrackPointKey.FORM_PURCHASE_SYNC_RAWMATERIAL_EDIT,
      [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_EDIT,
      [StateGridOrderSyncType.EXPERIMENT]: TrackPointKey.FORM_PURCHASE_SYNC_EXP_EDIT,
      [StateGridOrderSyncType.FINISHED_PRODUCT]: TrackPointKey.FORM_PURCHASE_SYNC_PRO_EDIT
    }[type];
  }

  function _showTimeline(data: ISyncCommon, callBackFn: Function) {
    if (typeof callBackFn === "function") {
      callBackFn(data);
    }
  }

  return {
    operatorColumn
  };
}
