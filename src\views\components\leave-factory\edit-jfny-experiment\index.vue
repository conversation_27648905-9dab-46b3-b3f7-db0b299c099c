<template>
  <div class="list">
    <div
      class="flex-1 flex items-center justify-between"
      v-for="(item, index) in state.ifnyExperimentList"
      :key="index"
    >
      <div class="flex flex-1 items-center gap-6">
        <el-form-item class="w-full" label="生产订单号">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>
          <el-input v-model="item.ipoNo" placeholder="请输入" />
        </el-form-item>
        <el-form-item class="w-full" label="盘号">
          <el-input v-model="item.reelNo" placeholder="请输入盘号" />
        </el-form-item>
      </div>
      <div class="w-6 h-9 cursor-pointer ml-3" @click="onDeletefnyExperimentItem(index)">
        <el-icon><Delete class="text-red-500" /></el-icon>
      </div>
    </div>
    <div class="flex items-center justify-center mt-4">
      <el-button type="primary" @click="onAddfnyExperimentItem()">添加生产订单/盘号</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from "vue";
import { Delete } from "@element-plus/icons-vue";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";
import { IEditJfnyExperimentForm, IJfnyExperimentList } from "@/models/leave-factory";

interface IIfnyExperiment {
  /** 生产订单号 */
  ipoNo: string;
  /** 盘号 */
  reelNo: string;
}

defineExpose({
  getEditExperimentValue
});

const props = withDefaults(
  defineProps<{
    ijfnyExperiment: IJfnyExperimentList;
  }>(),
  {}
);

const state = reactive<{
  dialogVisible: boolean;
  ifnyExperimentList: Array<IIfnyExperiment>;
}>({
  dialogVisible: false,
  ifnyExperimentList: []
});

watch(
  () => props.ijfnyExperiment,
  (ijfnyExperiment: IJfnyExperimentList) => {
    packageIjfnyExperiment(ijfnyExperiment);
  },
  {
    immediate: true
  }
);

const onDeletefnyExperimentItem = (index: number) => {
  state.ifnyExperimentList.splice(index, 1);
};

const onAddfnyExperimentItem = () => {
  state.ifnyExperimentList.push({ ipoNo: null, reelNo: null });
};

function getEditExperimentValue(): IEditJfnyExperimentForm {
  if (state.ifnyExperimentList?.length === 0) {
    return { ipoNos: [], reelNos: [] };
  }

  return state.ifnyExperimentList
    .filter(x => x.ipoNo || x.reelNo)
    .reduce(
      (pre, cur) => {
        cur.ipoNo && pre.ipoNos.push(cur.ipoNo.trim());
        cur.reelNo && pre.reelNos.push(cur.reelNo.trim());
        return pre;
      },
      { ipoNos: [], reelNos: [] }
    );
}

function packageIjfnyExperiment(ijfnyExperiment: IJfnyExperimentList): void {
  const ipoNos = ijfnyExperiment.ipoNos?.split(",") || [];
  const reelNos = ijfnyExperiment.reelNo?.split(",") || [];
  const ipoNoLength = ipoNos.length || 0;
  const reelNoLength = reelNos.length || 0;

  if (ipoNoLength === 0 && reelNoLength === 0) {
    state.ifnyExperimentList.push({ ipoNo: null, reelNo: null });
    return;
  }

  if (ipoNoLength < reelNoLength) {
    ipoNos.push(...Array.from({ length: reelNoLength - ipoNoLength }, () => ""));
  }

  packageIjfnyExperimentByMap(ipoNos, reelNos);
}

function packageIjfnyExperimentByMap(maxLengthValue: Array<string> = [], minLengthValue: Array<string> = []) {
  state.ifnyExperimentList = maxLengthValue.map((ipoNo, index) => {
    return {
      ipoNo,
      reelNo: minLengthValue?.[index]
    };
  });
}
</script>

<style scoped lang="scss"></style>
