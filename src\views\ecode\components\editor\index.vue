<template>
  <div class="editor">
    <Toolbar :editor="editorRef" :defaultConfig="props.toolbarConfig" :mode="props.mode" />
    <Editor
      :style="{ 'overflow-y': 'hidden', height: props.height }"
      v-model="valueCom"
      :defaultConfig="props.editorConfig"
      :mode="props.mode"
      @onChange="contentChange"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script setup lang="ts">
import "@wangeditor/editor/dist/css/style.css"; // 引入 css
import { onBeforeUnmount, shallowRef, computed } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";

const emits = defineEmits<{
  (e: "change", data: { html: string; text: string }): void;
  (e: "update:modelValue", value: string);
}>();

const props = withDefaults(
  defineProps<{
    modelValue?: string;
    mode?: "default" | "simple";
    toolbarConfig?: { [key: string]: any };
    editorConfig?: { [key: string]: any };
    height?: string;
  }>(),
  {
    mode: "default",
    height: "500px"
  }
);
const editorRef = shallowRef();

const valueCom = computed({
  get() {
    return props.modelValue;
  },
  set(value: string) {
    emits("update:modelValue", value);
  }
});

// [
//     "blockquote",
//     "header1",
//     "header2",
//     "header3",
//     "|",
//     "bold",
//     "underline",
//     "italic",
//     "through",
//     "color",
//     "bgColor",
//     "clearStyle",
//     "|",
//     "bulletedList",
//     "numberedList",
//     "todo",
//     "justifyLeft",
//     "justifyRight",
//     "justifyCenter",
//     "|",
//     "insertLink",
//     {
//         "key": "group-image",
//         "title": "图片",
//         "iconSvg": "<svg viewBox=\"0 0 1024 1024\"><path d=\"M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z\"></path></svg>",
//         "menuKeys": [
//             "insertImage",
//             "uploadImage"
//         ]
//     },
//     "insertVideo",
//     "insertTable",
//     "codeBlock",
//     "|",
//     "undo",
//     "redo",
//     "|",
//     "fullScreen"
// ]

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  if (editorRef.value == null) return;
  editorRef.value.destroy();
});

const handleCreated = (editor: any) => {
  editorRef.value = editor;
};

const contentChange = (editor: any) => {
  emits("change", {
    html: editor.getHtml(),
    text: editor.getText()
  });
};
</script>

<style lang="scss">
.editor {
  width: 100%;
  height: 100%;
  border: 1px solid #dcdfe6;
}

// 去除内置样式
// .w-e-text-container blockquote,
// .w-e-text-container li,
// .w-e-text-container p,
// .w-e-text-container td,
// .w-e-text-container th,
// .w-e-toolbar * {
//   line-height: unset !important;
// }
</style>
