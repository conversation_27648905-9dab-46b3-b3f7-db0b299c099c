<template>
  <div class="switch-tabs">
    <template v-for="(tag, index) in switchTags" :key="tag.processId">
      <CxTag class="tag" @click="clickTag(tag, index)" :class="getColor(index)">
        <div class="tag-content">
          <div class="tag-name">
            {{ tag.processName }}
          </div>
          <div class="status-icon" v-if="showTip(tag)">
            <el-tooltip effect="dark" :content="genTagContent(tag)" placement="top">
              <el-icon size="20">
                <WarningFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </div>
      </CxTag>
    </template>
  </div>
</template>

<script setup lang="ts">
import { WarningFilled } from "@element-plus/icons-vue";
import { ITagType, ISwitchTagEventType } from "./types";
import { ref } from "vue";
import CxTag from "@/components/CxTag/index.vue";
import { useThrottleFn } from "@vueuse/core";
import { DOUBLE_CLICK_PERIOD } from "@/consts";

const props = withDefaults(
  defineProps<{
    switchTags: ITagType[];
    hasIcon?: boolean;
    isProcessCheck?: boolean;
  }>(),
  {
    hasIcon: true
  }
);
const emits = defineEmits<{
  (event: "switchChange", data: ISwitchTagEventType, index: number): void;
}>();

const clickTag = useThrottleFn(switchTag, DOUBLE_CLICK_PERIOD, true);
const currenIndex = ref(0);

const showTip = (tag: ITagType) => {
  if (props.isProcessCheck) {
    return tag.needCheckProcess && props.hasIcon && (!tag.hasAutoCollectData || !tag.hasSystemPushData);
  }
  return tag.needCheckProcess && props.hasIcon && !tag.hasData;
};

// 切换不同的Tab
function switchTag(tag: ITagType, index: number) {
  currenIndex.value = index;
  emits(
    "switchChange",
    {
      type: "processInspection",
      data: tag
    },
    index
  );
}

//
const getColor = (index: number) => {
  return currenIndex.value !== index ? "" : "active";
};

const genTagContent = (tag: ITagType) => {
  if (props.isProcessCheck) {
    let str = `缺少${tag.processName}:`;
    if (tag.hasAutoCollectData === false) {
      str += ` 自动采集`;
    }
    if (tag.hasSystemPushData === false) {
      str += ` 过程检`;
    }
    return str;
  }
  return `缺少${tag.processName}检验信息`;
};
</script>

<style scoped lang="scss">
.switch-tabs {
  .tag {
    @apply inline-flex flex-ac py-1 px-2.5 text-base;
    cursor: pointer;
    height: 32px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--el-color-info-light-8);
    border-radius: 3px;
    color: var(--el-text-color-regular);
    box-shadow: 2px 2px 5px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    background-color: #fff;

    &.active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-5);
      background: var(--el-color-primary-light-9);
      box-shadow: 2px 2px 5px 0 var(--el-color-primary-light-9);
    }
  }

  .tag-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .status-icon {
      margin-left: 6px;
      display: flex;

      :deep(.el-icon) {
        color: var(--el-color-warning);
      }

      &.success {
        :deep(.el-icon) {
          color: var(--el-color-success);
        }
      }
    }
  }
}
</style>
