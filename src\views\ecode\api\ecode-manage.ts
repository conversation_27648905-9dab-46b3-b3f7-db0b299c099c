/**
 * @description: ecode管理
 */

import { http } from "@/utils/http";
import { IListResponse, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import {
  AddPrintCertificate,
  AddPrintCode,
  EditPrintCertificate,
  EditPrintCode,
  IEcodeInfoDetail,
  IEcodeInfoListItem,
  IEcodeUsageStatic,
  QueryEcodeInfoListParams
} from "../models/i-ecode-manage";

/**
 * @description: 获取Ecode列表
 */
export const getEcodeInfoList = (data: QueryEcodeInfoListParams) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodeEntity/list`);
  return http.get<QueryEcodeInfoListParams, IListResponse<IEcodeInfoListItem>>(url, { params: data });
};

/**
 * @description: 新增喷码
 */
export const addPrintCode = (data: AddPrintCode) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodePrinter`);
  return http.post<AddPrintCode, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑喷码
 */
export const editPrintCode = (data: EditPrintCode) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodePrinter`);
  return http.put<EditPrintCode, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除Ecode
 */
export const deleteEcode = (ecode: string) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodeEntity/delete`);
  return http.delete<void, IResponse<boolean>>(url, { params: { ecode } });
};

/**
 * @description: 撤回Ecode
 */
export const revokeEcode = (ecode: string) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodeEntity/rollBackEcode`);
  return http.get<{ ecode: string }, IResponse<boolean>>(url, { params: { ecode } });
};

/**
 * @description: 回传Ecode
 */
export const passBackEcode = (ecode: string) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodeEntity/sentBackEcode`);
  return http.get<{ ecode: string }, IResponse<boolean>>(url, { params: { ecode } });
};

/**
 * @description: 新增打印合格证
 */
export const addPrintCertificate = (data: AddPrintCertificate) => {
  const url: string = withApiGateway(`admin-api/ecode/cert/create`);
  return http.post<AddPrintCertificate, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑打印合格证
 */
export const editPrintCertificate = (data: EditPrintCertificate) => {
  const url: string = withApiGateway(`admin-api/ecode/cert`);
  return http.put<EditPrintCertificate, IResponse<boolean>>(url, { data });
};

/**
 * @description: 获取Ecode详情
 */
export const getEcodeInfoDetail = (ecode: string) => {
  const url: string = withApiGateway(`admin-api/ecode/ecodeEntity/detail`);
  return http.get<{ ecode: string }, IResponse<IEcodeInfoDetail>>(url, { params: { ecode } });
};

/**
 * @description: 获取未使用的Ecode
 */
export const getOneUnusedEcode = () => {
  const url = withApiGateway(`admin-api/ecode/ecodeEntity/getOne`);
  return http.get<void, IResponse<IEcodeInfoListItem>>(url);
};

/**
 * @description: 获取Ecode使用统计
 */
export const getEcodeUsageStatic = (query: { keyword: string }) => {
  const url = withApiGateway(`admin-api/ecode/ecodeEntity/statusNum`);
  return http.get<void, IResponse<IEcodeUsageStatic>>(url, { params: query });
};
