<template>
  <div class="absolute top-0 left-1/2 h-full translate-x-[-50%]">
    <div class="line" />
    <el-button
      class="relative translate-y-[-50%] z-10 w-6 !cursor-[all-scroll]"
      size="small"
      type="primary"
      :icon="icon"
      @click="toggleFullScreen"
      v-track="TrackPointKey.FORM_PURCHASE_SALES_FULL_SCREEN_ICON"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h } from "vue";
import { FontIcon } from "@/components/ReIcon";
import { TrackPointKey } from "@/consts";

const props = defineProps(["modelValue"]);
const emit = defineEmits(["update:modelValue"]);

const isFullScreen = computed(() => props.modelValue);
const fullScreenIcon = defineComponent({
  render: () => h(FontIcon, { icon: "icon-full-screen" })
});
const exitFullScreenIcon = defineComponent({
  render: () => h(FontIcon, { icon: "icon-exit-full-screen" })
});
const icon = computed(() => (isFullScreen.value ? exitFullScreenIcon : fullScreenIcon));

function toggleFullScreen() {
  emit("update:modelValue", !props.modelValue);
}
</script>

<style scoped>
.line {
  @apply absolute top-px left-1/2 h-full;
  border-left: 1px var(--el-border-color) var(--el-border-style);
}
</style>
