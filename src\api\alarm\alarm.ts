import {
  IListResponse,
  IAlarmDataParam,
  IResponse,
  IExperimentData,
  IAlarmRuleSetting,
  IAlarmData,
  IAlarmSolveReq,
  IAlarmDataDetail
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 查询告警列表数据 */
export const queryAlarm = (data: IAlarmDataParam, aim: string) => {
  const url: string = withApiGateway(`admin-api/system/${aim}/page`);
  return http.post<IAlarmDataParam, IListResponse<IAlarmData>>(url, { data });
};

/** 根据ID解决告警 */
export const solveAlarmById = (data: IAlarmSolveReq, aim: string) => {
  const url: string = withApiGateway(`admin-api/system/${aim}/solve`);
  return http.put<IAlarmSolveReq, IResponse<boolean>>(url, { data });
};

/** 根据ID查询告警数据详情 */
export const getAlarmDataById = (id: string, aim: string) => {
  const url: string = withApiGateway(`admin-api/system/${aim}/detail/${id}`);
  return http.get<void, IResponse<IAlarmDataDetail>>(url);
};

/** 根据ID获取实验数据 */
export const getDetailExperimentChartsById = (id: string) => {
  return http.post<string, IResponse<Array<IExperimentData>>>(`/api/get-experiment-data/${id}`);
};

/** 获取告警规则 */
export const getAlarmRules = () => {
  const url = withApiGateway("admin-api/system/alarm/rule");
  return http.get<void, IResponse<IAlarmRuleSetting>>(url);
};

/** 保存修改告警规则 */
export const saveAlarmRules = (data: IAlarmRuleSetting) => {
  const url = withApiGateway("admin-api/system/alarm/rule/save");
  return http.post<IAlarmRuleSetting, IResponse<boolean>>(url, {
    data
  });
};
