<template>
  <!-- 库存管理 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col v-for="viewItem in fieldViewAll" :key="viewItem.prop" :span="12">
        <el-form-item
          :label="viewItem.label"
          :prop="viewItem.prop"
          :rules="{
            required: viewItem.isRequire,
            message: viewItem.formType == 'text' ? `请输入${viewItem.label}` : `请选择${viewItem.label}`,
            trigger: 'change'
          }"
        >
          <!-- 文本输入 -->
          <template v-if="viewItem.formType == 'text'">
            <el-input
              v-model="formData[viewItem.prop as string]"
              :disabled="!viewItem.canEdit && isEdit"
              :placeholder="`请输入${viewItem.label}`"
              clearable
            />
          </template>
          <!-- 数字输入框 -->
          <template v-if="viewItem.formType == 'number'">
            <el-input-number
              class="w-full"
              v-model="formData[viewItem.prop as string]"
              :min="0"
              :placeholder="`请输入${viewItem.label}`"
              controls-position="right"
            />
          </template>
          <!-- 日期框 -->
          <template v-if="viewItem.formType == 'date' || viewItem.formType === 'datetime'">
            <el-date-picker
              v-model="formData[viewItem.prop as string]"
              :placeholder="`请选择${viewItem.label}`"
              class="flex-1"
              clearable
              type="datetime"
            />
          </template>
          <!-- 选择框 -->
          <template v-if="viewItem.formType == 'select'">
            <el-select
              v-model="formData[viewItem.prop as string]"
              class="w-full"
              :placeholder="`请选择${viewItem.label}`"
              :disabled="!viewItem.canEdit"
              clearable
              filterable
            >
              <el-option
                v-for="item in viewItem.options"
                :key="item.value"
                :label="item.label"
                :value="Number(item.value)"
              />
            </el-select>
          </template>
          <!-- 单选框 -->
          <template v-if="viewItem.formType == 'radio'">
            <el-radio-group v-model="formData[viewItem.prop as string]">
              <el-radio v-for="item in viewItem.options" :key="item.value" :label="Number(item.value)">{{
                item.label
              }}</el-radio>
            </el-radio-group>
          </template>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import { useRoute } from "vue-router";
import { InventoryInfoModel } from "@/models";
import { useColumns } from "./column-config";
import { EquipmentTypeEnumExt } from "@/enums";

const props = withDefaults(
  defineProps<{
    detail: InventoryInfoModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as InventoryInfoModel;
    },
    isEdit: false
  }
);
const route = useRoute();
const formData = reactive({} as InventoryInfoModel);

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();
const fieldViewAll = useColumns(isCombiner).filter(item => {
  return item.isFormShow;
});
const editMode = ref(false);

watchEffect(() => {
  Object.assign(formData, props.detail);
  if (!props.isEdit) {
    formData.equipmentType = route.query.type as string;
  }
  editMode.value = props.isEdit;
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as InventoryInfoModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
