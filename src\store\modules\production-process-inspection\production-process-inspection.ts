import { defineStore } from "pinia";
import {
  autoCollectItemsByProcessId,
  delProductionProcessList,
  getAutoCollect<PERSON><PERSON>s,
  getDetialProductionProcessList,
  getHistoryAutoCollectCharts,
  getNonCableProdProcessInspecProcess,
  getProdProcessInspecProcess,
  getProductionProcessList,
  getReportWorkBatchNo,
  productionProcessInspectCopy,
  saveProductionProcess
} from "@/api/production-test-sensing/production-process-inspection";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import {
  IAddProductionProcess,
  IAutoCollectChartReq,
  IProductionProcessList,
  ISearchProductionProcessList
} from "@/models/production-test-sensing/i-production-process-inspection";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts/date-format";
import { getAddRawMaterialCheckInfoByProcessId } from "@/api/production-test-sensing/raw-material-group-unit-check";
import { ICopyFromOrder, IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";
import { IResponse } from "@/models/response";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";
import { ProductOrWorkOrderEnum } from "@/enums";
import { IReportWorkBatchNoReq } from "@/models";
import { handleAutoCollectCharts } from "@/utils/auto-collect-points";

export const useProductionProcessInspecStore = defineStore({
  id: "production-process-inspection",
  state: () => ({
    switchTabList: [] as Array<IProductionMaterialProcess>,
    currentTagInfo: {} as IProductionMaterialProcess,
    queryParams: {} as ISearchProductionProcessList,
    loadingInspect: true,
    processInspectionTableData: [] as Array<IProductionProcessList>,
    processInspectionTableTotal: 0,
    processCheckCollectionItem: [] as Array<IRawMaterialCheckCollectionItem>,
    saveFormData: {} as IAddProductionProcess,
    mode: "add" as "add" | "edit" | "copy",
    detailProductionCheckInfo: {} as IProductionProcessList
  }),
  getters: {
    /** 根据工序判断是否可以 新增/编辑 */
    getIsCanOperateAddAndEdit() {
      return !this.currentTagInfo?.autoCollect;
    }
  },
  actions: {
    /**
     * 获取生产工艺过程检测的过程
     */
    async getProdProcessInspecProcessAction(parmaData?: ISearchProductionProcessList, isOnlySale = false) {
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      this.switchTabList = isCable
        ? await getProdProcessInspecProcess(dataId).then(res => res.data)
        : await getNonCableProdProcessInspecProcess(dataId).then(res => res.data);
      this.getProductionProcessList(parmaData, isOnlySale);
    },

    /**
     * 获取过程对应的过程检测列表
     */
    getProductionProcessList(parmaData: ISearchProductionProcessList, isOnlySale = false) {
      this.queryParams = {
        ...this.queryParams,
        ...parmaData
      };
      if (!this.queryParams.processCode) {
        this.queryParams.processCode = this.switchTabList[0]?.processCode;
      }
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      if (isCable) {
        this.queryParams.productionId = dataId;
      } else {
        this.queryParams.workOrderId = dataId;
      }
      this.loadingInspect = true;
      getProductionProcessList(this.queryParams)
        .then(res => {
          if (Array.isArray(res.data?.list) && res.data?.list.length) {
            const resList = res.data.list.map(item => {
              item.checkTime = formatDate(item.checkTime, fullDateFormat);
              return item;
            });
            this.processInspectionTableData = [...this.processInspectionTableData, ...resList].map(item => {
              item.processDetailDataValue = this.handleNotCollectInspectItems(item.processDetailDataValue);
              return item;
            });
            this.processInspectionTableTotal = res.data.total;
          } else {
            this.processInspectionTableData = [];
            this.processInspectionTableTotal = 0;
          }
        })
        .finally(() => {
          this.loadingInspect = false;
        });
    },

    /**
     * 新增时获取工艺的检测项目
     */
    async getProductionProcessCheckInfo(processId: string, specificationModel: string) {
      if (this.mode === "add") {
        this.processCheckCollectionItem = [];
        const checkInfoRes = await getAddRawMaterialCheckInfoByProcessId(processId, specificationModel);
        if (checkInfoRes?.data?.length) {
          this.processCheckCollectionItem = this.handleNotCollectInspectItems(checkInfoRes.data);
        }
      }
    },

    /**
     * 保存生产过程工艺检测
     */
    async saveProductionProcessCheck(saveData: IAddProductionProcess) {
      return await saveProductionProcess(saveData);
    },

    /**
     * 删除生产过程工艺检测
     */
    async delProductionProcessCheck(id: string): Promise<IResponse<number>> {
      return await delProductionProcessList(id);
    },

    /**
     * 获取生产过程工艺详情信息
     */
    async getDetailOfProdProcessInfo(id: string) {
      const detailRes = await getDetialProductionProcessList(id);
      if (detailRes.data?.id) {
        const { deviceCode, deviceName, deviceId } = detailRes.data;
        this.detailProductionCheckInfo = detailRes.data;
        this.detailProductionCheckInfo.devicedCode = deviceCode;
        this.detailProductionCheckInfo.devicedName = deviceName;
        this.detailProductionCheckInfo.devicedId = deviceId;
        this.processCheckCollectionItem = this.handleNotCollectInspectItems(detailRes.data.processDetailDataValue);
      } else {
        this.detailProductionCheckInfo = {};
        this.processCheckCollectionItem = [];
      }
    },

    /** 更新某一条数据 */
    async updateDetailInfoById(detailInfo: IProductionProcessList) {
      const { deviceCode: devicedCode, deviceName: devicedName, deviceId: devicedId, checkTime } = detailInfo;
      this.processInspectionTableData.forEach(item => {
        if (item.id === detailInfo.id) {
          detailInfo.checkTime = formatDate(checkTime, fullDateFormat);
          detailInfo.processDetailDataValue = this.handleNotCollectInspectItems(detailInfo.processDetailDataValue);
          Object.assign(item, { ...detailInfo, devicedCode, devicedName, devicedId });
        }
      });
    },

    /** 从订单中复制原材料检 */
    async copyProcessInspectFromOrder(paramData: ICopyFromOrder, isOnlySale = false) {
      const isCable = isOnlySale ? useSalesOrderDetailStore().isCable : usePurchaseOrderDetailStore().isCable;
      const orderType = isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
      return await productionProcessInspectCopy({ ...paramData, orderType });
    },

    /** 获取自动采集项数据 */
    async getCollectionItemsFromProcess(processId: string) {
      return (await autoCollectItemsByProcessId(processId)).data;
    },

    /** 获取报工批次号 */
    async getReportWorkBatchNo(params: IReportWorkBatchNoReq) {
      return (await getReportWorkBatchNo(params)).data;
    },

    /** 获取自动采集项数据 -- 实时 */
    async getAutoCollectCharts(params: IAutoCollectChartReq) {
      const res = await getAutoCollectCharts(params);
      return handleAutoCollectCharts(res.data?.items, res.data.points);
    },

    /** 获取自动采集项数据 -- 历史 */
    async getHistoryAutoCollectCharts(params: IAutoCollectChartReq, signal?: AbortSignal) {
      const res = await getHistoryAutoCollectCharts(params, signal);
      return handleAutoCollectCharts(res.data?.items, res.data.points);
    },

    /** 过滤非自动采集的检测项 */
    handleNotCollectInspectItems(data: IRawMaterialCheckCollectionItem[]) {
      return (data || []).filter(item => !item.collectionType);
    },

    /** 重置过程检测详情数据 */
    initDetailOfProcessInfo() {
      this.detailProductionCheckInfo = {};
      this.processCheckCollectionItem = [];
    },
    /** 重置搜索参数及loading */
    initQueryAndLoading() {
      this.queryParams = {};
      this.loadingInspect = true;
      this.processInspectionTableData = [];
    },
    initCurrentTagInfo() {
      this.currentTagInfo = {};
    }
  }
});
