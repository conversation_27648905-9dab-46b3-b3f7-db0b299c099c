import { NotificationTypeEnum, RequireReplyEnum, ResponseStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";

export interface IEventNotification extends IBase {
  /**
   * 编码
   */
  notificationId?: string;
  /**
   * 通知编码
   */
  notificationCode?: NotificationTypeEnum;
  /**
   * 通知描述
   */
  notificationDesc?: string;
  /**
   * 通知发生时间
   */
  notificationTime?: string;
  /**
   * 是否需要回复
   */
  requireReply?: RequireReplyEnum;
  /**
   * 监造计划编号
   */
  supervisionNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 设备唯一编码
   */
  entityId?: string;
  /**
   * 应答描述
   */
  reply?: string;

  status?: ResponseStatusEnum;
}
