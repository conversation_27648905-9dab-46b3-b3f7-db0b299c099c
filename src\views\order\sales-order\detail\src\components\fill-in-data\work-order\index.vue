<template>
  <div class="mb-4">
    <SearchBar @searchForm="searchForm" />
  </div>

  <PureTable
    row-key="id"
    :data="workOrderStore.workOrders"
    :columns="columns"
    showOverflowTooltip
    :pagination="pagination"
    @page-current-change="onPageCurrentPage"
    @page-size-change="onPageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </PureTable>

  <el-dialog
    v-model="workOrderStore.workOrderFormModalVis"
    :title="workOrderStore.isCreateWorkOrderForm ? '新增工单' : '编辑工单'"
    align-center
    class="default"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <WorkOrderForm
      ref="workOrderFormRef"
      :type="workOrderStore.isCreateWorkOrderForm ? 'create' : 'update'"
      :parent-no="ipoNo"
      :sub-class-code="subclassCode"
    />
    <template #footer>
      <el-button @click="cancelWorkOrderDialog">取消</el-button>
      <el-button type="primary" :loading="workOrderSaveLoading" @click="handleSaveWorkOrder">保存</el-button>
    </template>
  </el-dialog>

  <el-dialog
    destroy-on-close
    title="工单详情"
    class="default"
    align-center
    v-model="workOrderStore.workOrderDetailModalVis"
  >
    <WorkOrderDetail />
  </el-dialog>

  <el-dialog
    destroy-on-close
    title="报工列表"
    class="middle"
    align-center
    v-model="reportWorkStore.reportWorkTableModalVis"
    width="70%"
  >
    <ReportWorkTable />
  </el-dialog>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useSalesFillInDataStore, useReportWorkStore, useWorkOrderStore } from "@/store/modules";
import { useColumns } from "./columns";
import WorkOrderForm from "@/views/order/sales-order/detail/src/components/fill-in-data/work-order/work-order-form/index.vue";
import WorkOrderDetail from "@/views/order/sales-order/detail/src/components/fill-in-data/work-order-detail/index.vue";
import { computed, onMounted, ref, watch, watchEffect } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ICreateWorkOrder, IWorkOrderReq } from "@/models";
import { ElMessage } from "element-plus";
import ReportWorkTable from "@/views/order/sales-order/detail/src/components/fill-in-data/report-work/index.vue";
import { useTableConfig } from "@/utils/useTableConfig";
import SearchBar from "./search-bar.vue";

const workOrderStore = useWorkOrderStore();
const reportWorkStore = useReportWorkStore();
const fillInDataStore = useSalesFillInDataStore();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
let searchFormValue: IWorkOrderReq;

const workOrderFormRef = ref<InstanceType<typeof WorkOrderForm>>();
const workOrderSaveLoading = ref(false);
const handleSaveWorkOrder = useLoadingFn(saveWorkOrder, workOrderSaveLoading);

const { columns } = useColumns();

const ipoNo = computed(() => fillInDataStore.data?.ipoNo);
const subclassCode = computed(() => fillInDataStore.data?.subClassCode);

onMounted(() => {
  watch(
    () => fillInDataStore.dataId,
    productionId => {
      workOrderStore.setWorkOrderParams({
        productionId,
        pageNo: pagination.currentPage,
        pageSize: pagination.pageSize
      });
      workOrderStore.queryWorkOrderPaging();
    },
    {
      immediate: true
    }
  );
  watchEffect(() => {
    pagination.total = workOrderStore.total;
  });

  watchEffect(() => {
    if (workOrderStore.refreshWorkOrdersTag) {
      pagination.currentPage = 1;
    }
  });
});

function cancelWorkOrderDialog() {
  workOrderStore.setWorkOrderFormModalVis(false);
}

/** 保存工单 */
async function saveWorkOrder() {
  const formValue: ICreateWorkOrder | boolean = await workOrderFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  workOrderStore.isCreateWorkOrderForm
    ? await workOrderStore.createWorkOrder(formValue)
    : await workOrderStore.editWorkOrder(formValue);
  ElMessage.success(workOrderStore.isCreateWorkOrderForm ? "新增成功" : "编辑成功");

  if (workOrderStore.isCreateWorkOrderForm) {
    const workOrderParams: IWorkOrderReq = workOrderStore.workOrderParams;
    pagination.currentPage = 1;
    workOrderParams.pageNo = pagination.currentPage;
    workOrderStore.setWorkOrderParams(workOrderParams);
  }
  workOrderStore.setWorkOrderFormModalVis(false);
  void workOrderStore.queryWorkOrderPaging();
}

function onPageCurrentPage() {
  queryWorkOrderPaging();
}

function onPageSizeChange() {
  pagination.currentPage = 1;
  queryWorkOrderPaging();
}

function queryWorkOrderPaging() {
  let workOrderParams: IWorkOrderReq = workOrderStore.workOrderParams;
  workOrderParams.pageNo = pagination.currentPage;
  workOrderParams.pageSize = pagination.pageSize;

  if (searchFormValue) {
    workOrderParams = { ...workOrderParams, ...searchFormValue };
  }

  workOrderStore.setWorkOrderParams(workOrderParams);
  workOrderStore.queryWorkOrderPaging();
}

function searchForm(formValue: IWorkOrderReq) {
  searchFormValue = formValue;
  pagination.currentPage = 1;
  queryWorkOrderPaging();
}
</script>

<style scoped></style>
