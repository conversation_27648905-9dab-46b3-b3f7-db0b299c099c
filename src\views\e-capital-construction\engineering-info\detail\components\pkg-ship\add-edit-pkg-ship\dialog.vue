<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="drawerVisible"
      :title="isAddMode ? '新增' : '编辑'"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <PkgShipForm ref="formRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSaveBtn(false)" :loading="loading">保存</el-button>
          <el-button v-if="isAddMode" :loading="loading" type="primary" @click="handleSaveBtn(true)"
            >保存并新增</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import PkgShipForm from "./form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useRoute } from "vue-router";
import { getPkgShipById, createPkgShip, updatePkgShip } from "@/api/e-capital-construction/engineering-info";

const loading = ref(false);
const props = defineProps<{
  mode: "edit" | "add";
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);
const route = useRoute();

const formRef = ref<InstanceType<typeof PkgShipForm>>();
const drawerVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");
const handleSaveBtn = useLoadingFn((isContinue: boolean) => {
  return onSave(isContinue);
}, loading);
// 订阅弹窗开启状态，请求数据
watch(drawerVisible, async visible => {
  if (!visible || !isEditMode.value) {
    return;
  }
  const { data } = await getPkgShipById(props.id);
  formRef.value.initFormValue(data);
});

/**
 *  保存按钮点击事件
 */
async function onSave(isContinue: boolean) {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }
  const formVal = formRef.value.getFormValue();
  const params = {
    ...formVal,
    equipmentType: route.query.type as string,
    equipmentId: route.query.id as string
  };
  const { data } = isAddMode.value ? await createPkgShip(params) : await updatePkgShip(params);
  if (data) {
    emits("postSaveSuccess");
    ElMessage({ message: isAddMode.value ? "新增成功" : "编辑成功", type: "success" });
    if (isContinue) {
      formRef.value.resetFormValue();
    } else {
      closeDialog();
    }
  }
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  drawerVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  drawerVisible.value = false;
}
</script>

<style scoped></style>
