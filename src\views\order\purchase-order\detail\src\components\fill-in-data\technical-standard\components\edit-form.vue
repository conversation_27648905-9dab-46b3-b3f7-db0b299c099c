<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col>
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input placeholder="请输入" v-model="form.specificationModel" />
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="物料编号" prop="materialCode">
          <el-input v-model="form.materialCode" />
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="标准名称" prop="standardName">
          <el-input v-model.trim="form.standardName" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ITechnicalStandardInfo } from "@/models/technical-standard";
import { requiredMessage } from "@/utils/form";
import { FormInstance, FormRules } from "element-plus";
import { reactive, ref } from "vue";

const formRef = ref<FormInstance>();
const form = reactive({
  specificationModel: undefined,
  materialCode: undefined,
  standardName: undefined
});
const rules = reactive<FormRules>({
  standardName: [
    {
      required: true,
      message: requiredMessage("标准名称"),
      trigger: "change"
    },
    { max: 200, message: "标准名称超度不能超过200位", trigger: "change" }
  ]
});

async function validate() {
  if (!formRef.value) return;
  return await formRef.value?.validate();
}

/** 获取技术标准数据 */
async function getFormValue() {
  if (!(await validate())) {
    return;
  }
  return form;
}

/** 更新表单数据 */
function patchFormValue(values: ITechnicalStandardInfo) {
  Object.assign(form, { ...values });
}

defineExpose({
  getFormValue,
  patchFormValue
});
</script>

<style scoped lang="scss"></style>
