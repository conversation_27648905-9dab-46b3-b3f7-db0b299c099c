import { ColumnWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "生产单据编码",
      prop: "productionOrderNo",
      width: ColumnWidth.Char12
    },
    {
      label: "排产计划编码",
      prop: "productionPlanningNo",
      width: ColumnWidth.Char12
    },
    {
      label: "排产阶段",
      prop: "phaseCode",
      slot: "phaseCode"
    },
    {
      label: "车间编码",
      prop: "workshopCode"
    },
    {
      label: "设备编码",
      prop: "deviceCode"
    },
    {
      label: "数量",
      prop: "productionMaterialNumber"
    },
    {
      label: "单位",
      prop: "productionMaterialUnit"
    },
    {
      label: "计划开始日期",
      prop: "planStartDate"
    },
    {
      label: "计划完成日期",
      prop: "planEndDate"
    },
    {
      label: "同步状态",
      prop: "isSync",
      slot: "isSync"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char8
    }
  ];
  return { columns };
}
