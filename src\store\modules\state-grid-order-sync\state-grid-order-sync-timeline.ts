import { defineStore } from "pinia";
import { II<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IStateGridOrderJFNYSyncHistory, IStateGridOrderSyncHistory, ISyncCommon } from "@/models";
import * as api from "@/api/state-grid-order-sync";
import { downloadByUrl } from "@pureadmin/utils";
import { StateGridOrderSyncType } from "@/enums/state-grid-order/state-grid-order-sync-type.enum";

type StateGridOrderSyncTimelineType = {
  visible: boolean;
  syncData: ISyncCommon;
  histories: Array<IStateGridOrderSyncHistory & IStateGridOrderJFNYSyncHistory>;
};

export const useStateGridOrderSyncTimelineStore = defineStore("cx-state-grid-order-sync-timeline", {
  state: (): StateGridOrderSyncTimelineType => ({
    visible: false,
    syncData: undefined,
    histories: []
  }),
  actions: {
    async getSyncHistories(syncProcessStep: StateGridOrderSyncType, data: ISyncCommon) {
      const { purchaseLineId, dataId } = data;
      this.histories = await api.getSyncHistories(syncProcessStep, purchaseLineId, dataId).then(res => res.data);
      this.setData(data);
    },

    async getJFNYSyncHistories(data: ISyncCommon) {
      const { purchaseLineId, dataId } = data;
      this.histories = await api.getJFNYSyncHistoriesForEip(purchaseLineId, dataId).then(res => res.data);
      this.setData(data);
    },

    async getSyncIotOrCsgGuangzhouHistories(data: IIOTSyncCommon, channel: number) {
      const { dataId } = data;
      this.histories = await api.getSyncIotOrCsgGuangzhouHistories(dataId, channel).then(res => res.data);
      this.setData(data);
    },
    /** 赋值 */
    setData(data: ISyncCommon | IIOTSyncCommon) {
      this.syncData = data;
      this.visible = true;
    },
    /** 报文信息 */
    getMessageByTaskId(taskId: string) {
      return api.getMessageByTaskId(taskId).then(res => res.data);
    },
    /** 报文信息 */
    getMessageIOTByTaskId(taskId: string) {
      return api.getMessageIOTByTaskId(taskId).then(res => res.data);
    },
    /** 下载报文 */
    async downloadMessage(messageId: string) {
      const { fileUrl, fileName } = (await api.getMessageDownloadInfo(messageId)).data;
      downloadByUrl(fileUrl, fileName);
    },
    /** 下载IOT报文 */
    async downloadIOTMessage(messageId: string) {
      const { fileUrl, fileName } = (await api.getMessageIOTDownloadInfo(messageId)).data;
      downloadByUrl(fileUrl, fileName);
    }
  }
});
