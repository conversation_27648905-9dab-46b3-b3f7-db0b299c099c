import { IWaveFormItem } from "@/models";

/**
 * @description: 生成波形图映射
 */
function generateWaveFormDict(waveFormConfig: Array<IWaveFormItem>) {
  const dict = waveFormConfig.reduce((prev, cur) => {
    if (!prev[cur.name]) {
      prev[cur.name] = cur;
    }
    return prev;
  }, {} as Record<string, IWaveFormItem>);
  return dict;
}

function generateFakeData(dict: Record<string, IWaveFormItem>) {
  const tempList = Object.keys(dict).reduce((prev, key) => {
    if (!dict[key].valueType) {
      prev.push({
        xAxisData: [],
        yAxisData: [],
        name: dict[key].display
      });
    }
    return prev;
  }, []);
  return {
    title: "波形数据",
    list: tempList
  };
}

/**
 * @description: 生成波形列表
 */
export function genWaveList(data: string, waveFormConfig: Array<IWaveFormItem>) {
  const dict = generateWaveFormDict(waveFormConfig);
  if (!data) {
    return generateFakeData(dict);
  }
  let rawData = JSON.parse(data);
  if (Array.isArray(rawData)) {
    rawData = rawData[0];
  }
  const { lineItems, time, tolerantTerminal: title } = rawData;
  const list = lineItems.map(({ datum, name }) => {
    return {
      xAxisData: time,
      yAxisData: datum,
      name: dict[name].display
    };
  });

  return {
    list,
    title
  };
}
