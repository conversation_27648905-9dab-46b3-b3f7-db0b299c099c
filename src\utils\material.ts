import { CABLE_TERMINAL_ADAPTER } from "@/consts";
import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";

/** 物资工具 */
export function useMaterial() {
  /** 根据品类判断是否为线缆 */
  function isCable(categoryCode: string) {
    return categoryCode === MaterialCategoryCode.CABLE || categoryCode === MaterialCategoryCode.DISTRIBUTION_CABLE;
  }

  /** 根据物资种类判断是否是线缆 */
  function isCableBySubClassCode(subClassCode: string) {
    const cableArr: Array<string> = [
      MaterialSubclassCode.HIGH_VOLTAGE_CABLE,
      MaterialSubclassCode.MEDIUM_VOLTAGE_CABLE,
      MaterialSubclassCode.LOW_VOLTAGE_CABLES,
      MaterialSubclassCode.ADSS,
      MaterialSubclassCode.OPGW_OPPC,
      MaterialSubclassCode.WIRE_GROUND,
      MaterialSubclassCode.DISTRIBUTION_WIRE_GROUND,
      MaterialSubclassCode.CABLE_JOINT,
      MaterialSubclassCode.CABLE_TERMINAL
    ];
    return cableArr.includes(subClassCode);
  }

  /** 根据物资种类判断是否是线圈 */
  function isCoilBySubClassCode(subClassCode: string) {
    const coilArr: Array<string> = [
      MaterialSubclassCode.TRANSFORMER,
      MaterialSubclassCode.REACTOR,
      MaterialSubclassCode.CURRENT_TRANSFORMER,
      MaterialSubclassCode.VOLTAGE_TRANSFORMER,
      MaterialSubclassCode.LIGHTNING_ARRESTER
    ];
    return coilArr.includes(subClassCode);
  }

  /** 是否是金具 */
  function isArmourClamp(subclassCode: string) {
    return subclassCode === MaterialSubclassCode.ARMOUR_CLAMP;
  }

  /** 出厂试验是否 维护盘号，线芯名称 字段 */
  function showFieldOfOutFactoryExperiment(isCable: boolean, subclassCode: string) {
    return isCable && !CABLE_TERMINAL_ADAPTER.includes(subclassCode);
  }

  /** 物资种类是否变压器&组合电器 */
  function isTransformerCompositeApparatus(subclassCode: string) {
    const collection: Array<string> = [MaterialSubclassCode.TRANSFORMER, MaterialSubclassCode.COMBINATION_APPARATUS];
    return collection.includes(subclassCode);
  }

  function isCableJoint(subclassCode: string) {
    return MaterialSubclassCode.CABLE_JOINT === subclassCode;
  }

  /** 物资种类是否是电缆中间接头&电缆终端 */
  function isCableTerminalAdapter(subclassCode: string) {
    const collection: Array<string> = [MaterialSubclassCode.CABLE_JOINT, MaterialSubclassCode.CABLE_TERMINAL];
    return collection.includes(subclassCode);
  }

  /** 物资种类是否是电缆或配网导地线 */
  function isCableOrDistributionWireGround(subclassCode: string) {
    const collection: Array<string> = [
      MaterialSubclassCode.MEDIUM_VOLTAGE_CABLE,
      MaterialSubclassCode.HIGH_VOLTAGE_CABLE,
      MaterialSubclassCode.LOW_VOLTAGE_CABLES,
      MaterialSubclassCode.DISTRIBUTION_WIRE_GROUND
    ];
    return collection.includes(subclassCode);
  }

  return {
    isCable,
    isCableBySubClassCode,
    isCoilBySubClassCode,
    isArmourClamp,
    showFieldOfOutFactoryExperiment,
    isCableTerminalAdapter,
    isCableJoint,
    isCableOrDistributionWireGround,
    isTransformerCompositeApparatus
  };
}
