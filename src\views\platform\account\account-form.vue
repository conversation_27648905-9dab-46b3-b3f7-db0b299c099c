<template>
  <div class="px-5">
    <el-alert
      class="!mb-3"
      title="默认密码为: SDCC@eip@手机号 , 支持管理员手动修改密码"
      :closable="false"
      type="warning"
    />
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col>
          <el-form-item label="用户名" prop="username">
            <el-input placeholder="请输入用户名" v-model="form.username" :disabled="disabled" />
          </el-form-item>
        </el-col>
        <el-col>
          <ElCollapse>
            <el-form-item label="手机号" prop="mobile">
              <el-input placeholder="请输入手机号" v-model="form.mobile" />
            </el-form-item>
          </ElCollapse>
        </el-col>
        <el-col>
          <el-form-item label="姓名" prop="nickname">
            <el-input placeholder="请输入姓名" v-model="form.nickname" />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="邮箱" prop="email">
            <el-input placeholder="请输入邮箱" v-model="form.email" />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="所属企业" prop="tenantIds">
            <el-select
              placeholder="请选择所属企业"
              class="w-full"
              v-model="form.tenantIds"
              clearable
              :disabled="disabled"
              multiple
              filterable
            >
              <el-option
                v-for="item in tenantStore.tenants || []"
                :key="item.id"
                :label="item.comName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="个人简介" prop="remark">
            <el-input
              type="textarea"
              placeholder="请输入个人简介"
              show-word-limit
              clearable
              v-model="form.remark"
              :maxlength="15"
              resize="none"
            />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="form.status"
              :active-value="0"
              :inactive-value="1"
              active-text="启用"
              inactive-text="禁用"
              inline-prompt
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IAccountForm } from "@/models";
import { useAccountStore, useTenantStore } from "@/store/modules";
import { EMAIL_REGEXP, MOBILE_REGEXP, USER_NAME_REGEXP } from "@/consts";
import { StatusEnum } from "@/enums";

const props = defineProps<{
  isAdd: boolean;
}>();

defineExpose({
  validate,
  getValidValue
});

const accountStore = useAccountStore();
const tenantStore = useTenantStore();

const formRef = ref<FormInstance>();
const form = reactive<IAccountForm>({
  id: undefined,
  username: undefined,
  mobile: undefined,
  nickname: undefined,
  email: undefined,
  status: undefined,
  tenantIds: [],
  remark: undefined
});
const rules = reactive<FormRules>({
  username: props.isAdd
    ? [
        { required: true, trigger: "change", message: "用户名不能为空" },
        {
          required: true,
          message: "3到20字符,可包含 字母 数字 @ . _",
          trigger: "change",
          pattern: USER_NAME_REGEXP
        }
      ]
    : [{ required: true, trigger: "change", message: "用户名不能为空" }],
  mobile: [
    { required: true, trigger: "change", message: requiredMessage("手机号") },
    { trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }
  ],
  nickname: [{ required: true, message: requiredMessage("姓名"), trigger: "change" }],
  email: [{ message: "邮箱格式不正确", trigger: "change", pattern: EMAIL_REGEXP }],
  tenantIds: [{ required: true, message: requiredMessage("所属企业"), trigger: "change" }]
});

const disabled = ref<boolean>();
watchEffect(() => {
  if (accountStore.accountForm && Object.keys(accountStore.accountForm).length) {
    Object.assign(form, accountStore.accountForm);
  }
  disabled.value = !!accountStore.accountForm?.id;
});

tenantStore?.queryTenant({ status: StatusEnum.ENABLE, pageSize: 100 });

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IAccountForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
