<template>
  <el-scrollbar :height="348">
    <div class="air-tightness-list">
      <div
        class="overflow-hidden"
        v-infinite-scroll="loadMore"
        :infinite-scroll-immediate="false"
        v-if="exFactoryQMXStore.outGoingFactoryQmxExperimentList.length"
      >
        <div
          class="info-content mb-5"
          v-for="item in exFactoryQMXStore.outGoingFactoryQmxExperimentList"
          :key="item.id"
        >
          <div class="title">
            <div class="title-left">
              <span class="line" />
              <span class="text-info">试验信息</span>
            </div>
            <div class="operate">
              <el-button
                v-auth="PermissionKey.form.formPurchaseFactoryTrialDelete"
                type="danger"
                @click="delAirTightnessItem(item)"
              >
                <el-icon class="mr-2"><Delete /></el-icon>
                删除
              </el-button>
              <el-button
                v-auth="PermissionKey.form.formPurchaseFactoryTrialEdit"
                type="primary"
                v-if="isCanOperateAddAndEdit"
                @click="editAirTightnessItem(item)"
              >
                <FontIcon class="mr-2" icon="icon-edit" />
                编辑
              </el-button>
            </div>
          </div>
          <div class="inspection-info">
            <!-- 金具 -->
            <el-descriptions>
              <el-descriptions-item label="试验编号"> {{ item.experimentNo }}</el-descriptions-item>
              <el-descriptions-item label="产品名称">{{ item.productName }}</el-descriptions-item>
              <el-descriptions-item label="产品型号">{{ item.model }}</el-descriptions-item>
              <el-descriptions-item label="生产批次号">
                {{ item.productBatchNo }}
              </el-descriptions-item>
              <el-descriptions-item label="抽检批次号">
                {{ item.inspectBatchNo }}
              </el-descriptions-item>
              <el-descriptions-item label="检验时间">
                {{ formatDate(item.inspectDate, fullDateFormat) }}
              </el-descriptions-item>
              <el-descriptions-item label="样品编号">
                {{ item.sampleNo }}
              </el-descriptions-item>
              <el-descriptions-item label="检测人员">
                {{ item.inspectOperate }}
              </el-descriptions-item>
              <el-descriptions-item label="审核人员">
                {{ item.auditor }}
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="3">
                {{ item.remark }}
              </el-descriptions-item>
            </el-descriptions>

            <PureTable
              class="flex-1 overflow-hidden mt-1"
              row-key="id"
              :data="item.rawMetadataValue || []"
              :columns="columns"
              :max-height="300"
              showOverflowTooltip
              :row-class-name="getRowClassName"
            >
              <template #empty>
                <el-empty :image-size="120">
                  <template #image> <EmptyData /> </template>
                </el-empty>
              </template>
            </PureTable>
          </div>
        </div>
      </div>
      <div v-else class="flex flex-c">
        <div class="text-center p-4">
          <emptyData />
          <div class="not-data">暂无数据</div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { Delete } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { ElMessageBox, ElMessage } from "element-plus";
import { useEXFactoryQMXStore, useEXFactoryExperimentStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { IOutGoingFactoryQmxExperiment } from "@/models";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { fullDateFormat } from "@/consts/date-format";
import { PermissionKey } from "@/consts";
import { computed } from "vue";

const exFactoryQMXStore = useEXFactoryQMXStore();
const { columns } = useColumns();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();

/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => exFactoryExperimentStore.getIsCanOperateAddAndEdit);

const emits = defineEmits<{
  (event: "editQMXExperiment", params: IOutGoingFactoryQmxExperiment): void;
  (event: "deleteQMXExperimentSuccess"): void;
  (event: "loadMoreEvent"): void;
}>();

// 删除出厂试验信息
const delAirTightnessItem = (data: IOutGoingFactoryQmxExperiment) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const delRes = await exFactoryQMXStore.deleteOutGoingFactoryQMXExperiment(data.id);
      if (!delRes.data) {
        ElMessage.error(delRes.msg);
        return;
      }

      ElMessage({ type: "success", message: "删除成功" });
      emits("deleteQMXExperimentSuccess");
      productionTestSensingStore.refreshProductionProcessStatus();
    })
    .catch(() => {});
};
// 编辑出厂试验信息
const editAirTightnessItem = (data: IOutGoingFactoryQmxExperiment) => {
  emits("editQMXExperiment", data);
};

/**
 * 加载更多
 */
const loadMore = () => {
  emits("loadMoreEvent");
};

// 设置行类名
function getRowClassName({ row }) {
  const valid = row.validated;
  return valid ? null : "validated-waring";
}
</script>

<style scoped lang="scss">
.air-tightness-list {
  .info-content {
    border-radius: 10px;
    padding: 20px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 15px;
      box-sizing: border-box;
      border-bottom: 1px solid #dcdfe6;
      margin-bottom: 15px;

      .title-left {
        display: flex;
        align-items: center;

        .line {
          display: inline-block;
          width: 4px;
          height: 16px;
          margin-right: 10px;
          background: var(--el-color-primary);
        }
      }
    }
  }

  .text-center {
    .not-data {
      font-size: var(--el-font-size-base);
      color: var(--el-text-color-secondary);
    }
  }

  :deep(.validated-waring) {
    .valid-waring {
      color: var(--el-color-warning);
    }
  }
}
</style>
