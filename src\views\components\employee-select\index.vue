<template>
  <div>
    <ElForm :inline="true" class="flex-1" @submit.prevent>
      <ElFormItem class="w-2/5">
        <ElInput
          class="w-full"
          clearable
          v-model="state.keyword"
          placeholder="请输入用户名/姓名/手机号"
          @keydown.enter="onConfirmQuery()"
          @clear="onClearKeyWord()"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton type="primary" @click="onConfirmQuery()">搜索</ElButton>
      </ElFormItem>
    </ElForm>
  </div>
  <PureTable
    class="flex-1 overflow-hidden pagination"
    row-key="id"
    ref="multipleTableRef"
    :data="state.employees"
    :columns="columns"
    showOverflowTooltip
    :height="500"
    :loading="state.loading"
    @page-size-change="onPageSizeChange()"
    @page-current-change="onPageCurrentPage()"
    @selection-change="onSelectionChange"
    @row-click="onRowClick"
  >
    <template #empty>
      <CxEmptyData />
    </template>
  </PureTable>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns } from "./columns";
import { useEmployeeStore } from "@/store/modules";
import { reactive, ref } from "vue";
import CxEmptyData from "@/components/CxEmpty";
import { IEmployee, IEmployeeReq } from "@/models";
import PureTable from "@pureadmin/table";
import { ElTable } from "element-plus";
import { StatusEnum } from "@/enums";

const employeeStore = useEmployeeStore();
const { columns } = useColumns();
const { pagination } = useTableConfig();
const multipleTableRef = ref();
const props = withDefaults(
  defineProps<{
    params: IEmployeeReq;
  }>(),
  {
    params: () => ({})
  }
);

const state = reactive<{
  keyword?: string;
  employees: Array<IEmployee>;
  loading: boolean;
}>({
  keyword: undefined,
  employees: [],
  loading: false
});

const emits = defineEmits<{
  (e: "onSelectEmployee", params: Array<IEmployee>);
}>();

pagination.pageSize = 100;
queryEmployee(filterParam());

const onClearKeyWord = () => {
  pagination.currentPage = 1;
  queryEmployee(filterParam());
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  queryEmployee(filterParam());
};

const onPageSizeChange = () => {
  queryEmployee(filterParam());
};

const onPageCurrentPage = () => {
  queryEmployee(filterParam());
};

function filterParam(): IEmployeeReq {
  return { ...props.params, pageNo: pagination.currentPage, pageSize: pagination.pageSize, keyWords: state.keyword };
}

async function queryEmployee(params: IEmployeeReq) {
  state.loading = true;
  const employeeRes = await employeeStore.searchEmployee({ ...params, status: StatusEnum.ENABLE });
  pagination.total = employeeRes.data.total;
  state.employees = employeeRes.data.list;
  state.loading = false;
}

const onSelectionChange = (employees: Array<IEmployee>) => {
  emits("onSelectEmployee", employees);
};

const onRowClick = (row: IEmployee) => {
  const elTableRef: InstanceType<typeof ElTable> = multipleTableRef.value.getTableRef();
  const selectionRows: Array<IEmployee> = elTableRef.getSelectionRows();
  elTableRef.toggleRowSelection(row, !selectionRows.find(x => x.id === row.id));
  emits("onSelectEmployee", elTableRef.getSelectionRows());
};
</script>

<style scoped lang="scss"></style>
