<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex justify-end bg-white p-5 pt-0">
      <add-edit-certificate-dialog mode="add" @post-save-success="reloadList">
        <template #trigger="{ openDialog }">
          <el-button type="primary" @click="openDialog" :icon="Plus">新增合格证</el-button>
        </template>
      </add-edit-certificate-dialog>
    </div>
    <div class="p-5 mx-6 my-5 bg-white flex-1 flex flex-col overflow-hidden">
      <!-- 表格 -->
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="{ row }">
          <add-edit-certificate-dialog class="ml-0" mode="edit" :id="row.id" @post-save-success="reloadList">
            <template #trigger="{ openDialog }">
              <el-button type="primary" link @click="openDialog">编辑</el-button>
            </template>
          </add-edit-certificate-dialog>
          <el-button type="primary" link @click="onTemplateEdit(row.id)">模板编辑</el-button>
          <el-button type="danger" link @click="onDelete(row.id)"> 删除 </el-button>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { usePageStore } from "@/store/modules/page";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PureTable } from "@pureadmin/table";
import { genCertificateTableColumnsConfig } from "./column-config";
import AddEditCertificateDialog from "./add-edit-certificate-dialog/index.vue";
import { ICertificate } from "../models";
import { deleteCertificate, getCertificateList } from "../api/certificate-manage";
import { useConfirm } from "@/utils/useConfirm";
import { useRouter } from "vue-router";

usePageStore().setTitle("合格证管理");
const { pagination } = useTableConfig();
const router = useRouter();

const { columnsConfig } = genCertificateTableColumnsConfig();
const loading = ref(false);
const list = ref<ICertificate[]>([]);

onMounted(() => {
  requestList();
});

const requestList = useLoadingFn(async () => {
  const params = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };
  const { data } = await getCertificateList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }

  await deleteCertificate(id);
  reloadList();
};

const onTemplateEdit = (id: string) => {
  router.push(`ecode-certificate-edit/${id}`);
};
</script>

<style scoped></style>
