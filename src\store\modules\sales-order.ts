import { defineStore } from "pinia";
import {
  createSalesOrder,
  createSalesOrderAndLines,
  getSalesOrdersByPurchaseOrderId,
  updateSalesOrder
} from "@/api/sales-order";
import { ISalesOrderDto } from "@/models";

export const useSalesOrderStore = defineStore({
  id: "cx-sales-order",
  actions: {
    async getSalesOrdersByPurchaseOrderId(id: string) {
      const data = (await getSalesOrdersByPurchaseOrderId(id)).data;
      return Array.isArray(data) ? data : [];
    },
    async createSalesOrder(order: ISalesOrderDto) {
      return createSalesOrder(order);
    },
    async createSalesOrderAndLines(order: ISalesOrderDto) {
      return createSalesOrderAndLines(order);
    },
    async updateSalesOrder(order: ISalesOrderDto) {
      return updateSalesOrder(order);
    }
  }
});
