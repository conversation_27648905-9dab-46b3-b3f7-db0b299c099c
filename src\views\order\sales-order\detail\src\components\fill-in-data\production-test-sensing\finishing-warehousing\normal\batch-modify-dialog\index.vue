<template>
  <div class="inline-block mx-3">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      title="批量修改"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
      @close="closeDialog"
    >
      <!-- 内容 -->
      <modify-form ref="formRef" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import ModifyForm from "./modify-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IBatchModifyFinishedProductStorageReq } from "@/models";
import { batchModifyFinishedProductStorage } from "@/api/finished-product-storage";

/**
 * 批量修改成品入库的时间、状态
 */

const loading = ref(false);

const props = defineProps<{
  /**
   * 成品id
   */
  idList: string[];
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof ModifyForm>>();
const dialogVisible = ref(false);

const requestSave = useLoadingFn(async (v: Omit<IBatchModifyFinishedProductStorageReq, "ids">) => {
  const formVal: IBatchModifyFinishedProductStorageReq = Object.assign(v, { ids: props.idList });
  const { data } = await batchModifyFinishedProductStorage(formVal);
  return data;
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();

  // 请求保存
  const res = await requestSave(formVal);
  if (res) {
    // 处理保存后续事件
    closeDialog();
    emits("postSaveSuccess");
    ElMessage({ type: "success", message: "批量修改成功" });
  }
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
