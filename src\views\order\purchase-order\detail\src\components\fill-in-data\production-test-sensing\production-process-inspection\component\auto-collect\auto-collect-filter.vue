<template>
  <section class="filter-auto-collect">
    <span class="mr-1">报工批次号:</span>
    <el-select v-model="acitveItem" class="m-2" placeholder="请选择" filterable value-key="id">
      <el-option v-for="item in options" :key="item.id" :label="item.productBatchNo" :value="item" />
    </el-select>

    <el-button
      v-auth="PermissionKey.form.formPurchaseProcessCheckProdData"
      class="-mt-1 mx-2"
      type="primary"
      link
      @click="checkProductionData"
      >生产数据检查</el-button
    >

    <span v-if="acitveItem">
      选用设备：
      <el-button class="-mt-1" link type="primary" @click="navgateToDeviceDetail">
        {{ `${acitveItem.deviceName}（${acitveItem.deviceCode}）` }}
      </el-button>
    </span>
  </section>
</template>

<script setup lang="ts">
import { watch } from "vue";
import { IReportWork } from "@/models";
import { useVModel } from "@vueuse/core";
import { ElMessage, ElMessageBox } from "element-plus";
import { checkAutoCollectProdData } from "@/api";
import { PermissionKey } from "@/consts";

const props = defineProps<{
  modelValue: IReportWork;
  options: IReportWork[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: IReportWork): void;
}>();

const acitveItem = useVModel(props, "modelValue", emit);

/**
 * @description: 跳转到设备详情
 */
function navgateToDeviceDetail() {
  const baseUrl = window.location.protocol + "//" + window.location.host;
  const { deviceId, workStartTime, workEndTime } = acitveItem.value;
  const now = new Date().toISOString();
  window.open(
    `${baseUrl}/device/${deviceId}?tab=data-acquisition&subTab=HISTORY&workStartTime=${workStartTime}&workEndTime=${
      workEndTime || now
    }`
  );
}

/**
 * @description: 检查生产数据
 */
async function checkProductionData() {
  const action = await ElMessageBox.alert("是否确认检查生产数据？", "检查数据", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    showCancelButton: true,
    type: "warning"
  });
  if (action !== "confirm") {
    return;
  }
  const { data: result } = await checkAutoCollectProdData({
    reportIds: [acitveItem.value.id],
    doNotSync: true
  });
  if (result) {
    ElMessage.success("操作成功，已加入检查队列");
  }
}

watch(
  () => props.options,
  options => {
    if (options.length) {
      acitveItem.value = options[0];
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss"></style>
