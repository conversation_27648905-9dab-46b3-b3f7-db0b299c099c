<template>
  <el-dialog
    title="编辑技术标准"
    class="cx-form"
    v-model="editVisible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <TechnicalStandardForm ref="editFormRef" v-loading="loading" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
        >保存，并重新同步</el-button
      >
      <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TechnicalStandardForm from "@/views/order/purchase-order/detail/src/components/fill-in-data/technical-standard/components/edit-form.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ITechnicalStandardInfo } from "@/models";
import { provide, reactive, watch } from "vue";
import { useTechnicalStandardStore } from "@/store/modules";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";
import { ITechnicalStandardSync } from "@/models/state-grid-sync/iot-sync/i-technical-standard-sync";
import * as api from "@/api/technical-standard";

const props = defineProps<{
  subclassCode: string | undefined;
}>();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof TechnicalStandardForm>>(updateStandardInfo);

const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});
const store = useTechnicalStandardStore();
const { loading, handlePatchValue } = usePatchValue(patchValue);

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

async function openEditDialog(sync: ITechnicalStandardSync) {
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: sync.dataId,
    no: sync.ipoNo
  };
  handlePatchValue(sync.dataId);
}

async function patchValue(id: string) {
  const res = (await api.getDetailOfStandard(id)).data;
  editFormRef.value?.patchFormValue(res);
}

async function updateStandardInfo() {
  const formValue = (await editFormRef.value.getFormValue()) as ITechnicalStandardInfo;
  if (!formValue) {
    return;
  }
  return (await store.saveEditStandardInfo(formValue?.id, formValue)).data;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
