import { IEipLog, IEipLogReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/logging/eip";
import { isAllEmpty } from "@pureadmin/utils";
import { omitBy } from "lodash-unified";

export const useFetchOrderStore = defineStore({
  id: "fetch-order-store",
  state: () => ({
    total: 0 as number,
    loading: false as boolean,
    list: [] as Array<IEipLog>
  }),
  actions: {
    async querySgOrder(params?: IEipLogReq) {
      this.loading = true;
      const res = await api.queryEIPLog(omitBy(params, val => isAllEmpty(val)));
      this.total = res.data.total;
      this.list = res.data.list;
      this.loading = false;
    },

    async getDownloadFileUrl(id: string) {
      return (await api.getDownloadFileUrl(id)).data;
    }
  }
});
