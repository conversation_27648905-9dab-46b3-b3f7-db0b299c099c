<template>
  <!-- 工艺路线 -->
  <div class="flex h-full px-6 py-5">
    <!-- 原材料类型 -->
    <div class="w-1/5 mr-4 overflow-hidden raw-material-type bg-bg_color">
      <div class="h-full p-4" v-loading="treeLoading">
        <TitleBar title="物资种类" />
        <el-scrollbar height="calc(100% - 25px)">
          <el-tree
            ref="treeRef"
            class="filter-tree"
            :data="categoryStore.tenantCategoryCodeTree"
            :props="defaultProps"
            highlight-current
            node-key="categoryCode"
            @node-click="handleNodeClick"
            default-expand-all
            :current-node-key="selectTreeNode.categoryCode"
          />
        </el-scrollbar>
      </div>
    </div>
    <div class="flex flex-1 overflow-hidden raw-material-container">
      <div class="w-1/4 p-4 mr-4 raw-material-info bg-bg_color">
        <!-- 工艺路线  -->
        <RoutingList
          :currentCategoryCode="selectTreeNode.categoryCode"
          :currentCategoryName="selectTreeNode.categoryName"
          ref="rawMaterialDetailInstance"
        />
      </div>
      <div class="flex flex-1 overflow-hidden raw-material-detail">
        <div class="w-full p-4 bg-bg_color raw-material-test">
          <!-- 关联工序  -->
          <RelevanceProcess />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted, onMounted } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import TitleBar from "@/components/TitleBar";
import { ElTree } from "element-plus";
import RoutingList from "./routing-list/index.vue";
import RelevanceProcess from "./relevance-process/index.vue";
import { ICategoryInfoTree } from "@/models";
import { useCategoryStore, useProcessStore, useProcessRouteStore } from "@/store/modules";

usePageStoreHook().setTitle("工艺路线");

const categoryStore = useCategoryStore();

const treeLoading = ref<boolean>(false);

const defaultProps = {
  children: "children",
  label: "categoryName"
};

watch(
  () => categoryStore.tenantCategoryCodeTree,
  tenantCategoryCodeTree => {
    handleNodeClick(tenantCategoryCodeTree[0]);
  }
);

const processStore = useProcessStore();
const processRouteStore = useProcessRouteStore();
onUnmounted(() => {
  processStore.$reset();
  processRouteStore.$reset();
});

onMounted(() => {
  queryTenantCategory();
});

const queryTenantCategory = async () => {
  treeLoading.value = true;
  await categoryStore.queryTenantCategoryCodeTree();
  treeLoading.value = false;
};

const treeRef = ref<InstanceType<typeof ElTree>>();

const selectTreeNode = ref<{
  categoryCode: string;
  categoryName: string;
}>({
  categoryCode: "",
  categoryName: ""
});
const handleNodeClick = (data: ICategoryInfoTree) => {
  if (data.children && data.children.length > 0) {
    if (selectTreeNode.value.categoryCode) {
      treeRef.value.setCurrentKey(selectTreeNode.value.categoryCode);
    } else {
      treeRef.value.setCurrentKey(data.children[0].categoryCode);
      selectTreeNode.value = data.children[0];
    }
    return;
  } else {
    selectTreeNode.value = data;
  }
};
</script>

<style scoped lang="scss">
.el-tree {
  &.el-tree--highlight-current {
    :deep(.el-tree-node) {
      &.is-current {
        & > .el-tree-node__content {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
