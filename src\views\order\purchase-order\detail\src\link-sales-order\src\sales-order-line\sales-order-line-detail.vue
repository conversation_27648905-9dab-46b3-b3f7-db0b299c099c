<template>
  <div v-loading="store.loading">
    <Card margin="0 0 8px 0" title="基础信息" class="mb-5">
      <el-descriptions>
        <el-descriptions-item label="销售订单行项目号">{{ line.soItemNo }}</el-descriptions-item>
        <el-descriptions-item label="物资种类">{{ line.subClassName }}</el-descriptions-item>
        <el-descriptions-item label="物料编码">{{ line.materialCode }}</el-descriptions-item>
        <el-descriptions-item label="物料数量">
          <template v-if="line.materialNumber">
            {{ formatDecimal(line.materialNumber) }}
            <DictionaryName
              class="ml-1"
              :subClassCode="line.subClassCode"
              :parentCode="MEASURE_UNIT"
              :code="line.materialUnit"
            />
          </template>
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="物料名称">{{ line.materialName }}</el-descriptions-item>
        <el-descriptions-item label="电压等级">{{ voltage }}</el-descriptions-item>
        <el-descriptions-item :span="2" label="规格型号">{{ line.specificationType }}</el-descriptions-item>
        <el-descriptions-item :span="3" label="物料描述">{{ line.materialDesc }}</el-descriptions-item>
      </el-descriptions>
    </Card>
    <Card margin="0 0 8px 0" title="采购订单行项目" class="mb-4">
      <pure-table
        show-overflow-tooltip
        :loading="loading"
        :columns="columns"
        :data="store.purchaseOrderLines"
        :max-height="300"
        row-key="id"
      />
    </Card>
  </div>
</template>

<script setup lang="ts">
import { PureTable, TableColumns } from "@pureadmin/table";
import Card from "@/views/order/purchase-order/detail/src/link-sales-order/src/card.vue";
import { usePurchaseOrderDetailSalesOrderLineDetailStore } from "@/store/modules/purchase-order-detail";
import { computed, ref } from "vue";
import { ISalesOrderLine } from "@/models";
import { formatDecimal, formatEnum } from "@/utils/format";
import { TableWidth, VoltageClassesEnum } from "@/enums";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";
import { MEASURE_UNIT } from "@/consts";

const store = usePurchaseOrderDetailSalesOrderLineDetailStore();

const loading = ref(false);

const line = computed(() => store.salesOrderLine || ({} as ISalesOrderLine));
const voltage = computed(() => formatEnum(line.value.voltageLevel, VoltageClassesEnum, "voltageClassesEnum"));

const columns: Array<TableColumns> = [
  {
    label: "采购订单行项目号",
    prop: "poItemNo",
    width: 150
  },
  {
    label: "采购订单行项目ID",
    prop: "poItemId",
    width: 150
  },
  {
    label: "物料编码",
    prop: "materialCode",
    width: TableWidth.order
  },
  {
    label: "物料描述",
    prop: "materialDesc",
    minWidth: TableWidth.largeName
  },
  {
    label: "采购数量",
    prop: "amount",
    width: TableWidth.number,
    align: "center"
  },
  {
    label: "物资小类名称",
    prop: "matMinName",
    width: TableWidth.name
  }
];
</script>

<style scoped></style>
