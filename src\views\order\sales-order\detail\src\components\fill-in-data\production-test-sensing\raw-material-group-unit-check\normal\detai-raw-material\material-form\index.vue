<template>
  <div class="cx-form">
    <div class="mb-4">
      <TitleBar title="原材料检测" class="mb-2" />
      <DynamicForm ref="dynamicTableFormRef" :dynamic-form-data="resFormData" :edit-mode="false" />
    </div>
    <div class="mb-4">
      <TitleBar title="原材料报告" class="mb-2" />
      <div class="mx-5">
        <UploadFrom ref="dynamicFileRef" :editMode="editMode" :dynamic-file-data="resFileData" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DynamicForm from "@/components/DynamicForm";
import UploadFrom from "@/components/Upload";
import TitleBar from "@/components/TitleBar";
import { useRawMaterialCheckInfoHook } from "@/views/config/raw-material/hook/useRawMaterialCheck";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";

// 获取采集项目信息
const rawMaterilaGroupUnitStore = useRawMaterialGroupUnitStore();
const collectionData = rawMaterilaGroupUnitStore.detailRawMaterialCheckItem;
// 原材料检测项数据
const { getRawMaterialCheckFormData, getRawMaterialCheckFileData } = useRawMaterialCheckInfoHook();

const resFormData = getRawMaterialCheckFormData(collectionData);
const resFileData = getRawMaterialCheckFileData(collectionData);

defineProps({
  editMode: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped lang="scss"></style>
