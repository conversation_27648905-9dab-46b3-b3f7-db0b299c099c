<template>
  <div class="air-tightness-list h-96 overflow-auto">
    <div
      class="overflow-hidden"
      v-infinite-scroll="loadMore"
      :infinite-scroll-distance="30"
      :infinite-scroll-immediate="false"
      v-if="exFactoryQMXStore.outGoingFactoryQmxExperimentList.length"
    >
      <div class="info-content mb-5" v-for="item in exFactoryQMXStore.outGoingFactoryQmxExperimentList" :key="item.id">
        <div class="title">
          <div class="title-left">
            <span class="line" />
            <span class="text-info">试验信息</span>
          </div>
          <div class="operate">
            <el-button
              v-auth="PermissionKey.form.formPurchaseFactoryTrialDelete"
              type="danger"
              @click="delAirTightnessItem(item)"
            >
              <el-icon class="mr-2"><Delete /></el-icon>
              删除
            </el-button>
            <el-button
              v-auth="PermissionKey.form.formPurchaseFactoryTrialCreate"
              type="primary"
              v-if="isCanOperateAddAndEdit"
              :icon="CopyDocument"
              @click="copyAirTightnessItem(item)"
            >
              复制
            </el-button>

            <template
              v-if="
                licenseAuthIncludeEJJ &&
                item.rawMetadataValue?.some(x => x.identityCode === EControlType.WaveRoseControl)
              "
            >
              <el-button
                v-auth="PermissionKey.form.formPurchaseFactoryTrialEdit"
                type="primary"
                :icon="Upload"
                @click="onUploadWaveData(item)"
              >
                上传波形数据
              </el-button>
            </template>

            <el-button
              v-auth="PermissionKey.form.formPurchaseFactoryTrialEdit"
              type="primary"
              v-if="isCanOperateAddAndEdit"
              @click="editAirTightnessItem(item)"
            >
              <FontIcon class="mr-2" icon="icon-edit" />
              编辑
            </el-button>
          </div>
        </div>
        <div class="inspection-info">
          <el-descriptions>
            <el-descriptions-item label="试验编号"> {{ item.experimentNo }}</el-descriptions-item>
            <el-descriptions-item v-if="isCoil" label="试验时间">
              {{ formatDate(item.testTime, fullDateFormat) }}
            </el-descriptions-item>
            <div v-else>
              <el-descriptions-item label="成品编号">{{ item.finProNo }}</el-descriptions-item>
              <el-descriptions-item label="需求数量">{{ item.account }}</el-descriptions-item>
              <el-descriptions-item label="需求单位">
                {{ item.measUnit }}
              </el-descriptions-item>
              <div v-if="showFiled">
                <el-descriptions-item label="线芯名称">
                  {{ item.wireName }}
                </el-descriptions-item>
              </div>
              <el-descriptions-item label="开始时间">
                {{ formatDate(item.startTime, fullDateFormat) }}
              </el-descriptions-item>
              <el-descriptions-item label="结束时间">
                {{ formatDate(item.endTime, fullDateFormat) }}
              </el-descriptions-item>
              <el-descriptions-item label="试验状态">
                {{ formatEnum(item.experimentStatus, QMXExperimentStatusEnum, "QMXExperimentStatusEnum") }}
              </el-descriptions-item>
              <el-descriptions-item label="试验结果">
                {{ formatEnum(item.experimentResult, QMXExperimentResultsEnum, "QMXExperimentResultsEnum") }}
              </el-descriptions-item>
            </div>
          </el-descriptions>

          <PureTable
            class="flex-1 overflow-hidden mt-1"
            row-key="id"
            :data="item.rawMetadataValue || []"
            :columns="columns"
            :max-height="300"
            showOverflowTooltip
            :row-class-name="getRowClassName"
          >
            <template #targetValueLabel="{ row }">
              <wave-rose-dialog
                v-if="row.dataTypeIdentityDetail.identityCode === EControlType.WaveRoseControl"
                :data-source="genWaveList(row.targetValue, row.waveFormConfig)"
              />
              <div v-else>{{ getSelectLabel(row) }}</div>
            </template>
            <template #empty>
              <el-empty :image-size="120">
                <template #image> <EmptyData /> </template>
              </el-empty>
            </template>
          </PureTable>
        </div>
      </div>
    </div>
    <div v-else class="flex flex-c">
      <div class="text-center p-4">
        <emptyData />
        <div class="not-data">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { Delete, CopyDocument, Upload } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { ElMessageBox, ElMessage } from "element-plus";
import { useEXFactoryQMXStore, useEXFactoryExperimentStore, useSystemAuthStore } from "@/store/modules";
import { formatDate, formatEnum } from "@/utils/format";
import { QMXExperimentStatusEnum, QMXExperimentResultsEnum } from "@/enums";
import { IOutGoingFactoryQmxExperiment } from "@/models";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { fullDateFormat } from "@/consts/date-format";
import { PermissionKey } from "@/consts";
import { computed } from "vue";
import {} from "@element-plus/icons-vue";
import { useMaterial } from "@/utils/material";
import { useDynamicFormLabel } from "@/utils/useDynamicFormSelect";
import WaveRoseDialog from "@/views/components/wave-rose-dialog/index.vue";
import { genWaveList } from "@/views/components/wave-rose-dialog/data-conversion";
import { EControlType } from "@/enums";
import { computedAsync } from "@vueuse/core";

const { getSelectLabel } = useDynamicFormLabel();

const props = defineProps<{
  subClassCode?: string;
  isCable?: boolean;
}>();

const exFactoryQMXStore = useEXFactoryQMXStore();
const { columns } = useColumns();
const systemAuthStore = useSystemAuthStore();
const licenseAuthIncludeEJJ = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeEJJ);
const productionTestSensingStore = useSalesProductionTestSensingStore();
const exFactoryExperimentStore = useEXFactoryExperimentStore();

/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => exFactoryExperimentStore.getIsCanOperateAddAndEdit);
const materialTool = useMaterial();
const showFiled = computed(() => {
  return materialTool.showFieldOfOutFactoryExperiment(props.isCable, props.subClassCode);
});

const isCoil = computed(() => materialTool.isCoilBySubClassCode(props.subClassCode));

const emits = defineEmits<{
  (event: "editQMXExperiment", params: IOutGoingFactoryQmxExperiment): void;
  (event: "copyQMXExperiment", params: IOutGoingFactoryQmxExperiment): void;
  (event: "uploadWaveData", params: IOutGoingFactoryQmxExperiment): void;
  (event: "deleteQMXExperimentSuccess"): void;
  (event: "loadMoreEvent"): void;
}>();

// 删除出厂试验信息
const delAirTightnessItem = (data: IOutGoingFactoryQmxExperiment) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      await exFactoryQMXStore.deleteOutGoingFactoryQMXExperiment(data.id);
      ElMessage({ type: "success", message: "删除成功" });
      emits("deleteQMXExperimentSuccess");
      productionTestSensingStore.refreshProductionProcessStatus();
    })
    .catch(() => {});
};

// 复制出厂试验信息
const copyAirTightnessItem = (data: IOutGoingFactoryQmxExperiment) => {
  emits("copyQMXExperiment", data);
};

// 编辑非出厂试验信息
const editAirTightnessItem = (data: IOutGoingFactoryQmxExperiment) => {
  emits("editQMXExperiment", data);
};

const onUploadWaveData = (data: IOutGoingFactoryQmxExperiment) => {
  emits("uploadWaveData", data);
};

// 设置行类名
function getRowClassName({ row }) {
  const valid = row.validated;
  return valid ? null : "validated-waring";
}

/**
 * 加载更多
 */
const loadMore = () => {
  emits("loadMoreEvent");
};
</script>

<style scoped lang="scss">
@import "@/style/mixin.scss";

.air-tightness-list {
  @include scrollBar;

  .info-content {
    border-radius: 10px;
    padding: 20px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #dcdfe6;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 15px;
      box-sizing: border-box;
      border-bottom: 1px solid #dcdfe6;
      margin-bottom: 15px;

      .title-left {
        display: flex;
        align-items: center;

        .line {
          display: inline-block;
          width: 4px;
          height: 16px;
          margin-right: 10px;
          background: var(--el-color-primary);
        }
      }
    }
  }

  .text-center {
    .not-data {
      font-size: var(--el-font-size-base);
      color: var(--el-text-color-secondary);
    }
  }

  :deep(.validated-waring) {
    .valid-waring {
      color: var(--el-color-warning);
    }
  }
}
</style>
