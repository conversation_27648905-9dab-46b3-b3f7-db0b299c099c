import { ITenant } from "./tenant";

export type ITenantForm = Pick<
  ITenant,
  | "id"
  | "comName"
  | "name"
  | "address"
  | "status"
  | "contactName"
  | "contactMobile"
  | "contactEmail"
  | "enableEip"
  | "supplierCode"
  | "supplierName"
  | "subclassCode"
  | "publicKey"
  | "enableIot"
  | "iotSubclassCode"
  | "iotClientId"
  | "iotClientSecret"
  | "iotScopes"
  | "enableGz"
  | "gzSubclassCode"
  | "gzAccountName"
  | "gzAccountKey"
  | "tenantKey"
  | "manufacturerId"
>;
