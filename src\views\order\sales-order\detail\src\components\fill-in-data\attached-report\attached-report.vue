<template>
  <div class="attached-report flex flex-col">
    <div class="upload-attached flex justify-end mb-5">
      <el-button type="primary" @click="addProcessDoc()">
        <FontIcon class="mr-2" icon="icon-plus" />
        新增工序文档
      </el-button>
    </div>
    <!-- 工序文档数据 -->
    <PureTable
      ref="tableRef"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="processDocList"
      :height="360"
      :columns="columns"
      :loading="loading"
      showOverflowTooltip
    >
      <template #operate="data">
        <el-button type="primary" link @click="editProcessDoc(data.row)">编辑</el-button>
        <el-button type="danger" link @click="delProcessDoc(data.row.id)">删除</el-button>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
      <template #fileInfo="data">
        <el-popover :width="60" placement="left" trigger="hover">
          <div class="flex flex-col items-center">
            <el-button type="primary" link @click="previewProcessDoc(data.row)">预览</el-button>
            <el-divider class="!my-3" />
            <el-button type="primary" link @click="downLoadProcessDoc(data.row)">下载</el-button>
          </div>
          <template #reference>
            <el-button type="primary" link>
              <el-icon :size="14"><Document /></el-icon>
              <span class="name">{{ data.row.fileInfo.name }}</span>
            </el-button>
          </template>
        </el-popover>
      </template>
    </PureTable>
  </div>
  <!-- 新增或者编辑附件报告 -->
  <AttachedReportFormDialog
    v-model="visible"
    :selectedId="selectedId"
    :processDocInfo="currentRowInfo"
    @update:model-value="initProcessDoc"
  />
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import AttachedReportFormDialog from "./attached-report-info/attached-report-info.vue";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useColumns } from "./columns";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useAttachReportStore, useSalesFillInDataStore } from "@/store/modules";
import { IAttachDocumentRes } from "@/models/attached-report";
import { IProductOrder } from "@/models";
import { ElMessage } from "element-plus";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

const { columns } = useColumns();
const processDocList = ref<Array<IAttachDocumentRes>>([]);
const loading = ref<boolean>(false);
const initProcessDoc = useLoadingFn(refreshAttacheReport, loading);
const visible = ref<boolean>(false);
const selectedId = ref<string>(undefined);
const currentRowInfo = ref();
const attachReportStore = useAttachReportStore();
const fillInDataStore = useSalesFillInDataStore();

onMounted(() => {
  watch(
    () => fillInDataStore.data,
    async () => {
      if (fillInDataStore.data) {
        await initProcessDoc();
      }
    }
  );
});

/** 新增工序文档 */
function addProcessDoc() {
  selectedId.value = "";
  currentRowInfo.value = "";
  visible.value = true;
}

/** 编辑工序文档 */
function editProcessDoc(row: IAttachDocumentRes) {
  currentRowInfo.value = row;
  selectedId.value = row.id;
  visible.value = true;
}

/**
 * @description: 新开一个tab页预览图片、PDF
 */
function previewProcessDoc(row: IAttachDocumentRes) {
  const url = row.fileInfo.url;
  window.open(url, "_blank");
}

async function downLoadProcessDoc(row: IAttachDocumentRes) {
  const { id, name } = row.fileInfo;
  const blob = await downLoadFile(id);
  downloadByData(blob, name, blob.type);
}

/** 删除工序文档 */
async function delProcessDoc(id: string) {
  if (!(await useConfirm("正在执行删除工序文档，是否继续？", "确认删除"))) {
    return;
  }
  const res: boolean = await attachReportStore.delProcessDocument(id);
  if (res) {
    ElMessage.success(`删除成功`);
  }
  await refreshAttacheReport();
}

/** 刷新工序文档 */
async function refreshAttacheReport() {
  const id = (fillInDataStore.data as IProductOrder).id;
  processDocList.value = await attachReportStore.getProcessDocumentList(id);
}

onUnmounted(() => {
  currentRowInfo.value = null;
});
</script>

<style scoped lang="scss">
.fileInfo {
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
