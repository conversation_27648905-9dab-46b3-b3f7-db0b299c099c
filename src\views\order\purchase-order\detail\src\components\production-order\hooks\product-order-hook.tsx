import { ICreateProductOrder, IResponse } from "@/models";
import { useProductOrderStore, usePurchaseOrderDetailStore } from "@/store/modules";

export const useProductOrderHook = () => {
  const productOrderStore = useProductOrderStore();
  const purchaseOrderStore = usePurchaseOrderDetailStore();

  const onProductOrderModalVisible = () => {
    productOrderStore.initProductOrderDetail();
    productOrderStore.setCreateProductOrder({ categoryCode: purchaseOrderStore.purchaseOrder.categoryCode });
    productOrderStore.setProductOrderFormAddMode(true);
    productOrderStore.setProductOrderModalVisible(true);
  };

  const onEditProductOrderModalVisible = (id: string) => {
    productOrderStore.getProductOrderDetailById(id);
  };

  /** 取消编辑生产订单 */
  const onCloseEditProductOrder = () => {
    productOrderStore.setProductOrderDetail();
  };

  /** 确认编辑生产订单 */
  const onConfirmEditProductOrder = (productOrder: ICreateProductOrder): Promise<IResponse<boolean>> => {
    return productOrderStore.editProductOrder(productOrder);
  };

  return {
    onProductOrderModalVisible,
    onEditProductOrderModalVisible,
    onCloseEditProductOrder,
    onConfirmEditProductOrder
  };
};
