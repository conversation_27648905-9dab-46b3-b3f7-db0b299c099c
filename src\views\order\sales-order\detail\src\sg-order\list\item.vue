<template>
  <div class="flex rounded item-container" v-bind="$attrs">
    <!-- 同步状态图标 -->
    <SyncStatus :success="sync.syncResult" />
    <div class="flex flex-col flex-1 px-6 py-3 bg-bg_color">
      <div class="flex items-center mb-2.5">
        <Description :sync="sync" class="flex-1" />
        <div class="flex flex-col text-right gap-2">
          <el-badge :value="errorCount" :hidden="!errorCount">
            <el-button @click="openDetailDialog" v-track="TrackPointKey.FORM_PURCHASE_SYNC_BTN_DETAIL"
              >同步详情</el-button
            >
          </el-badge>
          <el-tooltip class="hidden" content="仅同步全部未同步、同步失败的数据">
            <el-button
              v-auth="PermissionKey.form.formPurchaseSyncBtnOneKey"
              v-track="TrackPointKey.FORM_PURCHASE_SYNC_BTN_ONE_KEY"
              type="primary"
              @click="showSyncAuditDialog"
            >
              一键同步
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <!-- 同步步骤  -->
      <sync-steps :steps="steps" />
    </div>
  </div>
  <el-dialog
    title="同步确认"
    v-model="syncAuditVisible"
    :close-on-press-escape="false"
    destroy-on-close
    fullscreen
    class="sync-audit-full-screen"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" class="flex justify-between">
        <div class="flex gap-3 items-center">
          <el-button link @click="close">
            <el-icon size="20"><Back /></el-icon>
          </el-button>
          <div :class="titleClass">同步确认</div>
        </div>
        <el-button type="danger" @click="close">
          <el-icon size="20"><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <SyncAudit
      :base-info="syncAuditBaseInfo"
      :business-audit-items="BUSINESS_CARD_IDS"
      :iot-audit-items="IOT_STATE_GRID_CARD_IDS"
      :subClassCode="subClassCode"
      allow-filter-by-order-no
      ref="syncAudit"
    />
    <template #footer>
      <el-button @click="syncAuditVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSyncAll" :loading="loading">确认同步</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SyncStatus from "@/views/order/sales-order/detail/src/components/sync-status.vue";
import Description from "@/views/order/sales-order/detail/src/sg-order/list/description.vue";
import SyncSteps from "@/views/order/sales-order/detail/src/sg-order/list/steps.vue";
import { IIOTStateGridOrderSync, IStateGridOrderSync, IStateGridSyncAuditBaseInfo } from "@/models";
import { computed, ref } from "vue";
import { formatSyncSteps } from "@/views/order/sales-order/detail/src/sg-order/sync-step-tool";
import {
  useSalesFillInDataStore,
  useSalesOrderDetailStore,
  useSalesOrderSyncInfo,
  useSalesStateGridOrderSyncListStore
} from "@/store/modules";
import { ElMessage, ElNotification } from "element-plus";
import { PermissionKey, TrackPointKey } from "@/consts";
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { BUSINESS_CARD_IDS, IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";
import { useConfirm } from "@/utils/useConfirm";
import { OrderType } from "@/enums";
import { Close, Back } from "@element-plus/icons-vue";

const props = defineProps<{
  sync: IStateGridOrderSync;
}>();

const store = useSalesStateGridOrderSyncListStore();
const detailStore = useSalesOrderSyncInfo();
const salesDetailStore = useSalesOrderDetailStore();
const fillInDataStore = useSalesFillInDataStore();

const syncAuditVisible = ref(false);
const syncAuditBaseInfo = ref<IStateGridSyncAuditBaseInfo>();
const syncAudit = ref<InstanceType<typeof SyncAudit>>();
const loading = ref(false);
const handleSyncAll = useLoadingFn(syncAll, loading);
const handleSyncIotAll = useLoadingFn(syncIotAll, loading);

const sync = computed(() => props.sync);
const steps = computed(() => formatSyncSteps(props.sync.detail));
const errorCount = computed(() => steps.value.reduce((prev, cur) => prev + (cur.errorCount ?? 0), 0));
const subClassCode = fillInDataStore.data?.subClassCode || fillInDataStore.data?.subclassCode;

function openDetailDialog() {
  // 国网同步详情
  if (detailStore.activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
    detailStore.openDialog(props.sync);
  } else if (detailStore.activeDetailType === SyncOrderTabEnum.SYNC_CSG_GUANGZHOU) {
    // 广州供电局同步详情
    const syncDetail = sync.value as IIOTStateGridOrderSync;
    detailStore.openCsgGuangzhouDialog(syncDetail);
  } else {
    // 上海平台同步详情
    const iotSync = sync.value as IIOTStateGridOrderSync;
    detailStore.openIotDialog(iotSync);
  }
  detailStore.subClassCode = sync.value.subClassCode;
}

/**
 * @description: 一键同步
 */
async function showSyncAuditDialog() {
  if (detailStore.activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
    // 国网一键同步
    syncAuditBaseInfo.value = {
      isCable: salesDetailStore.isCable,
      isArmourClamp: fillInDataStore.isArmourClamp,
      purchaseLineId: sync.value.id,
      orderType: OrderType.SALES,
      orderId: salesDetailStore.saleOrderId,
      ...sync.value
    };
    syncAuditVisible.value = true;
  } else if (detailStore.activeDetailType === SyncOrderTabEnum.SYNC_CSG_GUANGZHOU) {
    const csgSync = sync.value as IIOTStateGridOrderSync;
    const text = `同步【${csgSync.soItemNo}】下所有生产数据到广州供电局`;
    if (!(await useConfirm(text, "确认同步"))) {
      return;
    }
    handleSyncIotAll();
  } else {
    // 上海平台一键同步
    const iotSync = sync.value as IIOTStateGridOrderSync;
    const text = `同步【${iotSync.soItemNo}】下所有生产数据到上海平台`;
    if (!(await useConfirm(text, "确认同步"))) {
      return;
    }
    handleSyncIotAll();
  }
}

async function syncAll() {
  if (syncAudit.value.isEmpty()) {
    ElMessage.warning("当前采购订单行下没有数据可以同步，请先维护相关数据");
    return;
  }
  await store.syncAll(sync.value.id, sync.value.purchaseId);
  ElNotification.info({
    title: "数据同步",
    message: `正在同步采购订单行项目【${sync.value.poItemNo}】下的相关数据`,
    duration: 3000
  });
  syncAuditVisible.value = false;
}

async function syncIotAll() {
  const iotSync = sync.value as IIOTStateGridOrderSync;
  const { id, salesId } = iotSync;
  await store.syncAll(id, salesId);
  ElNotification.info({
    title: "数据同步",
    message: `正在同步销售订单行项目【${iotSync.soItemNo}】下的相关数据`,
    duration: 3000
  });
}
</script>

<style scoped>
.item-container {
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.08);
}
</style>

<style lang="scss">
@import "@/views/order/sales-order/detail/styles/mixin";

.sync-audit-full-screen {
  @include full-screen-dialog;
}
</style>
