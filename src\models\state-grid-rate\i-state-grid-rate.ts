export interface IStateGridRate {
  id: string;
  poNo?: string;
  poItemNo?: string;
  purchaseId?: string;
  purchaseLineId?: string;
  materialCode?: string;
  materialDesc?: string;
  amount?: number;
  matMinName?: string;
  syncResult?: boolean;
  scoreResult?: boolean;
  resultCount?: number;
  subClassCode?: string;
  subClassName?: string;
}

export interface IGridRateCheckRes {
  /** 校验结果 */
  verify: boolean;
  /** 校验信息 */
  message?: string;
}
