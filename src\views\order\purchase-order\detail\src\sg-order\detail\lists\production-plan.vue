<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditProductionPlanDialog ref="editDialog" />
  <DetailProductionPlanDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncType, TableWidth } from "@/enums";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import BaseList from "./base-list.vue";
import { IProductionPlanSync } from "@/models";
import { useStateGridOrderSyncDetailListStore } from "@/store/modules";
import { useSync } from "@/views/order/purchase-order/detail/src/sg-order/detail/lists/hooks/useSync";
import EditProductionPlanDialog from "@/views/components/state-grid-order-sync/dialogs/edit-production-plan-dialog.vue";
import { provide, reactive } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import DetailProductionPlanDialog from "@/views/components/state-grid-order-sync/dialogs/detail-production-plan-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";

const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditProductionPlanDialog>,
  InstanceType<typeof DetailProductionPlanDialog>,
  IProductionPlanSync
>();

const type = StateGridOrderSyncType.PRODUCTION_PLAN;
const { sync, syncByDataId, getHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getHistoryByDataId,
  editFn: openEditDialog
});
const listStore = useStateGridOrderSyncDetailListStore();
listStore.setType(type);

const columns: Array<TableColumns> = [
  {
    label: "销售订单明细",
    prop: "soItemNo",
    minWidth: TableWidth.suborder
  },
  {
    label: "排产计划",
    prop: "ppNo",
    minWidth: TableWidth.order,
    formatter: linkFormatter(openDetailDialog)
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);
</script>

<style scoped></style>
