/**
 * @description: 适用销售订单行tab页接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse } from "@/models";
import {
  ApplicationSalesOrderLineListItem,
  ApplicationSalesOrderLineListParams,
  AddApplicationSalesOrderLineParams,
  RemoveApplicationSalesOrderLineParams,
  ChoosableApplicationSalesOrderLineListParams,
  SelectSalesOrderLineListItem
} from "@/models/quality-tracing";

/**
 * @description: 查询销售订单行列表
 */
export const queryApplicationSalesOrderLineList = (params: ApplicationSalesOrderLineListParams) => {
  return http.post<ApplicationSalesOrderLineListParams, IListResponse<ApplicationSalesOrderLineListItem>>(
    withApiGateway(`admin-api/business/qualitySpecificationSalesLineLink/pageChooseSalesLineList`),
    {
      data: params
    }
  );
};

/**
 * @description: 查询可选适用销售订单行列表
 */
export const queryChoosableApplicationSalesOrderLineList = (params: ChoosableApplicationSalesOrderLineListParams) => {
  return http.post<ChoosableApplicationSalesOrderLineListParams, IListResponse<SelectSalesOrderLineListItem>>(
    withApiGateway(`admin-api/business/qualitySpecificationSalesLineLink/pageNotChooseSalesLineList`),
    {
      data: params
    }
  );
};

/**
 * @description: 添加适用销售订单行
 */
export const addApplicationSalesOrderLine = (params: AddApplicationSalesOrderLineParams) => {
  return http.post<AddApplicationSalesOrderLineParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/qualitySpecificationSalesLineLink/chooseSalesLines`),
    {
      data: params
    }
  );
};

/**
 * @description: 移除适用销售订单行
 */
export const removeApplicationSalesOrderLine = (params: RemoveApplicationSalesOrderLineParams) => {
  return http.post<RemoveApplicationSalesOrderLineParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/qualitySpecificationSalesLineLink/removeChooseSalesLines`),
    {
      data: params
    }
  );
};
