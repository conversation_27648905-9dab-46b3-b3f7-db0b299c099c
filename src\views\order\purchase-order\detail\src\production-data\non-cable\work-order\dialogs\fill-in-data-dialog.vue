<template>
  <el-dialog
    title="填报生产数据"
    v-model="visible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    fullscreen
    class="default state-grid-order-sync-detail-dialog"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div :id="titleId" class="flex justify-between">
        <div class="flex gap-3 items-center">
          <el-button link @click="close">
            <el-icon size="20"><Back /></el-icon>
          </el-button>
          <div :class="titleClass">【填报生产数据】</div>
          <div :class="titleClass" v-if="purchaseOrderDetailStore.purchaseOrder.poNo">
            采购订单号：{{ purchaseOrderDetailStore.purchaseOrder.poNo }}
          </div>
        </div>
        <el-button type="danger" @click="close">
          <el-icon size="20"><Close /></el-icon>
        </el-button>
      </div>
    </template>
    <FillInData />
  </el-dialog>
</template>

<script setup lang="ts">
import FillInData from "@/views/order/purchase-order/detail/src/components/fill-in-data/fill-in-data/index.vue";
import { computed } from "vue";
import { Close, Back } from "@element-plus/icons-vue";
import { usePurchaseOrderDetailStore } from "@/store/modules/purchase-order/purchase-order-detail";

const props = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", visible: boolean): void;
  (e: "shouldRefresh"): void;
}>();

const purchaseOrderDetailStore = usePurchaseOrderDetailStore();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(visible: boolean) {
    emit("update:modelValue", visible);
  }
});
</script>

<style scoped></style>
