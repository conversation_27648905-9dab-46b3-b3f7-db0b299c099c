<template>
  <div class="flex flex-col w-full">
    <component :is="currentDescriptionComp" :sync="sync" />
  </div>
</template>

<script setup lang="ts">
import StateGridSyncDescription from "./description-detail/state-grid-dec.vue";
import ShangHaiIOTDescription from "./description-detail/shang-hai-iot.vue";
import { IStateGridOrderSync } from "@/models";
import { computed } from "vue";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { useSalesOrderSyncInfo } from "@/store/modules";

const props = defineProps<{
  sync: IStateGridOrderSync;
}>();

const salesStateGridOrderSyncStore = useSalesOrderSyncInfo();
const sync = computed(() => props.sync);
const currentDescriptionComp = computed(() => {
  return salesStateGridOrderSyncStore.activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER
    ? StateGridSyncDescription
    : ShangHaiIOTDescription;
});
</script>

<style scoped lang="scss">
:deep(.descriptions) {
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-primary);

  .description-item {
    .label {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-secondary);
      padding-right: 8px;
    }

    .content {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-primary);
    }
  }
}

:deep(.title) {
  @apply text-lg mb-2;

  .label {
    padding-right: 12px;
  }
}
</style>
