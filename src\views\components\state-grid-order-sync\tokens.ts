import { IPagingReq } from "@/models";
import { ComputedRef, InjectionKey } from "vue";

interface IStateGridOrderSyncEdit {
  editData?: {
    id: string;
    /** 被编辑数据的编号，为保存并重新同步的消息提示而保存 */
    no?: string;
  };
  syncFn?: (dataId: string, no: string) => Promise<any>;
  refreshFn: (pageInfo?: IPagingReq) => any;
}

export const stateGridOrderSyncEditKey: InjectionKey<IStateGridOrderSyncEdit> = Symbol("state grid order sync");

export interface IInspectProvide {
  detailMaterialCategory?: string;
  isArmourClamp?: boolean;
  stepKey?: ComputedRef;
}
/** provide token */
export const PROVIDE_PROCESS_INSPECT_TOKEN: InjectionKey<IInspectProvide> = Symbol("prodArmourClamp");
