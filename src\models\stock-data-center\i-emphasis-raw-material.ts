import { IPagingReq } from "../i-paging-req";

/** 重点原材料下拉框 */
export interface IKeyRawMaterialOptions {
  createTime?: string;
  updateTime?: string;
  creator?: string;
  updater?: string;
  deleted?: true;
  id?: number;
  categoryCode?: string;
  categoryName?: string;
  subClassCode?: string;
  subClassName?: string;
  keyMaterialCode: string;
  keyMaterialName: string;
}

/** 查询条件 */
export interface ISearchEmphasisRawMaterial extends IPagingReq {
  processCode?: string;
  modelCode?: string;
  storageTime?: string[];
}

/** 重点原材料详情 */
export interface IEmphasisRawmaterialList {
  id?: string;
  categoryCode: string; // 品类编码
  categoryName: string; // 品类名称
  categorySubClassCode: string; // 物资种类编码
  categorySubClassName: string; // 物资种类名称
  materialName: string; // 原材料名称
  processCode: string; // 原材料类型
  processName: string; // 原材料类型名称
  inventory: number; // 原材料库存数量
  partUnit: string; // 计量单位
  modelCode: string; // 规格型号
  voltageGrade: string; // 电压等级
  oorMaterials: string; // 原材料产地
  supplierName: string; // 原材料供应商
  storageTime: string; // 入库时间
  locationCity: string; // 存放地点所在市
  dayUsed?: number; // 预计每日使用量
  inventoryTime?: string; // 盘库时间
  syncResult?: string; // 同步状态
  remarks: string; // 备注
}

/** 新增重点原材料 */
export interface IEmphasisRawmaterialReq extends IEmphasisRawmaterialList {
  others?: string;
}
