import { ISalesOrderLine } from "@/models";
import { VoltageClassesEnum } from "@/enums";

type createKeys =
  | "salesId"
  | "soNo"
  | "buyerName"
  | "subClassCode"
  | "materialCode"
  | "materialName"
  | "materialUnit"
  | "materialNumber"
  | "materialDesc"
  | "specificationType";

export interface ICreateSalesOrderLineDto extends Partial<Pick<ISalesOrderLine, createKeys>> {
  purchaseId: string;
  purchaseLineId: Array<string>;
  materialId: string;
  voltageLevel?: VoltageClassesEnum;
}

export const UPDATE_SALES_ORDER_LINE_PROPERTIES = [
  "id",
  "materialNumber",
  "materialCode",
  "materialName",
  "materialUnit",
  "specificationType",
  "voltageLevel",
  "prjCode",
  "productionWorkNo",
  "entityId"
] as const;

export type IUpdateSalesOrderLineDto = Partial<
  Pick<ISalesOrderLine, (typeof UPDATE_SALES_ORDER_LINE_PROPERTIES)[number]>
>;

export interface ISalesOrderLineForm {
  id?: string;
  soItemNo: string;
  subClassCode: string;
  materialCode: string;
  materialName: string;
  materialDesc: string;
  materialUnit: string;
  materialNumber: number;
  materialId: string;
  specificationType?: string;
  voltageLevel?: VoltageClassesEnum;
  prjCode?: string;
  productionWorkNo?: string;
  entityId?: string;
}
