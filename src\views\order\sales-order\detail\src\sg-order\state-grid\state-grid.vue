<!-- 销售订单-国网同步详情 -->
<template>
  <div class="relative w-full h-full overflow-hidden">
    <el-scrollbar v-if="syncs?.length || loadingReal" class="w-full flex-1">
      <div class="order-list flex flex-col gap-5 py-1">
        <LoadingSkeleton :loading="loadingReal" :count="count">
          <item v-for="sync in syncs" :key="sync.id" :sync="sync" />
        </LoadingSkeleton>
      </div>
      <el-dialog
        :title="detailStore.title"
        v-model="visible"
        fullscreen
        destroy-on-close
        :close-on-press-escape="false"
        class="state-grid-order-sync-detail-dialog"
        :show-close="false"
      >
        <template #header="{ close, titleId, titleClass }">
          <div :id="titleId" class="flex justify-between">
            <div class="flex gap-3 items-center">
              <el-button link @click="close">
                <el-icon size="20"><Back /></el-icon>
              </el-button>
              <div :class="titleClass" v-if="detailStore.title">{{ detailStore.title }}</div>
            </div>
            <el-button type="danger" @click="close">
              <el-icon size="20"><Close /></el-icon>
            </el-button>
          </div>
        </template>
        <Detail />
      </el-dialog>
    </el-scrollbar>
    <template v-else>
      <EmptyDataDisplay />
    </template>
  </div>
</template>

<script setup lang="ts">
import LoadingSkeleton from "@/views/order/sales-order/detail/src/sg-order/list/loading-skeleton.vue";
import Item from "@/views/order/sales-order/detail/src/sg-order/list/item.vue";
import Detail from "@/views/order/sales-order/detail/src/sg-order/detail/detail.vue";
import { useSalesOrderDetailStore, useSalesOrderSyncInfo, useSalesStateGridOrderSyncListStore } from "@/store/modules";
import { computed, onMounted, ref } from "vue";
import { useSocket } from "@/utils/useSocket";
import { RefreshSceneEnum, SocketEventEnum } from "@/enums";
import { useThrottleFn } from "@vueuse/core";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import EmptyDataDisplay from "@/views/order/components/empty-data-display/index.vue";
import { isOrderLineDataType } from "../sync-step-tool";
import { Close, Back } from "@element-plus/icons-vue";

const listStore = useSalesStateGridOrderSyncListStore();
const detailStore = useSalesOrderSyncInfo();
const salesDetailStore = useSalesOrderDetailStore();
const socket = useSocket();

const handleRefresh = useThrottleFn(refresh, REFRESH_SYNC_DATA_PERIOD, true);

const loadingReal = ref(false);
const count = computed(() => salesDetailStore.salesOrder?.purchaseLineCount);
const syncs = computed(() => listStore.syncs);
const visible = computed({
  get() {
    return detailStore.dialogVisible;
  },
  set(value) {
    detailStore.$patch({ dialogVisible: value });
  }
});

onMounted(() => {
  loadingReal.value = true;
  listStore.setSalesOrderId(salesDetailStore.saleOrderId);
  handleRefresh();
  addRefreshEventListener();
});

async function refresh() {
  await listStore.refreshSalesOrderSyncList();
  loadingReal.value = false;
}

function addRefreshEventListener() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, dataType, purchaseOrderItemId } = event;
    const matching = listStore.syncs.find(item => item.id === purchaseOrderItemId);
    if (type !== RefreshSceneEnum.SYNC_DATA || !matching) return;
    if (isOrderLineDataType(dataType)) {
      handleRefresh();
    }
  });
}
</script>

<style scoped lang="scss"></style>
