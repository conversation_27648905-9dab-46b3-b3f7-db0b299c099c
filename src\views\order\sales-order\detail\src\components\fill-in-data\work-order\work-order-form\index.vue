<template>
  <el-form
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row>
      <el-col :span="24">
        <div class="panel">
          <div class="flex items-center gap-2 mb-2">
            <FontIcon icon="icon-classify-fill" class="text-primary text-middle" />
            <div class="text-text_color_primary font-semibold text-middle">物料编号 {{ formValue.materialsCode }}</div>
          </div>
          <el-descriptions>
            <el-descriptions-item :span="2" label="物料名称">{{ formValue.materialName }} </el-descriptions-item>
            <el-descriptions-item label="物料单位">
              <DictionaryName
                :sub-class-code="formValue.subclassCode"
                :code="formValue.materialUnit"
                :parent-code="MEASURE_UNIT"
              />
            </el-descriptions-item>
            <el-descriptions-item :span="3" label="物料描述">{{ formValue.materialDesc }} </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工单编号" prop="woNo">
          <SerialNumber
            v-model="formValue.woNo"
            :code="PRODUCT_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :parent-no="parentNo"
            :create="props.type === 'create'"
            placeholder="请输入工单编号"
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工艺路线" prop="processRouteNo">
          <ProcessRoute
            v-model="formValue.processRouteNo"
            :subClassCode="subClassCode"
            @change="routeNoChange"
            @clearChange="clearChange"
            :disabled="fieldDisabled"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item class="flex-1 gap-y-10" label="工序" prop="processIds">
          <ProcessSelect
            v-model="formValue.processIds"
            :processOptions="processOptions"
            :isQuery="isQuery"
            :setDefaultAllCheck="setDefaultAllCheck"
            :subClassCode="subClassCode"
            :disabled="fieldDisabled"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="生产数量" prop="amount">
          <el-input-number
            :min="0"
            v-model="formValue.amount"
            controls-position="right"
            placeholder="请输入生产数量"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="unit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="props.subClassCode"
            class="w-full"
            placeholder="请选择计量单位"
            v-model="formValue.unit"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1" label="规格型号" prop="specificationType">
          <el-input v-model="formValue.specificationType" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="电压等级" prop="voltageLevel">
          <EnumSelect
            class="flex-1"
            v-model="formValue.voltageLevel"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="工单状态" prop="woStatus">
          <EnumSelect
            class="flex-1"
            v-model="formValue.woStatus"
            placeholder="请选择接工单状态"
            :enum="ProductionStateEnum"
            enumName="productionStateEnum"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1 gap-y-10" label="计划日期" prop="planDateArray">
          <el-date-picker
            v-model="formValue.planDateArray"
            type="daterange"
            label="计划日期"
            start-placeholder="请选择开始日期"
            end-placeholder="请选择结束日期"
            style="width: 100%"
            clearable
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item
          class="flex-1"
          label="实际开始日期"
          prop="actualStartDate"
          :rules="{
            required: actualStartDateRequired,
            message: '实际开始日期不能为空',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formValue.actualStartDate"
            label="实际开始日期"
            placeholder="请选择实际开始日期"
            class="flex-1"
            clearable
            :disabled-date="actualStartDateDisabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="flex-1" label="实际完成日期" prop="actualFinishDate">
          <el-date-picker
            v-model="formValue.actualFinishDate"
            label="实际完成日期"
            placeholder="请选择实际完成日期"
            class="flex-1"
            clearable
            :disabled-date="actualEndDateDisabledDate"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch, watchEffect, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ICreateWorkOrder, IProcessRouteList } from "@/models";
import { useSalesFillInDataStore, useWorkOrderStore } from "@/store/modules";
import EnumSelect from "@/components/EnumSelect";
import { ProductionStateEnum, VoltageClassesEnum } from "@/enums";
import { useProcessStore } from "@/store/modules";
import { PRODUCT_ORDER_NO_CODE, MEASURE_UNIT } from "@/consts";
import SerialNumber from "@/components/SerialNumber";
import Dictionary from "@/components/Dictionary";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";
import ProcessRoute from "@/views/order/components/process-route/process-route.vue";
import ProcessSelect from "@/views/order/components/process-select/process-select.vue";
import { voltageGardeRequireWorkOrder } from "@/views/order/utils";

interface ICreateWorkOrderExe extends ICreateWorkOrder {
  planDateArray?: any;
  subclassCode: string;
}

const props = defineProps<{
  type?: "create" | "update";
  parentNo?: string;
  subClassCode?: string;
}>();

const workOrderStore = useWorkOrderStore();
const processStore = useProcessStore();
const salesFillInDataStore = useSalesFillInDataStore();
// 工艺路线对应的工序
const subClassCode = computed(() => salesFillInDataStore.data?.subClassCode);
const processOptions = ref([]);
const isQuery = ref<boolean>(true);
const setDefaultAllCheck = ref<boolean>(false);
const voltageGradeRequire = computed(() => voltageGardeRequireWorkOrder(subClassCode.value));

const ruleFormRef = ref<FormInstance>();
const formValue = reactive<ICreateWorkOrderExe>({
  id: undefined,
  woNo: undefined,
  processRouteNo: null,
  processRouteId: undefined,
  amount: undefined,
  unit: undefined,
  materialsCode: undefined,
  materialName: undefined,
  materialDesc: undefined,
  materialUnit: undefined,
  specificationType: undefined,
  voltageLevel: undefined,
  woStatus: undefined,
  planStartDate: undefined,
  planFinishDate: undefined,
  actualStartDate: undefined,
  actualFinishDate: undefined,
  processIds: undefined,
  ipoNo: undefined,
  salesLineId: undefined,
  productionId: undefined,
  purchaseId: undefined,
  subclassCode: undefined,
  planDateArray: undefined
});

const rules = reactive<FormRules>({
  processIds: [{ required: true, message: "工序不能为空", trigger: "change" }],
  amount: [{ required: true, message: "生产数量不能为空", trigger: "blur" }],
  unit: [{ required: true, message: "计量单位", trigger: "blur" }],
  voltageLevel: [{ required: voltageGradeRequire.value, message: "电压等级不能为空", trigger: "change" }],
  specificationType: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  woStatus: [{ required: true, message: "工单状态不能为空", trigger: "change" }],
  planDateArray: [{ type: "array", required: true, message: "计划日期不能为空", trigger: "change" }]
});

const fieldDisabled = ref<boolean>();
const actualStartDateRequired = ref<boolean>();

watchEffect(() => {
  if (workOrderStore.createWorkOrderDetail && Object.keys(workOrderStore.createWorkOrderDetail).length) {
    setDefaultAllCheck.value = false;
    const { planStartDate, planFinishDate } = workOrderStore.createWorkOrderDetail;
    const planDate = [];
    if (planStartDate) {
      planDate.push(planStartDate);
    }
    if (planFinishDate) {
      planDate.push(planFinishDate);
    }
    Object.assign(formValue, workOrderStore.createWorkOrderDetail, {
      planDateArray: planDate
    });
  }

  if (workOrderStore.createWorkOrderDetail && !workOrderStore.createWorkOrderDetail.id) {
    setDefaultAllCheck.value = true;
  }

  fieldDisabled.value = !!workOrderStore.createWorkOrderDetail?.id;
});

watch(
  () => formValue.processRouteNo,
  newVal => {
    if (newVal) {
      const selectProcessRoute = processStore.processRouteOptions.find(item => item.code === newVal);
      formValue.processRouteId = selectProcessRoute.id;
      // 赋值工序信息
      if (selectProcessRoute.processInfos) {
        isQuery.value = false;
        formatProcessInfos(selectProcessRoute);
      }
    } else {
      isQuery.value = true;
    }
  }
);

onMounted(() => {
  watch(
    () => formValue.actualFinishDate,
    actualFinishDate => {
      if (!actualFinishDate) {
        actualStartDateRequired.value = false;
        ruleFormRef.value.clearValidate(["actualStartDate"]);
        return;
      }
      actualStartDateRequired.value = true;
    },
    { immediate: true }
  );
});

const parentNo = computed(() => props.parentNo);

const resetFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields();
};

const getFormValue = async (): Promise<boolean | ICreateWorkOrder> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});

  if (!valid) {
    return valid;
  }
  const { planDateArray, ...otherValue } = formValue;
  if (planDateArray) {
    return {
      ...otherValue,
      planStartDate: planDateArray[0],
      planFinishDate: planDateArray[1]
    };
  }
  return otherValue;
};

const actualStartDateDisabledDate = (date: Date) => {
  if (date > new Date()) {
    return true;
  }
  return formValue.actualFinishDate
    ? date >
        (typeof formValue.actualFinishDate === "string"
          ? new Date(formValue.actualFinishDate)
          : formValue.actualFinishDate)
    : false;
};

const actualEndDateDisabledDate = (date: Date) => {
  if (date > new Date()) {
    return true;
  }

  return formValue.actualStartDate
    ? date <
        (typeof formValue.actualStartDate === "string"
          ? new Date(formValue.actualStartDate)
          : formValue.actualStartDate)
    : false;
};

/** 清除工艺路线 */
async function clearChange() {
  resetProcess();
}

/** 工艺路线改变 */
function routeNoChange(processRouteInfo: IProcessRouteList) {
  // 清空工序信息
  resetProcess();
  formatProcessInfos(processRouteInfo);
}

function resetProcess() {
  formValue.processIds = undefined;
}

function formatProcessInfos(processRouteInfo: IProcessRouteList) {
  processOptions.value = processRouteInfo?.processInfos.map(item => {
    return {
      label: item.processName,
      value: item.id
    };
  });
}

defineExpose({
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.panel {
  padding: 12px 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #dcdfe6;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.12);
}
</style>
