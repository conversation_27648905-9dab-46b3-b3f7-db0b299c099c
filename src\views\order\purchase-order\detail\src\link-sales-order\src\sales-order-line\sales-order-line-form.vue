<template>
  <el-form
    ref="formInstance"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
    :model="form"
    :rules="rules"
  >
    <div :id="DIALOG_ERROR_ID_SALES_ORDER_LINE" />
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="销售订单行项目号" prop="soItemNo">
          <SerialNumber
            v-model="form.soItemNo"
            :code="SALES_ORDER_NO_CODE"
            :dependence-parent-no="true"
            :parent-no="props.parentNo"
            :create="props.type === 'create'"
            placeholder="请输入销售订单行项目号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <SubclassSelect v-model="form.subClassCode" :category-code="props.categoryCode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="选择物料">
          <MaterialSelect
            :selectedId="form.materialId"
            :subClassCode="form.subClassCode"
            @material-change="materialChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input placeholder="请输入" disabled v-model="form.materialCode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料名称" prop="materialName">
          <el-input placeholder="请输入" disabled v-model="form.materialName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料描述" prop="materialDesc">
          <el-input placeholder="请输入" disabled v-model="form.materialDesc" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料单位" prop="materialUnit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="form.subClassCode"
            class="w-full"
            v-model="form.materialUnit"
            placeholder="请选择"
            disabled
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料数量" prop="materialNumber">
          <el-input-number
            class="!w-full"
            placeholder="请输入"
            v-model="form.materialNumber"
            :min="0"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationType">
          <el-input placeholder="请输入" v-model="form.specificationType" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageLevel">
          <EnumSelect
            class="w-full"
            v-model="form.voltageLevel"
            placeholder="请选择"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目号" prop="prjCode">
          <el-input placeholder="请输入 项目号" v-model="form.prjCode" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产工号" prop="productionWorkNo">
          <el-input placeholder="请输入 生产工号" v-model="form.productionWorkNo" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实物ID" prop="entityId">
          <el-input placeholder="请输入 实物ID" v-model="form.entityId" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IMaterial, ISalesOrderLineForm } from "@/models";
import { VoltageClassesEnum } from "@/enums";
import EnumSelect from "@/components/EnumSelect";
import MaterialSelect from "@/views/components/material-select";
import SerialNumber from "@/components/SerialNumber";
import { SALES_ORDER_NO_CODE, MEASURE_UNIT, DIALOG_ERROR_ID_SALES_ORDER_LINE } from "@/consts";
import Dictionary from "@/components/Dictionary";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";

const props = defineProps<{
  type: "create" | "update";
  categoryCode: string;
  parentNo?: string;
}>();

defineExpose({
  getValue,
  initializeForm
});

const formInstance = ref<FormInstance>();
const form = ref<Partial<ISalesOrderLineForm>>({
  subClassCode: ""
});
const rules: FormRules = {
  soItemNo: [
    {
      required: true,
      message: requiredMessage("销售订单行项目号"),
      trigger: "change"
    }
  ],
  subClassCode: [
    {
      required: true,
      message: requiredMessage("物资种类"),
      trigger: "change"
    }
  ],
  materialCode: [
    {
      required: true,
      message: requiredMessage("物料编码"),
      trigger: "change"
    }
  ],
  materialName: [
    {
      required: true,
      message: requiredMessage("物料名称"),
      trigger: "change"
    }
  ],
  materialDesc: [
    {
      required: true,
      message: requiredMessage("物料描述"),
      trigger: "change"
    }
  ],
  materialUnit: [
    {
      required: true,
      message: requiredMessage("物料单位"),
      trigger: "change"
    }
  ],
  materialNumber: [
    {
      required: true,
      message: requiredMessage("物料数量"),
      trigger: "change"
    }
  ]
};

function initializeForm(value: Partial<ISalesOrderLineForm>) {
  if (value) {
    form.value = { ...value };
  }
}

async function getValue(): Promise<ISalesOrderLineForm> {
  const valid = await formInstance.value.validate(() => {});
  if (!valid) {
    return Promise.reject();
  }
  return form.value as ISalesOrderLineForm;
}

function materialChange(material: IMaterial) {
  const { materialCode, materialDescribe, materialName, materialUnit, specificationModel, voltageClass, id } = material;
  form.value = {
    ...form.value,
    materialCode,
    materialName,
    materialUnit,
    materialId: id,
    specificationType: specificationModel,
    voltageLevel: voltageClass,
    materialDesc: materialDescribe
  };
}
</script>

<style scoped></style>
