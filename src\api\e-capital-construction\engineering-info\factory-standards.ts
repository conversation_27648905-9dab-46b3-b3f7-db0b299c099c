/**
 * @description: 出厂试验 参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { FactoryStandardModel, FactoryStandardReportModel } from "@/models";
/**
 * @description: 出厂试验 参数标准列表
 */
export const FactoryStandardInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<FactoryStandardModel>>>(
    withApiGateway(`admin-api/ejj/project/experiment/param-std/list/${id}`)
  );
};

/**
 * @description: 出厂试验 参数标准创建
 */
export const FactoryStandardCreateApi = (params: FactoryStandardReportModel) => {
  return http.post<FactoryStandardReportModel, IResponse<Boolean>>(
    withApiGateway(`admin-api/ejj/project/experiment/param-std`),
    {
      data: params
    }
  );
};

/**
 * @description: 出厂试验 参数标准编辑
 */
export const FactoryStandardEditApi = (params: FactoryStandardReportModel) => {
  return http.put<FactoryStandardReportModel, IResponse<Boolean>>(
    withApiGateway(`admin-api/ejj/project/experiment/param-std`),
    {
      data: params
    }
  );
};

/**
 * @description: 出厂试验 参数标准删除
 */
export const FactoryStandardDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/experiment/param-std/${id}`));
};
