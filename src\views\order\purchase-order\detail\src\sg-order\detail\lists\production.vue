<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditProductionOrderDialog
    :orderType="OrderType.PURCHASE"
    :orderId="purchaseOrderStore.purchaseOrderId"
    ref="editDialog"
  />
  <DetailProductionOrderDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { OrderType, StateGridOrderSyncType, TableWidth } from "@/enums";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import BaseList from "./base-list.vue";
import { usePurchaseOrderDetailStore, useStateGridOrderSyncDetailListStore } from "@/store/modules";
import { IProductionOrderSync } from "@/models";
import { useSync } from "@/views/order/purchase-order/detail/src/sg-order/detail/lists/hooks/useSync";
import EditProductionOrderDialog from "@/views/components/state-grid-order-sync/dialogs/edit-production-order-dialog.vue";
import { provide, reactive } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import DetailProductionOrderDialog from "@/views/components/state-grid-order-sync/dialogs/detail-production-order-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";

const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditProductionOrderDialog>,
  InstanceType<typeof DetailProductionOrderDialog>,
  IProductionOrderSync
>();

const type = StateGridOrderSyncType.PRODUCTION_ORDER;
const { sync, syncByDataId, getHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getHistoryByDataId,
  editFn: openEditDialog
});
const listStore = useStateGridOrderSyncDetailListStore();
listStore.setType(type);
const purchaseOrderStore = usePurchaseOrderDetailStore();

const columns: Array<TableColumns> = [
  {
    label: "销售订单明细",
    prop: "soItemNo",
    minWidth: TableWidth.suborder
  },
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order,
    formatter: linkFormatter(openDetailDialog)
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);
</script>

<style scoped></style>
