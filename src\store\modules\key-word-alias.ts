import { defineStore } from "pinia";
import { IKeyWordAlias } from "@/models";
import * as api from "@/api/key-word-alias";

export const useKeyWordAliasStore = defineStore({
  id: "cx-key-word-alias",
  state: () => ({
    keyWordAlias: [] as Array<IKeyWordAlias>
  }),

  getters: {
    getKeyWordAlias: async state => {
      if (!Array.isArray(state.keyWordAlias) || state.keyWordAlias.length === 0) {
        const { data } = await api.queryKeyWordAlias();
        state.keyWordAlias = data;
      }
      return state.keyWordAlias;
    }
  },

  actions: {
    async queryKeyWordAlias(): Promise<void> {
      const { data } = await api.queryKeyWordAlias();
      this.keyWordAlias = data;
    }
  }
});
