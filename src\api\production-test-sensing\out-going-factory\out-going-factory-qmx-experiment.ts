import { http } from "@/utils/http";
import { withApiGateway } from "../../util";
import {
  IListResponse,
  IOutGoingFactoryNotJfnyReq,
  IOutGoingFactoryQMXExperimentForm,
  IOutGoingFactoryQmxExperiment,
  IResponse
} from "@/models";
import { ICopyFromOrder } from "@/models/raw-material/i-raw-material-res";

/** 根据生产订单id 获取出厂试验气密性试验详情 */
export const queryOutGoingFactoryQMXExperimentList = (params: IOutGoingFactoryNotJfnyReq) => {
  return http.post<IOutGoingFactoryNotJfnyReq, IListResponse<IOutGoingFactoryQmxExperiment>>(
    withApiGateway(`admin-api/business/outgoing/experiment/qmxsy/page`),
    {
      data: params
    }
  );
};

/** 根据工单id 获取非线缆出厂试验气密性试验详情 */
export const queryNonCableOutGoingFactoryQMXExperimentList = (params: IOutGoingFactoryNotJfnyReq) => {
  return http.post<IOutGoingFactoryNotJfnyReq, IListResponse<IOutGoingFactoryQmxExperiment>>(
    withApiGateway(`admin-api/business/outgoing/experiment/qmxsy/non-cable/page`),
    {
      data: params
    }
  );
};

/** 根据id 获取出厂试验气密性试验详情 */
export const getOutGoingFactoryQMXExperimentDetailById = (id: string) => {
  return http.get<void, IResponse<IOutGoingFactoryQmxExperiment>>(
    withApiGateway(`admin-api/business/outgoing/experiment/qmxsy/${id}`)
  );
};

/** 删除气密性试验根据Id */
export const deleteOutGoingFactoryQMXExperimentDetail = (id: string) => {
  return http.delete<void, IResponse<boolean>>(withApiGateway(`admin-api/business/outgoing/experiment/qmxsy/${id}`));
};

/**创建气密性试验 */
const createOutGoingFactoryQMXExperiment = (data: IOutGoingFactoryQMXExperimentForm) => {
  return http.post<IOutGoingFactoryQMXExperimentForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/outgoing/experiment/qmxsy/create"),
    { data },
    { showErrorInDialog: true }
  );
};

const editOutGoingFactoryQMXExperiment = (data: IOutGoingFactoryQMXExperimentForm) => {
  return http.post<IOutGoingFactoryQMXExperimentForm, IResponse<IOutGoingFactoryQmxExperiment>>(
    withApiGateway("admin-api/business/outgoing/experiment/qmxsy/update"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 从订（工）单中复制出厂试验 */
const copyOutFactoryExperimentFromOrder = (paramData: ICopyFromOrder) => {
  const url: string = withApiGateway(`admin-api/business/outgoing/copy`);
  return http.post<ICopyFromOrder, IResponse<Boolean>>(url, { data: paramData });
};

export const outGoingFactoryQMXExperimentApi = {
  queryOutGoingFactoryQMXExperimentList,
  queryNonCableOutGoingFactoryQMXExperimentList,
  getOutGoingFactoryQMXExperimentDetailById,
  deleteOutGoingFactoryQMXExperimentDetail,
  createOutGoingFactoryQMXExperiment,
  editOutGoingFactoryQMXExperiment,
  copyOutFactoryExperimentFromOrder
};
