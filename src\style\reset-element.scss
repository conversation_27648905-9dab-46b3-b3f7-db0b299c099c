@import "./mixin";

:root {
  // --el-color-primary: #00b678;
  // --el-color-primary-dark-1: #00a36c;
  // --el-color-primary-dark-2: #009260;
  // --el-color-primary-light-1: #19bd85;
  // --el-color-primary-light-2: #33c493;
  // --el-color-primary-light-3: #4ccca0;
  // --el-color-primary-light-4: #66d3ae;
  // --el-color-primary-light-5: #80dabb;
  // --el-color-primary-light-6: #99e2c9;
  // --el-color-primary-light-7: #b2e9d6;
  // --el-color-primary-light-8: #ccf0e4;
  // --el-color-primary-light-9: #e5f8f1;

  --color-neutral: #409eff;
  --color-neutral-light-8: #d9ecff;
  --color-neutral-light-9: #ecf5ff;

  --color-amber: #ffc300;
  --color-amber-light-8: #fff3cc;
  --color-amber-light-9: #fff9e5;

  --color-cyan: #00b578;
  --color-cyan-light-8: #ccf0e4;
  --color-cyan-light-9: #e5f8f1;

  --fullscreen-bg: #f4f8f9;
  --default-value: "--";

  --disabled-text-color: #c0c4cc;

  --border-color-light: rgba(0, 0, 0, 0.04);
}

/* empty时不需要default值 */
.no-default {
  --default-value: "";
}

/* 自定义element样式 */

.el-avatar {
  --el-avatar-bg-color: var(--el-color-info-light-9) !important;
}

// ----------el-button----------
.el-button {
  @apply font-medium;
  --el-text-color-regular: var(--el-text-color-primary);
}

// ----------el-collapse----------
.el-collapse {
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;

  .el-collapse-item__wrap {
    border-bottom-color: transparent !important;
  }

  .el-collapse-item__header {
    border-color: transparent !important;
  }

  .el-collapse-item__content {
    padding-bottom: 0;
  }
}

// ----------el-drawer----------
.el-drawer {
  .el-drawer__header {
    color: var(--el-text-color-primary) !important;
    margin: 0 !important;
    padding: 20px !important;
  }

  .el-drawer__body {
    padding: 20px 24px !important;
    background: var(--fullscreen-bg);
  }
}

// -----------dialog-----------
.el-dialog {
  .el-dialog__header {
    padding-bottom: var(--el-dialog-padding-primary);
  }
}

.el-dialog.is-fullscreen {
  & > .el-dialog__body {
    padding: var(--el-dialog-padding-primary);
  }
}

.el-dialog:not(.is-fullscreen) {
  & > .el-dialog__body {
    padding: 0 0 var(--el-dialog-padding-primary);
    margin: 0 var(--el-dialog-padding-primary);
    max-height: calc(100vh - 200px);
    overflow: overlay;
    @include scrollBar;
  }
}

.el-dialog.small {
  --el-dialog-width: 480px;
}

.el-dialog.small-md {
  --el-dialog-width: 540px;
}

.el-dialog.default {
  --el-dialog-width: 720px;
}

.el-dialog.middle {
  --el-dialog-width: 960px;
}

.el-dialog.large {
  --el-dialog-width: 1120px;
}

// -----------descriptions-----------
.el-descriptions {
  .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    width: 33.33%;

    &[colspan="2"] {
      width: 66.66%;
    }

    &[colspan="3"] {
      width: 100%;
    }
  }
}

.el-descriptions.half {
  .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    width: 50%;

    &[colspan="2"] {
      width: 100%;
    }
  }
}

.el-descriptions.quarter {
  .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    width: 25%;

    &[colspan="2"] {
      width: 50%;
    }

    &[colspan="3"] {
      width: 75%;
    }

    &[colspan="4"] {
      width: 100%;
    }
  }
}

.el-descriptions {
  .el-descriptions__label:not(.is-bordered-label) {
    color: var(--el-text-color-secondary);
    margin-right: 10px;
  }

  .el-descriptions__content:not(.is-bordered-label) {
    color: var(--el-text-color-primary);
  }

  .el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell {
    padding: 0;
    line-height: 2;
    vertical-align: top;
    display: inline-flex;

    .el-descriptions__label {
      display: inline-flex;
      align-items: baseline;
      text-align: start;
    }

    .el-descriptions__content {
      display: inline-flex;
      align-items: center;
      flex: 1;
      margin-right: 16px;
      word-break: break-word;
      overflow-wrap: break-word;
    }
  }

  .el-descriptions__content:empty:after {
    content: var(--default-value);
  }
}

// -----------el-empty----------
.el-empty__image svg {
  color: transparent !important;
}

// -----------el-form-----------
.el-form {
  // el-row gutter 动态生成margin 单位为px，为了完全匹配，这个单位使用PX，阻止rem转换
  /* prettier-ignore */
  padding: 0 20PX;

  &-item__label {
    --el-text-color-regular: var(--el-text-color-primary);
  }
}

// ----------el-input-number----------
.el-input-number .el-input__inner {
  text-align: left !important;
}

// ---------el-pagination----------
.el-pagination {
  --el-pagination-button-bg-color: transparent !important;
  --el-disabled-bg-color: transparent !important;
}

.el-pagination.is-background .el-pager li.is-active {
  color: var(--el-color-primary) !important;
  background-color: transparent !important;
}

.pure-pagination {
  margin: 16px 0 0 !important;
}

// ----------el-radio----------
.el-radio {
  margin-right: 12px !important;
}

.el-radio__inner::after {
  width: 5px !important;
  height: 5px !important;
}

.el-radio.is-bordered.is-checked {
  border-color: var(--el-color-primary-light-5) !important;
  background-color: var(--el-color-primary-light-9);
}

.el-radio.is-bordered {
  .el-radio__label {
    line-height: 1;
  }
}

.el-radio.is-bordered:hover {
  .el-radio__label {
    color: var(--el-color-primary);
  }

  .el-radio__inner {
    border-color: var(--el-color-primary);
  }
}

.el-radio-button.is-active {
  .el-radio-button__inner {
    color: var(--el-color-primary) !important;
    border-color: var(--el-color-primary-light-5) !important;
    background-color: var(--el-color-primary-light-9) !important;
    box-shadow: -1px 0 0 0 var(--el-color-primary-light-5) !important;
  }
}

// ----------el-select----------
.el-select-dropdown__item.hover {
  background-color: var(--el-color-primary-light-9) !important;
}

.el-select-dropdown__item.selected {
  font-weight: 400 !important;
}

// ----------el-steps----------
.el-steps.el-steps--horizontal {
  $arrowWidth: 3px;
  $arrowSize: 20px;
  padding: 0;

  .el-step {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1 !important;
    padding: 8px $arrowSize 8px 0;
    background-color: var(--el-color-info-light-9);

    .el-step__head {
      width: auto;
      margin-left: 25px;
      margin-right: 8px;

      .el-step__line {
        display: none;
      }

      .el-step__icon.is-text {
        border: none;
        color: var(--el-text-color-regular);
      }

      .el-step__icon-inner {
        font-weight: normal;
      }
    }

    .el-step__main {
      min-height: 24px;
    }

    .el-step__title {
      line-height: 1.5;
      color: var(--el-text-color-regular) !important;
    }
  }

  .el-step:nth-of-type(1) {
    clip-path: polygon(0 0, calc(100% - $arrowSize) 0, 100% 50%, calc(100% - $arrowSize) 100%, 0 100%);
  }

  .el-step + .el-step {
    margin-left: -$arrowSize;
    padding-left: $arrowSize;
    clip-path: polygon(
      $arrowWidth 0,
      calc(100% - $arrowSize) 0,
      100% 50%,
      calc(100% - $arrowSize) 100%,
      $arrowWidth 100%,
      $arrowWidth + $arrowSize 50%
    );
  }

  .el-step:last-of-type {
    clip-path: polygon(
      $arrowWidth 0,
      100% 0,
      100% 100%,
      calc(100% - $arrowSize) 100%,
      $arrowWidth 100%,
      $arrowWidth + $arrowSize 50%
    );
  }

  .el-step:has(.is-process, .is-finish) {
    background: linear-gradient(90deg, var(--el-color-primary-light-3) 0%, var(--el-color-primary) 97%);

    .el-step__icon.is-text {
      color: var(--el-color-primary);
    }

    .el-step__title {
      font-weight: 400;
      color: var(--el-color-white) !important;
    }

    .el-step__description {
      color: var(--el-color-white) !important;
    }
  }
}

// -----------el-table-----------
.el-table {
  --el-table-header-bg-color: var(--el-fill-color-light) !important;
  --el-table-header-text-color: var(--el-text-color-regular) !important;
  --el-table-row-hover-bg-color: var(--el-color-primary-light-9) !important;
  --el-table-text-color: var(--el-text-color-primary) !important;
}

.el-table:not(.el-table--border) {
  .el-table__inner-wrapper::before {
    background-color: transparent !important;
  }
}

.el-table--border th.el-table__cell {
  border-top: var(--el-table-border);
}

.el-table {
  .el-checkbox,
  .el-radio {
    height: unset;
  }
}

.el-table__empty-text {
  line-height: 22px !important;
}

.el-table thead tr th {
  @apply font-medium;
}

.el-table thead {
  .el-table-column--selection {
    font-size: 0;
  }
}

// ---------el-tag----------
.el-tag {
  &.el-tag--large {
    --el-tag-font-size: 14px;
  }

  &.el-tag--neutral {
    color: var(--color-neutral);
    border-color: var(--color-neutral-light-8);
    background-color: var(--color-neutral-light-9);
  }

  &.el-tag--amber {
    color: var(--color-amber);
    border-color: var(--color-amber-light-8);
    background-color: var(--color-amber-light-9);
  }

  &.el-tag--cyan {
    color: var(--color-cyan);
    border-color: var(--color-cyan-light-8);
    background-color: var(--color-cyan-light-9);
  }

  &.el-tag--inactive {
    color: var(--el-text-color-regular);
    border-color: var(--el-border-color);
    background-color: var(--el-color-white);
  }

  &.el-tag--custom {
    i {
      color: var(--el-text-color-secondary);
    }

    color: var(--el-text-color-primary);
    border-color: var(--el-color-info-light-8);
    background-color: var(--el-color-info-light-9);
  }

  .iconfont {
    font-size: 14px;
  }
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}

// ---------el-message-box----------
.el-message-box {
  .el-message-box__title {
    @apply text-primaryText font-semibold;
  }

  .el-message-box__status {
    top: 0;
    transform: translateY(0);
  }
}

// ---------el-notification----------
.el-notification {
  &__content {
    word-break: break-all;
  }
}

// ----------label----------
label {
  font-weight: normal !important;
}

.search-btn {
  min-width: 160px;
}

// ---------- .el-popover -----------
.el-popover.el-popper {
  min-width: 0 !important;
}
