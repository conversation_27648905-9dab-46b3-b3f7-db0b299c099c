import { ColumnWidth } from "@/enums";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig: TableColumnList = [
    {
      label: "",
      prop: "radio",
      slot: "radio",
      width: ColumnWidth.Char2
    },
    {
      label: "设备唯一编码",
      prop: "printerCode",
      minWidth: ColumnWidth.Char6
    },
    {
      label: "设备名称",
      prop: "printerName",
      width: ColumnWidth.Char10,
      slot: "enabled"
    },
    {
      label: "设备车间",
      prop: "workshop",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "设备产线",
      prop: "productionLine",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "设备图片",
      prop: "printerPhoto",
      minWidth: ColumnWidth.Char5,
      slot: "printerPhoto"
    }
  ];

  return { columnsConfig };
}
