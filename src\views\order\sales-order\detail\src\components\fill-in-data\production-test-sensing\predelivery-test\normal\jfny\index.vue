<template>
  <div class="h-full flex-1 flex flex-col jfny-experiment mt-2">
    <div class="operate flex justify-end">
      <!-- <div class="link-jfny-experiment ml-2">
        <el-button type="primary" @click="linkJfnyExperiment">关联局放耐压试验</el-button>
      </div> -->
    </div>

    <!-- 局方耐压列表 -->
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="exFactoryExperimentJFNYStore.exFactoryExperimentJfnyTableData"
      :columns="columns"
      showOverflowTooltip
      :height="300"
      :pagination="pagination"
      :loading="loading"
      @page-current-change="pageChange"
      @page-size-change="pageSizeChange"
    >
      <template #operate="{ row }">
        <div class="op-content">
          <EditJfnyExperimentDialog
            v-auth="PermissionKey.factoryTrial.factoryTrialJfnyDuplication"
            :ijfny-experiment="row"
            :save-success="refreshExFactoryExperimentJfnyList"
          />
          <DuplicationJfnyExperimentDialog
            v-auth="PermissionKey.factoryTrial.factoryTrialJfnyDuplication"
            :init-data="genCopyData(row)"
            :group-id="row.groupId"
            :meas-unit="row.measUnit"
            @post-save-success="refreshExFactoryExperimentJfnyList"
          />

          <ElButton
            v-auth="PermissionKey.form.formPurchaseFactoryTrialBtnLink"
            link
            type="primary"
            @click="onUpLoadFile(row)"
            >上传报告</ElButton
          >
          <ElButton link type="primary" @click="onDetailInfo(row)">详情</ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseFactoryTrialBtnLink"
            link
            type="danger"
            @click="onDeleteTest(row)"
            >删除</ElButton
          >
        </div>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>

    <!-- 上传报告 -->
    <el-dialog
      v-model="uploadFileVisibleRef"
      class="default"
      title="上传文件"
      :destroy-on-close="true"
      @close="cancelChange"
    >
      <UploadReport ref="uploadReportRef" @uploadReportChange="uploadReportChange" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelChange">取消</el-button>
          <el-button type="primary" @click="saveUploadFile">开始上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 出厂过程检测详情 -->
    <el-dialog
      v-model="detailPressRefVisible"
      title="局放耐压试验"
      class="large"
      align-center
      :destroy-on-close="true"
      @close="cancelDetailChange"
    >
      <WithStandPressDetail :experimentId="currentExperimentInfo.id" />
    </el-dialog>

    <!-- 关联局放耐压试验 -->
    <el-dialog
      v-model="linkJfnyExperimentRef"
      title="关联局放耐压试验"
      class="middle"
      align-center
      :destroy-on-close="true"
      @close="cancelLinkChange"
    >
      <div class="link-content">
        <LinkJfnyExperiment :productionId="productionId" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelLinkChange">取消</el-button>
          <el-button type="primary" @click="linkExperiment" :disabled="!linkJfnyList.length" :loading="linkLoading"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import UploadReport from "../../../component/upload-report/index.vue";
import LinkJfnyExperiment from "../../component/link-jfny-experiment/index.vue";
import WithStandPressDetail from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/predelivery-test/normal/jfny-detail/index.vue";
import { ref, computed, onMounted, watchEffect } from "vue";
import { useColumns } from "./columns";
import { useExFactoryExperimentJFNYStore } from "@/store/modules/ex-factory-experiment";
import { ElButton, ElMessageBox, ElMessage } from "element-plus";
import { IJfnyExperimentList, IResponse, IJfnyExperimentListReq } from "@/models/";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { EExFactoryTest } from "../../../component/switch-tab/types";
import { createJfnyData } from "@/api/production-test-sensing/out-going-factory/out-going-factory-jfny";
import { PermissionKey } from "@/consts";
import { UploadFile } from "element-plus";
import { useSalesFillInDataStore } from "@/store/modules";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useMaterial } from "@/utils/material";
import EditJfnyExperimentDialog from "@/views/components/leave-factory/edit-jfny-experiment/edit-jfny-experiment-dialog.vue";
import DuplicationJfnyExperimentDialog from "@/views/components/leave-factory/duplication-jfny-experiment-dialog/index.vue";

const { isCableTerminalAdapter } = useMaterial();

// 表格列
const { pagination } = useTableConfig();
pagination.pageSize = 10;
// 表格数据
const exFactoryExperimentJFNYStore = useExFactoryExperimentJFNYStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const fillIndata = useSalesFillInDataStore();
const productionId = fillIndata.dataId;
const subClassCode = fillIndata.data.subclassCode || fillIndata.data.subClassCode;

const columns = computed(() => {
  const { cableTerminalAdapterColumns, cableOrDistributionWireGroundColumns } = useColumns();
  // "电缆中间接头”&“电缆终端"
  if (isCableTerminalAdapter(subClassCode)) {
    return cableTerminalAdapterColumns;
  }
  // 现在，按照要求，除了"电缆中间接头”&“电缆终端"之外的都先走"中高低压&配网导,地线"的配置
  return cableOrDistributionWireGroundColumns;
});

const emits = defineEmits<{
  (event: "delSuccess", component?: string): void;
  (event: "addDataSuccess", component?: string): void;
}>();

const currentExperimentInfo = ref();
// 上传报告
const uploadFileVisibleRef = ref(false);
const uploadReportRef = ref();
const uploadReportSource = ref(null);

// 关联局放耐压试验
const linkJfnyExperimentRef = ref(false);
const linkJfnyList = computed(() => exFactoryExperimentJFNYStore.linkCheckJfnyExperimentData);
const linkLoading = ref<boolean>(false);
// 试验详情
const detailPressRefVisible = ref(false);
const loading = ref<boolean>(false);
const initExFactoryExperimentJfnyList = useLoadingFn(getExFactoryExperimentJfnyList, loading);
const defaultPageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

watchEffect(() => {
  pagination.total = exFactoryExperimentJFNYStore.jfnyTableTotal;
});

onMounted(() => {
  initExFactoryExperimentJfnyList({ productionId, ...defaultPageInfo });
});

/**
 * 获取局方耐压试验列表
 */
async function getExFactoryExperimentJfnyList(params: IJfnyExperimentListReq) {
  await exFactoryExperimentJFNYStore.getExFactoryExperimentJfnyListAction(params);
}

/**
 * 页码改变
 */
const pageChange = (pageNo: number) => {
  initExFactoryExperimentJfnyList({
    productionId,
    pageNo,
    pageSize: pagination.pageSize
  });
};

/**
 * 页码数量改变
 */
const pageSizeChange = (pageSize: number) => {
  pagination.currentPage = 1;
  initExFactoryExperimentJfnyList({
    productionId,
    pageNo: 1,
    pageSize
  });
};

/**
 * 上传报告
 */
const onUpLoadFile = (row: IJfnyExperimentList) => {
  uploadFileVisibleRef.value = true;
  currentExperimentInfo.value = row;
};

// 关闭上传报告弹框
const cancelChange = () => {
  uploadFileVisibleRef.value = false;
  uploadReportSource.value = null;
};

/** 报告文件上传 */
const uploadReportChange = (report?: UploadFile) => {
  uploadReportSource.value = report;
};

// 保存上传报告
const saveUploadFile = async () => {
  if (!uploadReportSource.value) {
    ElMessage.warning("请上传文件");
    return;
  }
  const { raw } = uploadReportSource.value;
  const params = {
    path: uploadReportSource.value?.name,
    experimentId: currentExperimentInfo.value?.id,
    file: raw
  };
  await exFactoryExperimentJFNYStore.uploadReportOfExperiment(params);
  uploadSuccess();
};

/**
 * 上传成功
 */
const uploadSuccess = () => {
  cancelChange();
  ElMessage.success("上传文件成功");
  // 刷新列表
  refreshExFactoryExperimentJfnyList();
};

// 试验详情
/**
 * 详情数据
 */
const onDetailInfo = (row: IJfnyExperimentList) => {
  currentExperimentInfo.value = row;
  detailPressRefVisible.value = true;
};

/** 关闭详情数据弹框 */
const cancelDetailChange = () => {
  detailPressRefVisible.value = false;
};

/**
 * 删除数据
 */
const onDeleteTest = (row: IJfnyExperimentList) => {
  currentExperimentInfo.value = row;
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      const res: IResponse<number> = await exFactoryExperimentJFNYStore.delExFactoryJfnyData(row.id, productionId);
      if (!res.data) {
        ElMessage({ type: "warning", message: res.msg || "网络异常" });
        return;
      }
      ElMessage({ type: "success", message: "删除成功" });
      refreshExFactoryExperimentJfnyList();
      productionTestSensingStore.refreshProductionProcessStatus();
      emits("delSuccess", EExFactoryTest.JFNY);
    })
    .catch(() => {});
};

/**
 * 刷新列表
 */
const refreshExFactoryExperimentJfnyList = () => {
  initExFactoryExperimentJfnyList({ productionId, ...defaultPageInfo });
};

/**
 * 控制关联局方耐压试验弹框
 */
const linkJfnyExperiment = () => {
  linkJfnyExperimentRef.value = true;
};

/**
 * 取消关联
 */
const cancelLinkChange = () => {
  linkJfnyExperimentRef.value = false;
  exFactoryExperimentJFNYStore.linkCheckJfnyExperimentData = [];
};

/**
 * 关联试验
 */
const linkExperiment = async () => {
  const linkExperimentList = linkJfnyList.value.map(item => item.id);
  linkLoading.value = true;
  await exFactoryExperimentJFNYStore.linkProductionAndExperiment(productionId, linkExperimentList).finally(() => {
    linkLoading.value = false;
  });
  ElMessage.success("关联成功");
  cancelLinkChange();
  refreshExFactoryExperimentJfnyList();
  productionTestSensingStore.refreshProductionProcessStatus();
  emits("addDataSuccess", EExFactoryTest.JFNY);
};

/**
 * 模拟局放耐压数据
 */
const testJfnyData = async () => {
  const createRes = (await createJfnyData(productionId))?.data;
  if (createRes) {
    refreshExFactoryExperimentJfnyList();
    productionTestSensingStore.refreshProductionProcessStatus();
    emits("addDataSuccess", EExFactoryTest.JFNY);
  }
};

function genCopyData(rowData: IJfnyExperimentList) {
  return {
    account: rowData.account,
    productModel: rowData.productModel,
    reelNo: rowData.reelNo,
    specificationModel: rowData.specificationModel
  };
}

defineExpose({
  linkJfnyExperiment,
  testJfnyData
});
</script>

<style scoped lang="scss">
.jfny-experiment {
  .test-jfny-data,
  .link-jfny-experiment {
    text-align: right;
    margin-bottom: 20px;
  }
}
</style>
