import { fullDateFormat } from "@/consts";
import { TableWidth, SpeciTypeEnum, VoltageListEnum, PullStatusEnumMapDesc } from "@/enums";
import { EProjectModel, EquipmentJSONModel } from "@/models";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";

export function useColumns(equipmentJSON: EquipmentJSONModel) {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "工程ID",
      prop: "projectId",
      width: TableWidth.order
    },
    {
      label: "物资种类",
      prop: "equipmentCode",
      width: TableWidth.type,
      formatter: (row: EProjectModel) => `${equipmentJSON[row.equipmentCode]?.name}`
    },
    {
      label: "设备名称",
      prop: "equipmentName",
      minWidth: TableWidth.order,
      formatter: (row: EProjectModel) => `${SpeciTypeEnum[row.equipmentName]}`
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      minWidth: TableWidth.order,
      formatter: (row: EProjectModel) => `${VoltageListEnum[row.voltageLevel]}`
    },
    {
      label: "设备型号",
      prop: "equipmentModel",
      width: TableWidth.order
    },
    {
      label: "生产工号",
      prop: "factoryWorkNumber",
      width: TableWidth.name
    },
    {
      label: "实物ID",
      prop: "utcNum",
      width: TableWidth.name
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      width: TableWidth.dateTime,
      cellRenderer: (data: TableColumnRenderer) => {
        return PullStatusEnumMapDesc[data.row.pullStatus];
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columnsConfig };
}

/**
 * @description: 主体变/调补变类型
 */
export function Transformer() {
  return {
    1: "主体变",
    2: "调补变"
  };
}
