<template>
  <el-form
    ref="formRef"
    :model="formValue"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-form-item label="设备端采集点编号" prop="no">
      <el-input
        placeholder="请输入设备端采集点编号"
        v-model="formValue.no"
        :maxlength="InputLengthEnum.normal"
        clearable
      />
    </el-form-item>
    <el-form-item label="设备端采集点名称" prop="name">
      <el-input
        placeholder="请输入设备端采集点名称"
        v-model="formValue.name"
        :maxlength="InputLengthEnum.normal"
        clearable
      />
    </el-form-item>
    <el-form-item label="设备端计量单位" prop="unit">
      <el-input
        placeholder="采集点没有单位可填写“无”"
        v-model="formValue.unit"
        :maxlength="InputLengthEnum.normal"
        clearable
      />
    </el-form-item>
    <el-form-item label="码点采集频率(S)" prop="frequency">
      <div class="flex-bc w-full">
        <el-input-number
          v-model="formValue.frequency"
          :min="1"
          controls-position="right"
          class="!w-full flex-1 mr-2"
          placeholder="请输入码点采集频率"
        />
        <span class="inline-block">秒</span>
      </div>
    </el-form-item>
    <el-form-item label="排序" prop="orderNum">
      <div class="flex-bc w-full">
        <el-input-number
          v-model="formValue.orderNum"
          :min="0"
          controls-position="right"
          class="!w-full flex-1 mr-2"
          placeholder="请输入排序"
        />
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { IDeviceAcquisitionForm } from "@/models";
import { useDeviceAcquisitionStore } from "@/store/modules/device";
import { requiredMessage } from "@/utils/form";
import { FormInstance, FormRules } from "element-plus";
import { reactive, ref, watchEffect } from "vue";
import { InputLengthEnum } from "@/enums/input-length";

const formRef = ref<FormInstance>();
const formValue = reactive<IDeviceAcquisitionForm>({
  id: undefined,
  no: undefined,
  name: undefined,
  unit: undefined,
  deviceId: undefined,
  frequency: 5,
  orderNum: undefined
});

const rules: FormRules = {
  no: [{ required: true, message: requiredMessage("设备端采集点编号"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("设备端采集点名称"), trigger: "change" }],
  unit: [{ required: true, message: requiredMessage("设备端计量单位"), trigger: "change" }],
  frequency: [{ required: true, message: requiredMessage("码点采集频率"), trigger: "change" }]
};

const deviceAcquisitionStore = useDeviceAcquisitionStore();

watchEffect(() => {
  if (deviceAcquisitionStore.deviceAcquisitionDetail) {
    Object.assign(formValue, deviceAcquisitionStore.deviceAcquisitionDetail);
  }
});

const getFormValue = async (): Promise<boolean | IDeviceAcquisitionForm> => {
  if (!formRef.value) {
    return false;
  }

  const valid = await formRef.value.validate(() => {});
  if (!valid) {
    return valid;
  }
  return formValue;
};

defineExpose({
  getFormValue
});
</script>

<style scoped lang="scss"></style>
