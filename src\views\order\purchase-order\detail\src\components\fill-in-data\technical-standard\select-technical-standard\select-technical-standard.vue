<template>
  <el-dialog
    v-model="visible"
    title="选用技术标准"
    align-center
    draggable
    class="large"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="closedChange"
  >
    <div class="select-technical-standard flex flex-col">
      <div class="search w-1/2 flex items-center mb-3">
        <el-input class="!w-1/2 mr-3" v-model="standardName" clearable placeholder="请输入技术标准" />
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
      <PureTable
        ref="tableInstance"
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="stockTechnicalStandardData"
        :height="460"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        showOverflowTooltip
        @row-click="rowClick"
        @page-current-change="currentPageChange"
        @page-size-change="pageSizeChange"
      >
        <template #radioGroup="{ row }">
          <el-radio-group class="text-center" v-model="currentCheckId">
            <el-radio class="text-center" :label="row.id">{{ "" }}</el-radio>
          </el-radio-group>
        </template>
        <template #empty>
          <CxEmpty />
        </template>
      </PureTable>
    </div>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="saveLoading" :disabled="disabled" @click="saveStandard">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { computed, inject, onUnmounted, ref, watch } from "vue";
import { useColumns } from "./columns";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useFillInDataStore, useTechnicalStandardStore } from "@/store/modules";
import { useTableConfig } from "@/utils/useTableConfig";
import { ElMessage } from "element-plus";
import { technicalStandardKey } from "../tokens";
import { ITechnicalStandardStockReq } from "@/models";

const props = defineProps<{
  modelValue?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});

const fillInDataStore = useFillInDataStore();
const store = useTechnicalStandardStore();
const standardName = ref<string>();
const { pagination } = useTableConfig();
const { columns } = useColumns();
const tableInstance = ref<PureTableInstance>();
const stockTechnicalStandardData = ref([]);
const loading = ref<boolean>(false);
const initTechnicalStandardStockData = useLoadingFn(getTechnicalStandardStockData, loading);
const saveLoading = ref<boolean>(false);
const currentCheckId = ref();
const disabled = computed(() => {
  return !currentCheckId.value;
});
const saveStandard = useLoadingFn(postSelectStandard, saveLoading);
const ctx = inject(technicalStandardKey);
const defaultPageInfo = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize
};

watch(
  visible,
  newVal => {
    if (newVal) {
      initTechnicalStandardStockData(defaultPageInfo);
    }
  },
  {
    immediate: true
  }
);

/** 获取技术标准库数据 */
async function getTechnicalStandardStockData(params: ITechnicalStandardStockReq) {
  const res = (await store.getTechnicalStandardStock(fillInDataStore.dataId, params)).data;
  stockTechnicalStandardData.value = res.list || [];
  pagination.total = res.total || 0;
}

/** 搜索数据 */
function search() {
  initTechnicalStandardStockData({
    standardName: standardName.value,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}

/** 页码改变 */
function currentPageChange(pageNo: number) {
  initTechnicalStandardStockData({
    standardName: standardName.value,
    pageNo,
    pageSize: pagination.pageSize
  });
}

/** 页码数量改变 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  initTechnicalStandardStockData({
    standardName: standardName.value,
    pageNo: pagination.currentPage,
    pageSize
  });
}

/** 点击行 */
function rowClick(row) {
  currentCheckId.value = row.id;
}

/** 保存数据 */
async function postSelectStandard() {
  await store.saveTechnicalStandardStock({
    productionOrderId: fillInDataStore.dataId,
    standardId: currentCheckId.value
  });
  ElMessage.success("保存成功");
  visible.value = false;
  ctx.refresh();
}

/** 弹框关闭后 */
function closedChange() {
  currentCheckId.value = null;
}

onUnmounted(() => {
  currentCheckId.value = null;
});
</script>

<style scoped lang="scss"></style>
