import { TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { fullDateFormat } from "@/consts";
import { isBoolean } from "lodash-unified";
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "技术标准名称",
      prop: "standardName",
      fixed: "left",
      width: TableWidth.name
    },
    {
      label: "物资品类",
      prop: "categoryCode",
      slot: "categoryCode",
      minWidth: TableWidth.name
    },
    {
      label: "物资种类",
      prop: "subClassCode",
      slot: "subClassCode",
      minWidth: TableWidth.name
    },
    {
      label: "物料编号",
      prop: "materialCode",
      width: TableWidth.order
    },
    {
      label: "规格型号",
      prop: "specificationModel",
      width: TableWidth.name
    },
    {
      label: "状态",
      prop: "enable",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const enable: boolean | string = data.row.enable;
        return isBoolean(enable) ? (
          enable ? (
            <CxTag type="success">启用</CxTag>
          ) : (
            <CxTag type="danger">禁用</CxTag>
          )
        ) : null;
      }
    },
    {
      label: "是否通用标准",
      prop: "isStandard",
      minWidth: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const isStandard: boolean = data.row.isStandard;
        return isStandard ? <CxTag type="success">是</CxTag> : <CxTag type="danger">否</CxTag>;
      }
    },
    {
      label: "同步状态",
      prop: "syncResult",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const syncResult: boolean | string = data.row.syncResult;
        return isBoolean(syncResult) ? (
          syncResult ? (
            <CxTag type="success">已同步</CxTag>
          ) : (
            <CxTag type="danger">同步失败</CxTag>
          )
        ) : (
          <CxTag type="info">未同步</CxTag>
        );
      }
    },
    {
      label: "最新同步时间",
      prop: "lastSyncTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "同步日志",
      prop: "syncNote",
      width: TableWidth.largeXgOperation
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  return { columns };
}
