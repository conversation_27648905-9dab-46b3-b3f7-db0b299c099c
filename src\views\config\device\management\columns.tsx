import {
  FunctionTypeEnum,
  TableWidth,
  CollectionModeEnum,
  DeviceAcceptanceStatusMapName,
  DeviceAcceptanceStatusEnum,
  IOTSyncStatusEnum
} from "@/enums";
import { IDevice } from "@/models";
import { dateFormat, fullDateFormat } from "@/consts";
import { formatEnum } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { RouterLink } from "vue-router";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter, enumFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "设备编号",
      prop: "deviceCode",
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => (
        <RouterLink class="text-primary" to={{ name: "deviceManagementDetail", params: { id: data.row.id } }}>
          {data.row.deviceCode}
        </RouterLink>
      ),
      width: TableWidth.suborder
    },
    {
      label: "设备名称",
      prop: "deviceName",
      width: TableWidth.name
    },
    {
      label: "功能类型",
      prop: "functionType",
      formatter: (row: IDevice) => formatEnum(row.functionType, FunctionTypeEnum, "FunctionTypeEnum"),
      width: TableWidth.type,
      showOverflowTooltip: true
    },
    {
      label: "设备验收状态",
      prop: "deviceName",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {data.row.gwAcceptanceStatus ? (
              <CxTag class="mr-2 el-tag--cyan">
                {data.row.gwAcceptanceStatus
                  ? DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.GW_ACCEPTANCE_STATUS]
                  : null}
              </CxTag>
            ) : null}
            {data.row.iotAcceptanceStatus ? (
              <CxTag class="el-tag--neutral">
                {data.row.iotAcceptanceStatus
                  ? DeviceAcceptanceStatusMapName[DeviceAcceptanceStatusEnum.IOT_ACCEPTANCE_STATUS]
                  : null}
              </CxTag>
            ) : null}
            {data.row.gwAcceptanceStatus === false && data.row.iotAcceptanceStatus === false ? "--" : ""}
          </div>
        );
      },
      width: TableWidth.name,
      showOverflowTooltip: true
    },
    {
      label: "数采方式",
      prop: "collectionMode",
      formatter: (row: IDevice) => formatEnum(row.collectionMode, CollectionModeEnum, "CollectionModeEnum"),
      width: TableWidth.type,
      showOverflowTooltip: true
    },
    {
      label: "最后采集时间",
      prop: "lastCollectionTime",
      width: TableWidth.type,
      formatter: dateFormatter(),
      showOverflowTooltip: true
    },

    {
      label: "iot同步状态",
      prop: "iotSyncStatus",
      width: TableWidth.status,
      formatter: enumFormatter(IOTSyncStatusEnum, "IOTSyncStatusEnum"),
      showOverflowTooltip: true
    },
    {
      label: "同步日志",
      prop: "syncResult",
      width: TableWidth.reason,
      showOverflowTooltip: true
    },
    {
      label: "最近同步时间",
      prop: "lastSyncTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(),
      showOverflowTooltip: true
    },
    {
      label: "设备产地",
      prop: "deviceUnit",
      width: TableWidth.name,
      showOverflowTooltip: true
    },
    {
      label: "应用工序",
      prop: "processName",
      formatter: (row: IDevice) => (row.processName ? row.processName.join(",") : null),
      width: TableWidth.largeType,
      showOverflowTooltip: true
    },
    {
      label: "购入年月",
      prop: "purchaseTime",
      formatter: dateFormatter(dateFormat),
      width: TableWidth.date,
      showOverflowTooltip: true
    },
    {
      label: "规格型号",
      prop: "specificationModel",
      width: TableWidth.largeType,
      showOverflowTooltip: true
    },
    {
      label: "所属车间",
      prop: "workshopName",
      width: TableWidth.type,
      showOverflowTooltip: true
    },
    {
      label: "创建时间",
      prop: "createTime",
      formatter: dateFormatter(fullDateFormat),
      width: TableWidth.dateTime,
      showOverflowTooltip: true
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.largeMgOperation
    }
  ];
  return { columns };
}
