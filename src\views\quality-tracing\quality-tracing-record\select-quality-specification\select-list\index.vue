<template>
  <div class="overflow-hidden w-full flex flex-col">
    <search-bar @handle-search="handleSearch" />
    <!-- 表格 -->
    <PureTable
      ref="tableRef"
      row-key="id"
      :data="list"
      :columns="columnsConfig"
      size="large"
      :loading="loading"
      height="400px"
      showOverflowTooltip
      v-model:pagination="pagination"
      @page-current-change="requestList"
      @page-size-change="reloadList"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
      <template #radio="{ row }">
        <div class="flex justify-center items-center">
          <el-radio v-model="selectedId" size="large" :label="row.id" @change="handleRadioChange">&nbsp;</el-radio>
        </div>
      </template>
    </PureTable>
    <div class="flex h-6 items-center">
      <div v-if="selectedRow">
        已选择： <el-tag closable @close="selectedId = ''">{{ selectedRow.qualityName }}</el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQualitySpecificationList } from "@/api/quality-tracing";
import { PureTable } from "@pureadmin/table";
import { QualitySpecificationListParams, QualitySpecificationItem } from "@/models/quality-tracing";
import SearchBar from "./search-bar.vue";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";

/**
 * 为生产订单/工单 选择 质量规范
 */

const props = defineProps<{
  /** 物资种类 */
  subClassCode: string;
  /** 已选中的质量规范 */
  qualityId?: string;
}>();

const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<QualitySpecificationItem>>([]);
const searchForm = reactive({
  keyWords: ""
});
const selectedId = ref(props.qualityId || "");
const tableRef = ref<PureTableInstance>();

const selectedRow = computed<null | QualitySpecificationItem>(() => {
  if (!selectedId.value) {
    return null;
  }
  return list.value.find(item => item.id === selectedId.value);
});

/**
 * @description: 选择事件
 */
function handleRadioChange(id: string) {
  selectedId.value = id;
}

/**
 * @description: searchbar搜索
 */
function handleSearch(form: { keyWords: string }) {
  searchForm.keyWords = form.keyWords;
  reloadList();
}

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: QualitySpecificationListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    subClassCode: props.subClassCode,
    ...searchForm
  };
  const { data } = await queryQualitySpecificationList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(requestList);

defineExpose({
  getSelectedId: () => selectedId.value
});
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper) {
  .el-checkbox {
    display: none;
  }
}
</style>
