import { linkStepOption } from "@/consts";
import { SalesLinkStepEnumAlias } from "@/enums";
import {
  IOption,
  ISalesLinkPurchaseOrder,
  ISalesLinkPurchaseOrderParams,
  ISalesOrder,
  ISalesOrderDetail,
  ISalesOrderParams
} from "@/models";
import { useSalesOrderManagementStore, useUserTenantStore } from "@/store/modules";
import { formatDecimal } from "@/utils/format";
import { includes, omit } from "lodash-unified";

/**
 * 销售订单的hook
 */
export const useSalesOrderHook = () => {
  const salesOrderManagementStore = useSalesOrderManagementStore();
  const userTenantStore = useUserTenantStore();
  const StepsValue = salesOrderManagementStore.stepValue;

  // 获取销售订单的列表数据和统计信息
  async function querySalesOrders(params: ISalesOrderParams) {
    const tableData: Array<ISalesOrder> = await salesOrderManagementStore.querySalesOrders(params);
    await salesOrderManagementStore.queryStepValue(omit(params, "linkStep"));
    await salesOrderManagementStore.querySalesOrderStatistic(omit(params, "linkStep"));
    return tableData;
  }

  // 删除销售订单
  async function deleteSalesOrdersById(id: string) {
    await salesOrderManagementStore.deleteSalesOrdersById(id);
  }

  // 创建销售订单
  async function ceateSalesOrder(salesOrderDetail: ISalesOrderDetail) {
    await salesOrderManagementStore.ceateSalesOrder(salesOrderDetail);
  }

  // 编辑销售订单
  async function updateSalesOrdersById(salesOrderDetail: ISalesOrderDetail) {
    await salesOrderManagementStore.updateSalesOrdersById(salesOrderDetail);
  }

  // 查询关联的采购订单列表
  async function querySalesLinkPurchaseOrders(params: ISalesLinkPurchaseOrderParams) {
    const tableData: Array<ISalesLinkPurchaseOrder> = await salesOrderManagementStore.querySalesLinkPurchaseOrders(
      params
    );
    return tableData;
  }

  // 销售订单行详情赋值
  function setSalesOrderDetail(saleOrderDetail: ISalesOrder) {
    salesOrderManagementStore.setSalesOrderDetailStorage(saleOrderDetail);
  }
  // 销售订单行详情清空
  function clearSalesOrderDetail() {
    salesOrderManagementStore.clearSalesOrderDetailStorage();
  }

  const getFaultPoNoCount = () => {
    const count: number = salesOrderManagementStore.faultPoNoCount;
    return `无采购订单号（${count}条）`;
  };

  const getFollowCount = () => {
    const count: number = salesOrderManagementStore.followCount;
    return `已关注（${count}条）`;
  };

  /** 获取归档数量 */
  const getdocumentationCount = () => {
    const count: number = salesOrderManagementStore.documentationCount;
    return `已归档（${count}条）`;
  };

  const getStepValue = (linkStep: string | number) => {
    // 取全部数据
    if (!linkStep) {
      return;
    }
    return `(${formatDecimal(Number(StepsValue[linkStep])) || 0})`;
  };
  const getSupportSalesLinkSteps = () => {
    const supportSalesLinkSteps: Array<SalesLinkStepEnumAlias> = userTenantStore.supportSalesLinkSteps;
    const option: Array<IOption> =
      linkStepOption.filter(linkStep => includes(supportSalesLinkSteps, linkStep.value)) || [];
    return option.length
      ? [{ label: "salesOrder.enum.linkStep.all", value: "", linkStepKey: undefined }, ...option]
      : option;
  };

  // 关注销售订单
  const salesOrderFollow = async (id: string) => {
    await salesOrderManagementStore.salesOrderFollow(id);
  };

  return {
    querySalesOrders,
    getFaultPoNoCount,
    getFollowCount,
    getSupportSalesLinkSteps,
    getStepValue,
    deleteSalesOrdersById,
    ceateSalesOrder,
    querySalesLinkPurchaseOrders,
    updateSalesOrdersById,
    setSalesOrderDetail,
    clearSalesOrderDetail,
    salesOrderFollow,
    getdocumentationCount
  };
};
