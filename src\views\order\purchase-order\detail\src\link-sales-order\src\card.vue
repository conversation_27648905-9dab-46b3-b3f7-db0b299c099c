<template>
  <div class="flex flex-col" :class="props.class">
    <div class="header flex-bc" :style="{ margin: props.margin }">
      <TitleBar :title="props.title" :fontWeight="props.fontWeight" />
      <slot name="action" />
    </div>
    <slot />
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
const props = defineProps<{
  title: string;
  class?: string;
  margin?: string;
  fontWeight?: number;
}>();
</script>

<style scoped lang="scss"></style>
