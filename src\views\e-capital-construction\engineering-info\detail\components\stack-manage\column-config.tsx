import { TableWidth, StackingPartEnumMapDesc, StorageMethodEnumMapDesc, StorageLocationEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 存栈管理
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "存栈部位",
      prop: "stackingPart",
      minWidth: TableWidth.largeType,
      cellRenderer: (data: TableColumnRenderer) => {
        return StackingPartEnumMapDesc[data.row.stackingPart];
      }
    },
    {
      label: "子实物编码",
      prop: "subPhysicalItemCode",
      minWidth: TableWidth.largeOrder
    },
    {
      label: "实物ID（原材料组部件）",
      prop: "utcNum",
      minWidth: TableWidth.largeOrder
    },
    {
      label: "存栈开始时间",
      prop: "stackingStartTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "充气存放开始时间",
      prop: "inflationStorageStartTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "本体压力表压力值（kPa）",
      prop: "mainBodyPressureGaugeValue",
      minWidth: TableWidth.name
    },
    {
      label: "注油存放开始时间",
      prop: "oilFillingStorageStartTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "存放方式",
      prop: "storageMethod",
      minWidth: TableWidth.status,
      cellRenderer: (data: TableColumnRenderer) => {
        return StorageMethodEnumMapDesc[data.row.storageMethod];
      }
    },
    {
      label: "存放地点",
      prop: "storageLocation",
      minWidth: TableWidth.status,
      cellRenderer: (data: TableColumnRenderer) => {
        return StorageLocationEnumMapDesc[data.row.storageLocation];
      }
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];

  return columnsConfig;
}
