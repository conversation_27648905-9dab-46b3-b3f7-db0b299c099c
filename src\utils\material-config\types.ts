export interface MaterialCategoryConfig {
  /** 品类名称 */
  name: string;
  /** 品类code */
  categoryCode: string;
  /** 该品类下的种类列表 */
  subClassMap: Record<string, MaterialSubClassConfig>;
}

export interface MaterialSubClassConfig {
  /** 种类名称 */
  name: string;
  /** 种类code */
  subClassCode: string;
  /** 填报生产数据配置 */
  productionDataFormConfig: {
    /** 生产试验感知配置 */
    productionTestSensingConfig: ProductionTestSensingConfig;
  };
}

/**
 * 生产试验感知配置
 */
export interface ProductionTestSensingConfig {
  /** 成品入库 */
  finishedProductStorage: {
    /** 电压等级是否必填 */
    voltageLevelNecessity: boolean;
  };
}
