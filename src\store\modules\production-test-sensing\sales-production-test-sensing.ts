import { defineStore } from "pinia";
import { IPerceptionProcessStatus } from "@/models/production-test-sensing/i-common";
import {
  getProductionProcessStatus,
  getNonCableProductionProcessStatus
} from "@/api/production-test-sensing/production-test-sensing";
import { useSalesOrderDetailStore } from "@/store/modules";

export const useSalesProductionTestSensingStore = defineStore({
  id: "cx-sales-production-test-sensing-store",
  state: () => ({
    productionProcessStatus: {} as IPerceptionProcessStatus,
    /** 线缆为生产订单id，非线缆为生产工单id */
    dataId: undefined as string
  }),
  actions: {
    setRefreshParams(dataId: string) {
      this.dataId = dataId;
    },
    /**
     * 刷新生产试验感知的数据状态
     */
    async refreshProductionProcessStatus() {
      this.productionProcessStatus = await (useSalesOrderDetailStore().isCable
        ? getProductionProcessStatus(this.dataId).then(res => res.data)
        : getNonCableProductionProcessStatus(this.dataId).then(res => res.data));
    }
  }
});
