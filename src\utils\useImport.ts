import * as importService from "@/api/import";
import { ImportDataTypeMapEnumName, RefreshSceneEnum, SocketEventEnum } from "@/enums";
import { IImportDataType } from "@/models";
import { useSocketStore } from "@/store/modules";
import { useUserStoreHook } from "@/store/modules/user";
import { downloadByUrl } from "@pureadmin/utils";
import { ElButton, ElNotification } from "element-plus";
import { h } from "vue";

export function useImport() {
  const subscribeImportTaskNotice = () => {
    const socket = useSocketStore();
    socket.on(SocketEventEnum.REFRESH, event => {
      const { type, dataId, userId } = event;
      if (type !== RefreshSceneEnum.SYNC_IMPORT) return;
      if (userId !== useUserStoreHook().profile?.id) return;
      importTaskNotice(dataId);
    });
  };
  const importTaskNotice = async (dataId: string) => {
    if (!dataId) {
      return;
    }
    const importResult = (await importService.getImportTaskResult(dataId)).data;
    if (!importResult) {
      return;
    }

    ElNotification({
      title: "Excel数据导入通知",
      message: showNotice(importResult),
      type: "info"
    });
  };

  const showNotice = (dataType: IImportDataType) => {
    const child = [
      h(
        "span",
        {
          class: "mr-1"
        },
        [h("span", "数据总数"), h("span", { class: "!text-primary" }, dataType.count), h("span", "条")]
      ),
      h(
        "span",
        {
          class: "mr-1"
        },
        [h("span", "导入成功"), h("span", { class: "!text-primary" }, dataType.successCount), h("span", "条")]
      )
    ];

    const content = [h("div", child)];

    if (dataType.errorCount) {
      child.push(
        h("span", [h("span", "导入失败"), h("span", { class: "text-danger" }, dataType.errorCount), h("span", "条")])
      );
      content.push(
        h(
          "div",
          {
            style: { "text-align": "end" }
          },
          [
            h(
              ElButton,
              {
                type: "primary",
                link: true,
                onClick: () => {
                  downloadByUrl(dataType.errorExcelUrl, ImportDataTypeMapEnumName[dataType.dataType]);
                }
              },
              () => "下载错误Excel"
            )
          ]
        )
      );
    }
    return h("div", content);
  };

  return { subscribeImportTaskNotice };
}
