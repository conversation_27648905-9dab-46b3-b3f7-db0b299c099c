import { defineStore } from "pinia";
import {
  IIOTSyncCommon,
  ISalesSyncListItemQueryPrams,
  ISyncSearchParams,
  ISyncCommon,
  ISyncQueryParams,
  IListResponse,
  ISyncListQueryParams,
  SyncListType
} from "@/models";
import {
  PurchaseChannel,
  StateGridOrderSyncDataFormatEnum,
  OrderSyncPriorityEnum,
  StateGridOrderSyncType
} from "@/enums";
import { useStateGridOrderSyncDetailStore } from "@/store/modules";
import * as api from "@/api/state-grid-order-sync";
import { TrackPointKey } from "@/consts";
import { useTableConfig } from "@/utils/useTableConfig";
import { PaginationProps } from "@pureadmin/table";
import { merge } from "lodash-unified";

type StateGridOrderSyncDetailType = {
  type: StateGridOrderSyncType;
  syncActionName: string;
  loading: boolean;
  data: Array<ISyncCommon>;
  pagination: PaginationProps;
  syncActionTrackPointKey: string;
  apiKey: SyncListType;
  searchParams?: ISyncSearchParams;
};

export const useStateGridOrderSyncDetailListStore = defineStore("cx-state-grid-order-sync-detail-list", {
  state: (): StateGridOrderSyncDetailType => ({
    /** 当前处于的同步步骤的key */
    type: undefined,
    /** 同步名称 */
    syncActionName: "",
    loading: false,
    data: [],
    pagination: useTableConfig().pagination,
    syncActionTrackPointKey: "",
    apiKey: undefined as SyncListType
  }),
  getters: {
    queryParams(): ISyncQueryParams {
      const { currentPage, pageSize } = this.pagination;
      const detailStore = useStateGridOrderSyncDetailStore();
      return {
        ...this.searchParams,
        pageNo: currentPage,
        pageSize,
        channel: detailStore.channel
      };
    },
    isEIPSateGridOrder() {
      return useStateGridOrderSyncDetailStore().channel === PurchaseChannel.EIP;
    },
    getSyncListFn() {
      return useStateGridOrderSyncDetailStore().channel === PurchaseChannel.EIP
        ? this.getSyncListByType.bind(this)
        : this.getCsgGuangzhouOrIotSyncDetailListByType.bind(this);
    },
    getStateGridOrderSyncParams() {
      const { sync, purchaseOrderId, iotSync } = useStateGridOrderSyncDetailStore();
      const detailStore = useStateGridOrderSyncDetailStore();
      if (detailStore.channel === PurchaseChannel.EIP) {
        return {
          orderId: purchaseOrderId,
          orderItemId: sync.id,
          channel: PurchaseChannel.EIP
        };
      } else if (detailStore.channel === PurchaseChannel.CSG_GuangZhou) {
        return {
          orderId: iotSync.salesId,
          orderItemId: iotSync.id,
          channel: PurchaseChannel.CSG_GuangZhou
        };
      } else {
        return {
          orderId: iotSync.salesId,
          orderItemId: iotSync.id,
          channel: PurchaseChannel.IOT
        };
      }
    }
  },
  actions: {
    setType(type: StateGridOrderSyncType) {
      this.type = type;
      this.data = [];
      this.resetPagination();
      this.resetSearchParams();
      this.resetSyncInfo();
    },
    updatePagination(pagination: Partial<PaginationProps>) {
      Object.assign(this.pagination, pagination);
    },
    resetPagination(pagination: PaginationProps = useTableConfig().pagination) {
      this.pagination = pagination;
    },
    updateSearchParams(params: Partial<ISyncSearchParams>) {
      Object.assign(this.searchParams, params);
    },
    resetSearchParams(params: ISyncSearchParams = {}) {
      this.searchParams = params;
    },
    refresh(silence = false) {
      useStateGridOrderSyncDetailStore().refreshSyncDetail();
      silence ? this.refreshSyncListSilence() : this.refreshSyncList();
    },
    async refreshSyncList() {
      this.loading = true;
      // 切换步骤时，先发出新的请求，再取消旧的请求，造成loading消失
      const currentType = this.type;
      this.getSyncListFn()
        .then(this.updateData)
        .finally(() => {
          if (currentType === this.type) {
            this.loading = false;
          }
        });
    },
    async refreshSyncListSilence() {
      this.getSyncListFn().then(this.updateData);
    },
    getSyncListByType<T extends ISyncCommon>() {
      const purchaseLineId: string = useStateGridOrderSyncDetailStore().sync.id;
      const params: ISyncListQueryParams = {
        cancelLast: true,
        type: this.apiKey,
        data: {
          purchaseLineId,
          ...this.queryParams
        }
      };
      return api.getSyncList<T>(params);
    },
    getCsgGuangzhouOrIotSyncDetailListByType<T extends IIOTSyncCommon>() {
      const stateGridOrderSyncDetailStore = useStateGridOrderSyncDetailStore();
      const { id } = stateGridOrderSyncDetailStore.iotSync;
      const params: ISalesSyncListItemQueryPrams = {
        saleLineId: id,
        syncProcess: stateGridOrderSyncDetailStore.activeStepKey,
        ...this.queryParams
      };
      return api.getCsgGuangzhouOrIotSyncDetailList<T>(params);
    },
    syncStateGridOrder(dataId?: string, dataFormat?: StateGridOrderSyncDataFormatEnum) {
      const priority = OrderSyncPriorityEnum.COMMON_PRIORITY;
      const type = this.isEIPSateGridOrder
        ? this.type
        : dataFormat === StateGridOrderSyncDataFormatEnum.FILE
        ? StateGridOrderSyncType.SYNC_FILE
        : this.type;
      const temp = {
        dataType: type,
        dataId,
        dataFormat,
        priority
      };
      const params = merge(temp, this.getStateGridOrderSyncParams);
      return api.syncStateGridOrder(params);
    },
    resetSyncInfo() {
      const { actionName, actionTrackPointKey, apiKey } = getSyncTypeInfoMap(this.type);
      this.apiKey = apiKey;
      this.syncActionName = actionName;
      this.syncActionTrackPointKey = actionTrackPointKey;
    },
    updateData(res: IListResponse<ISyncCommon>) {
      this.data = res.data.list || [];
      this.pagination.total = res.data.total || 0;
    }
  }
});

const SYNC_TYPE_MAP = {
  [StateGridOrderSyncType.SALE_ORDER_ITEM]: {
    apiKey: "sales",
    actionName: "同步销售订单明细",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_SALE_BATCH
  },
  [StateGridOrderSyncType.PRODUCTION_PLAN]: {
    apiKey: "production-plan",
    actionName: "同步排产计划",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_PLAN_BATCH
  },
  [StateGridOrderSyncType.PRODUCTION_ORDER]: {
    apiKey: "production",
    actionName: "同步生产订单",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_PO_BATCH
  },
  [StateGridOrderSyncType.WORK_ORDER]: {
    apiKey: "work-order",
    actionName: "同步工单",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_PWO_BATCH
  },
  [StateGridOrderSyncType.REPORT_WORK]: {
    apiKey: "work-report",
    actionName: "同步报工",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_WO_BATCH
  },
  [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: {
    apiKey: "raw-material",
    actionName: "同步原材料",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_RAWMATERIAL_BATCH
  },
  [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: {
    apiKey: "production-flow",
    actionName: "同步过程检",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_BATCH
  },
  [StateGridOrderSyncType.EXPERIMENT]: {
    apiKey: "experiment",
    actionName: "同步试验",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_EXP_BATCH
  },
  [StateGridOrderSyncType.FINISHED_PRODUCT]: {
    apiKey: "finished-production",
    actionName: "同步成品信息",
    actionTrackPointKey: TrackPointKey.FORM_PURCHASE_SYNC_PRO_BATCH
  },
  [StateGridOrderSyncType.TECHNICAL_STANDARD]: {
    apiKey: "technical-standard",
    actionName: "同步技术标准",
    actionTrackPointKey: ""
  },
  [StateGridOrderSyncType.PROCESS_DOCUMENT]: {
    apiKey: "process-document",
    actionName: "同步工序文档",
    actionTrackPointKey: ""
  },
  [StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT]: {
    apiKey: "process-data",
    actionName: "同步自动采集项",
    actionTrackPointKey: ""
  }
} as const;

function getSyncTypeInfoMap(type: StateGridOrderSyncType) {
  const info = SYNC_TYPE_MAP[type];
  if (!info) {
    throw new Error(`type: [${type}] is not implementation`);
  }
  return info;
}
