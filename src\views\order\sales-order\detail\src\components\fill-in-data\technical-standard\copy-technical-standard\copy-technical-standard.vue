<template>
  <el-dialog
    v-model="visible"
    title="选用技术标准"
    align-center
    draggable
    class="medium"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="closeDialog"
  >
    <SelectStandardFromOrder @currentChange="currentChange" />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :saveLoading="saveLoading" :disabled="disabled" @click="saveCopy">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SelectStandardFromOrder from "./select-standard-order.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { computed, inject, ref } from "vue";
import { technicalStandardKey } from "../tokens";
import { useTechnicalStandardStore } from "@/store/modules/technical-standard";
import { useSalesFillInDataStore } from "@/store/modules/fill-in-data";

const props = defineProps<{
  modelValue?: boolean;
  standardInfo?: any;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
}>();

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const store = useTechnicalStandardStore();
const fillInDataStore = useSalesFillInDataStore();
const selectOrder = ref();
const disabled = computed(() => {
  return !selectOrder.value && !selectOrder.value?.id;
});
const saveLoading = ref<boolean>(false);
const saveCopy = useLoadingFn(saveCopyStandard, saveLoading);
const ctx = inject(technicalStandardKey);

/** 当前选择的改变 */
function currentChange(data) {
  selectOrder.value = data;
}

/** 保存选择的技术标准 */
async function saveCopyStandard() {
  if (!selectOrder.value?.standardId) {
    return;
  }
  const { standardId } = selectOrder.value;
  const params = {
    productionOrderId: fillInDataStore.dataId,
    standardId
  };
  await store.saveTechnicalStandardStock(params);
  visible.value = false;
  ctx.refresh();
}

/** 关闭弹框 */
function closeDialog() {
  selectOrder.value = null;
}
</script>

<style scoped lang="scss"></style>
