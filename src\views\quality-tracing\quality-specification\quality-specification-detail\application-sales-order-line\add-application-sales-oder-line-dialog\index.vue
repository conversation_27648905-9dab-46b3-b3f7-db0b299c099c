<template>
  <div class="inline-block">
    <el-button
      v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationAddSalesLine"
      type="primary"
      :icon="Plus"
      @click="openDialog"
    >
      新增销售订单行
    </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="选择销售订单行"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <select-list ref="selectListRef" :quality-id="qualityId" />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { addApplicationSalesOrderLine } from "@/api/quality-tracing/application-sales-order-line";
import SelectList from "./select-list/index.vue";
import { AddApplicationSalesOrderLineParams } from "@/models/quality-tracing";
import { useLoadingFn } from "@/utils/useLoadingFn";

const loading = ref(false);

const props = defineProps<{
  /** 质量规范id */
  qualityId: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const saveLoading = ref(false);

const selectListRef = ref<InstanceType<typeof SelectList>>();

const requestAddApplicationSalesOrderLine = useLoadingFn(async () => {
  const selectedList = selectListRef.value?.getSelectedList();
  if (!selectedList || selectedList.length === 0) {
    ElMessage.warning("请选择物料");
    return false;
  }
  const params: AddApplicationSalesOrderLineParams = {
    qualitySpecificationId: props.qualityId,
    salesLineIdList: selectedList.map(item => item.id)
  };
  const res = await addApplicationSalesOrderLine(params);
  return res.data;
}, saveLoading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const res = await requestAddApplicationSalesOrderLine();

  if (!res) {
    return;
  }

  // 处理保存后续事件
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({
    message: "新增成功",
    type: "success"
  });
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
