<template>
  <div class="bg-bg_color p-5 flex flex-col">
    <div class="flex mb-4">
      <label class="label w-16 text-end">工序：</label>
      <div class="flex-1" v-if="state.processList.length">
        <el-radio-group class="process-radio-group" v-model="state.params.processId" @change="onChangeProcess()">
          <el-radio v-for="item in state.processList" :key="item.id" :label="item.id" border>{{
            item.processName
          }}</el-radio>
        </el-radio-group>
      </div>
      <div v-else class="empty text-secondary text-base mb-2.5">暂无数据</div>
    </div>
    <div class="flex flex-row items-center">
      <label class="label w-16 text-end">采集点：</label>
      <div class="switch-tabs">
        <el-radio-group v-model="state.params.required" @change="onChangeCollectionPoint()">
          <el-radio v-for="(item, index) in collectionPoint" :key="index" :label="item.key" border>{{
            item.name
          }}</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="w-full flex justify-end">
      <!-- <TitleBar title="维护技术标准库参数列表" class="mb-2" /> -->
      <ElButton
        v-auth="PermissionKey.meta.metaTechnicalStandardCollectEdit"
        type="primary"
        v-show="!state.isEdit"
        @click="onToggleEditMode()"
        >编辑
      </ElButton>

      <div class="flex flex-row justify-items-end" v-show="state.isEdit">
        <ElButton @click="onReset()">重置 </ElButton>
        <ElButton @click="onCancel()">取消 </ElButton>
        <ElButton type="primary" :loading="saveLoading" @click="onSave()">保存 </ElButton>
      </div>
    </div>
  </div>
  <div class="bg-bg_color flex flex-col flex-1 overflow-hidden px-5 pb-5">
    <PureTable
      ref="technicalStandardLibraryTableRef"
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.collectionPoints"
      :columns="state.columns"
      showOverflowTooltip
      :loading="loading"
    >
      <template #minValue="data">
        <el-checkbox
          class="!mr-2"
          label="包含"
          v-model="data.row.includeMinValue"
          @update:model-value="newValue => (data.row.includeMinValue = newValue)"
        />
        <el-input-number
          v-model="data.row.minValue"
          :min="0"
          :max="data.row.maxValue || Number.MAX_VALUE"
          class="!w-[120px]"
          controls-position="right"
        />
      </template>

      <template #maxValue="data">
        <el-checkbox
          class="!mr-2"
          label="包含"
          v-model="data.row.includeMaxValue"
          @update:model-value="newValue => (data.row.includeMaxValue = newValue)"
        />

        <el-input-number
          v-model="data.row.maxValue"
          :min="data.row.minValue || 0"
          class="!w-[120px]"
          controls-position="right"
        />
      </template>

      <template #collectRange="data">
        <div>{{ formatCollectRangeDisplay(data.row) }}</div>
      </template>

      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, nextTick, watch } from "vue";
import { ICollectionItem, ICollectionParams, IProcessData, ITechnicalStandardLibraryDetail } from "@/models";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useRoute } from "vue-router";
import { useTechnicalStandardLibraryHook } from "../hooks/technical-standard-library-hook";
import { usePageStoreHook } from "@/store/modules/page";
import { useColumns } from "./columns";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { cloneDeep, isNumber } from "lodash-unified";
import { ElMessage } from "element-plus";
import { PermissionKey, emptyDefaultValue } from "@/consts";
import { GREATE_THAN, GREATE_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL } from "@/consts/symbol";

const pageStoreHook = usePageStoreHook();
const route = useRoute();
const { showColumns, editColumns } = useColumns();
const allType: IProcessData = { processName: "全部" };
const standardId: string = route.params.id as string;
const state = reactive<{
  processList: Array<IProcessData>;
  params: ICollectionParams;
  collectionPoints: Array<ICollectionItem>;
  isEdit: boolean;
  isSave: boolean;
  columns: TableColumnList;
}>({
  processList: [],
  collectionPoints: [],
  isEdit: false,
  isSave: false,
  params: { standardId },
  columns: showColumns
});

const {
  getLibraryDetailById,
  getProductProcessByStandardId,
  queryStandardLibraryCollectionPoint,
  getCollectionItems,
  batchUpdateCollectionItems
} = useTechnicalStandardLibraryHook();

const loading = ref<boolean>(false);
const technicalStandardLibraryTableRef = ref<PureTableInstance>();
const saveLoading = ref(false);

const queryStandardLibrasryCollectionPoints = useLoadingFn(queryStandardLibraryCollectionPoint, loading);

const collectionPoint: Array<{ name: string; key?: boolean }> = [
  { name: "全部" },
  { name: "关键采集点", key: true },
  { name: "非关键采集点", key: false }
];

onMounted(async () => {
  const technicalStandardLibraryDetail: ITechnicalStandardLibraryDetail = await getLibraryDetailById(standardId);
  if (!technicalStandardLibraryDetail) {
    return;
  }
  const pageTitle = technicalStandardLibraryDetail.standardName;
  pageStoreHook.setTitle(pageTitle);

  // 获取工序
  await queryProcess(standardId);

  // 获取技术标准库参数列表
  await handleQueryStandardLibrasryCollectionPoints();
});

/** 表格列 变化重新渲染列
 * Eltable 说明 对 Table 进行重新布局。 当表格可见性变化时，您可能需要调用此方法以获得正确的布局
 */
watch(
  () => state.columns,
  () => {
    nextTick(() => {
      technicalStandardLibraryTableRef.value.getTableRef().doLayout();
    });
  }
);

/** 切换工序 */
const onChangeProcess = async () => {
  await handleQueryStandardLibrasryCollectionPoints();
};

/** 切换采集点 */
const onChangeCollectionPoint = async () => {
  await handleQueryStandardLibrasryCollectionPoints();
};

const queryProcess = async (id: string) => {
  state.processList = await getProductProcessByStandardId(id);
  if (state.processList?.length) {
    state.processList.unshift(allType);
  }
};

/** 根据工序，采集点 查询技术标准库参数列表 */
const handleQueryStandardLibrasryCollectionPoints = async () => {
  state.collectionPoints = [];
  const collectionPoints: Array<ICollectionItem> = await queryStandardLibrasryCollectionPoints(state.params);
  state.collectionPoints = cloneDeep(collectionPoints);
};

const onSave = useLoadingFn(async () => {
  await batchUpdateCollectionItems(state.collectionPoints);
  await handleQueryStandardLibrasryCollectionPoints();
  state.isEdit = false;
  state.columns = showColumns;
  ElMessage.success("保存成功");
}, saveLoading);

/** 切换编辑模式 */
const onToggleEditMode = () => {
  state.isEdit = true;
  state.columns = editColumns;
};

/** 重置 */
async function onReset() {
  state.collectionPoints = getCloneDeepcollectionPoint();
}

/** 取消 */
const onCancel = () => {
  state.columns = showColumns;
  state.collectionPoints = getCloneDeepcollectionPoint();
  state.isEdit = false;
};

const getCloneDeepcollectionPoint = () => {
  return cloneDeep(getCollectionItems());
};

/**格式采集范围 */
const formatCollectRangeDisplay = (data: ICollectionItem) => {
  const { maxValue, minValue, includeMaxValue, includeMinValue } = data;

  if (!isNumber(minValue) && !isNumber(maxValue)) {
    return emptyDefaultValue;
  }

  // 有最小值 没有最大值
  if (isNumber(minValue) && !isNumber(maxValue)) {
    return `值${includeMinValue ? GREATE_THAN_EQUAL : GREATE_THAN}${minValue}`;
  }

  // 有最大值 没有最小值
  if (isNumber(maxValue) && !isNumber(minValue)) {
    return `值${includeMaxValue ? LESS_THAN_EQUAL : LESS_THAN}${maxValue}`;
  }

  // 有最小值，有最大值
  return `${minValue}${includeMinValue ? LESS_THAN_EQUAL : LESS_THAN}值${
    includeMaxValue ? LESS_THAN_EQUAL : LESS_THAN
  }${maxValue}`;
};
</script>

<style scoped lang="scss">
.process-radio-group {
  gap: 12px;

  .el-radio {
    margin-right: 0 !important;
  }
}
</style>
