import { getSyncFinishedProductList } from "@/api/stock-data-center/sync-stock/sync-finished-product";
import { ISearchFinisedProduct } from "@/models/stock-data-center/i-finished-product";
import { ISyncFinishedProduct } from "@/models/stock-data-center/stock-sync/i-finished-proudct-info";
import { setSyncingStatus } from "@/views/stock-data-center/stock-sync/utils/sync-stock-utils";
import { defineStore } from "pinia";

export const useSyncFinishedProductStore = defineStore({
  id: "sync-finished-product-store",
  state: () => ({
    tableTotal: 0,
    syncFinishedProductData: [] as Array<ISyncFinishedProduct>,
    queryParams: {
      pageNo: 1,
      pageSize: 20
    } as ISearchFinisedProduct
  }),
  actions: {
    /** 查询产成品库存同步数据 */
    queryFinishedProductList(queryData: ISearchFinisedProduct) {
      this.queryParams = { ...this.queryParams, ...queryData };
      this.getSyncFinishedProductTableData();
    },

    /** 获取产成品库存同步数据 */
    async getSyncFinishedProductTableData() {
      const resData = await getSyncFinishedProductList(this.queryParams);
      if (Array.isArray(resData.data?.list) && resData.data?.list?.length) {
        this.syncFinishedProductData = setSyncingStatus(resData.data?.list);
        this.tableTotal = resData.data?.total;
      } else {
        this.syncFinishedProductData = [];
        this.tableTotal = 0;
      }
    },

    /** 重置查询参数 */
    initQueryParams() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
    }
  }
});
