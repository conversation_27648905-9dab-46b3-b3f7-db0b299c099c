<template>
  <!-- 到货进度 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item
          label="原材料组部件类型"
          prop="componentType"
          :rules="{ required: true, message: '请选择原材料组部件类型', trigger: 'change' }"
        >
          <el-select
            v-model="formData.componentType"
            class="w-full"
            placeholder="请选择原材料组部件类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in ComponentArrivalEnumOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="主体变/调补变类型"
          prop="mainRegulatingTransformer"
          :rules="{
            required: $route.query.equipmentName == EquipmentNameEnum.Transformer.toString(),
            message: '请选择主体变/调补变类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mainRegulatingTransformer">
            <el-radio v-for="item in MainRegulatEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{ required: true, message: '请输入子实物编码', trigger: 'change' }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder="请输入子实物编码" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="计划到货时间"
          prop="plannedArrivalTime"
          :rules="{ required: true, message: '请选择计划到货时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="formData.plannedArrivalTime"
            placeholder="请选择计划到货时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实际到货时间" prop="actualArrivalTime">
          <el-date-picker
            v-model="formData.actualArrivalTime"
            placeholder="请选择实际到货时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="滞后需求天数"
          prop="demandLagDays"
          :rules="{ required: isDemandRequire, message: '请选择计划到货时间', trigger: 'change' }"
        >
          <el-input-number
            class="w-full"
            :min="0"
            v-model="formData.demandLagDays"
            placeholder="请输入滞后需求天数"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect, computed } from "vue";
import { FormInstance } from "element-plus";
import { ComponentArrivalEnumOptions, EquipmentNameEnum, MainRegulatEnumOptions } from "@/enums";
import { ArrivalInfoModel } from "@/models";

const props = withDefaults(
  defineProps<{
    detail: ArrivalInfoModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as ArrivalInfoModel;
    },
    isEdit: false
  }
);
const formData = reactive({} as ArrivalInfoModel);

const isDemandRequire = computed(() => {
  return new Date(formData.actualArrivalTime).getTime() > new Date(formData.plannedArrivalTime).getTime();
});

watchEffect(() => {
  Object.assign(formData, props.detail);
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as ArrivalInfoModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
