<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="flex-1 ml-6">
      <ElFormItem label="标识：">
        <ElInput class="w-full" clearable v-model="state.params.flagName" placeholder="请输入标识" />
      </ElFormItem>

      <ElFormItem label="状态：">
        <ElSelect v-model="state.params.status" placeholder="请选择状态" class="w-full" clearable filterable>
          <ElOption v-for="item in stateOptions" :key="item.label" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton size="large" type="primary" :icon="Plus" @click="onAddFlagInfoVisible()">新增标识</ElButton>
  </div>
  <div class="flex flex-col flex-1 p-5 mx-6 my-5 overflow-hidden bg-bg_color">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      size="large"
      :data="synchronizationFlagStore.flagList"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="synchronizationFlagStore.loading"
      @page-size-change="querySynchronization"
      @page-current-change="querySynchronization"
    >
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onEditSynchronizationFlag(data.row)"> 编辑 </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="state.dialogTitle"
      align-center
      class="small"
      destroy-on-close
      v-model="state.synchronizationFlagVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="onCloseSynchronizationFlag"
    >
      <SynchronizationFlagForm ref="synchronizationFlagFormRef" />
      <template #footer>
        <el-button @click="onCloseSynchronizationFlag()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveSynchronizationFlag()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { ISynchronizationFlagReq, ISynchronizationFlagForm } from "@/models";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import SynchronizationFlagForm from "./synchronization-flag-form.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useSynchronizationFlagStore } from "@/store/modules";
usePageStoreHook().setTitle("同步标识");

const { columns } = useColumns();
const { pagination } = useTableConfig();
const synchronizationFlagStore = useSynchronizationFlagStore();

const saveLoading = ref<boolean>(false);
const synchronizationFlagFormRef = ref<InstanceType<typeof SynchronizationFlagForm>>();
const onSaveSynchronizationFlag = useLoadingFn(onSaveFlag, saveLoading);

const stateOptions: Array<{
  label: string;
  value: boolean;
}> = [
  { label: "启用", value: true },
  { label: "禁用", value: false }
];

const state = reactive<{
  synchronizationFlagVisible: boolean;
  dialogTitle: string;
  params: ISynchronizationFlagReq;
}>({
  synchronizationFlagVisible: false,
  dialogTitle: undefined,
  params: {}
});

watchEffect(() => {
  pagination.total = synchronizationFlagStore.total;
});

querySynchronization();

const onAddFlagInfoVisible = () => {
  state.dialogTitle = "新增标识";
  state.synchronizationFlagVisible = true;
  synchronizationFlagStore.clearSynchronizationFlagStorage();
};

const onEditSynchronizationFlag = async (data: ISynchronizationFlagForm) => {
  state.dialogTitle = "编辑标识";
  state.synchronizationFlagVisible = true;
  synchronizationFlagStore.setSynchronizationFlagStorage(data);
};

const onCloseSynchronizationFlag = () => {
  state.synchronizationFlagVisible = false;
};

async function onSaveFlag() {
  const formValue: ISynchronizationFlagForm | false = await synchronizationFlagFormRef.value
    .getValidValue()
    .catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.id) {
    await synchronizationFlagStore.createSynchronizationFlag(formValue);
  } else {
    await synchronizationFlagStore.editSynchronizationFlag(formValue);
  }

  state.synchronizationFlagVisible = false;
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  querySynchronization();
}

const onResetQuery = () => {
  state.params = {};
  pagination.currentPage = 1;
  querySynchronization();
};

const onConfirmQuery = () => {
  pagination.currentPage = 1;
  querySynchronization();
};

function querySynchronization() {
  synchronizationFlagStore.querySynchronizationFlag({
    ...state.params,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
}
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
