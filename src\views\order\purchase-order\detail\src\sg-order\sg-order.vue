<template>
  <section class="sync-order flex-1 flex flex-col">
    <el-radio-group class="mx-2 mb-4" v-model="currentChannel">
      <el-radio-button v-for="radio in orderSyncState.orderSyncChannelOption" :key="radio.value" :label="radio.value">
        {{ radio.label }}
      </el-radio-button>
    </el-radio-group>
    <Component class="flex-1" :is="currentList" />
  </section>
</template>

<script setup lang="ts">
import EipSyncList from "./state-grid/state-grid.vue";
import IotSyncList from "./shanghai-platform/shanghai-platform.vue";
import CsgGuangzhouSyncList from "./csg-guangzhou-sync-list/index.vue";
import { computed, onMounted, provide, ref } from "vue";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { SYNC_ORDER_TOKEN } from "./tokens";
import { usePurchaseOrderDetailStore, useStateGridOrderSyncDetailStore } from "@/store/modules";
import { PurchaseChannel, OrderType } from "@/enums";
import { useOrderSyncChannelHook } from "@/views/order/hooks";

type EipSyncListComponent = typeof EipSyncList;
type IotSyncListComponent = typeof IotSyncList;
type CsgGuangzhouSyncListComponent = typeof CsgGuangzhouSyncList;
type ListComponent = EipSyncListComponent | IotSyncListComponent | CsgGuangzhouSyncListComponent | undefined;

const stateGridOrderSyncDetailStore = useStateGridOrderSyncDetailStore();
const purchaseOrderDetail = usePurchaseOrderDetailStore();
const { getOrderSyncChannel, orderSyncState } = useOrderSyncChannelHook();

/** 当前所选渠道 */
const currentChannel = ref<SyncOrderTabEnum>();

provide(SYNC_ORDER_TOKEN, {
  currentKeyComp: computed(() => {
    return currentChannel.value;
  })
});

// 获取当前所选择的平台
const currentList = computed(() => {
  const channelIdentify = currentChannel.value;
  let listComponent: ListComponent = undefined;
  switch (channelIdentify) {
    case SyncOrderTabEnum.SYNC_STATE__GRID_ORDER:
      listComponent = EipSyncList;
      stateGridOrderSyncDetailStore.setSyncType(SyncOrderTabEnum.SYNC_STATE__GRID_ORDER);
      stateGridOrderSyncDetailStore.setChannel(PurchaseChannel.EIP);
      break;
    case SyncOrderTabEnum.SYNC_SHANGHAI_IOT:
      listComponent = IotSyncList;
      stateGridOrderSyncDetailStore.setSyncType(SyncOrderTabEnum.SYNC_SHANGHAI_IOT);
      stateGridOrderSyncDetailStore.setChannel(PurchaseChannel.IOT);
      break;
    case SyncOrderTabEnum.SYNC_CSG_GUANGZHOU:
      listComponent = CsgGuangzhouSyncList;
      stateGridOrderSyncDetailStore.setSyncType(SyncOrderTabEnum.SYNC_CSG_GUANGZHOU);
      stateGridOrderSyncDetailStore.setChannel(PurchaseChannel.CSG_GuangZhou);
      break;
  }
  return listComponent;
});

onMounted(async () => {
  // 获取订单同步渠道
  const orderSyncChannel = await stateGridOrderSyncDetailStore.getOrderSyncChannel({
    orderId: purchaseOrderDetail.purchaseOrder.id,
    viewMode: OrderType.PURCHASE
  });
  getOrderSyncChannel(orderSyncChannel);
  currentChannel.value = orderSyncState.orderSyncChannelOption[0].value as SyncOrderTabEnum;
});
</script>

<style lang="scss">
@import "@/views/order/purchase-order/detail/styles/mixin";

.state-grid-order-sync-detail-dialog {
  @include full-screen-dialog;
}

.order-list {
  width: calc(100% - 16px);
  margin-left: 8px;
}
</style>
