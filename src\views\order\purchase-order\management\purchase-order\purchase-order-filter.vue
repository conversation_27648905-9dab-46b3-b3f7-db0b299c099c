<template>
  <div class="flex flex-col mb-5">
    <div class="flex items-center text-base" v-if="licenseAuthIncludeCSG">
      <div>订单来源</div>
      <div class="ml-5">
        <ElCheckbox
          v-model="state.onlyEIP"
          :label="getEIPLable()"
          class="font-medium"
          @change="onQueryPurchaseOrderByChannel()"
        />
        <ElCheckbox
          v-model="state.onlyCSG"
          :label="getCSGLable()"
          class="font-medium"
          @change="onQueryPurchaseOrderByChannel()"
        />
      </div>
    </div>
    <div class="flex items-center text-base mb-3">
      <div>快捷筛选</div>

      <div class="ml-5">
        <ElCheckbox
          v-model="state.onlyLatestPull"
          :label="getUnReadCount()"
          class="font-medium"
          @change="onQueryUnReadPurchaseOrder()"
          v-track="TrackPointKey.FORM_PURCHASE_SEARCH_UNREAD"
        />
      </div>
      <div class="ml-5">
        <ElCheckbox
          v-model="state.onlyFollow"
          :label="getFollowLabel()"
          class="font-medium"
          @change="onQueryFollowPurchaseOrder()"
          v-track="TrackPointKey.FORM_PURCHASE_SEARCH_ATTENTION"
        />
      </div>

      <div class="ml-5">
        <ElCheckbox
          v-model="state.onlyFiling"
          :label="getFilingLabel()"
          class="font-medium"
          @change="onQueryFilingPurchaseOrder()"
          v-track="TrackPointKey.FORM_PURCHASE_SEARCH_ATTENTION"
        />
      </div>
    </div>
    <div class="flex justify-between">
      <ElRadioGroup
        v-model="state.linkStepKey"
        @change="onChangeLinkStepSearch()"
        v-track="TrackPointKey.FORM_PURCHASE_SEARCH_STEP"
      >
        <ElRadioButton :label="item.value" v-for="item of linkSteps" :key="item.value">
          {{ $t(item.label) }}
          <span class="text-sm leading-none">{{ getLinkStepValue(item.linkStepName) }}</span>
        </ElRadioButton>
      </ElRadioGroup>
      <div>
        <slot name="right" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { TrackPointKey } from "@/consts";
import { LinkStepEnum, LinkStepEnumAlias, PurchaseChannel } from "@/enums";
import { computed } from "vue";
import { formatDecimal } from "@/utils/format";
import { usePurchaseOrderStore, useSystemAuthStore } from "@/store/modules";
import { computedAsync } from "@vueuse/core";

const props = defineProps<{
  linkSteps: any[];
  modelValue: {
    linkStepKey?: LinkStepEnum;
    onlyLatestPull: boolean;
    onlyFollow: boolean;
    onlyFiling?: boolean;
    // 只看国网
    onlyEIP?: boolean;
    // 只看南网
    onlyCSG?: boolean;
  };
}>();

const emit = defineEmits<{
  (
    e: "update:modelValue",
    val: {
      linkStepKey?: LinkStepEnum;
      onlyLatestPull: boolean;
      onlyFollow: boolean;
      onlyFiling?: boolean;
      onlyEIP?: boolean;
      onlyCSG?: boolean;
    }
  );
  (e: "clear-selected"): void;
}>();

const purchaseOrderStore = usePurchaseOrderStore();
const systemAuthStore = useSystemAuthStore();

const licenseAuthIncludeCSG = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeCSG);

const state = computed({
  get() {
    return props.modelValue;
  },
  set(val: {
    linkStepKey?: LinkStepEnum;
    onlyLatestPull: boolean;
    onlyFollow: boolean;
    onlyFiling?: boolean;
    onlyEIP?: boolean;
    onlyCSG?: boolean;
  }) {
    emit("update:modelValue", val);
  }
});

const channel = computed(() => {
  if (state.value.onlyCSG && state.value.onlyEIP) {
    return undefined;
  } else if (state.value.onlyEIP) {
    return PurchaseChannel.EIP;
  } else if (state.value.onlyCSG) {
    return PurchaseChannel.CSG_GuangZhou;
  }
  return undefined;
});

const getEIPLable = () => {
  const eipTotal = purchaseOrderStore.statisticsPurchaseOrders?.eipTotal;
  return `国网（${formatDecimal(eipTotal) || 0}条）`;
};

const getCSGLable = () => {
  const csgTotal = purchaseOrderStore.statisticsPurchaseOrders?.gzTotal;
  return `南网（${formatDecimal(csgTotal) || 0}条）`;
};

const getFollowLabel = () => {
  const followCount = purchaseOrderStore.statisticsPurchaseOrders?.followCount;
  return `已关注（${formatDecimal(followCount) || 0}条）`;
};

const getUnReadCount = () => {
  const count: number = purchaseOrderStore.statisticsPurchaseOrders.unReadCount;
  return `仅看未读（${formatDecimal(count) || 0}条）`;
};

const getFilingLabel = () => {
  const filingCount = purchaseOrderStore.statisticsPurchaseOrders?.documentationCount;
  return `已归档（${formatDecimal(filingCount) || 0}条）`;
};

const getLinkStepValue = (linkStep: LinkStepEnumAlias) => {
  if (!linkStep) {
    return;
  }
  return ` (${formatDecimal(purchaseOrderStore.linkStep?.[linkStep]) || 0})`;
};

const onQueryPurchaseOrderByChannel = () => {
  purchaseOrderStore.queryPurchaseOrderByChannel({
    pageNo: 1,
    channel: channel.value
  });
  purchaseOrderStore.queryPurchaseLinkStep({
    readed: state.value.onlyLatestPull ? false : undefined
  });
  purchaseOrderStore.getStatisticsPurchaseOrders();
  emit("clear-selected");
};

const onQueryUnReadPurchaseOrder = () => {
  purchaseOrderStore.queryUnReadPurchaseOrder({ pageNo: 1, readed: state.value.onlyLatestPull ? false : undefined });
  purchaseOrderStore.queryPurchaseLinkStep({
    readed: state.value.onlyLatestPull ? false : undefined
  });
  purchaseOrderStore.getStatisticsPurchaseOrders();
  emit("clear-selected");
};

const onQueryFollowPurchaseOrder = () => {
  purchaseOrderStore.queryFollowPurchaseOrder({
    pageNo: 1,
    follow: state.value.onlyFollow ? state.value.onlyFollow : undefined
  });
  purchaseOrderStore.queryPurchaseLinkStep({
    follow: state.value.onlyFollow ? state.value.onlyFollow : undefined
  });
  purchaseOrderStore.getStatisticsPurchaseOrders();
  emit("clear-selected");
};

const onQueryFilingPurchaseOrder = () => {
  purchaseOrderStore.queryFollowPurchaseOrder({
    pageNo: 1,
    documentation: state.value.onlyFiling ? state.value.onlyFiling : undefined
  });
  purchaseOrderStore.queryPurchaseLinkStep({
    documentation: state.value.onlyFiling ? state.value.onlyFiling : undefined
  });
  purchaseOrderStore.getStatisticsPurchaseOrders();
  emit("clear-selected");
};

const onChangeLinkStepSearch = () => {
  purchaseOrderStore.queryPurchaseOrdersByLinkStep(state.value.linkStepKey);
  emit("clear-selected");
};
</script>

<style scoped lang="scss"></style>
