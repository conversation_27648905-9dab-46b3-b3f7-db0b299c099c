<template>
  <el-select
    class="w-full"
    v-if="loaded"
    v-model="modelValue"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择车间"
    filterable
    clearable
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <el-select
    v-else
    class="w-full"
    :loading="loading"
    :disabled="disabled"
    :loading-text="SELECTOR_LOADING_TEXT"
    placeholder="请选择车间"
    filterable
    clearable
  />
</template>

<script setup lang="ts">
import { onMounted, ref, withDefaults } from "vue";
import { useVModels } from "@vueuse/core";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";
import { queryWorkshopList } from "@/api/workshop-manage";

/**
 * 车间选择器
 */

interface IOption {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    /** 车间id */
    modelValue: string;
    disabled?: boolean;
  }>(),
  {
    disabled: false
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", code: string): void;
  (event: "loaded"): void;
}>();

const { modelValue } = useVModels(props, emits);
const loading = ref(false);
const loaded = ref(false);
const options = ref<Array<IOption>>([]);

const requestWorkshopList = useLoadingFn(async () => {
  const { data } = await queryWorkshopList();
  if (!data) {
    return;
  }
  options.value = data.map(({ workshopName, id }) => ({
    label: workshopName,
    value: id
  }));
  loaded.value = true;
  emits("loaded");
}, loading);

onMounted(requestWorkshopList);
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  height: var(--el-input-inner-height) !important;
}
</style>
