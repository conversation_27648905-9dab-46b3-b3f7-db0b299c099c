import { defineStore } from "pinia";
import * as api from "@/api/logging/login";
import { ILoginLogReq, ILoginLogging } from "@/models";

export const useLoginLogStore = defineStore({
  id: "cx-login-log-store",
  state: () => ({
    total: 0,
    loginLogs: [] as Array<ILoginLogging>,
    loading: false as boolean
  }),
  actions: {
    /** 查询网关设备日志列表 */
    async queryLoginLog(params: ILoginLogReq) {
      this.loading = true;
      this.gatewayQuery = { ...this.gatewayQuery, ...params };
      const res = await api.queryLoginLog(params);
      this.loginLogs = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    }
  }
});
