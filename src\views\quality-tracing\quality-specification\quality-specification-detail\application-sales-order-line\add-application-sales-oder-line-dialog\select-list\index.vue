<template>
  <div class="overflow-hidden w-full flex flex-col">
    <search-bar @handle-search="handleSearch" />
    <!-- 表格 -->
    <PureTable
      ref="tableRef"
      row-key="id"
      size="large"
      showOverflowTooltip
      height="400px"
      v-model:pagination="pagination"
      :data="list"
      :columns="columnsConfig"
      :loading="loading"
      @select="handleSelect"
      @page-current-change="requestList"
      @page-size-change="reloadList"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
      <template #opertion="{ row }">
        <RouterLink :to="`/quality-specification/${row.id}`">
          <el-button link type="primary">详情</el-button>
        </RouterLink>
      </template>
    </PureTable>
    <div class="mt-2 border border-gray-300 rounded-md p-2">
      <div>已选中列表：</div>
      <el-scrollbar height="100px">
        <el-tag class="m-1" v-for="item in selectedList" :key="item.id" closable @close="handleClose(item)">{{
          item.soItemNo
        }}</el-tag>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { RouterLink } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryChoosableApplicationSalesOrderLineList } from "@/api/quality-tracing";
import { PureTable } from "@pureadmin/table";
import { SelectSalesOrderLineListItem, ChoosableApplicationSalesOrderLineListParams } from "@/models/quality-tracing";
import SearchBar from "./search-bar.vue";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";

/**
 * 适用物料列表
 */

const props = defineProps<{
  qualityId: string;
}>();

interface SearchFormType {
  keyWords: string;
}

const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<SelectSalesOrderLineListItem>>([]);
const searchForm = reactive<SearchFormType>({
  keyWords: ""
});

const selectedList = ref<SelectSalesOrderLineListItem[]>([]);
const tableRef = ref<PureTableInstance>();

/**
 * @description: 选择事件
 */
function handleSelect(selection: SelectSalesOrderLineListItem[]) {
  selectedList.value = selection;
}

/**
 * @description: 关闭tag事件
 */
function handleClose(item: SelectSalesOrderLineListItem) {
  selectedList.value = selectedList.value.filter(({ id }) => item.id !== id);
  tableRef.value.getTableRef().toggleRowSelection(item, false);
}

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: ChoosableApplicationSalesOrderLineListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    qualitySpecificationId: props.qualityId,
    ...searchForm
  };
  const { data } = await queryChoosableApplicationSalesOrderLineList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 搜索
 */
function handleSearch(form: SearchFormType) {
  searchForm.keyWords = form.keyWords;
  reloadList();
}

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(requestList);

defineExpose({
  getSelectedList: () => selectedList.value
});
</script>

<style scoped lang="scss">
:deep(.el-table__header-wrapper) {
  .el-checkbox {
    display: none;
  }
}
</style>
