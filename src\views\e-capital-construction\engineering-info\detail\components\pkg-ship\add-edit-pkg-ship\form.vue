<template>
  <el-form ref="formRef" :model="form" label-position="top">
    <el-row :gutter="20">
      <el-col :span="24">
        <TitleBar class="mb-2" title="基础信息" />
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="运输车牌号"
          prop="transportationVehicleLicensePlate"
          :rules="{
            required: true,
            message: '请输入运输车牌号',
            trigger: 'change'
          }"
        >
          <el-input v-model="form.transportationVehicleLicensePlate" clearable placeholder="请输入运输车牌号" />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item label="车牌号" prop="licensePlateNumber">
          <el-input v-model="form.licensePlateNumber" clearable placeholder="请输入车牌号" />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="本体压力表压力值(kPa)"
          prop="bodyPressureValue"
          :rules="{
            required: true,
            message: '请输入运输车牌号',
            trigger: 'change'
          }"
        >
          <el-input-number
            class="!w-full"
            v-model="form.bodyPressureValue"
            clearable
            :precision="3"
            controls-position="right"
            placeholder="请输入本体压力表压力值(kPa)"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="本体压力表删除状态"
          prop="bodyPressureDeleteFlag"
          :rules="{
            required: true,
            message: '请选择本体压力表删除状态',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="form.bodyPressureDeleteFlag">
            <el-radio v-for="item in BodyPressureEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="12">
        <el-form-item
          label="压力表照片"
          prop="bodyPressureFileId"
          :rules="{
            required: true,
            message: '请选择压力表照片',
            trigger: 'change'
          }"
        >
          <!-- 附件上传 -->
          <UploadFileCom ref="bodyPressureFileIdRef" v-model="form.bodyPressureFileIdInfo" @change="uploadChangeBody" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item label="子实物ID(单元)" prop="subPhysicalItemId">
          <el-input v-model="form.subPhysicalItemId" clearable placeholder="请输入子实物ID(单元)" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{
            required: true,
            message: '请输入子实物编码',
            trigger: 'change'
          }"
        >
          <el-input v-model="form.subPhysicalItemCode" clearable placeholder="请输入子实物编码" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="发运日期"
          prop="dispatchDate"
          :rules="{
            required: true,
            message: '请选择发运日期',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="form.dispatchDate"
            placeholder="请选择发运日期"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="记录仪编号"
          prop="collisionRecorderSerial"
          :rules="{
            required: true,
            message: '请输入记录仪编号',
            trigger: 'change'
          }"
        >
          <el-input v-model="form.collisionRecorderSerial" clearable placeholder="请输入记录仪编号" />
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="12">
        <el-form-item
          label="安装状态"
          prop="recorderStatus"
          :rules="{
            required: true,
            message: '请选择安装状态',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="form.recorderStatus">
            <el-radio v-for="item in RecorderStatusEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isCombiner" :span="24">
        <el-form-item
          label="安装照片"
          prop="fileId"
          :rules="{
            required: true,
            message: '请上传安装照片',
            trigger: 'change'
          }"
        >
          <!-- 附件上传 -->
          <UploadFileCom ref="fileIdRef" v-model="form.fileIdInfo" @change="uploadChangeFile" />
        </el-form-item>
      </el-col>
      <el-col v-if="!isCombiner" :span="24">
        <el-row :gutter="20">
          <el-col :span="24">
            <TitleBar class="mb-2" title="露点值检测" />
          </el-col>
          <el-col :span="12">
            <el-form-item label="绝缘平均含水量(%) " prop="isolationWaterContent">
              <el-input-number
                class="!w-full"
                v-model="form.isolationWaterContent"
                clearable
                :precision="3"
                controls-position="right"
                placeholder="请输入绝缘平均含水量(%) "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="露点温度(℃ )" prop="dewPointTemperature">
              <el-input-number
                class="!w-full"
                v-model="form.dewPointTemperature"
                clearable
                :precision="3"
                controls-position="right"
                placeholder="请输入露点温度(℃ )"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="露点值照片" prop="dewPointFileId">
              <!-- 附件上传 -->
              <UploadFileCom ref="dewPointFileRef" v-model="form.dewPointFileIdInfo" @change="uploadChangeDew" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col v-if="!isCombiner" :span="24">
        <el-row :gutter="20">
          <el-col :span="24">
            <TitleBar class="mb-2" title="冲撞记录仪检测" />
          </el-col>
          <el-col :span="12">
            <el-form-item label="冲撞记录仪编号" prop="number">
              <el-input v-model="form.number" clearable placeholder="请输入冲撞记录仪编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="冲撞记录仪型号" prop="model">
              <el-input v-model="form.model" clearable placeholder="请输入冲撞记录仪型号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="冲撞记录照片" prop="collisionFileId">
              <!-- 附件上传 -->
              <UploadFileCom ref="uploadCollRef" v-model="form.collisionFileIdInfo" @change="uploadChangeColl" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance } from "element-plus";
import { IPkgShipForm, FileInfoModel } from "@/models";
import { useRoute } from "vue-router";
import { BodyPressureEnumOptions, EquipmentTypeEnumExt, RecorderStatusEnumOptions } from "@/enums";
import TitleBar from "@/components/TitleBar/index";
import UploadFileCom from "../../upload-file/index.vue";
defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  resetFormValue
});

const form = reactive<IPkgShipForm>({});
const formRef = ref<FormInstance>();
/** 本体压力表照片标识 */
const bodyPressureFileIdList = ref([]);
/** 安装照片 */
const fileIdList = ref([]);
/** 露点照片 */
const dewPointFileIdList = ref([]);
/** 冲撞记录仪照片标识 */
const collisionFileIdList = ref([]);

const uploadCollRef = ref();
const dewPointFileRef = ref();
const fileIdRef = ref();
const bodyPressureFileIdRef = ref();

const route = useRoute();

/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

const uploadChangeFile = (info: FileInfoModel) => {
  form.fileIdInfo = info;
  form.fileId = info?.id || "";
  fileIdList.value = info?.id ? [info] : [];
};

const uploadChangeColl = (info: FileInfoModel) => {
  form.collisionFileIdInfo = info;
  form.collisionFileId = info?.id || "";
  collisionFileIdList.value = info?.id ? [info] : [];
};

const uploadChangeDew = (info: FileInfoModel) => {
  form.dewPointFileIdInfo = info;
  form.dewPointFileId = info?.id || "";
  dewPointFileIdList.value = info?.id ? [info] : [];
};

const uploadChangeBody = (info: FileInfoModel) => {
  form.bodyPressureFileIdInfo = info;
  form.bodyPressureFileId = info?.id || "";
  bodyPressureFileIdList.value = info?.id ? [info] : [];
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IPkgShipForm) {
  if (route.query.type == EquipmentTypeEnumExt.Combiner.toString()) {
    fileIdList.value = [v.fileIdInfo];
  } else {
    bodyPressureFileIdList.value = v.bodyPressureFileIdInfo ? [v.bodyPressureFileIdInfo] : [];
    dewPointFileIdList.value = v.dewPointFileIdInfo ? [v.dewPointFileIdInfo] : [];
    collisionFileIdList.value = v.collisionFileIdInfo ? [v.collisionFileIdInfo] : [];
  }
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  uploadCollRef.value?.resetFormValue();
  dewPointFileRef.value?.resetFormValue();
  fileIdRef.value?.resetFormValue();
  bodyPressureFileIdRef.value?.resetFormValue();
  formRef.value.resetFields();
}
</script>

<style scoped></style>
