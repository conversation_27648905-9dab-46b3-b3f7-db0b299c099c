<template>
  <BaseList :columns="columns" type="process-data-result" v-bind="$attrs" />
  <DetailProcessQualityAutoDialog ref="detailDialogRef" />
</template>

<script setup lang="ts">
import { h, provide, ref } from "vue";
import { StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import BaseList from "../base-list.vue";
import { syncAuditCellSpanKey } from "../../tokens";
import DetailProcessQualityAutoDialog from "@/views/components/state-grid-order-sync/dialogs/detail-process-quality-auto/index.vue";

const { enumFormatter } = useTableCellFormatter();

const detailDialogRef = ref<InstanceType<typeof DetailProcessQualityAutoDialog>>();

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: "报工批次号",
    prop: "productBatchNo",
    width: TableWidth.order,
    formatter: (row: any) => {
      const { productReportId, productBatchNo } = row;
      return h(
        "a",
        { class: "text-primary", onClick: () => showDetail(productReportId, productBatchNo) },
        productBatchNo
      );
    }
  },
  {
    label: "设备",
    prop: "deviceName",
    minWidth: TableWidth.name
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  }
];
provide(syncAuditCellSpanKey, {
  uniqKeys: ["processId", "productReportId"],
  spanTypes: {
    processName: "processId",
    productBatchNo: "productReportId",
    deviceName: "productReportId",
    syncResult: "productReportId"
  }
});

function showDetail(id: string, no: string) {
  detailDialogRef.value.openDetailDialog(id, no);
}
</script>
