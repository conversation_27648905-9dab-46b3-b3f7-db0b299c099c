<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top" :disabled="disabled">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="Ecode码" prop="ecode">
          <el-input
            v-model="form.ecode"
            placeholder="请输入Ecode码"
            :maxlength="100"
            disabled
            @change="renderCertificate"
          >
            <template #append>
              <el-button @click="refreshEcode" :loading="loading">
                <template #icon>
                  <RefreshLeft class="el-button--text" />
                </template>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品型号" prop="categoryCode">
          <el-autocomplete
            class="w-full"
            v-model="form.categoryCode"
            clearable
            placeholder="请输入产品型号"
            :maxlength="100"
            :trigger-on-focus="false"
            :fetch-suggestions="requestInputAutoCompeleteList('model')"
            @select="handleSelect"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="产品规格" prop="model">
          <el-autocomplete
            class="w-full"
            v-model="form.model"
            clearable
            placeholder="请输入产品规格"
            :maxlength="100"
            :trigger-on-focus="false"
            :fetch-suggestions="requestInputAutoCompeleteList('specification')"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="额定电压" prop="voltageClasses">
          <el-input
            v-model="form.voltageClasses"
            :maxlength="100"
            clearable
            placeholder="请输入额定电压"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="产品名称" prop="categoryName">
          <el-autocomplete
            class="w-full"
            v-model="form.categoryName"
            clearable
            placeholder="请输入产品名称"
            :maxlength="100"
            :trigger-on-focus="false"
            :fetch-suggestions="requestInputAutoCompeleteList('name')"
            @select="handleSelect"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="执行标准" prop="standard">
          <el-autocomplete
            class="w-full"
            v-model="form.standard"
            clearable
            placeholder="请输入执行标准"
            :maxlength="100"
            :trigger-on-focus="false"
            :fetch-suggestions="requestInputAutoCompeleteList('standard')"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="出厂盘号" prop="reelNo">
          <el-input
            v-model="form.reelNo"
            :maxlength="100"
            clearable
            placeholder="请输入盘号"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="毛重" prop="grossWeight">
          <el-input
            v-model="form.grossWeight"
            type="number"
            clearable
            placeholder="请输入毛重"
            :maxlength="100"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="总长" prop="amount">
          <el-input
            v-model="form.amount"
            type="number"
            clearable
            placeholder="请输入总长"
            :maxlength="100"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="净重" prop="netWeight">
          <el-input
            v-model="form.netWeight"
            type="number"
            clearable
            placeholder="请输入净重"
            :maxlength="100"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="制造日期" prop="manufactureDate">
          <el-date-picker
            class="!w-full"
            clearable
            type="date"
            v-model="form.manufactureDate"
            placeholder="请选择制造日期"
            @change="renderCertificate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="检验员" prop="inspector">
          <el-input v-model="form.inspector" clearable placeholder="请输入检验员姓名" @change="renderCertificate" />
        </el-form-item>
      </el-col>
    </el-row>
    <Titlebar title="合格证预览" class="mb-2" />
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="合格证模板" prop="certificateTemplateId">
          <el-select class="w-full" v-model="form.certificateTemplateId" placeholder="请选择合格证模板">
            <el-option v-for="item in templateList" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <div v-show="templateBlob" class="w-full overflow-hidden">
      <ExcelEditor ref="excelEditorRef" preview class="w-96" :blob="templateBlob" :showGridLines="0" />
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { RefreshLeft } from "@element-plus/icons-vue";
import Titlebar from "@/components/TitleBar";
import { genQrcodeUrl } from "@/utils/gen-qrcode-url";
import { InputAutoCompeleteItem } from "../../models";
import { getInputAutoCompeleteList } from "../../api/specification-manage";
import { getAllCertificate, getCertificateDetail } from "../../api/certificate-manage";
import ExcelEditor from "../../components/excel-editor/editor.vue";
import { downLoadFile } from "../../api/upload-file";
import { formatDate } from "@/utils/format";
import { getOneUnusedEcode } from "../../api/ecode-manage";
import { useLoadingFn } from "@/utils/useLoadingFn";

interface FormType {
  ecode: string;
  categoryCode: string;
  model: string;
  voltageClasses: string;
  categoryName: string;
  standard: string;
  reelNo: string;
  grossWeight: string;
  amount: string;
  netWeight: string;
  manufactureDate: string;
  inspector: string;
  certificateTemplateId: string;
}

/**
 * 合格证表单
 */
withDefaults(
  defineProps<{
    /** 是否禁用表单 */
    disabled?: boolean;
  }>(),
  {
    disabled: false
  }
);

const loading = ref(false);
const form = reactive<FormType>({
  ecode: "",
  categoryCode: "",
  model: "",
  voltageClasses: "",
  categoryName: "",
  standard: "",
  reelNo: "",
  grossWeight: "",
  amount: "",
  netWeight: "",
  manufactureDate: "",
  inspector: "",
  certificateTemplateId: ""
});
const formRef = ref<FormInstance>();
const excelEditorRef = ref<InstanceType<typeof ExcelEditor>>();
const templateList = ref<Array<{ id: string; label: string; size: string; location: string[] }>>([]);
const templateBlob = ref<Blob>();
const rules: FormRules = {
  ecode: [{ required: true, message: "请输入Ecode码", trigger: "change" }],
  categoryCode: [{ required: true, message: "请输入产品型号", trigger: "change" }],
  model: [{ required: true, message: "请输入产品规格", trigger: "change" }],
  voltageClasses: [{ required: true, message: "请输入额定电压", trigger: "change" }],
  categoryName: [{ required: true, message: "请输入产品名称", trigger: "change" }],
  standard: [{ required: true, message: "请输入执行标准", trigger: "change" }],
  reelNo: [{ required: false, message: "请输入出厂盘号", trigger: "change" }],
  grossWeight: [{ required: false, message: "请输入毛重", trigger: "change" }],
  amount: [{ required: false, message: "请输入总长", trigger: "change" }],
  netWeight: [{ required: false, message: "请输入净重", trigger: "change" }],
  manufactureDate: [{ required: false, message: "请选择制造日期", trigger: "change" }],
  inspector: [{ required: false, message: "请输入检验员", trigger: "change" }],
  certificateTemplateId: [{ required: true, message: "请选择合格证模板", trigger: "change" }]
};

const curTemplateDetail = computed(() => {
  const item = templateList.value.find(item => item.id === form.certificateTemplateId);
  return item;
});

const refreshEcode = useLoadingFn(async () => {
  const { data } = await getOneUnusedEcode();
  form.ecode = data.ecode;
}, loading);

/**
 * @description: 获取合格证列表
 */
async function requestCertificateTemplateList() {
  const res = await getAllCertificate();
  return res.data.map(item => ({
    id: item.id,
    label: item.templateName,
    size: item.size,
    location: item.location ? item.location.split(",") : []
  }));
}

/**
 * @description: 获取输入框自动补全列表
 */
function requestInputAutoCompeleteList(field: "model" | "specification" | "name" | "standard") {
  return (queryString: string, cb: (arg: any) => void) => {
    if (queryString) {
      getInputAutoCompeleteList(field, queryString).then(res => {
        const filterRes = (res.data ?? []).map(item => {
          switch (field) {
            case "model":
              item.value = item.typeCode;
              break;
            case "specification":
              item.value = item.specificationName;
              break;
            case "name":
              item.value = item.typeName;
              break;
            case "standard":
              item.value = item.standardName;
              break;
          }
          return item;
        });
        cb(filterRes);
      });
    }
  };
}

/**
 * @description: 选择器补全事件
 */
function handleSelect(item: InputAutoCompeleteItem) {
  if (!form.standard) {
    if (!form.categoryName) {
      form.standard = item.standardName;
      form.categoryName = item.typeName;
      if (item.certificateId) {
        form.certificateTemplateId = item.certificateId;
      }
    } else if (!form.categoryCode) {
      form.standard = item.standardName;
      form.categoryCode = item.typeName;
      if (item.certificateId) {
        form.certificateTemplateId = item.certificateId;
      }
    }
  }
}

/**
 * @description: 清空模板文件
 */
function clearTemplateBlob() {
  templateBlob.value = undefined;
}

/**
 * @description: 渲染合格证
 */
function renderCertificate() {
  if (!excelEditorRef.value || !excelEditorRef.value.onPreview || !templateBlob.value) {
    return;
  }
  setTimeout(async () => {
    const dateStr = formatDate(form.manufactureDate, "YYYY-MM-DD") || "";
    let year = "";
    let month = "";
    let day = "";
    if (dateStr) {
      year = dateStr.split("-")[0];
      month = dateStr.split("-")[1];
      day = dateStr.split("-")[2];
    }
    excelEditorRef.value.onPreview({
      ecode: form.ecode || "",
      categoryCode: form.categoryCode || "",
      model: form.model || "",
      voltageClasses: form.voltageClasses || "",
      categoryName: form.categoryName || "",
      standard: form.standard || "",
      reelNo: form.reelNo || "",
      grossWeight: form.grossWeight || "",
      amount: form.amount || "",
      netWeight: form.netWeight || "",
      manufactureDate: formatDate(form.manufactureDate, "YYYY-MM-DD") || "",
      inspector: form.inspector || "",
      year: year,
      month: month,
      day: day,
      ecode1: form.ecode || "",
      categoryCode1: form.categoryCode || "",
      model1: form.model || "",
      voltageClasses1: form.voltageClasses || "",
      categoryName1: form.categoryName || "",
      standard1: form.standard || "",
      reelNo1: form.reelNo || "",
      grossWeight1: form.grossWeight || "",
      amount1: form.amount || "",
      netWeight1: form.netWeight || "",
      manufactureDate1: formatDate(form.manufactureDate, "YYYY-MM-DD") || "",
      inspector1: form.inspector || "",
      year1: year,
      month1: month,
      day1: day
    });
    if (
      curTemplateDetail.value &&
      curTemplateDetail.value.location.length &&
      curTemplateDetail.value.size &&
      curTemplateDetail.value.size.length
    ) {
      const qrcodeUrl = await genQrcodeUrl(`https://www.iotroot.com/E=${form.ecode}`);
      excelEditorRef.value.onSetQrCode({
        top: Number(curTemplateDetail.value.location[0]),
        left: Number(curTemplateDetail.value.location[1]),
        width: Number(curTemplateDetail.value.size),
        height: Number(curTemplateDetail.value.size),
        src: qrcodeUrl
      });
    }
  }, 500);
}

/**
 * @description: 打印合格证
 */
function printCertificate() {
  if (!excelEditorRef.value || !excelEditorRef.value.onPrint) {
    return;
  }
  excelEditorRef.value.onPrint();
}

/**
 * @description: 获取模板文件的二进制数据
 */
const getFileBlob = async (filedId: string) => {
  const blob: Blob = await downLoadFile(filedId);
  if (!blob) {
    ElMessage({ message: "查询模板文件异常", type: "error" });
    return;
  }
  templateBlob.value = blob;
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: Partial<FormType>) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

requestCertificateTemplateList().then(res => {
  templateList.value = res;
});

watch(
  () => form.certificateTemplateId,
  id => {
    if (!id) {
      return;
    }
    getCertificateDetail(id)
      .then(res => res.data.fileId)
      .then(id => {
        getFileBlob(id);
      })
      .then(() => {
        setTimeout(renderCertificate, 500);
      });
  }
);

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  clearTemplateBlob,
  renderCertificate,
  printCertificate
});
</script>

<style lang="scss" scoped>
:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
