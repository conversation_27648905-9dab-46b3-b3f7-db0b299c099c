import { defineStore } from "pinia";
import { EquipmentJSONModel, IEJJGatewayList, IEJJInterface, ISearchEJJGatewayReq } from "@/models";
import { getEjjGatewayLogList, getEjjInterfaceOptions } from "@/api/logging/gateway/ejj-gateway-log";
import { omitBy } from "lodash-unified";
import { isNullOrUnDef } from "@pureadmin/utils";

export const useEJJGatewayLogStore = defineStore({
  id: "ejj-gateway-log-store",
  state: () => ({
    tableTotal: 0,
    gatewayQuery: {} as ISearchEJJGatewayReq,
    pageData: [] as Array<IEJJGatewayList>,
    interfaceOptions: [] as Array<IEJJInterface>,
    equipmentList: {} as EquipmentJSONModel
  }),
  getters: {},
  actions: {
    /** 获取网关设备的搜索下拉框 */
    async getGatewaySearchOptions(subClassCode: string) {
      const res = await getEjjInterfaceOptions(subClassCode);
      this.matOptions = res.data || [];
    },
    /** 查询网关设备当日记录日志列表 */
    async queryEJJGatewayTLogList(params: ISearchEJJGatewayReq) {
      this.gatewayQuery = { ...this.gatewayQuery, ...params };
      await this.getGatewayLogList();
    },
    async getGatewayLogList() {
      const queryParams = omitBy(this.gatewayQuery, value => isNullOrUnDef(value));
      const res = await getEjjGatewayLogList(queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.pageData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.pageData = [];
        this.tableTotal = 0;
      }
    }
  }
});
