<template>
  <el-dialog
    v-model="visible"
    :title="title"
    class="large"
    align-center
    destroy-on-close
    close-on-press-escape
    @close="closeErrorDialog"
  >
    <Component :is="listComponent" :batchList="batchList" />
  </el-dialog>
</template>

<script setup lang="ts">
import SyncErrorList from "./batch-error-list/sync-error-list.vue";
import TriggerScoreErrorList from "./batch-error-list/trigger-score-error.vue";
import { PurchaseOrderLineDetailEnum } from "@/enums/purchase-order";
import { computed } from "vue";

const props = defineProps<{
  modelValue?: boolean;
  type?: string;
  batchList?: Array<Record<string, string>>;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
  (e: "closeErrorDialog"): void;
}>();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});

const title = computed(() => {
  return props.type === PurchaseOrderLineDetailEnum.DATA_SYNC ? "批量同步" : "批量触发评分";
});

const listComponent = computed(() => {
  switch (props.type) {
    case PurchaseOrderLineDetailEnum.DATA_SYNC:
      return SyncErrorList;
    case PurchaseOrderLineDetailEnum.TRIGGER_SCORE:
      return TriggerScoreErrorList;
    default:
      return null;
  }
});

function closeErrorDialog() {
  visible.value = false;
}
</script>

<style scoped lang="scss"></style>
