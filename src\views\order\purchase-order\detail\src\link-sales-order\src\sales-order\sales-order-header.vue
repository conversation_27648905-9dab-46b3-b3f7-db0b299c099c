<template>
  <div class="flex-bc">
    <div class="leading-8 text-lg font-semibold">销售订单</div>
    <div>
      <!-- <el-button @click="openLinkSalesOrderDialog">
        <FontIcon class="mr-2" icon="icon-link" />
        关联销售订单
      </el-button> -->
      <el-button
        v-auth="PermissionKey.form.formPurchaseSalesCreate"
        v-track="TrackPointKey.FORM_PURCHASE_SALES_CREATE"
        type="primary"
        @click="openSalesOrderDialog"
      >
        <FontIcon class="mr-2" icon="icon-plus" />
        新增销售订单
      </el-button>
    </div>
    <LinkSalesOrderLineDialog v-model="linkLineVisible" />
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import { salesOrderDialogKey } from "../../token";
import LinkSalesOrderLineDialog from "./link-sales-order-line-dialog.vue";
import { Permission<PERSON>ey, TrackPointKey } from "@/consts";

const dialog = inject(salesOrderDialogKey);
const linkLineVisible = ref(false);

// function openLinkSalesOrderDialog() {
//   linkLineVisible.value = true;
// }

function openSalesOrderDialog() {
  dialog.title = "新增销售订单";
  dialog.visible = true;
  dialog.id = null;
  dialog.type = "create";
}
</script>

<style scoped></style>
