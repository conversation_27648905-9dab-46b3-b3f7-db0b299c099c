<template>
  <div class="overflow-hidden grid">
    <BaseList
      :columns="columns"
      type="experiment"
      v-bind="$attrs"
      :cardId="props.cardId"
      ref="baseList"
      v-if="store.hasOtherExperiment"
    />
    <div class="gap-line py-2" v-if="store.hasOtherExperiment && store.hasJFNYExperiment" />
    <JfnyList v-if="store.hasJFNYExperiment" v-bind="$attrs" />
  </div>

  <EditAirghtnessExperienceDialog ref="editDialog" :subclass-code="subclassCode" :isCable="store.isCable" />
</template>

<script setup lang="ts">
import { KeywordAliasEnum, StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { computed, h, provide, ref } from "vue";
import JfnyList from "./jfny-list.vue";
import { OperatorCell } from "@/components/TableCells";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditAirghtnessExperienceDialog from "@/views/components/state-grid-order-sync/dialogs/edit-airghtness-experience-dialog.vue";
import BaseList from "../base-list.vue";
import { syncAuditCellSpanKey } from "../../tokens";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const props = defineProps<{
  cardId: string;
}>();

const store = useStateGridSyncAuditStore();
const { enumFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const editDialog = ref<InstanceType<typeof EditAirghtnessExperienceDialog>>();

const subclassCode = computed(() => store.subClassCode);

provide(stateGridOrderSyncEditKey, {
  refreshFn: () => baseList.value?.refresh()
});

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: store.isCable ? "生产订单号" : "生产工单号",
    prop: "orderNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: store.isCable ? KeywordAliasEnum.IPO_NO : "product_work_order_no",
        defaultText: store.isCable ? "生产订单号" : "生产工单号"
      }),
    width: TableWidth.order
  },
  {
    label: "试验编号",
    prop: "experimentNo",
    minWidth: TableWidth.largeOrder
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "检测项",
    prop: "inspectionName",
    minWidth: TableWidth.name,
    fixed: "right"
  },
  {
    label: "检测值",
    prop: "inspectionValue",
    minWidth: TableWidth.order,
    fixed: "right",
    className: "error-mark",
    slot: "inspectionValue"
  },
  {
    label: "数据格式要求",
    prop: "inspectionRule",
    minWidth: TableWidth.name,
    fixed: "right",
    className: "error-mark"
  },
  {
    label: "操作",
    prop: "operator",
    width: TableWidth.operation,
    fixed: "right",
    className: "no-default",
    cellRenderer: data => {
      if (!data.row.editable) {
        return null;
      }
      return OperatorCell([
        {
          name: "编辑",
          action: () => editDialog.value.openEditDialog(data.row),
          props: { type: "primary" }
        }
      ]);
    }
  }
];
provide(syncAuditCellSpanKey, {
  uniqKeys: ["processCode", "processCode_orderNo", "id"],
  spanTypes: {
    processName: "processCode",
    orderNo: "processCode_orderNo",
    experimentNo: "id",
    syncResult: "id",
    operator: "id"
  }
});
</script>

<style scoped></style>
