<template>
  <div class="bg-bg_color p-5 overflow-hidden w-full flex flex-col flex-1">
    <!-- 表格 -->
    <PureTable
      row-key="id"
      :data="list"
      :columns="columnsConfig"
      size="large"
      :loading="loading"
      showOverflowTooltip
      v-model:pagination="pagination"
      @page-current-change="requestList"
      @page-size-change="reloadList"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
      <template #opertion="{ row }">
        <remove-application-sales-order-line-btn
          :sales-line-id="row.salesLineId"
          :quality-id="props.qualityId"
          @post-delete-success="requestList"
        />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryApplicationSalesOrderLineList } from "@/api/quality-tracing";
import { PureTable } from "@pureadmin/table";
import { ApplicationSalesOrderLineListItem, ApplicationSalesOrderLineListParams } from "@/models/quality-tracing";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import RemoveApplicationSalesOrderLineBtn from "./remove-application-sales-order-line-btn.vue";

/**
 * 适用物料列表
 */

const props = defineProps<{
  qualityId: string;
}>();

const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<ApplicationSalesOrderLineListItem>>([]);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: ApplicationSalesOrderLineListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    qualitySpecificationId: props.qualityId
  };
  const { data } = await queryApplicationSalesOrderLineList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

onMounted(requestList);

defineExpose({
  requestApplicationSalesOrderLineList: requestList
});
</script>

<style scoped lang="scss"></style>
