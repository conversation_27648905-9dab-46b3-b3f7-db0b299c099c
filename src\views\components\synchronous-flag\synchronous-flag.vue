<template>
  <el-select
    class="w-full"
    v-model="selectedId"
    multiple
    collapse-tags
    collapse-tags-tooltip
    filterable
    placeholder="请选择标识"
    :max-collapse-tags="3"
    :disabled="disabled"
    clearable
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="checkDisabled(item)"
    />
  </el-select>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { IOption, ISynchronousFlag } from "@/models";
import { useSynchronousFlagStore } from "@/store/modules";
import { SynchronousFlagEnum } from "@/enums/synchronous-flag";

const props = defineProps<{
  modelValue?: string | Array<string>;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string | Array<string>): void;
}>();

const synchronousFlagStore = useSynchronousFlagStore();

const options = ref<Array<IOption>>([]);

const selectedId = computed({
  get() {
    return props.modelValue;
  },
  set(value: string | Array<string>) {
    emit("update:modelValue", value);
  }
});

onMounted(() => getOptions());

async function getOptions() {
  const res: Array<ISynchronousFlag> = await synchronousFlagStore.querySynchronousFlag();
  options.value = res.map(item => ({ label: item.flagName, value: item.id }));
}

function checkDisabled(item: IOption) {
  if (!Array.isArray(selectedId.value)) {
    return false;
  }
  // 判断已选中是否包含广州供电局 或 国网
  const hasCsgGZ = selectedId.value.some(id => id === SynchronousFlagEnum.CSG_GuangZhou_Flag);

  // 如果已选中广州供电局
  if (hasCsgGZ) {
    if (item.value === SynchronousFlagEnum.CSG_GuangZhou_Flag) {
      return false;
    }
    return true;
  }

  if (selectedId.value.length && !hasCsgGZ && item.value === SynchronousFlagEnum.CSG_GuangZhou_Flag) {
    return true;
  }

  return false;
}
</script>

<style scoped></style>
