import { defineStore } from "pinia";
import {
  IBusinessSync<PERSON><PERSON><PERSON>,
  IIot<PERSON>ync<PERSON><PERSON><PERSON>,
  IotSyncListType,
  IPagingReq,
  IStateGridSyncAuditBaseInfo,
  IStateGridSyncExperimentStatus,
  ISyncListQueryParams,
  SyncListType
} from "@/models";
import * as api from "@/api/state-grid-order-sync";

export const useStateGridSyncAuditStore = defineStore({
  id: "cx-state-grid-sync-audit",
  state: () => ({
    baseInfo: undefined as IStateGridSyncAuditBaseInfo,
    activeNo: "",
    limitOrderNo: undefined as string,
    empty: false,
    experimentStatus: undefined as IStateGridSyncExperimentStatus
  }),
  getters: {
    isCable: state => state.baseInfo?.isCable,
    isArmourClamp: state => state.baseInfo?.isArmourClamp,
    subClassCode: state => state.baseInfo?.subClassCode,
    hasJFNYExperiment: state => state.experimentStatus?.hasJFNY,
    hasOtherExperiment: state => state.experimentStatus?.hasOther,
    hasCollectData: state => state.experimentStatus?.hasCollectData
  },
  actions: {
    setBaseInfo(info: IStateGridSyncAuditBaseInfo) {
      this.baseInfo = info;
      this.setExperimentStatus(info.purchaseLineId);
    },
    setOrderNo(orderNo: string) {
      this.limitOrderNo = orderNo;
    },
    setEmpty(empty: boolean) {
      this.empty = empty;
    },
    getSyncListByType<T extends IBusinessSyncCommon>(
      type: SyncListType,
      checkData = false,
      cancelLast = false,
      pageInfo?: IPagingReq
    ) {
      const purchaseLineId: string = this.baseInfo.purchaseLineId;
      const params: ISyncListQueryParams = {
        type,
        checkData,
        cancelLast,
        data: {
          purchaseLineId,
          ...pageInfo
        }
      };
      return api.getSyncList<T>(params).then(res => res.data);
    },
    getMakeSureSyncListByType<T extends IIotSyncCommon>(
      type: IotSyncListType,
      pageInfo?: IPagingReq,
      containCollect = true,
      duplicateKey?: string
    ) {
      const purchaseLineId: string = this.baseInfo.purchaseLineId;
      return api
        .getMakeSureSyncList<T>(purchaseLineId, type, this.limitOrderNo, pageInfo, containCollect, duplicateKey)
        .then(res => res.data);
    },
    getJfnySyncList(pageInfo?: IPagingReq) {
      const purchaseLineId: string = this.baseInfo.purchaseLineId;
      return api.getJfnySyncList(purchaseLineId, this.limitOrderNo, pageInfo).then(res => res.data);
    },
    getFinishedProductionSyncList(pageInfo?: IPagingReq) {
      const purchaseLineId: string = this.baseInfo.purchaseLineId;
      return api.getFinishProductionSyncList(purchaseLineId, this.limitOrderNo, pageInfo).then(res => res.data);
    },
    getProductionWorkOrderOptions(): Promise<Array<string>> {
      const purchaseLineId: string = this.baseInfo.purchaseLineId;
      return api.getOrderNoList(purchaseLineId).then(res => res.data);
    },
    setExperimentStatus(purchaseLineId: string) {
      api.getExperimentStatus(purchaseLineId).then(res => (this.experimentStatus = res.data));
    }
  }
});
