<template>
  <el-dialog v-model="visible" class="default" title="工单详情" align-center destroy-on-close>
    <WorkOrderDetail />
  </el-dialog>
</template>

<script setup lang="ts">
import WorkOrderDetail from "@/views/order/purchase-order/detail/src/components/fill-in-data/work-order-detail/index.vue";
import { ref } from "vue";
import { useWorkOrderStore } from "@/store/modules";

const workOrderStore = useWorkOrderStore();

const visible = ref(false);

function openDetailDialog(id: string) {
  visible.value = true;
  workOrderStore.getWorkOrderDetailById(id);
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
