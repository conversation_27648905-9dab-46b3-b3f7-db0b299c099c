import { IPagingReq } from "@/models";
import { MaterialType, VoltageClassesEnum } from "@/enums";

export interface IMaterialQueryParams extends IPagingReq {
  id?: string;
  keyWords?: string;
  materialCode?: string;
  materialDescribe?: string;
  specificationModel?: string;
  materialName?: string;
  materialUnit?: string;
  voltageClass?: VoltageClassesEnum;
  materialType?: MaterialType;
  subClassCode?: string;
  orFilters?: Array<Record<string, string>>;
}
