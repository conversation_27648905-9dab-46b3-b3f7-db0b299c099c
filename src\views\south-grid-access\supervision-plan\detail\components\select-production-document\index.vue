<template>
  <el-select v-model="value" placeholder="请选择生产单据编码" class="w-full" clearable @change="handleChange($event)">
    <el-option
      v-for="item in options"
      :key="item.productionOrderNo"
      :label="item.productionOrderNo"
      :value="item.productionOrderNo"
    />
  </el-select>
</template>

<script setup lang="ts">
import { queryProductionDocumentList } from "@/api/south-grid-access/production-document";
import { IProductionDocument } from "@/models/south-grid-access";
import { watch } from "vue";
import { computed, onMounted, ref } from "vue";

const emits = defineEmits<{
  (e: "update:modelValue", val: string): void;
  (e: "onSelectedProductionDocument", val: IProductionDocument, initValue?: boolean): void;
}>();

const props = defineProps<{
  supervisionPlanId?: string;
  modelValue?: string;
}>();

const options = ref<Array<IProductionDocument>>();

const value = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    emits("update:modelValue", val);
  }
});

onMounted(async () => {
  if (props.supervisionPlanId) {
    const { data } = await queryProductionDocumentList({ supervisionPlanId: props.supervisionPlanId });
    options.value = data;
  }
});

watch([() => props.modelValue, () => options.value], ([modelValue, options]) => {
  if (modelValue && options?.length) {
    handleChange(props.modelValue, true);
  }
});

const handleChange = (value: string, initValue?: boolean) => {
  emits(
    "onSelectedProductionDocument",
    options.value.find(item => item.productionOrderNo === value),
    initValue
  );
};
</script>
