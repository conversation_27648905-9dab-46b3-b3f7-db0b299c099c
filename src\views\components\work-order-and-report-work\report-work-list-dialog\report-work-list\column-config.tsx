import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function genReportWorkColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "工序",
      prop: "processName",
      width: TableWidth.largeName
    },
    {
      label: "报工批次号",
      prop: "productBatchNo",
      width: TableWidth.order
    },
    {
      label: "设备",
      prop: "deviceName",
      width: TableWidth.largeName
    },
    {
      label: "开始时间",
      prop: "workStartTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "结束时间",
      prop: "workEndTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "报工地址",
      prop: "buyerProvince"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      width: TableWidth.operation,
      slot: "operation"
    }
  ];
  return { columns };
}
