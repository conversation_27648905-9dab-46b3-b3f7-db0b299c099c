<template>
  <div class="relative overflow-hidden">
    <template v-if="syncs?.length || loading">
      <el-scrollbar class="w-full" v-if="purchaseDetailStore.isCable">
        <div class="order-list flex flex-col gap-5 py-1">
          <LoadingSkeleton :loading="loading" :count="count">
            <item v-for="sync in syncs" :key="sync.id" :iot-syncs="sync" :type="PurchaseChannel.IOT" />
          </LoadingSkeleton>
        </div>
        <el-dialog
          :title="detailStore.title"
          v-model="visible"
          fullscreen
          destroy-on-close
          :close-on-press-escape="false"
          class="state-grid-order-sync-detail-dialog"
          :show-close="false"
        >
          <template #header="{ close, titleId, titleClass }">
            <div :id="titleId" class="flex justify-between">
              <div class="flex gap-3 items-center">
                <el-button link @click="close">
                  <el-icon size="20"><Back /></el-icon>
                </el-button>
                <div :class="titleClass" v-if="detailStore.title">{{ detailStore.title }}</div>
              </div>
              <el-button type="danger" @click="close">
                <el-icon size="20"><Close /></el-icon>
              </el-button>
            </div>
          </template>
          <Detail :type="PurchaseChannel.IOT" />
        </el-dialog>
      </el-scrollbar>
    </template>
    <template v-else>
      <EmptyDataDisplay />
    </template>
  </div>
</template>

<script setup lang="ts">
import LoadingSkeleton from "@/views/order/purchase-order/detail/src/sg-order/list/loading-skeleton.vue";
import Item from "@/views/order/purchase-order/detail/src/sg-order/list/item.vue";
import {
  usePurchaseOrderDetailStore,
  useStateGridOrderSyncDetailStore,
  useStateGridOrderSyncListStore
} from "@/store/modules";
import { computed, onMounted, ref } from "vue";
import { useThrottleFn } from "@vueuse/core";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { useSocket } from "@/utils/useSocket";
import { PurchaseChannel, RefreshSceneEnum, SocketEventEnum } from "@/enums";
import Detail from "@/views/order/purchase-order/detail/src/sg-order/detail/detail.vue";
import EmptyDataDisplay from "@/views/order/components/empty-data-display/index.vue";
import { isOrderLineDataType } from "../sync-step-tool";
import { Close, Back } from "@element-plus/icons-vue";

const listStore = useStateGridOrderSyncListStore();
const detailStore = useStateGridOrderSyncDetailStore();
const purchaseDetailStore = usePurchaseOrderDetailStore();

const handleRefresh = useThrottleFn(refresh, REFRESH_SYNC_DATA_PERIOD, true);

const socket = useSocket();
const loading = ref(false);
const count = computed(() => purchaseDetailStore.purchaseOrder?.linkSalesLines?.length || 0);
const syncs = computed(() => listStore.iotSyncs);
const visible = computed({
  get() {
    return detailStore.dialogVisible;
  },
  set(value) {
    detailStore.$patch({ dialogVisible: value });
  }
});

onMounted(() => {
  loading.value = true;
  listStore.setPurchaseOrderId(purchaseDetailStore.purchaseOrderId);
  handleRefresh();
});

async function refresh() {
  await listStore.getIotOrCsgOrderSyncList();
  loading.value = false;
  addRefreshEventListener();
}

function addRefreshEventListener() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, dataType, purchaseOrderItemId } = event;
    const matching = listStore.iotSyncs.find(item => item.id === purchaseOrderItemId);
    if (type !== RefreshSceneEnum.IOT_SYNC_DATA || !matching) return;
    if (isOrderLineDataType(dataType)) {
      handleRefresh();
    }
  });
}
</script>
