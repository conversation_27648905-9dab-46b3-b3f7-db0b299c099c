<template>
  <div>
    <el-input
      class="!w-96"
      placeholder="请输入工单编号"
      v-model="ctx.keyword"
      clearable
      :validate-event="false"
      @clear="ctx.refresh"
      @keydown.enter="ctx.refresh"
    />
    <el-button class="ml-2" type="primary" @click="ctx.refresh">查询</el-button>
  </div>
</template>
<script setup lang="ts">
import { inject } from "vue";
import { workOrderSelectKey } from "@/views/components/work-order-table-select/token";

const ctx = inject(workOrderSelectKey);
</script>
