<template>
  <div>
    <el-steps :active="active" class="mb-5">
      <el-step title="选择生产订单" />
      <el-step title="填写工单" />
      <el-step title="完成" />
    </el-steps>

    <ProductOrderSelect v-model="productOrder" v-show="isFirstStep" />
    <WorkOrderForm
      ref="workOrderForm"
      v-if="isSecondStep"
      type="create"
      :parent-no="productOrder.ipoNo"
      :sub-class-code="productOrder.subClassCode"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from "vue";
import ProductOrderSelect from "./product-order-select.vue";
import WorkOrderForm from "./work-order-form.vue";
import { ICreateWorkOrder, IProductOrder } from "@/models";
import { ElMessage } from "element-plus";
import { useWorkOrderDefaultValueFromProductOrder } from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/hooks/useWorkOrderDefaultValueFromProductOrder";
import { useCreateWorkWorkOrderAppendProductOrderInfo } from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/hooks/useCreateWorkWorkOrderAppendProductOrderInfo";

const active = ref(0);
const productOrder = ref<IProductOrder>();
const workOrderForm = ref<InstanceType<typeof WorkOrderForm>>();

const isFirstStep = computed(() => active.value === 0);
const isSecondStep = computed(() => active.value === 1);
const nextDisabled = computed(() => !productOrder.value);

function next() {
  if (!productOrder.value) {
    ElMessage.warning("请选择生产订单");
    return;
  }
  active.value = 1;
  const workOrder = useWorkOrderDefaultValueFromProductOrder(productOrder.value);
  nextTick(() => workOrderForm.value.initializeValue(workOrder));
}

function prev() {
  active.value = 0;
}

function continueCreate() {
  productOrder.value = undefined;
  active.value = 0;
}

async function getValidValue(): Promise<ICreateWorkOrder & { subClassCode: string }> {
  const data = await workOrderForm.value.getValidValue();
  return useCreateWorkWorkOrderAppendProductOrderInfo(data, productOrder.value);
}

defineExpose({
  isFirstStep,
  isSecondStep,
  nextDisabled,
  next,
  prev,
  continueCreate,
  getValidValue
});
</script>

<style scoped></style>
