import { defineStore } from "pinia";
import * as api from "@/api/workbench";
import { ISyncErrorStatistics } from "@/models";

export const useSyncErrorStatisticsStore = defineStore({
  id: "cx-sync-error-statistics",
  state: () => ({
    loading: false,
    statistics: {} as ISyncErrorStatistics
  }),
  getters: {
    salesCount: state => state.statistics.salesCount || 0,
    planCount: state => state.statistics.planCount || 0,
    productionCount: state => state.statistics.productionCount || 0,
    workOrderCount: state => state.statistics.workOrderCount || 0,
    workReportCount: state => state.statistics.workReportCount || 0,
    rawMaterialCount: state => state.statistics.rawMaterialCount || 0,
    processInfoCount: state => state.statistics.processInfoCount || 0,
    outgoingCount: state => state.statistics.outgoingCount || 0,
    finishProductCount: state => state.statistics.finishProductCount || 0,
    triggerScoreCount: state => state.statistics.triggerScoreCount || 0
  },
  actions: {
    refresh(loading = false) {
      this.loading = loading;
      api
        .getSyncErrorStatistics()
        .then(res => res.data)
        .then(statistics => (this.statistics = statistics))
        .finally(() => (this.loading = false));
    }
  }
});
