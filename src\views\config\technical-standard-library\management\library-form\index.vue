<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物资品类" prop="categoryCode">
          <CategorySelect v-model="form.categoryCode" placeholder="请选择物资品类" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <SubclassSelect
            v-model="form.subClassCode"
            limitByCategoryCode
            :category-code="form.categoryCode"
            placeholder="请选择物资种类"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="技术标准名称" prop="standardName">
          <el-input placeholder="请输入技术标准名称" v-model="form.standardName" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料编号" prop="materialCode">
          <el-input placeholder="请输入物料编号" v-model="form.materialCode" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input placeholder="请输入规格型号" v-model="form.specificationModel" clearable />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="是否通用标准" prop="standard">
          <el-switch
            clearable
            v-model="form.isStandard"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
            inline-prompt
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="enable">
          <el-switch
            clearable
            v-model="form.enable"
            :active-value="true"
            :inactive-value="false"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect, watch } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { ITechnicalStandardLibraryDetail } from "@/models";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import CategorySelect from "@/views/components/category-select/category-select.vue";
import { useTechnicalStandardLibraryStore } from "@/store/modules";

defineExpose({
  validate,
  getValidValue
});

const formRef = ref<FormInstance>();
const technicalStandardLibraryStore = useTechnicalStandardLibraryStore();
const form = reactive<Partial<ITechnicalStandardLibraryDetail>>({});
const rules: FormRules = {
  categoryCode: [{ required: true, message: requiredMessage("物资品类"), trigger: "change" }],
  subClassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "blur" }],
  standardName: [{ required: true, message: requiredMessage("技术标准名称"), trigger: "change" }]
};

watchEffect(() => {
  if (technicalStandardLibraryStore.technicalStandardLibrary) {
    Object.assign(form, technicalStandardLibraryStore.technicalStandardLibrary);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<ITechnicalStandardLibraryDetail> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form as ITechnicalStandardLibraryDetail;
}

watch(
  () => form.categoryCode,
  (newVal, oldVal) => {
    if (oldVal && oldVal != form.categoryCode) {
      form.subClassCode = null;
    }
  }
);
</script>

<style scoped></style>
