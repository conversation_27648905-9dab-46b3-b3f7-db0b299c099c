export interface IOpenApiLog {
  id: string;

  /** 租户Id */
  tenantId: string;

  /** 接口名称 */
  interfaceName: string;

  /** 接口中文名称 */
  interfaceUrlName: string;

  /** IP地址 */
  ip: string;

  /** 	请求时间 */
  requestTime: Date;

  /** 请求报文 */
  requestContent: string;

  /** 响应时间 */
  responseTime: Date;

  /** 响应报文 */
  responseContent: string;

  /** 	耗时 */
  elapsedTime: number;

  /** 请求方法 */
  requestMethod: string;

  /** 响应状态码 */
  statusCode: number;
}
