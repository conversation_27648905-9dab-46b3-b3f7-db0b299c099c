import { TableWidth } from "@/enums";
import { useDynamicFormLabel } from "@/utils/useDynamicFormSelect";

export function useColumns() {
  const { getSelectLabel } = useDynamicFormLabel();
  const columns: TableColumnList = [
    {
      label: "检测项目",
      prop: "targetName",
      minWidth: TableWidth.order
    },
    {
      label: "检测值",
      prop: "targetValue",
      minWidth: TableWidth.number,
      formatter: row => {
        return getSelectLabel(row);
      },
      className: "valid-waring"
    },
    {
      label: "单位",
      prop: "unit",
      minWidth: TableWidth.unit
    },
    {
      label: "数据格式要求",
      prop: "datumOrganization",
      minWidth: TableWidth.type,
      className: "valid-waring"
    }
  ];

  return {
    columns
  };
}
