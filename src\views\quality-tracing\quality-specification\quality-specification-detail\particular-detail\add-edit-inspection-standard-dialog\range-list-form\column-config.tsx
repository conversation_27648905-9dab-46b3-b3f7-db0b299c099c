import { ColumnWidth } from "@/enums";
import { ref } from "vue";

/**
 * @description: 生成动态公式表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig = ref<TableColumnList>([
    {
      label: "序号",
      prop: "index",
      width: ColumnWidth.Char2,
      fixed: "left",
      cell<PERSON><PERSON>er(data) {
        return <span>{data.index + 1}</span>;
      }
    },
    {
      label: "数值范围",
      prop: "range",
      align: "center",
      minWidth: ColumnWidth.Char18,
      showOverflowTooltip: false,
      slot: "range"
    },
    {
      label: "分值",
      prop: "score",
      align: "center",
      width: ColumnWidth.Char5,
      slot: "score"
    },
    {
      label: "操作",
      prop: "opertion",
      align: "center",
      width: ColumnWidth.Char3,
      fixed: "right",
      slot: "opertion"
    }
  ]);

  return { columnsConfig };
}
