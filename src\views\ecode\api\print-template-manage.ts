/**
 * @description: 喷码模板管理
 */

import { http } from "@/utils/http";
import { IListResponse, IPagingReq, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import { AddPrintTemplate, EditPrintTemplate, IPrintTemplate } from "../models/i-print-template-manage";

/**
 * @description: 获取喷码模板列表
 */
export const getPrintTemplateList = (data: IPagingReq) => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/list`);
  return http.get<IPagingReq, IListResponse<IPrintTemplate>>(url, { params: data });
};

/**
 * @description: 获取喷码模板详情
 */
export const getPrintTemplateDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/detail/${id}`);
  return http.get<string, IResponse<IPrintTemplate>>(url);
};

/**
 * @description: 新增喷码模板
 */
export const addPrintTemplate = (data: AddPrintTemplate) => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/create`);
  return http.post<AddPrintTemplate, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑喷码模板
 */
export const editPrintTemplate = (data: EditPrintTemplate) => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/update`);
  return http.put<EditPrintTemplate, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除喷码模板
 */
export const deletePrintTemplate = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/delete/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
};

/**
 * @description: 获取所有喷码模板
 */
export const getAllPrintTemplate = () => {
  const url: string = withApiGateway(`admin-api/ecode/printerTemplate/nameAll`);
  return http.get<void, IResponse<Array<IPrintTemplate>>>(url);
};
