<template>
  <div class="upload-form mx-5" v-if="list.length">
    <UploadFrom ref="formRef" :dynamic-file-data="list" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import UploadFrom from "@/components/Upload";
import { IDynamicFormItem } from "@/models/dynamic-form/i-dynamic-form";

/**
 * 原材料报告表单
 */

const list = ref<Array<IDynamicFormItem>>([]);
const formRef = ref<InstanceType<typeof UploadFrom>>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }

  return formRef.value.validUploadForm();
}

/**
 * @description: 初始化表单
 */
function initFormValue(data: Array<IDynamicFormItem>) {
  list.value = data;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  const { uploadData } = formRef.value.payloadUploadData();
  return uploadData.map(item => {
    const { targetCode, fileList, dataTypeIdentityDetail } = item;
    const { identityCode } = dataTypeIdentityDetail;
    return {
      dataCode: targetCode,
      dataValue: JSON.stringify(fileList),
      identityCode
    };
  });
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>
