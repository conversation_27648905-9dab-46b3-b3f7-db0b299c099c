import {
  IListResponse,
  IPagingReq,
  IPurchaseOderLine,
  IPurchaseOrder,
  IResponse,
  ISaleLineLinkPurchaseLineParams,
  ISalesLinkPurchaseOrderParams,
  ISalesOrder,
  ISalesOrderDetail,
  ISalesOrderFiling,
  ISalesOrderLineDetail,
  ISalesOrderLineLinkPurchase,
  ISalesOrderLineParams,
  ISalesOrderParams,
  ISalesOrderProcess,
  ISalesOrderStatistic,
  IStepValue
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { SalesLinkStepEnum } from "@/enums";

/** 查询销售订单列表数据 */
export const querySalesOrders = (data: ISalesOrderParams) => {
  const url: string = withApiGateway("admin-api/business/sales/page");
  return http.post<ISalesOrderParams, IListResponse<ISalesOrder>>(url, { data });
};

/** 查询销售订单按照进度状态分组统计数据 */
export const queryStepValue = (data: IPagingReq) => {
  const url: string = withApiGateway("admin-api/business/sales/link-step-count");
  return http.post<IPagingReq, IResponse<IStepValue>>(url, { data });
};

/** 查询销售订单按照统计数据（缺失和关注 归档） */
export const querySalesOrderStatistic = (data: ISalesOrderParams) => {
  const url: string = withApiGateway("admin-api/business/sales/page-condition-count");
  return http.post<ISalesOrderParams, IResponse<ISalesOrderStatistic>>(url, { data });
};

/** 创建销售订单 */
export const ceateSalesOrder = (salesOrderDetail: ISalesOrderDetail) => {
  const url: string = withApiGateway("admin-api/business/sales/savePage");
  return http.post<ISalesOrderDetail, IResponse<string>>(url, { data: salesOrderDetail });
};

/** 修改销售订单*/
export const updateSalesOrdersById = (salesOrderDetail: ISalesOrderDetail) => {
  const url: string = withApiGateway("admin-api/business/sales/updatePage");
  return http.post<ISalesOrderDetail, IResponse<string>>(url, { data: salesOrderDetail });
};

/** 根据ID查询销售订单详情数据 */
export const getSalesOrderDetailById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales/detail/${id}`);
  return http.get<string, IResponse<ISalesOrder>>(url);
};

/** 根据ID查询订单步骤信息 */
export const getStepsById = (id: string) => {
  const url = withApiGateway(`admin-api/business/sales/process/${id}`);
  return http.get<void, IResponse<ISalesOrderProcess>>(url);
};

/** 查询销售订单行列表数据 */
export const querySalesOrderLine = (data: ISalesOrderLineParams) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/sales-line-list`);
  return http.post<ISalesOrderLineParams, IListResponse<ISalesOrderLineLinkPurchase>>(url, {
    data
  });
};

/** 删除销售订单 */
export const deleteSalesOrdersById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales/delete/${id}`);
  return http.delete<ISalesOrderParams, IResponse<boolean>>(url);
};

/** 绑定采购订单列表 */
export const querySalesLinkPurchaseOrders = (params: ISalesLinkPurchaseOrderParams) => {
  return http.post<ISalesLinkPurchaseOrderParams, IListResponse<IPurchaseOrder>>(
    withApiGateway("admin-api/business/purchase/page-for-sales"),
    { data: params }
  );
};

/** 关注 取消关注销售订单 */
export const salesOrderFollow = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales/follow/${id}`);
  return http.put<string, IResponse<boolean>>(url);
};

/**获取缺失采购订单行数量 */
export const querySalesOrderLineFaultCount = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/sales-line-list/fault-count/${id}`);
  return http.get<string, IResponse<number>>(url);
};

/**获取销售订单行关联的采购订单数据 */
export const querySaleLineLinkPurchaseOrderLine = (saleId: string, data: ISaleLineLinkPurchaseLineParams) => {
  const url: string = withApiGateway(`admin-api/business/purchase/line-home-page/${saleId}`);
  return http.post<ISaleLineLinkPurchaseLineParams, IListResponse<IPurchaseOderLine>>(url, {
    data
  });
};

/**创建销售订单行信息 */
export const createSalesOrderLineDetail = (data: ISalesOrderLineDetail) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/sales-line-save`);
  return http.post<ISalesOrderLineDetail, IResponse<string>>(url, {
    data
  });
};

/** 删除销售订单行信息 */
export const deleteSaleOrderLineById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/delete/${id}`);
  return http.delete<ISalesOrderParams, IResponse<boolean>>(url);
};

/** 查询销售订单行详情数据 */
export const getSalesOrderLineDetailById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/detail/${id}`);
  return http.get<void, IResponse<ISalesOrderLineDetail>>(url);
};

/** 编辑销售订单行信息 */
export const editSalesOrderLineDetail = (data: ISalesOrderLineDetail) => {
  const url: string = withApiGateway(`admin-api/business/sales-line/sales-line-update`);
  return http.post<ISalesOrderLineDetail, IResponse<string>>(url, {
    data
  });
};

/** 销售订单归档 和取消归档 */
export const saleOrderToggleFiling = (data: ISalesOrderFiling) => {
  const url: string = withApiGateway(`admin-api/business/sales/documentation/${data.id}`);
  return http.put<{ remark: string }, IResponse<boolean>>(url, { data: { remark: data.remark } });
};

/** 销售订单同步步骤跳转到下一步校验 */
export const salesGoToStep = (saleId: string, targetStep: SalesLinkStepEnum) => {
  const url: string = withApiGateway(`admin-api/business/sales/move-to/${saleId}`);
  return http.post<void, IResponse<string>>(url, { params: { targetStep } }, { duplicateKey: "saleGoToStep" });
};
