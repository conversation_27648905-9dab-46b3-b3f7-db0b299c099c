<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <EditWorkOrderDialog :isCable="salesOrderDetailStore.isCable" ref="editDialog" />
  <DetailWorkOrderDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesOrderDetailStore, useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { IWorkOrderSync } from "@/models";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { provide, reactive } from "vue";
import EditWorkOrderDialog from "@/views/components/state-grid-order-sync/dialogs/edit-work-order-dialog.vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import DetailWorkOrderDialog from "@/views/components/state-grid-order-sync/dialogs/detail-work-order-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";

const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditWorkOrderDialog>,
  InstanceType<typeof DetailWorkOrderDialog>,
  IWorkOrderSync
>();

const type = StateGridOrderSyncType.WORK_ORDER;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const salesOrderDetailStore = useSalesOrderDetailStore();
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const columns: Array<TableColumns> = [
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order
  },
  {
    label: "工单",
    prop: "woNo",
    minWidth: TableWidth.suborder,
    formatter: linkFormatter(openDetailDialog)
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);
</script>

<style scoped></style>
