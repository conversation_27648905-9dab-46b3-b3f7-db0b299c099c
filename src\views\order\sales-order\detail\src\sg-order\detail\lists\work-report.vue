<template>
  <BaseList :columns="columns" :type="StateGridOrderSyncType.REPORT_WORK" v-bind="$attrs">
    <template #report-work-batch-no="{ value }">
      <ReportWorkLogDialog :id="value.workReportId">
        <template #report-work> {{ value.productionBatchNo }}</template>
      </ReportWorkLogDialog>
    </template>
  </BaseList>
  <EditWorkReportDialog ref="editWorkReportDialog" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { provide, reactive, ref } from "vue";
import { IWorkReportSync } from "@/models";
import EditWorkReportDialog from "@/views/components/state-grid-order-sync/dialogs/edit-work-report-dialog.vue";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import ReportWorkLogDialog from "@/views/components/report-work-log/report-work-log-dialog.vue";

const type = StateGridOrderSyncType.REPORT_WORK;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const editWorkReportDialog = ref<InstanceType<typeof EditWorkReportDialog>>();

const columns: Array<TableColumns> = [
  {
    label: "生产订单",
    prop: "ipoNo",
    minWidth: TableWidth.order
  },
  {
    label: "工单",
    prop: "woNo",
    minWidth: TableWidth.suborder
  },
  {
    label: "工序名称",
    prop: "processName",
    minWidth: TableWidth.order
  },
  {
    label: "报工批次号",
    prop: "productionBatchNo",
    minWidth: TableWidth.order,
    slot: "report-work-batch-no"
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

async function openEditDialog(data: IWorkReportSync) {
  editWorkReportDialog.value.openEditDialog(data);
}
</script>

<style scoped></style>
