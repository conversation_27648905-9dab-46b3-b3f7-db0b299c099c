import { TableWidth } from "@/enums";
import { IExperimentReport } from "@/models";
import { TableColumnRenderer } from "@pureadmin/table";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";

/**
 * @description: 出厂试验报告
 */

export function useColumns(isCombiner: Boolean) {
  let columns: TableColumnList = [];
  const commonColumn: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      minWidth: TableWidth.operation
    }
  ];
  if (isCombiner) {
    columns = [
      {
        label: "实物ID（间隔）",
        prop: "utcNum",
        minWidth: TableWidth.largeOrder
      },
      {
        label: "试验报告",
        prop: "fileName",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row)}>
              {data.row.fileInfo.name}
            </div>
          );
        }
      }
    ];
  } else {
    columns = [
      {
        label: "试验报告",
        prop: "fileName",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row)}>
              {data.row.fileInfo.name}
            </div>
          );
        }
      }
    ];
  }
  async function downLoad(row: IExperimentReport) {
    const blob = await downLoadFile(row.fileId);
    downloadByData(blob, row.fileInfo.name, blob.type);
  }
  return { columns: [...columns, ...commonColumn] };
}
