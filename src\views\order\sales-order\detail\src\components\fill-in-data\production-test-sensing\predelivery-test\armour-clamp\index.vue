<template>
  <div class="ex-factory-experiment">
    <div class="ex-factory-tabs flex justify-between">
      <div class="flex-1">
        <SwitchTab :switch-tags="exFactoryExperimentStore.processes" @switch-change="switchTag($event)" />
      </div>
      <!-- 局放耐压不显示 -->
      <div class="operate">
        <div class="add-air-tightness">
          <el-button
            v-auth="PermissionKey.form.formPurchaseFactoryTrialCreate"
            type="primary"
            v-if="isCanOperateAddAndEdit"
            @click="addAirTightness()"
          >
            <FontIcon class="mr-2" icon="icon-plus" />
            新增试验
          </el-button>
        </div>
      </div>
    </div>
    <!-- 非局放耐压试验 -->
    <div class="sensing-content min-h-[300px]">
      <AirTightness ref="experimentInstance" @delSuccess="delSuccess" @addDataSuccess="addDataSuccess" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SwitchTab from "../../component/switch-tab/index.vue";
import AirTightness from "./test-info/index.vue";
import { ISwitchTagEventType } from "../../component/switch-tab/types";
import { ref, onMounted } from "vue";
import { useEXFactoryExperimentStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { computed } from "vue";
import { PermissionKey } from "@/consts";

// 切换
const exFactoryExperimentStore = useEXFactoryExperimentStore();
const experimentInstance = ref();

/** 根据工序判断是否可以 新增/编辑 */
const isCanOperateAddAndEdit = computed(() => exFactoryExperimentStore.getIsCanOperateAddAndEdit);

onMounted(async () => {
  await exFactoryExperimentStore.getOutGoingFactoryProcess();
  const processList = exFactoryExperimentStore.processes;
  if (processList?.length) {
    const initProcessInfo: ISwitchTagEventType = {
      data: processList[0]
    };
    switchTag(initProcessInfo);
  }
});

// 切换试验
function switchTag(tag: ISwitchTagEventType) {
  if (!tag.data.processCode) {
    ElMessage.warning("工序编码不能为空");
    return;
  }
  const { processCode, processName, processId, hasData, autoCollect } = tag.data;
  exFactoryExperimentStore.setActiveProcess({ processCode, processName, processId, hasData, autoCollect });
}

const delSuccess = () => {
  exFactoryExperimentStore.getOutGoingFactoryProcess();
};
const addDataSuccess = () => {
  exFactoryExperimentStore.getOutGoingFactoryProcess();
};

/** 新增试验 */
const addAirTightness = () => {
  experimentInstance.value?.addAirTightness();
};
</script>

<style scoped lang="scss"></style>
