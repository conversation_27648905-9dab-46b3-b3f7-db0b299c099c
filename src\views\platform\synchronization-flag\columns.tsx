import { emptyDefaultValue, fullDateFormat } from "@/consts";
import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "标识",
      prop: "flagName",
      width: TableWidth.name
    },
    {
      label: "对接上海IOT",
      prop: "syncIotFlag",
      align: "center",
      cellRenderer(data: TableColumnRenderer) {
        const syncIotFlag: boolean = data.row.syncIotFlag;
        return syncIotFlag ? <span>√</span> : <span>{emptyDefaultValue}</span>;
      }
    },
    {
      label: "对接广州供电局",
      prop: "syncGzFlag",
      align: "center",
      cellRenderer(data: TableColumnRenderer) {
        const syncGzFlag: boolean = data.row.syncGzFlag;
        return syncGzFlag ? <span>√</span> : <span>{emptyDefaultValue}</span>;
      }
    },
    {
      label: "备注",
      prop: "remark"
    },
    {
      label: "状态",
      prop: "status",
      align: "center",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="success">启用</CxTag> : <CxTag type="danger">禁用</CxTag>;
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.simpleOperation
    }
  ];
  return { columns };
}
