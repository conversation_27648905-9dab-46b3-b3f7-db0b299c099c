<!-- 第二步：制定排产计划 -->
<template>
  <div class="overflow-hidden w-full flex flex-col">
    <QuickSolver title="需排产销售订单行" :items="schedulingPlanStore.schedulingPlanStatistics?.unSchduled">
      <template v-slot="{ item }">
        <NoSchedulingPlanList :item="item" />
      </template>
    </QuickSolver>
    <div class="card flex-1 py-3">
      <div class="mb-4">
        <SearchBar @operateChange="onAddSchedulingPlanModalVis" @searchForm="searchForm" />
      </div>
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        size="large"
        showOverflowTooltip
        :columns="columns"
        :loading="schedulingPlanStore.loading"
        :data="schedulingPlanStore.schedulingPlanData || []"
        :pagination="pagination"
        @page-size-change="onPageSizeChange"
        @page-current-change="onPageCurrentPage"
      >
        <template #progress="data">
          <el-progress class="w-full" :stroke-width="11" :percentage="Number(data.row.schedule)" striped>
            <span class="schedule ml-1">{{ data.row.schedule ? data.row.schedule : 0 }} %</span>
          </el-progress>
        </template>
        <template #empty>
          <CxEmpty />
        </template>
      </PureTable>

      <AddSchedulingPlan @cancel="onCancelAddSchedulingPlan()" @success="onAddSchedulingPlanSuccess()" />

      <el-dialog
        v-model="editSchedulingPlanModalVisible"
        title="编辑排产计划"
        class="default"
        align-center
        :destroy-on-close="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="onCloseEditAddSchedulingPlan()"
      >
        <SchedulingPlanForm ref="schedulingPlanFormRef" />
        <template #footer>
          <span>
            <el-button @click="onCloseEditAddSchedulingPlan()">取消</el-button>
            <el-button type="primary" @click="onConfirmEditAddSchedulingPlan()">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog
        v-model="schedulingPlanStore.schedulingPlanDetailVisible"
        title="排产计划详情"
        align-center
        class="middle"
        :destroy-on-close="true"
        @close="onCloseSchedulingPlanDetail()"
      >
        <SchedulingPlanDetail />
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchBar from "./search-bar.vue";
import { useColumns } from "./columns";
import { ref, watchEffect, onMounted } from "vue";
import AddSchedulingPlan from "../add-scheduling-plan/index.vue";
import NoSchedulingPlanList from "./no-scheduling-plan-list/index.vue";
import { useSalesOrderDetailStore } from "@/store/modules";
import SchedulingPlanForm, { ISchedulingPlanForm } from "../add-scheduling-plan/scheduling-plan-form/index.vue";
import { ICreateSchedulingPlan, IResponse, ISchedulingPlanQuery } from "@/models";
import { ElMessage } from "element-plus";
import SchedulingPlanDetail from "../scheduling-plan-detail/index.vue";
import { CreateSchedulingPlanStepEnum } from "@/enums";
import QuickSolver from "@/views/order/sales-order/detail/src/components/quick-solver/quick-solver.vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";

import { useTableConfig } from "@/utils/useTableConfig";
import { useSalesSchedulingPlanStore } from "@/store/modules/scheduling-plan/sales-scheduling-plan";

let salesOrderId: string;
let searchFormValue: ISchedulingPlanQuery;
const schedulingPlanFormRef = ref<ISchedulingPlanForm | null>();
const { editSchedulingPlanModalVisible, columns } = useColumns();
const salesOrderDetailStore = useSalesOrderDetailStore();
const schedulingPlanStore = useSalesSchedulingPlanStore();
const { pagination } = useTableConfig();

onMounted(() => {
  salesOrderId = salesOrderDetailStore.salesOrder?.id;
  querySchedulingPlanPaging();
  schedulingPlanStore.getSchedulingPlanStatistics(salesOrderId);
});

watchEffect(() => {
  pagination.total = schedulingPlanStore.total;
});

watchEffect(() => {
  if (schedulingPlanStore.deleteSchedulingPlanStatus) {
    pagination.currentPage = 1;
    querySchedulingPlanPaging();
    schedulingPlanStore.setDeleteSchedulingPlanStatus(false);
  }
});

// 搜索的时候重置页码
const searchForm = (formValue: ISchedulingPlanQuery) => {
  searchFormValue = formValue;
  pagination.currentPage = 1;
  querySchedulingPlanPaging();
};

const onAddSchedulingPlanSuccess = () => {
  pagination.currentPage = 1;
  schedulingPlanStore.setAddSchedulingPlanModalVisible(false);
  querySchedulingPlanPaging();
  schedulingPlanStore.getSchedulingPlanStatistics(salesOrderId);
  salesOrderDetailStore.refreshStepStatus();
};

const onCancelAddSchedulingPlan = () => {
  schedulingPlanStore.setAddSchedulingPlanModalVisible(false);
  schedulingPlanStore.setSchedulingPlanFormValue();
};

const onAddSchedulingPlanModalVis = () => {
  schedulingPlanStore.setActiveCreateSchedulingPlanStep(CreateSchedulingPlanStepEnum.selectSaleOrderLine);
  schedulingPlanStore.setSchedulingPlanFormAddMode(true);
  schedulingPlanStore.setIsQuickCreateSchedulingPlan(false);
  schedulingPlanStore.setAddSchedulingPlanModalVisible(true);
};

//#region 排产计划编辑
/** 取消排产计划编辑 */
const onCloseEditAddSchedulingPlan = () => {
  editSchedulingPlanModalVisible.value = false;
  schedulingPlanStore.setSchedulingPlanFormValue();
};

/** 确认排产计划编辑 */
const onConfirmEditAddSchedulingPlan = async () => {
  const formValue: ICreateSchedulingPlan | boolean = await schedulingPlanFormRef.value.getFormValue();
  if (typeof formValue === "boolean") {
    return;
  }
  const editRes: IResponse<boolean> = await schedulingPlanStore.editProductionPlan(formValue);
  if (!editRes.data) {
    ElMessage.warning(editRes.msg);
    return;
  }
  ElMessage.success("编辑成功");
  querySchedulingPlanPaging();
  editSchedulingPlanModalVisible.value = false;
  schedulingPlanStore.setSchedulingPlanFormValue();
};
//#endregion

const onCloseSchedulingPlanDetail = () => {
  schedulingPlanStore.setSchedulingPlanDetailVisible(false);
};

function querySchedulingPlanPaging() {
  let queryParams: ISchedulingPlanQuery = {
    saleId: salesOrderId,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  };

  if (searchFormValue) {
    queryParams = {
      ...queryParams,
      ...searchFormValue
    };
  }
  schedulingPlanStore.querySchedulingPlanPaging(queryParams);
}

function onPageSizeChange() {
  pagination.currentPage = 1;
  querySchedulingPlanPaging();
}

function onPageCurrentPage() {
  querySchedulingPlanPaging();
}
</script>
<style lang="scss" scoped>
@import "../../../../../../../../style/mixin";

.card {
  @include productCard;
}

.schedule {
  color: var(--el-text-color-primary);
  font-size: var(--el-font-size-base);
}

:deep(.el-progress-bar__inner) {
  $color: rgba(255, 255, 255, 0.2);
  background: repeating-linear-gradient(-60deg, $color 0, $color 2px, transparent 2px, transparent 8px) no-repeat bottom
    var(--el-color-primary);
}
</style>
