import { IPagingReq } from "../i-paging-req";
import { IRawMaterialCheckCollectionItem } from "../raw-material/i-raw-material-res";
import { IUpLoadFile } from "./i-common";

/**
 * 工序列表状态
 */
export interface IProductionMaterialProcess {
  processCode?: string;
  processUnionCode?: string;
  processName?: string;
  processId?: string;
  hasData?: boolean;
  /** 是否自动采集 (控制是否可新增和编辑) */
  autoCollect?: boolean;
  /** 是否包含系统推送项 */
  containsSystemPushItem?: boolean;
  /** 是否包含自动采集项 */
  containsAutoCollectItem?: boolean;
  /** 设备是否必填 */
  requireDevice?: boolean;
  /** 是否需要检查工序 */
  needCheckProcess?: boolean;
  /** 是否有自动采集数据 */
  hasAutoCollectData?: boolean;
  /** 是否有系统推送数据 */
  hasSystemPushData?: boolean;
}

/**
 * 搜索原材料检，组部件列表接口
 */
export interface ISearchMaterialList extends IPagingReq {
  workOrderId?: string;
  productionId?: number;
  processCode?: string;
}

export interface IRawMaterialInspectList {
  /** 唯一标识 */
  id?: string;
  /** 原材料编码 */
  code: string;
  /** 原材料名称 */
  name: string;
  /** 原材料类型编码 */
  processCode: string;
  /** 原材料类型名称 */
  processName: string;
  /** 创建时间 */
  createTime?: string;
  /** 计量单位 */
  partUnit?: string;
  /** 检验批次号 */
  inspectBatchNo: string;
  /** 检测日期 */
  inspectDate: string;
  /** 文件数据 */
  inspectionReport?: IUpLoadFile[];
  spotCheckReport?: IUpLoadFile[];
}

/**
 * 批量保存选择的原材料检数据
 */
export interface IChooseSaveRawMaterialInspect {
  productionId?: string;
  workOrderId?: string;
  purchaseId?: string;
  rawMaterialId?: string[];
  processId?: string;
  processCode?: string;
  processName?: string;
  inspectIds?: string[];
}

/**
 * 原材料检，组部件列表
 */
export interface IMaterialProcessList {
  id: string;
  code: string;
  name: string;
  processCode: string;
  processName: string;
  materialBatchNo: string;
  borMaterials: string;
  partUnit: string;
  voltageGrade?: number;
  checked?: boolean;
  /** 检验批次号 */
  inspectBatchNo: string;
  inspectionReport?: IUpLoadFile[];
  spotCheckReport?: IUpLoadFile[];
}

export interface IResMaterialProcessList {
  list?: IMaterialProcessList[];
  total: number;
}

export interface ISaveInspectCollectionsReq {
  dataCode: string;
  identityCode: string;
  dataValue: string;
}
export interface ISaveRawMaterialInspectReq {
  purchaseId: string;
  productionId: string;
  workOrderId: string;
  processId: string;
  processCode: string;
  processName: string;
  inspectBatchNo: string;
  inspectDate: string;
  rawMetadataValue: ISaveInspectCollectionsReq[];
  materialId: string;
}

export interface IRawMaterialGroupUnitInspect {
  /** 唯一标识 */
  id?: string;
  /** 生产订单号 */
  productionId: string;
  /** 原材料编码 */
  code: string;
  /** 原材料名称 */
  name: string;
  /** 原材料类型 */
  processCode: string;
  processName: string;
  /** 规格型号 */
  modelCode: string;
  /** 原材料制造商 */
  rawmManufacturer: string;
  /** 产地 */
  oorMaterials: string;
  /** 原材料批次号 */
  materialBatchNo: string;
  /** 检验批次号 */
  inspectBatchNo: string;
  /** 检验日期 */
  inspectDate: string;
  /** 电压等级 */
  voltageGrade: string;
  /** 生产日期 */
  productionDate: string;
  /** 品牌 */
  borMaterials: string;
  /** 计量单位 */
  partUnit: string;
  /** 用料量 */
  quantity: number;
  /** 原材料出厂日期 */
  manuFactureDate: string;
  /** 备注 */
  remark: string;
  /** 检测备注 */
  inspectRemark: string;
  /** 控件信息 */
  rawMetadataValue: IRawMaterialCheckCollectionItem[];
}
