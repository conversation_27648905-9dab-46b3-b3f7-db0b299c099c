<template>
  <div class="sync-detail h-full flex flex-col overflow-hidden">
    <Detail v-loading="loading" />
  </div>
</template>

<script setup lang="ts">
import Detail from "@/views/order/purchase-order/detail/src/sg-order/detail/detail.vue";
import { IPurchaseOrderLineRes } from "@/models/purchase-order";
import { useStateGridOrderSyncDetailStore } from "@/store/modules/state-grid-order-sync";
import { ref, watch } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { PurchaseChannel } from "@/enums/purchase-order/purchase-order-sync-status.enum";

const props = withDefaults(
  defineProps<{
    info: IPurchaseOrderLineRes | null;
  }>(),
  {}
);

const store = useStateGridOrderSyncDetailStore();
const loading = ref<boolean>(false);
const refreshSyncDetail = useLoadingFn(getSyncDetail, loading);

watch(
  () => props.info,
  newVal => {
    if (newVal?.id) {
      const { id, purchaseId, poNo, poItemNo } = newVal;
      const syncs = { id, purchaseOrderId: purchaseId, poNo, poItemNo };
      store.setSyncType(SyncOrderTabEnum.SYNC_STATE__GRID_ORDER);
      store.setChannel(PurchaseChannel.EIP);
      store.setSyncData(syncs);
      refreshSyncDetail();
    }
  },
  {
    immediate: true
  }
);

/** 获取详情同步状态字段 */
async function getSyncDetail() {
  await store.refreshSyncDetail();
}
</script>

<style scoped lang="scss"></style>
