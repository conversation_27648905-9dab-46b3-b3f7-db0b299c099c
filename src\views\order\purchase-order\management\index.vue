<template>
  <div class="bg-bg_color pt-[8px] pb-4 px-6">
    <PurchaseOrderHeader />
    <PurchaseOrderSearchForm />
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
    <PurchaseOrder />
  </div>
  <PurchaseOrderSync />
</template>

<script setup lang="ts">
import PurchaseOrderHeader from "@/views/order/purchase-order/management/purchase-order-header.vue";
import PurchaseOrderSearchForm from "@/views/order/purchase-order/management/purchase-order-search-form.vue";
import PurchaseOrderSync from "@/views/order/purchase-order/purchase-order-sync/index.vue";
import PurchaseOrder from "./purchase-order/purchase-order.vue";
</script>
<style lang="scss" scoped></style>
