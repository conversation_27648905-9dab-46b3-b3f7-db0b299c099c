import { defineStore } from "pinia";
import {
  IIOTStateGridOrderSync,
  IStateGridOrderSync,
  IStateGridOrderSyncDetailList,
  IStateGridOrderSyncParams
} from "@/models";
import * as api from "@/api/state-grid-order-sync";
import { OrderSyncPriorityEnum, StateGridOrderSyncType } from "@/enums";
import { useStateGridOrderSyncDetailStore } from "./state-grid-order-sync-detail";

type StateGridOrderSyncListType = {
  syncs: Array<IStateGridOrderSync>;
  iotSyncs: Array<IIOTStateGridOrderSync>;
  purchaseOrderId: string;
};

export const useStateGridOrderSyncListStore = defineStore({
  id: "purchase-order-sync-list",
  state: (): StateGridOrderSyncListType => ({
    syncs: [],
    purchaseOrderId: undefined,
    iotSyncs: []
  }),
  actions: {
    setPurchaseOrderId(id: string) {
      this.purchaseOrderId = id;
    },
    async refreshStateGridOrderSyncList() {
      this.syncs = (await api.getStateGridOrderSyncList(this.purchaseOrderId)).data;
    },
    updateStateGridOrderSyncDetail(id: string, detail: IStateGridOrderSyncDetailList[]) {
      const sync = this.syncs.find(s => s.id === id);
      if (sync) {
        sync.detail = detail;
      }
    },
    async syncAll(purchaseOrderLineId: string, orderId?: string) {
      const detailStore = useStateGridOrderSyncDetailStore();
      const channel = detailStore.channel;
      const priority = OrderSyncPriorityEnum.COMMON_PRIORITY;
      const params: IStateGridOrderSyncParams = {
        orderId: orderId ? orderId : this.purchaseOrderId,
        orderItemId: purchaseOrderLineId,
        dataType: StateGridOrderSyncType.ALL,
        channel,
        priority
      };
      return api.syncStateGridOrder(params);
    },

    /** 获取IOT或者CSG同步列表 */
    async getIotOrCsgOrderSyncList() {
      const detailStore = useStateGridOrderSyncDetailStore();
      const channel = detailStore.channel;
      this.iotSyncs = (await api.getIotOrCsgGuangzhouSyncListByPuchaseId(this.purchaseOrderId, channel)).data;
    },
    /**更新IOT或者CSG列表的详情（包括总数，失败数，等） */
    updateIotOrCsgSyncDetail(id: string, detail: IStateGridOrderSyncDetailList[]) {
      const iotSyncs = this.iotSyncs.find((s: IIOTStateGridOrderSync) => s.id === id);
      if (iotSyncs) {
        iotSyncs.detail = detail;
      }
    }
  }
});
