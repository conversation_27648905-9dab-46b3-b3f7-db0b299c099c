import { IExperimentOriginData } from "@/models";

/** 这里原本是需要传入一个Array<IItem>类型的数据，但是IItem里面还有一些别的属性，但是在进行数据转化时并不需要用到这些属性
 * 因此修改为只需要具备string的time，具备string构成keyValue的points就可以进行转换
 * 不需要关注别的属性有还是没有
 */
type IHandleAutoDataType = Array<{
  time: string;
  points: Record<string, string>;
}>;
export interface IAutoCollectChart {
  deviceName?: string;
  collectPoint?: string;
  collectName?: string;
  values: IExperimentOriginData[];
}

/**
 * 处理数据采集点数据格式化
 * @params { data: IHandleAutoDataType[] }
 * @return IExperimentData[]
 * <AUTHOR>
 * @Date 2023/12/19
 */
export function handleAutoCollectCharts(data: IHandleAutoDataType, points: string[]): IAutoCollectChart[] {
  if (!data.length) {
    return [];
  }
  /** 处理采集点数据 */
  const charts = [];
  for (const key of points) {
    const deviceName = key;
    const collectPoint = key;
    charts.push({
      deviceName,
      collectPoint,
      values: getValues(data, key)
    });
  }
  return charts;
}

function getValues(data: IHandleAutoDataType, key: string) {
  const list = data.map(item => {
    return {
      timestamp: item.time,
      value: item.points[key]
    };
  });
  // 只有value满足以下条件才能返回
  return list.filter(({ value }) => value !== "" && value !== undefined && value !== null);
}
