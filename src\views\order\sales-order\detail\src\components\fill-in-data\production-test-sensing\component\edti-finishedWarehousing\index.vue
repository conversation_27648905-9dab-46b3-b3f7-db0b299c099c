<template>
  <div class="edit-raw-material-dialog">
    <NormalEditFinishedWarehousing mode="edit" ref="finishingWarehousingFormRef" v-if="isNormal" />
    <AcEditFinishedWarehousing ref="finishingWarehousingFormRef" v-else />
  </div>
</template>

<script setup lang="ts">
import NormalEditFinishedWarehousing, {
  IFinishingWarehousingForm
} from "../../finishing-warehousing/normal/finishing-warehousing-form.vue";
import AcEditFinishedWarehousing from "../../finishing-warehousing/armour-clamp/finishing-warehousing-form.vue";
import { inject, onMounted, ref } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { ICreateFinishedProductStorage } from "@/models";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

const finishingWarehousingFormRef = ref<IFinishingWarehousingForm | null>();
const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const isNormal = ref<boolean>(true);

onMounted(() => {
  isNormal.value = prodCtx?.detailMaterialCategory === EMaterialCategory.Normal;
});

/** 获取表单数据 */
const getFormValue = async (): Promise<boolean | ICreateFinishedProductStorage> => {
  return await finishingWarehousingFormRef.value.getFormValue();
};

defineExpose({
  getFormValue
});
</script>

<style scoped lang="scss"></style>
