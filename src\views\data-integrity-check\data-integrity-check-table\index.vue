<template>
  <div class="bg-bg_colo pb-4 flex">
    <SearchForm v-model="state.filter" :miss-data="state.missData" @clear="filterSearch" />
    <el-button class="ml-5" type="primary" @click="filterSearch" :loading="loading">搜索</el-button>
    <el-button class="ml-5" @click="handleReset" :loading="loading">重置</el-button>
  </div>
  <div class="flex justify-between items-end mb-2">
    <div class="text-slate-500 text-xs">
      说明：1）表格中数据统计及其同步状态，每日凌晨自动执行一次计算；2）仅国网订单支持【触发评分】操作
    </div>
    <div class="flex items-center">
      <transition name="fade">
        <div class="flex justify-between items-center mr-6 text-base" v-if="Boolean(selectedCount)">
          已选中:
          <span class="text-primary mx-4">
            {{ selectedCount }}
          </span>
          项
          <el-icon class="close ml-4" @click="clearSelection"><CircleCloseFilled /></el-icon>
        </div>
      </transition>
      <el-button
        :disabled="!isBatching"
        :loading="loading"
        @click="handleBatchSyncData"
        v-auth="PermissionKey.form.formPurchaseSyncBtnOneKey"
      >
        批量同步数据
      </el-button>
      <el-button
        :disabled="!isBatching"
        :loading="loading"
        @click="handleBatchTriggerScoring"
        v-auth="PermissionKey.form.formPurchaseTriggerScoreBtnOneKey"
      >
        批量触发评分
      </el-button>
      <ExportIntegrityBtn :condition="exportParams" />
    </div>
  </div>
  <div class="bg-bg_color flex flex-col flex-1 overflow-hidden" id="data-integrity-check-table">
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      :class="isBatching ? 'hide-page' : ''"
      size="large"
      :data="state.dataIntegrityCheckTableData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      @selection-change="selectChange"
      @page-current-change="requestTableList"
      @page-size-change="requestTableList"
      @sort-change="sortChange"
    >
      <template #soNos="{ row }">
        <nav-dialog
          v-if="row.salesIds && row.salesIds.includes(',')"
          pre-path="/sales-order"
          title="选择销售订单"
          :list="calcNavDialogList(row.soNos, row.salesIds)"
        >
          <template #default="{ open }">
            <div
              v-if="row.soNos"
              class="text-primary w-full overflow-hidden text-ellipsis cursor-pointer"
              @click="open"
            >
              {{ row.soNos }}
            </div>
          </template>
        </nav-dialog>
        <router-link
          v-else-if="row.salesIds && !row.salesIds.includes(',')"
          class="text-primary"
          :to="`/sales-order/${row.salesIds}`"
        >
          {{ row.soNos }}
        </router-link>
      </template>
      <template #poNos="{ row }">
        <nav-dialog
          v-if="row.purchaseIds && row.purchaseIds.includes(',')"
          pre-path="/purchase-order"
          title="选择采购订单"
          :list="calcNavDialogList(row.poNos, row.purchaseIds)"
        >
          <template #default="{ open }">
            <div
              v-if="row.poNos"
              class="text-primary w-full overflow-hidden text-ellipsis cursor-pointer"
              @click="open"
            >
              {{ row.poNos }}
            </div>
          </template>
        </nav-dialog>
        <router-link
          v-else-if="row.purchaseIds && !row.purchaseIds.includes(',')"
          class="text-primary"
          :to="`/purchase-order/${row.purchaseIds}`"
        >
          {{ row.poNos }}
        </router-link>
      </template>
      <template #ipoNo="{ row }">
        <div v-if="!row.salesIds && !row.purchaseIds">
          {{ row.ipoNo }}
        </div>
        <FillInProductionData
          v-else
          :production-id="row.productionId"
          :sale-order-id="calcOrderId(row.salesIds)"
          :purchase-order-id="calcOrderId(row.purchaseIds)"
          :mode="calcOrderId(row.salesIds) ? 'SALE' : 'PURCHASE'"
          loadingTarget="#data-integrity-check-table"
          @post-dialog-close="requestTableList"
        >
          <el-button link type="primary">{{ row.ipoNo }}</el-button>
        </FillInProductionData>
      </template>
      <template #operation="data">
        <div>
          <el-button type="primary" link :disabled="data.row.inCalculation || isBatching" @click="reCount(data.row)">
            {{ data.row.inCalculation ? "计算中..." : "重新计算" }}
          </el-button>
          <el-button
            :disabled="isBatching"
            type="primary"
            link
            @click="handleSingleSyncData(data.row)"
            v-auth="PermissionKey.form.formPurchaseSyncBtnOneKey"
          >
            一键同步
          </el-button>
          <el-button
            :disabled="isBatching"
            type="primary"
            link
            @click="handleSingleTriggerScoring(data.row)"
            v-auth="PermissionKey.form.formPurchaseTriggerScoreBtnOneKey"
          >
            触发评分
          </el-button>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { reactive, h, ref, onMounted, watch, computed } from "vue";
import { IDataIntegrityCheck, IDataIntegrityCheckParams, IDataIntegrityCheckTable, IOption } from "@/models";
import { useDataIntegrityCheckHook } from "../hooks/data-integrity-check-hook";
import { pickBy, forEach } from "lodash-unified";
import { TableWidth } from "@/enums";
import DataIntegrityCheckPopover from "../data-integrity-check-popover/index.vue";
import { cloneDeep } from "lodash-unified";
import SearchForm from "../data-integrity-check-search-form/index.vue";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useDataIntegrityCheckStore } from "@/store/modules";
import FillInProductionData from "@/views/components/fill-in-production-data/index.vue";
import { useTableSelect } from "./useTableSelect";
import {
  batchTriggerScoringByProduction,
  batchSyncByProduction
} from "@/api/data-integrity-check/data-integrity-check";
import { CircleCloseFilled } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { handleOrderType } from "@/utils/sortByOrderType";
import NavDialog from "@/components/nav-dialog/index.vue";
import { isEmpty } from "@pureadmin/utils";
import ExportIntegrityBtn from "./export-integrity-btn.vue";

const props = defineProps<{
  categoryCode: string;
}>();

const dataIntegrityCheckStore = useDataIntegrityCheckStore();
const subClassCode = computed(() => props.categoryCode);
const { selectChange, selectedCount, selectedList } = useTableSelect(subClassCode);

const selectedIds = computed(() => selectedList.value.map(item => item.productionId));

/** 是否处于批量操作中 */
const isBatching = computed(() => Boolean(selectedIds.value.length));

let { columns } = useColumns();
const { pagination } = useTableConfig();
const { queryDataIntegrityCheckDataList } = useDataIntegrityCheckHook();
const tableInstance = ref<PureTableInstance>();
const state = reactive<{
  dataIntegrityCheckTableData: Array<IDataIntegrityCheck>;
  missData: Array<IOption>;
  originColumns: TableColumnList;
  filter: {
    dataMissing: string[];
    keyWords: string;
    productionStatus: string;
    dataCheckEipSyncStatus: number;
    eipTriggerStatus: number;
  };
}>({
  dataIntegrityCheckTableData: [],
  originColumns: columns,
  missData: [],
  filter: {
    dataMissing: [],
    keyWords: "",
    productionStatus: "",
    dataCheckEipSyncStatus: null,
    eipTriggerStatus: null
  }
});
const loading = ref(false);

const sortParams = ref({
  orderByField: "",
  orderByType: ""
});

const calcOrderId = (ids: unknown) => {
  if (typeof ids === "string") {
    const tempArr = ids.split(",");
    if (tempArr.length) {
      return tempArr[0];
    }
    return ids;
  }
  return "";
};

/**
 * @description: 计算导航弹窗列表所需数据
 */
function calcNavDialogList(noListStr = "", idListStr = "") {
  const noArr = noListStr.split(",");
  const idArr = idListStr.split(",");
  return idArr.map((id, index) => {
    return {
      no: noArr[index],
      id
    };
  });
}

/**
 * @description: 请求表格数据
 */
const requestTableList = useLoadingFn(async () => {
  const dataIntegrityCheckTable: IDataIntegrityCheckTable | void = await queryDataIntegrityCheckDataList(queryParams());
  if (dataIntegrityCheckTable) {
    const headers = dataIntegrityCheckTable.headers;
    setColumnsAndMissData(headers);
    state.dataIntegrityCheckTableData = cloneDeep(dataIntegrityCheckTable.list || []);
    pagination.total = dataIntegrityCheckTable.total;
  }
}, loading);

/**
 * @description: 过滤搜索
 */
const filterSearch = () => {
  pagination.currentPage = 1;
  requestTableList();
};

const exportParams = computed(() => {
  return {
    ...state.filter,
    subClassCode: props.categoryCode
  };
});

/**
 * @description: 请求参数
 */
function queryParams() {
  const pageParams = { pageNo: pagination.currentPage, pageSize: pagination.pageSize, ...sortParams.value };
  const params: IDataIntegrityCheckParams = pickBy(
    Object.assign({}, state.filter, pageParams, { subClassCode: props.categoryCode }),
    value => !isEmpty(value)
  );
  return params;
}

/**
 * @description: 设置缺失的动态列
 */
function setColumnsAndMissData(headers: Record<string, string>) {
  state.missData.length = 0;
  const tempArr = [];
  forEach(headers, (label, key) => {
    // set columns
    tempArr.push({
      label: label,
      prop: key,
      minWidth: TableWidth.suborder,
      cellRenderer(data) {
        if (data.row.dataListObj[key]) {
          return h(DataIntegrityCheckPopover, {
            popoverData: data.row.dataListObj[key],
            productionId: data.row.productionId,
            purchaseOrderId: data.row.purchaseIds,
            saleOrderId: data.row.salesIds
          });
        }
        return null;
      }
    });
    // set missData
    state.missData.push({
      label: label,
      value: key
    });
  });
  columns = state.originColumns.concat(tempArr);
}

/**
 * @description: 重置过滤器
 */
const resetFilter = () => {
  state.filter.dataMissing = [];
  state.filter.keyWords = "";
  state.filter.productionStatus = "";
  state.filter.dataCheckEipSyncStatus = null;
  state.filter.eipTriggerStatus = null;
};

/**
 * @description: 重置
 */
const handleReset = () => {
  resetFilter();
  if (pagination.currentPage > 1) {
    pagination.currentPage = 1;
  }
  requestTableList();
};

/**
 * @description: 重新计算
 */
async function reCount(dataIntegrityCheck: IDataIntegrityCheck) {
  const message = ElMessage({
    message: `生产订单${dataIntegrityCheck.ipoNo}，正在计算中`,
    type: "warning",
    duration: 0
  });
  state.dataIntegrityCheckTableData.find(tableData => tableData.ipoNo === dataIntegrityCheck.ipoNo).inCalculation =
    true;
  const res = await dataIntegrityCheckStore.queryReCalculateByProductionId(dataIntegrityCheck.productionId);
  message.close();
  await requestTableList();
  if (res) {
    ElMessage.success(`生产订单${dataIntegrityCheck.ipoNo}计算完成`);
  }
}

const requestBatchSyncData = async (idList: string[], postRequestFn?: () => void) => {
  const { data: res } = await batchSyncByProduction({
    productionIds: idList
  });
  if (postRequestFn) {
    postRequestFn();
  }
  if (res) {
    ElMessage.success(`生产订单开始批量同步`);
  }
};

const requestBatchTriggerScoring = async (idList: string[], postRequestFn?: () => void) => {
  const { data: res } = await batchTriggerScoringByProduction({
    productionIds: idList
  });
  if (postRequestFn) {
    postRequestFn();
  }
  if (res) {
    ElMessage.success(`生产订单开始批量触发评分`);
  }
};

const requestSingleSyncData = async (item: IDataIntegrityCheck, postRequestFn?: () => void) => {
  const { data: res } = await batchSyncByProduction({
    productionIds: [item.productionId]
  });
  if (postRequestFn) {
    postRequestFn();
  }
  if (res) {
    ElMessage.success(`生产订单${item.ipoNo}开始同步`);
  }
};

const requestSingleTriggerScoring = async (item: IDataIntegrityCheck, postRequestFn?: () => void) => {
  const { data: res } = await batchTriggerScoringByProduction({
    productionIds: [item.productionId]
  });
  if (postRequestFn) {
    postRequestFn();
  }
  if (res) {
    ElMessage.success(`生产订单${item.ipoNo}开始触发评分`);
  }
};

/**
 * @description: 批量同步
 */
const handleBatchSyncData = async () => {
  const isConfirm = await ElMessageBox.confirm(
    `已选中 ${selectedIds.value.length} 条数据，是否批量执行同步?`,
    "批量同步",
    {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  );
  if (isConfirm) {
    ElNotification.info({
      title: "批量数据同步",
      message: `正在批量同步生产订单下的数据`,
      duration: 3000
    });
    const ids = [...selectedIds.value];
    clearSelection();
    await requestBatchSyncData(ids);
  }
};

const handleBatchTriggerScoring = async () => {
  const isConfirm = await ElMessageBox.confirm(
    `已选中 ${selectedIds.value.length} 条数据，是否批量执行触发评分?`,
    "批量触发评分",
    {
      type: "warning",
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  );
  if (isConfirm) {
    ElNotification.info({
      title: "批量触发评分",
      message: `正在批量触发生产订单下生产数据的质量评分`,
      duration: 3000
    });
    const ids = [...selectedIds.value];
    clearSelection();
    await requestBatchTriggerScoring(ids);
  }
};

/**
 * @description: 单项同步
 */
const handleSingleSyncData = async (item: IDataIntegrityCheck) => {
  const isConfirm = await ElMessageBox.confirm(`确定同步生产订单${item.ipoNo}下的数据吗?`, "数据同步", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  });
  if (!isConfirm) {
    return;
  }
  ElNotification.info({
    title: "数据同步",
    message: `正在同步生产订单${item.ipoNo}下的数据`,
    duration: 3000
  });
  await requestSingleSyncData(item);
};

/**
 * @description: 单项触发评分
 */
const handleSingleTriggerScoring = async (item: IDataIntegrityCheck) => {
  if (!item.actualFinishDate || !item.actualStartDate) {
    ElMessage.warning(`该生产订单缺失实际开始日期或实际结束日期，请填写后再触发评分`);
    return;
  }
  const isConfirm = await ElMessageBox.confirm(`确定触发生产订单${item.ipoNo}的评分吗?`, "触发评分", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  });
  if (!isConfirm) {
    return;
  }
  ElNotification.info({
    title: "触发评分",
    message: `正在触发生产订单${item.ipoNo}的质量评分`,
    duration: 3000
  });
  await requestSingleTriggerScoring(item);
};

function clearSelection() {
  tableInstance.value.getTableRef().clearSelection();
}

async function sortChange({ prop, order }) {
  if (order) {
    sortParams.value = {
      orderByField: prop,
      orderByType: handleOrderType(order)
    };
  } else {
    sortParams.value = {
      orderByField: "",
      orderByType: ""
    };
  }

  pagination.currentPage = 1;
  requestTableList();
}

onMounted(requestTableList);

watch(
  () => props.categoryCode,
  () => {
    resetFilter();
    if (pagination.currentPage > 1) {
      pagination.currentPage = 1;
    }
    requestTableList();
  }
);
</script>

<style scoped lang="scss">
.close {
  color: var(--el-text-color-placeholder);
  cursor: pointer;
}

.hide-page {
  :deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  :deep(.el-input__suffix-inner) {
    pointer-events: none;
  }
}
</style>
