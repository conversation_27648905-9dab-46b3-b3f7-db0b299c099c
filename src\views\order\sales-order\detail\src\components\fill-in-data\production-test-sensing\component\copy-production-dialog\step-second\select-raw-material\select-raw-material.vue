<template>
  <div class="select-product-raw-material">
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="rawMaterialListInProductOrder"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      showOverflowTooltip
      @row-click="rowClick"
      @selection-change="selectionChange"
      @page-current-change="pageNoChange"
      @page-size-change="pageSizeChange"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { IProductOrder } from "@/models/product-order";
import { inject, onMounted, ref } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCopyProductOrderStore, useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing";
import { IOrdersForChooseReq, IRawMaterialFromOrderRes } from "@/models";
import { ProductOrWorkOrderEnum } from "@/enums/purchase-order";
import { COPY_FROM_ORDER_TOKEN } from "../../tokens";

const props = defineProps<{
  productInfo?: IProductOrder;
  modelValue?: IRawMaterialFromOrderRes[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", data: IRawMaterialFromOrderRes[]);
}>();

const copyCtx = inject(COPY_FROM_ORDER_TOKEN);
const copyProductOrderStore = useCopyProductOrderStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const { pagination } = useTableConfig();
const queryParams = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize,
  orderIds: [props.productInfo?.id]
};
const { columns } = useColumns();
const tableInstance = ref<PureTableInstance>();
const rawMaterialListInProductOrder = ref<Array<IRawMaterialFromOrderRes>>([]);
const loading = ref<boolean>(false);
const initRawMaterialInspectList = useLoadingFn(getRawMaterialInspectList, loading);

onMounted(() => {
  initRawMaterialInspectList(queryParams);
});

/** 获取原材料检测数据 */
async function getRawMaterialInspectList(params: IOrdersForChooseReq) {
  const orderType = copyCtx.isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
  // 生产订单id
  const productOrderId: string = productionTestSensingStore.dataId;
  const res = await copyProductOrderStore.getRawMaterialInspectFromOrder({
    ...params,
    orderType,
    currentOrderId: productOrderId
  });
  rawMaterialListInProductOrder.value = res.list || [];
  pagination.total = res.total || 0;
}

/** 点击行 */
function rowClick(row: IRawMaterialFromOrderRes) {
  tableInstance.value?.getTableRef()?.toggleRowSelection(row, undefined);
}

/** 页码 */
function pageNoChange(pageNo: number) {
  Object.assign(queryParams, { pageNo });
  initRawMaterialInspectList(queryParams);
}

/** 页码数量 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  Object.assign(queryParams, { pageNo: pagination.currentPage, pageSize });
  initRawMaterialInspectList(queryParams);
}

/** 选择切换  */
function selectionChange(val: IRawMaterialFromOrderRes[]) {
  emit("update:modelValue", val);
}
</script>

<style scoped lang="scss"></style>
