<template>
  <div class="overflow-hidden w-full flex flex-col flex-1 h-[400px]">
    <!-- 搜索条 -->
    <div class="py-2 px-6 text-right">
      <add-edit-inspection-standard-dialog
        v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationAddStandard"
        mode="add"
        :category-code="categoryCode"
        :production-stage-id="productionStageId"
        :quality-id="qualityId"
        @post-save-success="saveSuccess"
      >
        <template #trigger="{ openDialog }">
          <el-button type="primary" :icon="Plus" @click="openDialog">新增</el-button>
        </template>
      </add-edit-inspection-standard-dialog>
    </div>
    <!-- 表格 -->
    <div class="px-5 pb-4 flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @sort-change="handleSortChange"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #opertion="{ row }">
          <add-edit-inspection-standard-dialog
            mode="edit"
            :category-code="categoryCode"
            :production-stage-id="productionStageId"
            :quality-id="qualityId"
            :id="row.id"
            @post-save-success="saveSuccess"
          >
            <template #trigger="{ openDialog }">
              <el-button
                v-auth="PermissionKey.qualityTracing.qualityTracingSpecificationEditStandard"
                link
                type="primary"
                @click="openDialog"
                >编辑</el-button
              >
            </template>
          </add-edit-inspection-standard-dialog>
          <delete-inspection-standard-btn :id="row.id" @post-delete-success="saveSuccess" />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getQualitySpecificationParticularDetailList } from "@/api/quality-tracing/quality-specification-particular-detail";
import { PureTable } from "@pureadmin/table";
import { ProductionStageCodeToQualitySpecificationCategoryEnum } from "@/enums/quality-specification";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import AddEditInspectionStandardDialog from "../add-edit-inspection-standard-dialog/index.vue";
import DeleteInspectionStandardBtn from "../delete-inspection-standard-btn.vue";
import { useTableSort } from "@/utils/use-table-sort";
import { QualitySpecificationParticularDetailListItem } from "@/models/quality-tracing";

/**
 * 质量规范明细 阶段通用明细列表
 */

const props = defineProps<{
  productionStageId: string;
  /** 质量规范id */
  qualityId: string;
  /** 分类code */
  categoryCode: ProductionStageCodeToQualitySpecificationCategoryEnum;
}>();

const emits = defineEmits(["refreshScore"]);

const { pagination } = useTableConfig();
const { sortParams, handleSortChange } = useTableSort(reloadList);
const { columnsConfig } = genQualitySpecificationTableColumnsConfig();
const loading = ref(false);
const list = ref<Array<QualitySpecificationParticularDetailListItem>>([]);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await getQualitySpecificationParticularDetailList(
    {
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      orderByField: sortParams.value.orderByField,
      orderByType: sortParams.value.orderByType,
      category: props.categoryCode
    },
    props.qualityId
  );
  list.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}

/**
 * @description: 保存成功时刷新
 */
function saveSuccess() {
  requestList();
  emits("refreshScore");
}

onMounted(requestList);
</script>

<style scoped lang="scss"></style>
