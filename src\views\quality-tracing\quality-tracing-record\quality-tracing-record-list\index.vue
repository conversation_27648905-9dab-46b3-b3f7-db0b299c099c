<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 表格 -->
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <!-- 搜索条 -->
      <search-bar class="bg-bg_color pb-4" ref="searchBarRef" @handle-search="updateSearchParams" />
      <div class="flex justify-between mb-3">
        <level-tab ref="levelTabRef" v-model="currentLevel" />
        <batch-matching-btn />
      </div>
      <PureTable
        ref="tableRef"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @sort-change="handleSortChange"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #dataNo="{ row }">
          <!-- 线缆 走订单路线 -->
          <fill-in-production-data
            v-if="isCableBySubClassCode(row.subClassCode)"
            :sale-order-id="calcSaleOrderId(row.soIds)"
            :production-id="row.dataId"
            @post-dialog-close="requestList"
            mode="SALE"
          >
            <el-button link type="primary">{{ row.dataNo }}</el-button>
          </fill-in-production-data>
          <!-- 非线缆 走工单路线 -->
          <fill-in-production-data
            v-else
            :sale-order-id="calcSaleOrderId(row.soIds)"
            :production-id="row.productionId"
            :work-order-id="row.dataId"
            @post-dialog-close="requestList"
            mode="SALE"
          >
            <el-button link type="primary">{{ row.dataNo }}</el-button>
          </fill-in-production-data>
        </template>
        <template #soNos="{ row }">
          <nav-dialog
            v-if="row.soIds && row.soIds.includes(',')"
            pre-path="/sales-order"
            title="选择销售订单"
            :list="calcNavDialogList(row.soNos, row.soIds)"
          >
            <template #default="{ open }">
              <div
                v-if="row.soNos"
                class="text-primary w-full overflow-hidden text-ellipsis cursor-pointer"
                @click="open"
              >
                {{ row.soNos }}
              </div>
            </template>
          </nav-dialog>
          <router-link
            v-else-if="row.soIds && !row.soIds.includes(',')"
            class="text-primary"
            :to="`/sales-order/${row.soIds}`"
          >
            {{ row.soNos }}
          </router-link>
        </template>
        <template #poNos="{ row }">
          <nav-dialog
            v-if="row.poIds && row.poIds.includes(',')"
            pre-path="/purchase-order"
            title="选择采购订单"
            :list="calcNavDialogList(row.poNos, row.poIds)"
          >
            <template #default="{ open }">
              <div
                v-if="row.poNos"
                class="text-primary w-full overflow-hidden text-ellipsis cursor-pointer"
                @click="open"
              >
                {{ row.poNos }}
              </div>
            </template>
          </nav-dialog>
          <router-link
            v-else-if="row.poIds && !row.poIds.includes(',')"
            class="text-primary"
            :to="`/purchase-order/${row.poIds}`"
          >
            {{ row.poNos }}
          </router-link>
        </template>
        <template #opertion="{ row }">
          <router-link v-if="row.id" :to="`/quality-tracing-record/${row.id}`">
            <el-button link type="primary">详情</el-button>
          </router-link>
          <select-quality-specification
            :id="row.dataId"
            :quality-id="row.qualityId"
            :sub-class-code="row.subClassCode"
            @post-save-success="reloadList"
          />
          <gen-tracing-record-btn
            v-if="!row.calculating && row.qualityId"
            :label="row.id ? '重新生成' : '生成记录'"
            :id="row.dataId"
            :sub-class-code="row.subClassCode"
            :quality-id="row.qualityId"
            @post-gen-success="requestList"
          />
          <el-button v-if="row.calculating && row.qualityId" link disabled type="primary">生成中...</el-button>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { RouterLink } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getQualityTracingRecordList } from "@/api/quality-tracing";
import { PureTable } from "@pureadmin/table";
import { useTableSort } from "@/utils/use-table-sort";
import { QualityTracingRecordListParams, QualityTracingRecordListItem } from "@/models/quality-tracing";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import SearchBar from "./search-bar.vue";
import LevelTab from "./level-tab.vue";
import BatchMatchingBtn from "./batch-matching-btn.vue";
import SelectQualitySpecification from "../select-quality-specification/index.vue";
import GenTracingRecordBtn from "../gen-tracing-record-btn.vue";
import NavDialog from "@/components/nav-dialog/index.vue";
import FillInProductionData from "@/views/components/fill-in-production-data/index.vue";
import { useMaterial } from "@/utils/material";

/**
 * 质量追溯记录列表
 */

const props = defineProps<{
  subClassCode: string;
}>();

const searchBarRef = ref<InstanceType<typeof SearchBar>>();
const levelTabRef = ref<InstanceType<typeof LevelTab>>();
const tableRef = ref<PureTableInstance>();
const { pagination } = useTableConfig();
const { columnsConfig } = genQualitySpecificationTableColumnsConfig(computed(() => props.subClassCode));
const { isCableBySubClassCode } = useMaterial();
const list = ref<QualityTracingRecordListItem[]>([]);
/** 当前等级 */
const currentLevel = ref("");
const searchParams = ref({
  /** 搜索关键字 */
  keyWord: "",
  dateFrom: "",
  dateTo: ""
});

const loading = ref(false);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: QualityTracingRecordListParams = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    subClassCode: props.subClassCode,
    qualityLevelId: currentLevel.value,
    ...searchParams.value,
    ...sortParams.value
  };
  const { data } = await getQualityTracingRecordList(params);
  list.value = data.list;
  pagination.total = data.total;
}, loading);

const { sortParams, handleSortChange } = useTableSort(reloadList);

/**
 * @description: 计算销售订单ID
 */
function calcSaleOrderId(ids: string) {
  return ids.split(",")[0];
}

/**
 * @description: 计算导航弹窗列表所需数据
 */
function calcNavDialogList(noListStr = "", idListStr = "") {
  const noArr = noListStr.split(",");
  const idArr = idListStr.split(",");
  return idArr.map((id, index) => {
    return {
      no: noArr[index],
      id
    };
  });
}

/**
 * @description: 更新搜索参数
 */
function updateSearchParams(params: { keyWord: string; dateRange: [string, string] }) {
  searchParams.value = {
    keyWord: params.keyWord,
    dateFrom: params.dateRange ? params.dateRange[0] : "",
    dateTo: params.dateRange ? params.dateRange[1] : ""
  };
}

/**
 * @description: 重载列表
 */
function reloadList() {
  pagination.currentPage = 1;
  requestList();
}
// 当物资种类发生,重置搜索条件
watch(
  () => props.subClassCode,
  code => {
    if (!code) {
      return;
    }
    searchBarRef.value?.resetSearchBar();
  }
);

// 当搜索条件发生变化时，重置激活的等级
watch(searchParams, async () => {
  // 先清空当前激活的等级
  currentLevel.value = "";
  // 重新请求等级列表
  await levelTabRef.value?.requestLevelList({
    subClassCode: props.subClassCode,
    ...searchParams.value
  });
});

// 当 激活的 等级 发生变化时，重新请求列表
watch(currentLevel, level => {
  if (!level) {
    return;
  }
  sortParams.value.orderByField = "";
  sortParams.value.orderByType = "";
  tableRef.value?.getTableRef().clearSort();
  reloadList();
});
</script>

<style scoped lang="scss"></style>
