<template>
  <div class="px-6 pb-2 bg-bg_color">
    <SaleOrderHeader :sales-order-detail="salesOrderDetailStore.salesOrder" />
  </div>
  <div class="flex-1 mx-6 overflow-hidden">
    <div class="py-5 flex gap-5 h-full">
      <Step />
      <Component :is="component" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePageStoreHook } from "@/store/modules/page";
import SaleOrderHeader from "./src/header/index.vue";
import { computed, h, onMounted, onUnmounted, watch, reactive } from "vue";
import Step from "./src/step/step.vue";
import SalesOrderLine from "./src/sales-order-line/index.vue";
import ProductionSchedule from "./src/production-schedule/production-schedule.vue";
import ProductionOrder from "./src/production-order/production-order.vue";
import ProductionData from "./src/production-data/production-data.vue";
import SGOrder from "./src/sg-order/sg-order.vue";
import SGRate from "./src/sg-rate/sg-rate.vue";
import { useSalesOrderDetailStore, useSalesOrderManagementStore } from "@/store/modules";
import { ISalesOrder } from "@/models";
import { useRoute } from "vue-router";
import { SalesLinkStepEnum } from "@/enums";
import OrderStarProgress from "./src/header/order-star-progress.vue";

const route = useRoute();
const pageStore = usePageStoreHook();
const salesOrderDetailStore = useSalesOrderDetailStore();
const salesOrderManagementStore = useSalesOrderManagementStore();

let pageTitle = `销售订单`;
pageStore.setTitle(pageTitle);
const state = reactive<{
  salesOrderDetail: ISalesOrder;
  loading: boolean;
}>({
  salesOrderDetail: {} as ISalesOrder,
  loading: false
});

onMounted(async () => {
  const id = `${route.params?.id}`;
  salesOrderManagementStore.selectId = id;
  salesOrderDetailStore.setSaleOrderId(id);
  await salesOrderDetailStore.refreshSalesOrder();
  salesOrderDetailStore.refreshStepStatus();
  state.salesOrderDetail = salesOrderDetailStore.salesOrder;
});

const component = computed(() => getComponent(salesOrderDetailStore.activeStepKey));

function getComponent(key: SalesLinkStepEnum) {
  switch (key) {
    case SalesLinkStepEnum.SALES_LINE:
      return SalesOrderLine;
    case SalesLinkStepEnum.PRODUCTION_PLAN:
      return ProductionSchedule;
    case SalesLinkStepEnum.PRODUCTION_ORDER:
      return ProductionOrder;
    case SalesLinkStepEnum.PRODUCTION_DATA:
      return ProductionData;
    case SalesLinkStepEnum.SYNC_ORDER:
      return SGOrder;
    case SalesLinkStepEnum.QUALITY_EVAL:
      return SGRate;
    default: {
      const _exhaustiveCheck: never = key;
      return null;
    }
  }
}

watch(
  () => state.salesOrderDetail?.follow,
  () => {
    pageTitle = `销售订单 ${state.salesOrderDetail?.soNo ?? ""}`;
    pageStore.setTitle(pageTitle);
    pageStore.setComponent(h(OrderStarProgress));
  }
);

onUnmounted(() => {
  pageStore.$reset();
  salesOrderDetailStore.$reset();
});
</script>

<style lang="scss" scoped>
.el-divider {
  @apply my-1;
}
</style>
