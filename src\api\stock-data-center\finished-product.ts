import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models/response";
import {
  IFinishedProduct,
  IFinisedProductReq,
  IPurchaseOrderList,
  ISearchFinisedProduct,
  ISearchPurchaseOrderReq
} from "@/models/stock-data-center/i-finished-product";

/**
 * 获取产成品库存列表
 */
export function getFinishedProductList(queryParams: ISearchFinisedProduct) {
  const url = withApiGateway(`admin-api/business/inventory/getManufacturedInventoryAll`);
  return http.post<ISearchFinisedProduct, IListResponse<IFinishedProduct>>(url, {
    data: queryParams
  });
}

/**
 * 新增产成品
 */
export function saveFinishedProduct(paramsData: IFinisedProductReq) {
  const url = withApiGateway(`admin-api/business/inventory/addManufacturedInventory`);
  return http.post<IFinisedProductReq, IResponse<boolean>>(
    url,
    {
      data: paramsData
    },
    { showErrorInDialog: true }
  );
}

/**
 * 通过id修改产成品数据
 */
export function putFinishedProductInfo(paramsData: IFinisedProductReq) {
  const url = withApiGateway(`admin-api/business/inventory/updateManufacturedInventory/${paramsData?.id}`);
  return http.put<IFinisedProductReq, IResponse<boolean>>(
    url,
    {
      data: paramsData
    },
    { showErrorInDialog: true }
  );
}

/**
 * 删除产成品数据
 */
export function delFinishedProduct(id: string) {
  const url = withApiGateway(`admin-api/business/inventory/deleteManufacturedInventoryById/${id}`);
  return http.delete<string, IResponse<boolean>>(url);
}

/**
 * 通过Id获取产成品详情数据
 */
export function getDetailFinishedProductById(id: string) {
  const url = withApiGateway(`admin-api/business/inventory/getManufacturedInventoryById/${id}`);
  return http.get<void, IResponse<IFinishedProduct>>(url);
}

/**
 * 获取国网采购订单
 */
export function getPurchaseLinePage(paramsData: ISearchPurchaseOrderReq) {
  const url = withApiGateway(`admin-api/business/purchase/line-page`);
  return http.get<void, IListResponse<IPurchaseOrderList>>(url, {
    params: paramsData
  });
}
