import {
  IOTSyncStatusEnum,
  MonitorDeviceBrandEnum,
  MonitorDeviceStatusEnum,
  MonitorDeviceSubTypeEnum,
  MonitorDeviceRunningStatusEnum
} from "@/enums";

/**  监控设备 */
export interface IMonitorDevice {
  id: string;
  /** 设备编号  */
  no: string;

  /** 设备名称 */
  name: string;

  /** NVR IP地址 */
  nvrIp: string;

  /** 状态 */
  status: MonitorDeviceStatusEnum;

  /** 品牌 */
  brandCode: MonitorDeviceBrandEnum;

  /** 通道号 */
  channel: number;

  /** 账号 */
  account: string;

  /** 密码 */
  password: string;

  /** 端口号 */
  port: number;

  /** 分组 */
  groupName: string;

  /** 码流类型 */
  subType: MonitorDeviceSubTypeEnum;

  /** 排序 */
  orderNum: number;

  /** 备注 */
  remark: string;

  /** 设备分组ID */
  groupId: string;

  /** iot同步状态 */
  iotSyncStatus: IOTSyncStatusEnum;

  /** iot同步结果 */
  syncResult: string;

  /** 最后同步时间 */
  lastSyncTime: string;

  /** 是否开启转码 */
  shouldTranscode: boolean;

  runningStatus?: MonitorDeviceRunningStatusEnum;
}
