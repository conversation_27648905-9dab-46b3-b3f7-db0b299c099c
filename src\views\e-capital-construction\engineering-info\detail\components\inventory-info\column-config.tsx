import {
  ColumnWidth,
  TableWidth,
  GisComponentEnumOptions,
  GisComponentEnumMapDesc,
  OriginTypeEnumOptions,
  OriginTypeEnumMapDesc,
  SourceTypeEnumOptions,
  SourceTypeEnumMapDesc,
  ComponentInventoryEnumMapDesc,
  ComponentInventoryEnumOptions
} from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 库存管理
 */
export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig = [];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "原材料组部件类型",
        prop: "gisComponentType",
        formType: "select",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        options: GisComponentEnumOptions,
        width: ColumnWidth.Char10,
        cellRenderer: (data: TableColumnRenderer) => {
          return GisComponentEnumMapDesc[data.row.gisComponentType];
        }
      },
      {
        label: "批次编号",
        prop: "batchNumber",
        formType: "text",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char8
      },
      {
        label: "原材料组部件供应商编码",
        prop: "rawMaterialComponentSupplierCode",
        formType: "text",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char12
      },
      {
        label: "原材料组部件供应商全称",
        prop: "rawMaterialComponentSupplierName",
        formType: "text",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char12
      },
      {
        label: "来源类型",
        prop: "sourceType",
        formType: "radio",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        options: SourceTypeEnumOptions,
        width: ColumnWidth.Char8,
        cellRenderer: (data: TableColumnRenderer) => {
          return SourceTypeEnumMapDesc[data.row.sourceType];
        }
      },
      {
        label: "产地类型",
        prop: "originType",
        formType: "radio",
        canEdit: true,
        isFormShow: true,
        isRequire: true,
        options: OriginTypeEnumOptions,
        width: ColumnWidth.Char10,
        cellRenderer: (data: TableColumnRenderer) => {
          return OriginTypeEnumMapDesc[data.row.originType];
        }
      },
      {
        label: "批次数量",
        prop: "batchQuantity",
        formType: "number",
        canEdit: true,
        isFormShow: true,
        width: ColumnWidth.Char10
      },
      {
        label: "国产计划供给数量",
        prop: "plannedSupplyQuantityDomestic",
        formType: "number",
        canEdit: true,
        isFormShow: true,
        width: ColumnWidth.Char10
      },
      {
        label: "进口计划供给数量",
        prop: "plannedSupplyQuantityImported",
        formType: "number",
        canEdit: true,
        isFormShow: true,
        width: ColumnWidth.Char10
      },
      {
        label: "同步状态",
        prop: "pullStatus",
        formType: "number",
        canEdit: false,
        isRequire: false,
        isFormShow: false,
        width: ColumnWidth.Char10,
        slot: "pullStatus"
      },
      {
        label: "创建时间",
        prop: "createTime",
        canEdit: false,
        isFormShow: false,
        width: ColumnWidth.Char10,
        formatter: dateFormatter()
      },
      {
        label: "操作",
        prop: "operation",
        fixed: "right",
        isFormShow: false,
        slot: "operation",
        canEdit: false,
        width: TableWidth.operation
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "原材料组部件类型",
        prop: "componentType",
        formType: "select",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        options: ComponentInventoryEnumOptions,
        width: ColumnWidth.Char10,
        cellRenderer: (data: TableColumnRenderer) => {
          return ComponentInventoryEnumMapDesc[data.row.componentType];
        }
      },
      {
        label: "本工程需求量",
        prop: "projectDemandQuantity",
        formType: "number",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char8
      },
      {
        label: "本工程协议订单数量（国产）",
        prop: "projectOrderQuantityDomestic",
        formType: "number",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char14
      },
      {
        label: "本工程协议订单数量（进口）",
        prop: "projectOrderQuantityImported",
        formType: "number",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char14
      },
      {
        label: "本工程实际库存数量（国产）",
        prop: "projectInventoryQuantityDomestic",
        formType: "number",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char14
      },
      {
        label: "本工程实际库存数量（进口）",
        prop: "projectInventoryQuantityImported",
        formType: "number",
        canEdit: true,
        isRequire: true,
        isFormShow: true,
        width: ColumnWidth.Char14
      },
      {
        label: "同步状态",
        prop: "pullStatus",
        isFormShow: false,
        minWidth: ColumnWidth.Char10,
        slot: "pullStatus"
      },
      {
        label: "创建时间",
        prop: "createTime",
        canEdit: false,
        isFormShow: false,
        width: ColumnWidth.Char10,
        formatter: dateFormatter()
      },
      {
        label: "操作",
        prop: "operation",
        fixed: "right",
        isFormShow: false,
        slot: "operation",
        canEdit: false,
        width: TableWidth.operation
      }
    ];
  }
  return columnsConfig;
}
