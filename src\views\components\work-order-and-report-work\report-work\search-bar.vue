<template>
  <el-form :model="formValue" :inline="true">
    <el-form-item label="报工批次号:" prop="productBatchNo">
      <el-input v-model="formValue.productBatchNo" class="!w-52" placeholder="请输入报工批次号" clearable />
    </el-form-item>
    <el-form-item v-if="isProductionOrder" label="工单:" prop="productBatchNo">
      <work-order-selector
        :production-id="productionId"
        v-model="formValue.workId"
        class="!w-52"
        placeholder="请输入工单编号"
        clearable
      />
    </el-form-item>
    <el-form-item label="工序:" prop="processId">
      <process-single-selector v-model="formValue.processId" :sub-class-code="subclassCode" />
    </el-form-item>
    <el-form-item label="设备:" prop="deviceId">
      <device-selector v-model="formValue.deviceId" :process-id="formValue.processId" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">搜索</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, computed } from "vue";
import { IReportWorkReq } from "@/models";
import { useMaterial as genMaterialTool } from "@/utils/material";
import DeviceSelector from "@/views/components/device-selector/index.vue";
import ProcessSingleSelector from "@/views/components/process-selector/process-single-selector.vue";
import WorkOrderSelector from "@/views/components/work-order-selector/index.vue";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

const props = defineProps<{
  subclassCode: string;
  productionId: string;
}>();

const materialTool = genMaterialTool();

const formValue = reactive<IReportWorkReq>({
  workId: "",
  productBatchNo: "",
  deviceId: "",
  processId: ""
});

const emits = defineEmits<{
  (event: "searchForm", value: IReportWorkReq): void;
}>();

// 判断是否是生产订单，生产订单需要显示工单筛选
const isProductionOrder = computed(() => materialTool.isCableBySubClassCode(props.subclassCode));
const search = () => {
  emits(
    "searchForm",
    omitBy(formValue, value => isAllEmpty(value))
  );
};

const reset = () => {
  formValue.productBatchNo = "";
  formValue.deviceId = "";
  formValue.processId = "";
  formValue.workId = "";
  emits(
    "searchForm",
    omitBy(formValue, value => isAllEmpty(value))
  );
};
</script>

<style scoped lang="scss"></style>
