import { dateFormat, fullDateFormat } from "@/consts";
import { ElRadio, ElRadioGroup, RadioGroupProps, TableColumnCtx } from "element-plus";
import { formatDate, formatDecimal } from "@/utils/format";
import { h, Ref } from "vue";
import CxTag from "@/components/CxTag/index.vue";
import EnumTag from "@/components/EnumTag";
import { ESyncStockStatus } from "@/models/stock-data-center/stock-sync/i-sync-stock";
import { DictionaryName } from "@/components/Dictionary";

export const useTableCellFormatter = () => {
  const dateFormatter =
    (format: string = fullDateFormat) =>
    (row: any, column: TableColumnCtx<any>, cellValue: string) =>
      formatDate(cellValue, format);

  const dateRangeFormatter =
    (startField: string, endField: string, format: string = dateFormat) =>
    row => {
      const start = row[startField];
      const end = row[endField];
      if (!start && !end) {
        return null;
      }
      return `${formatDate(start, format) || ""} ~ ${formatDate(end, format) || ""}`;
    };

  const statusFormatter =
    (successText: string, failText: string, defaultText?: string) =>
    (row: any, column: TableColumnCtx<any>, cellValue: boolean) => {
      if (cellValue === true) {
        return h(CxTag, { type: "success", icon: "icon-success-fill" }, () => successText);
      } else if (cellValue === false) {
        return h(CxTag, { type: "danger", icon: "icon-fail-fill" }, () => failText);
      }
      if (defaultText != null) {
        return h(CxTag, { type: "info" }, () => defaultText);
      }
      return null;
    };

  const enumFormatter =
    (enumeration: Record<string, string | number>, enumName: string) =>
    (row: any, column: TableColumnCtx<any>, cellValue: string | number) => {
      if (cellValue == null) {
        return null;
      }
      return h(EnumTag, { enum: enumeration, enumName: enumName, value: cellValue });
    };

  const singleSelectFormatter =
    <T extends string | number>(selected: Ref<T>, attr?: Partial<RadioGroupProps>) =>
    (row: any, column: TableColumnCtx<any>, value: T) => {
      const radio = h(ElRadio, { label: value, style: { margin: "0 !important", width: "14px" } }, () => "");
      return h(ElRadioGroup, { modelValue: selected.value, ...(attr || {}) }, () => radio);
    };

  const mapFormatter =
    (map: Record<string | number, any>) => (row: any, column: TableColumnCtx<any>, cellValue: string | number) =>
      map[cellValue] || cellValue;

  const syncStatusFormatter =
    (successText: string, failText: string, syncingText: string, defaultText?: string) =>
    (row: any, column: TableColumnCtx<any>, cellValue: string) => {
      if (cellValue === ESyncStockStatus.SUCCEED_SYNC) {
        return h(CxTag, { type: "success", icon: "icon-success-fill" }, () => successText);
      } else if (cellValue === ESyncStockStatus.FAIL_SYNC) {
        return h(CxTag, { type: "danger", icon: "icon-fail-fill" }, () => failText);
      } else if (cellValue === ESyncStockStatus.TRANSFERRING) {
        return h(CxTag, { type: "neutral", icon: "icon-sync" }, () => syncingText);
      } else if (cellValue === ESyncStockStatus.NO_SYNC) {
        return h(CxTag, { type: "info" }, () => defaultText);
      }
      return null;
    };

  const dictionaryFormatter =
    (dictCode: string, subClassCodeField: string) => (row: any, column: TableColumnCtx<any>, cellValue: string) => {
      const subClassCode = row[subClassCodeField];
      return h(DictionaryName, { parentCode: dictCode, subClassCode, code: cellValue });
    };

  const withUnitFormatter =
    (unitField: string, subClassCodeField: string, dictCode: string) =>
    (row: any, column: TableColumnCtx<any>, cellValue: number) => {
      const unit = row[unitField];
      const subClassCode = row[subClassCodeField];
      if (!cellValue && typeof cellValue !== "number") {
        return null;
      }
      return h("span", [
        formatDecimal(cellValue),
        h(DictionaryName, { parentCode: dictCode, subClassCode, code: unit, class: "ml-1" })
      ]);
    };

  const decimalFormatter =
    (digitsInfo?: string, locales?: Intl.LocalesArgument) =>
    (row: any, column: TableColumnCtx<any>, cellValue: number) =>
      formatDecimal(cellValue, digitsInfo, locales);

  const statusCodeFormatter = () => (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    const success: boolean = cellValue >= 200 && cellValue < 300;
    return success ? h(CxTag, { type: "success" }, () => "成功") : h(CxTag, { type: "danger" }, () => "失败");
  };

  /**
   * @description: 百分比
   */
  const percentFormatter = (row: any, column: TableColumnCtx<any>, cellValue: number) => {
    return formatDecimal(cellValue, "1.0-2") + "%";
  };

  return {
    dateFormatter,
    dateRangeFormatter,
    enumFormatter,
    statusFormatter,
    singleSelectFormatter,
    mapFormatter,
    syncStatusFormatter,
    dictionaryFormatter,
    withUnitFormatter,
    decimalFormatter,
    statusCodeFormatter,
    percentFormatter
  };
};
