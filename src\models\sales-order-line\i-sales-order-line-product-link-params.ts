import { SaleOrderLineStateEnum } from "@/enums";
import { IPagingReq } from "@/models";

export interface ISalesOrderLineBaseProductLinkParams extends IPagingReq {
  /** 物资种类编码 */
  subclassCode: string;
  /** 生成订单状态 */
  productionStatus: SaleOrderLineStateEnum;
  /** 关键字 */
  keyWords?: string;
  /** 物料id */
  marterialId?: string;
  /** 生产订单ID */
  productionId?: string;
  /** 是否跨订单 */
  crossOrder?: boolean;
}

export interface ISalesOrderLinePurchaseProductLinkParams extends ISalesOrderLineBaseProductLinkParams {
  purchaseId: string;
}

export interface ISalesOrderLineSalesProductLinkParams extends ISalesOrderLineBaseProductLinkParams {
  salesId: string;
}

export type ISalesOrderLineProductLinkParams =
  | ISalesOrderLineBaseProductLinkParams
  | ISalesOrderLinePurchaseProductLinkParams
  | ISalesOrderLineSalesProductLinkParams;
