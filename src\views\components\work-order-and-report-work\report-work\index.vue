<template>
  <div class="flex flex-col h-[420px]">
    <div class="flex justify-between flex-wrap items-center">
      <search-bar :production-id="productionId" :subclass-code="subClassCode" @search-form="onFilterParamsChange" />
      <div class="flex gap-2 items-center mb-[1.125rem] pl-5">
        <batch-delete
          v-auth="PermissionKey.form.formPurchaseWorkReportDelete"
          :selected-id-list="selectedList"
          @clear-selection="clearSelection"
          @post-delete-success="postDeleteSuccess"
        />
        <add-edit-report-work-dialog
          mode="add"
          is-need-work-order-selector
          :work-id="isProductionOrder ? '' : props.workId"
          :subClassCode="subClassCode"
          :production-id="productionId"
          :work-order-selector-disabled="!isProductionOrder"
          @post-save-success="requestReportWorkList"
        >
          <template #default="{ openDialog }">
            <el-button
              :disabled="Boolean(selectedList.length)"
              v-auth="PermissionKey.form.formPurchaseWorkReportCreate"
              type="primary"
              :icon="Plus"
              @click="openDialog"
            >
              新增报工
            </el-button>
          </template>
        </add-edit-report-work-dialog>
      </div>
    </div>
    <PureTable
      ref="tableRef"
      showOverflowTooltip
      :class="selectedList.length ? 'hide-page' : ''"
      :loading="loading"
      :pagination="pagination"
      :data="dataList"
      :columns="columns"
      @page-current-change="requestReportWorkList"
      @page-size-change="requestReportWorkList"
      @sort-change="sortChange"
      @select="handleSelect"
      @select-all="handleSelect"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <empty-data /> </template>
        </el-empty>
      </template>
      <template #ProductBatchNo="{ row }">
        <ReportWorkLogDialog :id="row.id">
          <template #report-work> {{ row.productBatchNo }}</template>
        </ReportWorkLogDialog>
      </template>
      <template #woNo="{ row }">
        <work-order-detail-dialog :work-id="row.workId">
          <el-button link type="primary">{{ row.woNo }}</el-button>
        </work-order-detail-dialog>
      </template>
      <template #operation="{ row }">
        <add-edit-report-work-dialog
          mode="edit"
          work-order-selector-disabled
          :work-id="row.workId"
          :production-id="productionId"
          :report-work-id="row.id"
          :sub-class-code="subClassCode"
          @post-save-success="requestReportWorkList"
        >
          <template #default="{ openDialog }">
            <el-button
              v-auth="PermissionKey.form.formPurchaseWorkReportEdit"
              :disabled="Boolean(selectedList.length)"
              link
              type="primary"
              @click="openDialog"
            >
              编辑
            </el-button>
          </template>
        </add-edit-report-work-dialog>
        <el-button
          class="ml-2"
          link
          type="danger"
          :disabled="Boolean(selectedList.length)"
          v-auth="PermissionKey.form.formPurchaseWorkReportDelete"
          @click="handleDeleteReportWork(row.id)"
        >
          删除
        </el-button>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { IReportWork, IReportWorkReq, ISortReq } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genReportWorkColumns } from "./column-config";
import { useTableConfig } from "@/utils/useTableConfig";
import SearchBar from "./search-bar.vue";
import AddEditReportWorkDialog from "../add-edit-report-work-dialog/index.vue";
import WorkOrderDetailDialog from "../work-order-detail-dialog/index.vue";
import BatchDelete from "./batch-delete.vue";
import { PermissionKey } from "@/consts";
import { queryReportWorkByProductionId, deleteReportWork } from "@/api/report-work";
import { useMaterial as genMaterialTool } from "@/utils/material";
import { emitter as eventBus } from "@/utils/mitt";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";
import { handleOrderType } from "@/utils/sortByOrderType";
import ReportWorkLogDialog from "@/views/components/report-work-log/report-work-log-dialog.vue";

/**
 * 报工
 */
const props = defineProps<{
  productionId: string;
  /** 物资种类code */
  subClassCode: string;
  /** 生产订单编号 */
  productionNo: string;
  workId: string;
}>();

const material = genMaterialTool();

/** 当前场景 是否是 生产订单报工 */
const isProductionOrder = computed(() => material.isCableBySubClassCode(props.subClassCode));

const { pagination } = useTableConfig();
pagination.pageSize = 10;
const { columns } = genReportWorkColumns(isProductionOrder);
const tableRef = ref<PureTableInstance>();
const loading = ref(false);
const dataList = ref<Array<IReportWork>>([]);
const selectedList = ref<Array<string>>([]);
const filterParams = ref<IReportWorkReq>({} as IReportWorkReq);
const sortParams = ref<ISortReq>({
  orderByField: "",
  orderByType: ""
});

async function sortChange({ prop, order }) {
  if (order) {
    sortParams.value = {
      orderByField: prop,
      orderByType: handleOrderType(order)
    };
  } else {
    sortParams.value = {
      orderByField: "",
      orderByType: ""
    };
  }

  pagination.currentPage = 1;
  requestReportWorkList();
}

function handleSelect(selection: Array<IReportWork>) {
  selectedList.value = selection.map(({ id }) => id);
}

function clearSelection() {
  tableRef.value.getTableRef().clearSelection();
  selectedList.value = [];
}

function onFilterParamsChange(params: IReportWorkReq) {
  filterParams.value = params;
  pagination.currentPage = 1;
  requestReportWorkList();
}

function genRequestParams(): IReportWorkReq {
  return omitBy(
    {
      productionId: props.productionId,
      workId: props.workId,
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...sortParams.value,
      ...filterParams.value
    },
    v => isAllEmpty(v)
  );
}

const requestReportWorkList = useLoadingFn(async () => {
  const params = genRequestParams();
  const { data } = await queryReportWorkByProductionId(params);
  clearSelection();
  dataList.value = data.list;
  pagination.total = data.total;
}, loading);

function postDeleteSuccess() {
  requestReportWorkList();
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
}

async function handleDeleteReportWork(id: string) {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  loading.value = true;
  const res = await deleteReportWork(id).finally(() => {
    loading.value = false;
  });

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  postDeleteSuccess();
}

onMounted(requestReportWorkList);

defineExpose({
  /** 请求工单列表 */
  requestReportWorkList
});
</script>

<style scoped lang="scss">
.hide-page {
  :deep(.el-pagination) {
    transition: all 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  :deep(.el-input__suffix-inner) {
    pointer-events: none;
  }
}
</style>
