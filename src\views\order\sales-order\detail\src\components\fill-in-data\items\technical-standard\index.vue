<template>
  <el-collapse-item :name="name">
    <template #title>
      <CollapseItemTitle title="技术标准" :name="name" />
    </template>
    <div class="m-5 flex-1">
      <TechnicalStandard />
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import TechnicalStandard from "../../technical-standard/technical-standard.vue";
import { FillInDataOfCollapseNameEnum } from "../../fill-in-data/types";

const name = FillInDataOfCollapseNameEnum.TECHNICAL_STANDARD;
</script>
