<!-- 第二步：生产工艺及过程检测 -->
<template>
  <div class="production-process-inspect h-full overflow-hidden">
    <Component :is="processInspectComp" />
  </div>
</template>

<script setup lang="ts">
import NormalProcessInspect from "./normal/index.vue";
import AcProcessInspect from "./armour-clamp/index.vue";
import { inject, onMounted, shallowRef } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const processInspectComp = shallowRef();

onMounted(() => {
  // 处理显示的组件
  handleShowComponent();
});

/**
 * 根据传入参数显示不同的组件
 */
function handleShowComponent() {
  switch (prodCtx?.detailMaterialCategory) {
    case EMaterialCategory.Normal:
      processInspectComp.value = NormalProcessInspect;
      break;
    case EMaterialCategory.ArmourClamp:
      processInspectComp.value = AcProcessInspect;
      break;
    default:
      break;
  }
}
</script>

<style scoped lang="scss"></style>
