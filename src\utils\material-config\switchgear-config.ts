import { MaterialCategoryCode, MaterialSubclassCode } from "@/enums";
import { MaterialCategoryConfig, MaterialSubClassConfig } from "./types";

/**
 * 高压开关柜
 */
export const highVoltageSwitchgearConfig: MaterialSubClassConfig = {
  name: "高压开关柜",
  subClassCode: MaterialSubclassCode.HIGH_VOLTAGE_SWITCHGEAR,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 环网柜
 */
export const RingMainUnitConfig: MaterialSubClassConfig = {
  name: "环网柜",
  subClassCode: MaterialSubclassCode.RING_MAIN_UNIT,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 配电箱
 */
export const distributionBoxConfig: MaterialSubClassConfig = {
  name: "配电箱",
  subClassCode: MaterialSubclassCode.DISTRIBUTION_BOX,
  productionDataFormConfig: {
    productionTestSensingConfig: {
      finishedProductStorage: {
        voltageLevelNecessity: true
      }
    }
  }
};

/**
 * 物资品类-开关柜
 */
export const switchgearConfig: MaterialCategoryConfig = {
  name: "开关柜",
  categoryCode: MaterialCategoryCode.SWITCHGEAR,
  subClassMap: {
    highVoltageSwitchgearConfig,
    [MaterialSubclassCode.HIGH_VOLTAGE_SWITCHGEAR]: highVoltageSwitchgearConfig,
    RingMainUnitConfig,
    [MaterialSubclassCode.RING_MAIN_UNIT]: RingMainUnitConfig,
    distributionBoxConfig,
    [MaterialSubclassCode.DISTRIBUTION_BOX]: distributionBoxConfig
  }
};

export default switchgearConfig;
