<template>
  <div class="inline-block mx-3">
    <slot name="trigger" :open-dialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      :title="isAddMode ? '新增合格证' : '编辑合格证'"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="40%"
      @close="closeDialog"
    >
      <!-- 内容 -->
      <certificate-form ref="formRef" :isAddMode="isAddMode" />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import CertificateForm from "./certificate-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { addCertificate, editCertificate, getCertificateDetail } from "../../api/certificate-manage";
import { ICertificateForm } from "../../models";
import { uploadFile } from "../../api/upload-file";

const loading = ref(false);

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add";
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof CertificateForm>>();
const dialogVisible = ref(false);
/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");
/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, async visible => {
  if (visible) {
    if (isEditMode.value) {
      const { data } = await getCertificateDetail(props.id);
      const { id, templateName, fileId, bucketName, objectName, size, location } = data;
      formRef.value.initFormValue({ id, templateName, fileId, bucketName, objectName, size, location });
    }
  }
});

const requestSave = useLoadingFn(async (form: ICertificateForm) => {
  if (isAddMode.value) {
    const { data } = await addCertificate(form);
    return data;
  } else {
    const { data } = await editCertificate(form);
    return data;
  }
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const validResult = await formRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const formVal = formRef.value.getFormValue();

  if (isAddMode.value) {
    const file: File = formRef.value.getExcelFile();
    const formData = new FormData();
    formData.append("file", file);
    const { data } = await uploadFile(formData);
    formVal.fileId = data.id;
    formVal.bucketName = data.bucket;
    formVal.objectName = data.name;
  }

  // 请求保存
  const res = await requestSave(formVal);
  if (res) {
    // 处理保存后续事件
    closeDialog();
    emits("postSaveSuccess");
    ElMessage({ message: props.mode === "add" ? "新增成功" : "编辑成功", type: "success" });
  }
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  formRef.value.emptyExcelFile();
  dialogVisible.value = false;
}
</script>

<style scoped></style>
