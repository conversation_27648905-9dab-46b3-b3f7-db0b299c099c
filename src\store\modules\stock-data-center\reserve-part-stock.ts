import {
  addSparePart,
  delSparePart,
  getSparePartDetailById,
  getSparePartProductList,
  putSparePart
} from "@/api/stock-data-center/reserve-part-stock";
import { IResponse } from "@/models";
import { ISearchSparePartReq, ISparePartProduct } from "@/models/stock-data-center/i-reserve-part-stock";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isAllEmpty } from "@pureadmin/utils";

export const useReservePartStockStore = defineStore({
  id: "reserve-part-stock-store",
  state: () => ({
    tableTotal: 0 as number,
    queryParams: {
      pageNo: 1,
      pageSize: 20,
      orFilters: []
    } as ISearchSparePartReq,
    sparePartProductTableData: [] as Array<ISparePartProduct>,
    sparePartDetailInfo: {} as ISparePartProduct
  }),
  actions: {
    /**
     * 查询备品备件列表
     */
    querySparePartProductList(paramsData: ISearchSparePartReq) {
      this.queryParams = { ...this.queryParams, ...paramsData };
      this.getSparePartProductListAction();
    },

    /**
     * 获取备品备件的数据列表
     */
    async getSparePartProductListAction() {
      const resData = (await getSparePartProductList(omitBy(this.queryParams, val => isAllEmpty(val)))).data;
      if (Array.isArray(resData?.list) && resData?.list) {
        this.sparePartProductTableData = resData.list;
        this.tableTotal = resData.total;
      } else {
        this.sparePartProductTableData = [];
        this.tableTotal = 0;
      }
    },

    /**
     * 保存新增的备品备件数据
     */
    async saveSparePartProduct(paramsData: ISparePartProduct): Promise<IResponse<boolean>> {
      return await addSparePart(paramsData);
    },

    /**
     * 更新备品备件信息
     */
    async putSparePartProduct(paramsData: ISparePartProduct): Promise<IResponse<boolean>> {
      return await putSparePart(paramsData);
    },

    /**
     * 根据Id获取备品备件的详情
     */
    async getSparePartDetailById(id: string) {
      const detailRes = await getSparePartDetailById(id);
      if (detailRes.data?.id) {
        this.sparePartDetailInfo = detailRes.data;
      } else {
        this.sparePartDetailInfo = {};
      }
    },

    /**
     * 删除备品备件
     */
    async delSparePartInfo(id: string): Promise<IResponse<boolean>> {
      return await delSparePart(id);
    },

    /**
     * 查询数据根据页码数量改变
     */
    getSparePartProductListByPageSize(pageSize: number) {
      this.queryParams.pageSize = pageSize;
      this.queryParams.pageNo = 1;
      this.getSparePartProductListAction();
    },

    /**
     * 查询数据根据页码改变
     */
    getSparePartProductListByPageNo(pageNo: number) {
      this.queryParams.pageNo = pageNo;
      this.getSparePartProductListAction();
    },

    /** 重置初始数据 */
    initData() {
      this.sparePartDetailInfo = {};
    },
    initQueryData() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
      this.sparePartProductTableData = [];
    }
  }
});
