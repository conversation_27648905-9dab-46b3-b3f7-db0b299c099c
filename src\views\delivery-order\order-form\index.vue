<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="采购供货单编号" prop="supplyNo">
          <el-input
            placeholder="请输入采购供货单编号"
            :maxlength="InputLengthEnum.normal"
            v-model="form.supplyNo"
            :disabled="!isAdd"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合同类型" prop="conType">
          <EnumSelect
            class="w-full"
            v-model="form.conType"
            placeholder="请选择合同类型"
            :enum="ContractTypeEnum"
            enumName="ContractTypeEnum"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="合同编号" prop="conCode">
          <el-input :maxlength="InputLengthEnum.normal" placeholder="请输入合同编号" v-model="form.conCode" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="货物名称" prop="cargoName">
          <el-input
            :maxlength="InputLengthEnum.normal"
            placeholder="请选择货物名称"
            v-model="form.cargoName"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="采购方公司名称" prop="purchaseName">
          <el-input
            :maxlength="InputLengthEnum.normal"
            placeholder="请输入采购方公司名称"
            v-model="form.purchaseName"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            :maxlength="InputLengthEnum.normal"
            placeholder="请输入备注"
            v-model="form.remark"
            type="textarea"
            :rows="2"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDeliveryOrderListData } from "@/models";
import { ContractTypeEnum } from "@/enums";
import { useDeliveryOrderStore } from "@/store/modules/delivery-order";
import EnumSelect from "@/components/EnumSelect";
import { InputLengthEnum } from "@/enums/input-length";

defineExpose({
  getValidValue,
  validate
});

const formRef = ref<FormInstance>();
const deliveryOrderStore = useDeliveryOrderStore();

const form = reactive<IDeliveryOrderListData>({});
const rules: FormRules = {
  supplyNo: [{ required: true, message: requiredMessage("采购供货单编号"), trigger: "change" }],
  conType: [{ required: true, message: requiredMessage("合同类型"), trigger: "change" }],
  conCode: [{ required: true, message: requiredMessage("合同编号"), trigger: "change" }],
  cargoName: [{ required: true, message: requiredMessage("货物名称"), trigger: "change" }],
  purchaseName: [{ required: true, message: requiredMessage("采购方公司名称"), trigger: "change" }]
};
const isAdd = computed(() => !form.id);

watchEffect(() => {
  if (!form.id) {
    Object.assign(form, deliveryOrderStore.deliveryOrder);
  }
});
async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IDeliveryOrderListData> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
