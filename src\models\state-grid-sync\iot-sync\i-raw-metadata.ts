import { IWaveFormItem } from "@/models/collection-items";
import { IDataTypeIdentityDetail } from "@/models/dynamic-form/i-dynamic-form";

export interface IRawMetadata {
  id: string;
  /** 工序Id */
  processId: string;
  /** 工序编码 */
  targetCode: string;
  /** 工序名称 */
  targetName: string;
  /** 检测项值 */
  targetValue: string;
  /** 检测项Label */
  targetValueLabel: string;
  /** 采集方式 编码 名称 */
  collectionType: number;
  collectionTypeName: string;
  /** 排序 */
  sort: number;
  /** 状态 */
  status: boolean;
  /** 是否必填 */
  required: boolean;
  /** 必填校验 */
  requiredPre: string;
  /** */
  targetValueType: string;
  /**  */
  identityId: string;
  /** 单位 */
  unit: string;
  /** 单位长度 */
  unitLength: string;
  /**  */
  itemCode: string;
  /** 备注 */
  remarks: string;
  /** 数据项编码别名Code */
  targetCodeAlias: string;
  /** 数据项编码别名 */
  targetNameAlias: string;
  /** 是否显示 */
  isShow: boolean;
  /** 组编码 */
  groupCode: string;
  /** 组名称 */
  groupName: string;
  /**  */
  initValue: string;
  /**  */
  initType: string;
  /** 数据格式要求 */
  datumOrganization: string;
  /** 校验结果 */
  validated: boolean;
  dataTypeIdentityDetail: IDataTypeIdentityDetail;
  /** 波形图配置 */
  waveFormConfig: Array<IWaveFormItem>;
}
