<template>
  <div class="add-air-tightness-content">
    <div class="process-inspect-content px-4">
      <div class="mb-5">
        <TitleBar title="基本信息" class="mb-3" />
        <AirTightnessInfo ref="qmxExperimentBaseFormValueInst" />
      </div>
      <div class="inspec-content inspec-check">
        <TitleBar title="试验检测" class="mb-3" />
        <AirTightnessExpers ref="qmxExperimentValueInst" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import AirTightnessInfo from "./air-tightness-info/index.vue";
import AirTightnessExpers from "./air-tightness-experi/index.vue";
import { ref } from "vue";
import { IOutGoingFactoryQMXExperimentForm } from "@/models";

/** 气密性试验基本信息 */
const qmxExperimentBaseFormValueInst = ref<InstanceType<typeof AirTightnessInfo>>();
/** 气密性试验 检测 */
const qmxExperimentValueInst = ref<InstanceType<typeof AirTightnessExpers>>();

const getQMXExperimentBaseFormValue = (): Promise<boolean | IOutGoingFactoryQMXExperimentForm> => {
  return qmxExperimentBaseFormValueInst.value.getQMXExperimentFormValue();
};

const getQMXAirTightnessExpersFormValue = () => {
  return qmxExperimentValueInst.value.getQMXExperimentValue();
};

defineExpose({
  getQMXExperimentBaseFormValue,
  getQMXAirTightnessExpersFormValue,
  resetQMXExperimentBaseFormValue: () => qmxExperimentBaseFormValueInst.value.resetExperimentFormValue()
});
</script>

<style scoped lang="scss"></style>
