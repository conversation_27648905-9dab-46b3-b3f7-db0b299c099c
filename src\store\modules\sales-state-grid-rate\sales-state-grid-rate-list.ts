import { defineStore } from "pinia";
import { IStateGridRate } from "@/models";
import * as api from "@/api/state-grid-rate";

type StateGridRateListType = {
  rates: Array<IStateGridRate>;
  salesOrderId: string;
};

export const useSalesStateGridRateListStore = defineStore({
  id: "cx-sales-state-grid-rate-list",
  state: (): StateGridRateListType => ({
    rates: [],
    salesOrderId: undefined
  }),
  actions: {
    setPurchaseOrderId(id: string) {
      this.salesOrderId = id;
    },
    async refreshStateGridRateList() {
      this.rates = (await api.getSalesStateGridRateList(this.salesOrderId)).data;
    }
  }
});
