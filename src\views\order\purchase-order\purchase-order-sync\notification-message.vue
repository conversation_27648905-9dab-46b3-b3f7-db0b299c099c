<template>
  <div class="flex-ac gap-[10px] w-full">
    <ProgressFail v-if="status === PurchaseOrderSyncStatus.FAIL" />
    <ProgressSuccess v-else-if="status === PurchaseOrderSyncStatus.SUCCESS" />
    <ProgressRunning v-else />
    <div class="flex-1 flex flex-col gap-[10px]">
      <p class="text-base text-primaryText">{{ tips }}</p>
      <el-progress class="w-full" :text-inside="true" :percentage="percentage" :status="progressStatus">
        <span />
      </el-progress>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePurchaseOrderSyncStore } from "@/store/modules";
import { computed } from "vue";
import { PurchaseOrderSyncStatus } from "@/enums";
import ProgressRunning from "@/assets/svg/progress_running.svg?component";
import ProgressFail from "@/assets/svg/progress_fail.svg?component";
import ProgressSuccess from "@/assets/svg/progress_success.svg?component";

const store = usePurchaseOrderSyncStore();

const status = computed(() => store.sync.status);
const progressStatus = computed(() => (store.sync.status === PurchaseOrderSyncStatus.FAIL ? "exception" : ""));
const percentage = computed(() => store.sync.percentage);
const tips = computed(() => store.sync.tips);
</script>

<style scoped lang="scss">
:deep(.el-progress-bar__inner) {
  $color: rgba(255, 255, 255, 0.2);
  background: repeating-linear-gradient(-60deg, $color 0, $color 2px, transparent 2px, transparent 8px) no-repeat bottom
    var(--el-color-primary);
}
</style>
