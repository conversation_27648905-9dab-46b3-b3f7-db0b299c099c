<template>
  <el-dialog v-model="visible" class="middle" title="生产订单详情" align-center destroy-on-close>
    <ProductOrderDetail />
  </el-dialog>
</template>

<script setup lang="ts">
import ProductOrderDetail from "@/views/order/purchase-order/detail/src/components/production-order/product-order-detail/index.vue";
import { ref } from "vue";
import { useProductOrderStore } from "@/store/modules";

const productOrderStore = useProductOrderStore();

const visible = ref(false);

function openDetailDialog(id: string) {
  visible.value = true;
  productOrderStore.getProductOrderDetailById(id);
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
