/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { ArrivalInfoModel } from "@/models";
/**
 * @description: 原材料原材料到货进度列表
 */
export const ArrivalInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<ArrivalInfoModel>>>(
    withApiGateway(`admin-api/ejj/project/raw-material/arrival/list/${id}`)
  );
};
/**
 * @description: 原材料原材料到货进度创建
 */
export const ArrivalCreateApi = (params: ArrivalInfoModel) => {
  return http.post<ArrivalInfoModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/arrival`), {
    data: params
  });
};

/**
 * @description: 原材料原材料到货进度编辑
 */
export const ArrivalEditApi = (params: ArrivalInfoModel) => {
  return http.put<ArrivalInfoModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/arrival`), {
    data: params
  });
};

/**
 * @description: 原材料原材料到货进度删除
 */
export const ArrivalDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/arrival/${id}`));
};
