<template>
  <div class="inline-block">
    <span v-if="count > 0" class="cursor-pointer rounded-full px-4 bg-neutral text-bg_color" @click="openDialog">
      {{ count }}
    </span>
    <span v-else>
      {{ count }}
    </span>
    <el-dialog
      v-model="dialogVisible"
      title="报工列表"
      width="70%"
      align-center
      draggable
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <report-work-list :production-id="productionId" :work-id="workId" :sub-class-code="subClassCode" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import ReportWorkList from "./report-work-list/index.vue";

/**
 * 报工列表弹窗
 */

defineProps<{
  /** 报工条数 */
  count: number;
  /** 物资种类code */
  subClassCode: string;
  productionId: string;
  workId: string;
}>();

const emit = defineEmits<{
  (event: "postDialogClose"): () => void;
}>();

const dialogVisible = ref(false);

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

watch(dialogVisible, visible => {
  if (!visible) {
    emit("postDialogClose");
  }
});
</script>

<style scoped></style>
