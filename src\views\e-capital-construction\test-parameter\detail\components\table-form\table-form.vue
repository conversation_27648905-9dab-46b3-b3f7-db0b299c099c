<template>
  <div class="table-form">
    <div class="table-content h-full">
      <el-form ref="tableFormRef" :model="formData" class="rules-form h-full" :scroll-to-error="true">
        <el-table :data="formData.tableData">
          <el-table-column type="index" label="序号" :width="TableWidth.index" />
          <el-table-column
            v-for="columnItem in columnsConfig"
            :key="columnItem.prop"
            :min-width="columnItem.minWidth"
            :label="columnItem.label"
          >
            <template #default="{ row }">
              <div v-if="(columnItem.prop == 'minValue' || columnItem.prop == 'maxValue') && editMode">
                <el-form-item :props="columnItem.prop">
                  <el-input-number v-model="row[columnItem.prop]" controls-position="right" />
                </el-form-item>
              </div>
              <div v-else>{{ row[columnItem.prop as string] || "--" }}</div>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty :image-size="120">
              <template #image> <EmptyData /> </template>
            </el-empty>
          </template>
        </el-table>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { FormInstance } from "element-plus";
import { reactive, ref, watchEffect } from "vue";
import { TableWidth } from "@/enums";
import { CollectionItemModel } from "@/models/e-capital-construction";
import { genLoginReportTableColumnsConfig } from "./column-config";
const props = withDefaults(
  defineProps<{
    /** 是 展示 还是 编辑 */
    editMode: boolean;
    tableData: Array<CollectionItemModel>; // 表格表单数据
  }>(),
  {
    tableData: () => {
      return [];
    },
    editMode: false
  }
);
const { columnsConfig } = genLoginReportTableColumnsConfig();
// const emits = defineEmits<{
//   (event: "formControlValueChange", configData): void;
// }>();

// const tableControlKeyWords = `tableControlKey`;
// const formControlKeyWords = `formControlKey`;
const tableFormRef = ref<FormInstance>();
// const rules = reactive<FormRules>({});
const formData = reactive({
  tableData: []
});
watchEffect(() => {
  formData.tableData = [];
  Object.assign(formData.tableData, props.tableData);
});

/**
 * 验证表单
 * @param formEl
 */
const submitValidForm = async (formEl: FormInstance | undefined = tableFormRef.value) => {
  if (!formEl) return;
  return await formEl.validate(() => {});
};

/**
 * 表单校验类型
 * @param rowData
 */
// const currentFormRules = () => {};

/**
 * 控件数据变化时，更新表单值
 * @param data
 */
// const formValueChange = () => {};

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return formData.tableData;
}

defineExpose({
  formData, // 仅表单数据
  tableFormRef, // 表单实例
  getFormValue, // 获取表单值
  validDynamicForm: submitValidForm
});
</script>

<style scoped lang="scss">
:deep(.el-input-number) {
  margin: 0;
  width: 100%;
}

:deep(.el-form-item) {
  margin: 0;
}

.table-form {
  overflow: hidden;

  :deep(.el-table__inner-wrapper) {
    display: flex;
  }

  :deep(.el-table__body-wrapper) {
    display: flex;
    flex: 1 1 0%;
    height: 100%;
    overflow: auto;
  }

  :deep(.el-table--fit) {
    height: 100%;
  }
}
</style>
