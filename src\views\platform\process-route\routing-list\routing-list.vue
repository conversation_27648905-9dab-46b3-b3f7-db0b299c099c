<template>
  <div class="process-info">
    <div class="detail-item" v-if="processRouteInfo">
      <div class="mb-1 top flex-bc">
        <div class="flex flex-1 detail-info">
          <div class="flex mr-2 item-text code-info">
            <span class="mr-1 icon text-primary" v-if="processRouteInfo.status">启</span>
            <span class="mr-1 icon icon-danger text-primary" v-else>禁</span>
            <ShowTooltip className="max-w-[12em] text-sm" placement="top" :content="processRouteInfo.code" />
          </div>
        </div>
        <div class="flex del-btn">
          <el-button
            v-auth="PermissionKey.meta.metaRawMaterialEdit"
            :icon="Edit"
            class="el-icon-edit-hover"
            link
            @click="editProcessRoute(processRouteInfo)"
          />
          <el-button
            v-auth="PermissionKey.meta.metaRawMaterialDelete"
            :icon="Delete"
            class="el-icon-delete-hover"
            link
            @click.stop="delProcessRoute(processRouteInfo)"
          />
        </div>
      </div>
      <div class="text-base break-words center">
        <span>{{ processRouteInfo.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { Edit, Delete } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { IProcessRoute } from "@/models";

withDefaults(
  defineProps<{
    processRouteInfo: IProcessRoute;
  }>(),
  {}
);

const emits = defineEmits<{
  (event: "onEditProcessRoute", id: string): void;
  (event: "onDelProcessRoute", id: string): void;
}>();

/** 编辑工艺路线 */
const editProcessRoute = (processRouteInfo: IProcessRoute) => {
  emits("onEditProcessRoute", processRouteInfo.id);
};

/** 删除工艺路线 */
const delProcessRoute = (processRouteInfo: IProcessRoute) => {
  emits("onDelProcessRoute", processRouteInfo.id);
};
</script>

<style scoped lang="scss">
.process-info {
  width: 100%;
}

.item-text {
  @apply flex items-center text-secondary;

  .icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    width: 16px;
    height: 16px;
    border-radius: 1px;
    margin-right: 4px;
    color: var(--el-color-primary-light-3);
    background: var(--el-color-primary-light-9);
    border: 0.5px solid var(--el-color-primary-light-7);
  }

  .icon-danger {
    color: var(--el-color-danger);
    background: var(--el-color-danger-light-9);
    border-color: var(--el-color-danger-light-8);
  }
}

.del-btn {
  .el-button {
    @apply text-secondary;

    &:hover {
      @apply text-regular;
    }
  }

  .el-icon-edit-hover {
    &:hover {
      color: var(--el-color-primary);
    }
  }

  .el-icon-delete-hover {
    &:hover {
      color: var(--el-color-danger);
    }
  }
}
</style>
