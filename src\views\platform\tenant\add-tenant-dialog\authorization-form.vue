<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="right" v-loading="loading">
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar title="添加授权文件" class="mb-5" />
        <el-form-item prop="license">
          <el-upload
            class="w-full"
            ref="uploadRef"
            drag
            :auto-upload="false"
            accept=".bin"
            v-model:file-list="fileList"
            :limit="1"
            :on-change="onFileChange"
            :on-exceed="onExceed"
            :on-remove="onRemove"
          >
            <el-icon size="24"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              <div class="upload-text"><span>将授权文件拖到此处，或</span><em>点击上传</em></div>
              <div class="upload-tips">
                <span>仅支持bin格式文件.</span>
              </div>
            </div>
          </el-upload>
        </el-form-item>
        <div v-if="showEIP" class="mb-5">
          <div class="flex-bc mb-3">
            <TitleBar title="EIP授权配置" />
            <el-form-item prop="enableEip">
              <el-switch v-model="form.enableEip" inline-prompt size="large" active-text="开" inactive-text="关" />
            </el-form-item>
          </div>
          <el-row :gutter="40" class="mb-3" v-if="form.enableEip">
            <el-col :span="12">
              <el-form-item label="供应商编码" prop="supplierCode">
                <el-input placeholder="请输入供应商编码" v-model="form.supplierCode" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="供应商名称" prop="supplierName">
                <el-input placeholder="请输入供应商名称" v-model="form.supplierName" />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="物资种类" prop="subclassCode">
                <el-tree-select
                  :props="treeSelectorProps"
                  multiple
                  :data="treeSelectList"
                  class="w-full"
                  v-model="form.subclassCode"
                  placeholder="请选择物资种类"
                  clearable
                  default-expand-all
                />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="供应商密钥" prop="publicKey">
                <el-input placeholder="请输入供应商密钥" v-model="form.publicKey" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div v-if="showIOT" class="mb-5">
          <div class="flex-bc mb-3">
            <TitleBar title="IOT授权配置" />
            <el-form-item prop="enableIot">
              <el-switch v-model="form.enableIot" inline-prompt active-text="开" inactive-text="关" size="large" />
            </el-form-item>
          </div>
          <el-row :gutter="40" class="mb-3" v-if="form.enableIot">
            <el-col :span="12">
              <el-form-item label="物资种类" prop="iotSubclassCode">
                <el-tree-select
                  :props="treeSelectorProps"
                  multiple
                  :data="treeSelectList"
                  class="w-full"
                  v-model="form.iotSubclassCode"
                  placeholder="请选择物资种类"
                  clearable
                  default-expand-all
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户端ID" prop="iotClientId">
                <el-input placeholder="请输入客户端ID" v-model="form.iotClientId" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="客户端密码" prop="iotClientSecret">
                <el-input placeholder="请输入客户端密码" v-model="form.iotClientSecret" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="授权范围" prop="iotScopes">
                <el-input placeholder="请输入授权范围" v-model="form.iotScopes" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="mb-5" v-if="showCSG">
          <div class="flex-bc mb-3">
            <TitleBar title="广州供电局授权配置" />
            <el-form-item prop="enableGz">
              <el-switch v-model="form.enableGz" inline-prompt active-text="开" inactive-text="关" size="large" />
            </el-form-item>
          </div>
          <el-row :gutter="40" class="mb-3" v-if="form.enableGz">
            <el-col :span="12">
              <el-form-item label="物资种类" prop="gzSubclassCode">
                <el-tree-select
                  :props="treeSelectorProps"
                  multiple
                  :data="treeSelectList"
                  class="w-full"
                  v-model="form.gzSubclassCode"
                  placeholder="请选择物资种类"
                  clearable
                  default-expand-all
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户端ID" prop="gzAccountName ">
                <el-input placeholder="请输入客户端ID" v-model="form.gzAccountName" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户端密码" prop="gzAccountKey">
                <el-input placeholder="请输入客户端密码" v-model="form.gzAccountKey" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue";
import { FormInstance, FormRules, genFileId, UploadFile } from "element-plus";
import { decodeLicenseDisplayInfo, queryCategoryInfoTree } from "@/api";
import { CHANNEL_CSG, CHANNEL_EIP, CHANNEL_IOT, EMAIL_REGEXP, MOBILE_REGEXP } from "@/consts";
import TitleBar from "@/components/TitleBar";
import { requiredMessage } from "@/utils/form";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ICategoryInfoTree } from "@/models";

interface FormType {
  enableEip: boolean;
  subclassCode: string[];
  supplierCode: string;
  supplierName: string;
  publicKey: string;
  enableIot: boolean;
  iotSubclassCode: string[];
  iotClientId: string;
  iotClientSecret: string;
  iotScopes: string;
  enableGz: boolean;
  gzSubclassCode: string[];
  gzAccountName: string;
  gzAccountKey: string;
  license: string;
}

const treeSelectorProps = {
  label: "categoryName",
  value: "categoryCode"
};
const form = reactive<FormType>({
  license: "",
  enableEip: false,
  subclassCode: [],
  supplierCode: "",
  supplierName: "",
  publicKey: "",
  enableIot: false,
  iotSubclassCode: [],
  iotClientId: "",
  iotClientSecret: "",
  iotScopes: "",
  enableGz: false,
  gzSubclassCode: [],
  gzAccountName: "",
  gzAccountKey: ""
});

const uploadRef = ref();
const formRef = ref<FormInstance>();
const fileList = ref<Array<UploadFile>>();
/** 当前授权文件支持的平台 */
const licenseChannelList = ref<Array<string>>([]);
/** 原始物资品类树 */
const rawCategoryTreeList = ref<Array<ICategoryInfoTree>>([]);
/** 符合授权文件的物资品类树 */
const treeSelectList = ref<Array<ICategoryInfoTree>>([]);
const loading = ref(false);

const showEIP = computed(() => licenseChannelList.value.includes(CHANNEL_EIP));
const showIOT = computed(() => licenseChannelList.value.includes(CHANNEL_IOT));
const showCSG = computed(() => licenseChannelList.value.includes(CHANNEL_CSG));

const rules: FormRules = {
  license: [{ required: true, message: requiredMessage("授权文件"), trigger: "change" }],
  comName: [{ required: true, message: requiredMessage("企业名称"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("企业简称"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }],
  contactMobile: [{ trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }],
  contactEmail: [{ trigger: "change", message: "邮箱格式不正确", pattern: EMAIL_REGEXP }],
  // eip平台
  publicKey: [{ required: true, message: requiredMessage("供应商密钥"), trigger: "change" }],
  supplierName: [{ required: true, message: requiredMessage("供应商名称"), trigger: "change" }],
  supplierCode: [{ required: true, message: requiredMessage("供应商编码"), trigger: "change" }],
  subclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  // iot平台
  iotSubclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  iotClientId: [{ required: true, message: requiredMessage("客户端ID"), trigger: "change" }],
  iotClientSecret: [{ required: true, message: requiredMessage("客户端密码"), trigger: "change" }],
  iotScopes: [{ required: true, message: requiredMessage("授权范围"), trigger: "change" }],
  // SCG平台
  csgSubclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  csgAccountName: [{ required: true, message: requiredMessage("客户端ID"), trigger: "change" }],
  csgAccountKey: [{ required: true, message: requiredMessage("客户端密码"), trigger: "change" }]
};

const requestCategoryTreeList = useLoadingFn(async () => {
  const { data } = await queryCategoryInfoTree();
  rawCategoryTreeList.value = data;
}, loading);

/**
 * @description: 文件数量超出限制时自动替换上一个文件
 */
const onExceed = files => {
  uploadRef.value!.clearFiles();
  const file: UploadFile = files[0];
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

/**
 * @description: 文件移除时，重置表单
 */
const onRemove = () => {
  fileList.value = undefined;
  formRef.value?.resetFields();
  licenseChannelList.value = [];
  form.license = "";
};

/**
 * @description: 文件变化时，解析授权文件，更新表单
 */
const onFileChange = async (file: UploadFile) => {
  formRef.value?.resetFields();
  licenseChannelList.value = [];
  form.license = "";
  const reader = new FileReader();
  reader.readAsArrayBuffer(file.raw);
  reader.onload = async function (e) {
    // 解析license授权信息
    const ints = new Uint8Array(e.target.result as any);
    const license: string = new TextDecoder("gb2312").decode(ints);
    form.license = license;
    const { data: licenseInfo } = await decodeLicenseDisplayInfo(license);
    // 获取授权的平台、品类、种类
    const {
      integrationChannel,
      categoryCodes: categoryCodeList,
      subCategoryCodes: subClassCodeList
    } = licenseInfo.authorization;
    licenseChannelList.value = integrationChannel;
    // 生成符合license授权的筛选结构
    // 过滤出符合license的Category tree
    const filterCategoryList = rawCategoryTreeList.value.filter(item => categoryCodeList.includes(item.categoryCode));
    // 过滤出符合license的Subclass tree
    const filterSubclassList = filterCategoryList.map(subTree => {
      const children = subTree.children?.filter(item => subClassCodeList.includes(item.categoryCode));
      subTree.children = children;
      return subTree;
    });
    treeSelectList.value = filterSubclassList;
  };
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

onMounted(requestCategoryTreeList);

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped></style>
