import { IPagingReq } from "@/models";

export interface ITechnicalStandardLibrary {
  /** 技术标准库ID */
  id: string;
  /** 物资品类 */
  categoryCode: string;

  /** 物资种类  */
  subClassCode: string;

  /** 技术标准名称  */
  standardName: string;

  /** 物料编号  */
  materialCode: string;

  /** 规格型号  */
  specificationModel: string;

  /** 是否通用标准  */
  isStandard: boolean;

  /** 同步状态  */
  syncResult?: boolean;

  /** 最新同步时间  */
  lastSyncTime?: string;

  /** 同步日志  */
  syncNote?: string;
}

export interface ITechnicalStandardLibraryReqParams extends IPagingReq {
  /** 物资品类 */
  categoryCode?: string;

  /** 物资种类  */
  subClassCode?: string;

  /** 技术标准名称  */
  standardName?: string;
}

export type ITechnicalStandardLibraryDetail = Pick<
  ITechnicalStandardLibrary,
  "categoryCode" | "subClassCode" | "standardName" | "isStandard" | "specificationModel" | "materialCode"
> & { id?: string } & { enable?: string };

/** 技术标准库详情对应的工序列表 */
export interface IProcessData {
  /** 工序ID */
  id?: string;
  /** 工序名称 */
  processName: string;
  /** 工序编码 */
  processCode?: string;
}

/** 技术标准库详情列表查询条件 */
export interface ICollectionParams {
  standardId?: string;
  processId?: string;
  required?: boolean;
}

/** 技术标准库详情 */
export interface IDetailStandardInfo {
  id: string;
  standardId: string;
  productionOrderId: string;
  standardName: string;
  materialCode: string;
  specificationModel: string;
  isStandard: boolean;
}
/** 技术标准库详情列表 */
export interface ICollectionItem {
  /** 采集点列表Id */
  id: string;
  /** 技术标准库Id */
  standardId: string;
  /** 工序ID */
  processId: string;
  /** 工序名称 */
  processName: string;
  /** 采集点id */
  metadataModelId: string;
  /** 采集点名称 */
  metadataModelName: string;
  /** 计量单位*/
  unit: string;
  /** 必采点 */
  required: boolean;
  /** 是否包含上限 */
  includeMaxValue: boolean;
  /** 上限 */
  maxValue: string;
  /** 是否包含下限 */
  includeMinValue: boolean;
  /** 下限 */
  minValue: string;
  /** 是否是数字用于编辑 */
  numberType: boolean;
}

/** 技术标准库详情列表 */

export type IBatchCollectionItem = Pick<
  ICollectionItem,
  | "id"
  | "standardId"
  | "processId"
  | "metadataModelId"
  | "includeMaxValue"
  | "maxValue"
  | "includeMinValue"
  | "minValue"
>;

export const IBatchCollectionProperty = [
  "id",
  "standardId",
  "processId",
  "metadataModelId",
  "includeMaxValue",
  "maxValue",
  "includeMinValue",
  "minValue"
];
