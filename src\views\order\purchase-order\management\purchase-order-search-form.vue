<template>
  <SearchForm
    keyword-filter-key="orFilters"
    :keyword-fields="keywordFields"
    :search-form="form"
    :search-items="items"
    placeholder="输入采购订单号/采购方公司名称/项目名称/合同名称"
    @search="onSearch()"
    @reset="sendResetRecord"
  >
    <ElButton
      v-auth="PermissionKey.form.formPurchaseBtnSync"
      type="primary"
      size="large"
      @click="syncStore.syncPurchaseOrder"
      v-track="TrackPointKey.FORM_PURCHASE_BTN_SYNC"
    >
      <FontIcon icon="icon-sync" class="pr-2" />
      拉取国网订单
    </ElButton>
    <ElButton
      v-if="licenseAuthIncludeCSG"
      v-auth="PermissionKey.form.formPurchaseBtnSync"
      type="primary"
      size="large"
      @click="syncCSGPurchaseOrder"
      v-track="TrackPointKey.FORM_PURCHASE_BTN_SYNC"
    >
      <FontIcon icon="icon-sync" class="pr-2" />
      拉取南网订单
    </ElButton>
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from "@/components/SearchForm/src/search-form.vue";
import { IKeywordField, ISearchItem } from "@/components/SearchForm";
import { defineComponent, h, reactive, ref, toRaw, watch } from "vue";
import { ElDatePicker, ElRadioGroup, ElRadio } from "element-plus";
import { usePurchaseOrderStore, usePurchaseOrderSyncStore, useSystemAuthStore } from "@/store/modules";
import { PermissionKey, TrackPointKey } from "@/consts";
import { useTrack } from "@/utils/useTrack";
import SubclassSelect from "@/views/components/subclass-select";
import {
  PurchaseOrderSearchBySyncStatusEnum,
  PurchaseOrderSearchByTriggerScoreEnum
} from "@/enums/purchase-order/search-order.enum";
import { ElNotification, NotificationHandle } from "element-plus";
import CSGNotificationMessage from "../purchase-order-sync/csg-notification-message.vue";
import { PurchaseChannel, PurchaseOrderSyncStatus } from "@/enums/purchase-order/purchase-order-sync-status.enum";
import { getPurchaseOrderSyncResult, syncPurchaseOrder } from "@/api/purchase-order-sync";
import { computedAsync } from "@vueuse/core";
const systemAuthStore = useSystemAuthStore();

const syncStore = usePurchaseOrderSyncStore();
const purchaseOrderStore = usePurchaseOrderStore();
const { sendUserOperation } = useTrack();
const licenseAuthIncludeCSG = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeCSG);

const SyncStatus = defineComponent({
  name: "syncType",
  render() {
    const radios = [
      { label: "全部", value: undefined },
      { label: "未同步", value: PurchaseOrderSearchBySyncStatusEnum.NO_SYNC },
      { label: "已触发同步", value: PurchaseOrderSearchBySyncStatusEnum.SYNCED },
      { label: "同步失败", value: PurchaseOrderSearchBySyncStatusEnum.SYNC_FAIL }
    ].map(option => h(ElRadio, { label: option.value, border: true }, () => option.label));
    return h(ElRadioGroup, () => radios);
  }
});

// 定义物资种类下拉框数据
const SyncSubClassComponents = defineComponent({
  name: "subclassCode",
  render() {
    return h(SubclassSelect, { clearable: true });
  }
});

const TriggerStatus = defineComponent({
  name: "triggerType",
  render() {
    const radios = [
      { label: "全部", value: undefined },
      { label: "未触发", value: PurchaseOrderSearchByTriggerScoreEnum.NO_TRIGGER },
      { label: "已触发评分", value: PurchaseOrderSearchByTriggerScoreEnum.TRIGGERED },
      { label: "评分触发失败", value: PurchaseOrderSearchByTriggerScoreEnum.TRIGGER_FAIL }
    ].map(option => h(ElRadio, { label: option.value, border: true }, () => option.label));
    return h(ElRadioGroup, () => radios);
  }
});

const form = reactive({
  sellerSignTime: [],
  subclassCode: undefined,
  syncType: undefined,
  triggerType: undefined,
  orFilters: [] as Array<Record<string, string>>
});

watch([() => form.syncType, () => form.triggerType], () => {
  onSearch();
});

watch([() => form.subclassCode, () => form.sellerSignTime], () => {
  purchaseOrderStore.patchQueryParams(form);
});

const items: Array<ISearchItem> = [
  {
    key: "sellerSignTime",
    full: false,
    label: "合同签订日期：",
    style: { width: "500px" },
    component: ElDatePicker,
    componentProps: {
      type: "daterange",
      rangeSeparator: "～",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  { full: false, key: "subclassCode", label: "物资种类：", component: SyncSubClassComponents },
  { full: true, key: "syncType", label: "同步状态：", component: SyncStatus },

  { full: true, key: "triggerType", label: "触发结果：", component: TriggerStatus }
];

const keywordFields: Array<IKeywordField> = [
  { key: "po_no", title: "采购订单号" },
  { key: "buyer_name", title: "采购方公司名称" },
  { key: "prj_name", title: "项目名称" },
  { key: "con_name", title: "合同名称" }
];

const onSearch = () => {
  purchaseOrderStore.queryPurchaseOrdersByFilter({
    sellerSignTime: form.sellerSignTime,
    subclassCode: form.subclassCode,
    syncType: form.syncType,
    triggerType: form.triggerType,
    orFilters: form.orFilters
  });
  sendSearchRecord();
};

function sendSearchRecord() {
  const { sellerSignTime, syncType, triggerType } = form;
  const isAdvanced: boolean = !!sellerSignTime?.length || !isNil(syncType) || !isNil(triggerType);
  sendUserOperation(
    isAdvanced ? TrackPointKey.FORM_PURCHASE_SEARCH_ADVANCED : TrackPointKey.FORM_PURCHASE_SEARCH_BASIC
  );
}

function sendResetRecord() {
  sendUserOperation(TrackPointKey.FORM_PURCHASE_SEARCH_RESET);
}

function isNil(value: unknown): boolean {
  return toRaw(value) == null;
}

async function syncCSGPurchaseOrder() {
  const tips = ref(`正在拉取南网采购订单数据`);
  const status = ref(PurchaseOrderSyncStatus.RUNNING);
  const notification: NotificationHandle = ElNotification({
    message: h(CSGNotificationMessage, {
      tips: tips,
      status: status
    }),
    duration: 0,
    showClose: false,
    customClass: "el-notification__sync-purchase-order"
  });

  const { code, data } = await syncPurchaseOrder(PurchaseChannel.CSG_GuangZhou).catch(({ response }) => {
    return response.data;
  });

  await new Promise(resolve => {
    const t = setTimeout(() => {
      resolve(true);
      clearTimeout(t);
    }, 1000);
  });

  if (code) {
    tips.value = "拉取采购订单失败,请稍后重试拉取";
    status.value = PurchaseOrderSyncStatus.FAIL;
  } else {
    if (data && data.dataRowCount) {
      tips.value = "拉取完成，正在写入系统";
      status.value = PurchaseOrderSyncStatus.SUCCESS;
    }
  }

  await new Promise(resolve => {
    const t = setTimeout(() => {
      resolve(true);
      clearTimeout(t);
    }, 2000);
  });

  if (code) {
    notification.close();
    return;
  }

  const {
    data: { dataRowCount }
  } = await getPurchaseOrderSyncResult(PurchaseChannel.CSG_GuangZhou);
  if (dataRowCount) {
    tips.value = `成功写入【${dataRowCount}】条南网采购订单行`;
  } else {
    tips.value = `未拉取到新的南网订单`;
  }
  const t = setTimeout(() => {
    notification.close();
    purchaseOrderStore.queryPurchaseOrdersByFilter();
    clearTimeout(t);
  }, 2000);
}
</script>

<style scoped>
:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}
</style>
