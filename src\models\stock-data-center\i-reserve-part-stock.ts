import { IPagingReq } from "../i-paging-req";
import { IDictionaryOption } from "../platform";

/** 搜索备品备件 */
export interface ISearchSparePartReq extends IPagingReq {
  putStorageTime?: string[];
  orFilters?: string[];
}

/** 备品备件列表 */
export interface ISparePartProduct {
  id?: string;
  spareProductCode?: string; // 备品备件编号
  productName: string; // 备品备件名称
  productAmount: number; // 备品备件库存数量
  productUnit: number; // 计量单位
  categoryCode: string; // 品类编码
  subclassCode: string; // 种类编码
  categoryName: string; // 品类名称
  subclassName: string; // 种类名称
  speModels: string; // 规格型号
  voltageLevel: string; // 电压等级
  putStorageTime: string; // 入库时间
  storeCity: string; // 存放地点所在城市
  spareProductType: number; // 备品备件类型
  version?: number; //
  lastUpdateTime?: string; //
  lastSyncTime?: string; //
  syncResult?: string; //
  reason?: string; //
  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;
}

/** 修改备品备件 */
export type IUpateSparePartReq = ISparePartProduct;
