<template>
  <div class="header">
    <div class="flex items-center">
      <TitleBar title="进度告警" />
      <div class="sub-title pl-2">每24小时触发一次进度告警规则</div>
    </div>
    <el-switch v-model="progressRules.switchOn" active-text="开" inactive-text="关" inline-prompt />
  </div>
  <div class="mt-4 ml-4">
    <div class="flex items-center">
      <el-checkbox
        label="生产订单在计划开始日期的"
        v-model="progressRules.productionOrderNoReportAlarm"
        :disabled="disabledAlarmRule"
      />
      <div class="flex items-center">
        <div class="flex items-stretch">
          <el-input-number
            class="!w-24 ml-2"
            v-model="progressRules.overDays"
            :min="1"
            :step="1"
            controls-position="right"
            :disabled="!progressRules.productionOrderNoReportAlarm || disabledAlarmRule"
            @click.prevent
          />
          <span class="mx-2 flex items-center px-2 ui-input-group-addon">天</span>
        </div>
        <div class="text-base label">后未产生报工则产生告警</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watchEffect, watch, computed } from "vue";
import TitleBar from "@/components/TitleBar";
import { IProgressRule } from "@/models";

const props = defineProps<{
  progressRules: IProgressRule;
}>();

const progressRules = reactive<Partial<IProgressRule>>({});
const emits = defineEmits<{
  (e: "setValue", progressRules: IProgressRule): void;
}>();

// 是否禁用告警配置
const disabledAlarmRule = computed(() => !progressRules.switchOn);

watchEffect(() => {
  if (props.progressRules != progressRules) {
    Object.assign(progressRules, props.progressRules);
  }
});

watch(progressRules, newValue => {
  emits("setValue", newValue as IProgressRule);
});
</script>

<style scoped lang="scss">
.describe-content {
  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    line-height: 30px;
    margin-top: 15px;
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: space-between;

  .sub-title {
    font-size: var(--el-font-size-extra-small);
    color: var(--el-text-color-placeholder);
    margin-left: 4px;
  }
}

.el-radio-group {
  display: grid;
}

.ui-input-group-addon {
  color: var(--el-text-color-secondary);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  box-shadow: 0 0 0 0.0625rem var(--el-input-border-color, var(--el-border-color)) inset;
}

:deep {
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: var(--el-text-color-regular);
  }
}

.label {
  color: var(--el-text-color-regular);
}
</style>
