import { defineStore } from "pinia";
import * as api from "@/api/purchase-order-sync";
import { fullDateFormat } from "@/consts";
import { IPurchaseOrderSyncResult, IPurchaseOrderSyncStep } from "@/models";
import { ref } from "vue";
import { PurchaseOrderSyncStatus } from "@/enums";
import { usePurchaseOrderStore } from "@/store/modules";
import { AxiosError } from "axios";
import { formatDate } from "@/utils/format";

export const usePurchaseOrderSyncStore = defineStore("cx-purchase-order-sync", () => {
  const dialogVisible = ref(false);
  const background = ref(false);
  const result = ref<IPurchaseOrderSyncResult>();
  const steps = ref<Array<IPurchaseOrderSyncStep>>([]);
  const activeStep = ref<IPurchaseOrderSyncStep>();
  const status = ref<PurchaseOrderSyncStatus>();
  const percentage = ref(0);
  const tips = ref("");

  const _abortSignal = ref(false);
  const _abortMessage = ref("");
  const _tips = ["验证访问EIP资格", "建立采购订单传输通道", "正在拉取采购订单数据", "拉取完成，正在写入系统"];
  let _stepIndex: number;
  let _intervalHandle: ReturnType<typeof setInterval>;

  async function refreshSyncResult() {
    const data = (await api.getPurchaseOrderSyncResult()).data;
    _setResult(data);
  }

  function syncPurchaseOrder() {
    if (status.value === PurchaseOrderSyncStatus.RUNNING) {
      return;
    }
    _initializeSync();
    _startSync();
  }

  function reSyncPurchaseOrder() {
    if (status.value === PurchaseOrderSyncStatus.RUNNING) {
      return;
    }
    _initializeSync();
    // wait for progress animation
    setTimeout(() => _startSync(), 600);
  }

  function _startSync() {
    status.value = PurchaseOrderSyncStatus.RUNNING;
    _stepIndex = 0;
    _execStep();
  }

  async function _execStep() {
    if (_stepIndex >= steps.value.length) {
      _complete();
      return;
    }
    tips.value = _tips[_stepIndex];
    activeStep.value = steps.value[_stepIndex];
    activeStep.value.status = PurchaseOrderSyncStatus.RUNNING;
    const { duration, action, start, end } = activeStep.value;
    _interval(duration, start, end);
    const promises = [_wait(duration)];
    if (action) {
      promises.push(
        action().catch(e => {
          _fail(e);
          return Promise.reject(e);
        })
      );
    }
    const [, syncResult] = await Promise.all(promises);
    percentage.value = end;
    if (syncResult === PurchaseOrderSyncStatus.BACKGROUND_PULL) {
      activeStep.value.status = PurchaseOrderSyncStatus.BACKGROUND_PULL;
    } else {
      activeStep.value.status = PurchaseOrderSyncStatus.SUCCESS;
    }
    _abortSignal.value ? _abort() : _nextStep();
  }

  function _nextStep() {
    _stepIndex++;
    _execStep();
  }

  function _interval(duration: number, start: number, end: number) {
    _clearInterval();
    let time = 0;
    _intervalHandle = setInterval(() => {
      time += 100;
      percentage.value = Math.floor((1 - Math.exp((-1 * time) / duration)) * (end - start) + start);
    }, 100);
  }

  async function _abort() {
    tips.value = _abortMessage.value;
    if (activeStep.value.status === PurchaseOrderSyncStatus.BACKGROUND_PULL) {
      status.value = PurchaseOrderSyncStatus.BACKGROUND_PULL;
    } else {
      status.value = PurchaseOrderSyncStatus.SUCCESS;
    }
    _clearInterval();
    await refreshSyncResult();
  }

  async function _complete() {
    status.value = PurchaseOrderSyncStatus.SUCCESS;
    percentage.value = 100;
    _clearInterval();
    await refreshSyncResult();
    tips.value = `成功写入【${result.value.dataRowCount}条】采购订单行`;

    usePurchaseOrderStore().queryPurchaseOrdersByFilter();
  }

  function _fail(e: AxiosError) {
    console.log(e);
    status.value = PurchaseOrderSyncStatus.FAIL;
    activeStep.value.status = PurchaseOrderSyncStatus.FAIL;
    tips.value = e.message;
    _clearInterval();
    refreshSyncResult();
  }

  function _wait(period: number) {
    return new Promise(resolve => setTimeout(() => resolve(""), period));
  }

  function _clearInterval() {
    if (_intervalHandle) {
      clearInterval(_intervalHandle);
      _intervalHandle = null;
    }
  }

  function _setResult(data: IPurchaseOrderSyncResult) {
    const { modifyTime, dataRowCount, upgradeStatus, todayCount } = data;
    result.value = {
      dataRowCount,
      upgradeStatus,
      todayCount,
      modifyTime: formatDate(modifyTime, fullDateFormat)
    };
  }

  function _syncPurchaseOrder() {
    return api.syncPurchaseOrder().then(
      res => {
        if (res.code === 500) {
          _abortSignal.value = true;
          _abortMessage.value = res.msg;
          throw new Error();
        }
        if (!res.data.dataRowCount) {
          _abortSignal.value = true;
          _abortMessage.value = "未拉取到新的采购订单";
        }
        return res;
      },
      e => {
        if (e.code === 500) {
          _abortSignal.value = true;
          _abortMessage.value = e.message;
          return PurchaseOrderSyncStatus.BACKGROUND_PULL;
        }
        if (e.request.status === 504) {
          _abortSignal.value = true;
          _abortMessage.value = "EIP接口请求较慢或更新数量较多，本次拉取将在后台自动运行";
          return PurchaseOrderSyncStatus.BACKGROUND_PULL;
        }
      }
    );
  }

  function _initializeSync() {
    dialogVisible.value = true;
    background.value = false;
    steps.value = [
      { name: "访问EIP验证", percent: 19, duration: 1000, start: 0, end: 19, action: api.checkPurchaseOrderSync },
      { name: "建立传输通道", percent: 10, duration: 1000, start: 19, end: 29 },
      { name: "传输订单数据", percent: 58, duration: 2000, start: 29, end: 84, action: _syncPurchaseOrder },
      { name: "写入订单数据", percent: 16, duration: 1000, start: 84, end: 100 }
    ];
    percentage.value = 0;
    status.value = PurchaseOrderSyncStatus.RUNNING;
    tips.value = _tips[0];
    _abortSignal.value = false;
    _abortMessage.value = "";
  }

  return {
    result,
    background,
    dialogVisible,
    sync: {
      steps,
      status,
      percentage,
      tips
    },
    refreshSyncResult,
    syncPurchaseOrder,
    reSyncPurchaseOrder
  };
});
