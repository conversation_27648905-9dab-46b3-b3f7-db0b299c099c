<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <!-- 表格 -->
    <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        row-key="id"
        :data="list"
        :columns="columnsConfig"
        size="large"
        :loading="loading"
        showOverflowTooltip
        v-model:pagination="pagination"
        @page-current-change="requestList"
        @page-size-change="reloadList"
      >
        <template #empty>
          <el-empty :image-size="120">
            <template #image> <EmptyData /> </template>
          </el-empty>
        </template>
        <template #operation="{ row }">
          <el-button
            v-auth="PermissionKey.form.formPurchaseFactoryTrialCreate"
            link
            type="primary"
            @click="copyExperiment(row)"
          >
            复制
          </el-button>

          <template
            v-if="
              licenseAuthIncludeEJJ && row.rawMetadataValue?.some(x => x.identityCode === EControlType.WaveRoseControl)
            "
          >
            <el-button
              v-auth="PermissionKey.form.formPurchaseFactoryTrialEdit"
              link
              type="primary"
              @click="onUploadWaveData(row)"
            >
              上传波形数据
            </el-button>
          </template>

          <el-button
            v-auth="PermissionKey.form.formPurchaseFactoryTrialEdit"
            link
            type="primary"
            @click="editExperiment(row)"
          >
            编辑
          </el-button>
          <el-button
            v-auth="PermissionKey.form.formPurchaseFactoryTrialDelete"
            link
            type="danger"
            @click="delExperiment(row)"
          >
            删除
          </el-button>
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { useRoute } from "vue-router";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useTableConfig } from "@/utils/useTableConfig";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PureTable } from "@pureadmin/table";
import { usePageStoreHook } from "@/store/modules/page";
import { genOutGoingFactoryExperimentTableColumnsConfig } from "./column-config";
import {
  deleteOutGoingFactoryQMXExperimentDetail,
  queryNonCableOutGoingFactoryQMXExperimentList,
  queryOutGoingFactoryQMXExperimentList
} from "@/api/production-test-sensing/out-going-factory/out-going-factory-qmx-experiment";
import {
  IOutGoingFactoryNotJfnyReq,
  IOutGoingFactoryQmxExperiment
} from "@/models/production-test-sensing/out-going-factory-qmx-experiment/i-out-going-factory-qmx-experiment";
import { useEXFactoryExperimentStore } from "@/store/modules/ex-factory-experiment/ex-factory-experiment";
import { useSalesFillInDataStore } from "@/store/modules/fill-in-data/sales-fill-in-data";
import { useSalesOrderDetailStore } from "@/store/modules/sales-order-management/sales-order-detail";
import { emitter } from "@/utils/mitt";
import { ElMessage, ElMessageBox } from "element-plus";
import { PermissionKey } from "@/consts";
import { useSalesProductionTestSensingStore } from "@/store/modules/production-test-sensing/sales-production-test-sensing";
import { computedAsync } from "@vueuse/core";
import { useSystemAuthStore } from "@/store/modules";
import { EControlType } from "@/enums";
/**
 * 出厂试验-列表
 */
const props = defineProps<{
  editExperiment: (row: IOutGoingFactoryQmxExperiment) => void;
  deleteExperimentSuccess: () => void;
  copyExperiment: (row: IOutGoingFactoryQmxExperiment) => void;
  onUploadWaveData: (row: IOutGoingFactoryQmxExperiment) => void;
}>();

const route = useRoute();
const systemAuthStore = useSystemAuthStore();
const fillInDataStore = useSalesFillInDataStore();
const predeliveryExperimentStore = useEXFactoryExperimentStore();
const productionTestSensingStore = useSalesProductionTestSensingStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const licenseAuthIncludeEJJ = computedAsync(() => systemAuthStore.checkLicenseAuthIncludeEJJ);

usePageStoreHook().setTitle(route.meta.title as string);

const { pagination } = useTableConfig();
const { columnsConfig } = genOutGoingFactoryExperimentTableColumnsConfig();
const loading = ref(false);
const list = ref<IOutGoingFactoryQmxExperiment[]>([]);

const delExperiment = (row: IOutGoingFactoryQmxExperiment) => {
  ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    await deleteOutGoingFactoryQMXExperimentDetail(row.id);
    ElMessage({ type: "success", message: "删除成功" });
    props.deleteExperimentSuccess();
    productionTestSensingStore.refreshProductionProcessStatus();
    reloadList();
  });
};

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const params: IOutGoingFactoryNotJfnyReq = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    processId: predeliveryExperimentStore.activeProcess.processId
  };
  if (salesOrderDetailStore.isCable) {
    params.productionId = fillInDataStore.dataId;
    const { data } = await queryOutGoingFactoryQMXExperimentList(params);
    list.value = data.list;
    pagination.total = data.total;
  } else {
    params.workOrderId = fillInDataStore.dataId;
    const { data } = await queryNonCableOutGoingFactoryQMXExperimentList(params);
    list.value = data.list;
    pagination.total = data.total;
  }
}, loading);

/**
 * @description: 重载列表
 */
function reloadList() {
  requestList();
}

emitter.on("refreshOutGoingFactoryExperimentList", reloadList);

onMounted(() => {
  requestList();
});

onUnmounted(() => {
  emitter.off("refreshOutGoingFactoryExperimentList", reloadList);
});
</script>

<style scoped></style>
