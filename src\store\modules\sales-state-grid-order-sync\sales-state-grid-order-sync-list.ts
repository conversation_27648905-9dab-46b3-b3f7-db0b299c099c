import { defineStore } from "pinia";
import { IStateGridOrderSync, IStateGridOrderSyncDetailList, IStateGridOrderSyncParams } from "@/models";
import * as api from "@/api/state-grid-order-sync";
import { PurchaseChannel, OrderSyncPriorityEnum, StateGridOrderSyncType } from "@/enums";
import { useSalesOrderSyncInfo } from "./sales-state-grid-order-sync-detail";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";

type StateGridOrderSyncListType = {
  syncs: Array<IStateGridOrderSync>;
  salesOrderId: string;
};

/**
 * @description: 销售订单的eip平台同步列表
 */
export const useSalesStateGridOrderSyncListStore = defineStore({
  id: "sales-order-eip-sync-list",
  state: (): StateGridOrderSyncListType => ({
    syncs: [],
    salesOrderId: undefined
  }),
  actions: {
    setSalesOrderId(id: string) {
      this.salesOrderId = id;
    },
    async refreshSalesOrderSyncList() {
      this.syncs = (await api.getSalesGridOrderSyncList(this.salesOrderId)).data;
    },
    updateStateGridOrderSyncDetail(id: string, detail: IStateGridOrderSyncDetailList[]) {
      const sync = this.syncs.find(s => s.id === id);
      if (sync) {
        sync.detail = detail;
      }
    },
    async syncAll(orderLineId: string, orderId?: string) {
      const detailStore = useSalesOrderSyncInfo();
      let channel: number;
      switch (detailStore.activeDetailType) {
        case SyncOrderTabEnum.SYNC_STATE__GRID_ORDER:
          channel = PurchaseChannel.EIP;
          break;
        case SyncOrderTabEnum.SYNC_SHANGHAI_IOT:
          channel = PurchaseChannel.IOT;
          break;
        case SyncOrderTabEnum.SYNC_CSG_GUANGZHOU:
          channel = PurchaseChannel.CSG_GuangZhou;
          break;
      }
      const priority = OrderSyncPriorityEnum.COMMON_PRIORITY;
      const params: IStateGridOrderSyncParams = {
        orderId: orderId ? orderId : this.salesOrderId,
        orderItemId: orderLineId,
        dataType: StateGridOrderSyncType.ALL,
        channel,
        priority
      };
      return api.syncStateGridOrder(params);
    }
  }
});
