<template>
  <div class="link-jfny-experiment-content">
    <!-- 搜索 -->
    <div class="search mb-5">
      <el-row :align="'middle'">
        <el-col :span="12">
          <div class="search-input">
            <el-input
              v-model="searchWords"
              placeholder="请输入试验编号/成品编号/产品型号/盘号"
              @keydown.enter="handleSearch"
              @clear="handleSearch"
              clearable
            />
          </div>
        </el-col>
        <el-button class="ml-2" type="primary" @click="handleSearch">查询</el-button>
      </el-row>
    </div>

    <!-- 耐压试验数据 -->
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden"
      row-key="id"
      :data="exFactoryExperimentJFNYStore.waitLinkJfnyExperimentData"
      :columns="jfnyColumns"
      :pagination="pagination"
      showOverflowTooltip
      :height="400"
      :max-height="400"
      @row-click="handleCurrentChange"
      @selection-change="handleSelectionChange"
      @page-current-change="pageNoChange"
      @page-size-change="pageSizeChange"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <emptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { useColumns } from "./columns";
import { TableWidth } from "@/enums";
import emptyData from "@/assets/svg/empty_data.svg?component";
import { IJfnyExperimentList } from "@/models/production-test-sensing/i-out-going-factory-jfny-experiment";
import { useExFactoryExperimentJFNYStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useTableConfig } from "@/utils/useTableConfig";
import { watchEffect } from "vue";

const props = withDefaults(
  defineProps<{
    productionId: string;
  }>(),
  {}
);

const searchWords = ref();
const tableInstance = ref<PureTableInstance>();
const multipleRawMaterialData = ref<any[]>([]);
const exFactoryExperimentJFNYStore = useExFactoryExperimentJFNYStore();
const loading = ref<boolean>(false);
const initExperimentWaitLinkList = useLoadingFn(getExperimentWaitLinkList, loading);

const { columns } = useColumns();
const { pagination } = useTableConfig();
pagination.pageSize = 10;
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};
const multiple = ref(true);
const sectionColumns = multiple.value
  ? [
      {
        width: TableWidth.check,
        type: "selection"
      }
    ]
  : [];
const jfnyColumns = [...sectionColumns, ...columns];
const elTableInstance = computed(() => tableInstance.value.getTableRef());

watchEffect(() => {
  pagination.total = exFactoryExperimentJFNYStore.waitLinkJfnyTotal;
});

// 关联表格数据
onMounted(() => {
  initExperimentWaitLinkList();
});

// 获取待关联的局放耐压试验列表
async function getExperimentWaitLinkList() {
  const params = {
    productionId: props.productionId,
    keyWords: null,
    ...pageInfo
  };

  await exFactoryExperimentJFNYStore.queryWaitLinkJfny(params);
}

// 选择原材料数据
const handleCurrentChange = (line: IJfnyExperimentList) => {
  elTableInstance.value.toggleRowSelection(line, undefined);
};
// 选择原材料数据
const handleSelectionChange = (line: IJfnyExperimentList[]) => {
  multipleRawMaterialData.value = line;
  exFactoryExperimentJFNYStore.linkCheckJfnyExperimentData = multipleRawMaterialData.value;
};

// 查询待关联的试验列表
const handleSearch = () => {
  initPagination();
  exFactoryExperimentJFNYStore.queryWaitLinkJfny({
    productionId: props.productionId,
    keyWords: searchWords.value,
    ...pageInfo
  });
};

/**
 * 根据页码变化查询列表
 */
const pageNoChange = (pageNo: number) => {
  exFactoryExperimentJFNYStore.queryWaitLinkJfny({
    pageNo
  });
};

/**
 * 根据页码数量改变查询列表
 */
const pageSizeChange = (pageSize: number) => {
  initPagination();
  exFactoryExperimentJFNYStore.queryWaitLinkJfny({
    pageNo: 1,
    pageSize
  });
};

/**
 * 重置分页信息
 */
const initPagination = () => {
  pagination.currentPage = 1;
};
</script>

<style scoped lang="scss"></style>
