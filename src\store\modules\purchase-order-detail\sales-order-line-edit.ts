import { defineStore } from "pinia";
import { ISalesOrderLine, ISalesOrderLineForm, UPDATE_SALES_ORDER_LINE_PROPERTIES } from "@/models";
import * as api from "@/api";
import { pick } from "lodash-unified";

export const usePurchaseOrderDetailSalesOrderLineEditStore = defineStore({
  id: "cx-purchase-order-detail-sales-order-line-edit",
  state: () => ({
    loading: false,
    visible: false,
    salesOrderLine: undefined as ISalesOrderLine
  }),
  actions: {
    openEditDialog(id: string) {
      this.visible = true;
      this.loading = true;
      api
        .getSalesOrderLineById(id)
        .then(res => (this.salesOrderLine = res.data))
        .finally(() => (this.loading = false));
    },
    updateSalesOrderLine(formData: ISalesOrderLineForm) {
      const data = pick(formData, UPDATE_SALES_ORDER_LINE_PROPERTIES);
      return api.updateSalesOrderLine(data);
    }
  }
});
