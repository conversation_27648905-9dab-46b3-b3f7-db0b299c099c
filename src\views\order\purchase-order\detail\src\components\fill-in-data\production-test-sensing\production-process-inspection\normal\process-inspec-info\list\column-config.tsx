import { ColumnWidth } from "@/enums";
import { useDynamicFormLabel } from "@/utils/useDynamicFormSelect";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 过程检测表格配置
 */
export function getProcessInspecTableColumnsConfig() {
  const { getSelectLabel } = useDynamicFormLabel();
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig: TableColumnList = [
    {
      label: "过程检测编号",
      prop: "code",
      minWidth: ColumnWidth.Char10,
      fixed: "left"
    },
    {
      label: "设备",
      prop: "deviceName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "检测时间",
      prop: "checkTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "检测项",
      prop: "processDetailDataValue",
      minWidth: ColumnWidth.Char15,
      formatter(row) {
        return row.processDetailDataValue
          .filter(item => !item.collectionType)
          .map(item => {
            return `${item.targetName}： ${getSelectLabel(item)} ${item.unit}；`;
          });
      }
    },
    {
      label: "操作",
      prop: "operation",
      slot: "operation",
      width: ColumnWidth.Char10,
      fixed: "right"
    }
  ];

  return { columnsConfig };
}
