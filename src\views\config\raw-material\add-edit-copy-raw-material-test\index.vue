<template>
  <div class="inline-block">
    <slot name="trigger" :open-dialog="openDialog" :close-dialog="closeDialog" />
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <div v-loading="loading">
        <base-info :raw-material-detail="rawMaterialDetail" />
        <title-bar title="原材料检验信息" />
        <inspect-info-form
          ref="inspectInfoFormRef"
          class="mt-4"
          :mode="props.mode"
          :raw-material-detail="rawMaterialDetail"
          :raw-material-insect-detail-loaded="rawMaterialInsectDetailLoaded"
          @set-inspect-item-list="setInspectItemListData"
          @init-inspect-item-list="initInspectItemList"
        />
        <title-bar title="原材料检测" />
        <inspect-item-list ref="inspectItemListRef" class="mt-4" :raw-material-detail="rawMaterialDetail" />
        <title-bar title="原材料报告" class="mt-4" />
        <report-list ref="reportListRef" class="mt-4" :raw-material-detail="rawMaterialDetail" />
      </div>
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="saveLoading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import TitleBar from "@/components/TitleBar";
import BaseInfo from "./base-info.vue";
import InspectInfoForm from "./inspect-info-form.vue";
import InspectItemList from "./inspect-item-list.vue";
import ReportList from "./report-list.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  getDetailRawMaterialById,
  getDetailRawMaterialInspectInfoById,
  getRawMaterialCheckInfoByProductStePro,
  addRawmaterialInspecInfo as addRawMaterialInspect,
  putRawmaterialInspecInfo as editRawMaterialInspect
} from "@/api/base-config/raw-material";
import { IAddRawMaterialInspection, ISaveAddRawMaterial } from "@/models/raw-material/i-raw-material-res-v2";
import { useRawMaterialCheckInfoHook } from "../hook/useRawMaterialCheck";

/**
 * 新增/编辑/复制 原材料检 弹窗
 * 根据不同模式，执行不同的操作：前置请求、保存请求等
 * 组成：
 * 1. 基础信息条
 * 2. 原材料信息表单
 * 3. 原材料检测项列表
 * 4. 原材料报告列表
 */

const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add" | "copy";
  /** 标题 */
  title: string;
  /** 原材料id */
  materialId: string;
  /** 原材料检id */
  id?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const { getRawMaterialCheckFormData, getRawMaterialCheckFileData } = useRawMaterialCheckInfoHook();

const dialogVisible = ref(false);

/** 原材料详情 */
const rawMaterialDetail = ref<ISaveAddRawMaterial>({} as ISaveAddRawMaterial);
/** 原材料检验详情 */
const rawMaterialInsectDetail = ref<IAddRawMaterialInspection>({} as IAddRawMaterialInspection);

const inspectInfoFormRef = ref<InstanceType<typeof InspectInfoForm>>();
const inspectItemListRef = ref<InstanceType<typeof InspectItemList>>();
const reportListRef = ref<InstanceType<typeof ReportList>>();
const saveLoading = ref(false);
const loading = ref(false);
// 是否已经初始化原材料检测项列表
const isInitInspectItemList = ref(false);
// 原材料检测详情是否加载完成
const rawMaterialInsectDetailLoaded = ref(false);

const isAddMode = computed(() => props.mode === "add");
const isEditMode = computed(() => props.mode === "edit");
const isCopyMode = computed(() => props.mode === "copy");

/**
 * @description: 校验两张表单
 */
const validateForms = async () => {
  const inspectInfoValid = await inspectInfoFormRef.value?.validateForm();
  const inspectItemListValid = await inspectItemListRef.value?.validateForm();
  const reportListValid = await reportListRef.value?.validateForm();
  return inspectInfoValid && inspectItemListValid && reportListValid;
};

/**
 * @description: 获取最终(合计)表单值
 */
async function getFinallyFormValue() {
  const baseInfoFormVal = inspectInfoFormRef.value?.getFormValue();
  const inspectItemListFormVal = await inspectItemListRef.value?.getFormValue();
  const reportListFormVal = reportListRef.value?.getFormValue();

  return {
    id: props.id,
    materialId: props.materialId,
    rawMetadataValue: [...inspectItemListFormVal, ...reportListFormVal],
    ...baseInfoFormVal
  };
}

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const valid = await validateForms();
  if (!valid) {
    return;
  }
  const finallyFormValue = await getFinallyFormValue();

  requestSave(finallyFormValue);
};

/**
 * @description: 弹窗开启后置事件
 */
const postDialogOpen = useLoadingFn(async () => {
  // 获取原材料详情
  const { data: rawMaterialDetailData } = await getDetailRawMaterialById(props.materialId);
  rawMaterialDetail.value = rawMaterialDetailData;

  // 新增
  if (isAddMode.value) {
    await addModePreHandle(rawMaterialDetailData);
    return;
  }

  // 获取原材料检验详情
  const { data: rawMaterialInsectDetailData } = await getDetailRawMaterialInspectInfoById(props.id);
  if (!rawMaterialInsectDetailData) {
    return;
  }

  // 编辑
  if (isEditMode.value) {
    editModePreHandle(rawMaterialInsectDetailData);
    return;
  }

  // 复制
  if (isCopyMode.value) {
    copyModePreHandle(rawMaterialInsectDetailData);
    return;
  }
}, loading);

/**
 * @description: 新增模式打开前置处理
 */
async function addModePreHandle(rawMaterialDetailData: ISaveAddRawMaterial) {
  // 获取默认检测项
  const { data: defaultCheckInfoData } = await getRawMaterialCheckInfoByProductStePro(
    rawMaterialDetailData.processCode,
    rawMaterialDetailData.modelCode
  );
  // 获取原材料检测项列表数据
  const inspectItemListData = getRawMaterialCheckFormData(defaultCheckInfoData);

  // 获取原材料检测报告数据
  const reportListData = getRawMaterialCheckFileData(defaultCheckInfoData);
  inspectItemListRef.value?.initFormValue(inspectItemListData);
  reportListRef.value?.initFormValue(reportListData);
}

/**
 * @description: 编辑模式打开前置处理
 */
function editModePreHandle(rawMaterialInspectDetailData: IAddRawMaterialInspection) {
  // 初始化检测项信息表单
  rawMaterialInsectDetail.value = rawMaterialInspectDetailData;
  inspectInfoFormRef.value?.initFormValue(rawMaterialInspectDetailData);
  rawMaterialInsectDetailLoaded.value = true;

  // 初始化原材料检测报告数据
  const reportListData = getRawMaterialCheckFileData(rawMaterialInsectDetail.value.rawMetadataValue);
  reportListRef.value?.initFormValue(reportListData);
}

/**
 * @description: 复制模式打开前置处理
 */
function copyModePreHandle(rawMaterialInspectDetailData: IAddRawMaterialInspection) {
  // 初始化检测项信息表单
  rawMaterialInsectDetail.value = rawMaterialInspectDetailData;
  inspectInfoFormRef.value?.initFormValue(Object.assign({}, rawMaterialInspectDetailData, { inspectBatchNo: "" }));
  rawMaterialInsectDetailLoaded.value = true;

  // 初始化原材料检测报告数据
  const reportListData = getRawMaterialCheckFileData(rawMaterialInsectDetail.value.rawMetadataValue);
  reportListRef.value?.initFormValue(reportListData);
}

/**
 * @description: 设置原材料检测项列表数据
 */
function setInspectItemListData(data: Array<IAddRawMaterialInspection>) {
  inspectItemListRef.value?.initFormValue(data);
}

/**
 * @description: 将获取到的详情数据填充给检测项列表
 */
function initInspectItemList() {
  if (isInitInspectItemList.value || props.mode === "add") {
    return;
  }
  isInitInspectItemList.value = true;
  const inspectItemListData = getRawMaterialCheckFormData(rawMaterialInsectDetail.value.rawMetadataValue);
  inspectItemListRef.value?.initFormValue(inspectItemListData);
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}

/**
 * @description: 请求保存
 */
const requestSave = useLoadingFn(async (params: IAddRawMaterialInspection) => {
  let apiFunction;
  switch (props.mode) {
    case "add":
      apiFunction = addRawMaterialInspect;
      break;
    case "edit":
      apiFunction = editRawMaterialInspect;
      break;
    case "copy":
      apiFunction = addRawMaterialInspect;
      break;
  }

  const { data } = await apiFunction(params);
  const tip = {
    add: "新增",
    edit: "编辑",
    copy: "复制"
  };

  if (data) {
    emits("postSaveSuccess");
    ElMessage({
      message: `${tip[props.mode]}成功`,
      type: "success"
    });
    closeDialog();
  }
}, saveLoading);

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, visible => {
  isInitInspectItemList.value = false;
  rawMaterialInsectDetailLoaded.value = false;
  if (visible) {
    postDialogOpen();
  }
});
</script>

<style scoped></style>
