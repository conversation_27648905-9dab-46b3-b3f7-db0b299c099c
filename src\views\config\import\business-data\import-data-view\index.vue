<template>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="state.data"
      :columns="state.columns || []"
      showOverflowTooltip
      :loading="state.loading"
      :pagination="pagination"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
    >
      <template #empty>
        <CxEmptyData />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from "vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { queryImportDataView } from "@/api/import";
import { usePageStoreHook } from "@/store/modules/page";
import CxEmptyData from "@/components/CxEmpty";
import { useRoute } from "vue-router";
import { useColumnHook } from "../column-hook";
import { ImportDataTypeEnum, ImportDataTypeMapName } from "@/enums";

const { dataColumns } = useColumnHook();
const route = useRoute();
const { pagination } = useTableConfig();
pagination.pageSize = 20;

const state = reactive<{
  data: Array<{ [key: string]: any }>;
  columns: TableColumnList;
  dataType?: ImportDataTypeEnum;
  bathNo: string;
  loading: boolean;
}>({
  dataType: undefined,
  data: [],
  columns: [],
  bathNo: "",
  loading: false
});
onMounted(() => {
  if (route.params?.type) {
    state.dataType = Number(route.params.type) as ImportDataTypeEnum;
    state.columns = dataColumns(state.dataType);
    usePageStoreHook().setTitle(`${ImportDataTypeMapName[state.dataType]}导入数据列表`);
  }
  if (route.params?.bathNo) {
    state.bathNo = route.params.bathNo as string;
  }

  if (typeof state.dataType === "number" && state.bathNo) {
    query();
  }
});

/**
 * 根据页码数量改变查询
 */
const onPageSizeChange = () => {
  pagination.currentPage = 1;
  query();
};

/**
 * 页码改变查询
 */
const onCurrentPageChange = () => {
  query();
};

async function query() {
  state.loading = true;
  const res = await queryImportDataView({
    batchCode: state.bathNo,
    dataType: state.dataType,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize
  });
  state.loading = false;
  state.data = res.data.list;
  pagination.total = res.data.total;
}
</script>

<style scoped lang="scss"></style>
