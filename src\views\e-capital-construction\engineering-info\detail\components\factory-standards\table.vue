<template>
  <!-- 出厂试验参数标准 -->
  <div class="flex justify-end pb-3">
    <el-button
      :loading="loading"
      v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
      type="primary"
      @click="handleSynchronization"
      >一键同步</el-button
    >
    <el-button v-if="!tableData.length" :loading="loading" :icon="Plus" type="primary" @click="handleAdd"
      >新增</el-button
    >
  </div>
  <PureTable
    row-key="id"
    class="flex-1 overflow-hidden pagination"
    :data="tableData"
    :columns="columnsConfig"
    size="large"
    v-loading="loading"
    showOverflowTooltip
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
    <template #pullStatus="data">
      <el-tag
        :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
        @click="handleShowDetail(data)"
        class="cursor-pointer"
      >
        {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
      </el-tag>
    </template>
    <template #operation="data">
      <ElButton type="primary" link @click="handleEdit(data.row)"> 编辑 </ElButton>
      <ElButton type="danger" link @click="handleDelete(data.row.id)"> 删除 </ElButton>
    </template>
  </PureTable>
  <!-- 同步明细 -->
  <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
  <el-dialog
    v-model="state.dialogShow"
    :title="state.isEdit ? '编辑' : '新增'"
    width="800px"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCloseDialog()"
  >
    <PureTable
      row-key="id"
      ref="singleTableRef"
      :data="tableDetailData"
      :columns="columnsConfigDetail"
      size="large"
      v-loading="loading"
      showOverflowTooltip
      v-model:pagination="pagination"
      highlight-current-row
      @page-current-change="requestDetailList"
      @page-size-change="requestDetailList"
      @current-change="changeSelectInspection"
    >
      <template #radio="{ row }">
        <el-radio v-model="row.radio" :label="row.id" @change="changeSelectInspection(row)">{{ "" }}</el-radio>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
    <template #footer>
      <span>
        <el-button :loading="loading" @click="handleCloseDialog()">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSave()">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useConfirm } from "@/utils/useConfirm";
import { Plus } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";
import { useColumns, useDetailColumns } from "./column-config";
import { getTestParameterReportList } from "@/api/e-capital-construction/test-parameter/index";
import { EParamsStandardReport } from "@/models";
import type { TableInstance } from "element-plus";
// utils
import {
  FactoryStandardInfoApi,
  FactoryStandardDeleteApi,
  FactoryStandardCreateApi,
  FactoryStandardEditApi,
  IntermediateSyncByVoucherTypeApi
} from "@/api/e-capital-construction/engineering-info/index";
import { PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
import { FactoryStandardModel, EParamsStandard } from "@/models";
import { PermissionKey } from "@/consts";

const { pagination } = useTableConfig();

const loading = ref(false);

const route = useRoute();
const tableData = ref([] as Array<FactoryStandardModel>);
const tableDetailData = ref([] as Array<EParamsStandard>);
const singleTableRef = ref<TableInstance>();

const state = reactive({
  isEdit: false,
  dialogShow: false,
  selectRow: {} as FactoryStandardModel,
  selectDialogRow: {} as EParamsStandard
});
const columnsConfig = useColumns();
const columnsConfigDetail = useDetailColumns();
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("FactoryStandards", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
/**
 * @description: 请求列表数据
 */
const requestDetailList = useLoadingFn(async () => {
  const params: EParamsStandardReport = {
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    example: route.query.type as string,
    equipmentCode: route.query.equipmentCode as string,
    equipmentName: route.query.equipmentName as string
  };
  const { data } = await getTestParameterReportList(params);
  tableDetailData.value = data.list;
  tableDetailData.value.forEach(item => {
    if (item.id !== state.selectRow.paramStdId) {
      item.radio = "";
    } else {
      item.radio = state.selectRow.paramStdId;
      state.selectDialogRow = JSON.parse(JSON.stringify(item));
    }
  });
  pagination.total = data.total;
}, loading);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await FactoryStandardInfoApi(route.query.id as string);
  tableData.value = data;
}, loading);

/**
 * @description: 新增
 */
const handleAdd = () => {
  state.dialogShow = true;
  state.selectRow = {} as FactoryStandardModel;
  state.selectDialogRow = {} as EParamsStandard;
  state.isEdit = false;
  requestDetailList();
};

/**
 * @description: 取消
 */
const handleCloseDialog = () => {
  state.dialogShow = false;
  state.isEdit = false;
};

/**
 * @description: 编辑
 */
const handleEdit = async (row: FactoryStandardModel) => {
  state.selectRow = row as FactoryStandardModel;
  state.isEdit = true;
  state.dialogShow = true;
  requestDetailList();
};

/**
 * @description: 删除
 */
const handleDelete = async (id: string) => {
  if (!(await useConfirm("是否确认删除", "删除确认"))) {
    return;
  }
  const { data } = await FactoryStandardDeleteApi(id as string);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
};

/**
 * @description: 保存
 */
const handleSave = useLoadingFn(async () => {
  const params = {
    paramStdId: state.selectDialogRow.id as string,
    equipmentId: route.query.id as string,
    equipmentType: route.query.type as string,
    id: state.selectRow?.id
  };
  const { data } = state.isEdit ? await FactoryStandardEditApi(params) : await FactoryStandardCreateApi(params);
  if (data) {
    ElMessage.success(state.isEdit ? "编辑成功 ！" : "新增成功！");
    requestList();
    state.isEdit = false;
    state.dialogShow = false;
  }
}, loading);

const changeSelectInspection = (row: EParamsStandard) => {
  if (row && row.id) {
    row.radio = row.id;
    state.selectDialogRow = row;
    tableDetailData.value.forEach(item => {
      if (item.id !== row.id) {
        item.radio = "";
      }
    });
  }
};

onMounted(() => {
  requestList();
});
</script>

<style lang="scss" scoped>
.label {
  @apply text-base text-secondary mb-2;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
