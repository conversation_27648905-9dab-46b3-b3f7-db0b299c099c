<template>
  <el-card v-for="{ yAxisData, xAxisData, name } in list" :key="name" class="mt-4">
    <div>{{ name }}</div>
    <chart class="h-60" :x-axis-data="xAxisData" :y-axis-data="yAxisData" />
  </el-card>
</template>

<script setup lang="ts">
import Chart from "./chart.vue";

defineProps({
  list: Array<{
    yAxisData: Array<number>;
    xAxisData: Array<number>;
    name: string;
  }>
});
</script>

<style scoped lang="scss"></style>
