<template>
  <el-dialog
    v-model="editVisible"
    title="编辑生产订单"
    class="default !w-[900px]"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-loading="loading">
      <ProductOrder :purchaseOrderId="orderId" ref="editFormRef" v-if="isPurchaseOrder" />
      <SalesOrderProductOrder :salesOrderId="orderId" ref="editFormRef" v-if="isSalesOrder" />
    </div>

    <template #footer>
      <span>
        <el-button @click="editVisible = false">取消</el-button>
        <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
          >保存，并重新同步</el-button
        >
        <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import ProductOrder from "@/views/order/purchase-order/detail/src/components/production-order/add-product-order/product-order/index.vue";
import SalesOrderProductOrder from "@/views/order/sales-order/detail/src/components/production-order/add-product-order/product-order/index.vue";
import { useEdit, usePatchValue } from "@/views/components/state-grid-order-sync/hooks";
import { ICreateProductOrder, IProductionOrderSync, IProductOrder } from "@/models";
import { useProductOrderStore, useSalesProductOrderStore } from "@/store/modules";
import { OrderType } from "@/enums";
import { computed } from "vue";

const props = defineProps<{
  orderId: string;
  orderType: OrderType;
}>();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof ProductOrder>>(updateProduction);
const { loading, handlePatchValue } = usePatchValue(patchValue);

const orderId = computed(() => props.orderId);
const isPurchaseOrder = computed(() => props.orderType === OrderType.PURCHASE);
const isSalesOrder = computed(() => props.orderType === OrderType.SALES);
const productStore = computed(() => {
  if (isPurchaseOrder.value) {
    return useProductOrderStore();
  }
  if (isSalesOrder.value) {
    return useSalesProductOrderStore();
  }
  throw "需要确定订单类型";
});

function openEditDialog(data: IProductionOrderSync) {
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.ipoNo
  };
  handlePatchValue(data.dataId);
}

async function patchValue(dataId: string) {
  const productionOrder: IProductOrder = await productStore.value.getProductOrderDetailById(dataId);
  productStore.value.setCreateProductOrder(productionOrder);
}

async function updateProduction() {
  const data: ICreateProductOrder | boolean = await editFormRef.value.getFormValue();
  if (typeof data === "boolean") {
    return;
  }
  await productStore.value.editProductOrder(data);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
