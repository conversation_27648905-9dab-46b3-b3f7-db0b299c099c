<template>
  <el-input :model-value="modelValue" @update:model-value="handleInput" @blur="handleBlur" clearable>
    <template #suffix>
      <span>%</span>
    </template>
  </el-input>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();

const handleInput = (value: string) => {
  // 移除非数字字符
  let newValue = value.replace(/[^\d.]/g, "").replace(/^0+(\d)/, "$1");

  // 确保只有一个小数点
  const parts = newValue.split(".");
  if (parts.length > 2) {
    newValue = parts[0] + "." + parts.slice(1).join("");
  }

  // 限制小数位数（这里限制为两位小数）
  if (parts[1] && parts[1].length > 2) {
    newValue = parseFloat(newValue).toFixed(2);
  }

  emit("update:modelValue", newValue);
};

const handleBlur = () => {
  let value = props.modelValue;
  // 在失去焦点时，如果值为空或不是有效数字，设置为 ""
  if (value === "" || isNaN(parseFloat(value))) {
    value = "";
  }

  // 确保值不超过 100
  const numValue = parseFloat(value);
  if (numValue > 100) {
    value = "100";
  }

  emit("update:modelValue", value);
};
</script>
