<template>
  <el-select
    class="w-full"
    :class="!options.length && subclassCode ? 'hide-select-text' : ''"
    v-model="subclassCode"
    filterable
    :placeholder="props.placeholder"
    :disabled="props.disabled"
    :clearable="clearable"
    @clear="
      () => {
        emit('clear');
      }
    "
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from "vue";
import { ICategory, IOption, IOrderSubclassParams } from "@/models";
import { useCategoryStore } from "@/store/modules";
import { OrderType } from "@/enums";

const props = withDefaults(
  defineProps<{
    modelValue?: string | Array<string>;
    // 是否根据物资品类筛选物资种类
    limitByCategoryCode?: boolean;
    // 物资品类
    categoryCode?: string;
    disabled?: boolean;
    // 是否根据订单ID筛选物资种类
    limitByOrder?: boolean;
    purchaseOrderId?: string;
    salesOrderId?: string;
    placeholder?: string;
    clearable?: boolean;
  }>(),
  {
    placeholder: "请选择",
    clearable: false
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", value: string | Array<string>): void;
  (e: "clear"): void;
}>();

const categoryStore = useCategoryStore();

const options = ref<Array<IOption>>([]);

const subclassCode = computed({
  get() {
    return props.modelValue;
  },
  set(value: string | Array<string>) {
    emit("update:modelValue", value);
  }
});

watchEffect(async onCleanup => {
  let canceled = false;
  onCleanup(() => (canceled = true));
  const subclasses: Array<ICategory> = await querySubclasses();
  if (canceled) {
    return;
  }
  updateOptions(subclasses);
});

/**
 * @description: 请求物资种类列表
 */
function querySubclasses() {
  // 请求订单下可选的物资种类列表
  if (props.limitByOrder) {
    return queryOrderSubclasses(props.categoryCode, props.purchaseOrderId, props.salesOrderId);
  }

  // 请求物资品类下对应的物资种类列表
  if (props.limitByCategoryCode || props.categoryCode) {
    return queryCategorySubclasses(props.categoryCode);
  }
  return categoryStore.allSubclasses;
}

/**
 * @description: 请求
 */
async function queryOrderSubclasses(
  categoryCode: string,
  purchaseOrderId: string | undefined,
  salesOrderId: string | undefined
): Promise<Array<ICategory>> {
  if (!categoryCode) {
    return [];
  }
  if (!purchaseOrderId && !salesOrderId) {
    return [];
  }
  const params: IOrderSubclassParams = {
    categoryCode,
    orderId: purchaseOrderId || salesOrderId,
    viewMode: purchaseOrderId ? OrderType.PURCHASE : OrderType.SALES
  };
  return categoryStore.querySubclassesByOrderId(params);
}

/**
 * @description: 根据物资品类code获取该品类下的物资种类
 */
async function queryCategorySubclasses(categoryCode: string): Promise<Array<ICategory>> {
  if (!categoryCode) {
    return [];
  }
  return categoryStore.querySubclassesByCategory(categoryCode);
}

function updateOptions(subclasses: Array<ICategory>) {
  options.value = Array.isArray(subclasses)
    ? subclasses.map(subclass => ({
        label: subclass.categoryName,
        value: subclass.categoryCode
      }))
    : [];
}

function getSelectedSubClassName() {
  const selectedItem = options.value.find(({ value }) => value === subclassCode.value);
  if (selectedItem) {
    return selectedItem.label;
  }
  return "";
}

defineExpose({
  getSelectedSubClassName
});
</script>

<style scoped lang="scss">
.hide-select-text {
  :deep(.el-input__inner) {
    color: transparent !important;
    -webkit-text-fill-color: transparent !important;
  }
}
</style>
