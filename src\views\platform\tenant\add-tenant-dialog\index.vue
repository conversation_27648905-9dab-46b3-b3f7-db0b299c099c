<template>
  <div class="inline-block">
    <el-button size="large" type="primary" :icon="Plus" @click="openDialog"> 新增租户 </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="新增租户"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="handleDialogClosed"
    >
      <!-- 内容 -->
      <el-steps :active="activeStep" class="mb-3 mx-5">
        <el-step title="填写基础信息" />
        <el-step title="授权配置" />
        <el-step title="完成" />
      </el-steps>
      <base-info-form ref="baseInfoFormRef" v-show="activeStep === 0" />
      <authorization-form ref="authorizationFormRef" v-show="activeStep === 1" />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-if="activeStep !== 0" @click="handleClickPrevBtn">上一步</el-button>
        <el-button v-else type="primary" @click="handleClickNextBtn">下一步</el-button>
        <el-button v-if="activeStep === 1" type="primary" @click="handleClickSaveBtn" :loading="loading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import BaseInfoForm from "./base-info-form.vue";
import AuthorizationForm from "./authorization-form.vue";
import { addTenant } from "@/api";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ITenantForm } from "@/models";

const loading = ref(false);

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);
const activeStep = ref(0);
const baseInfoFormRef = ref<InstanceType<typeof BaseInfoForm>>();
const authorizationFormRef = ref<InstanceType<typeof AuthorizationForm>>();

const requestAddTenant = useLoadingFn(async (params: ITenantForm) => {
  const { data } = await addTenant(params);
  return data;
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const validResult = await authorizationFormRef.value.validateForm();
  if (!validResult) {
    return;
  }

  const finallyForm = {
    ...baseInfoFormRef.value.getFormValue(),
    ...authorizationFormRef.value.getFormValue()
  };

  const data = await requestAddTenant(finallyForm);

  // 处理保存后续事件
  closeDialog();
  if (data) {
    emits("postSaveSuccess");
    ElMessage({
      message: "新增成功",
      type: "success"
    });
  }
};

/**
 * @description: 当前步骤表单验证通过，进入下一步
 */
async function handleClickNextBtn() {
  const validResult = await baseInfoFormRef.value.validateForm();
  if (validResult) {
    activeStep.value = activeStep.value + 1;
  }
}

function handleClickPrevBtn() {
  activeStep.value = activeStep.value - 1;
}

function handleDialogClosed() {
  activeStep.value = 0;
}

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
