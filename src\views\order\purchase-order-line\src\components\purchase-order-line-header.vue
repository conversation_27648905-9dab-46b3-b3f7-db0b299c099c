<template>
  <div>
    <p class="mb-4 text-xl text-primaryText font-semibold">采购订单行</p>
    <SearchForm
      keyword-filter-key="orFilters"
      :keyword-fields="keywordFields"
      :search-form="searchForm"
      :search-items="items"
      placeholder="输入采购订单号/采购订单行号/项目名称/合同名称"
      @search="search"
    />
  </div>
</template>

<script setup lang="ts">
import { defineComponent, h, inject, watch } from "vue";
import SearchForm, { IKeywordField, ISearchItem } from "@/components/SearchForm";
import { ElDatePicker, ElRadio, ElRadioGroup } from "element-plus";
import {
  PurchaseOrderSearchBySyncStatusEnum,
  PurchaseOrderSearchByTriggerScoreEnum
} from "@/enums/purchase-order/search-order.enum";
import SubclassSelect from "@/views/components/subclass-select";
import { purchaseOrderLineDataKey } from "../tokens/purchase-order-line-data-token";

const { searchForm, updateCurrentPage } = inject(purchaseOrderLineDataKey);

const keywordFields: Array<IKeywordField> = [
  { key: "po_no", title: "采购订单号" },
  { key: "po_item_no", title: "采购订单行号" },
  { key: "prj_name", title: "项目名称" },
  { key: "con_name", title: "合同名称" }
];

const SyncStatus = defineComponent({
  name: "syncType",
  render() {
    const radios = [
      { label: "全部", value: undefined },
      { label: "未同步", value: PurchaseOrderSearchBySyncStatusEnum.NO_SYNC },
      { label: "已触发同步", value: PurchaseOrderSearchBySyncStatusEnum.SYNCED },
      { label: "同步失败", value: PurchaseOrderSearchBySyncStatusEnum.SYNC_FAIL }
    ].map(option => h(ElRadio, { label: option.value, border: true }, () => option.label));
    return h(ElRadioGroup, () => radios);
  }
});
const SyncSubClassComponents = defineComponent({
  name: "subclassCode",
  render() {
    return h(SubclassSelect, { clearable: true });
  }
});
const TriggerStatus = defineComponent({
  name: "triggerType",
  render() {
    const radios = [
      { label: "全部", value: undefined },
      { label: "未触发", value: PurchaseOrderSearchByTriggerScoreEnum.NO_TRIGGER },
      { label: "已触发评分", value: PurchaseOrderSearchByTriggerScoreEnum.TRIGGERED },
      { label: "评分触发失败", value: PurchaseOrderSearchByTriggerScoreEnum.TRIGGER_FAIL }
    ].map(option => h(ElRadio, { label: option.value, border: true }, () => option.label));
    return h(ElRadioGroup, () => radios);
  }
});

const items: Array<ISearchItem> = [
  {
    key: "sellerSignTime",
    full: false,
    label: "合同签订日期：",
    style: { width: "500px" },
    component: ElDatePicker,
    componentProps: {
      type: "daterange",
      rangeSeparator: "～",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  { full: false, key: "subclassCode", label: "物资种类：", component: SyncSubClassComponents },
  { full: true, key: "syncType", label: "同步状态：", component: SyncStatus },

  { full: true, key: "triggerType", label: "触发结果：", component: TriggerStatus }
];

watch([() => searchForm.syncType, () => searchForm.triggerType], search);

function search() {
  updateCurrentPage(1);
}
</script>

<style scoped lang="scss"></style>
