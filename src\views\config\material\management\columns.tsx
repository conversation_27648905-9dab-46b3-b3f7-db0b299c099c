import { fullDateFormat } from "@/consts";
import { TableWidth, VoltageClassesEnum } from "@/enums";
import { IMaterial } from "@/models";
import { formatEnum } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "物资种类",
      prop: "subClassName",
      width: TableWidth.name
    },
    {
      label: "物料编号",
      prop: "materialCode",
      width: TableWidth.order
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料描述",
      prop: "materialDescribe",
      minWidth: TableWidth.largeName
    },
    {
      label: "物料单位",
      prop: "materialUnit",
      width: TableWidth.unit,
      formatter: (row: IMaterial) => row?.unitDictionary?.name
    },
    {
      label: "规格型号",
      prop: "specificationModel",
      width: TableWidth.type
    },
    {
      label: "电压等级",
      prop: "voltageClass",
      width: TableWidth.type,
      formatter: (row: IMaterial) => formatEnum(row.voltageClass, VoltageClassesEnum, "voltageClassesEnum")
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  return { columns };
}
