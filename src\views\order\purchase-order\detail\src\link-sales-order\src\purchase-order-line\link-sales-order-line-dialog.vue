<template>
  <el-dialog
    v-model="visible"
    title="关联销售订单行"
    destroy-on-close
    class="middle"
    align-center
    :close-on-click-modal="false"
  >
    <SalesOrderLineSelectTable :purchaseLineId="purchaseLineId" v-model="salesLines" />
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="checkAndSave" :loading="loading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import SalesOrderLineSelectTable from "../sales-order-line/sales-order-line-select-table.vue";
import { ILinkSalesOrderLines } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { usePurchaseOrderDetailSalesOrderLineStore } from "@/store/modules/purchase-order-detail";

const props = defineProps(["modelValue", "purchaseLineId"]);
const emit = defineEmits(["update:modelValue", "linkSuccess"]);

const salesOrderLineStore = usePurchaseOrderDetailSalesOrderLineStore();
const salesLines = ref<Array<ILinkSalesOrderLines>>([]);
const loading = ref(false);
const handleSave = useLoadingFn(saveAndClose, loading);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});
const purchaseLineId = computed(() => props.purchaseLineId);

function cancel() {
  visible.value = false;
}

async function checkAndSave() {
  if (!salesLines.value.length) {
    ElMessage.warning("请选择销售订单行");
    return;
  }
  await handleSave();
}

async function saveAndClose() {
  await save();
  ElMessage.success("关联成功");
  emit("linkSuccess");
  cancel();
}

async function save() {
  await salesOrderLineStore.linkSalesOrderLine(purchaseLineId.value, salesLines.value);
}
</script>

<style scoped></style>
