<template>
  <div class="production-select">
    <pure-table
      show-overflow-tooltip
      row-key="id"
      :height="460"
      :data="processStore.process"
      :columns="columns"
      :loading="loading"
      @selection-change="selectiProcessChange"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </pure-table>
  </div>
</template>
<script setup lang="ts">
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { ref, watchEffect } from "vue";
import { TableWidth } from "@/enums";
import { useProcessStore, useProcessRouteStore } from "@/store/modules";
import { IProcess } from "@/models";

const processStore = useProcessStore();

const loading = ref(false);

const emits = defineEmits<{
  (e: "onSelectProcessIds", value: Array<string>): void;
}>();

const columns: TableColumnList = [
  {
    width: TableWidth.check,
    type: "selection"
  },
  {
    label: "类型",
    prop: "productionStageName",
    minWidth: TableWidth.name
  },
  {
    label: "工序名称",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: "工序编码",
    prop: "processCode",
    minWidth: TableWidth.name
  },
  {
    label: "2.0工序编码",
    prop: "processCodeV2",
    minWidth: TableWidth.unit
  }
];

const processRouteStore = useProcessRouteStore();
watchEffect(() => {
  if (processRouteStore.currentProcessDetail) {
    queryNotBoundProcess();
  }
});

// 选择数据
const selectiProcessChange = (lines: Array<IProcess>) => {
  emits(
    "onSelectProcessIds",
    lines.map(item => item.processId)
  );
};

async function queryNotBoundProcess() {
  loading.value = true;
  await processStore.queryNotBoundProcess(
    processRouteStore.currentProcessDetail.categoryCode,
    processRouteStore.currentProcessDetail.id
  );
  loading.value = false;
}
</script>

<style scoped lang="scss"></style>
