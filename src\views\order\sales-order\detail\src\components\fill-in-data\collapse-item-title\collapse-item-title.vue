<template>
  <div class="header flex items-center justify-between py-2.5 w-full">
    <div class="flex items-center">
      <div class="left-border" />
      <div class="text-lg mx-4 font-semibold">{{ props.title }}</div>
      <slot />
    </div>
    <div class="flex items-center justify-center mr-5 cursor-pointer hover:text-primary">
      <slot name="actions" />
      <div class="text-base mr-2">{{ isActive ? "展开" : "收起" }}</div>
      <FontIcon :icon="`icon-arrow-${isActive ? 'down' : 'up'}`" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from "vue";
import { collapseContextKey } from "element-plus";
import { FontIcon } from "@/components/ReIcon";

const props = defineProps<{
  title: string;
  name: string;
}>();
const collapse = inject(collapseContextKey);
const isActive = computed(() => collapse?.activeNames.value.includes(props.name));
</script>

<style scoped lang="scss">
.header {
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);

  .left-border {
    width: 4px;
    height: 16px;
    opacity: 1;
    background: var(--el-color-primary);
  }
}
</style>
