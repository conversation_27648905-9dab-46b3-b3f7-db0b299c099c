import { MEASURE_UNIT } from "@/consts";
import CxTag from "@/components/CxTag/index.vue";
import { TableWidth, VoltageClassesEnumMapDesc } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { computed, h, inject, ref, shallowRef } from "vue";
import { salesOrderLineSelectKey } from "@/views/components/sales-order-line-select/tokens";
import { IListData, ISalesOrderLine, ISalesOrderLineExt, ISalesOrderLineProductLinkParams } from "@/models";
import { querySalesOrderLines } from "../tools";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useRowspan } from "@/utils/useRowspan";
import { watchTriggerable } from "@vueuse/core";

export function useSelectTable() {
  const ctx = inject(salesOrderLineSelectKey);
  const { withUnitFormatter, singleSelectFormatter, mapFormatter } = useTableCellFormatter();

  const loading = ref(false);
  const data = shallowRef<Array<ISalesOrderLineExt>>([]);
  const selected = computed(() => ctx.selectedLines?.[0]?.id);
  const columns: TableColumnList = [
    {
      label: "销售订单号",
      prop: "soNo",
      width: TableWidth.order
    },
    {
      label: "选择",
      prop: "id",
      width: 61,
      align: "center",
      showOverflowTooltip: false,
      reserveSelection: true,
      hide: () => ctx.multiple,
      formatter: singleSelectFormatter(selected)
    },
    {
      type: "selection",
      width: 61,
      align: "center",
      reserveSelection: true,
      hide: () => !ctx.multiple
    },
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      width: TableWidth.order
    },
    {
      label: "物资种类",
      prop: "subClassName",
      width: TableWidth.type
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      width: TableWidth.number,
      align: "right",
      formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      width: TableWidth.number,
      formatter: mapFormatter(VoltageClassesEnumMapDesc)
    },
    {
      label: "有无生产订单",
      prop: "isProduction",
      width: TableWidth.type,
      fixed: "right",
      formatter: (row, column, value) => {
        return value
          ? h(CxTag, { disableTransitions: true, type: "success" }, () => "有")
          : h(CxTag, { disableTransitions: true, type: "warning" }, () => "无");
      }
    }
  ];
  const handleQuerySalesOrderLines = useLoadingFn(querySalesOrderLines, loading);
  const params = computed<ISalesOrderLineProductLinkParams>(() => ({
    ...ctx.propParams,
    pageNo: ctx.pagination.currentPage,
    pageSize: ctx.pagination.pageSize,
    keyWords: ctx.keyword,
    productionStatus: ctx.salesOrderLineStatus,
    crossOrder: ctx.crossOrder
  }));

  const { trigger: refreshLines } = watchTriggerable(
    params,
    async (p, oldP, onCleanup) => {
      let canceled = false;
      onCleanup(() => (canceled = true));
      const listData = await handleQuerySalesOrderLines(p).then(res => res.data);
      if (canceled) {
        return;
      }
      _updateData(listData);
    },
    { immediate: true }
  );

  function _updateData(listData: IListData<ISalesOrderLine>) {
    data.value = useRowspan(listData.list, "soNo");
    ctx.pagination.total = listData.total;
  }

  function _clearSelectedLines() {
    ctx.selectedLines.length = 0;
  }

  return {
    data,
    loading,
    columns,
    refreshLines
  };
}
