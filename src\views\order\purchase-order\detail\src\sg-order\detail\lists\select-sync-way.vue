<template>
  <div class="inline-block">
    <slot :open-dialog="openDialog" />
    <el-dialog
      v-model="dialogVisible"
      title="同步确认"
      align-center
      width="600"
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-radio-group class="!block" v-model="mode">
        <el-radio class="!block" label="all" size="large">仅同步“未同步”和“同步失败”的数据</el-radio>
        <el-radio class="!block" label="condition" size="large">同步符合当前列表搜索条件的所有数据</el-radio>
      </el-radio-group>
      <!-- 内容 -->
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

/**
 * 选择同步方式 对话框
 */

const emits = defineEmits<{
  /**
   * @description 确定按钮点击事件
   * @param {boolean} onCondition 是否根据条件
   */
  (event: "onConfirm", onCondition: boolean): void;
}>();

const mode = ref<"all" | "condition">("all");
const loading = ref(false);
const dialogVisible = ref(false);

const handleConfirm = async () => {
  emits("onConfirm", mode.value === "condition");
  // 处理保存后续事件
  closeDialog();
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
  mode.value = "all";
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
