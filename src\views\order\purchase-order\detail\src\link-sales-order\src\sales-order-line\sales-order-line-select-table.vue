<template>
  <div>
    <div class="flex mb-5">
      <el-input
        class="!w-96"
        placeholder="销售订单号/物料名称"
        v-model="params.keyWords"
        clearable
        @clear="searchWithLoading"
        @keydown.enter="searchWithLoading"
      />
      <el-button class="ml-2" type="primary" @click="searchWithLoading">搜索</el-button>
      <div class="flex-c text-sm ml-2 self-end text-secondary" v-if="onlySameSubClassAndBuyer">
        <FontIcon icon="icon-warning-fill" />
        <span class="whitespace-nowrap ml-1">仅支持关联同一个物资种类、采购方公司名称的销售订单行</span>
      </div>
    </div>
    <pure-table
      ref="tableInstance"
      border
      show-overflow-tooltip
      row-key="id"
      height="400"
      :columns="columns"
      :data="salesOrderLineStore.salesOrderLines"
      :loading="loading"
      :span-method="spanMethod"
      :pagination="pagination"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      @page-size-change="pageSizeChange"
      @page-current-change="pageCurrentChange"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watchEffect } from "vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import { usePurchaseOrderDetailStore, useSalesOrderLineStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ILinkSalesOrderLines, ISalesOrderLine, ISalesOrderLineLinkParams } from "@/models";
import { useTableConfig } from "@/utils/useTableConfig";
import { TableWidth } from "@/enums";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { MEASURE_UNIT } from "@/consts";

const props = defineProps(["modelValue", "purchaseLineId", "onlySameSubClassAndBuyer"]);
const emit = defineEmits(["update:modelValue"]);

const selected = computed<Array<ILinkSalesOrderLines>>({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const tableInstance = ref<PureTableInstance>();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const salesOrderLineStore = useSalesOrderLineStore();
const loading = ref(false);
const searchWithLoading = useLoadingFn(search, loading);
const { pagination } = useTableConfig();
const { dictionaryFormatter } = useTableCellFormatter();
const params = reactive<ISalesOrderLineLinkParams>({
  keyWords: "",
  purchaseLineId: props.purchaseLineId,
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize
});
searchWithLoading();

const columns: Array<TableColumns> = [
  {
    label: "销售订单号",
    prop: "soNo",
    width: TableWidth.order
  },
  {
    width: 40,
    type: "selection"
  },
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    minWidth: TableWidth.order
  },
  {
    label: "采购方公司名称",
    prop: "buyerName",
    width: TableWidth.name
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.name
  },
  {
    label: "物料单位",
    prop: "materialUnit",
    width: TableWidth.unit,
    formatter: dictionaryFormatter(MEASURE_UNIT, "subClassCode")
  },
  {
    label: "物料数量",
    prop: "materialNumber",
    width: TableWidth.number
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    width: TableWidth.number
  }
];
const spanMethod = data => {
  if (data.columnIndex === 0) {
    return {
      rowspan: data.row.rowSpan,
      colspan: data.row.colSpan
    };
  }
};

const elTableInstance = computed(() => tableInstance.value.getTableRef());

watchEffect(() => (pagination.total = salesOrderLineStore.total));

function handleRowClick(line: ISalesOrderLine) {
  elTableInstance.value.toggleRowSelection(line, undefined);
}

function handleSelectionChange(lines: Array<ISalesOrderLine>) {
  selected.value = lines.map(line => ({ salesId: line.salesId, salesLineId: line.id }));
}

function pageSizeChange(pageSize: number) {
  params.pageSize = pageSize;
  searchWithLoading();
}

function pageCurrentChange(pageNo: number) {
  params.pageNo = pageNo;
  searchWithLoading();
}

function search() {
  if (props.onlySameSubClassAndBuyer) {
    const { subClassCode, buyerName } = purchaseOrderDetailStore.purchaseOrder;
    params.subClassCode = subClassCode;
    params.buyerName = buyerName;
  } else {
    params.currentPurchase = true;
  }
  return salesOrderLineStore.queryLinesByLink(params);
}
</script>

<style scoped></style>
