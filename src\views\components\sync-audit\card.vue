<template>
  <div class="card">
    <header class="flex-bc py-2.5">
      <span class="card-title text-middle font-medium text-primaryText">{{ props.title }}</span>
      <span
        class="flex justify-center items-baseline cursor-pointer select-none hover:text-primary"
        @click="collapse = !collapse"
      >
        {{ tips }}
        <FontIcon class="icon-arrow-up transition-transform duration-300 ml-1.5" :class="iconClass" />
      </span>
    </header>
    <div class="content" :style="contentStyle">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  title: string;
  modelValue?: boolean;
}>();
const emit = defineEmits(["update:modelValue"]);

const collapse = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit("update:modelValue", value);
  }
});
const tips = computed(() => (collapse.value ? "展开" : "收起"));
const iconClass = computed(() => (collapse.value ? "rotate-180" : ""));
const contentStyle = computed(() =>
  collapse.value ? "grid-template-rows: 0fr" : "grid-template-rows: 1fr; padding-bottom: 10px"
);
</script>

<style scoped>
.card {
  @apply bg-bg_color px-5;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
}

.card .iconfont {
  font-size: 14px;
}

.content {
  @apply overflow-hidden grid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
