import { ColumnWidth } from "@/enums";
// import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 试验参数标准详情列表
 */
export function genLoginReportTableColumnsConfig() {
  // const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "试验项目",
      prop: "collectionName",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "测量参数",
      prop: "collectionItemName",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "最小测量标准值",
      prop: "minValue",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "最大测量标准值",
      prop: "maxValue",
      minWidth: ColumnWidth.Char10
    }
  ];

  return { columnsConfig };
}
