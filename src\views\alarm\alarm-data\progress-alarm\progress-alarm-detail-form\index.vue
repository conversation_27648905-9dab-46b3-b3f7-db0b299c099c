<template>
  <el-scrollbar :max-height="618" v-loading="props.loading">
    <div class="mt-2.5 mb-8">
      <div class="header">
        <TitleBar title="基础信息" />
      </div>
      <el-descriptions class="describe-content" :column="3">
        <el-descriptions-item label="告警时间">
          {{ formatDate(alarmDetail.alarmTime, fullDateFormat) }}
        </el-descriptions-item>
        <el-descriptions-item label="生产订单号">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>

          {{ alarmDetail.ipoNo }}
        </el-descriptions-item>
        <el-descriptions-item label="销售订单号">
          {{ alarmDetail.soNo }}
        </el-descriptions-item>
        <el-descriptions-item label="采购订单号">
          {{ alarmDetail.poNo }}
        </el-descriptions-item>
        <el-descriptions-item label="持续时长">
          {{ convertTime(alarmDetail.duration) }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="describe-content">
        <el-descriptions-item label="告警原因">
          <span v-html="content" />
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="mb-5">
      <div class="header">
        <TitleBar title="解决告警" />
      </div>
      <el-descriptions class="describe-content">
        <el-descriptions-item label="解决状态">
          <AlarmSolveStatusEnumComponent :status="alarmDetail.solveStatus" />
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions class="describe-content !w-full">
        <el-descriptions-item label="备注">
          {{ alarmDetail.remark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { reactive, watchEffect, computed } from "vue";
import { IAlarmDataDetail } from "@/models";
import { useAlarmDataStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import AlarmSolveStatusEnumComponent from "../../component/alarm-solve-status.vue";
import { highLight } from "../../untils/high-light-alarm-message";
import { convertTime } from "../../untils/conver-time";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

const props = defineProps<{
  loading: boolean;
}>();
const alarmDetail = reactive<Partial<IAlarmDataDetail>>({});

const alarmDataStore = useAlarmDataStore();

watchEffect(() => {
  if (alarmDataStore.alarmDataDetail) {
    Object.assign(alarmDetail, alarmDataStore.alarmDataDetail);
  }
});

const content = computed(() => highLight(alarmDetail?.message));
</script>

<style scoped lang="scss">
.describe-content {
  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    align-items: baseline;
  }

  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell .el-descriptions__content) {
    line-height: 1.5;
  }
}

.header {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 10px;
}
</style>
