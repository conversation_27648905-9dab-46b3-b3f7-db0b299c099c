<template>
  <el-button
    v-auth="PermissionKey.qualityTracing.qualityTracingRecordGenRecord"
    :link="!disabledLink"
    type="primary"
    :disabled="loading"
    @click="handleGenerate"
  >
    {{ dynamicLable }}
  </el-button>
</template>
<script setup lang="ts">
import { ElMessage } from "element-plus";
import { PermissionKey } from "@/consts";
import { genQualityTracingRecord } from "@/api/quality-tracing";
import { computed, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";

/**
 * 生成 质量追溯记录
 */

const props = defineProps<{
  /** 生产订单/工单的id */
  id: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 按钮文本 */
  label: string;
  /** 质量规范id */
  qualityId?: string | null;
  /** 是否禁用link样式按钮 */
  disabledLink?: boolean;
}>();

const emits = defineEmits<{
  /** 生成后置操作 */
  (event: "postGenSuccess"): void;
}>();

const loading = ref(false);

/** 动态按钮文本 */
const dynamicLable = computed(() => {
  return loading.value ? "生成中..." : props.label;
});

/**
 * @description: 请求生成质量追溯记录
 */
const requestGenerate = useLoadingFn(async (id: string, subClassCode: string) => {
  return genQualityTracingRecord(id, subClassCode);
}, loading);

/**
 * @description: 生成行为
 */
async function handleGenerate() {
  const messageHandle = ElMessage({ type: "warning", message: "正在生成质量追溯记录, 请稍等..." });
  const res = await requestGenerate(props.id, props.subClassCode).finally(() => {
    messageHandle.close();
  });

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }

  ElMessage({ type: "success", message: "生成成功" });
  emits("postGenSuccess");
}
</script>
