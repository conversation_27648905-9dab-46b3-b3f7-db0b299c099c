<template>
  <div class="bg-bg_color px-6 py-3 flex flex-col h-full">
    <div class="text-right mb-3">
      <el-button link>
        <template #icon>
          <QuestionFilled class="text-primary" />
        </template>
        <span class="text-primaryText hover:underline underline-offset-4" @click="openEIPGuideDialog">
          在EIP检查数据
        </span>
      </el-button>
      <el-button @click="refresh(pageInfo)" :loading="loading">
        <template #icon>
          <FontIcon icon="icon-refresh" />
        </template>
        刷新
      </el-button>
      <el-button
        v-auth="PermissionKey.form.formPurchaseTriggerScoreBtnOneKey"
        v-track="TrackPointKey.FORM_PURCHASE_TRIGGER_BTN_ONE_KEY"
        type="primary"
        @click="showSyncAuditDialog"
      >
        <template #icon>
          <FontIcon icon="icon-sync" />
        </template>
        触发质量评分
      </el-button>
    </div>
    <StateGridTriggerScoreList
      class="flex-1 overflow-hidden"
      :data="store.triggerList"
      :columns="columns"
      :loading="loading"
      v-model="pageInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { QuestionFilled } from "@element-plus/icons-vue";
import { h, inject, onMounted, provide, reactive, ref, watch } from "vue";
import { useStateGriRateDetailStore } from "@/store/modules/state-grid-rate";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey, REFRESH_SYNC_DATA_PERIOD, TrackPointKey } from "@/consts";
import {
  eipGuideKey,
  ITriggerScoreProductOrder,
  triggerScoreProductOrderKey
} from "@/views/components/state-grid-trigger-score/tokens";
import StateGridTriggerScoreList from "@/views/components/state-grid-trigger-score/state-grid-trigger-score-list.vue";
import { useTriggerScoreColumns } from "@/views/components/state-grid-trigger-score/hooks/useTriggerScoreColumns";
import {
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc,
  OrderType,
  RefreshSceneEnum,
  SocketEventEnum,
  StateGridOrderSyncType,
  TableWidth
} from "@/enums";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { TableColumns } from "@pureadmin/table";
import { syncAuditKey } from "@/views/order/purchase-order/detail/src/sg-rate/tokens";
import { useSocket } from "@/utils/useSocket";
import { useThrottleFn } from "@vueuse/core";
import { useTableConfig } from "@/utils/useTableConfig";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const eipGuideCtx = inject(eipGuideKey);
const syncAuditCtx = inject(syncAuditKey);
const store = useStateGriRateDetailStore();
const purchaseOrderStore = usePurchaseOrderDetailStore();
const socket = useSocket();
const { pagination } = useTableConfig();
const pageInfo = ref({
  pageNo: 1,
  pageSize: pagination.pageSize
});

const loading = ref(false);
const refresh = useLoadingFn(page => store.refreshTriggerList(page ? page : pageInfo), loading);
const handleRefresh = useThrottleFn(store.refreshTriggerList, REFRESH_SYNC_DATA_PERIOD, true);

const ctx = reactive<ITriggerScoreProductOrder>({
  refresh
});
provide(triggerScoreProductOrderKey, ctx);
const { triggerScoreColumns } = useTriggerScoreColumns(ctx);

const orderNoColumn: TableColumns = purchaseOrderStore.isCable
  ? {
      label: "生产订单号",
      prop: "ipoNo",
      headerRenderer: () =>
        h(KeywordAliasHeader, {
          code: KeywordAliasEnum.IPO_NO,
          defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
        }),
      minWidth: TableWidth.order
    }
  : {
      label: "生产工单号",
      prop: "woNo",
      minWidth: TableWidth.order
    };

const columns: TableColumnList = [
  {
    label: "销售订单明细",
    prop: "soItemNo",
    minWidth: TableWidth.order
  },
  orderNoColumn,
  ...triggerScoreColumns
];

onMounted(() => {
  watch(
    () => store.rate,
    newVal => {
      if (newVal) {
        refresh(pageInfo.value);
      }
    },
    {
      immediate: true
    }
  );
  addRefreshEventListener();
});

function showSyncAuditDialog() {
  syncAuditCtx.baseInfo = {
    isCable: purchaseOrderStore.isCable,
    isArmourClamp: purchaseOrderStore.isArmourClamp,
    poNo: purchaseOrderStore.purchaseOrder.poNo,
    subClassCode: purchaseOrderStore.purchaseOrder.subClassCode,
    orderType: OrderType.PURCHASE,
    orderId: purchaseOrderStore.purchaseOrderId,
    ...store.rate
  };
  syncAuditCtx.score = store.rate;
  syncAuditCtx.visible = true;
}

function openEIPGuideDialog() {
  eipGuideCtx.visible = true;
}

function addRefreshEventListener() {
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, dataType, purchaseOrderItemId } = event;
    if (purchaseOrderItemId !== store.rate?.purchaseLineId) return;
    if (
      // 同步状态发生变化
      type === RefreshSceneEnum.SYNC_DATA_LIST ||
      // 触发质量评分状态发生变化
      (type === RefreshSceneEnum.SYNC_DATA && dataType === StateGridOrderSyncType.TRIGGER_SCORE)
    ) {
      handleRefresh(pageInfo.value);
    }
  });
}
</script>

<style scoped lang="scss">
:deep(.pure-table) {
  .iconfont.icon-refresh {
    animation: loading-rotate 2s linear infinite;
  }
}
</style>
