import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc, TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { AlarmSolveStatusEnum, AlarmSolveStatusEnumMapDisplayName } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { fullDateFormat } from "@/consts";
import { convertTimeFormatter } from "../untils/conver-time";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "",
      fixed: "left",
      type: "selection",
      reserveSelection: true
    },
    {
      label: "告警时间",
      fixed: "left",
      prop: "alarmTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "生产订单号",
      prop: "ipoNo",
      headerRenderer: () => (
        <KeywordAliasHeader
          code={KeywordAliasEnum.IPO_NO}
          defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
        />
      ),
      minWidth: ColumnWidth.Char14
    },
    {
      label: "销售订单号",
      prop: "soNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "持续时长",
      prop: "duration",
      width: TableWidth.dateTime,
      formatter: convertTimeFormatter
    },
    {
      label: "设备编号",
      prop: "deviceCode",
      minWidth: TableWidth.suborder
    },
    {
      label: "解决状态",
      prop: "solveStatus",
      fixed: "right",
      width: TableWidth.status,
      cellRenderer(data: TableColumnRenderer) {
        const solveStatus: AlarmSolveStatusEnum = data.row.solveStatus;
        const status: string = AlarmSolveStatusEnumMapDisplayName[data.row.solveStatus];
        switch (solveStatus) {
          case AlarmSolveStatusEnum.UNRESOLVED:
            return (
              <CxTag type="warning">
                <span>{status}</span>
              </CxTag>
            );
          case AlarmSolveStatusEnum.RESOLVED:
            return (
              <CxTag type="success">
                <span>{status}</span>
              </CxTag>
            );
          case AlarmSolveStatusEnum.UNSOLVABLE:
            return (
              <CxTag type="danger">
                <span>{status}</span>
              </CxTag>
            );
        }
      }
    },
    {
      label: "告警原因",
      fixed: "right",
      minWidth: TableWidth.largeName,
      slot: "content"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operation
    }
  ];
  return { columns };
}
