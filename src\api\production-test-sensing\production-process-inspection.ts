import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IReportWork, IReportWorkBatchNoReq, IResponse } from "@/models";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";
import {
  IAddProductionProcess,
  IAutoCollectChartReq,
  IAutoCollectChartRes,
  IProductionProcessList,
  ISearchProductionProcessList
} from "@/models/production-test-sensing/i-production-process-inspection";
import { ICopyFromOrder, IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";

/********* 生产工艺及过程检测的接口 *************/
/**
 * 获取线缆生产工艺过程检测的工序
 */
export function getProdProcessInspecProcess(productionId: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/process/status/${productionId}`);
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(url);
}

/**
 * 获取非线缆生产工艺过程检测的工序
 */
export function getNonCableProdProcessInspecProcess(workOrderId: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/process/status/non-cable/${workOrderId}`);
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(url);
}

/**
 * 获取对应过程下的过程检测信息
 */
export function getProductionProcessList(paramsData: ISearchProductionProcessList) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/getProductionAndProcessPage`);
  return http.post<ISearchProductionProcessList, IListResponse<IProductionProcessList>>(url, {
    data: paramsData
  });
}

/**
 * 获取过程检测信息的详情数据
 */
export function getDetialProductionProcessList(id: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/getProductionAndProcessById/${id}`);
  return http.get<void, IResponse<IProductionProcessList>>(url);
}

/**
 * 删除过程检测信息
 */
export function delProductionProcessList(id: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/delete/${id}`);
  return http.delete<string, IResponse<number>>(url);
}

/**
 * 保存新增生产过程工艺及检测
 */
export function saveProductionProcess(params: IAddProductionProcess) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/saveProductionAndProcess`);
  return http.post<IAddProductionProcess, IResponse<IProductionProcessList>>(
    url,
    { data: params },
    { showErrorInDialog: true }
  );
}

/** 从生成订单中复制过程检测 */
export function productionProcessInspectCopy(paramData: ICopyFromOrder) {
  const url: string = withApiGateway(`admin-api/business/productionProcessInfo/copy`);
  return http.post<ICopyFromOrder, IResponse<Boolean>>(url, { data: paramData });
}

/** 获取自动采集项根据工序Id */
export function autoCollectItemsByProcessId(processId: string) {
  const url: string = withApiGateway(
    `admin-api/system/metadataModelInfo/findCategoryByProcessId/autoCollect/${processId}`
  );
  return http.get<void, IResponse<Array<IRawMaterialCheckCollectionItem>>>(url);
}

/** 根据当前工序查询报工批次号列表 */
export function getReportWorkBatchNo(params: IReportWorkBatchNoReq) {
  const url: string = withApiGateway(`admin-api/business/production-report/workreport`);
  return http.post<IReportWorkBatchNoReq, IResponse<Array<IReportWork>>>(url, { data: params });
}

/** 获取过程检 -- 自动采集项图表数据(实时数据查询) */
export function getAutoCollectCharts(params: IAutoCollectChartReq) {
  const url = withApiGateway(`admin-api/business/device/deviceRealTimeDataAcquisition`);
  return http.post<IAutoCollectChartReq, IResponse<IAutoCollectChartRes>>(url, { data: params });
}

/** 获取过程检 -- 自动采集项图表数据(历史数据查询) */
export function getHistoryAutoCollectCharts(params: IAutoCollectChartReq, signal?: AbortSignal) {
  const url = withApiGateway(`admin-api/business/device/deviceHistoryDataAcquisition`);
  return http.post<IAutoCollectChartReq, IResponse<IAutoCollectChartRes>>(url, { data: params }, { signal });
}
