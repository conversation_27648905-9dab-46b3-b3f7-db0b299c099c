<template>
  <div class="flex flex-col">
    <pure-table
      class="flex-1 overflow-hidden pagination"
      show-overflow-tooltip
      :columns="columns"
      :data="batchList"
      row-key="id"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
  </div>

  <el-dialog
    v-model="editVisible"
    :title="dialogTitle"
    class="default"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <ProductOrder
      v-if="isCable"
      ref="productOrderRef"
      :actualStartDateRules="actualStartDateRules"
      :actualFinishDateRules="actualFinishDateRules"
    />
    <WorkOrderForm
      v-else
      ref="workOrderFormRef"
      :actualStartDateRules="actualStartDateRules"
      :actualFinishDateRules="actualFinishDateRules"
    />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="warning" @click="handleUpdateAndTriggerScore" :loading="updateAndTriggerScoreLoading">
        保存，并触发质量评分
      </el-button>
      <el-button type="primary" @click="handleUpdate" :loading="updateLoading">保存</el-button>
    </template>
  </el-dialog>

  <el-dialog
    title="触发评分确认"
    v-model="syncAuditVisible"
    destroy-on-close
    :close-on-press-escape="false"
    fullscreen
    class="sync-audit-full-screen"
  >
    <SyncAudit :base-info="syncAuditBaseInfo" :iot-audit-items="IOT_STATE_GRID_CARD_IDS" />
    <template #footer>
      <el-button @click="syncAuditVisible = false">取消</el-button>
      <el-button type="primary" @click="handleTriggerScoreAfterSyncAudit" :loading="syncLoading">确认触发</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import ProductOrder from "@/views/order/purchase-order/detail/src/components/production-order/add-product-order/product-order/index.vue";
import WorkOrderForm from "@/views/order/purchase-order/detail/src/production-data/non-cable/work-order/forms/work-order-form.vue";
import SyncAudit from "@/views/components/sync-audit/sync-audit.vue";
import { ITableOperator, OperatorCell } from "@/components/TableCells";
import { dateFormat } from "@/consts";
import { StateGridOrderTriggerStatus } from "@/enums/state-grid-order";
import { TableWidth } from "@/enums/table-width.enum";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { computed, h, ref } from "vue";
import { ElMessage, ElNotification, FormItemRule } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  triggerScore,
  updateProductionOrderByTrigger,
  updateWorkOrderByTrigger
} from "@/views/components/state-grid-trigger-score/tools";
import { ICreateProductOrder, IProductOrder } from "@/models/product-order";
import { useProductOrderStore } from "@/store/modules/product-order";
import { IOT_STATE_GRID_CARD_IDS } from "@/views/components/sync-audit/consts";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

defineProps<{
  batchList: Array<Record<string, string>>;
}>();

const { dateFormatter, statusFormatter, enumFormatter } = useTableCellFormatter();
const columns: TableColumnList = [
  {
    label: "采购订单号",
    prop: "poNo",
    width: TableWidth.order
  },
  {
    label: "采购订单行项目号",
    prop: "poItemNo",
    width: TableWidth.order
  },
  {
    label: "生产订单号",
    prop: "poNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),

    minWidth: TableWidth.order
  },
  {
    label: "生产工单号",
    prop: "woNo",
    minWidth: TableWidth.order
  },
  {
    label: "实际开始日期",
    prop: "actualStartDate",
    width: TableWidth.dateTime,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "实际结束日期",
    prop: "actualFinishDate",
    width: TableWidth.dateTime,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "最后更新时间",
    prop: "lastUpdateTime",
    width: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "最后触发时间",
    prop: "lastTriggerTime",
    minWidth: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "订单同步状态",
    prop: "syncResult",
    fixed: "right",
    width: TableWidth.type,
    formatter: statusFormatter("已触发同步", "同步失败", "未同步")
  },
  {
    label: "触发评分状态",
    prop: "status",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderTriggerStatus, "StateGridOrderTriggerStatus")
  },
  {
    label: "可能原因",
    prop: "reason",
    fixed: "right",
    width: TableWidth.reason
  },
  {
    label: "操作",
    fixed: "right",
    width: TableWidth.largeOperation,
    cellRenderer: data => {
      const actions: Array<ITableOperator> = [
        {
          name: "触发质量评分",
          action: () => openSyncAuditDialog(data.row),
          props: { type: "primary" }
        }
      ];
      if (data.row.status !== StateGridOrderTriggerStatus.SUCCESS) {
        actions.unshift({
          name: "修改数据",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        });
      }
      return OperatorCell(actions);
    }
  }
];

const productStore = useProductOrderStore();
const isCable = computed(() => true);
const dialogTitle = computed(() => (isCable.value ? "编辑生产订单" : "编辑生产工单"));
const currentClickDetail = ref();
const editVisible = ref<boolean>(false);
const syncAuditVisible = ref<boolean>(false);
const syncAuditBaseInfo = ref();
const actualStartDateRules: Array<FormItemRule> = [
  { required: true, message: requiredMessage("实际开始时间"), trigger: "change" }
];
const actualFinishDateRules: Array<FormItemRule> = [
  { required: true, message: requiredMessage("实际结束时间"), trigger: "change" }
];
const updateAndTriggerScoreLoading = ref<boolean>(false);
const updateLoading = ref(false);
const handleUpdate = useLoadingFn(update, updateLoading);
const handleUpdateAndTriggerScore = useLoadingFn(updateAndTriggerScore, updateAndTriggerScoreLoading);
const productOrderRef = ref<InstanceType<typeof ProductOrder> | null>();
const workOrderFormRef = ref<InstanceType<typeof WorkOrderForm> | null>();
const syncLoading = ref<boolean>(false);
const handleTriggerScoreAfterSyncAudit = useLoadingFn(triggerScoreAfterSyncAudit, syncLoading);

/** 修改数据 */
function openEditDialog(row) {
  currentClickDetail.value = row;
  patchValue(row.purchaseId);
  editVisible.value = true;
}
/** 触发评分 */
function openSyncAuditDialog(row) {
  currentClickDetail.value = row;
  const { id, poItemNo, purchaseId, poNo, materialCode, materialDesc, subClassCode } = row;
  syncAuditBaseInfo.value = {
    isCable: true,
    isArmourClamp: false,
    purchaseId,
    subClassCode,
    purchaseLineId: id,
    poNo,
    poItemNo,
    materialCode,
    materialDesc
  };
  syncAuditVisible.value = true;
}

async function updateAndTriggerScore() {
  await updateData();
  const { purchaseId, purchaseLineId, productionId, workOrderId, dataId } = currentClickDetail.value;
  await triggerScore(purchaseId, purchaseLineId, dataId, productionId, workOrderId);
  editVisible.value = false;
}

async function update() {
  await updateData();
  editVisible.value = false;
  ElMessage.success("编辑成功");
}

function updateData(): Promise<void> {
  return isCable.value ? updateProductionOrder() : updateWorkOrder();
}

async function updateProductionOrder(): Promise<void> {
  const data: ICreateProductOrder | boolean = await productOrderRef.value.getFormValue();
  if (typeof data === "boolean") {
    return Promise.reject("invalid");
  }
  if (data.salesLineDetails) {
    delete data.salesLineDetails;
  }
  await updateProductionOrderByTrigger(data, currentClickDetail.value.id);
}

async function updateWorkOrder(): Promise<void> {
  const workOrder = await workOrderFormRef.value.getValidValue();
  await updateWorkOrderByTrigger(workOrder, currentClickDetail.value.id);
}

async function triggerScoreAfterSyncAudit() {
  const { purchaseId, id } = currentClickDetail.value;
  await triggerScore(purchaseId, id);
  syncAuditVisible.value = false;
  showNotification();
}

async function patchValue(dataId: string) {
  const productionOrder: IProductOrder = await productStore.getProductOrderDetailById(dataId);
  productStore.setCreateProductOrder(productionOrder);
}

function showNotification() {
  const { woNo, ipoNo } = currentClickDetail.value;
  const tag = isCable.value ? "生产订单" : "生产工单";
  const no = isCable.value ? ipoNo : woNo;
  ElNotification.info({
    title: "触发评分",
    message: `正在触发${tag}【${no}】下生产数据的质量评分`,
    duration: 3000
  });
}
</script>

<style scoped></style>
