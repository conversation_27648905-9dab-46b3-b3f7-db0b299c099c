<template>
  <div class="h-full flex flex-col gap-5 pb-5">
    <div class="px-6 pb-2 bg-bg_color">
      <div class="flex-bc py-2">
        <Header :detail="detailInfo" />
        <el-button
          :loading="loading"
          v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
          size="large"
          type="primary"
          @click="handleSynchronization"
          >一键同步</el-button
        >
      </div>
    </div>
    <div v-loading="loading" class="flex-1 mx-6 flex flex-col overflow-hidden bg-bg_color py-5 px-5 gap-2">
      <div class="flex-bc">
        <ProjectRadio class="flex-1" @change="handleChangeSelect" />
      </div>
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import { useRoute } from "vue-router";
// components
import Header from "./components/header/header.vue";
import ProjectRadio from "./components/project-radio/project-radio.vue";
import UnitBasicInfo from "./components/unit-basic-info/table.vue";
import BasicInfo from "./components/basic-info/table.vue";
import InventoryInfo from "./components/inventory-info/table.vue";
import ProductionProgress from "./components/production-progress/table.vue";
import ArrivalInfo from "./components/arrival-info/table.vue";
import StackManage from "./components/stack-manage/table.vue";
import PkgShip from "./components/pkg-ship/index.vue";
import ExperimentReport from "./components/experiment-report/index.vue";
import EnvReport from "./components/env-report/index.vue";
import SpotCheck from "./components/spot-check/table.vue";
import InspectRaw from "./components/inspect-raw/table.vue";
import InspectProcess from "./components/inspect-process/table.vue";
import FactoryTest from "./components/factory-test/table.vue";
import NoComponents from "./components/no-components/index.vue";
import FactoryStandards from "./components/factory-standards/table.vue";
import { detailEngineeringApi, IntermediateSyncApi } from "@/api/e-capital-construction/engineering-info/index";
import { EProjectModel } from "@/models";
import { ElMessage } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey } from "@/consts";

usePageStoreHook().setTitle("基础信息");

enum RadioType {
  /** 库存管理 */
  Inventory = "1",
  /** 到货进度 */
  Arrival = "2",
  /** 原材料基本信息 */
  BasicInfo = "3",
  /** 原材料组部件检验 */
  InspectRaw = "4",
  /** 生产进度 */
  ProductionProgress = "5",
  /** 生产工艺及过程检测 */
  InspectProcess = "6",
  /** 存栈管理 */
  StackManage = "7",
  /** 出厂试验 */
  FactoryTest = "8",
  /** 出厂试验报告 */
  ExperimentReport = "9",
  /** 包装发运 */
  PkgShip = "10",
  /** 出厂试验参数标准 */
  FactoryStandards = "11"
}

enum RadioTypeCom {
  /** 单元基础信息 */
  UnitBasicInfo = "1",
  /** 库存管理 */
  Inventory = "2",
  /** 原材料基本信息 */
  BasicInfo = "3",
  /** 原材料组部件检验 */
  InspectRaw = "4",
  /** 原材料组部件抽检 */
  SpotCheck = "5",
  /** 生产进度 */
  ProductionProgress = "6",
  /** 环境上报 */
  EnvReport = "7",
  /** 生产工艺及过程检测 */
  InspectProcess = "8",
  /** 出厂试验 */
  FactoryTest = "9",
  /** 出厂试验报告 */
  ExperimentReport = "10",
  /** 包装发运 */
  PkgShip = "11",
  /** 出厂试验参数标准 */
  FactoryStandards = "12"
}

const route = useRoute();
const loading = ref(false);

const selectRadio = ref("");

let detailInfo = reactive({} as EProjectModel);

function swapKeyValue(obj: { [key: string]: string }) {
  return Object.fromEntries(Object.entries(obj).map(([key, value]) => [value, key]));
}

const componentMap = {
  1: swapKeyValue(RadioType),
  2: swapKeyValue(RadioType),
  3: swapKeyValue(RadioTypeCom)
};

// 当前应该渲染的组件
const currentComponent = computed(() => {
  const componentName = componentMap[route.query.type as string]?.[selectRadio.value];
  switch (componentName) {
    case "Inventory":
      return InventoryInfo;
    case "Arrival":
      return ArrivalInfo;
    case "BasicInfo":
      return BasicInfo;
    case "InspectRaw":
      return InspectRaw;
    case "ProductionProgress":
      return ProductionProgress;
    case "InspectProcess":
      return InspectProcess;
    case "StackManage":
      return StackManage;
    case "FactoryTest":
      return FactoryTest;
    case "ExperimentReport":
      return ExperimentReport;
    case "PkgShip":
      return PkgShip;
    case "FactoryStandards":
      return FactoryStandards;
    case "UnitBasicInfo":
      return UnitBasicInfo;
    case "SpotCheck":
      return SpotCheck;
    case "EnvReport":
      return EnvReport;
    default:
      return NoComponents;
  }
});

/**
 * @description: 请求详情数据
 */
const initDetail = useLoadingFn(async () => {
  const { data } = await detailEngineeringApi(route.query.id as string);
  detailInfo = data;
}, loading);

/**
 * @description: 切换
 */
const handleChangeSelect = async (val: string) => {
  selectRadio.value = val;
};

/**
 * @description: 一键同步
 */

const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncApi(route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    initDetail();
  }
}, loading);

onMounted(() => {
  initDetail();
});
</script>

<style lang="scss" scoped>
.el-divider {
  @apply my-1;
}
</style>
