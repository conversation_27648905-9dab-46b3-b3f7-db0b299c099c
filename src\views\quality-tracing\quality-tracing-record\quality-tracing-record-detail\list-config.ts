import {
  ProductionStageCodeToQualitySpecificationCategoryEnum,
  ProductionStageCodeToQualitySpecificationEnum,
  QualitySpecificationDetailEnum,
  QualityStageCategoryConfig
} from "@/enums/quality-specification";

interface StageListConfig {
  key: QualitySpecificationDetailEnum;
  title: string;
  category: ProductionStageCodeToQualitySpecificationCategoryEnum;
  collectionCode: ProductionStageCodeToQualitySpecificationEnum;
  scoreKey: string;
}

/**
 * 质量规范追溯记录 各阶段列表配置
 */
export const stageListConfigArr: Array<StageListConfig> = [
  {
    key: QualityStageCategoryConfig.RawMaterial.name,
    title: "原材料检验",
    category: QualityStageCategoryConfig.RawMaterial.category,
    collectionCode: QualityStageCategoryConfig.RawMaterial.collectionCode,
    scoreKey: "rawMaterialTotalScore"
  },
  {
    key: QualityStageCategoryConfig.ProductiveProcess.name,
    title: "生产过程",
    category: QualityStageCategoryConfig.ProductiveProcess.category,
    collectionCode: QualityStageCategoryConfig.ProductiveProcess.collectionCode,
    scoreKey: "processTotalScore"
  },
  {
    key: QualityStageCategoryConfig.PredeliveryTest.name,
    title: "出厂试验",
    category: QualityStageCategoryConfig.PredeliveryTest.category,
    collectionCode: QualityStageCategoryConfig.PredeliveryTest.collectionCode,
    scoreKey: "experimentTotalScore"
  },
  {
    key: QualityStageCategoryConfig.ProcessStability.name,
    title: "工艺稳定性",
    category: QualityStageCategoryConfig.ProcessStability.category,
    collectionCode: QualityStageCategoryConfig.ProcessStability.collectionCode,
    scoreKey: "processStabilitysTotalScore"
  }
];
