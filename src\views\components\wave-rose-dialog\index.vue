<template>
  <div class="inline-block">
    <el-button @click="openDialog" link type="primary" class="mr-2"> 波形数据 </el-button>
    <el-dialog
      v-model="dialogVisible"
      :title="dataSource.title"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-loading="loading">
        <wave-rose :list="dataSource.list" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import WaveRose from "./wave-rose/index.vue";

defineProps<{
  dataSource: {
    title: string;
    list: Array<{
      yAxisData: Array<number>;
      xAxisData: Array<number>;
      name: string;
    }>;
  };
}>();

const dialogVisible = ref(false);
const loading = ref(false);

function openDialog() {
  dialogVisible.value = true;
}
</script>

<style scoped></style>
