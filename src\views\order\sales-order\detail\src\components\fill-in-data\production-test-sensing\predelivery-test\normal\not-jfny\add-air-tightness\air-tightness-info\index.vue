<template>
  <el-form class="overflow-x-hidden" ref="formInstance" :scroll-to-error="true" :model="form" :rules="rules">
    <div>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="试验编号" prop="experimentNo">
            <SerialNumber
              :code="OUT_FACTORY_EXPERIMENT_NO_CODE"
              :create="!exFactoryQMXStore.outGoingFactoryQmxExperimentDetail?.id"
              v-model="form.experimentNo"
              placeholder="请输入试验编号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成品编号" prop="finProNo">
            <el-input placeholder="请输入成品编号" v-model="form.finProNo" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求数量" prop="account">
            <el-input-number
              v-model="form.account"
              :precision="4"
              :min="0"
              controls-position="right"
              placeholder="请输入需求数量"
              clearable
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求单位" prop="measUnit">
            <el-input placeholder="请输入需求单位" v-model="form.measUnit" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
            <el-form-item label="盘号" prop="reelNo">
              <el-input placeholder="请输入盘号" v-model="form.reelNo" />
            </el-form-item>
          </el-col> -->
        <el-col :span="12" v-if="showFiled">
          <el-form-item label="线芯名称" prop="wireName">
            <el-input placeholder="请输入线芯名称" v-model="form.wireName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="请选择开始日期"
              :disabled-date="startStartTimeDisabledDate"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              :disabled-date="endEndTimeDisabledDate"
              placeholder="请选择结束日期"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试验状态" prop="experimentStatus">
            <EnumSelect
              :enum="QMXExperimentStatusEnum"
              enumName="QMXExperimentStatusEnum"
              class="w-full"
              v-model="form.experimentStatus"
              placeholder="请选择试验状态"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="试验结果" prop="experimentResult">
            <EnumRadioGroup
              :enum="QMXExperimentResultsEnum"
              enumName="QMXExperimentResultsEnum"
              class="w-full"
              v-model="form.experimentResult"
              placeholder="请选择试验结果"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import EnumSelect from "@/components/EnumSelect/index";
import EnumRadioGroup from "@/components/EnumRadioGroup/index";
import SerialNumber from "@/components/SerialNumber";
import { ref, reactive, watchEffect, onMounted, computed } from "vue";
import { FormRules, FormInstance } from "element-plus";
import { requiredMessage, Validators } from "@/utils/form";
import { QMXExperimentStatusEnum, QMXExperimentResultsEnum } from "@/enums";
import { IOutGoingFactoryQMXExperimentForm, IWorkOrder } from "@/models";
import { useEXFactoryQMXStore, useSalesFillInDataStore, useSalesOrderDetailStore } from "@/store/modules";
import { SPECIAL_CHARACTER_REGEXP, OUT_FACTORY_EXPERIMENT_NO_CODE } from "@/consts";
import { useMaterial } from "@/utils/material";

const props = defineProps<{
  subClassCode?: string;
  isCable?: boolean;
}>();

const fillInDataStore = useSalesFillInDataStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const formInstance = ref<FormInstance>();
const exFactoryQMXStore = useEXFactoryQMXStore();
const notArmourClampForm = reactive<IOutGoingFactoryQMXExperimentForm>({
  id: undefined,
  experimentNo: undefined,
  finProNo: undefined,
  account: undefined,
  measUnit: undefined,
  reelNo: undefined,
  wireName: undefined,
  startTime: undefined,
  endTime: undefined,
  experimentStatus: undefined,
  experimentResult: undefined,
  productionId: undefined,
  purchaseId: undefined,
  processId: undefined,
  processCode: undefined,
  processName: undefined
});
const form: any = notArmourClampForm;
const rules: FormRules = {
  experimentNo: [
    { message: requiredMessage("试验编号"), trigger: "change", required: true },
    { message: "试验编号 不允许输入符号", trigger: "change", pattern: SPECIAL_CHARACTER_REGEXP }
  ],
  finProNo: [{ required: true, message: requiredMessage("成品编号"), trigger: "change" }],
  account: [{ required: true, message: requiredMessage("需求数量"), trigger: "change" }],
  measUnit: [{ required: true, message: requiredMessage("需求单位"), trigger: "change" }],
  reelNo: [{ required: true, message: requiredMessage("盘号"), trigger: "change" }],
  wireName: [{ required: true, message: requiredMessage("线芯名称"), trigger: "change" }],
  startTime: [
    { message: requiredMessage("开始时间"), trigger: "change", required: true },
    { message: "开始时间不能大于结束时间", trigger: "change", validator: Validators.maxDate(() => form.endTime) }
  ],
  endTime: [
    { message: requiredMessage("结束时间"), trigger: "change", required: true },
    { message: "结束时间不能小于开始时间", trigger: "change", validator: Validators.minDate(() => form.startTime) }
  ],
  experimentStatus: [{ required: true, message: requiredMessage("试验状态"), trigger: "change" }],
  experimentResult: [{ required: true, trigger: "change", message: requiredMessage("试验结果") }]
};

const materialTool = useMaterial();
const showFiled = computed(() => {
  return materialTool.showFieldOfOutFactoryExperiment(props.isCable, props.subClassCode);
});

watchEffect(async () => {
  if (exFactoryQMXStore.outGoingFactoryQmxExperimentDetail && exFactoryQMXStore.outGoingFactoryQmxExperimentDetail.id) {
    Object.assign(form, exFactoryQMXStore.outGoingFactoryQmxExperimentDetail);
  } else {
    Object.assign(form, {
      id: undefined,
      experimentNo: undefined,
      finProNo: undefined,
      account: undefined,
      measUnit: undefined,
      startTime: undefined,
      endTime: undefined,
      experimentStatus: undefined,
      experimentResult: undefined,
      productionId: undefined,
      processId: undefined,
      processCode: undefined,
      processName: undefined
    });
  }
});

onMounted(() => {
  if (!exFactoryQMXStore.isEditTightness && !salesOrderDetailStore.isCable) {
    // 从工单带入数据
    const { specificationType: model, productName } = fillInDataStore.data as IWorkOrder;
    Object.assign(form, { model, productName });
  }
});

/** 获取表单的值 */
const getQMXExperimentFormValue = async (): Promise<boolean | IOutGoingFactoryQMXExperimentForm> => {
  if (!formInstance.value) {
    return false;
  }

  const valid = await formInstance.value.validate(() => {});

  if (!valid) {
    return valid;
  }

  return form;
};

const resetExperimentFormValue = () => {
  formInstance.value?.resetFields();
};

const startStartTimeDisabledDate = (date: Date) => {
  return form.endTime ? date > form.endTime : false;
};

const endEndTimeDisabledDate = (date: Date) => {
  return form.startTime ? date < form.startTime : false;
};

defineExpose({
  getQMXExperimentFormValue,
  resetExperimentFormValue
});
</script>
<style scoped lang="scss"></style>
