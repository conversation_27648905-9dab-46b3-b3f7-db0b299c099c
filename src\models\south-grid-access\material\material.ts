import { IBase } from "@/models";

export interface IMaterial extends IBase {
  /**
   * 数据项编码类别
   */
  collectionItems?: Array<ICollectionItem>;
  /**
   * 信息类别编码
   */
  infoCode?: string;
  /**
   * 信息类别名称
   */
  infoName?: string;
}

export interface ICollectionItem extends IBase {
  /**
   * 采集类别ID
   */
  collectionId?: string;
  /**
   * 数据项编码
   */
  itemCode?: string;
  /**
   * 说明
   */
  itemDesc?: string;
  /**
   * 数据项名称
   */
  itemName?: string;
  /**
   * 单位
   */
  itemUnit?: string;

  /** 检验值 */
  itemValue?: number;
}
