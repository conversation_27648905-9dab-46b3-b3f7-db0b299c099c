import {
  ICreateFinishedProductStorage,
  IFinishedProductStorage,
  IFinishedProductStorageReq,
  IResponse
} from "@/models";
import { defineStore } from "pinia";
import * as finishedProductStorageService from "@/api/finished-product-storage";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";

export const useFinishedProductStorageStore = defineStore({
  id: "cx-finished-product-storage-store",
  state: () => ({
    finishedProductStorages: [] as Array<IFinishedProductStorage>,
    finishedProductStoragesTotal: 0,
    finishedProductStorageDetail: {} as IFinishedProductStorage
  }),
  actions: {
    /** 查询设备不分类 */
    async queryFinishedProductStorage(pageInfo?: IFinishedProductStorageReq, isOnlySale = false) {
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      const params: IFinishedProductStorageReq = {};
      isCable ? (params.productionId = dataId) : (params.workOrderId = dataId);
      const finishedProductStorageRes = await finishedProductStorageService.queryFinishedProductStorages({
        ...params,
        ...pageInfo
      });

      this.finishedProductStorages = finishedProductStorageRes.data?.list || [];
      this.finishedProductStoragesTotal = finishedProductStorageRes.data?.total || 0;
    },

    async createFinishedProductStorage(data: ICreateFinishedProductStorage): Promise<IResponse<boolean>> {
      return finishedProductStorageService.createFinishedProductStorage(data);
    },

    async editFinishedProductStorage(data: ICreateFinishedProductStorage): Promise<IResponse<boolean>> {
      return finishedProductStorageService.editFinishedProductStorage(data);
    },

    async setCreateFinishedProductStorageDetail(data: ICreateFinishedProductStorage) {
      this.finishedProductStorageDetail = data;
    },

    async setFinishedProductStorageDetail(data: IFinishedProductStorage) {
      this.finishedProductStorageDetail = data;
    },

    async deleteFinishedProductStorage(id: string) {
      return finishedProductStorageService.deleteFinishedProductStorage(id);
    },
    async getFinishedProductStorageDetailById(id: string) {
      return finishedProductStorageService.getFinishedProductStorageDetailById(id);
    }
  }
});
