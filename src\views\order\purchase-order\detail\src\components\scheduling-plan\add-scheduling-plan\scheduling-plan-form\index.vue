<template>
  <ElForm
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    label-position="top"
    require-asterisk-position="right"
    class="cx-form"
  >
    <div :id="DIALOG_ERROR_ID_SALES_ORDER_LINE" />
    <el-row :gutter="40">
      <el-col :span="12">
        <ElFormItem label="排产计划编码" prop="ppNo" required>
          <SerialNumber
            :code="SCHEDULING_PLAN"
            :create="schedulingPlanStore.schedulingPlanFormAddMode"
            v-model="formValue.ppNo"
          />
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="排产数量" prop="amount" required>
          <ElInputNumber
            :min="0"
            class="!w-full"
            v-model="formValue.amount"
            controls-position="right"
            placeholder="请输入排产数量"
          />
        </ElFormItem>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <ElFormItem label="计量单位" prop="measUnit" required>
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="formValue.subClassCode"
            class="w-full"
            placeholder="请选择计量单位"
            v-model="formValue.measUnit"
            disabled
          />
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="排产进度" prop="schedule" required>
          <div class="flex justify-between !w-full">
            <el-slider v-model="schedule" :step="10" show-stops />
            <span class="inline-block w-16 ml-4">{{ schedule }} %</span>
          </div>
        </ElFormItem>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <ElFormItem label="计划日期" prop="planDate" required>
          <ElDatePicker
            class="w-full"
            v-model="formValue.planDate"
            type="daterange"
            range-separator="～"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="defaultTime"
            @change="onChangePlanDate($event)"
          />
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="计划工期(天)" prop="planWorkDuration" required>
          <el-input v-model="formValue.planWorkDuration" disabled placeholder="请输入计划工期" />
        </ElFormItem>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <ElFormItem label="最晚交付日期" prop="deliveryDate" required>
          <ElDatePicker type="date" class="!w-full" v-model="formValue.deliveryDate" placeholder="请选择最晚交付日期" />
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="实际开始日期" prop="actStartDate">
          <el-date-picker
            type="date"
            class="!w-full"
            placeholder="请选择实际开始日期"
            v-model="formValue.actStartDate"
            :disabled-date="actualStartDateDisabledDate"
          />
        </ElFormItem>
      </el-col>
      <el-col :span="12">
        <ElFormItem label="实际结束日期" prop="actEndDate">
          <el-date-picker
            class="!w-full"
            type="date"
            placeholder="请选择实际结束日期"
            v-model="formValue.actEndDate"
            :disabled-date="actualEndDateDisabledDate"
          />
        </ElFormItem>
      </el-col>
    </el-row>
  </ElForm>
</template>

<script setup lang="ts">
import { ICreateSchedulingPlan } from "@/models";
import { FormInstance, FormRules } from "element-plus";
import { reactive, ref, watchEffect, computed } from "vue";
import { SCHEDULING_PLAN, MEASURE_UNIT, DIALOG_ERROR_ID_SALES_ORDER_LINE } from "@/consts";
import dayjs from "dayjs";
import SerialNumber from "@/components/SerialNumber";
import { useSchedulingPlanStore } from "@/store/modules";
import Dictionary from "@/components/Dictionary";

export interface ISchedulingPlanForm {
  getFormValue: () => ICreateSchedulingPlan | boolean;
  resetFormValue: () => {};
  patchFormValue: () => {};
}

const editState = ref<boolean>(true);
const ruleFormRef = ref<FormInstance>();
const schedulingPlanStore = useSchedulingPlanStore();
const defaultTime = ref<[Date, Date]>([new Date(1970, 1, 1, 0, 0, 0), new Date(1970, 1, 1, 23, 59, 59)]);

const schedule = computed<number>({
  get() {
    return formValue.schedule ? Number(formValue.schedule) : 0;
  },
  set(schedule: number) {
    formValue.schedule = schedule || 0;
  }
});

const formValue = reactive<ICreateSchedulingPlan>({
  id: undefined,
  purchaseId: undefined,
  salesLineId: undefined,
  amount: undefined,
  measUnit: undefined,
  planDate: undefined,
  planWorkDuration: undefined,
  deliveryDate: undefined,
  actStartDate: undefined,
  actEndDate: undefined,
  ppNo: undefined,
  schedule: undefined
});

watchEffect(() => {
  const schedulingPlanForm = schedulingPlanStore.schedulingPlanForm;
  if (schedulingPlanForm && Object.keys(schedulingPlanForm).length) {
    Object.assign(formValue, schedulingPlanForm, { ppNo: formValue.ppNo || schedulingPlanForm.ppNo });
    editState.value = !!schedulingPlanForm.id;
  }
});

const rules = reactive<FormRules>({
  ppNo: {
    required: true,
    message: "请输入排产计划编码",
    trigger: "blur"
  },
  amount: {
    required: true,
    message: "请输入排产数量",
    trigger: "change"
  },
  measUnit: {
    required: true,
    message: "计量单位不能为空",
    trigger: "change"
  },
  deliveryDate: {
    required: true,
    message: "请输入交付日期",
    trigger: "change"
  },
  planDate: {
    required: true,
    message: "请输入计划日期",
    trigger: "change"
  },
  planWorkDuration: {
    required: true,
    message: "请输入计划工期",
    trigger: "change"
  },
  schedule: {
    required: true,
    message: "请选择排产进度",
    trigger: "change"
  }
});

const onChangePlanDate = async event => {
  const diffDays: number = dayjs(event[1]).diff(dayjs(event[0]), "days");
  formValue.planWorkDuration = diffDays + 1;
  await ruleFormRef.value.validateField("planWorkDuration", val => {
    return !val;
  });
};

const actualStartDateDisabledDate = (date: Date) => {
  return formValue.actEndDate
    ? date > (typeof formValue.actEndDate === "string" ? new Date(formValue.actEndDate) : formValue.actEndDate)
    : false;
};

const actualEndDateDisabledDate = (date: Date) => {
  return formValue.actStartDate
    ? date < (typeof formValue.actStartDate === "string" ? new Date(formValue.actStartDate) : formValue.actStartDate)
    : false;
};

const getFormValue = async (): Promise<boolean | ICreateSchedulingPlan> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});

  if (!valid) {
    return valid;
  }
  return formValue;
};

const resetFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields();
};

const patchFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields([
    "amount",
    "measUnit",
    "planDate",
    "planWorkDuration",
    "deliveryDate",
    "actStartDate",
    "actEndDate"
  ]);
};

defineExpose({
  getFormValue,
  resetFormValue,
  patchFormValue
});
</script>

<style scoped lang="scss">
:deep(.el-slider .el-slider__stop) {
  border: 1px solid var(--el-slider-runway-bg-color);
}
</style>
