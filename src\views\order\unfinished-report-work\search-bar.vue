<template>
  <el-form
    ref="formRef"
    :model="form"
    :inline="true"
    size="large"
    class="flex"
    label-position="right"
    label-width="6em"
  >
    <el-form-item label="编号搜索:" prop="keyword">
      <el-input class="!w-[300px]" v-model="form.keyword" :placeholder="placeholder" @clear="handleSearch" clearable />
    </el-form-item>
    <el-form-item label="报工开始时间:" prop="startTime" label-width="100px">
      <el-date-picker
        v-model="form.startTime"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        clearable
      />
    </el-form-item>
    <div class="search-btn mb-4">
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { FormInstance } from "element-plus";
import { computedAsync, useVModels } from "@vueuse/core";
import { ref } from "vue";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

interface IFormType {
  keyword: string;
  startTime: [string, string];
}

const props = withDefaults(
  defineProps<{
    modelValue: IFormType;
  }>(),
  {
    modelValue: () => ({ keyword: "", startTime: [undefined, undefined] })
  }
);
const keyWordAliasHook = usekeyWordAliasHook();
const emits = defineEmits<{
  (event: "update:modelValue", value: IFormType): void;
  (event: "search", value: IFormType): void;
  (event: "reset", value: IFormType): void;
}>();

const formRef = ref<FormInstance>();
const { modelValue: form } = useVModels(props, emits);

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `请输入${KeywordAliasEnum.IPO_NO}/工单编号/报工批次号`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const handleSearch = () => {
  emits("search", form.value);
};

const handleReset = () => {
  formRef.value.resetFields();
  emits("reset", form.value);
};
</script>

<style scoped lang="scss">
.status {
  margin-bottom: 0.75rem;
}
</style>
