<template>
  <pure-table
    show-overflow-tooltip
    size="large"
    class="flex-1 overflow-hidden"
    border
    :row-key="getRowKey"
    :data="data"
    :columns="props.columns"
    :pagination="pagination"
    :loading="loading"
    :span-method="spanMethod"
    :row-class-name="getRowClassName"
    :max-height="600"
    @page-current-change="pageChange"
    @page-size-change="pageSizeChange"
  >
    <template #inspectionValue="{ row }">
      <wave-rose-dialog
        v-if="row.dataTypeIdentityDetail.identityCode === EControlType.WaveRoseControl"
        :data-source="genWaveList(row.inspectionValue, row.waveFormConfig)"
      />
      <div v-else>{{ row.inspectionValue }}</div>
    </template>
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import { PureTable } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { inject, ref, watch } from "vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IIotSyncCommon, IotSyncListType, IPagingReq } from "@/models";
import { flatten } from "lodash-unified";
import { useTableConfig } from "@/utils/useTableConfig";
import { useSpan } from "./hooks/useSpan";
import { useAuditItemStatus } from "../hooks/audit-item-status";
import { syncAuditCellSpanKey } from "../tokens";
import { buildUUID } from "@pureadmin/utils";
import WaveRoseDialog from "@/views/components/wave-rose-dialog/index.vue";
import { genWaveList } from "@/views/components/wave-rose-dialog/data-conversion";
import { EControlType } from "@/enums";

interface IIotSync extends IIotSyncCommon {
  inspectionName?: string;
  inspectionValue?: string;
  inspectionRule?: string;
  inspectionValidated?: boolean;
}

const props = defineProps<{
  type: IotSyncListType;
  columns: TableColumnList;
  cardId?: string;
}>();

const emits = defineEmits<{
  (e: "dataChange", data: Array<IIotSync>): void;
}>();

const store = useStateGridSyncAuditStore();
const spanCtx = inject(syncAuditCellSpanKey);
const { setSpan } = useSpan();

const loading = ref(false);
const data = ref<Array<IIotSync>>();
const handleRefresh = useLoadingFn(store.getMakeSureSyncListByType, loading);
const { pagination } = useTableConfig();
const { updateItemStatus } = useAuditItemStatus();
// 分页信息
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};

pagination.hideOnSinglePage = true;
const duplicateKey: string = buildUUID();

// 订阅 生产订单号切换
watch(
  () => store.activeNo,
  async (activeNo: string) => {
    pageInfo.pageNo = 1;
    pagination.currentPage = 1;
    store.setOrderNo(activeNo);
    await refresh(pageInfo);
  },
  {
    immediate: true
  }
);

function getRowKey(row: IIotSync): string {
  return `${row.id}-${row.inspectionName}`;
}

function spanMethod({ row, column }) {
  const property = column.property;
  if (!property) {
    return;
  }
  const spanTypes = spanCtx.spanTypes;
  if (!spanTypes) {
    return;
  }
  const spanType = getSpanField(spanTypes[property]);
  return row[spanType];
}

function getRowClassName({ row }) {
  const valid = row.inspectionValidated ?? true;
  return valid ? null : "invalid";
}

async function refresh(pageInfo?: IPagingReq) {
  const originalData = await handleRefresh(props.type, pageInfo, true, duplicateKey).catch(() => ({
    list: [],
    total: 0
  }));
  data.value = splitByRawInspectionItems(originalData.list || []);
  pagination.total = originalData.total;
  emits("dataChange", data.value);
  const hasError: boolean = data.value.some(datum => !datum.inspectionValidated);
  spanCtx.uniqKeys?.forEach(key => setSpan(data.value, key, getSpanField(key)));
  updateItemStatus(props.cardId, hasError);
}

function splitByRawInspectionItems(syncList: Array<IIotSync>): Array<IIotSync> {
  return flatten(
    syncList.map(sync => {
      const rawMetadataValue = sync.rawMetadataValue;
      if (!Array.isArray(rawMetadataValue) || !rawMetadataValue.length) {
        return { ...sync, inspectionValidated: true } as IIotSync;
      }
      return rawMetadataValue
        .map(value => {
          const {
            targetCode,
            waveFormConfig,
            targetName,
            targetNameAlias,
            targetValue,
            targetValueLabel,
            unit,
            datumOrganization,
            validated,
            collectionType,
            dataTypeIdentityDetail
          } = value;
          const inspectValue = targetValueLabel || targetValue;
          return {
            ...sync,
            inspectionName: targetNameAlias || targetName,
            inspectionValue: inspectValue ? `${inspectValue} ${unit}` : "",
            inspectionRule: datumOrganization,
            inspectionValidated: validated,
            collectionType,
            targetCode,
            waveFormConfig,
            dataTypeIdentityDetail
          };
        })
        .filter(list => !list.collectionType);
    })
  );
}

function getSpanField(key: string) {
  return `_${key}Span`;
}

/**
 * 切换页码
 */
function pageChange(pageNo: number) {
  refresh({ pageSize: pagination.pageSize, pageNo });
}
/**
 * 切换页码数量
 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  refresh({ pageNo: 1, pageSize });
}

defineExpose({
  refresh,
  getData: () => data
});
</script>

<style scoped lang="scss">
:deep(.invalid) {
  .error-mark {
    color: var(--el-color-warning);
  }
}
</style>
