import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models/response";
import {
  IEmphasisRawmaterialList,
  IEmphasisRawmaterialReq,
  IKeyRawMaterialOptions,
  ISearchEmphasisRawMaterial
} from "@/models/stock-data-center/i-emphasis-raw-material";

/**
 * 获取重点原材料的原材料类型的数据
 */
export function getEmphasisRawMaterilKinds(params?: { categoryCode?: string; subClassCode?: string }) {
  const url = withApiGateway(`admin-api/system/categoryDictionary/select`);
  return http.post<{ categoryCode?: string; subClassCode?: string }, IResponse<Array<IKeyRawMaterialOptions>>>(url, {
    data: params
  });
}

/**
 * 获取重点原材料列表数据
 * @param queryParams
 */
export function getEmphasisRawMaterialList(queryParams: ISearchEmphasisRawMaterial) {
  const url = withApiGateway(`admin-api/business/keyRawMaterial/page`);
  return http.post<ISearchEmphasisRawMaterial, IListResponse<IEmphasisRawmaterialList>>(url, {
    data: queryParams
  });
}

/**
 * 保存新增的重点原材料
 */
export function saveAddEmphasisRawMaterial(paramsData: IEmphasisRawmaterialReq) {
  const url = withApiGateway(`admin-api/business/keyRawMaterial/save`);
  return http.post<IEmphasisRawmaterialReq, IResponse<string>>(
    url,
    {
      data: paramsData
    },
    { showErrorInDialog: true }
  );
}

/**
 * 根据 ID 获取某一个重点原材料的详情
 */
export function getDetailAddEmphasisRawMaterial(id: string) {
  const url = withApiGateway(`admin-api/business/keyRawMaterial/detail/${id}`);
  return http.get<void, IResponse<IEmphasisRawmaterialList>>(url);
}

/**
 * 删除重点原材料接口
 */
export function delEmphasisRawMaterilaById(id: string) {
  const url = withApiGateway(`admin-api/business/keyRawMaterial/delete/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}
