import { defineStore } from "pinia";
import { ISearchSparePartReq } from "@/models/stock-data-center/i-reserve-part-stock";
import { ISyncSparePartProduct } from "@/models/stock-data-center/stock-sync/i-spare-part-info";
import { getSparePartProductList } from "@/api/stock-data-center/reserve-part-stock";
import { setSyncingStatus } from "@/views/stock-data-center/stock-sync/utils/sync-stock-utils";

export const useSyncSparePartInfoStore = defineStore({
  id: "sync-spare-part-info-store",
  state: () => ({
    tableTotal: 0,
    syncSparePartData: [] as Array<ISyncSparePartProduct>,
    queryParams: {
      pageNo: 1,
      pageSize: 20
    } as ISearchSparePartReq
  }),
  actions: {
    /** 查询同步的备品备件 */
    querySyncSparePartList(queryParams: ISearchSparePartReq) {
      this.queryParams = { ...this.queryParams, ...queryParams };
      this.getSyncSparePartListAction();
    },
    /** 获取同步的备品备件列表 */
    async getSyncSparePartListAction() {
      const resData = await getSparePartProductList(this.queryParams);
      if (Array.isArray(resData.data?.list) && resData.data?.list?.length) {
        this.syncSparePartData = setSyncingStatus(resData.data?.list);
        this.tableTotal = resData.data?.total;
      } else {
        this.syncSparePartData = [];
        this.tableTotal = 0;
      }
    },

    /** 重置查询参数 */
    initQueryParams() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
    }
  }
});
