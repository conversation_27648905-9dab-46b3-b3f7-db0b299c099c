/**
 * @description: 适用物料tab页接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse } from "@/models";
import {
  ApplicationMaterialListItem,
  ApplicationMaterialListParams,
  AddApplicationMaterialParams,
  RemoveApplicationMaterialParams,
  ChoosableApplicationMaterialListParams
} from "@/models/quality-tracing";

/**
 * @description: 查询物料列表
 */
export const queryApplicationMaterialList = (params: ApplicationMaterialListParams) => {
  return http.post<ApplicationMaterialListParams, IListResponse<ApplicationMaterialListItem>>(
    withApiGateway(`admin-api/business/qualitySpecificationMaterialLink/pageChooseMaterialList`),
    {
      data: params
    }
  );
};

/**
 * @description: 查询可选适用物料列表
 */
export const queryChoosableApplicationMaterialList = (params: ChoosableApplicationMaterialListParams) => {
  return http.post<ChoosableApplicationMaterialListParams, IListResponse<ApplicationMaterialListItem>>(
    withApiGateway(`admin-api/business/qualitySpecificationMaterialLink/pageNotChooseMaterialList`),
    {
      data: params
    }
  );
};

/**
 * @description: 添加适用物料
 */
export const addApplicationMaterial = (params: AddApplicationMaterialParams) => {
  return http.post<AddApplicationMaterialParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/qualitySpecificationMaterialLink/chooseMaterials`),
    {
      data: params
    }
  );
};

/**
 * @description: 移除适用物料
 */
export const removeApplicationMaterial = (params: RemoveApplicationMaterialParams) => {
  return http.post<RemoveApplicationMaterialParams, IResponse<boolean>>(
    withApiGateway(`admin-api/business/qualitySpecificationMaterialLink/removeChooseMaterials`),
    {
      data: params
    }
  );
};
