<template>
  <div class="px-5 mt-2">
    <el-form ref="formRef" :model="formValue" :rules="rules" label-width="60px">
      <el-row>
        <el-col>
          <el-form-item label="分组" prop="groupName">
            <el-input placeholder="请输入分组名称" v-model="formValue.groupName" clearable />
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="排序" prop="status">
            <el-input-number
              v-model="formValue.orderNum"
              :min="0"
              controls-position="right"
              class="!w-full"
              placeholder="请输入排序"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IDeviceGroupForm } from "@/models";

defineExpose({
  getFormValue
});

const props = withDefaults(
  defineProps<{
    deviceGroup?: IDeviceGroupForm;
  }>(),
  {}
);

const formValue = reactive<IDeviceGroupForm>({
  id: undefined,
  groupName: undefined,
  orderNum: undefined
});
const rules: FormRules = {
  groupName: [{ required: true, message: requiredMessage("分组名称"), trigger: "change" }]
};

watchEffect(() => {
  if (props.deviceGroup) {
    Object.assign(formValue, props.deviceGroup);
  }
});

const formRef = ref<FormInstance>();

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getFormValue(): Promise<IDeviceGroupForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return formValue;
}
</script>

<style scoped lang="scss"></style>
