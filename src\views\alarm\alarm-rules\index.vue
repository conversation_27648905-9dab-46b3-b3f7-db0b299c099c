<template>
  <div class="overflow-auto w-[55%] m-auto mt-5 mb-5 flex flex-col" :loading="loading">
    <el-scrollbar class="flex-1">
      <div class="bg-bg_color mb-5 p-5">
        <QualityAlarmRules :quality-rules="alarmRulesSetting.qulityAlarm" @setValue="setQualityAlarmRulesValue" />
      </div>
      <div class="bg-bg_color mb-5 p-5">
        <ProcessAlarmRules :progress-rules="alarmRulesSetting.progressAlarm" @setValue="setProgressAlarmRulesValue" />
      </div>
      <div class="bg-bg_color p-5">
        <OperationsAlarmRules
          :operations-rules="alarmRulesSetting.devOpsAlarm"
          @setValue="setOperationsAlarmRulesValue"
        />
      </div>
    </el-scrollbar>

    <div class="mt-5 text-right" v-show="!isSaveAlarmRules">
      <ElButton v-auth="PermissionKey.alarm.alarmRuleEdit" size="large" @click="reset()">重置</ElButton>
      <ElButton
        v-auth="PermissionKey.alarm.alarmRuleEdit"
        type="primary"
        size="large"
        @click="save()"
        :disabled="isSaveAlarmRules"
        >保存</ElButton
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import { IAlarmRuleSetting, IQualityRule, IProgressRule, IOperationsRule } from "@/models";
import QualityAlarmRules from "./quality-alarm-rules/index.vue";
import ProcessAlarmRules from "./progress-alarm-rules/index.vue";
import OperationsAlarmRules from "./operations-alarm-rules/index.vue";
import { useAlarmRulesHook } from "./hooks/alarm-rules-hook";
import { useAlarmRulesStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { isEqual } from "lodash-unified";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { PermissionKey } from "@/consts";

// 设置标题
usePageStoreHook().setTitle("告警规则");

const alarmRulesSetting = reactive<IAlarmRuleSetting>({
  qulityAlarm: {} as IQualityRule,
  progressAlarm: {} as IProgressRule,
  devOpsAlarm: {} as IOperationsRule
});

const { queryAlarmRules, saveAlarmRules } = useAlarmRulesHook();
const alarmRulesStore = useAlarmRulesStore();
const isSaveAlarmRules = ref<boolean>(false);
const loading = ref<boolean>(false);
const onSave = useLoadingFn(saveAlarmRules, loading);
// 获取告警规则
onMounted(async () => {
  await queryAlarmRules();
  Object.assign(alarmRulesSetting, alarmRulesStore.alarmRulesSetting);
});

async function reset() {
  Object.assign(alarmRulesSetting, alarmRulesStore.alarmRulesSetting);
  ElMessage.success("重置成功");
}

async function save() {
  await onSave(alarmRulesSetting);
  await queryAlarmRules();
  Object.assign(alarmRulesSetting, alarmRulesStore.alarmRulesSetting);
  ElMessage.success("保存成功");
}
watch(
  () => alarmRulesSetting,
  () => {
    isSaveAlarmRules.value = isEqual(alarmRulesSetting, alarmRulesStore.alarmRulesSetting);
  },
  { deep: true }
);

// 设置告警规则
function setQualityAlarmRulesValue(qulityAlarm: IQualityRule) {
  alarmRulesSetting.qulityAlarm = qulityAlarm;
}

function setProgressAlarmRulesValue(progressAlarm: IProgressRule) {
  alarmRulesSetting.progressAlarm = progressAlarm;
}

function setOperationsAlarmRulesValue(devOpsAlarm: IOperationsRule) {
  alarmRulesSetting.devOpsAlarm = devOpsAlarm;
}
</script>

<style scoped lang="scss">
.describe-content {
  :deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
    line-height: 30px;
    margin-top: 15px;
  }
}

.header {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding-bottom: 8px;
}

.el-radio-group {
  display: grid;
}

.ui-input-group-addon {
  color: var(--disabled-text-color);
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  box-shadow: 0 0 0 0.0625rem var(--el-input-border-color, var(--el-border-color)) inset;
}

.el-checkbox {
  margin-bottom: 10px;
}
</style>
