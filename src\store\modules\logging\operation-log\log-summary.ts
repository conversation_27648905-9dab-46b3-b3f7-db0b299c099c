import { getLogSummaryList } from "@/api/logging/operate-log";
import { IOperateLog, IOperateLogReq } from "@/models/logging";
import { defineStore } from "pinia";
import { omitBy } from "lodash-unified";
import { isNullOrUnDef } from "@pureadmin/utils";

export const useLogSummaryStore = defineStore({
  id: "log-summary-store",
  state: () => ({
    queryParams: {} as IOperateLogReq,
    tableTotal: 0,
    logSummaryQuery: {},
    logSummaryTableData: [] as Array<IOperateLog>
  }),
  actions: {
    /** 查询日志汇总列表 */
    async queryLogSummaryList(params: IOperateLogReq) {
      this.queryParams = { ...this.queryParams, ...params };
      await this.getLogSummaryTableData();
    },

    /**
     * 获取日志汇总列表
     */
    async getLogSummaryTableData() {
      const queryParams = omitBy(this.queryParams, value => isNullOrUnDef(value));
      const res = await getLogSummaryList(queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.logSummaryTableData = res.data.list;
        this.tableTotal = res.data.total;
      } else {
        this.logSummaryTableData = [];
        this.tableTotal = 0;
      }
    }
  }
});
