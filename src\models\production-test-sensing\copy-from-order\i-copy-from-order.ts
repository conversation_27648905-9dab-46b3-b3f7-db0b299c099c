import { IPagingReq } from "@/models/i-paging-req";
import { IAddProductionProcess, IOutGoingFactoryQmxExperiment } from "@/models";
import { IRawMaterialGroupUnitInspect } from "../i-raw-material-group-unit-check";

/** 查询生成订（工）单 */
export interface IOrderListReq extends IPagingReq {
  /** 生成订单id */
  productionId?: string;
  /** 生成工单id */
  workOrderId?: string;
  /** 关键字 */
  keywords?: string;
  orderType?: string;
  /** 填报步骤区分 */
  dataType?: string;
}

/** 生成订单列表 */
export interface IProductionPageRes {
  /** id */
  id: string;
  /** 销售订单号 */
  soNo: string;
  /** 销售订单行项目号 */
  soItemNo: string;
  /** 生产订单号 */
  ipoNo: string;
  /** 厂家物料名称 */
  materialsName: string;
  /** 生产数量 */
  amount: number;
  /** 计量单位 */
  unit: number;
  /** 应用类型 */
  applicationType: number;
  /** 接头类型 */
  jointCategories: number;
  /** 电压类型 */
  voltageType: string;
  /** 电压等级 */
  voltageClasses: string;
  /** 种类编码 */
  subclassCode: string;
  /** 规格型号 */
  specificationModel: string;
}

/** 根据历史订（工）单选择 */
export interface IOrdersForChooseReq extends IPagingReq {
  orderIds: string[];
  orderType?: number;
  processId?: string;
  currentOrderId?: string;
}

export type IRawMaterialFromOrderRes = IRawMaterialGroupUnitInspect;

export type IProcessInspectFromOrderRes = IAddProductionProcess;

export type IOutFactoryFromOrderRes = IOutGoingFactoryQmxExperiment;
