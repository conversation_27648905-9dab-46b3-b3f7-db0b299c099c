import { StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumns } from "@pureadmin/table/dist";

export function useNormalColumns(dateSupportOrder = false) {
  const { enumFormatter, dateFormatter } = useTableCellFormatter();
  const sortable = (dateSupportOrder && "custom") as false | "custom";

  const normalColumns: Array<TableColumns> = [
    {
      sortable,
      label: "最后更新时间",
      prop: "lastUpdateTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      sortable,
      label: "最后同步时间",
      prop: "lastSyncTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "同步次数",
      prop: "retryTimes",
      minWidth: TableWidth.type
    },
    {
      label: "同步状态",
      prop: "syncResult",
      width: TableWidth.type,
      fixed: "right",
      formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
    },
    {
      label: "可能原因",
      prop: "reason",
      fixed: "right",
      minWidth: TableWidth.largeName
    }
  ];

  return {
    normalColumns
  };
}
