import * as api from "@/api/state-grid-rate";
import * as syncApi from "@/api/state-grid-order-sync";
import { ICreateProductOrder, ICreateWorkOrder, IStateGridOrderRateParams } from "@/models";
import { StateGridOrderSyncType } from "@/enums";

export const allowTriggerStateGridScore = (purchaseLineId: string, productionId?: string, workId?: string) =>
  api.allowTriggerStateGridScore(purchaseLineId, productionId, workId);

export const triggerScore = (
  purchaseId: string,
  purchaseLineId: string,
  dataId?: string,
  productionId?: string,
  workOrderId?: string
) => {
  const params: IStateGridOrderRateParams = {
    purchaseId,
    purchaseLineId,
    dataType: StateGridOrderSyncType.TRIGGER_SCORE,
    dataId,
    productionId,
    workId: workOrderId
  };
  return syncApi.syncStateGridOrderAndValidate(params);
};

export const updateProductionOrderByTrigger = (data: ICreateProductOrder, triggerId: string) =>
  api.updateProductionOrder({ ...data, triggerId });

export const updateWorkOrderByTrigger = (data: ICreateWorkOrder, triggerId: string) =>
  api.updateWorkOrder({ ...data, triggerId });
