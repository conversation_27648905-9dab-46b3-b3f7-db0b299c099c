import { SyncStatusEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";

export interface IEquipmentInfo extends IBase {
  /**
   * 设备编码
   */
  entityId?: string;
  /**
   * 规格型号
   */
  model?: string;
  /**
   * 设备描述
   */
  entityDesc?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 数量(米数)
   */
  amount?: number;
  /**
   * 入库日期
   */
  storageDate?: string;
  /**
   * 发货日期
   */
  deliveryDate?: string;
  /**
   * 发货单号
   */
  deliveryOrderNo?: string;
  /**
   * 出厂实验报告
   */
  testReportId?: string;

  testReportFileName?: string;

  /** 出厂试验报告sdcc返回文件ID  */
  testReportFileId?: string;
  /**
   * 同步状态
   */
  isSync?: SyncStatusEnum;
}
