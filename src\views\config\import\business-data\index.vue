<template>
  <ImportDataType />
  <ElDialog
    title="数据查看"
    align-center
    class="middle"
    destroy-on-close
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onCloseViewData()"
  >
    <ImportDataView />
  </ElDialog>
</template>

<script setup lang="ts">
import ImportDataView from "./import-data-view/index.vue";
import ImportDataType from "./import-data-type/index.vue";
import { ref } from "vue";

const dialogVisible = ref(false);

const onCloseViewData = () => {
  dialogVisible.value = false;
};
</script>

<style scoped></style>
