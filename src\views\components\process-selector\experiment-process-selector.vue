<template>
  <el-select
    v-if="loaded"
    class="w-full"
    v-model="modelValue"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
    @clear="handleClear"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <!-- 数据未加载完成时显示空白选择器 -->
  <el-select
    v-else
    class="w-full"
    filterable
    clearable
    :placeholder="placeholder"
    :loading="loading"
    :loading-text="SELECTOR_LOADING_TEXT"
  />
</template>

<script setup lang="ts">
/**
 * 出厂试验工序选择器
 * 注意区别之前的工序选择器，之前的工序选择器是过程检的工序选择器
 */
import { ref, watch, withDefaults, computed } from "vue";
import { useVModels } from "@vueuse/core";
import { getPredeliveryExperiment } from "@/api/process";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";

interface IOption {
  label: string;
  value: string;
  code: string;
}

const props = withDefaults(
  defineProps<{
    /** 物资种类 */
    subClassCode: string;
    /** 已选中id列表 */
    modelValue: string;
    /** 已选中的processCode */
    processCode?: string;
    /** 根据processCode排除的工序 */
    excludeProcessCode?: Array<string>;
  }>(),
  {
    processCode: "",
    excludeProcessCode: () => [] as Array<string>
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", id: string): void;
  (event: "update:processCode", code: string): void;
}>();

const { modelValue, processCode } = useVModels(props, emits);

const options = ref<Array<IOption>>([]);
const loading = ref(false);
const loaded = ref(false);

const placeholder = computed(() => {
  return "请选择试验类型";
});

/**
 * @description: 根据物资种类查询工序
 */
const requestDefaultProcessBySubclassCode = useLoadingFn(async () => {
  const { data } = await getPredeliveryExperiment(props.subClassCode);
  if (!data) {
    return;
  }
  const tempList = data.filter(({ processCode }) => !props.excludeProcessCode.includes(processCode));
  options.value = tempList.map(({ id, processName, processCode }) => ({
    label: processName,
    value: id,
    code: processCode
  }));
  loaded.value = true;
}, loading);

function handleClear() {
  processCode.value = "";
}

function getSelectedProcessName() {
  const selectedItem = options.value.find(({ value }) => value === modelValue.value);
  if (selectedItem) {
    return selectedItem.label;
  }
  return "";
}

watch(
  () => props.subClassCode,
  code => {
    if (code) {
      requestDefaultProcessBySubclassCode();
    }
  },
  {
    immediate: true
  }
);

watch(modelValue, id => {
  const activeItem = options.value.find(({ value }) => value === id);
  if (activeItem) {
    processCode.value = activeItem.code;
  }
});

defineExpose({
  getSelectedProcessName
});
</script>

<style scoped></style>
