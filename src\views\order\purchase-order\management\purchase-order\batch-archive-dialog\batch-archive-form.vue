<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="right">
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="" prop="remark">
          <el-input
            v-model="form.remark"
            placeholder="请输入"
            show-word-limit
            maxlength="200"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 6 }"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";

interface FormType {
  remark: string;
}

const form = reactive({
  remark: ""
});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  remark: [{ required: true, message: "归档备注不能为空", trigger: "change", max: 200 }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  form.remark = v.remark;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped></style>
