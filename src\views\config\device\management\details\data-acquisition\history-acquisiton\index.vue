<!-- 生产设备—数据采集-历史数据组件 -->
<template>
  <div class="h-full">
    <el-scrollbar v-loading="loading">
      <section v-if="chartList.length">
        <!-- 历史数据 -->
        <div class="flex flex-row flex-wrap mr-3">
          <el-card
            v-for="(item, index) in chartList"
            :key="item.collectPoint"
            class="lg:basis-[31%] sm:basis-[40%] basis-[45%] shadow-md mb-5 mr-5 min-w-96"
            @click="toggleDialog(index)"
          >
            <div class="h-[280px] flex flex-col">
              <div class="pl-2">{{ item.collectName }}</div>
              <div class="flex-1">
                <chart :x-axis-data="item.xAxisData" :y-axis-data="item.yAxisData" />
              </div>
            </div>
          </el-card>
        </div>
      </section>
      <CxEmpty v-else />
    </el-scrollbar>

    <el-dialog
      :title="focusData ? focusData.collectName : '--'"
      align-center
      class="middle"
      width="80%"
      destroy-on-close
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="h-[70vh] flex">
        <div class="w-[25%]">
          <el-table-v2
            :data="devicePointArr"
            :columns="columns"
            v-model:sort-state="sortState"
            :width="340"
            :height="650"
            @column-sort="onSort"
          />
        </div>

        <div class="w-[75%]">
          <chart :x-axis-data="focusData.xAxisData" :y-axis-data="focusData.yAxisData" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, onUnmounted, onMounted } from "vue";
import CxEmpty from "@/components/CxEmpty";
import { useDataAcauisitionStore } from "@/store/modules/device";
import { IDeviceDataAcquisitionReq, IDevicePoint, IDevicePointData } from "@/models/device";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCancelHttp } from "@/utils/http/cancel-http";
import Chart from "./chart.vue";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import type { SortBy, SortState } from "element-plus";
import { queryDeviceAcquisitionAll } from "@/api/device/device-acquistion";
import { TableV2SortOrder } from "element-plus";
import { cloneDeep } from "@pureadmin/utils";

export interface IExperimentData {
  /** 采集项编码 */
  collectPoint: string;
  /** 采集项名称 */
  collectName: string;
  /** x轴数据 */
  xAxisData: Array<string>;
  /** y轴数据 */
  yAxisData: Array<string>;
}

const columns: { [key: string]: any } = [
  {
    title: "时间",
    key: "time",
    dataKey: "time",
    sortable: true,
    width: 180
  },
  {
    title: "码点值",
    key: "point",
    align: "center",
    dataKey: "point",
    width: 140,
    sortable: true
  }
];

const onSort = ({ key, order }: SortBy) => {
  sortState.value[key] = order;
  devicePointArr.value = devicePointArr.value.reverse();
};

const props = defineProps<{
  deviceId: string;
}>();

const dialogVisible = ref(false);
const devicePointArr = ref<Array<IDevicePoint>>([]);
const loading = ref(false);
const dataAcauisitionStore = useDataAcauisitionStore();
// echarts列表数据
const chartList = ref<Array<IExperimentData>>([]);
const cancelHttp = useCancelHttp();
const focusData = ref<IExperimentData | null>(null);
// 试验数据列表
const experimentList = ref<Array<IExperimentData>>([]);

/**
 * @description: 切换弹窗显示
 */
const toggleDialog = (index: number) => {
  dialogVisible.value = !dialogVisible.value;
  focusData.value = chartList.value[index];
  let { xAxisData, yAxisData } = cloneDeep(focusData.value);
  xAxisData = xAxisData.reverse();
  yAxisData = yAxisData.reverse();

  devicePointArr.value = xAxisData.map((item, index) => {
    return {
      time: item,
      point: yAxisData[index]
    };
  });
};

/**
 * @description: 生成实验数据列表
 */
const generateExperimentList = useLoadingFn(async () => {
  // 采集点列表
  const { data: pointList = [] } = await queryDeviceAcquisitionAll(props.deviceId);
  // 试验数据列表
  if (!pointList.length) {
    return;
  }
  // 构建试验数据列表数据结构
  pointList.forEach(point =>
    experimentList.value.push({
      collectPoint: point.no,
      collectName: point.name,
      xAxisData: [],
      yAxisData: []
    })
  );
  chartList.value = experimentList.value;
}, loading);

/**
 * @description: 拼接原始数据
 */
const combinationRawData = (list: IDevicePointData[]) => {
  if (list.length) {
    experimentList.value.forEach(experiment => {
      experiment.xAxisData = [];
      experiment.yAxisData = [];
      list.forEach(({ points, time }) => {
        if (Object.keys(points).includes(experiment.collectPoint) && points[experiment.collectPoint]) {
          experiment.yAxisData.push(points[experiment.collectPoint]);
          experiment.xAxisData.push(time);
        }
      });
    });
  }
  chartList.value = experimentList.value.map(item => {
    const obj = { ...item };
    obj.xAxisData = obj.xAxisData.map(time => formatDate(time, fullDateFormat));
    return obj;
  });
};

/**
 * @description: 拼装历史数据
 */
const combinationHistoryList = useLoadingFn(async (params: IDeviceDataAcquisitionReq) => {
  const charts = (await dataAcauisitionStore.deviceHistoryDataAcquisition(params, cancelHttp.signal.value)) || [];
  combinationRawData(charts);
}, loading);

onMounted(async () => {
  await generateExperimentList();
});

onUnmounted(cancelHttp.abort);

defineExpose({
  combinationHistoryList
});

const sortState = ref<SortState>({
  time: TableV2SortOrder.DESC,
  point: TableV2SortOrder.DESC
});
</script>

<style scoped lang="scss"></style>
