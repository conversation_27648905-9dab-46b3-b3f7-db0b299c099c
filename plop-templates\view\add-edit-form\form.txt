<template>
  <el-form ref="formRef" :model="form"  {{#if this.existRule}}:rules="rules"{{/if}} label-position="{{this.labelPosition}}">
   {{#each formFields}}
    <el-row :gutter="20">
      {{#each this}}
        <el-col :span="{{../../this.span}}">
              {{#if (equal this.formFieldConfig.type "string")}}
                <el-form-item label="{{this.label}}" prop="{{this.prop}}">
                  <el-input v-model="form.{{this.prop}}" clearable placeholder="{{this.placeholder}}" />
               </el-form-item>
              {{/if}} 
              {{#if (equal this.formFieldConfig.type "number")}}
                <el-form-item label="{{this.label}}" prop="{{this.prop}}">
                   <el-input-number class="!w-full" v-model="form.{{this.prop}}" clearable controls-position="right" placeholder="{{this.placeholder}}"/>
               </el-form-item>
              {{/if}}
              {{#if (equal this.formFieldConfig.type "textarea")}}
                <el-form-item label="{{this.label}}" prop="{{this.prop}}">
                  <el-input v-model="form.{{this.prop}}" :rows="2"  type="textarea" clearable placeholder="{{this.placeholder}}" />
               </el-form-item>
              {{/if}}
              {{#if (equal this.formFieldConfig.type "date")}}
                <el-form-item label="{{this.label}}" prop="{{this.prop}}">
                 <el-date-picker class="!w-full" v-model="form.{{this.prop}}" type="date" clearable placeholder="{{this.placeholder}}"/>
               </el-form-item>
              {{/if}}
              {{#if (equal this.formFieldConfig.type "file")}}
               <el-form-item label="{{this.label}}" prop="{{this.prop}}">
                 <el-upload class="w-full" ref="uploadRef" drag :auto-upload="false" :limit="1" :on-exceed="onExceed">
                   <el-icon><upload-filled /></el-icon>
                   <div>拖动上传 or <em>点击上传</em></div>
                  <!-- <template #tip>
                   <div class="el-upload__tip">jpg/png files with a size less than 500kb</div>
                  </template> -->
                </el-upload>
              </el-form-item>
            {{/if}}  
      </el-col>  
      {{/each}}
    </el-row>
   {{/each}}
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, {{#if this.existFle}} genFileId, FormRules,UploadInstance,UploadProps,UploadRawFile {{/if}} } from "element-plus";
import { I{{properCase name}}Form } from "@/models";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<I{{properCase name}}Form>({});
const formRef = ref<FormInstance>();
{{#if this.existFle }}
 const uploadRef = ref<UploadInstance>();
{{/if}}

{{#if this.existRule }}
const rules:FormRules = {{{ formRule formFields  }}}
{{/if}}

{{#if this.existFle}}
const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};
{{/if}}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: I{{properCase name}}Form) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}


</script>

<style scoped></style>
