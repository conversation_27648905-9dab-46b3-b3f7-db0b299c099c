<template>
  <div class="technical-standard-info h-full flex flex-col overflow-hidden">
    <section class="top mb-4">
      <!-- 工序 -->
      <div class="process flex items-center mb-4">
        <span class="w-[80px] text-right mr-2">工序：</span>
        <div class="process-list">
          <el-radio-group v-model="processValue" @change="processChange">
            <el-radio border>全部</el-radio>
            <el-radio border v-for="item in processOptions" :key="item.id" :label="item.id">{{
              item.processName
            }}</el-radio>
          </el-radio-group>
        </div>
      </div>
      <!-- 采集点 -->
      <div class="acquisition-point flex items-center">
        <span class="w-[80px] text-right mr-2">采集点：</span>
        <div class="process-list">
          <el-radio-group v-model="pointValue" @change="pointChange">
            <el-radio border>全部</el-radio>
            <el-radio :label="true" border>关键采集点</el-radio>
            <el-radio :label="false" border>非关键采集点</el-radio>
          </el-radio-group>
        </div>
      </div>
    </section>
    <section class="center flex-1 flex flex-col">
      <!-- 技术标准数据 -->
      <div v-if="isEditBtn" class="edit-button flex justify-end items-center mb-3">
        <template v-if="isEdit">
          <el-button :disabled="disabled" @click="reset">重置</el-button>
        </template>
        <el-button :type="isEdit ? 'default' : 'primary'" :disabled="disabled" @click="edit">{{
          isEdit ? "取消" : "编辑"
        }}</el-button>

        <template v-if="isEdit">
          <el-button type="primary" :disabled="disabled" :loading="saveLoading" @click="batchSave">保存</el-button>
        </template>
      </div>
      <PureTable
        class="flex-1 overflow-hidden"
        ref="technicalStandardLibraryTableRef"
        row-key="id"
        size="large"
        :data="infoList"
        :height="540"
        :columns="tableColumns"
        :loading="loading"
        showOverflowTooltip
      >
        <template #minValue="data">
          <el-checkbox
            class="!mr-2"
            label="包含"
            v-model="data.row.includeMinValue"
            @update:model-value="newValue => (data.row.includeMinValue = newValue)"
          />
          <el-input-number
            v-model="data.row.minValue"
            :min="0"
            :max="data.row.maxValue || Number.MAX_VALUE"
            class="!w-[120px]"
            controls-position="right"
          />
        </template>

        <template #maxValue="data">
          <el-checkbox
            class="!mr-2"
            label="包含"
            v-model="data.row.includeMaxValue"
            @update:model-value="newValue => (data.row.includeMaxValue = newValue)"
          />

          <el-input-number
            v-model="data.row.maxValue"
            :min="data.row.minValue || 0"
            class="!w-[120px]"
            controls-position="right"
          />
        </template>
        <template #collectRange="data">
          <div>{{ formatCollectRangeDisplay(data.row) }}</div>
        </template>
        <template #empty>
          <CxEmpty />
        </template>
      </PureTable>
    </section>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { computed, onUnmounted, ref, watch, nextTick } from "vue";
import { useColumns } from "./columns";
import { ICollectionItem, ICollectionParams, IProcessData, ITechnicalStandardInfo } from "@/models";
import { useTechnicalStandardStore } from "@/store/modules/technical-standard";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { cloneDeep } from "@pureadmin/utils";
import { ElMessage } from "element-plus";
import { isNumber } from "lodash-unified";
import { GREATE_THAN, GREATE_THAN_EQUAL, LESS_THAN, LESS_THAN_EQUAL, emptyDefaultValue } from "@/consts";

const props = withDefaults(
  defineProps<{
    isEditBtn?: boolean;
    standardInfo?: ITechnicalStandardInfo;
    processOptions: IProcessData[];
  }>(),
  {
    isEditBtn: true
  }
);

const store = useTechnicalStandardStore();
const { showColumns, editColumns } = useColumns();
const infoList = ref<Array<ICollectionItem>>([]);
const infoListOld = ref([]);
const isEdit = ref<boolean>(false);
const processValue = ref();
const pointValue = ref();
const tableColumns = ref<TableColumnList>(showColumns);
const processOptions = computed(() => props.processOptions);
const loading = ref<boolean>(false);
const saveLoading = ref(false);
const technicalStandardLibraryTableRef = ref<PureTableInstance>();
const initDetailInfo = useLoadingFn(getDetailOfStandard, loading);
const queryParams = {};

const disabled = computed(() => {
  return !infoList.value?.length;
});

onUnmounted(() => {
  infoListOld.value = [];
});

watch(
  processOptions,
  newVal => {
    if (newVal?.length) {
      initDetailInfoOfStandard(props.standardInfo);
    }
  },
  {
    immediate: true
  }
);

/** 表格列 变化重新渲染列
 * Eltable 说明 对 Table 进行重新布局。 当表格可见性变化时，您可能需要调用此方法以获得正确的布局
 */
watch(
  () => tableColumns.value,
  () => {
    nextTick(() => {
      technicalStandardLibraryTableRef.value.getTableRef().doLayout();
    });
  }
);

/** 初始化获取数据 */
function initDetailInfoOfStandard(info: ITechnicalStandardInfo) {
  const params = {
    orderStandardId: info.id
  };
  Object.assign(queryParams, params);
  initDetailInfo(queryParams);
}

/** 切换工序 */
function processChange() {
  Object.assign(queryParams, { processId: processValue.value });
  initDetailInfo(queryParams);
}

/** 切换是否关键采集点 */
function pointChange() {
  Object.assign(queryParams, { required: pointValue.value });
  initDetailInfo(queryParams);
}

/** 获取标准库详细信息 */
async function getDetailOfStandard(params: ICollectionParams) {
  const res = (await store.getStandardDetailInfoList(params)).data;
  infoList.value = res || [];
  infoListOld.value = cloneDeep(
    infoList.value.map(item => {
      item["includeMaxValue"] = item.includeMaxValue ? true : false;
      item["includeMinValue"] = item.includeMinValue ? true : false;
      return item;
    })
  );
}

/** 重置 */
function reset() {
  infoList.value = cloneDeep(infoListOld.value);
}

/** 编辑 */
function edit() {
  // 取消编辑
  if (isEdit.value) {
    tableColumns.value = showColumns;
    reset();
  } else {
    // 编辑
    tableColumns.value = editColumns;
  }
  isEdit.value = !isEdit.value;
}

/** 保存数据 */
const batchSave = useLoadingFn(async () => {
  const params = infoList.value.map(item => {
    const { id, includeMaxValue, maxValue, includeMinValue, minValue } = item;
    return {
      id,
      includeMaxValue,
      maxValue,
      includeMinValue,
      minValue
    };
  });
  await store.saveStandardDetailInfo(params);
  tableColumns.value = showColumns;
  isEdit.value = false;
  initDetailInfoOfStandard(props.standardInfo);
  ElMessage.success("保存成功");
}, saveLoading);

/**格式采集范围 */
const formatCollectRangeDisplay = (data: ICollectionItem) => {
  const { maxValue, minValue, includeMaxValue, includeMinValue } = data;

  if (!isNumber(minValue) && !isNumber(maxValue)) {
    return emptyDefaultValue;
  }

  // 有最小值 没有最大值
  if (isNumber(minValue) && !isNumber(maxValue)) {
    return `值${includeMinValue ? GREATE_THAN_EQUAL : GREATE_THAN}${minValue}`;
  }

  // 有最大值 没有最小值
  if (isNumber(maxValue) && !isNumber(minValue)) {
    return `值${includeMaxValue ? LESS_THAN_EQUAL : LESS_THAN}${maxValue}`;
  }

  // 有最小值，有最大值
  return `${minValue}${includeMinValue ? LESS_THAN_EQUAL : LESS_THAN}值${
    includeMaxValue ? LESS_THAN_EQUAL : LESS_THAN
  }${maxValue}`;
};
</script>

<style scoped lang="scss"></style>
