import {
  IImportDataReq,
  IImportDataType,
  IImportExcelTask,
  IListResponse,
  IPagingReq,
  IResponse,
  IUpLoadFileRes
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import {
  ExperimentDataImportItem,
  GetExperimentDataImportTemplateParams
} from "@/models/import/import-experiment-data";

//#region 业务数据导入
export const queryImportDataType = (): Promise<IResponse<Array<IImportDataType>>> => {
  return http.get<void, IResponse<Array<IImportDataType>>>(
    withApiGateway("admin-api/business/import/getImportDataList")
  );
};

export const queryImportDataView = (data: IImportDataReq) => {
  return http.post<IImportDataReq, IListResponse<{ [key: string]: any }>>(
    withApiGateway("admin-api/business/import/getImportDataListByBatchCode"),
    { data }
  );
};

export const getImportTaskResult = (taskId: string) => {
  return http.get<void, IResponse<IImportDataType>>(
    withApiGateway(`admin-api/business/import/getImportDataList/${taskId}`)
  );
};

export const submitImportTask = (task: IImportExcelTask) => {
  return http.post<IImportExcelTask, IResponse<string>>(withApiGateway("admin-api/business/import/import-excel"), {
    data: task
  });
};

export const fileUpolad = (fileData: FormData) => {
  return http.post<FormData, IResponse<IUpLoadFileRes>>(withApiGateway(`admin-api/business/import/upload`), {
    data: fileData,
    headers: { "Content-type": "multipart/form-data" }
  });
};

export const uploadExcelTemplate = (fileData: FormData) => {
  return http.post<FormData, IResponse<IUpLoadFileRes>>(withApiGateway(`admin-api/business/template/upload`), {
    data: fileData,
    headers: { "Content-type": "multipart/form-data" }
  });
};

export const downloadImportTemplate = (dataType: string) => {
  return http.get<void, Blob>(withApiGateway(`admin-api/business/template/download-file?dataType=${dataType}`), {
    responseType: "blob"
  });
};
//#endregion

//#region 试验数据导入
/**
 * @description: 获取试验数据导入列表
 */
export const getExperimentDataImortList = (params: IPagingReq) => {
  return http.post<IPagingReq, IListResponse<ExperimentDataImportItem>>(
    withApiGateway(`admin-api/business/import/iot/import-log/page`),
    {
      data: params
    }
  );
};

/**
 * @description: 导入试验数据
 */
export const importExperimentData = (params: FormData) => {
  return http.post<FormData, any>(
    withApiGateway(`admin-api/business/import/experiment/upload`),
    {
      data: params
    },
    {
      headers: { "Content-type": "multipart/form-data" }
    }
  );
};

/**
 * @description: 获取试验数据导入模板
 */
export const getExperimentDataImportTemplate = (params: GetExperimentDataImportTemplateParams) => {
  return http.get<void, Blob>(
    withApiGateway(
      `admin-api/business/import/experiment/template?subClassCode=${params.subClassCode}&processCode=${params.processCode}`
    ),
    {
      responseType: "blob"
    }
  );
};

//#endregion
