<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top" label-width="7rem">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="生产订单号" prop="productionOrderNo">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>
          <el-input v-model="form.productionOrderNo" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品型号" prop="productModel">
          <el-input v-model="form.productModel" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="产品规格" prop="specificationModel">
          <el-input v-model="form.specificationModel" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :label="accountLabel" prop="account">
          <el-input-number :min="0" v-model="form.account" controls-position="right" class="!w-full" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="盘号" prop="reelNo">
          <el-input v-model="form.reelNo" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

interface FormType {
  productionOrderNo: string;
  productModel: string;
  specificationModel: string;
  account: number;
  reelNo: string;
}

const props = defineProps<{
  measUnit: string;
}>();

const form = reactive<Partial<FormType>>({
  productionOrderNo: "",
  productModel: "",
  specificationModel: "",
  account: undefined,
  reelNo: ""
});
const formRef = ref<FormInstance>();
const accountLabel = computed(() => {
  const label = "试验产品长度/数量";
  if (props.measUnit) {
    return label + `（${props.measUnit}）`;
  }
  return label;
});

const rules: FormRules = {
  productionOrderNo: [{ required: true, message: "请输入", trigger: "change", max: 100 }],
  productModel: [{ required: true, message: "请输入产品型号", trigger: "change", max: 100 }],
  specificationModel: [{ required: true, message: "请输入产品规格", trigger: "change", max: 100 }],
  account: [{ required: true, trigger: "change", message: "请输入产品长度" }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: Partial<FormType>) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form as FormType);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped></style>
