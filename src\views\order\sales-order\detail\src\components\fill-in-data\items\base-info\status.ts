import { defineComponent, h } from "vue";
import { ProductionStateEnum } from "@/enums";
import EnumTag from "@/components/EnumTag";
import { useSalesFillInDataStore, useSalesOrderDetailStore } from "@/store/modules";
import { IProductOrder, IWorkOrder } from "@/models";

const purchaseStore = useSalesOrderDetailStore();
const fillInDataStore = useSalesFillInDataStore();

export default defineComponent({
  name: "Status",
  render() {
    const enumeration: typeof ProductionStateEnum = ProductionStateEnum;
    const enumName = "productionStateEnum";
    const value: ProductionStateEnum = purchaseStore.isCable
      ? (fillInDataStore.data as IProductOrder)?.ipoStatus
      : (fillInDataStore.data as IWorkOrder)?.woStatus;
    return h(EnumTag, { value, enum: enumeration, enumName });
  }
});
