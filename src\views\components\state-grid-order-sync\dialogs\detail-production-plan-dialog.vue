<template>
  <el-dialog v-model="visible" class="middle" title="排产计划详情" align-center destroy-on-close>
    <SchedulingPlanDetail />
  </el-dialog>
</template>

<script setup lang="ts">
import SchedulingPlanDetail from "@/views/order/purchase-order/detail/src/components/scheduling-plan/scheduling-plan-detail/index.vue";
import { ref } from "vue";
import { useSchedulingPlanStore } from "@/store/modules";

const schedulingPlanStore = useSchedulingPlanStore();

const visible = ref(false);

function openDetailDialog(id: string) {
  visible.value = true;
  schedulingPlanStore
    .getSchedulingPlanDetailById(id)
    .then(res => schedulingPlanStore.setSchedulingPlanDetail(res.data));
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped></style>
