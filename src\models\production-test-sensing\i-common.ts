/**
 * 工序列表状态
 */
export interface IProductionMaterialProcess {
  processCode: string;
  processName: string;
  processId: number;
  hasData: boolean;
  /** 是否自动采集 (控制是否可新增和编辑) */
  autoCollect?: boolean;
  /** 是否需要检查工序 */
  needCheckProcess?: boolean;
  /** 是否有自动采集数据 */
  hasAutoCollectData?: boolean;
  /** 是否有系统推送数据 */
  hasSystemPushData?: boolean;
}

export interface IUpLoadFile {
  id: string;
  name: string;
  url: string;
  path?: string;
  downloadUrl?: string;
}

/**
 * 生产试验感知步骤数据状态
 */
export interface IPerceptionProcessStatus {
  rawMaterial: boolean;

  /** 过程检 false 提示缺少 */
  processInfo: boolean;

  /** 自动采集 false 提示缺少 */
  processData: boolean;

  experiment: boolean;
  product: boolean;
}
