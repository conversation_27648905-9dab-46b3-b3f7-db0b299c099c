<template>
  <el-dialog
    title="销售订单行项目"
    v-model="visible"
    class="middle"
    align-center
    destroy-on-close
    @closed="dialogClosed"
  >
    <SalesOrderLineDetail />
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { usePurchaseOrderDetailSalesOrderLineDetailStore } from "@/store/modules/purchase-order-detail";
import SalesOrderLineDetail from "@/views/order/purchase-order/detail/src/link-sales-order/src/sales-order-line/sales-order-line-detail.vue";

const store = usePurchaseOrderDetailSalesOrderLineDetailStore();

const visible = computed({
  get() {
    return store.visible;
  },
  set(value) {
    store.$patch({ visible: value });
  }
});

function dialogClosed() {
  store.$reset();
}
</script>

<style scoped></style>
