<template>
  <div class="h-full flex flex-col">
    <cx-switch :operate-modules="tabList" v-model:current-tab-index="currentTabIndex" />
    <record-list :sub-class-code="currentSubClassCode" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import CxSwitch from "@/components/cx-tab-switch/index.vue";
import RecordList from "./quality-tracing-record-list/index.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getAllSubclasses } from "@/api/category/category";
import { usePageStoreHook } from "@/store/modules/page";

/**
 * 质量追溯记录
 */

const route = useRoute();
usePageStoreHook().setTitle(route.meta.title as string);
const tabList = ref<
  Array<{
    moduleCode: string;
    moduleName: string;
  }>
>([]);
const currentTabIndex = ref(0);

const currentSubClassCode = computed(() => {
  if (!tabList.value.length) {
    return "";
  }
  return tabList.value[currentTabIndex.value].moduleCode;
});

const loading = ref(false);

/**
 * @description: 请求物资种类列表
 */
const requestSubClassList = useLoadingFn(async () => {
  const { data: list } = await getAllSubclasses();
  tabList.value = list.map(item => {
    return {
      moduleCode: item.categoryCode,
      moduleName: item.categoryName
    };
  });
}, loading);

onMounted(requestSubClassList);
</script>

<style scoped></style>
