import { defineStore } from "pinia";
import {
  ICreateSalesOrderLineDto,
  ILinkSalesOrderLines,
  ISalesOrderLine,
  ISalesOrderLineForm,
  ISalesOrderLineLinkDto
} from "@/models";
import * as salesOrderLineService from "@/api";
import * as salesOrderService from "@/api/sales-order-management/sales-order";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { ref, watch } from "vue";
import { getSalesOrderLinesBySalesOrderId } from "@/api";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail/sales-order";

export const usePurchaseOrderDetailSalesOrderLineStore = defineStore(
  "cx-purchase-order-detail-sales-order-line",
  () => {
    const _purchaseOrderDetailStore = usePurchaseOrderDetailStore();
    const _salesOrderStore = usePurchaseOrderDetailSalesOrderStore();
    const createVisible = ref(false);
    const loadingList = ref(false);
    const activeLine = ref<ISalesOrderLine>();
    const salesOrderLines = ref<Array<ISalesOrderLine>>([]);

    watch([() => salesOrderLines.value, () => _purchaseOrderDetailStore.purchaseOrder.lines], () =>
      _updateLinearGradient()
    );

    async function refreshSalesOrderLines() {
      loadingList.value = true;
      const orderId = _salesOrderStore.activeOrderId;
      const purchaseId = _purchaseOrderDetailStore.purchaseOrderId;
      try {
        salesOrderLines.value = orderId ? (await getSalesOrderLinesBySalesOrderId(orderId, purchaseId)).data : [];
      } catch (e) {
        console.error(e);
      }
      loadingList.value = false;
    }

    function linkSalesOrderLine(purchaseLineId: string, salesLines: Array<ILinkSalesOrderLines>) {
      const data: ISalesOrderLineLinkDto = {
        purchaseId: _purchaseOrderDetailStore.purchaseOrderId,
        purchaseLineId,
        salesLines
      };
      return salesOrderLineService.linkSalesOrderLine(data);
    }

    function createSalesOrderLine(purchaseLineId: Array<string>, formData: ISalesOrderLineForm) {
      const { id: salesId, soNo } = _salesOrderStore.activeOrder;
      const { buyerName, id: purchaseId } = _purchaseOrderDetailStore.purchaseOrder;
      const data: ICreateSalesOrderLineDto = {
        ...formData,
        salesId,
        soNo,
        buyerName,
        purchaseId,
        purchaseLineId
      } as ICreateSalesOrderLineDto;
      return salesOrderLineService.createSalesOrderLine(data);
    }

    function unlinkSalesOrderLine(id: string) {
      return salesOrderLineService.unlinkSalesOrderLine(id, _purchaseOrderDetailStore.purchaseOrderId);
    }

    function deleteSalesOrderLine(id: string) {
      return salesOrderService.deleteSaleOrderLineById(id);
    }

    function openCreateDialog() {
      createVisible.value = true;
    }

    function _updateLinearGradient() {
      salesOrderLines.value.forEach(line => {
        line.poItemNoArray = line.poItemNos.split(",");
        const purchaseLines = _purchaseOrderDetailStore.purchaseOrder.lines;
        const colors = line.poItemNoArray
          .map(poItemNo => purchaseLines.find(line => line.poItemNo === poItemNo))
          .map(line => line?.color)
          .filter(color => !!color);
        const delta = (1 / colors.length) * 100;
        const linearGradient = colors.reduce((prev, color, index) => {
          const current = `${color} ${index * delta}%, ${color} ${(index + 1) * delta}%`;
          prev += `${prev ? ", " : ""}${current}`;
          return prev;
        }, "");
        line.linearGradient = `linear-gradient(${linearGradient})`;
      });
    }

    return {
      loadingList,
      activeLine,
      createVisible,
      salesOrderLines,
      openCreateDialog,
      linkSalesOrderLine,
      createSalesOrderLine,
      unlinkSalesOrderLine,
      deleteSalesOrderLine,
      refreshSalesOrderLines
    };
  }
);
