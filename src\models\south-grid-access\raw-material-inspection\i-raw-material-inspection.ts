import { InspectResultEnum } from "@/enums/south-grid-access";
import { IBase } from "@/models";
import { ICollectionItem } from "../material/material";

export interface IRawMaterialInspection extends IBase {
  /** 监造计划Id */

  supervisionPlanId?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 原材料批次号
   */
  materialBatchNo?: string;
  /**
   * 设备唯一编码
   */
  entityId?: string;
  /**
   * 原材料检验批次号
   */
  inspectBatchNo?: string;
  /**
   * 原材料制造商
   */
  materialManufacturer?: string;
  /**
   * 原材料品牌
   */
  materialBrand?: string;
  /**
   * 原材料规格型号
   */
  materialModel?: string;
  /**
   * 原材料生产日期
   */
  productionDate?: string;
  /**
   * 原材料检验日期
   */
  inspectDate?: string;
  /**
   * 原材料检验结果
   */
  inspectResult?: InspectResultEnum;
  /**
   * 原材料供应商检验报告
   */
  materialSupplierReportId?: string;

  /** 原材料供应商检验报告sdcc返回文件名称 */
  materialSupplierReportFileId?: string;

  materialSupplierReportFileName?: string;
  /**
   * 来料抽检报告
   */
  incomingInspectionReportId?: string;

  /** 来料抽检报告sdcc返回文件ID  */
  incomingInspectionReportFileId: string;

  /** 来料抽检报告sdcc返回文件名称 */
  incomingInspectionReportFileName: string;

  /**
   * 原材料类型
   */
  materialType?: string;

  rawMaterialDataList?: Array<ICollectionItem>;
}
