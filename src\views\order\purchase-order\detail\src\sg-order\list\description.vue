<template>
  <div class="flex flex-col w-full">
    <component :is="currentDescriptionComp" :sync="sync" />
  </div>
</template>

<script setup lang="ts">
import StateGridSyncDescription from "./description-detail/state-grid-dec.vue";
import ShangHaiIOTDescription from "./description-detail/shang-hai-iot.vue";
import { IIOTStateGridOrderSync, IStateGridOrderSync } from "@/models";
import { computed, inject } from "vue";
import { SYNC_ORDER_TOKEN } from "../tokens";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";

const props = defineProps<{
  sync: IStateGridOrderSync | IIOTStateGridOrderSync;
}>();

const syncOrderInject = inject(SYNC_ORDER_TOKEN);
const sync = computed(() => props.sync);
const currentDescriptionComp = computed(() => {
  return syncOrderInject?.currentKeyComp.value === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER
    ? StateGridSyncDescription
    : ShangHaiIOTDescription;
});
</script>

<style scoped lang="scss">
:deep(.descriptions) {
  font-size: var(--el-font-size-base);
  color: var(--el-text-color-primary);

  .description-item {
    .label {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-secondary);
      padding-right: 8px;
    }

    .content {
      display: inline-flex;
      align-items: baseline;
      color: var(--el-text-color-primary);
    }
  }
}

:deep(.title) {
  @apply text-lg mb-2;

  .label {
    padding-right: 12px;
  }
}
</style>
