<template>
  <div class="process-info">
    <div class="detail-item" v-if="processInfo || rawMaterialInfo">
      <div class="top flex-bc mb-1">
        <div class="detail-info flex-1 flex">
          <div class="item-text code-info flex mr-2">
            <span class="icon mr-1 text-primary">编</span>
            <ShowTooltip className="max-w-[14em] text-sm" placement="top" :content="rawMaterialInfo.code" />
          </div>
        </div>
        <div class="del-btn flex">
          <el-button
            v-auth="PermissionKey.meta.metaRawMaterialDelete"
            :icon="Delete"
            link
            @click="delRawMaterial(rawMaterialInfo)"
          />
        </div>
      </div>
      <div class="center text-base break-words">
        <span>{{ rawMaterialInfo.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { Delete } from "@element-plus/icons-vue";
import { PermissionKey } from "@/consts";
import { IProductionStageProcessCode, IRawMaterialList } from "@/models/raw-material/i-raw-material-res-v2";

withDefaults(
  defineProps<{
    processInfo: IProductionStageProcessCode;
    rawMaterialInfo: IRawMaterialList;
  }>(),
  {}
);

const emits = defineEmits<{
  (event: "delRawMaterialEvent", id: string): void;
}>();

// const ctx = inject<{
//   delRawMaterialCb: Function;
// }>("rawMaterialV2Token");

/**
 * 删除原材数据
 */
const delRawMaterial = (rawMaterialInfo: IRawMaterialList) => {
  emits("delRawMaterialEvent", rawMaterialInfo.id);
};
</script>

<style scoped lang="scss">
.item-text {
  @apply flex items-center text-secondary;

  .icon {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    width: 16px;
    height: 16px;
    border-radius: 1px;
    margin-right: 4px;
    color: var(--el-color-primary-light-3);
    background: var(--el-color-primary-light-9);
    border: 0.5px solid var(--el-color-primary-light-7);
  }
}

.del-btn {
  .el-button {
    @apply text-secondary;

    &:hover {
      @apply text-regular;
    }
  }
}
</style>
