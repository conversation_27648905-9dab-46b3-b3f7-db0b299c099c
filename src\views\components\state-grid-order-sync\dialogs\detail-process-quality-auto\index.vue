<template>
  <el-dialog v-model="visible" class="large" :title="reportWorkBatchNo" align-center destroy-on-close>
    <chart-list :report-work-id="reportWorkId" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ChartList from "./chart-list.vue";

const visible = ref(false);
const reportWorkId = ref<string>("");
const reportWorkBatchNo = ref<string>("");

async function openDetailDialog(id: string, batchNo: string) {
  reportWorkId.value = id;
  reportWorkBatchNo.value = batchNo;
  visible.value = true;
}

defineExpose({
  openDetailDialog
});
</script>

<style scoped lang="scss">
.label {
  color: var(--el-text-color-secondary);
}

.value:empty:after {
  content: var(--default-value);
}
</style>
