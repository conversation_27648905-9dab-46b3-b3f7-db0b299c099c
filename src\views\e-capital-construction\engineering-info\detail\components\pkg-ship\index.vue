<template>
  <AddEditPkgShipDialog mode="add" @post-save-success="requestList()">
    <template #trigger="{ openDialog }">
      <div class="flex justify-end">
        <el-button
          :loading="loading"
          v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
          type="primary"
          @click="handleSynchronization"
          >一键同步
        </el-button>
        <el-button :icon="Plus" type="primary" @click="openDialog">新增</el-button>
      </div>
    </template>
  </AddEditPkgShipDialog>

  <PureTable
    class="flex-1 overflow-hidden pagination"
    row-key="id"
    :data="state.list"
    :columns="columns"
    :loading="loading"
    showOverflowTooltip
  >
    <template #pullStatus="data">
      <el-tag
        :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
        @click="handleShowDetail(data)"
        class="cursor-pointer"
      >
        {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
      </el-tag>
    </template>
    <template #operation="data">
      <div>
        <AddEditPkgShipDialog mode="edit" :id="data.row.id" @post-save-success="requestList()">
          <template #trigger="{ openDialog }">
            <el-button link type="primary" @click="openDialog">编辑</el-button>
          </template>
        </AddEditPkgShipDialog>
        <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除</ElButton>
      </div>
    </template>
    <template #empty>
      <CxEmptyData />
    </template>
  </PureTable>
  <!-- 同步明细 -->
  <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
</template>

<script setup lang="ts" name="pkg-ship">
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useLoadingFn } from "@/utils/useLoadingFn";
import {
  queryPkgShip,
  deletePkgShipById,
  IntermediateSyncByVoucherTypeApi
} from "@/api/e-capital-construction/engineering-info";
import { IPkgShipForm } from "@/models";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditPkgShipDialog from "./add-edit-pkg-ship/dialog.vue";
import { useRoute } from "vue-router";
import { EquipmentTypeEnumExt, PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
import { PermissionKey } from "@/consts";

const loading = ref(false);
const state = reactive<{
  list: Array<IPkgShipForm>;
  params: { [key: string]: string };
}>({
  list: [],
  params: {}
});
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
onMounted(() => {
  requestList();
});

const route = useRoute();
const { columns } = useColumns(route.query.type == EquipmentTypeEnumExt.Combiner.toString());
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("PkgShip", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deletePkgShipById(id);
  ElMessage.success("删除成功");
  requestList();
};

const requestList = useLoadingFn(async () => {
  const { data } = await queryPkgShip(route.query.id as string);
  state.list = data;
}, loading);
</script>
