import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IListResponse, IResponse } from "@/models";
import { ICreateDevice, IDevice, IDeviceReq } from "@/models/device";

/** 查询设备列表不分页 */
export const queryDeviceList = (params: IDeviceReq) => {
  return http.post<IDeviceReq, IResponse<Array<IDevice>>>(withApiGateway(`admin-api/business/device/getDeviceList`), {
    data: { ...params }
  });
};

/** 查询页面分页 */
export const queryDevice = (params: IDeviceReq) => {
  return http.post<IDeviceReq, IListResponse<IDevice>>(withApiGateway(`admin-api/business/device/getAllDevices`), {
    data: { ...params }
  });
};

export const createDevice = (data: ICreateDevice) => {
  return http.post<ICreateDevice, IResponse<boolean>>(
    withApiGateway("admin-api/business/device/create"),
    { data },
    { showErrorInDialog: true }
  );
};

export const editDevice = (data: ICreateDevice) => {
  return http.put<ICreateDevice, IResponse<boolean>>(
    withApiGateway(`admin-api/business/device/updateDevice/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

export const deleteDevice = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/device/deleteDevice/${id}`));
};

export const getDeviceDetailById = (id: string) => {
  return http.get<string, IResponse<IDevice>>(withApiGateway(`admin-api/business/device/getDeviceById/${id}`));
};

/** 一键同步 */
export const syncAllDevice = () => {
  return http.post<void, IResponse<boolean>>(withApiGateway(`admin-api/business/device/syncAllDevice`));
};

/** 单个同步，返回结果为true仅表示同步完成，不表示同步是否成功 */
export const syncDeviceById = (id: string) => {
  return http.post<void, IResponse<boolean>>(withApiGateway(`admin-api/business/device/syncDevice/${id}`));
};

/** 检查当前租户是否有权新增设备 true 可以新增 false不可以新增 */
export const checkDeviceAllowAdd = () => {
  return http.get<void, IResponse<boolean>>(withApiGateway(`admin-api/business/device/checkDeviceNumByTenant`));
};
