<template>
  <el-dialog
    title="新增工单"
    class="middle"
    v-model="visible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="dialogOpen"
    @close="dialogClose"
  >
    <CreateWorkOrderForm ref="createForm" />

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button
        type="primary"
        v-if="createForm?.isFirstStep"
        :disabled="createForm.nextDisabled"
        @click="createForm.next"
      >
        下一步
      </el-button>
      <template v-if="createForm?.isSecondStep">
        <el-button @click="createForm.prev">上一步</el-button>
        <el-button type="warning" :loading="continueLoading" @click="handleSaveAndContinue">保存，并继续新增</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">保存</el-button>
      </template>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import CreateWorkOrderForm from "../forms/create-work-order-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useNonCableWorkOrderStore } from "@/store/modules";
import { ElMessage } from "element-plus";

const props = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", visible: boolean): void;
  (e: "shouldRefresh"): void;
}>();

const workOrderStore = useNonCableWorkOrderStore();

const createForm = ref<InstanceType<typeof CreateWorkOrderForm>>();
const saveLoading = ref(false);
const continueLoading = ref(false);
const handleSave = useLoadingFn(save, saveLoading);
const handleSaveAndContinue = useLoadingFn(saveAndContinue, continueLoading);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(visible: boolean) {
    emit("update:modelValue", visible);
  }
});

let saveSuccess: boolean;

function dialogOpen() {
  saveSuccess = false;
}

function dialogClose() {
  if (saveSuccess) {
    emit("shouldRefresh");
  }
}

async function saveAndContinue() {
  const success = await createWorkOrder()
    .then(() => true)
    .catch(() => false);
  if (!success) {
    return;
  }
  saveSuccess = true;
  ElMessage.success("新增成功");
  createForm.value.continueCreate();
}

async function save() {
  const success = await createWorkOrder()
    .then(() => true)
    .catch(() => false);
  if (!success) {
    return;
  }
  saveSuccess = true;
  ElMessage.success("新增成功");
  visible.value = false;
}

async function createWorkOrder() {
  const workOrder = await createForm.value.getValidValue();
  return workOrderStore.createWorkOrder(workOrder);
}
</script>

<style scoped></style>
