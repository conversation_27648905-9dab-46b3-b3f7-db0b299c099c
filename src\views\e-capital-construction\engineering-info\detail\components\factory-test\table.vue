<template>
  <!-- 出厂试验 -->
  <div v-loading="loading" class="flex justify-between pb-3">
    <el-radio-group v-model="state.radioType" class="gap-2" @change="handleChangeRadio">
      <el-radio border v-for="item in collectionList" :key="item.code" :label="item.code" size="small">{{
        item.name
      }}</el-radio>
    </el-radio-group>
    <div style="white-space: nowrap">
      <el-button
        :loading="loading"
        v-auth="PermissionKey.ejijian.ejijianManagementDetailOneClickSync"
        type="primary"
        @click="handleSynchronization"
        >一键同步</el-button
      >
      <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
    </div>
  </div>
  <PureTable
    row-key="id"
    class="flex-1 overflow-auto"
    :data="filterTableData"
    :columns="columnsConfig"
    size="large"
    v-loading="loading"
    showOverflowTooltip
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
    <template #pullStatus="data">
      <el-tag
        :type="PullStatusEnumTagType[data.row?.pullStatus] || 'info'"
        @click="handleShowDetail(data)"
        class="cursor-pointer"
      >
        {{ PullStatusEnumMapDesc[data.row?.pullStatus] || "--" }}
      </el-tag>
    </template>
    <template #operation="data">
      <ElButton type="primary" link @click="handleEdit(data.row)"> 编辑 </ElButton>
      <ElButton type="danger" link @click="handleDelete(data.row.id)"> 删除 </ElButton>
    </template>
  </PureTable>
  <!-- 同步明细 -->
  <SynchronousDetails :dialog-show="dialogShow" :batch-id="currentStore.batchId" @close="dialogShow = false" />
  <el-dialog
    v-model="state.dialogShow"
    :title="state.isEdit ? '编辑' : '新增'"
    width="52%"
    :destroy-on-close="true"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCloseDialog()"
  >
    <edit-form
      ref="tableFormRef"
      v-loading="loading"
      :detail="state.selectRow"
      :collection="collectionList"
      :table-field="state.tableField"
      :isEdit="state.isEdit"
    />
    <template #footer>
      <span>
        <el-button :loading="loading" @click="handleCloseDialog()">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSave(false)">保存</el-button>
        <el-button v-if="!state.isEdit" :loading="loading" type="primary" @click="handleSave(true)"
          >保存并新增</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useConfirm } from "@/utils/useConfirm";
import { Plus } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useColumns } from "./column-config";
import EditForm from "./edit-form.vue";

// utils
import {
  FactoryTestApi,
  FactoryTestCreateApi,
  FactoryTestEditApi,
  FactoryTestDeleteApi,
  FactoryTableDetailApi,
  CollectionApi,
  FactoryTableApi,
  IntermediateSyncByVoucherTypeApi
} from "@/api/e-capital-construction/engineering-info/index";

import { FactoryTestModel, CollectionModel, FactoryTableModel } from "@/models";
import { EquipmentTypeEnumExt, PullStatusEnumMapDesc, PullStatusEnumTagType } from "@/enums";
import SynchronousDetails from "@/views/e-capital-construction/engineering-info/detail/components/synchronous-details/index.vue";
import { PermissionKey } from "@/consts";

const loading = ref(false);
const tableFormRef = ref();

const route = useRoute();
const tableData = ref([] as Array<FactoryTestModel>);

/** 过滤后的表单 */
const filterTableData = ref([] as Array<FactoryTestModel>);

/** 采集项 */
const collectionList = ref([] as Array<CollectionModel>);
/**
 * @description:  单类型一键同步
 */
const handleSynchronization = useLoadingFn(async () => {
  const { data } = await IntermediateSyncByVoucherTypeApi("FactoryTest", route.query.id as string);
  if (data) {
    ElMessage.success("一键同步成功!");
    requestList();
  }
}, loading);
const state = reactive<{
  /** 动态表单字段 */
  tableField: FactoryTableModel;
  radioType: string;
  selectRow: FactoryTestModel;
  dialogShow: boolean;
  isEdit: boolean;
  formData: FactoryTestModel;
  collectionJSON: { [key: string]: string };
}>({
  tableField: {},
  radioType: "",
  selectRow: {} as FactoryTestModel,
  dialogShow: false,
  isEdit: false,
  formData: {} as FactoryTestModel,
  collectionJSON: {}
});
const dialogShow = ref(false);
const currentStore = reactive({
  batchId: undefined
});
const handleShowDetail = data => {
  if (data.row?.pullStatus) {
    currentStore.batchId = data.row.batchId;
    dialogShow.value = true;
  }
};
/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();

let columnsConfig = useColumns(isCombiner);

/**
 * @description: 请求列表数据
 */
const requestList = useLoadingFn(async () => {
  const { data } = await FactoryTestApi(route.query.id as string);
  tableData.value = data;
  filterTableData.value = data.filter(item => item.testItemCode == state.radioType);
}, loading);

/**
 * @description: 获取表单字段
 */
const getTableField = useLoadingFn(async () => {
  const { data } = await FactoryTableApi(route.query.id as string, state.radioType);
  state.tableField = {
    collectionItems: data.collectionItems,
    enviromentItems: data.enviromentItems
  };
}, loading);

/**
 * @description: 根据生产阶段编码、设备类型获取采集类型
 */
const getCollectionList = useLoadingFn(async () => {
  state.collectionJSON = {};
  const { data } = await CollectionApi("SYSJ", Number(route.query.type as string), route.query.equipmentName as string);
  collectionList.value = data;
  data.forEach(item => {
    state.collectionJSON[item.code] = item.name;
  });
  columnsConfig = useColumns(isCombiner);
  state.radioType = collectionList.value[0].code;
  requestList();
  getTableField();
}, loading);

/**
 * @description: 新增
 */
const handleAdd = () => {
  state.dialogShow = true;
  state.selectRow = {
    testItemCode: state.radioType,
    enviromentValueData: [],
    measureValueValue: [],
    testItemName: collectionList.value.filter((item: CollectionModel) => item.code == state.radioType)[0].name
  } as FactoryTestModel;
  state.isEdit = false;
};

/**
 * @description: 取消
 */
const handleCloseDialog = () => {
  state.dialogShow = false;
  state.isEdit = false;
};

/**
 * @description: 编辑
 */
const handleEdit = useLoadingFn(async (row: FactoryTestModel) => {
  const { data } = await FactoryTableDetailApi(row.id);
  state.selectRow = data;
  state.isEdit = true;
  state.dialogShow = true;
}, loading);

/**
 * @description: 切换采集项
 */
const handleChangeRadio = async () => {
  requestList();
  getTableField();
};

/**
 * @description: 删除
 */
const handleDelete = async (id: string) => {
  if (!(await useConfirm("是否确认删除", "删除确认"))) {
    return;
  }
  onDelete(id);
};

const onDelete = useLoadingFn(async (id: string) => {
  const { data } = await FactoryTestDeleteApi(id as string);
  if (data) {
    requestList();
    ElMessage({ type: "success", message: "删除成功" });
  }
}, loading);

/**
 * @description: 保存
 */
const handleSave = useLoadingFn(async (isAdd: boolean) => {
  if (!tableFormRef.value) {
    return;
  }
  const valid = await tableFormRef.value.validateForm();
  if (!valid) {
    return;
  }
  const params = {
    ...tableFormRef.value.getFormValue(),
    equipmentId: route.query.id as string,
    equipmentType: route.query.type as string
  };
  const { data } = state.isEdit ? await FactoryTestEditApi(params) : await FactoryTestCreateApi(params);
  if (data) {
    ElMessage.success(state.isEdit ? "编辑成功 ！" : "新增成功！");
    requestList();
    state.isEdit = false;
    if (isAdd) {
      tableFormRef.value.resetFormValue();
    } else {
      state.dialogShow = false;
    }
  }
}, loading);

onMounted(() => {
  getCollectionList();
});
</script>

<style lang="scss" scoped>
.label {
  @apply text-base text-secondary mb-2;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
