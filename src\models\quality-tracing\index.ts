import {
  ProductionStageCodeToQualitySpecificationCategoryEnum,
  QualitySpecificationMatchTypeEnum
} from "@/enums/quality-specification";
import { IPagingReq, ISortReq } from "../i-paging-req";

/**
 * 质量规范列表请求参数
 */
export interface QualitySpecificationListParams extends IPagingReq {
  /** 关键字 */
  keyWords?: string;
  /** 物资种类 */
  subClassCode?: string;
}

/**
 * 质量规范列表项
 */
export interface QualitySpecificationItem {
  /** 规范编号 */
  qualityCode: string;
  /** 规范名称 */
  qualityName: string;
  /** 备注 */
  remark: string;
  /** 是否启用 */
  enabled: boolean;
  /** 原材料权重 */
  rawMaterialWeight: number;
  /** 生产过程权重 */
  processWeight: number;
  /** 实验权重 */
  experimentWeight: number;
  /** 工艺稳定性权重 */
  processStabilityWeight: number;
  /** 物资品类 */
  categoryCode: string;
  /** 物资种类 */
  subClassCode: string;
  /** 主键 */
  id: string;
  /** 物资品类名称 */
  categoryName: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 更新时间 */
  updateTime: string;
  /** 适用类型 */
  matchType: QualitySpecificationMatchTypeEnum;
}

/**
 * 新增质量规范请求参数
 */
export interface EditQualitySpecificationParams {
  /** 物资种类 */
  subClassCode: string;
  /** 规范编号 */
  qualityCode: string;
  /** 规范名称 */
  qualityName: string;
  /** 备注 */
  remark: string;
  /** 是否启用 */
  enabled: boolean;
  /** 原材料权重 */
  rawMaterialWeight: number;
  /** 过程检权重 */
  processWeight: number;
  /** 实验权重 */
  experimentWeight: number;
  /** 工艺稳定性权重 */
  processStabilityWeight: number;
  /** 适用类型 */
  matchType: QualitySpecificationMatchTypeEnum;
}

/**
 * 质量规范详情明细列表请求参数
 */
export interface QualitySpecificationParticularDetailListParams extends IPagingReq, ISortReq {
  /** 检测类型 */
  category: ProductionStageCodeToQualitySpecificationCategoryEnum;
}

/**
 * 质量规范明细各阶段的合计分值映射
 */
export interface QualitySpecificationParticularDetailScore {
  /** 原材料合计分值 */
  rawMaterialTotalScore: number;
  /** 生产过程合计分值 */
  processTotalScore: number;
  /** 实验合计分值 */
  experimentTotalScore: number;
  /** 工艺稳定性合计分值 */
  processStabilitysTotalScore: number;
}

/**
 * 质量规范详情明细列表项
 */
export interface QualitySpecificationParticularDetailListItem {
  id: string;
  /** 所属质量规范id */
  qualityId: string;
  category: number;
  /** 检测类型id */
  processId: string;
  /** 检测类型编码 */
  processCode: string;
  /** 检测类型名称 */
  processName: string;
  /** 检测项id */
  modelId: string;
  /** 检测项编码 */
  modelCode: string;
  /** 检测项名称 */
  modelName: string;
  /** 判断类型 */
  judgeType: number;
  /** 评分 */
  score: number;
  /** 左边达标值 */
  standardMin: number | null;
  /** 右边达标值 */
  standardMax: number | null;
  /** 左边操作符 */
  minOp: number | null;
  /** 右边操作符 */
  maxOp: number | null;

  judgeStandard: Array<JudgeStandard>;

  /** 判断标准描述 */
  judgeStandardStr: string;
}

/**
 * 判断标准
 */
export interface JudgeStandard {
  minValue: number;
  minOp: number | null;
  maxValue: number;
  maxOp: number | null;
  score: number;
  sort?: number;
}

/**
 * 新增/编辑 质量规范详情明细请求参数
 */
export interface EditQualitySpecificationParticularDetailParams {
  /** 所属质量规范id */
  qualityId: string;
  /** 检测阶段 */
  category: number;
  /** 检测类型id */
  processId: string;
  /** 检测项id */
  modelId: string;
  /** 判断类型 */
  judgeType: number;
  /** 评分 */
  score: number;
  /** 左边达标值 */
  standardMin: number | null;
  /** 右边达标值 */
  standardMax: number | null;
  /** 左边操作符 */
  minOp: number | null;
  /** 右边操作符 */
  maxOp: number | null;
  /** 判断标准列表 */
  judgeStandard: Array<JudgeStandard>;
}

/**
 * 质量规范明细(检测标准)的检测项列表项
 */
export interface QualitySpecificationParticularDetailInsepectionItem {
  id: string;
  /** 编码 */
  code: string;
  /** 名称 */
  name: string;
  /** 采集类型 */
  collectionType: number;
  /** 采集类型名称 */
  collectionTypeName: string;
  /** 是否禁用 */
  disabled: boolean;
}

/**
 * 适用物料列表请求参数
 */
export interface ApplicationMaterialListParams extends IPagingReq {
  /** 质量规范id */
  qualitySpecificationId: string;
}

/**
 * 可选适用物料列表请求参数
 */
export interface ChoosableApplicationMaterialListParams extends IPagingReq {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 关键字 */
  keyWords?: string;
}

/**
 * 适用物料列表项
 */
export interface ApplicationMaterialListItem {
  /** 品类编码 */
  categoryCode: string;
  /** 品类名称 */
  categoryName: string;
  /** 创建时间 */
  createTime: string;
  /** 主键 */
  id: string;
  /** 物料编码 */
  materialCode: string;
  /** 物料描述 */
  materialDescribe: string;
  /** 物料名称 */
  materialName: string;
  /** 物料单位枚举值 */
  materialUnit: number;
  /** 物料单位名称 */
  materialUnitName: string;
  /** 规格型号 */
  specificationModel: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 电压等级 */
  voltageClass: string;
}

/**
 * 添加适用物料请求参数
 */
export interface AddApplicationMaterialParams {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 添加的物料id列表 */
  materialIdList: Array<string>;
}

/**
 * 移除适用物料请求参数
 */
export interface RemoveApplicationMaterialParams {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 移除的物料id列表 */
  removeChooseMaterialId: string;
}

/**
 * 适用销售订单行列表请求参数
 */
export interface ApplicationSalesOrderLineListParams extends IPagingReq {
  /** 质量规范id */
  qualitySpecificationId: string;
}

/**
 * 可选适用销售订单行列表请求参数
 */
export interface ChoosableApplicationSalesOrderLineListParams extends IPagingReq {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 关键字 */
  keyWords?: string;
}

/**
 * 适用销售订单行列表项
 */
export interface ApplicationSalesOrderLineListItem {
  /** 品类编码 */
  categoryCode: string;
  /** 品类名称 */
  categoryName: string;
  /** 创建时间 */
  createTime: string;
  /** 物料编码 */
  materialCode: string;
  /** 物料名称 */
  materialName: string;
  /** 物料单位 */
  materialUnit: number;
  /** 物料单位名称 */
  materialUnitName: string;
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 销售订单id */
  salesId: string;
  /** 销售订单行id */
  salesLineId: string;
  /** 销售订单行号 */
  salesLineNo: string;
  /** 销售订单号 */
  soNo: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 物资种类名称 */
  subClassName: string;
}

/**
 * 可选销售订单行列表项
 */
export interface SelectSalesOrderLineListItem {
  /** 主键 */
  id: string;
  /** 物料编码 */
  materialCode: string;
  /** 物料描述 */
  materialDescribe: string;
  /** 物料名称 */
  materialName: string;
  /** 物料数量 */
  materialNumber: string;
  /** 物料单位枚举值 */
  materialUnit: number;
  /** 物料单位名称 */
  materialUnitName: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 电压等级 */
  voltageClass: string;
  /** 销售订单行号 */
  soItemNo: string;
  /** 销售订单号 */
  soNo: string;
  /** 规格型号 */
  specificationType: string;
}

/**
 * 添加适用销售订单行请求参数
 */
export interface AddApplicationSalesOrderLineParams {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 添加的销售订单行id列表 */
  salesLineIdList: Array<string>;
}

/**
 * 移除适用销售订单行请求参数
 */
export interface RemoveApplicationSalesOrderLineParams {
  /** 质量规范id */
  qualitySpecificationId: string;
  /** 移除的销售订单行id列表 */
  removeChooseSalesLineId: string;
}

/**
 * 质量追溯记录等级列表请求参数
 */
export interface QualityTracingRecordLevelListParams {
  /** 物资种类 */
  subClassCode: string;
  /** 开始时间 */
  dateFrom?: string;
  /** 结束时间 */
  dateTo?: string;
  /** 关键字 */
  keyWord?: string;
}

/**
 * 质量追溯记录等级列表项
 */
export interface QualityTracingRecordLevelListItem {
  /** 质量等级ID */
  qualityLevelId: string;
  /** 等级名称 */
  levelName: string;
  /** 等级描述 */
  levelDesc: string;
  /** 数量 */
  countVal: number;
}
/**
 * 质量追溯记录列表请求参数
 */
export interface QualityTracingRecordListParams extends IPagingReq, ISortReq {
  /** 物资种类 */
  subClassCode: string;
  /** 质量等级ID */
  qualityLevelId: string;
  /** 关键字 */
  keyWords?: string;
  /** 开始时间 */
  dateFrom?: string;
  /** 结束时间 */
  dateTo?: string;
}

/**
 * 质量追溯记录列表项
 */
export interface QualityTracingRecordListItem {
  /** 记录id （该列表是根据生产订单/工单查出，如果id为空，表示该单子还未生成追溯记录） */
  id: string | null;
  /** 生产订单/工单 ID */
  dataId: string;
  /** 生产订单id */
  productionId: string;
  /** 生产订单/工单 编号 */
  dataNo: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 物资种类编码 */
  subClassCode: string;
  /** 物料名称 */
  materialName: string;
  /** 销售订单编号 */
  soNos: string;
  /** 销售订单ID */
  soIds: string;
  /** 采购订单编号 */
  poNos: string;
  /** 采购订单ID */
  poIds: string;
  /** 采购订单状态 */
  ipoStatus: number;
  /** 质量规范ID */
  qualityId: string;
  /** 质量规范名称 */
  qualityName: string;
  /** 原材料分值 */
  rawMaterialScore: number;
  /** 生产过程分值 */
  processScore: number;
  /** 实验分值 */
  experimentScore: number;
  /** 工艺稳定性分值 */
  processStabilityScore: number;
  /** 总分值 */
  totalScore: number;
  /** 计算时间 */
  calcTime: string;
  /** 是否正在计算 */
  calculating: boolean;
}

/**
 * 为生产订单/工单绑定质量规范请求参数
 */
export interface BindingQualitySpecificationParams {
  /** 物资种类 */
  subClassCode: string;
  /** 质量规范 */
  qualityId: string;
  /** 订单/工单id */
  dataId: string;
}

/**
 * 质量追溯记录详情
 */
export interface QualityTracingRecordDetail {
  /** 追溯记录id */
  id: string;
  /** 记录生成时间 */
  calcTime: string;
  /** 生产订单/工单id */
  dataId: string;
  /** 生产订单/工单编号 */
  dataNo: string;
  /** 出厂试验分数 */
  experimentScore: number;
  /** 出厂试验权重 */
  experimentWeight: number;
  /** 物料编码 */
  materialCode: string;
  /** 物料名称 */
  materialName: string;
  /** 过程检验权重 */
  processWeight: number;
  /** 过程检验分值 */
  processScore: number;
  /** 工艺稳定性权重 */
  processStabilityWeight;
  /** 工艺稳定性分值 */
  processStabilityScore: number;
  /** 质量规范id */
  qualityId: string;
  /** 质量规范名称 */
  qualityName: string;
  /** 原材料权重 */
  rawMaterialWeight: number;
  /** 原材料分数 */
  rawMaterialScore: number;
  /** 分数计算描述 */
  scoreDescription: string;
  /** 物资种类 */
  subClassCode: string;
  /** 物资种类名称 */
  subClassName: string;
  /** 总分 */
  totalScore: number;
  /** 权重描述 */
  weightDesc: string;
  /** 采购订单id */
  poIds: string;
  /** 采购订单编号 */
  poNos: string;
  /** 销售订单id */
  soIds: string;
  /** 销售订单编号 */
  soNos: string;
  /** 生产订单id */
  productionId: string;
}

/**
 * 质量追溯记录详情分类列表请求参数
 */
export interface QualityTracingCategoryRecordListParams extends IPagingReq, ISortReq {
  /** 阶段分类 */
  category: ProductionStageCodeToQualitySpecificationCategoryEnum;
}

/**
 * 质量追溯记录详情分类列表项
 */
export interface QualityTracingCategoryRecordListItem {
  /** 质量规范详情id */
  qualityDetailId: string;
  /** 阶段分类 */
  category: ProductionStageCodeToQualitySpecificationCategoryEnum;
  /** 检测类型id */
  processId: string;
  /** 检测类型编码 */
  processCode: string;
  /** 检测类型名称 */
  processName: string;
  /** 检测项id */
  modelId: string;
  /** 检测项编码 */
  modelCode: string;
  /** 检测项名称 */
  modelName: string;
  /** 判断类型 */
  judgeType: number;
  /** 评分 */
  score: number;
  /** 左边达标值 */
  standardMin: number;
  /** 左边操作符 */
  minOp: number;
  /** 右边达标值 */
  standardMax: number;
  /** 最大值操作符 */
  maxOp: number;
  /** 判断标准描述 */
  judgeStandardStr: string;
  /** 实际值 */
  actualValue: number[];
  /** 实际评分 */
  actualScore: number;
  /** 设备信息 */
  deviceInfo: string[];
  /** 备注 */
  remark: string;
}

/**
 * 质量等级列表项
 */
export interface QualityLevelListItem {
  id: string;
  /** 等级名称 */
  levelName: string;
  /** 最小值 */
  minValue: number;
  /** 最小值操作符 */
  minOp: number;
  /** 最大值 */
  maxValue: number;
  /** 最大值操作符 */
  maxOp: number;
}

/**
 * 编辑质量等级入参
 */
export interface EditQualityLevelParams {
  data: Array<QualityLevelListItem>;
}

/**
 * 自动匹配质量规范响应结果
 */
export interface BatchMatchingQualitySpecificationRes {
  /** 加入匹配队列的生产订单数量 */
  productionNum: number;
  /** 加入匹配队列的工单数量 */
  workOrderNum: number;
}
