import { ImportDataTypeMapName } from "@/enums";
import { IImportDataType } from "@/models";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
const { dateFormatter } = useTableCellFormatter();

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "数据类型",
      prop: "dataType",
      formatter: (row: IImportDataType) => ImportDataTypeMapName[row.dataType]
    },
    {
      label: "最新导入时间",
      prop: "newImportTime",
      formatter: dateFormatter()
    },
    {
      label: "总条数",
      prop: "count"
    },
    {
      label: "导入成功条数",
      prop: "successCount"
    },
    {
      label: "导入失败条数",
      prop: "errorCount"
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: 390
    }
  ];
  return { columns };
}
