<template>
  <!-- 存栈管理 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="过程检测" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="存栈部位"
          prop="stackingPart"
          :rules="{ required: true, message: '请选择存栈部位', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.stackingPart">
            <el-radio v-for="item in StackingPartEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="子实物编码"
          prop="subPhysicalItemCode"
          :rules="{
            required: formData.stackingPart == StackingPartEnum.Sleeve,
            message: '请输入子实物编码',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.subPhysicalItemCode" placeholder="请输入子实物编码" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="实物ID（原材料组部件）"
          prop="utcNum"
          :rules="{
            required: formData.mainEquipmentutcNum,
            message: '请输入 实物ID（原材料组部件）',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.utcNum" placeholder="请输入 实物ID（原材料组部件）" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="存栈开始时间"
          prop="stackingStartTime"
          :rules="{ required: true, message: '请选择存栈开始时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="formData.stackingStartTime"
            placeholder="请选择存栈开始时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="充气存放开始时间"
          prop="inflationStorageStartTime"
          :rules="{
            required:
              formData.stackingPart == StackingPartEnum.Ontology ||
              formData.stackingPart == StackingPartEnum.HighVoltage,
            message: '请选择充气存放开始时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.inflationStorageStartTime"
            placeholder="请选择充气存放开始时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="本体压力表压力值（kPa）"
          prop="mainBodyPressureGaugeValue"
          :rules="{
            required: formData.stackingPart == StackingPartEnum.Ontology,
            message: '请输入本体压力表压力值',
            trigger: 'change'
          }"
        >
          <el-input-number
            class="w-full"
            :precision="3"
            v-model="formData.mainBodyPressureGaugeValue"
            placeholder="请输入本体压力表压力值"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="注油存放开始时间"
          prop="oilFillingStorageStartTime"
          :rules="{
            required:
              formData.stackingPart == StackingPartEnum.Ontology ||
              formData.stackingPart == StackingPartEnum.HighVoltage,
            message: '请选择注油存放开始时间',
            trigger: 'change'
          }"
        >
          <el-date-picker
            v-model="formData.oilFillingStorageStartTime"
            placeholder="请选择注油存放开始时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="存放方式"
          prop="storageMethod"
          :rules="{
            required:
              formData.stackingPart == StackingPartEnum.Ontology ||
              formData.stackingPart == StackingPartEnum.HighVoltage,
            message: '请选择存放方式',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.storageMethod">
            <el-radio v-for="item in StorageMethodEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="存放地点"
          prop="storageLocation"
          :rules="{ required: true, message: '请选择存放地点', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.storageLocation">
            <el-radio v-for="item in StorageLocationEnumOptions" :key="item.value" :label="item.value">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import {
  StackingPartEnum,
  StorageLocationEnumOptions,
  StorageMethodEnumOptions,
  StackingPartEnumOptions
} from "@/enums";
import { StackManageModel } from "@/models";
import TitleBar from "@/components/TitleBar/index";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    detail: StackManageModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as StackManageModel;
    },
    isEdit: false
  }
);
const formData = reactive({} as StackManageModel);
const route = useRoute();

watchEffect(() => {
  Object.assign(formData, props.detail);
  if (!props.isEdit) {
    formData.equipmentType = Number(route.query.type);
  }
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as StackManageModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
