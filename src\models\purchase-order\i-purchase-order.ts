import { ContractTypeEnum, LinkStepEnum, PurchaseChannel, ProcessStepEnum, VirtualFlag } from "@/enums";
import { IPurchaseOrderLine, ISalesOrderLine } from "@/models";

interface IPurchaseOrderBase {
  id: string;
  poNo: string;
  /** 种类名称 */
  subClassName: string;
  /** 项目名称 */
  prjName: string;
  /** 采购方公司名称 */
  buyerName: string;
  /** 合同编号 */
  conCode: string;
  /** 合同名称 */
  conName: string;
  /** 合同编号(国网经法) */
  sellerConCode: string;
  /** 合同签订日期 */
  sellerSignTime: string;
  /** 物资种类编码 */
  categoryCode: string;
  /** 是否关注 */
  follow: boolean;
  /** 是否归档 */
  documentation?: boolean;
}

export interface IPurchaseOrder extends IPurchaseOrderBase {
  readed: boolean;
  /** 当前填报环节,示例值(1) */
  linkStep: LinkStepEnum;
  synType: boolean;
  pullingTime: Date;
  /** 订单来源 */
  syncChannel: PurchaseChannel;
}

export interface IPurchaseOrderDetail extends IPurchaseOrderBase {
  /** 物资种类编码 */
  subClassCode: string;
  /** 技术规范流水号 */
  serialNumber: string;
  /** 合同类型 */
  conType: ContractTypeEnum;
  /** 物资大类名称 */
  matMaxName: string;
  /** 物资中类名称 */
  matMedName: string;
  /** 物资小类名称 */
  matMinName: string;
  /** 虚拟订单标识 */
  virFlag: VirtualFlag;
  /** 当前进度 */
  linkStep: ProcessStepEnum;
  /** 采购订单行信息 */
  lines: Array<IPurchaseOrderLine>;
  /** 关联销售订单行信息 */
  linkSalesLines?: Array<ISalesOrderLine>;
  /** 订单渠道(来源) */
  syncChannel: PurchaseChannel;
}

export const synTypeMap: Map<boolean, string> = new Map<boolean, string>([
  [true, "purchaseOrder.column.purchaseOrder.synState.true"],
  [false, "purchaseOrder.column.purchaseOrder.synState.false"]
]);
