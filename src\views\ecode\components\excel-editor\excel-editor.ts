import "./jquery.mousewheel.min.js";
import LuckySheet from "luckysheet/dist/luckysheet.esm.js";
import "luckysheet/dist/css/luckysheet.css";
import "luckysheet/dist/assets/iconfont/iconfont.css";
import LuckyExcel from "luckyexcel";
import { read } from "xlsx/xlsx.esm.mjs";

// Types
import type { WorkBook, DefinedName } from "xlsx";
import type { IExcelEditorScreenshot, IExcelEditorValue, IExcelEditorNames } from "./model";

class ExcelEditor {
  static columeHeaderWordIndex: Recordable<number> = {
    A: 0,
    B: 1,
    C: 2,
    D: 3,
    E: 4,
    F: 5,
    G: 6,
    H: 7,
    I: 8,
    J: 9,
    K: 10,
    L: 11,
    M: 12,
    N: 13,
    O: 14,
    P: 15,
    Q: 16,
    R: 17,
    S: 18,
    T: 19,
    U: 20,
    V: 21,
    W: 22,
    X: 23,
    Y: 24,
    Z: 25
  };

  _workbook: WorkBook | null = null;
  LuckyExcelOptions: Recordable = {};
  screenshots: IExcelEditorScreenshot[] = [];

  constructor(options = {}) {
    this.LuckyExcelOptions = {
      container: "luckysheet", // luckysheet is the container id
      showinfobar: false,
      lang: "zh",
      showtoolbarConfig: {
        postil: false, // 批注
        print: false, // 打印
        chart: false, // 图表
        pivotTable: false, // 数据透视表
        screenshot: false, // 截图
        function: false, // 公式
        frozenMode: false, // 冻结方式
        sortAndFilter: false, // 排序和筛选
        conditionalFormat: false, // 条件格式
        dataVerification: false, // 数据验证
        protection: false // 工作表保护
      },
      ...options,
      borderInfo: [
        {
          rangeType: "range",
          borderType: "border-none",
          style: "2",
          color: "#000",
          range: [
            {
              row: [3, 3],
              column: [3, 4]
            }
          ]
        }
      ]
    };
    this.screenshots = [];
  }

  // 加载 excel 文件
  async loadExcel(fileBlob: Blob, showGridLines: 0 | 1 = 1): Promise<any> {
    if (fileBlob instanceof Blob) {
      const fileBuffer: ArrayBuffer = await fileBlob.arrayBuffer();
      this._workbook = read(fileBuffer, { type: "buffer" });
      LuckyExcel.transformExcelToLucky(fileBlob, (luckyExcelJson: Recordable) => {
        if (luckyExcelJson.sheets === null || luckyExcelJson.sheets.length === 0) {
          console.error("Failed to read the content of the excel file", luckyExcelJson);
          return;
        }
        luckyExcelJson.sheets[0].showGridLines = showGridLines;
        this.destroyExcel();
        console.log("luckyExcelJson.sheets", luckyExcelJson.sheets);
        this.createExcel({
          ...this.LuckyExcelOptions,
          data: luckyExcelJson.sheets
        });
        LuckySheet.setRangeShow("A0", { show: false });
        // LuckySheet.setCellValueStyle({
        //   "row": [0, LuckySheet.getLastRow()], // 行范围，这里表示所有行
        //   "column": [0, LuckySheet.getLastColumn()] // 列范围，这里表示所有列
        // }, {
        //   "border-top": "",
        //   "border-right": "",
        //   "border-bottom": "",
        //   "border-left": ""
        // });
        return LuckySheet;
      });
    }
  }

  // 创建 excel
  createExcel(options: Recordable) {
    LuckySheet.create(options);
  }

  // 注销 excel
  destroyExcel(): void {
    LuckySheet.destroy();
  }

  // 获取当前工作表信息
  getCurrentSheet(): Recordable {
    return LuckySheet.getSheet();
  }

  // 根据工作表名称获取定义的名称
  _getNames(sheetName: string): IExcelEditorNames {
    const names: IExcelEditorNames = {};
    if (this._workbook) {
      const workbook: WorkBook = this._workbook;
      workbook.Workbook.Names.forEach((item: DefinedName) => {
        const defineName = item.Name;
        const keys = item.Ref.replaceAll("'", "").split(/!?\$/);
        if (keys && keys.length === 3 && sheetName === keys[0]) {
          names[defineName] = keys;
        }
      });
    } else {
      console.warn("[ExcelEditor warn]: not found workbook!");
    }
    return names;
  }

  // 获取工作表数据（默认为当前工作表）
  getDefineNameValues(sheetName?: string): IExcelEditorValue[] {
    if (!sheetName) {
      sheetName = this.getCurrentSheet().name;
    }
    const names: IExcelEditorNames = this._getNames(sheetName);
    const values: IExcelEditorValue[] = [];
    for (const defineName in names) {
      const [_sheetName, column, row] = names[defineName];
      const columnIndex: number = ExcelEditor.columeHeaderWordIndex[column];
      const rowIndex: number = Number(row) - 1;
      const sheetJson: Recordable = LuckySheet.getAllSheets().find(sheet => sheet.name === sheetName);
      const locatedCell: Recordable = sheetJson.data?.[rowIndex]?.[columnIndex];
      if (locatedCell?.mc?.c === columnIndex && locatedCell?.mc?.r === rowIndex) {
        values.push({
          defineName,
          column,
          columnIndex,
          row,
          rowIndex,
          cell: locatedCell,
          rawValue:
            locatedCell.v !== undefined
              ? locatedCell.v
              : locatedCell.ct?.s
              ? locatedCell.ct.s.map(textItem => textItem.v).join("")
              : "",
          formattedText: locatedCell.m || ""
        });
      } else if (!locatedCell.mc && columnIndex > -1 && rowIndex > -1) {
        values.push({
          defineName,
          column,
          columnIndex,
          row,
          rowIndex,
          cell: locatedCell,
          rawValue: locatedCell.v || "",
          formattedText: locatedCell.m || ""
        });
      }
    }
    return values;
  }

  // 设置工作表名称值
  setDefineNameValues(data: Recordable, sheetName?: string): void {
    if (!sheetName) {
      sheetName = this.getCurrentSheet().name;
    }
    const names: IExcelEditorNames = this._getNames(sheetName);
    for (const defineName in names) {
      const [_sheetName, column, row] = names[defineName];
      const columnIndex = ExcelEditor.columeHeaderWordIndex[column];
      const rowIndex = Number(row) - 1;
      if (data[defineName] !== undefined && data[defineName] !== "") {
        LuckySheet.setCellValue(rowIndex, columnIndex, data[defineName]);
      }
    }
  }

  // 获取当前工作表截图
  getScreenshots(): IExcelEditorScreenshot[] {
    this.screenshots = [];
    const currentSheet: Recordable = this.getCurrentSheet();
    if (currentSheet && currentSheet.images) {
      for (const imageKey in currentSheet.images) {
        const imageItem = currentSheet.images[imageKey];
        this.screenshots.push({ ...imageItem.default, src: imageItem.src });
      }
    }
    let maxRow = 0;
    let maxColumn = 0;
    for (let rowIndex = 0; rowIndex < currentSheet.data.length; rowIndex++) {
      const rowArr = currentSheet.data[rowIndex];
      if (rowArr[0] && rowArr[1] && rowArr[2]) {
        for (let itemIndex = 0; itemIndex < rowArr.length; itemIndex++) {
          if (!rowArr[itemIndex] && !rowArr[itemIndex + 1] && !rowArr[itemIndex + 2]) {
            if (itemIndex - 1 > maxColumn) {
              maxColumn = itemIndex - 1;
            }
            break;
          }
        }
      } else {
        maxRow = rowIndex - 1;
        break;
      }
    }
    LuckySheet.setRangeShow({ row: [0, maxRow], column: [0, maxColumn] });
    const screenshot: string = LuckySheet.getScreenshot();
    this.screenshots.unshift({ top: 0, left: 0, src: screenshot });
    return this.screenshots;
  }
}

export default ExcelEditor;
