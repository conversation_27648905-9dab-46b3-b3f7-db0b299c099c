import { defineStore } from "pinia";
import { IIOTStateGridOrderSync, IStateGridOrderSyncDetailList, IStateGridOrderSyncParams } from "@/models";
import * as api from "@/api/state-grid-order-sync";
import { PurchaseChannel, OrderSyncPriorityEnum, StateGridOrderSyncType } from "@/enums";

type StateGridOrderSyncListType = {
  iotSyncs: Array<IIOTStateGridOrderSync>;
  salesOrderId: string;
  channel: number;
};

/**
 * @description: 销售订单的 iot平台或者 csg平台的 同步列表
 */
export const useSalesStateGridOrderSyncIotListStore = defineStore({
  id: "sales-order-csg-iot-sync-list",
  state: (): StateGridOrderSyncListType => ({
    iotSyncs: [],
    salesOrderId: undefined,
    channel: undefined
  }),
  actions: {
    setSalesOrderId(id: string) {
      this.salesOrderId = id;
    },
    setChannel(channel: PurchaseChannel) {
      this.channel = channel;
    },
    async refreshSalesOrderSyncIotList() {
      this.iotSyncs = (await api.getCsgGuangzhouOrIotSalesOrderLineSyncList(this.salesOrderId, this.channel)).data;
    },
    updateStateGridOrderSyncDetail(id: string, detail: IStateGridOrderSyncDetailList[]) {
      const sync = this.iotSyncs.find(s => s.id === id);
      if (sync) {
        sync.detail = detail;
      }
    },
    async syncAll(orderLineId: string, salesOrderId?: string) {
      const channel = this.channel;
      const priority = OrderSyncPriorityEnum.COMMON_PRIORITY;
      const params: IStateGridOrderSyncParams = {
        orderId: salesOrderId ? salesOrderId : this.salesOrderId,
        orderItemId: orderLineId,
        dataType: StateGridOrderSyncType.ALL,
        channel,
        priority
      };
      return api.syncStateGridOrder(params);
    }
  }
});
