import { formatEnum } from "@/utils/format";
import { StateGridOrderSyncStep, StateGridOrderSyncType } from "@/enums";
import { IStateGridOrderSyncDetailList } from "@/models";
import { STATE_GRID_ORDER_SYNC_STEP } from "@/consts/state-grid-order-sync-step";

export type SyncStepType = "success" | "fail" | "info" | "syncing";

export interface ISyncStep {
  key: StateGridOrderSyncStep;
  title: string;
  type: SyncStepType;
  errorCount: number;
  successCount: number;
  totalCount: number;
}

export function formatSyncSteps(detail: IStateGridOrderSyncDetailList[]): Array<ISyncStep> {
  if (!detail) {
    return [];
  }
  /**  将生产过程自动采集项 放在 过程检验数据 后面*/
  return detail
    .map(item => {
      return {
        key: item.key,
        title: getTitle(item.key),
        type: calcStepIconType(item.totalCnt, item.successCnt, item.errCnt, item.syncingCnt),
        errorCount: item.errCnt,
        successCount: item.successCnt,
        totalCount: item.totalCnt,
        order: getOrder(item.key)
      };
    })
    .sort((a, b) => a.order - b.order);
}

function getTitle(value: StateGridOrderSyncStep) {
  return formatEnum(value, StateGridOrderSyncStep, "StateGridOrderSyncStep");
}

function getOrder(value: StateGridOrderSyncStep) {
  const step = STATE_GRID_ORDER_SYNC_STEP[value];
  return step ? step : Object.keys(STATE_GRID_ORDER_SYNC_STEP).length + 1;
}

/**
 * @description: 计算步骤图标类型
 */
function calcStepIconType(total: number, successCount: number, failCount: number, syncingCount: number): SyncStepType {
  // 如果有失败的就算作失败
  if (failCount) {
    return "fail";
  }

  // 成功且全部成功
  if (successCount && successCount >= total) {
    return "success";
  }

  // 如果有正在同步的，算作同步中
  if (syncingCount) {
    return "syncing";
  }

  return "info";
}

/**
 * @description: 是否是订单行数据的同步类型
 */
export function isOrderLineDataType(dataType: StateGridOrderSyncType) {
  if (
    dataType <= StateGridOrderSyncType.FINISHED_PRODUCT ||
    dataType === StateGridOrderSyncType.TECHNICAL_STANDARD ||
    dataType === StateGridOrderSyncType.PROCESS_DOCUMENT ||
    dataType === StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT
  ) {
    return true;
  }
  return false;
}
