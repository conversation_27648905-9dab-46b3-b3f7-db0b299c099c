<template>
  <div class="overflow-hidden w-full flex flex-col h-full">
    <div class="bg-bg_color flex justify-end">
      <AddEditStartWorkDialog :supervisionPlan="props.supervisionPlan" mode="add" @post-save-success="onQuery()">
        <template #trigger="{ openDialog }">
          <el-button class="mb-5" :icon="Plus" type="primary" @click="openDialog">新增开工信息</el-button>
        </template>
      </AddEditStartWorkDialog>
    </div>

    <div class="bg-bg_color flex flex-col flex-1 overflow-hidden relative">
      <PureTable
        class="flex-1 overflow-hidden pagination"
        row-key="id"
        :data="state.list"
        :columns="columns"
        :loading="loading"
        showOverflowTooltip
      >
        <template #isSync="{ row }">
          <CXTag :type="SyncStatusEnumMapColor[row.isSync]">{{ SyncStatusEnumMapDesc[row.isSync] }}</CXTag>
        </template>
        <template #processCode="{ row }">
          {{ ProcessEnumMapDesc[row.processCode] }}
        </template>
        <template #operation="data">
          <div>
            <AddEditStartWorkDialog
              :supervisionPlan="props.supervisionPlan"
              mode="edit"
              :id="data.row.id"
              @post-save-success="onQuery()"
            >
              <template #trigger="{ openDialog }">
                <el-button link type="primary" @click="openDialog">编辑</el-button>
              </template>
            </AddEditStartWorkDialog>
            <ElButton link type="danger" @click="onDelete(data.row.id)"> 删除 </ElButton>
          </div>
        </template>
        <template #empty>
          <CxEmptyData />
        </template>
      </PureTable>
    </div>
  </div>
</template>

<script setup lang="ts" name="start-work">
import { ref, reactive, watch } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import CxEmptyData from "@/components/CxEmpty";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { deleteStartWorkById, queryStartWorkList } from "@/api/south-grid-access";
import { IStartWork, ISupervisionPlan } from "@/models/south-grid-access";
import { ElButton, ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import AddEditStartWorkDialog from "./add-edit-start-work/dialog.vue";
import { ProcessEnumMapDesc } from "@/enums/south-grid-access";
import { SyncStatusEnumMapDesc, SyncStatusEnumMapColor } from "@/enums/south-grid-access";
import CXTag from "@/components/CxTag/index.vue";

const props = withDefaults(
  defineProps<{
    supervisionPlan: ISupervisionPlan;
  }>(),
  {}
);

const { columns } = useColumns();
const loading = ref(false);
const handleQueryStartWorkList = useLoadingFn(onQueryStartWorkList, loading);
const state = reactive<{
  list: Array<IStartWork>;
}>({
  list: []
});

watch(
  () => props.supervisionPlan?.id,
  () => {
    if (props.supervisionPlan?.id) {
      handleQueryStartWorkList();
    }
  },
  {
    immediate: true
  }
);

const onQuery = () => {
  handleQueryStartWorkList();
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteStartWorkById(id);
  ElMessage.success("删除成功");
  handleQueryStartWorkList();
};

async function onQueryStartWorkList() {
  const { data } = await queryStartWorkList({ supervisionPlanId: props.supervisionPlan?.id });
  state.list = data;
}
</script>
