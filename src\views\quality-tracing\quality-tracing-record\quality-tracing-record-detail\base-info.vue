<template>
  <div class="w-full bg-white flex" v-loading="loading">
    <el-descriptions class="px-6 flex-1">
      <template #title>
        <div class="flex items-center justify-between">
          <span class="text-lg font-bold">
            {{
              (isCableBySubClassCode(recordDetail.subClassCode) ? "生产订单" : "生产工单") + ` ${recordDetail.dataNo}`
            }}</span
          >
          <el-tag class="ml-4" size="large">{{ recordDetail.totalScore }} 分</el-tag>
        </div>
      </template>
      <el-descriptions-item label="物资种类">{{ recordDetail.subClassName }}</el-descriptions-item>
      <el-descriptions-item label="物料名称">
        {{ recordDetail.subClassName }}
      </el-descriptions-item>
      <el-descriptions-item label="质量规范">
        <router-link class="text-primary" :to="`/quality-specification/${recordDetail.qualityId}`">
          {{ recordDetail.qualityName }}
        </router-link>
      </el-descriptions-item>
      <el-descriptions-item label="原材料检权重">
        {{ formatWeight(recordDetail.rawMaterialWeight) }}
      </el-descriptions-item>
      <el-descriptions-item label="生产过程权重">
        {{ formatWeight(recordDetail.processWeight) }}
      </el-descriptions-item>
      <el-descriptions-item label="出厂试验权重">
        {{ formatWeight(recordDetail.experimentWeight) }}
      </el-descriptions-item>
      <el-descriptions-item label="工艺稳定性权重">
        {{ formatWeight(recordDetail.processStabilityWeight) }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间">
        {{ formatDate(recordDetail.calcTime, fullDateFormat) }}
      </el-descriptions-item>
    </el-descriptions>
    <!-- 右边区域 -->
    <div class="flex w-[236px] pr-6 items-center">
      <div class="flex gap-2" v-show="!loading">
        <gen-tracing-record-btn
          label="重新生成"
          disabled-link
          :id="recordDetail.dataId || ''"
          :quality-id="recordDetail.qualityId || ''"
          :sub-class-code="recordDetail.subClassCode || ''"
          @post-gen-success="postFillProductionDataClose"
        />
        <!-- 线缆 走订单路线 -->
        <fill-in-production-data
          v-if="recordDetail.subClassCode && isCableBySubClassCode(recordDetail.subClassCode)"
          :sale-order-id="calcSaleOrderId(recordDetail.soIds)"
          :production-id="recordDetail.dataId"
          @post-dialog-close="postFillProductionDataClose"
          mode="SALE"
        >
          <el-button type="primary">查看生产订单</el-button>
        </fill-in-production-data>
        <fill-in-production-data
          v-else-if="recordDetail.subClassCode"
          :sale-order-id="calcSaleOrderId(recordDetail.soIds)"
          :production-id="recordDetail.productionId"
          :work-order-id="recordDetail.dataId"
          @post-dialog-close="postFillProductionDataClose"
          mode="SALE"
        >
          <el-button type="primary">查看生产工单</el-button>
        </fill-in-production-data>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { formatDate } from "@/utils/format";
import { emptyDefaultValue, fullDateFormat } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getQualityTracingRecordDetail } from "@/api/quality-tracing";
import { QualityTracingRecordDetail } from "@/models/quality-tracing";
import FillInProductionData from "@/views/components/fill-in-production-data/index.vue";
import { useMaterial } from "@/utils/material";
import { emitter } from "@/utils/mitt";
import GenTracingRecordBtn from "../gen-tracing-record-btn.vue";
/**
 * 质量规范追溯记录详情- 基础信息条
 */

const props = defineProps<{
  /** 规范记录id */
  id: string;
}>();

const { isCableBySubClassCode } = useMaterial();
const recordDetail = ref<QualityTracingRecordDetail>({} as QualityTracingRecordDetail);

const loading = ref(false);

/**
 * @description: 格式化权重
 */
const formatWeight = (weight?: number | null) => {
  if (typeof weight !== "number") {
    return emptyDefaultValue;
  }
  return weight + "%";
};

/**
 * @description: 请求质量规范追溯记录详情
 */
const requestRecordDetail = useLoadingFn(async () => {
  const result = await getQualityTracingRecordDetail(props.id);
  recordDetail.value = result.data;
}, loading);

/**
 * @description: 计算销售订单ID
 */
function calcSaleOrderId(ids: string) {
  return ids.split(",")[0];
}

/**
 * @description: 关闭填报生产数据集回调
 */
function postFillProductionDataClose() {
  requestRecordDetail();
  emitter.emit("refreshQualityTracingRecordDetailCategoryList");
}

onMounted(requestRecordDetail);

defineExpose({
  recordDetail
});
</script>
<style lang="scss" scoped></style>
