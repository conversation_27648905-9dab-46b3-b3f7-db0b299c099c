import { defineStore } from "pinia";
import { getNonCableProdProcessInspecProcess } from "@/api/production-test-sensing/production-process-inspection";
import {
  delAcProductionProcessList,
  getAcDetialProductionProcessList,
  getAcProductionProcessList,
  saveAcProductionProcess
} from "@/api/production-test-sensing/armour-clamp/process-inspection";
import {
  IAddProductionProcess,
  IProductionMaterialProcess,
  IProductionProcessList,
  IResponse,
  ISearchProductionProcessList
} from "@/models";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import { useFillInDataStore } from "@/store/modules";

/** 金具生产过程工艺 */
export const useArmourClampProdProcessInspectStore = defineStore({
  id: "armour-clamp-prod-process-inspect-store",
  state: () => ({
    switchTabList: [] as Array<IProductionMaterialProcess>,
    currentTagInfo: {} as IProductionMaterialProcess,
    prodProcessInspectList: [] as Array<IProductionProcessList>,
    inspectListTotal: 0,
    detailOfProcessInspect: {} as IProductionProcessList
  }),
  getters: {
    /** 根据工序判断是否可以 新增/编辑 */
    getIsCanOperateAddAndEdit() {
      return !this.currentTagInfo?.autoCollect;
    }
  },
  actions: {
    /**
     * 获取金具生产工艺过程检测的过程
     */
    async getProdProcessInspecProcessAction(parmaData?: ISearchProductionProcessList) {
      const workOrderId = useFillInDataStore().dataId;
      this.switchTabList = await getNonCableProdProcessInspecProcess(workOrderId).then(res => res.data);
      this.getProdProcessInspectList(parmaData);
    },

    /**
     * 获取生产过程工艺列表
     */
    async getProdProcessInspectList(parmaData: ISearchProductionProcessList) {
      this.queryParams = {
        ...this.queryParams,
        ...parmaData
      };
      if (!this.queryParams.processCode) {
        const activeProcess = this.switchTabList[0];
        this.queryParams.processCode = activeProcess?.processCode;
        this.currentTagInfo = activeProcess;
      }
      this.queryParams.workOrderId = useFillInDataStore().dataId;
      this.prodProcessInspectList = [];
      const res = await getAcProductionProcessList(this.queryParams);
      if (Array.isArray(res.data?.list) && res.data?.list.length) {
        this.prodProcessInspectList = res.data.list.map(item => {
          item.productFinishTime = formatDate(item.productFinishTime, fullDateFormat);
          return item;
        });
        this.inspectListTotal = res.data.total;
      } else {
        this.prodProcessInspectList = [];
        this.inspectListTotal = 0;
      }
    },

    /**
     * 保存生产过程工艺检测
     */
    async saveAcProductionProcessCheck(saveData: IAddProductionProcess) {
      return await saveAcProductionProcess(saveData);
    },

    /**
     * 删除生产过程工艺检测
     */
    async delAcProductionProcessCheck(id: string): Promise<IResponse<number>> {
      return await delAcProductionProcessList(id);
    },

    /**
     * 获取生产过程工艺详情信息
     */
    async getAcDetailOfProdProcessInfo(id: string) {
      const detailRes = await getAcDetialProductionProcessList(id);
      if (detailRes.data?.id) {
        this.detailOfProcessInspect = detailRes.data || {};
      } else {
        this.detailOfProcessInspec = {};
      }
    },

    /**
     * 重置过程检测详情数据
     */
    initAcDetailOfProcessInspect() {
      this.detailOfProcessInspect = {};
    }
  }
});
