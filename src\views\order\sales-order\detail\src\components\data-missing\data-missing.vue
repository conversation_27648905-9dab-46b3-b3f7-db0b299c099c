<template>
  <div class="data-missing-contain" v-loading="loadingRef">
    <div class="item" v-if="dataMissing?.workOrder">
      <div class="cir" />
      <div class="info">
        <div class="title">工单</div>
        <div class="msg">{{ dataMissing.workOrder }}</div>
      </div>
    </div>
    <div class="item" v-if="dataMissing?.workReport?.length">
      <div class="cir" />
      <div class="info">
        <div class="title">报工</div>
        <div class="msg" v-for="(item, index) in dataMissing?.workReport" :key="index">
          <template v-for="(process, index) in item.processName" :key="index">
            <span>{{ process }}缺失;</span>
          </template>
        </div>
      </div>
    </div>
    <div class="item" v-if="dataMissing?.rawMaterial">
      <div class="cir" />
      <div class="info">
        <div class="title">原材料、组部件检测</div>
        <span class="msg" v-for="(item, index) in dataMissing?.rawMaterial?.processName" :key="index">
          {{ item }}缺失;
        </span>
      </div>
    </div>
    <div class="item" v-if="dataMissing?.processInfo">
      <div class="cir" />
      <div class="info">
        <div class="title">生产工艺及过程检验</div>
        <span class="msg" v-for="(item, index) in dataMissing?.processInfo?.processName" :key="index">
          {{ item }}缺失;
        </span>
      </div>
    </div>
    <div class="item" v-if="dataMissing?.outgoing">
      <div class="cir" />
      <div class="info">
        <div class="title">出厂试验</div>
        <span class="msg" v-for="(item, index) in dataMissing?.outgoing?.processName" :key="index">
          {{ item }}缺失;
        </span>
      </div>
    </div>
    <div class="item" v-if="dataMissing?.finProduction">
      <div class="cir" />
      <div class="info">
        <div class="title">成品入库</div>
        <div class="msg">{{ dataMissing.finProduction }}</div>
      </div>
    </div>
    <CxEmpty v-if="!dataMissing" />
  </div>
</template>

<script setup lang="ts">
import { IDataCheck } from "@/models";
import { computed, ref, watchEffect } from "vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";

const props = defineProps<{
  dataMissing?: IDataCheck;
}>();

const loadingRef = ref<boolean>(true);
const dataMissing = computed(() => props.dataMissing);

watchEffect(() => {
  loadingRef.value = !props.dataMissing;
});
</script>

<style scoped lang="scss">
.data-missing-contain {
  .item {
    display: flex;
    align-items: baseline;
    position: relative;
    padding-bottom: 20px;

    .cir {
      width: 14px;
      height: 14px;
      opacity: 1;
      background: #ffffff;
      box-sizing: border-box;
      border: 2px solid #f56c6c;
      border-radius: 100%;
      margin-right: 16px;
    }

    .info {
      flex: 1;

      .title {
        font-size: 16px;
        font-weight: normal;
        line-height: 24px;
        letter-spacing: 0;
        color: #303133;
        margin-bottom: 4px;
      }

      .msg {
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        color: #f56c6c;

        span {
          margin-right: 5px;
        }
      }
    }

    &::after {
      content: "";
      position: absolute;
      width: 2px;
      background-color: #e4e7ed;
      top: 18px;
      left: 6px;
      bottom: 0;
      border-radius: 8px;
    }

    &:last-child::after {
      content: "";
      display: none;
    }
  }
}
</style>
