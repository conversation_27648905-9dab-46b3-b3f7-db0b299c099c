<template>
  <BaseList :columns="columns" type="work-report" v-bind="$attrs" ref="baseList" />
  <EditWorkReportDialog ref="editDialog" />
</template>

<script setup lang="ts">
import { StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import BaseList from "../base-list.vue";
import { OperatorCell } from "@/components/TableCells";
import { provide, ref } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditWorkReportDialog from "@/views/components/state-grid-order-sync/dialogs/edit-work-report-dialog.vue";

const { enumFormatter, dateFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const editDialog = ref<InstanceType<typeof EditWorkReportDialog>>();

provide(stateGridOrderSyncEditKey, {
  refreshFn: pageInfo => baseList.value?.refresh(pageInfo)
});

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: "报工批次号",
    prop: "productionBatchNo",
    minWidth: TableWidth.order
  },
  {
    label: "设备",
    prop: "deviceName",
    minWidth: TableWidth.name
  },
  {
    label: "开始时间",
    prop: "workStartTime",
    width: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "结束时间",
    prop: "workEndTime",
    width: TableWidth.dateTime,
    formatter: dateFormatter()
  },
  {
    label: "报工地址",
    prop: "reportAddress",
    width: TableWidth.name
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    width: TableWidth.operation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => editDialog.value.openEditDialog(data.row),
          props: { type: "primary" }
        }
      ])
  }
];
</script>

<style scoped></style>
