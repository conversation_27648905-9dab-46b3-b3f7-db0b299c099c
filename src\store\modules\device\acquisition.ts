import { defineStore } from "pinia";
import { IDeviceAcquisition, IDeviceAcquisitionReq, IDeviceAcquisitionForm } from "@/models/device";
import * as api from "@/api/device/device-acquistion";

/** 设备采集点 */
export const useDeviceAcquisitionStore = defineStore({
  id: "cx-acquisition-store",
  state: () => ({
    acquisitions: [] as Array<IDeviceAcquisition>,
    total: 0,
    loading: false,
    deviceAcquisitionDetail: {} as IDeviceAcquisitionForm,
    deviceAcquisition: [] as Array<IDeviceAcquisition>
  }),
  actions: {
    /** 查询设备采集点 */
    async queryAcquisition(data?: IDeviceAcquisitionReq) {
      this.loading = true;
      const res = await api.queryDeviceAcquisition(data);
      this.loading = false;
      this.total = res.data.total;
      this.acquisitions = res.data.list;
    },
    /** 查询设备采集点--不分页 */
    async queryAcquisitionAll(deviceId: string) {
      this.deviceAcquisition = (await api.queryDeviceAcquisitionAll(deviceId)).data;
      return this.deviceAcquisition;
    },
    /** 新增设备采集点 */
    async createDeviceAcquisition(data: IDeviceAcquisitionForm) {
      return api.createDeviceAcquisition(data);
    },
    /** 编辑设备采集点 */
    async editDeviceAcquisition(data: IDeviceAcquisitionForm) {
      return api.editDeviceAcquisition(data);
    },
    /** 删除设备采集点 */
    async deleteDeviceAcquisition(id: string) {
      return api.deleteDeviceAcquisition(id);
    },
    // 设备采集点编辑赋值
    setDeviceAcquisitionStorage(acquisitionDetail: IDeviceAcquisitionForm) {
      this.deviceAcquisitionDetail = acquisitionDetail;
    },
    // 设备采集点编辑赋值清空
    clearDeviceAcquisitionStorage() {
      this.deviceAcquisitionDetail = {};
    }
  }
});
