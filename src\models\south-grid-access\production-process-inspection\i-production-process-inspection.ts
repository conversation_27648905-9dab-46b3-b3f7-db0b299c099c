import { IBase } from "@/models";
import { ICollectionItem } from "../material";

export interface IProductionProcessInspection extends IBase {
  /**
   * 检验批次号
   */
  inspectBatchNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 检验日期
   */
  inspectDate?: string;
  /**
   * 工序编码
   */
  procedureCode?: string;

  /** 检验数据列表 */
  inspectDataList?: Array<ICollectionItem>;
}
