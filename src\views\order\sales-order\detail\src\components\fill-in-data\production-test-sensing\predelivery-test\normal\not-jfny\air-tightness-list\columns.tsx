import { TableWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "检查项目",
      prop: "targetName"
    },
    {
      label: "检测值",
      prop: "targetValueLabel",
      className: "valid-waring",
      slot: "targetValueLabel"
    },
    {
      label: "单位",
      prop: "unit",
      minWidth: TableWidth.unit
    },
    {
      label: "数据格式要求",
      prop: "datumOrganization",
      minWidth: TableWidth.type,
      className: "valid-waring"
    }
  ];

  return {
    columns
  };
}
