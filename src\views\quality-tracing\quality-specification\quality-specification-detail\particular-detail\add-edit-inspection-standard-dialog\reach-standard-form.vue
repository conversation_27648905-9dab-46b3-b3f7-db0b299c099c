<template>
  <div class="pb-3">
    <form-title-bar title="数值达标范围" />
    <el-row class="pl-3">
      <el-col :span="15">
        <input-formual :model-value="formual" @update:model-value="updateModelValue" />
      </el-col>
    </el-row>
    <div v-if="showEmpty" class="text-danger">达标数值范围存在空缺</div>
    <div v-if="showErrorComparision" class="text-danger">达标数值范围存在比较关系错误</div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import FormTitleBar from "./form-title-bar.vue";
import InputFormual from "@/components/input-formual/index.vue";
import { ComparisonSymbolEnum } from "@/components/input-formual/types";

const formual = reactive({
  leftValue: "",
  leftSymbol: ComparisonSymbolEnum.LessThanValue as number,
  midValue: "x",
  rightSymbol: ComparisonSymbolEnum.LessThanValue as number,
  rightValue: ""
});

const showErrorComparision = ref(false);
const showEmpty = ref(false);

function updateModelValue(v: any) {
  Object.assign(formual, v);
}

/**
 * @description: 检查公式比较关系
 */
function handleCheckFormulaComparision() {
  showErrorComparision.value = false;
  // 当左右值都存在时，检查比较关系
  if (formual.leftValue && formual.rightValue) {
    const isError =
      (formual.leftSymbol === ComparisonSymbolEnum.LessThanValue ||
        formual.rightSymbol === ComparisonSymbolEnum.LessThanValue) &&
      +formual.leftValue >= +formual.rightValue;
    if (isError) {
      showErrorComparision.value = true;
    }
    return !isError;
  }
  return true;
}
/**
 * @description: 校验空缺公式
 */
function handleCheckEmpytFormula() {
  showEmpty.value = false;
  const result = Boolean((formual.leftValue || formual.rightValue) && formual.midValue);

  if (!result) {
    showEmpty.value = true;
  }
  return result;
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  const res1 = handleCheckFormulaComparision();
  const res2 = handleCheckEmpytFormula();
  return res1 && res2;
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: any) {
  Object.assign(formual, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formual);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  resetForm: () => {
    Object.assign(formual, {
      leftValue: "",
      leftSymbol: ComparisonSymbolEnum.LessThanValue as number,
      midValue: "x",
      rightSymbol: ComparisonSymbolEnum.LessThanValue as number,
      rightValue: ""
    });
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-input__inner) {
  text-align: center;
}
</style>
