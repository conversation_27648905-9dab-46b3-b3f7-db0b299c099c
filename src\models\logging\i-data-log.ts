import { IPagingReq, ISortReq } from "../i-paging-req";

/**  查询日志 */
export interface ISearchDataLog extends IPagingReq, ISortReq {
  /** 对象表 */
  tableConfigId?: string;
  /** 数据Id */
  dataId?: string;
  /** 操作人 */
  operator?: string;
  /** 变更时间 */
  changeTime?: string[];
  /** 更新类型 */
  changeType?: number;
  /** 操作人 */
  content?: string;
}

export interface IDataBaseList {
  id: string;
  /** 表名称 */
  tableName: string;
  /** 表名 */
  tableExplain: string;
}

/** 数据日志列表 */
export interface IDataLogList {
  id: string;
  /** 对象表 */
  objectTable: string;
  /** 对象表Id */
  tableConfigId: string;
  /** 数据Id */
  dataId: string | number;
  /** 变更时间 */
  changeTime: string;
  /** 更新类型 */
  changeType: number;
  /** 值 */
  valueDeclaration: string;
  /** 原数据 */
  oldData: string;
  /** 数据记录 */
  recordData: string;
  /** 操作人 */
  operator: string;
}

export interface IDataDetailList {
  /** 字段名 */
  updateFiled: string;
  /** 原值 */
  oldValue?: string;
  /** 新值 */
  newValue?: string;
  /** 跟新类型 */
  changeType?: number;
  /** 增加和删除的字段值 */
  value?: string;
  /** 排序 */
  sort?: number;
}
