import { IPagingReq } from "../i-paging-req";
import { IDictionaryOption } from "../platform";

/** 查询条件 */
export interface ISearchFinisedProduct extends IPagingReq {
  categoryCode?: string;
  subclassCode?: string;
  putStorageTime?: string[];
  orFilters?: any[];
  hideZero?: boolean;
}

export interface ISearchPurchaseOrderReq extends IPagingReq {
  keyword?: string;
  subClassCode?: string;
}

/** 采购订单信息 */
export interface IPurchaseOrderInfo {
  id?: string;
  materialCode?: string;
  materialDesc?: string;
  poNo?: string;
  poItemNo?: string;
  prjName?: string;
  buyerName?: string;
  subClassName?: string;
}

/** 国网采购订单 */
export type IPurchaseOrderList = IPurchaseOrderInfo;

export type IPurchaseOrderFormReq = IPurchaseOrderList;

/** 产成品库存数量 */
export interface IFinishedProduct {
  id?: string;
  categoryCode: string; // 品类编码
  categoryName: string; // 品类名称
  subclassCode: string; // 物资种类编码
  subclassName: string; // 物资种类名称
  productName?: string; // 产成品名称
  productAmount: number; // 产成品库存数量
  productUnit: string; // 计量单位
  eipMatCode: string; // 物料编码
  eipMatDes: string; // 物资描述
  speModel: string; // 规格型号
  voltageLevel: string; // 电压等级
  storeCity: string; // 存放地点所在市
  putStorageTime?: string; // 入库时间
  materialId?: string; // 物料Id
  purchaseOrderLineId?: string; // 采购订单行Id
  poNo?: string; // 采购订单号
  purchaseOrderId?: string; // 采购订单号
  poItemNo?: string; // 采购订单行项目号
  lastUpdateTime?: string; // 最后更新时间
  lastSyncTime?: string; // 最后同步时间
  syncResult?: string; // 同步状态
  reason?: string; // 可能原因
  line?: IPurchaseOrderInfo; // 采购订单信息
  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;
  /** 产品计量单位名称 */
  productUnitName?: string;
}

export type IFinisedProductReq = IFinishedProduct;
