import { fullDateFormat } from "@/consts";
import { TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { TableColumnRenderer } from "@pureadmin/table";
import CxTag from "@/components/CxTag/index.vue";
import { ITenant } from "@/models";

export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "企业名称",
      prop: "comName",
      width: TableWidth.name
    },
    {
      label: "企业简称",
      prop: "name",
      width: TableWidth.name
    },
    {
      label: "租户编号",
      prop: "tenantKey",
      width: TableWidth.name
    },
    {
      label: "物资种类",
      prop: "subclassName",
      width: TableWidth.name,
      formatter: (row: ITenant) => {
        return row.subclassName?.concat(row.iotSubclassName).join(",");
      }
    },
    {
      label: "状态",
      prop: "status",
      width: TableWidth.type,
      cellRenderer(data: TableColumnRenderer) {
        const status: boolean = data.row.status;
        return status ? <CxTag type="danger">禁用</CxTag> : <CxTag type="success">启用</CxTag>;
      }
      // formatter: (row: ITenant) => formatEnum(row.status, StatusEnum, "StatusEnum")
    },
    {
      label: "供应商编码",
      prop: "supplierCode",
      minWidth: TableWidth.type
    },
    {
      label: "联系人姓名",
      prop: "contactName",
      width: TableWidth.name
    },
    {
      label: "联系人电话",
      prop: "contactMobile",
      width: TableWidth.type
    },
    {
      label: "联系人邮箱",
      prop: "contactEmail",
      width: TableWidth.name
    },

    {
      label: "创建时间",
      prop: "createTime",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.largeXgOperation
    }
  ];
  return { columns };
}
