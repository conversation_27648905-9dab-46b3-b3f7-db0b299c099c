import { ColumnWidth } from "@/enums";

/**
 * @description: 生成质量规范表格配置
 */
export function genQualitySpecificationTableColumnsConfig() {
  const columnsConfig: TableColumnList = [
    {
      label: "选择",
      prop: "selection",
      type: "selection",
      fixed: "left",
      reserveSelection: true,
      width: ColumnWidth.Char1,
      headerRenderer: () => {
        return <div>选择</div>;
      }
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "物料编号",
      prop: "materialCode",
      width: ColumnWidth.Char5
    },
    {
      label: "物料单位",
      prop: "materialUnitName",
      width: ColumnWidth.Char5
    },
    {
      label: "物料描述",
      prop: "materialDescribe",
      minWidth: ColumnWidth.Char7
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: ColumnWidth.Char7
    },
    {
      label: "规格型号",
      prop: "specificationModel",
      minWidth: ColumnWidth.Char5
    },
    {
      label: "电压等级",
      prop: "voltageClass",
      minWidth: ColumnWidth.Char4
    }
  ];

  return { columnsConfig };
}
