<template>
  <div class="flex justify-start items-center flex-1 overflow-hidden mr-2">
    <div>
      <div class="label">物资种类</div>
      <div class="flex-bc text-middle">
        <span>{{ EquipmentTypeEnumMapDesc[detailInfo?.equipmentCode] || "--" }}</span>
      </div>
    </div>
    <el-divider direction="vertical" />
    <div>
      <div class="label">设备名称</div>
      <div class="flex-bc text-middle">
        <ShowTooltip
          className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl"
          :content="SpeciTypeEnum[detailInfo?.equipmentName]"
        />
      </div>
    </div>
    <el-divider direction="vertical" />
    <div>
      <div class="label">电压等级</div>
      <div class="flex-bc text-middle">
        <ShowTooltip
          className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl"
          :content="VoltageListEnum[detailInfo?.voltageLevel]"
        />
      </div>
    </div>
    <el-divider direction="vertical" />
    <div>
      <div class="label">厂内生产工号</div>
      <div class="flex-bc text-middle">
        <ShowTooltip className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl" :content="detailInfo?.factoryWorkNumber" />
      </div>
    </div>
    <el-divider direction="vertical" />
    <div>
      <div class="label">实物ID</div>
      <div class="flex-bc text-middle">
        <ShowTooltip className="md:max-w-[15em] lg:max-w-[20em] xl:max-w-xl" :content="detailInfo?.utcNum" />
      </div>
    </div>
    <el-divider direction="vertical" />
    <div>
      <div class="label">主数据同步状态</div>
      <div class="flex-bc text-middle">
        <el-tag
          :type="PullStatusEnumTagType[detailInfo?.pullStatus] || 'info'"
          class="cursor-pointer"
          @click="handleShowDetail"
          >{{ PullStatusEnumMapDesc[detailInfo?.pullStatus] || "--" }}</el-tag
        >
      </div>
    </div>
    <!-- 同步明细 -->
    <SynchronousDetails :dialog-show="dialogShow" :batch-id="detailInfo.batchId" @close="dialogShow = false" />
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from "vue";
import {
  SpeciTypeEnum,
  VoltageListEnum,
  EquipmentTypeEnumMapDesc,
  PullStatusEnumMapDesc,
  PullStatusEnumTagType
} from "@/enums";
import ShowTooltip from "@/components/ShowTooltip";
import { EProjectModel } from "@/models";
import SynchronousDetails from "../synchronous-details/index.vue";

const detailInfo = reactive({} as EProjectModel);

const props = withDefaults(
  defineProps<{
    detail: EProjectModel; // 详情数据
  }>(),
  {
    detail: () => {
      return {} as EProjectModel;
    }
  }
);

const dialogShow = ref(false);

watch(
  () => props.detail,
  newVal => {
    Object.assign(detailInfo, newVal);
  },
  { immediate: true }
);

/**
 * @description: 同步明细
 */

const handleShowDetail = () => {
  dialogShow.value = true;
};
</script>

<style scoped lang="scss">
.subclass-icons {
  @apply flex-bc gap-1 pl-2.5 pr-4 py-2;
  border-radius: 3px;
  border: 1px solid var(--el-color-primary-light-8);
  background: var(--el-color-primary-light-9);

  .value {
    @apply text-primary font-medium;
  }
}

.label {
  @apply text-base text-secondary mb-2;
}

.iconfont {
  @apply mr-2 text-primary_light_5 text-xxl leading-none;
}

.el-divider {
  @apply h-10 mx-6;
}
</style>
