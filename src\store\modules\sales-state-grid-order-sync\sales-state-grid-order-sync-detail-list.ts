/**
 * 销售订单。同步详情列表
 */

import { defineStore } from "pinia";
import {
  ISyncSearchParams,
  IStateGridOrderSyncParams,
  ISyncCommon,
  ISyncQueryParams,
  SyncListType,
  ISyncListQueryParams
} from "@/models";
import {
  PurchaseChannel,
  StateGridOrderSyncDataFormatEnum,
  OrderSyncPriorityEnum,
  StateGridOrderSyncType
} from "@/enums";
import { useSalesOrderDetailStore, useSalesOrderSyncInfo } from "@/store/modules";
import * as api from "@/api/state-grid-order-sync";
import { TrackPointKey } from "@/consts";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { useTableConfig } from "@/utils/useTableConfig";
import { PaginationProps } from "@pureadmin/table";

type StateGridOrderSyncDetailType = {
  type: StateGridOrderSyncType;
  syncActionName: string;
  loading: boolean;
  data: Array<ISyncCommon>;
  syncActionTrackPointKey: string;
  pagination: PaginationProps;
  searchParams?: ISyncSearchParams;
};

export const useSalesStateGridOrderSyncDetailListStore = defineStore("sales-order-sync-detail-dialog", {
  state: (): StateGridOrderSyncDetailType => ({
    type: undefined,
    syncActionName: "",
    loading: false,
    data: [],
    syncActionTrackPointKey: "",
    pagination: useTableConfig().pagination
  }),
  getters: {
    queryParams(): ISyncQueryParams {
      const { currentPage, pageSize } = this.pagination;
      const detailStore = useSalesOrderSyncInfo();
      return { ...this.searchParams, pageNo: currentPage, pageSize, channel: detailStore.channel };
    },
    getStateGridOrderSyncParams() {
      const { activeDetailType, sync, purchaseOrderId } = useSalesOrderSyncInfo();
      const isShangHaiIotGridOrder = activeDetailType === SyncOrderTabEnum.SYNC_SHANGHAI_IOT;
      const isGuangZhouCsgOrder = activeDetailType === SyncOrderTabEnum.SYNC_CSG_GUANGZHOU;
      let orderId = null;
      let orderItemId = null;
      let channel = null;
      if (isShangHaiIotGridOrder || isGuangZhouCsgOrder) {
        const { id } = useSalesOrderDetailStore().salesOrder;
        orderId = id;
        orderItemId = sync.id;
        channel = isGuangZhouCsgOrder ? PurchaseChannel.CSG_GuangZhou : PurchaseChannel.IOT;
      } else {
        orderId = purchaseOrderId;
        orderItemId = sync.id;
        channel = PurchaseChannel.EIP;
      }
      return {
        orderId,
        orderItemId,
        channel
      };
    }
  },
  actions: {
    updatePagination(pagination: Partial<PaginationProps>) {
      Object.assign(this.pagination, pagination);
    },
    resetPagination(pagination: PaginationProps = useTableConfig().pagination) {
      this.pagination = pagination;
    },
    updateSearchParams(params: Partial<ISyncSearchParams>) {
      Object.assign(this.searchParams, params);
    },
    resetSearchParams(params: ISyncSearchParams = {}) {
      this.searchParams = params;
    },
    setType(type: StateGridOrderSyncType) {
      this.type = type;
      this.data = [];
      this.syncActionName = {
        [StateGridOrderSyncType.SALE_ORDER_ITEM]: "同步销售订单明细",
        [StateGridOrderSyncType.PRODUCTION_PLAN]: "同步排产计划",
        [StateGridOrderSyncType.PRODUCTION_ORDER]: "同步生产订单",
        [StateGridOrderSyncType.WORK_ORDER]: "同步工单",
        [StateGridOrderSyncType.REPORT_WORK]: "同步报工",
        [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: "同步原材料",
        [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: "同步过程检",
        [StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT]: "同步自动采集项",
        [StateGridOrderSyncType.EXPERIMENT]: "同步试验",
        [StateGridOrderSyncType.FINISHED_PRODUCT]: "同步成品信息",
        [StateGridOrderSyncType.TECHNICAL_STANDARD]: "同步技术标准",
        [StateGridOrderSyncType.PROCESS_DOCUMENT]: "同步工序文档"
      }[type];
      this.syncActionTrackPointKey = {
        [StateGridOrderSyncType.SALE_ORDER_ITEM]: TrackPointKey.FORM_PURCHASE_SYNC_SALE_BATCH,
        [StateGridOrderSyncType.PRODUCTION_PLAN]: TrackPointKey.FORM_PURCHASE_SYNC_PLAN_BATCH,
        [StateGridOrderSyncType.PRODUCTION_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PO_BATCH,
        [StateGridOrderSyncType.WORK_ORDER]: TrackPointKey.FORM_PURCHASE_SYNC_PWO_BATCH,
        [StateGridOrderSyncType.REPORT_WORK]: TrackPointKey.FORM_PURCHASE_SYNC_WO_BATCH,
        [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: TrackPointKey.FORM_PURCHASE_SYNC_RAWMATERIAL_BATCH,
        [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_BATCH,
        [StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT]: TrackPointKey.FORM_PURCHASE_SYNC_P_ING_BATCH,
        [StateGridOrderSyncType.EXPERIMENT]: TrackPointKey.FORM_PURCHASE_SYNC_EXP_BATCH,
        [StateGridOrderSyncType.FINISHED_PRODUCT]: TrackPointKey.FORM_PURCHASE_SYNC_PRO_BATCH
      }[type];
      this.resetPagination();
      this.resetSearchParams();
    },
    refresh(silence = false) {
      const activeDetailType = useSalesOrderSyncInfo().activeDetailType;
      if (activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
        useSalesOrderSyncInfo().refreshSyncDetail();
      } else {
        useSalesOrderSyncInfo().getIotOrCsgGuangzhouSalesOrderLineDetail();
      }
      silence ? this.refreshSyncListSilence() : this.refreshSyncList();
    },
    async refreshSyncList() {
      this.loading = true;
      const activeDetailType = useSalesOrderSyncInfo().activeDetailType;
      if (activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
        this.getSyncListByType()
          .then(res => {
            this.data = res.data.list || [];
            this.pagination.total = res.data.total || 0;
          })
          .finally(() => (this.loading = false));
      } else {
        this.getShangHaiIotSyncListBySales().finally(() => (this.loading = false));
      }
    },
    async refreshSyncListSilence() {
      const activeDetailType = useSalesOrderSyncInfo().activeDetailType;
      if (activeDetailType === SyncOrderTabEnum.SYNC_STATE__GRID_ORDER) {
        const res = (await this.getSyncListByType()).data;
        this.data = res.list || [];
        this.pagination.total = res.total || 0;
      } else {
        this.getShangHaiIotSyncListBySales();
      }
    },
    getSyncListByType<T extends ISyncCommon>() {
      const purchaseLineId: string = useSalesOrderSyncInfo().sync.id;
      const type: SyncListType = {
        [StateGridOrderSyncType.SALE_ORDER_ITEM]: "sales",
        [StateGridOrderSyncType.PRODUCTION_PLAN]: "production-plan",
        [StateGridOrderSyncType.PRODUCTION_ORDER]: "production",
        [StateGridOrderSyncType.WORK_ORDER]: "work-order",
        [StateGridOrderSyncType.REPORT_WORK]: "work-report",
        [StateGridOrderSyncType.RAW_MATERIAL_INSPECTION]: "raw-material",
        [StateGridOrderSyncType.PRODUCTION_TECHNOLOGY]: "production-flow",
        [StateGridOrderSyncType.PRODUCTION_AUTO_COLLECT]: "process-data",
        [StateGridOrderSyncType.EXPERIMENT]: "experiment",
        [StateGridOrderSyncType.FINISHED_PRODUCT]: "finished-production",
        [StateGridOrderSyncType.TECHNICAL_STANDARD]: "technical-standard",
        [StateGridOrderSyncType.PROCESS_DOCUMENT]: "process-document"
      }[this.type];
      const params: ISyncListQueryParams = {
        type,
        cancelLast: true,
        data: {
          purchaseLineId,
          ...this.queryParams
        }
      };
      return api.getSyncList<T>(params);
    },
    /** 销售订单--上海平台--详情数据列表 */
    async getShangHaiIotSyncListBySales() {
      const syncDetailStore = useSalesOrderSyncInfo();
      const params = {
        ...this.queryParams,
        saleLineId: syncDetailStore.salesLineId,
        syncProcess: syncDetailStore.activeStepKey
      };
      const res = (await api.getCsgGuangzhouOrIotSyncDetailList(params)).data;
      this.data = res.list || [];
      this.pagination.total = res.total || 0;
    },
    syncStateGridOrder(dataId?: string, dataFormat?: StateGridOrderSyncDataFormatEnum) {
      const { activeDetailType } = useSalesOrderSyncInfo();
      const isShangHaiIotGridOrder = activeDetailType === SyncOrderTabEnum.SYNC_SHANGHAI_IOT;
      const priority = OrderSyncPriorityEnum.COMMON_PRIORITY;
      const type = isShangHaiIotGridOrder
        ? dataFormat === StateGridOrderSyncDataFormatEnum.FILE
          ? StateGridOrderSyncType.SYNC_FILE
          : this.type
        : this.type;
      const params: IStateGridOrderSyncParams = {
        dataType: type,
        dataId,
        dataFormat,
        priority,
        ...this.getStateGridOrderSyncParams
      };
      return api.syncStateGridOrder(params);
    }
  }
});
