<template>
  <div class="mt-2.5">
    <TitleBar class="my-2.5" title="物联数据" v-if="hasBusinessItem" />

    <div class="my-2.5 flex items-start flex-wrap" v-if="showOrderNoFilter">
      <label class="mr-5">{{ radioLabel }}:</label>
      <el-radio-group class="flex-1" size="small" v-model="syncAuditStore.activeNo">
        <el-radio border class="bg-bg_color mb-2" label="">全部</el-radio>
        <el-radio border class="bg-bg_color mb-2" v-for="no in noes" :key="no" :label="no">{{ no }}</el-radio>
      </el-radio-group>
    </div>

    <div class="flex-bc my-2.5" v-if="!hasBusinessItem">
      <div class="flex items-center flex-1">
        <el-icon class="warn-icon">
          <WarnTriangleFilled />
        </el-icon>
        <p class="w-1/2 mr-2">
          同步实际结束日期会触发EIP的质量评分，请先到EIP检查订单下的原材料、过程检、试验数据已完整且全部显示到EIP后再同步实际结束日期
        </p>
        <a class="text-primary hover:underline" @click="eipGuideCtx.visible = true"> 点击查看EIP检查数据流程 </a>
      </div>
      <CardCollapse />
    </div>

    <Card class="card" v-for="item in items" :key="item.id" :title="item.title" :id="item.id" v-model="item.collapsed">
      <component :is="item.component" :cardId="item.id" />
    </Card>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import Card from "../card.vue";
import CardCollapse from "../card-collapse.vue";
import { computed, inject, onMounted, ref } from "vue";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { eipGuideKey } from "@/views/components/state-grid-trigger-score/tokens";
import { WarnTriangleFilled } from "@element-plus/icons-vue";
import { ElIcon } from "element-plus";
import { syncAuditStateKey } from "../tokens";
import { computedAsync } from "@vueuse/core";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

const syncAuditStore = useStateGridSyncAuditStore();
const keyWordAliasHook = usekeyWordAliasHook();
const ctx = inject(syncAuditStateKey);
const eipGuideCtx = inject(eipGuideKey, undefined);

const noes = ref<Array<string>>([]);

const hasBusinessItem = computed(() => ctx.hasBusinessItem);
const items = computed(() => ctx.iotItems);
const radioLabel = computedAsync(() => {
  if (syncAuditStore.isCable) {
    return keyWordAliasHook.getAliasByCode(KeywordAliasEnum.IPO_NO, KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]);
  }
  return "工单号";
});
const showOrderNoFilter = computed(() => ctx.allowFilterByOrderNo && noes.value.length > 1);

onMounted(async () => {
  noes.value = await syncAuditStore.getProductionWorkOrderOptions();
});
</script>

<style scoped>
.warn-icon {
  @apply mr-2.5;
  font-size: 30px;
  color: var(--el-color-warning);
}

.card + .card {
  @apply mt-5;
}
</style>
