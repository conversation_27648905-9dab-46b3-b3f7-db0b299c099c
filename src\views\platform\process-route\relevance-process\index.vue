<template>
  <div class="flex flex-col h-full overflow-hidden test-container">
    <TitleBar title="关联工序" />
    <div class="flex justify-end pb-4">
      <ElButton size="large" type="primary" :icon="Plus" @click="onAddRelevanceProcess()"> 新增关联工序 </ElButton>
    </div>
    <PureTable
      class="flex-1 overflow-hidden"
      row-key="id"
      size="large"
      :data="processStore.boundProcess"
      :columns="columns"
      showOverflowTooltip
      :loading="state.loading"
    >
      <template #operation="data">
        <div>
          <ElButton link type="danger" @click="onRemoveProcess(data.row.processId)"> 移除 </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
    <!-- 关联工序 -->
    <el-dialog
      v-model="state.relevanceProcessVisible"
      title="关联工序"
      class="middle"
      align-center
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="onCloseRelevanceProcess()"
    >
      <RelevanceProcess @onSelectProcessIds="handleSelectProcessIds($event)" />
      <template #footer>
        <el-button @click="onCloseRelevanceProcess()">取消</el-button>
        <el-button
          type="primary"
          :loading="saveLoading"
          :disabled="selectedProcessIds.length === 0"
          @click="onSaveRelevanceProcess()"
          >保存</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useProcessStore, useProcessRouteStore } from "@/store/modules";
import TitleBar from "@/components/TitleBar";
import RelevanceProcess from "./relevance-process.vue";
import { useConfirm } from "@/utils/useConfirm";
import { ElMessage } from "element-plus";

const { columns } = useColumns();
const processStore = useProcessStore();
const processRouteStore = useProcessRouteStore();

const saveLoading = ref<boolean>(false);
const selectedProcessIds = ref<Array<string>>([]);

const state = reactive<{
  relevanceProcessVisible: boolean;
  loading: boolean;
}>({
  relevanceProcessVisible: false,
  loading: false
});

watchEffect(() => {
  if (processRouteStore.currentProcessDetail) {
    if (processRouteStore.currentProcessDetail.id) {
      queryBoundProcessPage();
    }
  } else {
    processStore.boundProcess = [];
  }
});

const onAddRelevanceProcess = () => {
  state.relevanceProcessVisible = true;
  selectedProcessIds.value = [];
};

const onCloseRelevanceProcess = () => {
  state.relevanceProcessVisible = false;
};

const handleSelectProcessIds = (processIds: Array<string>) => {
  selectedProcessIds.value = processIds;
};
async function onSaveRelevanceProcess() {
  if (!Array.isArray(selectedProcessIds.value) || selectedProcessIds.value.length === 0) {
    ElMessage.warning("请选择关联工序");
    return;
  }

  await processStore.bindProcess({
    processRouteId: processRouteStore.currentProcessDetail.id,
    processIdList: selectedProcessIds.value
  });
  state.relevanceProcessVisible = false;
  ElMessage.success("关联成功");
  queryBoundProcessPage();
}

const onRemoveProcess = async (id: string) => {
  if (!(await useConfirm("确认移除当前工序", "确认移除"))) {
    return;
  }
  await processStore.unbindProcess({ processRouteId: processRouteStore.currentProcessDetail.id, processId: id });
  ElMessage.success("移除成功");
  queryBoundProcessPage();
};

async function queryBoundProcessPage() {
  state.loading = true;
  await processStore.queryBoundProcessPage({
    processRouteId: processRouteStore.currentProcessDetail.id,
    pageNo: 1,
    pageSize: 100
  });
  state.loading = false;
}
</script>
<style scoped lang="scss"></style>
