import {
  delCollectionItemsById,
  getCollectionConfigInfoByProductSteProcessId,
  getDetailOfCollectionItemsById,
  getIdentityList,
  getProcessInfoByProductProcess,
  getProductProcessByCategory,
  saveAddCollectionItems
} from "@/api/collection-items";
import { EControlType } from "@/enums";
import { IResponse } from "@/models";
import {
  ICollectionFormIdenitifyType,
  ICollectionIdenitifyList,
  IProcessInfoList,
  IProductProcessList,
  ISaveCollectionItemsReq
} from "@/models/collection-items";
import { defineStore } from "pinia";

export const useCollectionItemsStore = defineStore({
  id: "collection-items-store",
  state: () => ({
    productProcessList: [] as Array<IProductProcessList>,
    processInfoList: [] as Array<IProcessInfoList>,
    collectionLoading: false,
    collectionItemsTableList: [] as Array<ICollectionIdenitifyList>,
    tableTotal: 0,
    identifyTypeList: [] as Array<ICollectionFormIdenitifyType>,
    detailOfCollectionItems: {} as ICollectionIdenitifyList
  }),
  actions: {
    /**
     * 获取品类下的生产阶段
     */
    async getProductProcessByCategory(subClassCode: string) {
      const resData = (await getProductProcessByCategory(subClassCode)).data;
      this.productProcessList = resData?.length ? resData : [];
    },

    /**
     * 获取生产阶段ID下的工序信息
     */
    async getProcessInfoByProductProcessId(id: string) {
      const resData = (await getProcessInfoByProductProcess(id)).data;
      this.processInfoList = resData?.length
        ? resData.map(item => {
            const { processCode, processName, produceStageId, id } = item;
            item["processCode"] = processCode;
            item["processName"] = processName;
            item["processId"] = id;
            item["produceStageId"] = produceStageId;
            return item;
          })
        : [];
    },

    /** 通过工序Code获取采集项信息 */
    async getCollectionItemsDataActions(process: IProcessInfoList) {
      this.collectionLoading = true;
      const res = await getCollectionConfigInfoByProductSteProcessId(process.id).finally(() => {
        this.collectionLoading = false;
      });
      if (Array.isArray(res.data) && res.data?.length) {
        this.collectionItemsTableList = res.data.map((item: ICollectionIdenitifyList) => {
          item.code = process.processCode;
          item.name = process.processName;
          return item;
        });
        this.tableTotal = res.data.length;
      } else {
        this.collectionItemsTableList = [];
        this.tableTotal = 0;
      }
    },

    /**
     * 获取表单控件的下拉列表
     */
    async getCollectionIdentifyList() {
      const res = await getIdentityList();
      if (Array.isArray(res.data) && res.data?.length) {
        this.identifyTypeList = res.data;
      } else {
        this.identifyTypeList = [];
      }
    },

    /**
     * 保存新增的采集项信息
     */
    async saveCollectionItemsActions(paramsData: ISaveCollectionItemsReq): Promise<IResponse<string>> {
      return await saveAddCollectionItems(paramsData);
    },

    /**
     * 获取控件的详情信息
     */
    async getDetailOfCollectionItemsById(id: string) {
      const detailRes = await getDetailOfCollectionItemsById(id);
      if (detailRes.data?.id) {
        this.detailOfCollectionItems = detailRes.data;
        const { identityCode } = detailRes.data.dataTypeIdentityDetail;
        if (identityCode) {
          this.detailOfCollectionItems["identityCode"] = identityCode;
          // 数字框
          if (identityCode === EControlType.NumberTextControl) {
            const { step } = detailRes.data.dataTypeIdentityDetail.identityDetailList[0];
            this.detailOfCollectionItems["step"] = step;
          }
          // 日期框
          if (identityCode === EControlType.DateControl) {
            const { identityLabel, identityValue } = detailRes.data.dataTypeIdentityDetail.identityDetailList[0];
            this.detailOfCollectionItems["dateTypeCode"] = identityValue;
            this.detailOfCollectionItems["dateTypeName"] = identityLabel;
          }
          this.detailOfCollectionItems["identityDetailList"] =
            detailRes.data.dataTypeIdentityDetail?.identityDetailList;
        }
      }
    },

    /**
     * 删除新增的采集项目
     */
    async delCollectionItemsById(id: string): Promise<IResponse<boolean>> {
      return await delCollectionItemsById(id);
    },

    /** 初始化详情数据 */
    initDetialInfo() {
      this.detailOfCollectionItems = {};
    }
  }
});
