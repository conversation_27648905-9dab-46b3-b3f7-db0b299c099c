<template>
  <el-form
    ref="baseInfoFormRef"
    class="base-info-form cx-form pt-6"
    label-position="top"
    require-asterisk-position="right"
    :model="baseInfoForm"
    :rules="rules"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="原材料编号" prop="code">
          <SerialNumber
            v-if="showSerialNumber"
            :code="RAW_MATERIAL_NO_CODE"
            :create="isAddRawMaterial"
            v-model="baseInfoForm.code"
            placeholder="请输入原材料编号"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="form-item" label="原材料名称" prop="name">
          <el-input v-model="baseInfoForm.name" placeholder="请输入原材料名称" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="原材料类型" prop="processCode">
          <el-select
            v-model="baseInfoForm.processCode"
            class="!w-full"
            filterable
            placeholder="请选择原材料类型"
            disabled
          >
            <el-option v-for="item in kindOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="用料量" prop="quantity">
          <el-input-number
            v-model="baseInfoForm.quantity"
            :min="0"
            class="!w-full"
            controls-position="right"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="计量单位" prop="partUnit">
          <el-input v-model="baseInfoForm.partUnit" placeholder="请输入计量单位" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="form-item" label="电压等级" prop="voltageGrade">
          <EnumSelect
            class="flex-1"
            v-model="baseInfoForm.voltageGrade"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            clearable
            allow-create
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="规格型号" prop="modelCode">
          <el-input v-model="baseInfoForm.modelCode" placeholder="请输入规格型号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="form-item" label="品牌" prop="borMaterials">
          <el-input v-model="baseInfoForm.borMaterials" placeholder="请输入品牌" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="产地" prop="oorMaterials">
          <el-input v-model="baseInfoForm.oorMaterials" placeholder="请输入产地" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="form-item" label="制造商" prop="rawmManufacturer">
          <el-input v-model="baseInfoForm.rawmManufacturer" placeholder="请输入制造商" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item class="form-item" label="原材料批次号" prop="materialBatchNo">
          <el-input v-model="baseInfoForm.materialBatchNo" placeholder="请输入原材料批次号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item class="form-item" label="生产日期" prop="productionDate">
          <el-date-picker
            v-model="baseInfoForm.productionDate"
            type="date"
            placeholder="请选择日期"
            class="!w-full"
            :disabled-date="disabledNowAfterDate"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="材料标准" prop="materialStandard">
          <el-input v-model="baseInfoForm.materialStandard" placeholder="请输入材料标准" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import EnumSelect from "@/components/EnumSelect";
import SerialNumber from "@/components/SerialNumber";
import { IRawMaterialReq } from "@/models/raw-material/i-raw-material-res";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { FormInstance, FormRules } from "element-plus";
import { reactive, computed, ref, watchEffect, onMounted } from "vue";
import { VoltageClassesEnum } from "@/enums";
import { RAW_MATERIAL_NO_CODE } from "@/consts/serial-number-code";
import { disabledNowAfterDate } from "@/utils/disabledDate";

// 表单信息
const baseInfoFormRef = ref();
const baseInfoForm = reactive<IRawMaterialReq>({
  name: "",
  code: "",
  processCode: "",
  processName: "",
  categoryCode: null,
  processId: null,
  voltageGrade: "",
  modelCode: "",
  mrmSpecification: null,
  borMaterials: "",
  rawmManufacturer: "",
  oorMaterials: "",
  materialBatchNo: "",
  productionDate: "",
  partUnit: "",
  quantity: null,
  materialStandard: null
});
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入原材料名称", trigger: "change" }],
  code: [{ required: true, message: "请输入原材料编号", trigger: "change" }],
  processCode: [{ required: true, message: "请选择原材料类型", trigger: "change" }],
  quantity: [{ required: false, message: "请输入用料量", trigger: "change" }],
  partUnit: [{ required: false, message: "请输入计量单位", trigger: "change" }],
  voltageGrade: [{ required: true, message: "请选择/输入电压等级", trigger: "change" }],
  modelCode: [{ required: true, message: "请输入规格型号", trigger: "change" }],
  borMaterials: [{ required: true, message: "请输入品牌名称", trigger: "change" }],
  rawmManufacturer: [{ required: true, message: "请输入制造商", trigger: "change" }],
  materialBatchNo: [{ required: true, message: "请输入原材料批次号", trigger: "change" }],
  productionDate: [{ required: true, message: "请选择生产日期", trigger: "change" }],
  materialStandard: [{ required: true, message: "请输入材料标准", trigger: "change" }]
});

const rawMaterialStore = useRawMaterialGroupUnitStore();
rawMaterialStore.getRawMaterialKindOptions();
const kindOptions = computed(() => {
  return (rawMaterialStore.rawMaterialKindOptions || []).map(item => {
    const { processCode, processName } = item;
    return {
      label: processName,
      value: processCode
    };
  });
});

// 控制渲染SerialNumber
const showSerialNumber = ref(true);

// 初始化数据
onMounted(() => {
  if (rawMaterialStore.isAdd) {
    const { processCode, processName, processId } = rawMaterialStore.currentTagInfo;
    Object.assign(baseInfoForm, { processCode, processName, processId });
  }
});

watchEffect(() => {
  if (rawMaterialStore.isAdd) return;
  const rawmaterialData = rawMaterialStore.selectRawMaterialBaseInfo;
  if (rawmaterialData?.id) {
    Object.assign(baseInfoForm, rawMaterialStore.selectRawMaterialBaseInfo);
  }
});

const isAddRawMaterial = computed(() => {
  return rawMaterialStore.isAdd;
});
// 返回表单数据之前校验表单
const submitForm = async () => {
  const formEl: FormInstance | undefined = baseInfoFormRef.value;
  if (!formEl) return;
  return await formEl.validate((valid, fields) => {
    if (valid) {
      rawMaterialStore.selectBaseInfoValid = true;
      rawMaterialStore.selectRawMaterialBaseInfo = baseInfoForm;
    } else {
      rawMaterialStore.selectBaseInfoValid = false;
      console.warn("error base info submit!", fields);
    }
  });
};

// 重置数据信息
const resetFormFiles = () => {
  baseInfoFormRef.value.resetFields();
  if (rawMaterialStore.isAdd) {
    // 请求自动编码
    setTimeout(() => {
      setShowSerialNumber(true);
    });
    const { processCode, processName } = rawMaterialStore.currentTagInfo;
    Object.assign(baseInfoForm, { processCode, processName });
  }
};

// 设置是否显示编号框
const setShowSerialNumber = (flag: boolean) => {
  showSerialNumber.value = flag;
};

defineExpose({
  baseInfoFormRef,
  setShowSerialNumber,
  submitForm,
  resetFormFiles
});
</script>

<style scoped lang="scss"></style>
