<template>
  <BaseList :columns="columns" type="production-plan" v-bind="$attrs" ref="baseList" />
  <EditProductionPlanDialog ref="editDialog" />
  <DetailProductionPlanDialog ref="detailDialog" />
</template>

<script setup lang="ts">
import { provide, ref } from "vue";
import { OperatorCell } from "@/components/TableCells";
import { dateFormat } from "@/consts";
import { ColumnWidth, StateGridOrderSyncResult, TableWidth } from "@/enums";
import { IProductionPlanSync } from "@/models";
import { formatDecimal } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditProductionPlanDialog from "@/views/components/state-grid-order-sync/dialogs/edit-production-plan-dialog.vue";
import DetailProductionPlanDialog from "@/views/components/state-grid-order-sync/dialogs/detail-production-plan-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import BaseList from "../base-list.vue";

const { enumFormatter, dateFormatter } = useTableCellFormatter();

const baseList = ref<InstanceType<typeof BaseList>>();
const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditProductionPlanDialog>,
  InstanceType<typeof DetailProductionPlanDialog>,
  IProductionPlanSync
>();

provide(stateGridOrderSyncEditKey, {
  refreshFn: pageInfo => baseList.value?.refresh(pageInfo)
});

const columns: TableColumnList = [
  {
    label: "排产计划编码",
    prop: "ppNo",
    width: TableWidth.order,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "销售订单号",
    prop: "soNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    minWidth: ColumnWidth.Char14
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.largeName
  },
  {
    label: "排产数量",
    prop: "amount",
    minWidth: TableWidth.number,
    formatter: row => {
      const { amount, measUnitName } = row;
      return amount ? `${formatDecimal(amount)} ${measUnitName}` : null;
    }
  },
  {
    label: "交货日期",
    prop: "deliveryDate",
    width: TableWidth.date,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    width: TableWidth.operation,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        }
      ])
  }
];
</script>

<style scoped></style>
