<template>
  <el-form
    ref="ruleFormRef"
    :model="formValue"
    :rules="rules"
    :validate-on-rule-change="false"
    class="cx-form"
    label-width="7rem"
  >
    <div :id="DIALOG_ERROR_ID_SALES_ORDER_LINE" />
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="生产订单号" prop="ipoNo">
          <template #label>
            <span
              v-alias="{ code: KeywordAliasEnum.IPO_NO, default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO] }"
            />
          </template>
          <SerialNumber
            :code="PRODUCT_ORDER_NO_CODE"
            :create="productOrderStore.productOrderFormAddMode"
            v-model="formValue.ipoNo"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <!-- 占据空间 -->
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产状态" prop="ipoStatus">
          <EnumSelect
            class="w-full"
            v-model="formValue.ipoStatus"
            placeholder="请选择接生产状态"
            :enum="ProductionStateEnum"
            enumName="productionStateEnum"
            clearable
          />
        </el-form-item>
      </el-col>
      <template v-if="true">
        <el-col :span="12">
          <el-form-item label="物资种类" prop="subClassCode">
            <SubclassSelect
              clearable
              v-model="formValue.subClassCode"
              :categoryCode="salesOrderDetailStore.salesOrder.categoryCode"
              :disabled="fieldDisabled"
              :limitByOrder="!fieldDisabled"
              :salesOrderId="salesOrderId"
              @update:model-value="onSubClassCodeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料编号" prop="materialsCode" class="w-full">
            <div class="flex w-full">
              <el-input class="flex-1 mr-2" v-model="formValue.materialsCode" placeholder="请输入物料编号" disabled />
              <MaterialSelect
                :subClassCode="formValue.subClassCode"
                :selectedId="formValue.materialId"
                :disabled="disabledBySubClassCode"
                @material-change="materialChange"
              />
            </div>
          </el-form-item>
        </el-col>
      </template>
      <el-col :span="12" v-if="formValue.subClassCode === MaterialSubclassCode.CABLE_TERMINAL">
        <el-form-item label="应用类型" prop="applicationType">
          <EnumSelect
            class="w-full"
            placeholder="请选择接应用类型"
            v-model="formValue.applicationType"
            :enum="DeviceTerminalEnum"
            enumName="deviceTerminalEnum"
            :disabled="fieldDisabled && !isOnlyCableTerminalApter"
            clearable
          />
        </el-form-item>
      </el-col>
      <template v-if="isOnlyCableTerminalApter">
        <el-col :span="12">
          <el-form-item label="接头类型" prop="jointCategories">
            <EnumSelect
              class="w-full"
              v-model="formValue.jointCategories"
              placeholder="请选择接头类型"
              :enum="JointCategoriesEnum"
              enumName="jointCategoriesEnum"
              :disabled="fieldDisabled && !isOnlyCableTerminalApter"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电压类型" prop="voltageType">
            <EnumSelect
              class="w-full"
              v-model="formValue.voltageType"
              placeholder="请选择电压类型"
              :enum="VoltageTypeEnum"
              enumName="voltageTypeEnum"
              :disabled="fieldDisabled && !isOnlyCableTerminalApter"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电压等级" prop="voltageClasses">
            <ElSelect
              class="w-full"
              :placeholder="voltageClassPlaceholder"
              v-model="formValue.voltageClasses"
              :disabled="fieldDisabled && !isOnlyCableTerminalApter"
              clearable
              filterable
            >
              <el-option
                v-for="item in voltageClassesOptionsRef"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </el-form-item>
        </el-col>
      </template>
      <template v-else>
        <el-col :span="12">
          <el-form-item label="电压等级" prop="voltageClasses">
            <EnumSelect
              class="w-full"
              v-model="formValue.voltageClasses"
              placeholder="请选择"
              :enum="VoltageClassesEnum"
              enumName="voltageClassesEnum"
              clearable
              allow-create
            />
          </el-form-item>
        </el-col>
      </template>
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input v-model="formValue.specificationModel" placeholder="请输入规格型号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产数量" prop="amount">
          <el-input-number
            v-model="formValue.amount"
            controls-position="right"
            :min="0"
            placeholder="请输入生产数量"
            clearable
            class="!w-full"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计量单位" prop="unit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="formValue.subClassCode"
            class="w-full"
            placeholder="请选择计量单位"
            v-model="formValue.unit"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="计划日期" prop="planDateArray">
          <el-date-picker
            class="w-full"
            v-model="formValue.planDateArray"
            type="daterange"
            label="计划日期"
            start-placeholder="请选择开始日期"
            end-placeholder="请选择结束日期"
            style="width: 100%"
            clearable
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="实际开始日期"
          prop="actualStartDate"
          :rules="[
            ...(props.actualStartDateRules || []),
            {
              required: actualStartDateRequired,
              message: '实际开始日期不能为空',
              trigger: 'change'
            }
          ]"
        >
          <el-date-picker
            class="!w-full"
            v-model="formValue.actualStartDate"
            label="请选择实际开始日期"
            placeholder="请选择实际开始日期"
            :disabled-date="actualStartDateDisabledDate"
            clearable
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="实际结束日期" prop="actualFinishDate" :rules="props.actualFinishDateRules">
          <el-date-picker
            class="!w-full"
            v-model="formValue.actualFinishDate"
            label="请选择实际结束日期"
            placeholder="请选择实际结束日期"
            clearable
            :disabled-date="actualEndDateDisabledDate"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formValue.remark"
            placeholder="请输入备注"
            type="textarea"
            :rows="2"
            :maxlength="200"
            :show-word-limit="true"
            resize="none"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <!-- 关联销售订单 -->
  <el-row class="px-5 pb-5 mt-5">
    <el-col :span="24" class="text-right flex items-center justify-between">
      <TitleBar title="销售订单行项目" />
      <SalesOrderLineSelect
        :subclassCode="formValue.subClassCode"
        :salesOrderId="salesOrderId"
        v-model="selectedData"
        @post-save="onPostSave"
      />
    </el-col>
  </el-row>
  <el-row class="px-5">
    <SelectSalesOrderLine v-model="selectedData" />
  </el-row>
</template>

<script setup lang="ts">
import SerialNumber from "@/components/SerialNumber";
import Dictionary from "@/components/Dictionary";
import EnumSelect from "@/components/EnumSelect";
import SelectSalesOrderLine from "../selected-sales-order-line/index.vue";
import TitleBar from "@/components/TitleBar/";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import MaterialSelect from "@/views/components/material-select/material-select.vue";
import { computed, reactive, ref, watch, watchEffect, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { ICreateProductOrder, ICreateProductOrderExe, IMaterial, IOption, ISalesOrderLineExt } from "@/models";
import { useSalesOrderDetailStore, useSalesProductOrderStore } from "@/store/modules";
import {
  PRODUCT_ORDER_NO_CODE,
  MEASURE_UNIT,
  DIALOG_ERROR_ID_SALES_ORDER_LINE,
  CABLE_TERMINAL_ADAPTER
} from "@/consts";
import { useVoltageTypeMapVoltageClasses } from "@/utils/format";
import {
  DeviceTerminalEnum,
  ProductionStateEnum,
  JointCategoriesEnum,
  VoltageTypeEnum,
  VoltageClassesEnum,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc
} from "@/enums";
import { FormItemRule } from "element-plus/es/components/form/src/types";
import SalesOrderLineSelect from "@/views/components/sales-order-line-select";
import { requiredMessage, Validators } from "@/utils/form";
import { MaterialSubclassCode } from "@/enums/material";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";
import { computedAsync } from "@vueuse/core";

const props = defineProps<{
  salesOrderId?: string;
  actualStartDateRules?: Array<FormItemRule>;
  actualFinishDateRules?: Array<FormItemRule>;
}>();

const voltageTypeMapVoltageClasses = useVoltageTypeMapVoltageClasses();
const productOrderStore = useSalesProductOrderStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const keyWordAliasHook = usekeyWordAliasHook();
const ruleFormRef = ref<FormInstance>();
const actualStartDateRequired = ref<boolean>();
const voltageClassesOptionsRef = ref<Array<IOption>>(voltageTypeMapVoltageClasses[VoltageTypeEnum.INTERFLOW]);
const formValue = reactive<ICreateProductOrderExe>({
  ipoNo: "",
  ipoStatus: undefined,
  subClassCode: "",
  materialsCode: "",
  materialsName: "",
  materialId: "",
  applicationType: undefined,
  jointCategories: undefined,
  voltageType: undefined,
  amount: undefined,
  unit: undefined,
  specificationModel: null,
  voltageClasses: undefined,
  planDateArray: undefined,
  remark: null
});
const fieldDisabled = ref<boolean>();
const rules = computedAsync<FormRules>(async () => {
  const ipoNoErrorMessage = await keyWordAliasHook.getReplaceAlias(
    `${KeywordAliasEnum.IPO_NO}不能为空`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  );
  return {
    ipoNo: [{ required: true, message: ipoNoErrorMessage, trigger: "blur" }],
    ipoStatus: [{ required: true, message: "生产状态不能为空", trigger: "change" }],
    subClassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
    materialsCode: [{ required: true, message: requiredMessage("物料编号"), trigger: "change" }],
    applicationType: [{ required: true, message: "应用类型不能为空", trigger: "change" }],
    jointCategories: [{ required: true, message: "接头类型不能为空", trigger: "change" }],
    voltageType: [{ required: true, message: "电压类型不能为空", trigger: "change" }],
    voltageClasses: [{ required: isOnlyCableTerminalApter.value, message: "电压等级不能为空", trigger: "change" }],
    amount: [
      { required: true, trigger: "change", message: "生产数量不能为空" },
      { required: true, trigger: "change", message: "数量必须大于0", validator: Validators.positiveNumber }
    ],
    unit: [{ required: true, message: "计量单位不能为空", trigger: "change" }],
    specificationModel: [{ required: true, message: "规格型号不能为空", trigger: "change" }],
    planDateArray: [
      {
        type: "array",
        required: true,
        message: "计划日期不能为空",
        trigger: "change",
        fields: {
          0: { type: "date", required: true, message: "请选择开始日期" },
          1: { type: "date", required: true, message: "请选择结束日期" }
        }
      }
    ]
  };
});

const voltageClassPlaceholder = computed(() => (formValue.voltageType ? "请选择/输入电压等级" : "请先选择电压类型"));
const isCable = computed(() => salesOrderDetailStore.isCable);
const isOnlyCableTerminalApter = computed(() => CABLE_TERMINAL_ADAPTER.includes(formValue.subClassCode));
const disabledBySubClassCode = computed(() => !formValue.subClassCode);
const salesOrderId = computed(() => props.salesOrderId || salesOrderDetailStore.saleOrderId);
const selectedData = ref<Array<ISalesOrderLineExt>>([]);

if (isCable.value) {
  watch(
    () => formValue.voltageType,
    newVal => {
      //编辑状态下不重置voltageClasses
      if (!productOrderStore.createProductOrderDetail?.id) {
        ruleFormRef?.value?.resetFields(["voltageClasses"]);
      }

      voltageClassesOptionsRef.value = voltageTypeMapVoltageClasses[newVal] || [];
    },
    { immediate: true }
  );
}

onMounted(() => {
  watch(
    () => formValue.actualFinishDate,
    actualFinishDate => {
      if (!actualFinishDate) {
        actualStartDateRequired.value = false;
        ruleFormRef.value?.clearValidate(["actualStartDate"]);
      } else {
        actualStartDateRequired.value = true;
      }
    }
  );
});

watchEffect(() => {
  if (productOrderStore.createProductOrderDetail && productOrderStore.createProductOrderDetail.id) {
    Object.assign(formValue, productOrderStore.createProductOrderDetail);
    fieldDisabled.value = true;
    formValue.planDateArray = [
      productOrderStore.createProductOrderDetail.planStartDate,
      productOrderStore.createProductOrderDetail.planFinishDate
    ];
    selectedData.value = productOrderStore.createProductOrderDetail.salesLineDetails || [];
    return;
  }
  formValue.unit = productOrderStore.createProductOrderDetail?.unit;
  formValue.specificationModel = productOrderStore.createProductOrderDetail?.specificationModel;
  if (!isCable.value) {
    formValue.amount = productOrderStore.createProductOrderDetail?.amount;
    formValue.voltageClasses = productOrderStore.createProductOrderDetail?.voltageClasses;
  }
  fieldDisabled.value = false;
});

watch(
  () => productOrderStore.setCreateProductOrderFromBySoItemNo,
  newVal => {
    if (newVal) {
      Object.assign(formValue, productOrderStore.createProductOrderDetail);
      selectedData.value = productOrderStore.createProductOrderDetail.salesLineDetails || [];
    }
  },
  {
    immediate: true
  }
);

const onSubClassCodeChange = () => {
  resetFromValueBySubClassCode();
};

// 保存选择的物料弹框
const materialChange = (material: IMaterial) => {
  const { materialCode, id, voltageClass, materialName } = material;
  Object.assign(formValue, {
    materialsCode: materialCode,
    materialsName: materialName,
    materialId: id,
    voltageClasses: voltageClass
  });
};

const resetFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields();
};

const patchFormValue = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.resetFields([
    "ipoStatus",
    "applicationType",
    "jointCategories",
    "voltageType",
    "amount",
    "unit",
    "specificationModel",
    "voltageClasses",
    "planDateArray",
    "remark",
    "actualStartDate",
    "actualFinishDate"
  ]);
};

const getFormValue = async (): Promise<boolean | ICreateProductOrder> => {
  if (!ruleFormRef.value) {
    return false;
  }

  const valid = await ruleFormRef.value.validate(() => {});

  if (!valid) {
    return valid;
  }

  if (!selectedData.value?.length) {
    ElMessage.warning("请先关联销售订单行");
    return false;
  }

  const salesLineIds = selectedData.value.map(item => item.id);
  formValue.salesLineIds = salesLineIds || [];
  const { categoryCode, id } = salesOrderDetailStore.salesOrder;
  formValue.categoryCode = categoryCode;
  formValue.saleId = id;
  const { planDateArray, ...otherValue } = formValue;
  if (planDateArray) {
    return { ...otherValue, planStartDate: planDateArray[0], planFinishDate: planDateArray[1] };
  }
  return otherValue;
};

const actualStartDateDisabledDate = (date: Date) => {
  if (date > new Date()) {
    return true;
  }
  return formValue.actualFinishDate
    ? date >
        (typeof formValue.actualFinishDate === "string"
          ? new Date(formValue.actualFinishDate)
          : formValue.actualFinishDate)
    : false;
};

const actualEndDateDisabledDate = (date: Date) => {
  if (date > new Date()) {
    return true;
  }

  return formValue.actualStartDate
    ? date <
        (typeof formValue.actualStartDate === "string"
          ? new Date(formValue.actualStartDate)
          : formValue.actualStartDate)
    : false;
};

function resetFromValueBySubClassCode() {
  formValue.materialId = undefined;
  formValue.materialsCode = undefined;
  formValue.materialsName = undefined;
  formValue.voltageClasses = undefined;
  formValue.unit = undefined;
  selectedData.value = [];
}

/**
 * @description: 关联销售订单行保存后置回调
 */
function onPostSave(list: Array<ISalesOrderLineExt>) {
  if (!list || !list.length) {
    return;
  }
  const latestSaleOrderLine = list[0];
  // check list中的销售行 生产工号是否有多个（新增模式）
  if (!productOrderStore.createProductOrderDetail?.id) {
    const uniqueCodes = [...new Set(list.filter(item => item.productionWorkNo).map(m => m.productionWorkNo))];
    if (uniqueCodes.length === 1) {
      formValue.ipoNo = uniqueCodes[0];
    }
  }
  Object.assign(formValue, {
    materialId: latestSaleOrderLine.materialId,
    // 物料编号
    materialsCode: latestSaleOrderLine.materialCode,
    materialsName: latestSaleOrderLine.materialName,
    // 规格型号
    specificationModel: latestSaleOrderLine.specificationType,
    // 数量
    amount: latestSaleOrderLine.materialNumber,
    // 单位
    unit: latestSaleOrderLine.materialUnit,
    // 电压等级
    voltageClasses: latestSaleOrderLine.voltageLevel
  });
}

defineExpose({
  getFormValue,
  resetFormValue,
  patchFormValue
});
</script>

<style scoped lang="scss"></style>
