import { IAutoCollectChartReq, IReportWork } from "@/models";
import { useDeviceAcquisitionStore, useReportWorkStore } from "@/store/modules";
import { useProductionProcessInspecStore } from "@/store/modules/production-process-inspection";

/**
 * 生产过程自动采集项的hook
 */

export const useAutoCollectionHook = () => {
  const productionProcessInspecStore = useProductionProcessInspecStore();
  const deviceAcquisitionStore = useDeviceAcquisitionStore();

  const reportWorkStore = useReportWorkStore();
  // 根据工序id获取工序详情
  async function getReportWorkById(id: string) {
    const reportWork: IReportWork = await reportWorkStore.getReportWorkById(id);
    return reportWork;
  }

  // 根据设备编号 获取自动采集数据
  async function getHistoryAutoCollectCharts(params: IAutoCollectChartReq, signal?: AbortSignal) {
    return await productionProcessInspecStore.getHistoryAutoCollectCharts(params, signal).catch(() => []);
  }

  // 根据设备id 获取采集点信息
  async function queryAcquisitionAll(deviceId: string) {
    return await deviceAcquisitionStore.queryAcquisitionAll(deviceId).catch(() => []);
  }

  return {
    getReportWorkById,
    getHistoryAutoCollectCharts,
    queryAcquisitionAll
  };
};
