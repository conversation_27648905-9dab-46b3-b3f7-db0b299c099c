import { getSyncEmphasisRawMaterialList } from "@/api/stock-data-center/sync-stock/sync-emphasis-raw-material";
import { ISearchEmphasisRawMaterial } from "@/models/stock-data-center/i-emphasis-raw-material";
import { ISyncEmphasisRawMaterial } from "@/models/stock-data-center/stock-sync/i-raw-material-info";
import { setSyncingStatus } from "@/views/stock-data-center/stock-sync/utils/sync-stock-utils";
import { defineStore } from "pinia";

export const useSyncRawMaterialStore = defineStore({
  id: "sync-raw-material-store",
  state: () => ({
    tableTotal: 0,
    syncEmphasisRawMaterialData: [] as Array<ISyncEmphasisRawMaterial>,
    queryParams: {
      pageNo: 1,
      pageSize: 20
    } as ISearchEmphasisRawMaterial
  }),
  actions: {
    /**
     * 查询同步的重点原材料
     */
    querySyncEmphasisRawMaterialData(queryData: ISearchEmphasisRawMaterial) {
      this.queryParams = { ...this.queryParams, ...queryData };
      this.getSyncEmphasisRawMaterialTableData();
    },

    /**
     * 获取重点原材料库存信息
     */
    async getSyncEmphasisRawMaterialTableData() {
      const resData = await getSyncEmphasisRawMaterialList(this.queryParams);
      if (Array.isArray(resData.data?.list) && resData.data?.list.length) {
        this.syncEmphasisRawMaterialData = setSyncingStatus(resData.data?.list);
        this.tableTotal = resData.data?.total;
      } else {
        this.syncEmphasisRawMaterialData = [];
        this.tableTotal = 0;
      }
    },

    /** 重置查询参数 */
    initQueryParams() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      };
    }
  }
});
