import { withApiGateway } from "@/api/util";
import { IProductionMaterialProcess, IResponse } from "@/models";
import { http } from "@/utils/http";

const getOutGoingFactoryProcess = (productionId: string) => {
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(
    withApiGateway(`admin-api/business/outgoing/process/status/${productionId}`)
  );
};

const getNonCableOutGoingFactoryProcess = (workOrderId: string) => {
  return http.get<void, IResponse<Array<IProductionMaterialProcess>>>(
    withApiGateway(`admin-api/business/outgoing/process/status/non-cable/${workOrderId}`)
  );
};

// 获取控制 模拟局放耐压 按钮的配置
const getOutGoingFactoryJFNYButton = () => {
  return http.get<void, IResponse<boolean>>(withApiGateway(`admin-api/business/outgoing/isHide`));
};

export const outGoingFactoryApi = {
  getOutGoingFactoryProcess,
  getNonCableOutGoingFactoryProcess,
  getOutGoingFactoryJFNYButton
};
