import { IDeviceGroup, IDeviceGroupReq, IDeviceGroupForm } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/monitor-device/device-group";

export const useDeviceGroupStore = defineStore({
  id: "cx-device-group",
  state: () => ({
    deviceGroupList: [] as Array<IDeviceGroup>
  }),
  actions: {
    /** 查询设备分组数据 */
    async queryDeviceGroup(params?: IDeviceGroupReq) {
      const res = await api.queryDeviceGroup(params);
      this.deviceGroupList = res.data;
    },

    /** 创建设备分组 */
    async createDeviceGroup(data: IDeviceGroupForm) {
      return api.createDeviceGroup(data);
    },

    /** 编辑设备分组 **/
    async editDeviceGroup(data: IDeviceGroupForm) {
      return api.editDeviceGroup(data);
    },

    /** 删除设备分组 **/
    async deleteDeviceGroup(data: IDeviceGroupForm) {
      return api.deleteDeviceGroup(data.id);
    }
  }
});
