<template>
  <el-radio-group size="small" v-model="collapse" @change="collapseChange">
    <el-radio border :label="false" class="bg-bg_color">全部展开</el-radio>
    <el-radio border :label="true" class="bg-bg_color !mr-0">全部收起</el-radio>
  </el-radio-group>
</template>

<script setup lang="ts">
import { inject, ref } from "vue";
import { syncAuditStateKey } from "./tokens";

const ctx = inject(syncAuditStateKey);
const collapse = ref(false);

function collapseChange(value: boolean) {
  ctx.businessItems.forEach(item => (item.collapsed = value));
  ctx.iotItems.forEach(item => (item.collapsed = value));
}
</script>

<style scoped></style>
