import { ElButton, TableColumnCtx } from "element-plus";
import { IFileSync } from "@/models";
import { PurchaseChannel, StateGridOrderSyncResult } from "@/enums";
import { h } from "vue";
import CxTag from "@/components/CxTag/index.vue";
import { Document } from "@element-plus/icons-vue";
import { useStateGridOrderSyncDetailStore } from "@/store/modules/state-grid-order-sync/state-grid-order-sync-detail";
import { useSalesOrderSyncInfo } from "@/store/modules/sales-state-grid-order-sync/sales-state-grid-order-sync-detail";

export function fileSyncFormatter(showFileSyncListDialog: (id: string) => void) {
  const purchaseSyncInfo = useStateGridOrderSyncDetailStore();
  const salesOrderSyncInfo = useSalesOrderSyncInfo();

  // 只有EIP平台的文件同步状态才显示同步图标按钮
  const showButton =
    purchaseSyncInfo.channel === PurchaseChannel.EIP || salesOrderSyncInfo.channel === PurchaseChannel.EIP;

  return (row: any, column: TableColumnCtx<any>, files: Array<IFileSync>) => {
    if (!Array.isArray(files) || files.length < 1) {
      return null;
    }
    const tag = getFileSyncTag(files);
    const button = h(ElButton, {
      link: true,
      icon: Document,
      type: "primary",
      class: "!text-lg",
      onClick: () => showFileSyncListDialog(row.id)
    });
    return h("div", { class: "flex items-center" }, showButton ? [button, tag] : [tag]);
  };
}

function getFileSyncTag(files: Array<IFileSync>) {
  const status = getFileSyncStatus(files);
  const type = status == null ? "info" : status ? "success" : "danger";
  const text = status == null ? "未同步" : status ? "已触发同步" : "同步失败";
  return h(CxTag, { type }, () => text);
}

function getFileSyncStatus(files: Array<IFileSync>): boolean | null {
  let successCont = 0;
  let failCont = 0;
  files.forEach(file => {
    const syncResult = file.syncResult;
    successCont += Number(syncResult === StateGridOrderSyncResult.SUCCESS);
    failCont += Number(
      syncResult === StateGridOrderSyncResult.FAULT || syncResult === StateGridOrderSyncResult.GATEWAY_FAULT
    );
  });
  if (failCont) {
    return false;
  }
  if (successCont === files.length) {
    return true;
  }
  return null;
}
