import { dateFormat, fullDateFormat } from "@/consts/date-format";
import { TableWidth, ColumnWidth } from "@/enums";
import { IPurchaseOrderLineRes } from "@/models/purchase-order";
import { formatDate } from "@/utils/format/date";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { RouterLink } from "vue-router";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const { statusFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "",
      type: "selection",
      fixed: "left",
      minWidth: TableWidth.check,
      reserveSelection: true
    },
    {
      label: "采购订单行项目号",
      slot: "poItemNo",
      prop: "poItemNo",
      fixed: "left",
      sortable: "custom",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "采购订单行项目ID",
      prop: "poItemId",
      minWidth: TableWidth.largeType
    },
    {
      label: "采购订单号",
      prop: "poNo",
      sortable: "custom",
      minWidth: ColumnWidth.Char9,
      formatter: (row: IPurchaseOrderLineRes) => {
        return (
          <RouterLink
            class="text-primary"
            onClick={(ev: Event) => cancelBubbling(ev)}
            to={{ name: "purchaseOrderDetail", params: { id: row.purchaseId } }}
          >
            {row.poNo}
          </RouterLink>
        );
      }
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: TableWidth.type
    },
    {
      label: "物料编码",
      prop: "materialCode",
      minWidth: TableWidth.type
    },
    {
      label: "物料描述",
      prop: "materialDesc",
      minWidth: TableWidth.name
    },
    {
      label: "采购数量",
      prop: "amount",
      minWidth: TableWidth.number
    },
    {
      label: "合同编号",
      prop: "conCode",
      sortable: "custom",
      minWidth: TableWidth.order
    },
    {
      label: "合同名称",
      prop: "conName",
      minWidth: TableWidth.name
    },
    {
      label: "采购方公司名称",
      prop: "buyerName",
      minWidth: TableWidth.name,
      formatter: row => {
        if (row.buyerName) {
          return (
            <CxTag icon="icon-company-fill" type="custom">
              {row.buyerName}
            </CxTag>
          );
        }
      }
    },
    {
      label: "合同编号（国网经法号）",
      prop: "sellerConCode",
      sortable: "custom",
      minWidth: TableWidth.name
    },
    {
      label: "项目名称",
      prop: "prjName",
      minWidth: TableWidth.name
    },
    {
      label: "合同签订日期",
      prop: "sellerSignTime",
      sortable: "custom",
      minWidth: TableWidth.name,
      formatter: row => formatDate(row.sellerSignTime, dateFormat)
    },
    {
      label: "合同计划交货日期",
      prop: "dlvTime",
      minWidth: TableWidth.dateTime,
      formatter: row => formatDate(row.dlvTime, dateFormat)
    },
    {
      label: "合同最终交货日期",
      prop: "cfmDlvTime",
      minWidth: TableWidth.dateTime,
      formatter: row => formatDate(row.cfmDlvTime, dateFormat)
    },
    {
      label: "拉取时间",
      prop: "pullingTime",
      sortable: "custom",
      minWidth: TableWidth.name,
      formatter: row => formatDate(row.pullingTime, fullDateFormat)
    },
    {
      label: "同步状态",
      prop: "syncResult",
      fixed: "right",
      minWidth: TableWidth.type,
      formatter: statusFormatter("已触发同步", "同步失败")
    },
    {
      label: "触发结果",
      prop: "isTrigger",
      fixed: "right",
      minWidth: TableWidth.order,
      formatter: statusFormatter("已触发评分", "评分触发失败")
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operate",
      minWidth: TableWidth.largeXgOperation
    }
  ];

  function cancelBubbling(ev: Event) {
    ev.stopPropagation();
    ev.preventDefault();
  }

  return { columns };
}
