<template>
  <div class="flex justify-end pb-4">
    <ElButton v-auth="PermissionKey.meta.metaDeviceCollect" type="primary" :icon="Plus" @click="onAddAcquisitionInfo()">
      <template #icon>
        <FontIcon icon="icon-plus" />
      </template>
      新增采集点
    </ElButton>
  </div>
  <PureTable
    class="flex-1 overflow-hidden pagination tooltip-max-w"
    row-key="id"
    :data="deviceAcquisitionStore.acquisitions"
    :columns="columns"
    :pagination="pagination"
    showOverflowTooltip
    :loading="deviceAcquisitionStore.loading"
    @page-size-change="onPageSizeChange()"
    @page-current-change="onCurrentPageChange()"
  >
    <template #operation="data">
      <div>
        <ElButton v-auth="PermissionKey.meta.metaDeviceCollect" type="primary" link @click="onEdit(data.row)">
          编辑
        </ElButton>
        <ElButton v-auth="PermissionKey.meta.metaDeviceCollect" link type="danger" @click="onDelete(data.row.id)">
          删除
        </ElButton>
      </div>
    </template>
    <template #empty>
      <el-empty :image-size="120">
        <template #image>
          <EmptyData />
        </template>
      </el-empty>
    </template>
  </PureTable>
  <el-dialog
    :title="state.title"
    align-center
    class="small"
    destroy-on-close
    v-model="state.acquisitionFormvModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCloseAcquisitionForm"
  >
    <AcquisitionForm ref="acquisitionFormRef" />
    <template #footer>
      <el-button @click="onCloseAcquisitionForm()">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onSaveAcquisitionForm()">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, watchEffect } from "vue";
import { useColumns } from "./columns";
import { useTableConfig } from "@/utils/useTableConfig";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import AcquisitionForm from "./acquisition-form.vue";
import { ElMessage } from "element-plus";
import { useConfirm } from "@/utils/useConfirm";
import { Plus } from "@element-plus/icons-vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useRoute } from "vue-router";
import { IDeviceAcquisitionForm } from "@/models/device";
import { useDeviceAcquisitionStore } from "@/store/modules/device";
import { PermissionKey } from "@/consts";

const route = useRoute();
const deviceId: string = route.params.id as string;

const deviceAcquisitionStore = useDeviceAcquisitionStore();

const state = reactive<{
  acquisitionFormvModalVis: boolean;
  acquisitionInfoTitleorNot: boolean;
  title: string;
}>({
  acquisitionFormvModalVis: false,
  acquisitionInfoTitleorNot: false,
  title: undefined
});

const acquisitionFormRef = ref<InstanceType<typeof AcquisitionForm>>();
const { columns } = useColumns();
const { pagination } = useTableConfig();
const saveLoading = ref<boolean>(false);

deviceAcquisitionStore.queryAcquisition(queryParams());

watchEffect(() => {
  pagination.total = deviceAcquisitionStore.total;
});

async function onAddAcquisitionInfo() {
  state.title = "新增设备采集点";
  state.acquisitionFormvModalVis = true;
  deviceAcquisitionStore.clearDeviceAcquisitionStorage();
}

const onEdit = (item: IDeviceAcquisitionForm) => {
  state.title = "编辑设备采集点";
  state.acquisitionFormvModalVis = true;
  deviceAcquisitionStore.setDeviceAcquisitionStorage(item);
};

const onCloseAcquisitionForm = () => {
  state.acquisitionFormvModalVis = false;
};

const onSaveAcquisitionForm = useLoadingFn(saveDeviceAcquisition, saveLoading);

async function saveDeviceAcquisition() {
  const formValue: IDeviceAcquisitionForm | boolean = await acquisitionFormRef.value.getFormValue();

  if (typeof formValue === "boolean") {
    return;
  }

  const _formValue: IDeviceAcquisitionForm = { ...formValue, deviceId };
  if (!formValue.id) {
    await deviceAcquisitionStore.createDeviceAcquisition(_formValue);
  } else {
    await deviceAcquisitionStore.editDeviceAcquisition(_formValue);
  }
  state.acquisitionFormvModalVis = false;
  ElMessage.success(formValue.id ? "修改成功" : "新增成功");
  pagination.currentPage = 1;
  deviceAcquisitionStore.queryAcquisition(queryParams());
}

const onDelete = async (id: string) => {
  if (!(await useConfirm("数据删除后将无法恢复，是否继续？", "确认删除"))) {
    return;
  }
  await deviceAcquisitionStore.deleteDeviceAcquisition(id);
  ElMessage.success("删除成功");
  pagination.currentPage = 1;
  deviceAcquisitionStore.queryAcquisition({ deviceId });
};

const onPageSizeChange = () => {
  deviceAcquisitionStore.queryAcquisition(queryParams());
};
const onCurrentPageChange = () => {
  deviceAcquisitionStore.queryAcquisition(queryParams());
};

function queryParams() {
  return { deviceId, pageNo: pagination.currentPage, pageSize: pagination.pageSize };
}
</script>

<style scoped lang="scss"></style>
