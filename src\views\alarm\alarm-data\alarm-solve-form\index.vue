<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="alarm-form" label-width="100px" label-position="right">
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="解决状态：" prop="alarmSolveStatus">
          <el-radio-group v-model="form.alarmSolveStatus">
            <el-radio border :label="AlarmSolveStatusEnum.RESOLVED">{{
              AlarmSolveStatusEnumMapDisplayName[AlarmSolveStatusEnum.RESOLVED]
            }}</el-radio>
            <el-radio border :label="AlarmSolveStatusEnum.UNSOLVABLE">{{
              AlarmSolveStatusEnumMapDisplayName[AlarmSolveStatusEnum.UNSOLVABLE]
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="备注：" prop="notes">
          <el-input type="textarea" placeholder="请输入备注" :rows="2" resize="none" v-model="form.notes" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { AlarmSolveStatusEnum, AlarmSolveStatusEnumMapDisplayName } from "@/enums";
import { IAlarmSolveReq } from "@/models";

defineExpose({
  getValidValue,
  validate
});

const formRef = ref<FormInstance>();

const form = reactive<Partial<IAlarmSolveReq>>({});
const rules: FormRules = {
  alarmSolveStatus: [{ required: true, message: requiredMessage("解决状态"), trigger: "change" }]
};

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IAlarmSolveReq> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form as IAlarmSolveReq;
}
</script>

<style scoped lang="scss">
.alarm-form {
  padding: 0 20px;
  overflow: hidden;
}
</style>
