<template>
  <div class="bg-bg_color flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="flex-1 !p-0">
      <ElFormItem label="关键字：">
        <ElInput class="!w-[300px]" clearable v-model="state.params.keyWords" :placeholder="placeholder" />
      </ElFormItem>
      <ElFormItem label="解决状态：">
        <ElSelect v-model="state.params.alarmSolveStatus" clearable placeholder="请选择解决状态">
          <el-option v-for="item in alarmSolveStatus" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="告警时间：">
        <el-date-picker
          v-model="state.params.alarmTimes"
          type="daterange"
          range-separator="～"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onSearch()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { IAlarmDataParam } from "@/models";
import {
  AlarmSolveStatusEnum,
  AlarmSolveStatusEnumMapDisplayName,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc
} from "@/enums";
import { computedAsync } from "@vueuse/core";
import { usekeyWordAliasHook } from "@/views/components/key-word-alias/keyword-alias-hook";

const keyWordAliasHook = usekeyWordAliasHook();
const state = reactive<{
  params: IAlarmDataParam;
}>({
  params: {}
});

const emits = defineEmits<{
  (e: "onSearch", params: IAlarmDataParam): void;
}>();

const placeholder = computedAsync(async () =>
  keyWordAliasHook.getReplaceAlias(
    `输入${KeywordAliasEnum.IPO_NO}/销售订单/采购订单/告警原因`,
    KeywordAliasEnum.IPO_NO,
    KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
  )
);

const alarmSolveStatus = [
  {
    label: AlarmSolveStatusEnumMapDisplayName[AlarmSolveStatusEnum.UNRESOLVED],
    value: AlarmSolveStatusEnum.UNRESOLVED
  },
  {
    label: AlarmSolveStatusEnumMapDisplayName[AlarmSolveStatusEnum.RESOLVED],
    value: AlarmSolveStatusEnum.RESOLVED
  },
  {
    label: AlarmSolveStatusEnumMapDisplayName[AlarmSolveStatusEnum.UNSOLVABLE],
    value: AlarmSolveStatusEnum.UNSOLVABLE
  }
];

const onSearch = () => {
  emits("onSearch", state.params);
};

const onResetQuery = () => {
  state.params = {};
  emits("onSearch", state.params);
};
</script>

<style scoped>
:deep(.el-form-item__label) {
  color: var(--el-text-color-primary) !important;
}
</style>
