import { DataTypeEnum, InterfaceCategoryEnum, InterfaceStatusEnum } from "@/enums";
import { IPagingReq, ISortReq } from "@/models/i-paging-req";

export interface IEipLogReq extends IPagingReq, ISortReq {
  requestTimeStart?: Date;
  requestTimeEnd?: Date;

  /** 接口类别(1采购订单拉取/2上报业务数据/3国网评分) */
  interfaceCategory?: InterfaceCategoryEnum;

  /**  状态(1成功/0异常) */
  interfaceStatus?: InterfaceStatusEnum;
  /** 数据类型 */
  dataType?: DataTypeEnum;

  /** 业务编号,示例值(1) */
  businessNo?: string;

  /**	数据ID,示例值(1)  */
  reportDataId?: string;

  /** 操作人 */
  operator?: string;
}
