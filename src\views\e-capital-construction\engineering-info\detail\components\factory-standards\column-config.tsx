import { ColumnWidth, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 出厂试验参数标准
 */
export function useColumns() {
  const { dateFormatter } = useTableCellFormatter();
  const columnsConfig = [
    {
      label: "参数标准编码",
      prop: "paramStdNo",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "参数标准名称",
      prop: "paramStdName",
      minWidth: ColumnWidth.Char8
    },
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char10,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      canEdit: false,
      isFormShow: false,
      width: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      isFormShow: false,
      slot: "operation",
      canEdit: false,
      width: TableWidth.operation
    }
  ];

  return columnsConfig;
}

/**
 * @description: 出厂试验参数标准详情
 */
export function useDetailColumns() {
  const columnsConfig = [
    {
      label: "选择",
      prop: "radio",
      slot: "radio",
      width: ColumnWidth.Char5
    },
    {
      label: "编号",
      prop: "no",
      minWidth: ColumnWidth.Char10
    },
    {
      label: "试验参数标准",
      prop: "name",
      minWidth: ColumnWidth.Char8
    }
  ];

  return columnsConfig;
}
