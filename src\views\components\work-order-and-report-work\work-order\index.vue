<template>
  <div class="flex flex-col h-[420px]">
    <div class="mb-4 flex justify-between">
      <search-bar @search-form="onFilterParamsChange" />
      <add-edit-work-order-dialog
        mode="add"
        :production-id="productionId"
        :sub-class-code="subClassCode"
        :production-no="productionNo"
        @post-save-success="requestWorkOrderList"
      />
    </div>
    <PureTable
      :loading="loading"
      showOverflowTooltip
      :pagination="pagination"
      :data="dataList"
      :columns="columns"
      @page-current-change="requestWorkOrderList"
      @page-size-change="requestWorkOrderList"
      @sort-change="sortChange"
    >
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <empty-data /> </template>
        </el-empty>
      </template>
      <template #woNo="{ row }">
        <work-order-detail-dialog :work-id="row.id">
          <el-button link type="primary">{{ row.woNo }}</el-button>
        </work-order-detail-dialog>
      </template>
      <template #workCount="{ row }">
        <report-work-list-dialog
          :production-id="productionId"
          :work-id="row.id"
          :count="row.workCount"
          :sub-class-code="subClassCode"
          :post-dialog-close="requestWorkOrderList"
        />
      </template>
      <template #operation="{ row }">
        <add-edit-report-work-dialog
          mode="add"
          work-order-selector-disabled
          :work-id="row.id"
          :production-id="productionId"
          :sub-class-code="row.subclassCode"
          @post-save-success="requestWorkOrderList"
        />
        <add-edit-work-order-dialog
          mode="edit"
          :work-id="row.id"
          :production-id="productionId"
          :sub-class-code="subClassCode"
          :production-no="productionNo"
          @post-save-success="requestWorkOrderList"
        />
        <el-button
          class="ml-2"
          v-auth="PermissionKey.form.formPurchaseWorkOrderDelete"
          v-track="TrackPointKey.FORM_PURCHASE_PWO_DELETE"
          link
          type="danger"
          @click="handleDeleteWorkOrder(row.id)"
        >
          删除
        </el-button>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { PermissionKey, TrackPointKey } from "@/consts";
import { queryWorkOrderPaging, deleteWorkOrder } from "@/api/work-order";
import { ISortReq, IWorkOrder, IWorkOrderReq } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { genProductionWorkOrderTableColumnsConfig } from "./column-config";
import { ElMessage, ElMessageBox } from "element-plus";
import { useTableConfig } from "@/utils/useTableConfig";
import SearchBar from "./search-bar.vue";
import WorkOrderDetailDialog from "../work-order-detail-dialog/index.vue";
import AddEditReportWorkDialog from "../add-edit-report-work-dialog/index.vue";
import AddEditWorkOrderDialog from "../add-edit-work-order-dialog/index.vue";
import ReportWorkListDialog from "../report-work-list-dialog/index.vue";
import { emitter as eventBus } from "@/utils/mitt";
import { handleOrderType } from "@/utils/sortByOrderType";

/**
 * 工单
 */

const props = defineProps<{
  productionId: string;
  /** 物资种类code */
  subClassCode: string;
  /** 生产订单编号 */
  productionNo: string;
}>();

const { pagination } = useTableConfig();
pagination.pageSize = 10;
const { columns } = genProductionWorkOrderTableColumnsConfig();
const loading = ref(false);
const dataList = ref<Array<IWorkOrder>>([]);
const filterParams = ref<IWorkOrderReq>({} as IWorkOrderReq);
const sortParams = ref<ISortReq>({
  orderByField: "",
  orderByType: ""
});

async function sortChange({ prop, order }) {
  if (order) {
    sortParams.value = {
      orderByField: prop,
      orderByType: handleOrderType(order)
    };
  } else {
    sortParams.value = {
      orderByField: "",
      orderByType: ""
    };
  }

  pagination.currentPage = 1;
  requestWorkOrderList();
}

/**
 * @description: 过滤参数变化
 */
function onFilterParamsChange(params: IWorkOrderReq) {
  filterParams.value = params;
  pagination.currentPage = 1;
  requestWorkOrderList();
}

/**
 * @description: 生成请求参数
 */
function genRequestParams(): IWorkOrderReq {
  return {
    productionId: props.productionId,
    pageNo: pagination.currentPage,
    pageSize: pagination.pageSize,
    ...filterParams.value,
    ...sortParams.value
  };
}

/**
 * @description: 请求工单列表
 */
const requestWorkOrderList = useLoadingFn(async () => {
  const params = genRequestParams();
  const { data } = await queryWorkOrderPaging(params);
  dataList.value = data.list;
  pagination.total = data.total;
}, loading);

/**
 * @description: 删除工单
 */
async function handleDeleteWorkOrder(id: string) {
  await ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  loading.value = true;
  const res = await deleteWorkOrder(id).finally(() => {
    loading.value = false;
  });

  if (!res.data) {
    ElMessage({ type: "warning", message: res.msg || "网络异常, 请稍后再试" });
    return;
  }
  ElMessage({ type: "success", message: "删除成功" });
  requestWorkOrderList();
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
}
onMounted(requestWorkOrderList);

defineExpose({
  /** 请求工单列表 */
  requestWorkOrderList
});
</script>

<style scoped lang="scss"></style>
