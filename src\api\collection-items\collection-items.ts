import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import { IResponse } from "@/models";
import {
  ICollectionFormIdenitifyType,
  ICollectionIdenitifyList,
  IProcessInfoList,
  IProductProcessList,
  ISaveCollectionItemsReq
} from "@/models/collection-items";
import { IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";

/** 根据品类获取品类下的生产阶段 */
export function getProductProcessByCategory(subClassCode: string) {
  const url = withApiGateway(`admin-api/system/produce/getProduceStageListByCategory/${subClassCode}`);
  return http.get<void, IResponse<Array<IProductProcessList>>>(url);
}

/** 根据生产阶段ID获取该生产阶段的工序信息 */
export function getProcessInfoByProductProcess(id: string) {
  const url = withApiGateway(`admin-api/system/ProductionStageProcess/getProductionStageProcessByProduceId/${id}`);
  return http.get<void, IResponse<Array<IProcessInfoList>>>(url);
}

/**
 * 通过工序ID获取采集配置信息
 */
export function getCollectionConfigInfoByProductSteProcessId(processId: string) {
  const url = withApiGateway(`admin-api/system/metadataModelInfo/findCategoryByProcessId/${processId}`);
  return http.get<string, IResponse<Array<IRawMaterialCheckCollectionItem>>>(url);
}

/** 获取表单控件值列表 */
export function getIdentityList() {
  const url = withApiGateway(`admin-api/system/dataTypeIdentity/list`);
  return http.get<any, IResponse<Array<ICollectionFormIdenitifyType>>>(url);
}

/**
 * 保存新增的采集项表单
 */
export function saveAddCollectionItems(paramsData: ISaveCollectionItemsReq) {
  const url = withApiGateway(`admin-api/system/metadataModelInfo/save`);
  return http.post<ISaveCollectionItemsReq, IResponse<string>>(url, { data: paramsData }, { showErrorInDialog: true });
}

/**
 * 获取采集信息的详情
 */
export function getDetailOfCollectionItemsById(id: string) {
  const url = withApiGateway(`admin-api/system/metadataModelInfo/detail/${id}`);
  return http.get<void, IResponse<ICollectionIdenitifyList>>(url);
}

/**
 * 删除采集项信息
 */
export function delCollectionItemsById(id: string) {
  const url = withApiGateway(`admin-api/system/metadataModelInfo/delete/${id}`);
  return http.delete<void, IResponse<boolean>>(url);
}
