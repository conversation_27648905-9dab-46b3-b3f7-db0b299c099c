import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import {
  IProductionProcessInspection,
  IProductionProcessInspectionForm,
  IProductionProcessInspectionReq
} from "@/models/south-grid-access";
import { IListResponse, IResponse } from "@/models";

/** 查询分页  */
export const queryProductionProcessInspection = (params: IProductionProcessInspectionReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/procedures/pageList`);
  return http.get<IProductionProcessInspectionReq, IListResponse<IProductionProcessInspection>>(url, {
    params
  });
};

/** 查询 列表  */
export const queryProductionProcessInspectionList = (data: IProductionProcessInspectionReq) => {
  const url: string = withApiGateway(`admin-api/southgrid/procedures/list`);
  return http.post<IProductionProcessInspectionReq, IResponse<Array<IProductionProcessInspection>>>(url, {
    data
  });
};

/** 根据id 查询详情 */
export const getProductionProcessInspectionById = (id: string, collectionTypeId: string) => {
  const url: string = withApiGateway(`admin-api/southgrid/procedures/${id}/${collectionTypeId}`);
  return http.get<string, IResponse<IProductionProcessInspection>>(url);
};

/** 新增 */
export const createProductionProcessInspection = (data: IProductionProcessInspectionForm) => {
  return http.post<IProductionProcessInspectionForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/procedures/create"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑 */
export const updateProductionProcessInspection = (data: IProductionProcessInspectionForm) => {
  return http.put<IProductionProcessInspectionForm, IResponse<boolean>>(
    withApiGateway(`admin-api/southgrid/procedures/${data.id}`),
    { data },
    { showErrorInDialog: true }
  );
};

/** 删除根据Id */
export const deleteProductionProcessInspectionById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/southgrid/procedures/${id}`));
};

/** 从生产订单中选用原材料  */
export const createProductionProcessInspectionFromSelectProductionOrder = (data: IProductionProcessInspectionForm) => {
  return http.post<IProductionProcessInspectionForm, IResponse<boolean>>(
    withApiGateway("admin-api/southgrid/procedures/addByProductionOrder"),
    { data }
  );
};
