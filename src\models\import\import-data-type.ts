import { ImportDataTypeEnum } from "@/enums";

export interface IImportDataType {
  id: string;
  dataType: ImportDataTypeEnum;

  //最新导入时间
  newImportTime: Date;

  //导入成功数量
  successCount: number;
  errorCount: number;
  count: number;
  templateUrl: string;
  errorExcelUrl: string;
  //批次号
  batchCode: string;
  categoryCode: string;
  subclassCode: string;

  // 当然导入任务执行状态
  resultStatus: boolean;

  /** 是否存在导入模板 */
  isExists: boolean;
}
