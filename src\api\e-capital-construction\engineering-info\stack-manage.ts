/**
 * @description: 存栈管理接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse } from "@/models";
import { StackManageModel } from "@/models";
/**
 * @description: 存栈列表
 */
export const StackManageApi = (id: String) => {
  return http.get<string, IResponse<Array<StackManageModel>>>(withApiGateway(`admin-api/ejj/project/stack/list/${id}`));
};
/**
 * @description: 创建存栈
 */
export const StackManageCreateApi = (params: StackManageModel) => {
  return http.post<StackManageModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/stack`), {
    data: params
  });
};

/**
 * @description: 编辑存栈
 */
export const StackManageEditApi = (params: StackManageModel) => {
  return http.put<StackManageModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/stack`), {
    data: params
  });
};

/**
 * @description: 删除存栈
 */
export const StackManageDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/stack/${id}`));
};
