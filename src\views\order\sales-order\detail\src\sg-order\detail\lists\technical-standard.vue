<template>
  <BaseList :columns="columns" v-bind="$attrs" />
  <!-- 编辑 -->
  <EditTechnicalStandardDialog ref="editTechnicalStandardDialog" :subclassCode="subclassCode" />
</template>

<script setup lang="ts">
import { TableColumns } from "@pureadmin/table";
import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc, StateGridOrderSyncType, TableWidth } from "@/enums";
import BaseList from "./base-list.vue";
import EditTechnicalStandardDialog from "@/views/components/state-grid-order-sync/dialogs/edit-technical-standard.vue";
import { useNormalColumns, useOperatorColumn } from "@/views/components/state-grid-order-sync/hooks";
import { useSalesStateGridOrderSyncDetailListStore } from "@/store/modules";
import { useSync } from "@/views/order/sales-order/detail/src/sg-order/detail/lists/hooks/useSync";
import { h, provide, reactive, ref } from "vue";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import { useSubclassCode } from "./hooks/useSubclassCode";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const type = StateGridOrderSyncType.TECHNICAL_STANDARD;
const { sync, syncByDataId, getSyncHistoryByDataId } = useSync();
const { normalColumns } = useNormalColumns();
const { operatorColumn } = useOperatorColumn({
  type,
  syncFn: sync,
  syncDetailFn: getSyncHistoryByDataId,
  editFn: openEditDialog
});
const { subclassCode } = useSubclassCode();
const editTechnicalStandardDialog = ref<InstanceType<typeof EditTechnicalStandardDialog>>();
const listStore = useSalesStateGridOrderSyncDetailListStore();
listStore.setType(type);

const columns: Array<TableColumns> = [
  {
    label: "生产订单号",
    prop: "ipoNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: KeywordAliasEnum.IPO_NO,
        defaultText: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
      }),
    minWidth: ColumnWidth.Char14
  },
  {
    label: "技术标准名称",
    prop: "standardName",
    minWidth: TableWidth.largeOrder
  },
  ...normalColumns,
  operatorColumn
];

provide(
  stateGridOrderSyncEditKey,
  reactive({
    syncFn: syncByDataId,
    refreshFn: listStore.refreshSyncList
  })
);

function openEditDialog(data) {
  editTechnicalStandardDialog.value.openEditDialog(data);
}
</script>

<style scoped lang="scss"></style>
