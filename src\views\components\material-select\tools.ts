import { IMaterial, IMaterialDto, IMaterialQueryParams } from "@/models";
import * as api from "@/api/material";

export function queryMaterials(params: IMaterialQueryParams) {
  if (params.keyWords) {
    params.keyWords = params.keyWords.trim();
  }
  return api.queryMaterials(params);
}

export function createMaterial(material: IMaterialDto): Promise<IMaterial> {
  return api.createMaterial(material).then(res => res.data);
}
