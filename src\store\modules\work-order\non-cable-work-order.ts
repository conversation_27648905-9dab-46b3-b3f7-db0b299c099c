import { defineStore } from "pinia";
import { ICreateWorkOrder, IDataCheck, IListResponse, IProductOrder, IWorkOrder, IWorkOrderReq } from "@/models";
import { usePurchaseOrderDetailStore, useSalesOrderDetailStore } from "@/store/modules";
import * as workOrderApi from "@/api/work-order";
import * as productOrderApi from "@/api/production-order";
import { getProductionProcessListByCategoryCodeAndProductionStage, queryDataIntegrityByWorkOrderList } from "@/api";
import { ElMessage } from "element-plus";

type State = {
  loading: boolean;
  workOrders: Array<IWorkOrder>;
  noWorkProductOrders: Array<IProductOrder>;
  workOrder: IWorkOrder;
  total: number;
};

export const useNonCableWorkOrderStore = defineStore({
  id: "cx-non-cable-work-order",
  state: (): State => ({
    loading: false,
    workOrders: [],
    noWorkProductOrders: [],
    workOrder: undefined,
    total: 0
  }),
  actions: {
    /** 通过采购订单查询非线缆的生产工单 */
    async queryNonCableWorkOrderByPurchaseOrder(params: IWorkOrderReq) {
      return workOrderApi.queryNonCableWorkOrderPaging(params).then(this.setWorkOrderFromResponse);
    },
    /** 通过销售订单查询非线缆的生产工单 */
    async queryNonCableWorkOrderBySalesOrder(params: IWorkOrderReq) {
      return workOrderApi.querySalesNonCableWorkOrderPaging(params).then(this.setWorkOrderFromResponse);
    },
    /** 没有工单的生产订单 快速创建工单 */
    async queryNoWorkProductOrders(isOnlySale = false) {
      const purchaseId: string = usePurchaseOrderDetailStore().purchaseOrderId;
      const salesId: string = useSalesOrderDetailStore().saleOrderId;
      this.noWorkProductOrders = isOnlySale
        ? (await productOrderApi.queryProductOrders(salesId, false)).data
        : (await productOrderApi.queryPurchaseProductOrders(purchaseId, false)).data;
    },

    async refreshWorkOrderDetail(id: string) {
      this.workOrder = undefined;
      this.workOrder = await workOrderApi.getWorkOrderById(id).then(res => res.data);
    },

    async queryProductOrdersByPurchase(isWork: boolean): Promise<Array<IProductOrder>> {
      const purchaseId: string = usePurchaseOrderDetailStore().purchaseOrderId;
      return productOrderApi.queryPurchaseProductOrders(purchaseId, isWork).then(res => res.data);
    },

    async queryProductOrdersBySales(isWork: boolean): Promise<Array<IProductOrder>> {
      const salesId: string = useSalesOrderDetailStore().saleOrderId;
      return productOrderApi.queryProductOrders(salesId, isWork).then(res => res.data);
    },
    async createWorkOrder(data: ICreateWorkOrder & { subClassCode?: string }) {
      const { subClassCode, ...workOrder } = data;
      let { processIds } = data;
      const { minClassCode } = workOrder;
      // 金具
      const isArmourClamp = usePurchaseOrderDetailStore()?.isArmourClamp;
      if (!processIds) {
        const processes = await getProductionProcessListByCategoryCodeAndProductionStage(
          isArmourClamp ? minClassCode : subClassCode
        ).then(res => res.data);
        processIds = processes?.length ? processes.join("-") : "";
      }

      if (!processIds) {
        ElMessage.error("未找到工序，请检查生产订单");
        return Promise.reject();
      }
      const purchaseId: string = usePurchaseOrderDetailStore().purchaseOrderId;
      return workOrderApi.createWorkOrder({ ...workOrder, processIds, purchaseId });
    },
    async deleteWorkOrder(id: string) {
      return workOrderApi.deleteWorkOrder(id);
    },
    async getWorkOrderById(id: string) {
      return workOrderApi.getWorkOrderById(id).then(res => res.data);
    },
    async updateWorkOrder(workOrder: ICreateWorkOrder) {
      return workOrderApi.editWorkOrder(workOrder);
    },
    async checkData(id: string): Promise<IDataCheck> {
      return workOrderApi.checkData(id).then(res => res.data);
    },
    setWorkOrderFromResponse(res: IListResponse<IWorkOrder>) {
      this.workOrders = res.data.list || [];
      this.total = res.data.total || 0;
      this.queryDataMissingByList(this.workOrders);
    },
    /**
     * @description: 查询工单列表每一项的数据完整性
     */
    async queryDataMissingByList(list: Array<IWorkOrder>) {
      if (!list.length) {
        return;
      }
      const idList = list.map(({ id }) => id);
      const { data } = await queryDataIntegrityByWorkOrderList(idList);
      const integrityMap = new Map<string, boolean>();
      data.forEach(({ workId, missingData }) => {
        integrityMap.set(workId, missingData);
      });
      list.forEach(item => {
        item.missingData = integrityMap.get(item.id);
      });
    }
  }
});
