<template>
  <el-form ref="formRef" class="cx-form h-[160px]" :model="form" :rules="rules" label-width="6rem">
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="工序" prop="processCode">
          <el-select v-model="form.processCode" placeholder="请选择工序" filterable clearable @change="changeProcess()">
            <el-option
              v-for="option in processOptions"
              :key="option.processCode"
              :label="option.processName"
              :value="option.processCode"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="24">
        <el-form-item label="上传附件" prop="uploadFile" class="w-full">
          <el-upload
            v-model:file-list="fileList"
            ref="uploadRef"
            :limit="1"
            :auto-upload="false"
            :on-exceed="handleExceed"
            :on-change="handleChange"
            :on-remove="removeFileList"
          >
            <template #trigger>
              <el-button type="primary" :plain="true">
                <el-icon class="mr-1" size="large"><UploadFilled /></el-icon>
                请选择
              </el-button>
            </template>
            <span class="ml-3 tip-text relative -top-1" @click.prevent>{{ FILE_TYPE_TEXT }}</span>
          </el-upload>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import {
  ElMessage,
  FormInstance,
  FormRules,
  UploadFile,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadUserFile,
  genFileId
} from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";
import { onMounted, onUnmounted, reactive, ref } from "vue";
import { EFileType } from "@/views/config/collection/types";
import { useUploadFileValidate } from "@/utils/use-upload-validate";
import { requiredMessage } from "@/utils/form";
import { UploadFileValidTypeEnum } from "@/enums/use-upload-validate";
import { FILE_TYPE_TEXT } from "@/consts";
import { IAttachDocumentRes, ISaveDocumentReq } from "@/models/attached-report";
import { useAttachReportStore, useSalesFillInDataStore } from "@/store/modules";
import { IProcess, IProductOrder } from "@/models";

const props = defineProps<{
  processDocInfo: IAttachDocumentRes;
}>();

const formRef = ref<FormInstance>();
const form = reactive<Partial<ISaveDocumentReq>>({});
const attachReportStore = useAttachReportStore();
const fillInDataStore = useSalesFillInDataStore();
const fileList = ref<UploadUserFile[]>([]);

const rules: FormRules = {
  processCode: {
    required: true,
    message: requiredMessage("工序"),
    trigger: "change"
  },
  uploadFile: {
    required: true,
    message: requiredMessage("附件"),
    trigger: "change"
  }
};
const processOptions = ref<Array<IProcess>>([]);

onMounted(async () => {
  const subClassCode = (fillInDataStore.data as IProductOrder).subClassCode;
  processOptions.value = await attachReportStore.queryProcessBysubClassCode(subClassCode);
  Object.assign(form, props.processDocInfo);
  if (props.processDocInfo) {
    fileList.value[0] = props.processDocInfo.fileInfo;
    form.fileId = props.processDocInfo.fileInfo.id;
    form.uploadFile = props.processDocInfo.fileInfo;
  }
});

const uploadRef = ref<UploadInstance>();
const currentFileType = [EFileType.PDF, EFileType.PNG, EFileType.JPG];
const { validate } = useUploadFileValidate();
const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

/** 单独校验上传组件表单验证 */
async function validUploadForm() {
  await formRef.value.validateField(["uploadFile"]);
}

/** 图片变化 */
async function handleChange(uploadFile: UploadFile) {
  const res = validate(uploadFile, currentFileType);
  if (!res.valid && res.type === UploadFileValidTypeEnum.FILE_TYPE) {
    uploadRef.value?.handleRemove(uploadFile);
    const errorMsg = `上传文件类型不正确，支持文件类型(${currentFileType.join("，")})`;
    ElMessage.error(errorMsg);
    return;
  }

  if (!res.valid && res.type === UploadFileValidTypeEnum.FILE_TYPE) {
    uploadRef.value?.handleRemove(uploadFile);
    const errorMsg = `上传文件过大,不能超过(${20}M)`;
    ElMessage.error(errorMsg);
    return;
  }
  // 校验上传文件字段
  form.uploadFile = uploadFile;
  validUploadForm();
  if (form.uploadFile) {
    form.fileId = await attachReportStore.upload(uploadFile.raw);
  }
}

/** 删除 */
function removeFileList() {
  form.uploadFile = undefined;
  form.fileId = undefined;
  fileList.value.length = 0;
  // 校验上传文件字段
  validUploadForm();
}
/** 校验表单 */
async function validateFrom() {
  if (!formRef?.value) return;
  return await formRef.value?.validate();
}
/** 获取表单数据 */
async function getFormValue() {
  if (!(await validateFrom())) {
    return Promise.reject("invalid");
  }
  return form as ISaveDocumentReq;
}

/** 编辑时更新数据 */
function patchFromValue(data) {
  Object.assign(form, { ...data });
}

onUnmounted(() => {
  form.uploadFile = {} as UploadFile;
  form.fileId = "";
  fileList.value = [];
});

function changeProcess() {
  const process: IProcess = processOptions.value.find(process => process.processCode === form.processCode);
  form.processId = process ? process.id : "";
}
defineExpose({
  getFormValue,
  patchFromValue
});
</script>

<style scoped lang="scss"></style>
