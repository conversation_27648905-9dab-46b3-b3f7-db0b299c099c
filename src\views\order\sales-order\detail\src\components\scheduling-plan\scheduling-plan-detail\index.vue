<template>
  <div class="base-info mb-5">
    <TitleBar title="基础信息" class="mb-3" />
    <el-descriptions>
      <el-descriptions-item label="排产计划编码">
        {{ schedulingPlanStore.schedulingPlanDetail?.ppNo }}
      </el-descriptions-item>
      <el-descriptions-item label="排产数量">
        <template v-if="schedulingPlanStore.schedulingPlanDetail?.amount">
          {{ formatDecimal(schedulingPlanStore.schedulingPlanDetail.amount) }}
          <DictionaryName
            class="ml-1"
            :subClassCode="schedulingPlanStore.schedulingPlanDetail.salesLine.subClassCode"
            :parentCode="MEASURE_UNIT"
            :code="schedulingPlanStore.schedulingPlanDetail.measUnit"
          />
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="计划日期">
        {{ formatPlanDate() }}
      </el-descriptions-item>
      <el-descriptions-item label="计划工期">
        {{ getPlanWorkDuration() }}
      </el-descriptions-item>
      <el-descriptions-item label="最晚交付日期">
        {{ formatDate(schedulingPlanStore.schedulingPlanDetail?.deliveryDate) }}
      </el-descriptions-item>
      <el-descriptions-item label="实际开始日期">
        {{ formatDate(schedulingPlanStore.schedulingPlanDetail?.actStartDate) }}
      </el-descriptions-item>
      <el-descriptions-item label="实际结束日期">
        {{ formatDate(schedulingPlanStore.schedulingPlanDetail?.actEndDate) }}
      </el-descriptions-item>
    </el-descriptions>
  </div>

  <div class="sale-orders mb-5">
    <TitleBar title="销售订单行项目" class="mb-3" />
    <PureTable showOverflowTooltip :columns="columns" :data="getSalesLineData()" :max-height="300">
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";
import { useSalesSchedulingPlanStore } from "@/store/modules";
import { formatDate, formatDecimal } from "@/utils/format";
import { useColumns } from "./columns";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { MEASURE_UNIT } from "@/consts";
import DictionaryName from "@/components/Dictionary/src/dictionary-name.vue";

const { columns } = useColumns();

const schedulingPlanStore = useSalesSchedulingPlanStore();
const formatPlanDate = () => {
  const planDate: Array<Date> = schedulingPlanStore.schedulingPlanDetail?.planDate;
  return `${planDate?.[0] ? formatDate(planDate?.[0]) : ""}～${planDate?.[1] ? formatDate(planDate?.[1]) : ""}`;
};

const getSalesLineData = () => {
  // 有salesLineV2 用V2 表示一个生产计划关联多个销售订单行，没有用v1
  const salesLineV2 = schedulingPlanStore?.schedulingPlanDetail?.salesLineV2 || [];
  if (salesLineV2.length) {
    return schedulingPlanStore.schedulingPlanDetail.salesLineV2;
  }
  const salesLine = schedulingPlanStore?.schedulingPlanDetail?.salesLine;
  if (!salesLine || Object.keys(salesLine).length === 0) {
    return [];
  }
  return [salesLine];
};

const getPlanWorkDuration = () => {
  if (!schedulingPlanStore.schedulingPlanDetail?.planWorkDuration) {
    return null;
  }
  return `${schedulingPlanStore.schedulingPlanDetail.planWorkDuration}天`;
};
</script>
<style scoped lang="scss"></style>
