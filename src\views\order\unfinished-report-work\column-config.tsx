import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";
import { fullDateFormat } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

/**
 * @description: 生成生产订单表格配置
 */
export function genProductionTableColumnsConfig() {
  const { dateFormatter } = useTableCellFormatter();

  const columnsConfig: TableColumnList = [
    {
      label: "报工批次号",
      prop: "productBatchNo",
      width: ColumnWidth.Char14
    },
    {
      label: "工序",
      prop: "processName",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "生产订单号",
      prop: "ipoNo",
      headerRenderer: () => (
        <KeywordAliasHeader
          code={KeywordAliasEnum.IPO_NO}
          defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
        />
      ),
      width: ColumnWidth.Char14,
      slot: "ipoNo"
    },
    {
      label: "工单编号",
      prop: "woNo",
      width: ColumnWidth.Char14
    },
    {
      label: "设备",
      prop: "deviceName",
      width: ColumnWidth.Char15
    },
    {
      label: "开始时间",
      prop: "workStartTime",
      width: ColumnWidth.Char15,
      sortable: "custom",
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "结束时间",
      prop: "workEndTime",
      width: ColumnWidth.Char15,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "报工地址",
      prop: "buyerProvince",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "操作",
      prop: "operation",
      width: ColumnWidth.Char3,
      fixed: "right",
      slot: "operation"
    }
  ];

  return { columnsConfig };
}
