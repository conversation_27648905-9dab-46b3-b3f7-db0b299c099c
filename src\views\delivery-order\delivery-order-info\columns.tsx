import { TableWidth } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";

export function useColumns() {
  const columns: TableColumnList = [
    {
      label: "供货单项目号",
      prop: "poItemNo",
      fixed: "left",
      width: TableWidth.largeOrder
    },
    {
      label: "收货方公司名称",
      prop: "receivedName",
      minWidth: TableWidth.name
    },
    {
      label: "物料编码",
      prop: "materialCode",
      width: TableWidth.name
    },
    {
      label: "物料描述",
      prop: "materialDesc",
      minWidth: TableWidth.largeName
    },
    {
      label: "物资品类",
      prop: "categoryName",
      minWidth: TableWidth.name
    },
    {
      label: "物资种类",
      prop: "subClassName",
      minWidth: TableWidth.type
    },
    {
      label: "采购数量",
      prop: "amount",
      minWidth: TableWidth.type,
      align: "right",
      cell<PERSON><PERSON>er(data: TableColumnRenderer) {
        return <span>{`${data.row.amount} ${data.row.measUnit}`}</span>;
      }
    },
    {
      label: "备注",
      prop: "remark",
      minWidth: TableWidth.largeName
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: TableWidth.operations
    }
  ];
  return { columns };
}
