<template>
  <div class="text-middle mb-1">
    <span class="mr-1">销售行</span>
    <span>{{ item?.soItemNo }}</span>
  </div>
  <div class="flex-bc">
    <div class="w-[200px] xl:w-[240px] text-secondary text-base">
      <span class="mr-1">销售订单号</span>
      <span>{{ item?.soNo }}</span>
    </div>
    <el-button
      v-auth="PermissionKey.form.formPurchaseProductionOrderBtnQuickCreate"
      v-track="TrackPointKey.FORM_PURCHASE_PO_FAST_CREATE"
      size="small"
      type="warning"
      @click="addProductOrderBySoItemNo(item)"
      >快速创建</el-button
    >
  </div>
</template>

<script setup lang="ts">
import { useSalesProductOrderStore } from "@/store/modules";
import { ISalesOrderLine } from "@/models";
import { computed } from "vue";
import { PermissionKey, TrackPointK<PERSON> } from "@/consts";

defineOptions({
  name: "NoProductionSchedulingSaleOrderLine"
});

const props = defineProps<{
  item: ISalesOrderLine;
}>();

const productOrderStore = useSalesProductOrderStore();

const item = computed(() => props.item);

const addProductOrderBySoItemNo = (data: ISalesOrderLine) => {
  productOrderStore.setSelectedSaleOrderLineId(data.id);
  const { subClassCode, materialCode, materialId } = data;
  productOrderStore.setCreateProductOrder({
    unit: data.materialUnit,
    amount: data.materialNumber,
    voltageClasses: data.voltageLevel,
    specificationModel: data.specificationType,
    subClassCode,
    materialsCode: materialCode,
    materialId,
    salesLineDetails: [data]
  });
  productOrderStore.setProductOrderModalVisible(true);
  productOrderStore.setProductOrderFormAddMode(true);
  productOrderStore.setCreateProductOrderFromBySoItemNo(true);
};
</script>
