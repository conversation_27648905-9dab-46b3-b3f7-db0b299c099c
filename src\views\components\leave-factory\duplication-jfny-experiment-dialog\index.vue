<template>
  <div class="inline-block">
    <el-button type="primary" link @click="openDialog"> 复制 </el-button>
    <el-dialog
      v-model="dialogVisible"
      title="复制试验"
      align-center
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <duplication-jfny-experiment-form ref="formRef" :meas-unit="measUnit" />
      <!-- 内容 -->
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";
import { ElMessage } from "element-plus";
import DuplicationJfnyExperimentForm from "./duplication-experiment-form.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { duplicationJfnyExperiment } from "@/api/leave-factory/jfny-experiment";

const props = defineProps<{
  groupId: string;
  /** 试验产品长度单位 */
  measUnit: string;
  /** 初始化数据 */
  initData: {
    /** 数量 */
    account: number;
    /** 产品型号 */
    productModel: string;
    /** 盘号 */
    reelNo: string;
    /** 规格型号 */
    specificationModel: string;
  };
}>();
const emits = defineEmits(["postSaveSuccess"]);

const formRef = ref<InstanceType<typeof DuplicationJfnyExperimentForm>>();
const loading = ref(false);
const dialogVisible = ref(false);

const requestSave = useLoadingFn(async () => {
  const formVal = formRef.value.getFormValue();

  const params = {
    groupId: props.groupId,
    ...formVal
  };

  const { data: result } = await duplicationJfnyExperiment(params);
  return result;
}, loading);

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const verificationResult = await formRef.value.validateForm();
  if (!verificationResult) {
    return;
  }

  const result = await requestSave();

  if (!result) {
    ElMessage({
      message: "复制失败，请稍后重试",
      type: "error"
    });
    return;
  }

  // 处理保存后续事件
  closeDialog();
  emits("postSaveSuccess");
  ElMessage({
    message: "复制成功",
    type: "success"
  });
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.initFormValue(props.initData);
  });
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}
</script>

<style scoped></style>
