import { IPurchaseOrderLineRes } from "@/models";
import { OrderSyncPriorityEnum } from "@/enums";

/** 是否为普通优先级采购订单行 */
export function isCommonPriority(orderLine: IPurchaseOrderLineRes): boolean {
  return orderLine.priority === OrderSyncPriorityEnum.COMMON_PRIORITY;
}

/** 是否为快速优先级采购订单行 */
export function isFastPriority(orderLine: IPurchaseOrderLineRes): boolean {
  return orderLine.priority === OrderSyncPriorityEnum.FAST_PRIORITY;
}
