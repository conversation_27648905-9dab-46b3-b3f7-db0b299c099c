<template>
  <el-dialog
    title="新增工单"
    class="middle"
    v-model="visible"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @open="dialogOpen"
  >
    <WorkOrderForm
      ref="workOrderForm"
      type="create"
      :parent-no="productOrder.ipoNo"
      :sub-class-code="productOrder.subClassCode"
    />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import WorkOrderForm from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/forms/work-order-form.vue";
import { IProductOrder } from "@/models";
import { computed, ref } from "vue";
import { useNonCableWorkOrderStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { useWorkOrderDefaultValueFromProductOrder } from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/hooks/useWorkOrderDefaultValueFromProductOrder";
import { useCreateWorkWorkOrderAppendProductOrderInfo } from "@/views/order/sales-order/detail/src/production-data/non-cable/work-order/hooks/useCreateWorkWorkOrderAppendProductOrderInfo";

const props = defineProps<{
  modelValue: boolean;
  productOrder?: IProductOrder;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", visible: boolean): void;
  (e: "shouldRefresh"): void;
}>();

const store = useNonCableWorkOrderStore();

const workOrderForm = ref<InstanceType<typeof WorkOrderForm>>();
const loading = ref(false);
const handleSave = useLoadingFn(save, loading);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(visible: boolean) {
    emit("update:modelValue", visible);
  }
});

function dialogOpen() {
  if (!props.productOrder) {
    return;
  }
  const workOrder = useWorkOrderDefaultValueFromProductOrder(props.productOrder);
  workOrderForm.value.initializeValue(workOrder);
}

async function save() {
  const workOrder = await workOrderForm.value.getValidValue();
  await store.createWorkOrder(useCreateWorkWorkOrderAppendProductOrderInfo(workOrder, props.productOrder));
  ElMessage.success("新增成功");
  visible.value = false;
  emit("shouldRefresh");
}
</script>

<style scoped></style>
