<template>
  <el-form ref="formRef" :model="form" :rules="rules" class="cx-form" label-width="9rem">
    <TitleBar title="企业信息" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="12">
        <el-form-item label="企业名称" prop="comName">
          <el-input placeholder="请输入企业名称" v-model="form.comName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="企业简称" prop="name">
          <el-input placeholder="请输入企业简称" v-model="form.name" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="企业地址" prop="address">
          <el-input placeholder="请输入企业地址" v-model="form.address" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="租户编号" prop="tenantKey">
          <el-input placeholder="请输入租户编号" v-model="form.tenantKey" maxlength="30" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="统一社会信用代码" prop="manufacturerId">
          <el-input placeholder="请输入 统一社会信用代码" v-model="form.manufacturerId" maxlength="30" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="0"
            :inactive-value="1"
            active-text="启用"
            inactive-text="禁用"
            inline-prompt
          />
        </el-form-item>
      </el-col>
    </el-row>

    <TitleBar title="联系人信息" class="mb-3" />
    <el-row :gutter="40" class="mb-3">
      <el-col :span="12">
        <el-form-item label="联系人姓名" prop="contactName">
          <el-input placeholder="请输入联系人姓名" v-model="form.contactName" />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <el-form-item label="联系人电话" prop="contactMobile">
          <el-input placeholder="请输入联系人手机" v-model="form.contactMobile" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="联系人邮箱" prop="contactEmail">
          <el-input placeholder="请输入联系人邮箱" v-model="form.contactEmail" />
        </el-form-item>
      </el-col>
    </el-row>

    <div v-if="displayAuthEIP()" class="mb-5">
      <div class="flex-bc mb-3">
        <TitleBar title="EIP授权配置" />
        <el-switch
          v-model="form.enableEip"
          inline-prompt
          size="large"
          active-text="开"
          inactive-text="关"
          :disabled="!license"
          @click="onTipTenantAuth()"
        />
      </div>
      <el-row :gutter="40" class="mb-3" v-if="form.enableEip">
        <el-col :span="12">
          <el-form-item label="供应商编码" prop="supplierCode">
            <el-input placeholder="请输入供应商编码" v-model="form.supplierCode" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="供应商名称" prop="supplierName">
            <el-input placeholder="请输入供应商名称" v-model="form.supplierName" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="物资种类" prop="subclassCode">
            <el-tree-select
              :props="props"
              multiple
              :data="categoryStore.categorySubclassTree"
              class="w-full"
              v-model="form.subclassCode"
              placeholder="请选择物资种类"
              clearable
              default-expand-all
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="供应商密钥" prop="publicKey">
            <el-input placeholder="请输入供应商密钥" v-model="form.publicKey" type="textarea" autosize resize="none" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div v-if="displayAuthIOT()" class="mb-5">
      <div class="flex-bc mb-3">
        <TitleBar title="IOT授权配置" />
        <el-switch
          v-model="form.enableIot"
          inline-prompt
          active-text="开"
          inactive-text="关"
          size="large"
          :disabled="!license"
          @click="onTipTenantAuth()"
        />
      </div>
      <el-row :gutter="40" class="mb-3" v-if="form.enableIot">
        <el-col :span="12">
          <el-form-item label="物资种类" prop="iotSubclassCode">
            <el-tree-select
              :props="props"
              multiple
              :data="categoryStore.categorySubclassTree"
              class="w-full"
              v-model="form.iotSubclassCode"
              placeholder="请选择物资种类"
              clearable
              default-expand-all
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户端ID" prop="iotClientId">
            <el-input placeholder="请输入客户端ID" v-model="form.iotClientId" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="客户端密码" prop="iotClientSecret">
            <el-input placeholder="请输入客户端密码" v-model="form.iotClientSecret" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="授权范围" prop="iotScopes">
            <el-input placeholder="请输入授权范围" v-model="form.iotScopes" type="textarea" autosize resize="none" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div class="mb-5" v-if="displayAuthCSG()">
      <div class="flex-bc mb-3">
        <TitleBar title="广州供电局授权配置" />
        <el-switch
          v-model="form.enableGz"
          inline-prompt
          active-text="开"
          inactive-text="关"
          size="large"
          :disabled="!license"
          @click="onTipTenantAuth()"
        />
      </div>
      <el-row :gutter="40" class="mb-3" v-if="form.enableGz">
        <el-col :span="12">
          <el-form-item label="物资种类" prop="gzSubclassCode">
            <el-tree-select
              :props="props"
              multiple
              :data="categoryStore.categorySubclassTree"
              class="w-full"
              v-model="form.gzSubclassCode"
              placeholder="请选择物资种类"
              clearable
              default-expand-all
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户端ID" prop="gzAccountName ">
            <el-input placeholder="请输入客户端ID" v-model="form.gzAccountName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户端密码" prop="gzAccountKey">
            <el-input placeholder="请输入客户端密码" v-model="form.gzAccountKey" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch, watchEffect } from "vue";
import { ElForm, ElMessage, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { ITenantForm, IBusinessLicense } from "@/models";
import { useTenantStore, useCategoryStore, useSystemAuthStore } from "@/store/modules";
import { EMAIL_REGEXP, MOBILE_REGEXP, CHANNEL_IOT, CHANNEL_EIP, CHANNEL_CSG } from "@/consts";
import TitleBar from "@/components/TitleBar";

defineExpose({
  validate,
  getValidValue
});

const props = {
  label: "categoryName",
  value: "categoryCode"
};

const tenantStore = useTenantStore();
const categoryStore = useCategoryStore();
const systemAuthStore = useSystemAuthStore();

const formRef = ref<FormInstance>();

const form = reactive<ITenantForm>({
  id: undefined,
  comName: undefined,
  name: undefined,
  tenantKey: undefined,
  manufacturerId: undefined,
  address: undefined,
  status: undefined,
  contactName: undefined,
  contactMobile: undefined,
  contactEmail: undefined,
  // EIP 使用 ----------------
  enableEip: false,
  supplierCode: undefined,
  supplierName: undefined,
  subclassCode: [],
  publicKey: undefined,
  // IOT 使用 ----------------
  enableIot: false,
  iotSubclassCode: [],
  iotClientId: undefined,
  iotClientSecret: undefined,
  iotScopes: undefined,
  // CSG广州供电局 使用 -----------------
  enableGz: false,
  gzSubclassCode: [],
  gzAccountName: undefined,
  gzAccountKey: undefined
});
const rules: FormRules = {
  comName: [{ required: true, message: requiredMessage("企业名称"), trigger: "change" }],
  name: [{ required: true, message: requiredMessage("企业简称"), trigger: "change" }],
  status: [{ required: true, message: requiredMessage("状态"), trigger: "change" }],
  contactMobile: [{ trigger: "change", message: "手机号码格式不正确", pattern: MOBILE_REGEXP }],
  contactEmail: [{ trigger: "change", message: "邮箱格式不正确", pattern: EMAIL_REGEXP }],
  // eip平台
  publicKey: [{ required: true, message: requiredMessage("供应商密钥"), trigger: "change" }],
  supplierName: [{ required: true, message: requiredMessage("供应商名称"), trigger: "change" }],
  supplierCode: [{ required: true, message: requiredMessage("供应商编码"), trigger: "change" }],
  subclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  // iot平台
  iotSubclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  iotClientId: [{ required: true, message: requiredMessage("客户端ID"), trigger: "change" }],
  iotClientSecret: [{ required: true, message: requiredMessage("客户端密码"), trigger: "change" }],
  iotScopes: [{ required: true, message: requiredMessage("授权范围"), trigger: "change" }],
  // SCG平台
  csgSubclassCode: [{ required: true, message: requiredMessage("物资种类"), trigger: "change" }],
  csgAccountName: [{ required: true, message: requiredMessage("客户端ID"), trigger: "change" }],
  csgAccountKey: [{ required: true, message: requiredMessage("客户端密码"), trigger: "change" }]
};

const disabled = ref<boolean>();
const license = ref<IBusinessLicense>();

watch(
  () => tenantStore.tenantForm,
  tenantForm => {
    if (tenantForm && Object.keys(tenantForm).length) {
      Object.assign(form, tenantForm);
    }
    disabled.value = !!tenantForm.id;
  }
);

watchEffect(async () => {
  if (!form.id) {
    return;
  }

  const latestLicense: IBusinessLicense = (await systemAuthStore.getTenantAuthInfo(form.id)).data;
  if (!latestLicense.isAuthorization) {
    return;
  }
  license.value = latestLicense;
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<ITenantForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}

const onTipTenantAuth = async () => {
  if (!license.value) {
    ElMessage.warning("请添加授权");
    return;
  }
};

const displayAuthIOT = () => {
  if (!license.value) {
    return true;
  }
  return license.value?.authorization?.integrationChannel?.includes(CHANNEL_IOT);
};

const displayAuthEIP = () => {
  if (!license.value) {
    return true;
  }
  return license.value?.authorization?.integrationChannel?.includes(CHANNEL_EIP);
};

const displayAuthCSG = () => {
  if (!license.value) {
    return true;
  }
  return license.value?.authorization?.integrationChannel?.includes(CHANNEL_CSG);
};
</script>

<style scoped></style>
