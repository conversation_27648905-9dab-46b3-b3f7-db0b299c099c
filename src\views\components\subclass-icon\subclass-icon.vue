<template>
  <FontIcon :icon="icon" v-if="icon" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import { MaterialSubclassCode } from "@/enums";

const props = defineProps<{
  subclassCode?: string;
}>();

const SUBCLASS_ICON_MAP = {
  /** 中压电缆 */
  [MaterialSubclassCode.MEDIUM_VOLTAGE_CABLE]: "icon-medium-voltage",
  /** 高压电缆 */
  [MaterialSubclassCode.HIGH_VOLTAGE_CABLE]: "icon-high-voltage",
  /** 导、地线 */
  [MaterialSubclassCode.WIRE_GROUND]: "icon-mn-conductor",
  /** OPGW-OPPC光缆 */
  [MaterialSubclassCode.OPGW_OPPC]: "icon-OPGW-OPPC",
  /** ADSS光缆 */
  [MaterialSubclassCode.ADSS]: "icon-ADSS",
  /** 电缆中间接头 */
  [MaterialSubclassCode.CABLE_JOINT]: "icon-cable-adapter",
  /** 电缆终端 */
  [MaterialSubclassCode.CABLE_TERMINAL]: "icon-cable-terminal",

  /** 变压器 */
  [MaterialSubclassCode.TRANSFORMER]: "icon-transformer",
  /** 电抗器 */
  [MaterialSubclassCode.REACTOR]: "icon-reactor",
  /** 电流互感器 */
  [MaterialSubclassCode.CURRENT_TRANSFORMER]: "icon-current-transformer",
  /** 电压互感器 */
  [MaterialSubclassCode.VOLTAGE_TRANSFORMER]: "icon-voltage-transformer",
  /** 避雷器 */
  [MaterialSubclassCode.LIGHTNING_ARRESTER]: "icon-arrester",

  /** 隔离开关 */
  [MaterialSubclassCode.DISCONNECTOR]: "icon-disconnector",

  /** 金具 */
  [MaterialSubclassCode.ARMOUR_CLAMP]: "icon-armour-clamp",

  /** 配电变压器 */
  [MaterialSubclassCode.DISTRIBUTION_TRANSFORMER]: "icon-distribution-transformer",

  /** 高压开关柜 */
  [MaterialSubclassCode.HIGH_VOLTAGE_SWITCHGEAR]: "icon-high-switch",
  /** 环网柜 */
  [MaterialSubclassCode.RING_MAIN_UNIT]: "icon-RMU",
  /** 配电箱 */
  [MaterialSubclassCode.DISTRIBUTION_BOX]: "icon-distribution-box",

  /** 低压电缆 */
  [MaterialSubclassCode.LOW_VOLTAGE_CABLES]: "icon-low-voltage",
  /** 配网导、地线 */
  [MaterialSubclassCode.DISTRIBUTION_WIRE_GROUND]: "icon-dn-conductor"
};

const icon = computed(() => {
  const subclassCode = props.subclassCode;
  return subclassCode ? SUBCLASS_ICON_MAP[subclassCode] : "";
});
</script>
