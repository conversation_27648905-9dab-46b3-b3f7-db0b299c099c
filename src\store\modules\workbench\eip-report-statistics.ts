import { defineStore } from "pinia";
import * as api from "@/api/workbench";
import { IEipRecord, IEipReportStatistics } from "@/models";

export type EipReport = {
  categoryCode: string;
  categoryName: string;
  interfaceCount: number;
  activeInterfaceCount: number;
  errorInterfaceCount: number;
};

export const useEipReportStatisticsStore = defineStore({
  id: "cx-eip-report-statistics",
  state: () => ({
    loading: false,
    activeCategoryCode: "",
    statistics: [] as Array<IEipReportStatistics>,
    reports: [] as Array<EipReport>,
    records: [] as Array<IEipRecord>
  }),
  actions: {
    refresh(loading = false) {
      this.loading = loading;
      return api
        .getEIPReportStatistics()
        .then(res => res.data)
        .then(statistics => {
          this.statistics = statistics;
          this.reports = statistics.map(s => {
            const { categoryCode, categoryName, scenes } = s;
            const interfaceCount = scenes.length;
            let activeInterfaceCount = 0;
            let errorInterfaceCount = 0;
            scenes.forEach(scene => {
              if (scene.activeState) activeInterfaceCount++;
              if (scene.interfacePrompt?.length) errorInterfaceCount++;
            });
            return {
              categoryCode,
              categoryName,
              interfaceCount,
              activeInterfaceCount,
              errorInterfaceCount
            };
          });
          this.updateRecords(this.activeCategoryCode);
        })
        .finally(() => (this.loading = false));
    },
    setActive(categoryCode: string) {
      if (categoryCode && categoryCode === this.activeCategoryCode) {
        return;
      }
      this.updateRecords(categoryCode);
    },
    updateRecords(categoryCode: string) {
      const statistics = this.statistics.find(s => s.categoryCode === categoryCode) || this.statistics[0];
      this.activeCategoryCode = statistics?.categoryCode;
      this.records = statistics.scenes || [];
    }
  }
});
