<template>
  <div class="flex flex-col p-4 mt-8 max-h-40 select-background" v-if="state.multipleSelection.length">
    <div class="ml-2 mb-2">{{ props.title }}</div>
    <el-scrollbar height="100" class="flex-1">
      <el-tag
        v-for="tag in state.multipleSelection"
        :key="tag"
        class="m-2"
        closable
        :disable-transitions="false"
        @close="handleClose(tag)"
      >
        <span class="truncate"> {{ tag }} </span>
      </el-tag>
    </el-scrollbar>
  </div>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";

const props = defineProps<{
  multipleSelection: Array<string>;
  title: string;
}>();
const state = reactive<{
  multipleSelection: Array<string>;
}>({
  multipleSelection: []
});
const emit = defineEmits<{
  (e: "deleteTag", val: string): void;
}>();

watch(
  () => props.multipleSelection,
  () => {
    state.multipleSelection = props.multipleSelection;
  },
  {
    immediate: true
  }
);
const handleClose = (tag: string) => {
  emit("deleteTag", tag);
};
</script>

<style scoped lang="scss">
.select-background {
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
}
</style>
