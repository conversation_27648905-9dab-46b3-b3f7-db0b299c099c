<template>
  <div class="exit-factory-experiment h-full">
    <Component :is="processInspectComp" />
  </div>
</template>

<script setup lang="ts">
import NormalDeliveryTest from "./normal/index.vue";
import AcDeliveryTest from "./armour-clamp/index.vue";
import { inject, shallowRef, onMounted } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const processInspectComp = shallowRef();

onMounted(() => {
  // 处理显示的组件
  handleShowComponent();
});

/**
 * 根据传入参数显示不同的组件
 */
function handleShowComponent() {
  switch (prodCtx?.detailMaterialCategory) {
    case EMaterialCategory.Normal:
      processInspectComp.value = NormalDeliveryTest;
      break;
    case EMaterialCategory.ArmourClamp:
      processInspectComp.value = AcDeliveryTest;
      break;
    default:
      break;
  }
}
</script>

<style scoped lang="scss"></style>
