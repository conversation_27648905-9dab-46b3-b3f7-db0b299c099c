export interface IEipReportStatistics {
  categoryCode: string;
  categoryName: string;
  scenes: Array<IEipRecord>;
}

export interface IEipRecord {
  id: string;
  matStageCode: string;
  /** 生产阶段(接口类型) */
  matStageName: string;
  matProcessCode: string;
  /** 工序 */
  matProcessName: string;
  /** 最后一次请求时间 */
  lastTime: string;
  /** 地址 */
  interfaceUrl: string;
  /** 活跃状态(true活跃/false异常) */
  activeState: boolean;
  /** 请求时间间隔 */
  timeDifference: string;
  /** 请求条数 */
  requestNum: number;
  /** 错误提示 */
  interfacePrompt: Array<string>;
}
