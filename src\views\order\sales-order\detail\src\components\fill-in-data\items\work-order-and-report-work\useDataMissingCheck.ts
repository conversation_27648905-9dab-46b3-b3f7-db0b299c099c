import { IReportWork, IWorkOrder } from "@/models";
import { useReportWorkStore, useSalesFillInDataStore, useWorkOrderStore } from "@/store/modules";

export function useDataMissingCheck() {
  const reportWorkStore = useReportWorkStore();
  const fillInDataStore = useSalesFillInDataStore();
  const workOrderStore = useWorkOrderStore();

  // 检测非线缆数据缺失
  function checkNonCableDataMissing(): boolean {
    const data = fillInDataStore.data as IWorkOrder;
    if (!data) {
      return false;
    }
    //判断报工是否缺少工序
    const processIdArr = data.processIds?.split("-");
    if (!Array.isArray(processIdArr) || processIdArr.length === 0) {
      return true;
    }

    const reportWorks: Array<IReportWork> = reportWorkStore.reportWorks;
    if (!Array.isArray(reportWorks) || reportWorks.length === 0) {
      return true;
    }
    const reportWorkProcessIds: Array<string> = [...new Set(reportWorks.map(x => x.processId))];
    return processIdArr.length != reportWorkProcessIds.length;
  }

  // 检测线缆数据缺失
  async function checkCableReportWorkMissingStatus() {
    // 获取生产订单id
    const productionId = fillInDataStore.dataId;
    return await workOrderStore.queryCableReportWorkMissingStatus(productionId);
  }

  return {
    checkCableReportWorkMissingStatus,
    checkNonCableDataMissing
  };
}
