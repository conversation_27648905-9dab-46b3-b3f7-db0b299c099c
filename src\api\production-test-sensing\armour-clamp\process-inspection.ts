import { withApiGateway } from "@/api/util";
import {
  IAddProductionProcess,
  IListResponse,
  IProductionProcessList,
  IResponse,
  ISearchProductionProcessList
} from "@/models";
import { http } from "@/utils/http";

/**
 * 获取对应过程下的过程检测信息
 */
export function getAcProductionProcessList(paramsData: ISearchProductionProcessList) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/getProductionAndProcessPage`);
  return http.post<ISearchProductionProcessList, IListResponse<IProductionProcessList>>(url, {
    data: paramsData
  });
}

/**
 * 获取过程检测信息的详情数据
 */
export function getAcDetialProductionProcessList(id: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/getProductionAndProcessById/${id}`);
  return http.get<void, IResponse<IProductionProcessList>>(url);
}

/**
 * 删除过程检测信息
 */
export function delAcProductionProcessList(id: string) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/delete/${id}`);
  return http.delete<string, IResponse<number>>(url);
}

/**
 * 保存新增生产过程工艺及检测
 */
export function saveAcProductionProcess(params: IAddProductionProcess) {
  const url = withApiGateway(`admin-api/business/productionProcessInfo/saveProductionAndProcess`);
  return http.post<IAddProductionProcess, IResponse<string>>(url, { data: params });
}
