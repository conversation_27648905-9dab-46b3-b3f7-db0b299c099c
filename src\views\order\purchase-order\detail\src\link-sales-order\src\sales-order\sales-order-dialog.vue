<template>
  <el-dialog
    v-model="dialog.visible"
    :title="dialog.title"
    :close-on-click-modal="false"
    align-center
    class="default"
    destroy-on-close
    :close-on-press-escape="false"
    @open="dialogOpen"
  >
    <SalesOrderForm ref="salesOrderForm" :type="dialog.type" :value="initValue" v-loading="fetchLoading" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-tooltip placement="top" v-if="isCreate() && isEipChannel">
          <el-button
            v-auth="PermissionKey.form.formPurchaseSalesCreate"
            v-track="TrackPointKey.FORM_PURCHASE_SALES_CREATE_AUTO"
            type="primary"
            @click="handleAutoGenerate"
            :loading="autoGenerateLoading"
          >
            自动生成
            <FontIcon class="ml-2" icon="icon-info" />
          </el-button>
          <template #content>
            <p>保存销售订单</p>
            <p>并针对采购订单行自动生成对应的销售订单行</p>
          </template>
        </el-tooltip>
        <el-button type="primary" :loading="saveLoading" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, h, inject, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { salesOrderDialogKey } from "../../token";
import { ISalesOrderDto } from "@/models";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { usePurchaseOrderDetailStore } from "@/store/modules";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail";
import { usePurchaseOrderLink } from "@/views/order/purchase-order/detail/src/hooks/usePurchaseOrderLink";
import SalesOrderForm from "@/views/order/purchase-order/detail/src/link-sales-order/src/sales-order/sales-order-form.vue";
import { PurchaseChannel, SalesOrderStatus } from "@/enums";
import { PermissionKey, TrackPointKey } from "@/consts";
import { useOrderSaleSyncFlagHook } from "@/views/order/hooks";

const dialog = inject(salesOrderDialogKey);
const store = usePurchaseOrderDetailSalesOrderStore();
const purchaseOrderDetailStore = usePurchaseOrderDetailStore();
const { refreshLink } = usePurchaseOrderLink();

const salesOrderForm = ref<InstanceType<typeof SalesOrderForm>>();
const initValue = ref<Partial<ISalesOrderDto>>();
const fetchLoading = ref(false);
const saveLoading = ref(false);
const autoGenerateLoading = ref(false);
const handleSave = useLoadingFn(save, saveLoading);
const handleAutoGenerate = useLoadingFn(autoGenerate, autoGenerateLoading);
const handleFetchSalesOrder = useLoadingFn(store.getSalesOrderById, fetchLoading);
const { getDefaultSyncFlag } = useOrderSaleSyncFlagHook();

const isCreate = () => dialog.type === "create";
/** 该订单是非来自国网渠道 */
const isEipChannel = computed(() => {
  if (!purchaseOrderDetailStore.purchaseOrder) {
    return false;
  }
  const channel = purchaseOrderDetailStore.purchaseOrder.syncChannel;
  return channel === PurchaseChannel.EIP;
});

function dialogOpen() {
  initializeFormValue();
}

function cancel() {
  dialog.visible = false;
}

function autoGenerateConfirm() {
  const message = h("div", { class: "flex flex-col" }, [
    h("span", { class: "text-primaryText" }, "是否自动生成销售订单行？"),
    h("span", { class: "text-secondary" }, "保存销售订单，并针对采购订单行自动生成对应的销售订单行")
  ]);
  return ElMessageBox.confirm(message, "自动生成", {
    confirmButtonText: "确认生成",
    cancelButtonText: "取消"
  });
}

async function autoGenerate() {
  if (!(await salesOrderForm.value.validate())) {
    return;
  }
  const confirm = await autoGenerateConfirm().catch(() => false);
  if (!confirm) {
    return;
  }
  const data: ISalesOrderDto = (await getFormData()) as ISalesOrderDto;
  await store.createSalesOrderAndLines(data);
  await closeAndRefreshSalesOrders("新增成功");
  refreshLink();
}

async function save() {
  if (!dialog.visible) {
    return;
  }
  const data: ISalesOrderDto | false = await getFormData();
  if (!data) {
    return;
  }
  if (dialog.type === "create") {
    await store.createSalesOrder(data);
    await closeAndRefreshSalesOrders("新增成功");
  } else if (dialog.type === "update") {
    await store.updateSalesOrder(data);
    await closeAndRefreshSalesOrders("编辑成功");
  }
  purchaseOrderDetailStore.refreshPurchaseOrder();
}

async function getFormData(): Promise<ISalesOrderDto | false> {
  const data: Partial<ISalesOrderDto> | false = await salesOrderForm.value.getFormValue().catch(() => false);
  if (!data) {
    return false;
  }
  data.purchaseId = purchaseOrderDetailStore.purchaseOrder.id;
  return data as ISalesOrderDto;
}

async function closeAndRefreshSalesOrders(message: string) {
  cancel();
  ElMessage.success(message);
  await store.refreshSalesOrders();
}

async function initializeFormValue() {
  const { type, id } = dialog;
  if (type === "create") {
    const purchaseOrder = purchaseOrderDetailStore.purchaseOrder;
    const { buyerName, prjName, conCode, categoryCode } = purchaseOrder;
    initValue.value = {
      buyerName,
      prjName,
      conCode,
      categoryCode,
      orderProgress: SalesOrderStatus.InProduction,
      matSyncFlagId: getDefaultSyncFlag()
    };
  }
  if (type === "update" && id) {
    initValue.value = await handleFetchSalesOrder(id);
  }
}
</script>

<style scoped></style>
