import {
  CheckProductionOrderListIntegrityParams,
  CheckWorkOrderListIntegrityParams,
  IBatchSyncByProductionIdsParams,
  ICheckAutoCollectProdDataParams,
  IDataIntegrityCheckParams,
  IDataIntegrityCheckTable,
  IProductionBatchTriggerScoringParams,
  IResponse,
  ProductionOrderListIntegrityItem,
  WorkOrderListIntegrityItem
} from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/**
 * @description: 物资品类的数据完整性检测的列表数据
 */
export const queryDataIntegrityCheckDataList = async (params: IDataIntegrityCheckParams) => {
  const url = withApiGateway(`admin-api/business/data-integrity-check/page`);
  return http.post<IDataIntegrityCheckParams, IResponse<IDataIntegrityCheckTable>>(url, { data: params });
};

/**
 * @description: 生成数据完整性检查
 */
export const queryReCalculateByProductionId = async (productionId: string) => {
  const url = withApiGateway(`admin-api/business/data-integrity-check/${productionId}`);
  return http.put<unknown, IResponse<boolean>>(
    url,
    {},
    {
      timeout: 0
    }
  );
};

/**
 * @description: 查询工单列表的数据完整性
 */
export const queryDataIntegrityByWorkOrderList = async (workIdList: Array<string>) => {
  const url = withApiGateway(`admin-api/business/workOrder/checkDataIntegrityByWorkId`);
  return http.post<CheckWorkOrderListIntegrityParams, IResponse<Array<WorkOrderListIntegrityItem>>>(url, {
    data: {
      wordIdList: workIdList
    }
  });
};

/**
 * @description: 查询生产订单列表的数据完整性
 */
export const queryDataIntegrityByProductionOrderList = async (orderIdList: Array<string>) => {
  const url = withApiGateway(`admin-api/business/production/checkDataIntegrityByOrderId`);
  return http.post<CheckProductionOrderListIntegrityParams, IResponse<Array<ProductionOrderListIntegrityItem>>>(url, {
    data: {
      productionIdList: orderIdList
    }
  });
};

/**
 * @description: 根据生产订单ID批量触发评分
 */
export const batchTriggerScoringByProduction = async (params: IProductionBatchTriggerScoringParams) => {
  const url = withApiGateway(`admin-api/business/data-integrity-check/trigger-by-production-order`);
  return http.post<IProductionBatchTriggerScoringParams, IResponse<boolean>>(url, {
    data: params
  });
};

/**
 * @description: 根据生产订单ID触发批量同步
 */
export const batchSyncByProduction = (params: IBatchSyncByProductionIdsParams) => {
  const url: string = withApiGateway("admin-api/business/data-integrity-check/sync-by-production-order");
  return http.post<IBatchSyncByProductionIdsParams, IResponse<boolean>>(url, {
    data: params
  });
};

/**
 * @description: 检查自动采集生产数据
 */
export const checkAutoCollectProdData = (params: ICheckAutoCollectProdDataParams) => {
  const url = withApiGateway("admin-api/business/production-report");
  return http.post<ICheckAutoCollectProdDataParams, IResponse<boolean>>(url, {
    data: params
  });
};

/**
 * @description: 导出数据完整性检查
 */
export const exportDataInterity = (params: IDataIntegrityCheckParams) => {
  const url = withApiGateway("admin-api/business/data-integrity-check/export");
  return http.post<IDataIntegrityCheckParams, IResponse<boolean>>(url, {
    data: params
  });
};
