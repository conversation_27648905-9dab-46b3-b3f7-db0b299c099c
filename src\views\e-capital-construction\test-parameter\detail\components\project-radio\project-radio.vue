<template>
  <div v-loading="loading" class="test-project flex items-center">
    <div class="label">试验项目：</div>
    <el-radio-group v-model="testItem" size="default" class="flex-1 gap-2" @change="handleChange">
      <el-radio border v-for="item in collectionList" :key="item.code" :label="item.code">{{ item.name }}</el-radio>
    </el-radio-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { CollectionModel } from "@/models";
import { getCollectionListApi } from "@/api/e-capital-construction/test-parameter/index";

const collectionList = ref<CollectionModel[]>([]);
const route = useRoute();
const loading = ref(false);

/**
 * @description: 请求详情数据
 */
const initCollection = useLoadingFn(async () => {
  const { data } = await getCollectionListApi(route.query.id as string);
  collectionList.value = data;
  testItem.value = collectionList.value[0]?.code;
  emits("change", testItem.value);
}, loading);

onMounted(() => {
  initCollection();
});

const testItem = ref();

const emits = defineEmits<{
  (event: "change", value: string): void;
}>();

const handleChange = (val: string) => {
  emits("change", val);
};
</script>

<style scoped lang="scss">
.label {
  font-size: var(--el-font-size-base);
}
</style>
