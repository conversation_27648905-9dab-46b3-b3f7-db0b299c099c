<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="right" label-width="100px">
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="原材料检验" prop="rawMaterialWeight">
          <percentage-input v-model="form.rawMaterialWeight" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生产过程" prop="processWeight">
          <percentage-input v-model="form.processWeight" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="出厂试验" prop="experimentWeight">
          <percentage-input v-model="form.experimentWeight" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="工艺稳定性" prop="processStabilityWeight">
          <percentage-input v-model="form.processStabilityWeight" />
        </el-form-item>
      </el-col>
    </el-row>
    <div v-show="showError" class="text-danger">数据权重总和需等于100%</div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import PercentageInput from "./percentage-input.vue";

/**
 * 新增/编辑/复制 质量规范 的 数据权重表单
 */

interface FormType {
  /** 原材料检 */
  rawMaterialWeight: string;
  /** 生产过程 */
  processWeight: string;
  /** 出厂试验 */
  experimentWeight: string;
  /** 工艺稳定性 */
  processStabilityWeight: string;
}

const form = reactive({
  rawMaterialWeight: "",
  processWeight: "",
  experimentWeight: "",
  processStabilityWeight: ""
});

const showError = ref(false);

const formRef = ref<FormInstance>();

/**
 * @description: 权重字符串转数字
 */
const parseWeight = (weight: string): number => {
  const parsed = parseFloat(weight);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * @description: 检查权重总计
 */
function checkTotalWeight() {
  return (
    parseWeight(form.rawMaterialWeight) +
      parseWeight(form.processWeight) +
      parseWeight(form.experimentWeight) +
      parseWeight(form.processStabilityWeight) ===
    100
  );
}

const rules: FormRules = {
  rawMaterialWeight: [{ required: true, message: "请输入原材料检验权重", trigger: "change" }],
  processWeight: [{ required: true, message: "请输入生产过程权重", trigger: "change" }],
  experimentWeight: [{ required: true, message: "请输入出厂试验权重", trigger: "change" }],
  processStabilityWeight: [{ required: true, message: "请输入工艺稳定性权重", trigger: "change" }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  const valid = await formRef.value.validate(valid => valid);
  if (!valid) {
    return false;
  }
  const isPass = checkTotalWeight();
  if (!isPass) {
    showError.value = true;
    return false;
  }
  return true;
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: FormType) {
  form.rawMaterialWeight = v.rawMaterialWeight;
  form.processWeight = v.processWeight;
  form.experimentWeight = v.experimentWeight;
  form.processStabilityWeight = v.processStabilityWeight;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});
</script>

<style scoped></style>
