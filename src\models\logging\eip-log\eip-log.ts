import { DataTypeEnum, InterfaceCategoryEnum, InterfaceStatusEnum, PullTypeEnum } from "@/enums";

export interface IEipLog {
  id: string;

  /** 租户Id */
  tenantId: string;

  /** 接口类别(1采购订单拉取/2上报业务数据/3国网评分) */
  interfaceCategory: InterfaceCategoryEnum;

  /** 拉取方式(1手动/2自动) */
  pullType: PullTypeEnum;

  /**  状态(1成功/0异常) */
  interfaceStatus: InterfaceStatusEnum;

  /** 可能原因  */
  possibleReason: string;

  /** 操作人 */
  operator: string;

  /** 采购订单号 */
  poNo: string | number;

  /** 采购订单行号 */
  poItemNo: string | number;

  /** 数据类型 */
  dataType: DataTypeEnum;

  /** 业务编号  */
  businessNo: string;

  /** 上报数据ID */
  reportDataId: string | number;

  /** 	请求时间 */
  requestTime: Date;

  /** 请求报文 */
  requestContent: string;

  /** 响应时间 */
  responseTime: Date;

  /** 响应报文 */

  responseContent: string;

  /** 	耗时 */
  elapsedTime: number;

  /** 拉取条数 */
  pullSize: number;

  /** 采购订单行ID */
  purchaseOrderItemId: string;

  /**采购订单ID */
  purchaseId: string;
  /** 请求头 */
  requestHeader: string;
}
