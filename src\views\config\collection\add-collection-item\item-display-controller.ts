import { EControlType, WaveRoseAxisEnum } from "@/enums/dynamic-form.enum";
import { ISaveCollectionItemsReq } from "@/models/collection-items";
import { computed } from "vue";

export function getItemDispalyController(collectionForm: ISaveCollectionItemsReq) {
  /** 是否显示选项列表 */
  const isShowAddOptions = computed(() => {
    return (
      collectionForm.identityCode === EControlType.SelectControl ||
      collectionForm.identityCode === EControlType.RadioControl ||
      collectionForm.identityCode === EControlType.WaveRoseControl
    );
  });

  /**
   * 是否显示文本限制校验
   */
  const isShowTextFormItem = computed(() => {
    return collectionForm.identityCode === EControlType.TextControl;
  });

  /** 校验是否显示填写精度的表单 */
  const isShowNumberFormItem = computed(() => {
    return collectionForm.identityCode === EControlType.NumberTextControl;
  });

  /** 是否显示选择日期框的表单 */
  const isShowDateTypeFormItem = computed(() => {
    return collectionForm.identityCode === EControlType.DateControl;
  });

  /** 是否显示文件的选择框 */
  const isShowFileTypeFormItem = computed(() => {
    return collectionForm.identityCode === EControlType.FileControl;
  });

  /** 是否显示最大值 */
  const isShowMaxValueInput = computed(() => {
    return collectionForm.validMaxValue;
  });

  /** 是否显示最大值 */
  const isShowMinValueInput = computed(() => {
    return collectionForm.validMinValue;
  });

  /** 是否可以选择X轴 */
  const xAxisOptionDisabled = computed(() => {
    const { waveFormConfig } = collectionForm;
    if (!waveFormConfig.length) {
      return false;
    }
    if (waveFormConfig.some(({ valueType }) => valueType === WaveRoseAxisEnum.XAxis)) {
      return true;
    }
    return false;
  });

  return {
    xAxisOptionDisabled,
    isShowAddOptions,
    isShowTextFormItem,
    isShowNumberFormItem,
    isShowDateTypeFormItem,
    isShowFileTypeFormItem,
    isShowMaxValueInput,
    isShowMinValueInput
  };
}
