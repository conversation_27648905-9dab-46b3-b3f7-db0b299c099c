<template>
  <div class="wrapper grid mt-3.5 h-full" :style="gridStyle">
    <div class="overflow-hidden flex flex-col gap-1 min-h-[60px]">
      <el-row class="w-full" v-for="field in props.fields" :key="field.key">
        <el-col :span="6">
          <label class="text-sm text-secondary mr-2 whitespace-nowrap">{{ field.title }}</label>
        </el-col>
        <el-col :span="18" class="relative">
          <ShowTooltip className="text-right text-base w-full" :content="getValue(field.key)" v-if="!field.showAll" />
          <div v-else class="show-all text-base flex justify-end absolute w-full">
            <span class="">{{ getValue(field.key) }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import ShowTooltip from "@/components/ShowTooltip";
import { computed } from "vue";

const props = defineProps<{
  fields: Array<{ title: string; key: string; showAll?: boolean }>;
  data: Record<string, any>;
  collapse: boolean;
}>();

const gridStyle = computed(() => ({ "grid-template-rows": props.collapse ? "0fr" : "1fr" }));

function getValue(key: string) {
  return props.data?.[key] || "--";
}
</script>

<style scoped lang="scss">
.wrapper {
  border-radius: 3px;
  padding: 8px 11px;
  background: rgba(108, 108, 108, 0.05);
  transition: grid-template-rows 0.3s linear;
}
</style>
