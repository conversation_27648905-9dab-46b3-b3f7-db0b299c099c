import { ICollectionItem } from "../material";

export interface IProductionProcessInspectionForm {
  id?: string;
  /**
   * 检验批次号
   */
  inspectBatchNo?: string;
  /**
   * 生产单据编码
   */
  productionOrderNo?: string;
  /**
   * 检验日期
   */
  inspectDate?: string;
  /**
   * 工序编码
   */
  procedureCode?: string;

  /** 选中生产单据编码 */
  formProductionOrderNo?: string;

  /**  监造计划Id */
  supervisionPlanId?: string;

  /** 检验数据列表 */
  inspectDataList?: Array<ICollectionItem>;
}
