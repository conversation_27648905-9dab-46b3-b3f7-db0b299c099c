import { http } from "@/utils/http";
import {
  ICategory,
  ICategoryContainProcess,
  IListNoPageResponse,
  IOrderSubclassParams,
  IResponse,
  ISubclassMinClassCode
} from "@/models";
import { withApiGateway } from "../util";
import { ICategoryInfoFindTenantList, ICategoryInfoTree } from "@/models";

export const getCategories = () => {
  const url = withApiGateway("admin-api/business/category/category");
  return http.get<void, IListNoPageResponse<ICategory>>(url);
};

export const getAllSubclasses = () => {
  const url = withApiGateway("admin-api/business/category/getSubclassAll");
  return http.get<void, IListNoPageResponse<ICategory>>(url);
};

export const querySubclassesByCategory = (categoryCode: string) => {
  const url = withApiGateway(`admin-api/business/category/subclass/${categoryCode}`);
  return http.get<void, IListNoPageResponse<ICategory>>(url);
};

export const querySubclassesByOrderId = (data: IOrderSubclassParams) => {
  const url = withApiGateway("admin-api/business/production/category");
  return http.post<IOrderSubclassParams, IListNoPageResponse<ICategory>>(url, { data });
};

/** 查询分类下 包含工序 */
export const queryCategoryContainProcessTree = () => {
  return http.get<void, IResponse<Array<ICategoryContainProcess>>>(
    withApiGateway("admin-api/system/categoryInfo/findTenantCategory")
  );
};

/** 查询品类下 包含物资种类 */
export const queryCategoryInfoTenantCategoryList = () => {
  return http.get<void, IResponse<Array<ICategoryInfoFindTenantList>>>(
    withApiGateway("admin-api/system/categoryInfo/findTenantCategoryList")
  );
};

/** 查询金具物资子种类 */
export const querySubclassMinClassCodeList = (subClassCode: string) => {
  return http.get<void, IResponse<Array<ISubclassMinClassCode>>>(
    withApiGateway(`admin-api/business/category/minClassCode-by-subClassCode/${subClassCode}`)
  );
};

/** 查询物资种类 */
export const queryCategorySubclassTreeByTenantId = (tenantId: string): Promise<IResponse<Array<ICategoryInfoTree>>> => {
  return http.get(withApiGateway(`admin-api/system/categoryInfo/category-subclass-tree/${tenantId}`));
};

/** 查询当前租户的物资种类及其下属品类（树形） */
export const queryTenantCategoryCodeTree = (): Promise<IResponse<Array<ICategoryInfoTree>>> => {
  return http.get(withApiGateway("admin-api/business/category/getCategoryCodeByTenant"));
};

/**
 * @description: 查询物资品类树
 */
export const queryCategoryInfoTree = (): Promise<IResponse<Array<ICategoryInfoTree>>> => {
  return http.get(withApiGateway("admin-api/system/categoryInfo/category-subclass-tree"));
};
