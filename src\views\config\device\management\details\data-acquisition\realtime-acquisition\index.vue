<!-- 生产设备—数据采集-实时数据组件 -->
<template>
  <div class="h-full">
    <el-scrollbar v-loading="loading">
      <section v-if="chartList.length">
        <!-- 实时数据 -->
        <div class="flex flex-row flex-wrap mr-3">
          <el-card
            v-for="(item, index) in chartList"
            :key="item.collectPoint"
            class="lg:basis-[31%] sm:basis-[45%] basis-[45%] shadow-md mb-5 mr-5 min-w-96"
            @click="toggleDialog(index)"
          >
            <div class="h-[200px] flex flex-col">
              <div class="pl-2">{{ item.collectName }}</div>
              <div class="flex-1">
                <chart :x-axis-data="item.xAxisData" :y-axis-data="item.yAxisData" />
              </div>
            </div>
          </el-card>
        </div>
      </section>
      <CxEmpty v-else />
    </el-scrollbar>

    <el-dialog
      :title="focusIndex >= 0 ? chartList[focusIndex].collectName : '--'"
      align-center
      class="middle"
      destroy-on-close
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="h-[280px]">
        <chart :x-axis-data="chartList[focusIndex].xAxisData" :y-axis-data="chartList[focusIndex].yAxisData" />
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import CxEmpty from "@/components/CxEmpty";
import { useDataAcauisitionStore } from "@/store/modules/device";
import { useDeviceStore } from "@/store/modules";
import type { IDeviceAcquisition, IDeviceDataAcquisitionReq, IDevicePointData } from "@/models/device";
import dayjs from "dayjs";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCancelHttp } from "@/utils/http/cancel-http";
import Chart from "./chart.vue";
import { formatDate } from "@/utils/format";
import { hoursFormat } from "@/consts";

export interface IExperimentData {
  /** 采集项编码 */
  collectPoint: string;
  /** 采集项名称 */
  collectName: string;
  /** x轴数据 */
  xAxisData: Array<string>;
  /** y轴数据 */
  yAxisData: Array<string>;
}
const LIMIT_SIZE = 100;

const dialogVisible = ref(false);
let interval = null;
const loading = ref(false);
const dataAcauisitionStore = useDataAcauisitionStore();
const deviceStore = useDeviceStore();
// echarts列表数据
const chartList = ref<Array<IExperimentData>>([]);
const cancelHttp = useCancelHttp();
const focusIndex = ref(-1);
// 试验数据列表
const experimentList = ref<Array<IExperimentData>>([]);

/**
 * @description: 切换弹窗显示
 */
const toggleDialog = (index: number) => {
  dialogVisible.value = !dialogVisible.value;
  focusIndex.value = index;
};

/**
 * @description: 获取实时数据
 */
const requestRealtimeData = useLoadingFn(async () => {
  cancelHttp.abortAndCreateNewController();
  await generateExperimentList();
  const params: IDeviceDataAcquisitionReq = {
    deviceCode: deviceStore.deviceDetail.deviceCode,
    timeFrom: dayjs().subtract(5, "minute").toISOString(),
    timeEnd: dayjs().toISOString()
  };
  await combinationHistoryList(params);
}, loading);

/**
 * @description: 生成实验数据列表
 */
const generateExperimentList = async () => {
  // 采集点列表
  const pointList: Array<IDeviceAcquisition> = (await dataAcauisitionStore.queryDeviceCodePoints()) || [];
  // 试验数据列表
  if (!pointList.length) {
    return;
  }
  // 构建数据结构
  pointList.forEach(point =>
    experimentList.value.push({
      collectPoint: point.no,
      collectName: point.name,
      xAxisData: [],
      yAxisData: []
    })
  );
};

/**
 * @description: 计算出最新数据列表
 */
const calcNewList = (list: IDevicePointData[]) => {
  const tempItem = experimentList.value[0];
  // 当前数据长度
  const curLength = tempItem ? experimentList.value[0].xAxisData.length : 0;
  // 获取数据起点的时间戳
  const dataTimeBegin = curLength
    ? dayjs(experimentList.value[0].xAxisData[curLength - 1]).valueOf()
    : dayjs().subtract(1, "h").valueOf();

  // 获取时间起点以后的数据
  return list.filter(({ time }) => dayjs(time).valueOf() > dataTimeBegin);
};

/**
 * @description: 计算要截去的数据长度
 */
const calcSliceSize = () => {
  if (experimentList.value.length) {
    return experimentList.value[0].xAxisData.length - LIMIT_SIZE;
  }
  return LIMIT_SIZE;
};

/**
 * @description: 拼接原始数据
 */
const combinationRawData = (list: IDevicePointData[]) => {
  if (list.length) {
    const newList = calcNewList(list);
    const sliceSize = calcSliceSize();
    // 补充最新的码点数据
    experimentList.value.forEach(experiment => {
      // 截去掉超出长度限制的数据
      if (sliceSize > 0) {
        experiment.xAxisData = experiment.xAxisData.slice(sliceSize);
        experiment.yAxisData = experiment.yAxisData.slice(sliceSize);
      }
      // 补充最新数据集
      newList.forEach(({ points, time }) => {
        if (Object.keys(points).includes(experiment.collectPoint) && points[experiment.collectPoint]) {
          experiment.yAxisData.push(points[experiment.collectPoint]);
          experiment.xAxisData.push(time);
        }
      });
    });
  }
  chartList.value = experimentList.value.map(item => {
    const obj = { ...item };
    obj.xAxisData = obj.xAxisData.map(time => formatDate(time, hoursFormat));
    return obj;
  });
};

/**
 * @description: 拼装实时数据
 */
const combinationRealTimeList = async (params: IDeviceDataAcquisitionReq) => {
  const list = await dataAcauisitionStore.deviceRealTimeDataAcquisition(params, cancelHttp.signal.value);
  combinationRawData(list);
};

/**
 * @description: 拼装历史数据
 */
const combinationHistoryList = async (params: IDeviceDataAcquisitionReq) => {
  const charts = (await dataAcauisitionStore.deviceHistoryDataAcquisition(params, cancelHttp.signal.value)) || [];
  combinationRawData(charts);
};

onMounted(async () => {
  await requestRealtimeData();
  // 第一次数据查询完成后，使用定时器进行轮询
  await combinationRealTimeList({ deviceCode: deviceStore.deviceDetail.deviceCode });
  interval = setInterval(() => {
    combinationRealTimeList({ deviceCode: deviceStore.deviceDetail.deviceCode });
  }, 5000);
});

onUnmounted(() => {
  cancelHttp.abort();
  clearInterval(interval);
  interval = null;
});
</script>

<style scoped lang="scss"></style>
