<template>
  <div>
    <div class="flex py-3 px-14 bg-slate-100">
      <el-row class="w-full">
        <el-col :span="4">
          <div class="content">
            <div class="label text-center mb-1">工序</div>
            <div class="value text-center">{{ detail.processName }}</div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="content">
            <div class="label text-center mb-1">设备</div>
            <div class="value text-center">{{ detail.deviceName }}</div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="content">
            <div class="label text-center mb-1">开始时间</div>
            <div class="value text-center">
              {{ formatDate(detail.workStartTime, fullDateFormat) }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="content">
            <div class="label text-center mb-1">结束时间</div>
            <div class="value text-center">
              {{ formatDate(detail.workEndTime, fullDateFormat) }}
            </div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="content">
            <div class="label text-center mb-1">报工地址</div>
            <div class="value text-center">{{ detail.buyerProvince }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <el-scrollbar class="m-2" :height="450" v-loading="loading">
      <section v-if="chartList.length">
        <el-row :gutter="40">
          <el-col :span="12" v-for="(item, index) in chartList" :key="item.collectPoint">
            <el-card class="shadow-md mb-5 min-w-96" @click="toggleDialog(index)">
              <div class="h-[200px] flex flex-col">
                <div class="pl-2">{{ item.collectName }}</div>
                <div class="flex-1">
                  <chart :x-axis-data="item.xAxisData" :y-axis-data="item.yAxisData" />
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </section>
      <CxEmpty v-else />
    </el-scrollbar>
    <el-dialog
      :title="focusData ? focusData.collectName : '--'"
      align-center
      class="middle"
      destroy-on-close
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="h-[280px]">
        <chart :x-axis-data="focusData.xAxisData" :y-axis-data="focusData.yAxisData" :data-zoom="true" />
      </div>
    </el-dialog>
    <auto-acquisition-dialog ref="historyDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { IDeviceDataAcquisitionReq, IReportWork, IDevicePointData } from "@/models";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import { getReportWorkById } from "@/api/report-work";
import { queryDeviceAcquisitionAll } from "@/api/device";
import { useCancelHttp } from "@/utils/http/cancel-http";
import { deviceHistoryDataAcquisition } from "@/api/device/data-acquisition";
import Chart from "./chart.vue";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import AutoAcquisitionDialog from "@/views/components/auto-acquisition-dialog/index.vue";

interface IExperimentData {
  /** 采集项编码 */
  collectPoint: string;
  /** 采集项名称 */
  collectName: string;
  /** x轴数据 */
  xAxisData: Array<string>;
  /** y轴数据 */
  yAxisData: Array<string>;
}

const props = defineProps<{
  /** 报工id */
  reportWorkId;
}>();

const dialogVisible = ref(false);
const loading = ref(false);
const detail = ref<IReportWork>({} as IReportWork);
const cancelHttp = useCancelHttp();
const focusData = ref<IExperimentData | null>(null);
// echarts列表数据
const chartList = ref<Array<IExperimentData>>([]);
// 试验数据列表
const experimentList = ref<Array<IExperimentData>>([]);
const isRealtime = computed(() => !detail.value.workEndTime);

const historyDialogRef = ref<InstanceType<typeof AutoAcquisitionDialog>>();

/**
 * @description: 切换弹窗显示
 */
const toggleDialog = (index: number) => {
  if (isRealtime.value) {
    dialogVisible.value = !dialogVisible.value;
    focusData.value = chartList.value[index];
  } else {
    if (!historyDialogRef.value || !detail.value.deviceId) {
      return;
    }
    historyDialogRef.value.openAccquisitionDialog(
      detail.value.deviceId,
      detail.value.deviceCode,
      detail.value.processId,
      chartList.value[index].collectPoint,
      formatDate(detail.value.workStartTime, fullDateFormat),
      formatDate(detail.value.workEndTime, fullDateFormat) || formatDate(Date.now(), fullDateFormat)
    );
  }
};

/**
 * @description: 生成实验数据列表
 */
const generateExperimentList = async (deviceId: string) => {
  // 采集点列表
  const { data: pointList = [] } = await queryDeviceAcquisitionAll(deviceId);
  // 试验数据列表
  if (!pointList.length) {
    return;
  }
  // 构建试验数据列表数据结构
  pointList.forEach(point =>
    experimentList.value.push({
      collectPoint: point.no,
      collectName: point.name,
      xAxisData: [],
      yAxisData: []
    })
  );
};

/**
 * @description: 拼装历史数据
 */
const combinationHistoryList = async (params: IDeviceDataAcquisitionReq) => {
  const { data } = await deviceHistoryDataAcquisition(params, cancelHttp.signal.value);
  const chartsRawData = data.items || [];
  const pointsList = data.points;
  combinationRawData(chartsRawData, pointsList);
};

/**
 * @description: 拼接原始数据
 */
const combinationRawData = (list: IDevicePointData[], pointList: Array<string>) => {
  if (list.length) {
    experimentList.value = experimentList.value.filter(({ collectPoint }) => pointList.includes(collectPoint));
    experimentList.value.forEach(experiment => {
      experiment.xAxisData = [];
      experiment.yAxisData = [];
      list.forEach(({ points, time }) => {
        if (Object.keys(points).includes(experiment.collectPoint) && points[experiment.collectPoint]) {
          experiment.yAxisData.push(points[experiment.collectPoint]);
          experiment.xAxisData.push(time);
        }
      });
    });
  }
  chartList.value = experimentList.value.map(item => {
    const obj = { ...item };
    obj.xAxisData = obj.xAxisData.map(time => formatDate(time, fullDateFormat));
    return obj;
  });
};

const requestWorkDetail = async (reportWorkId: string) => {
  // 详情数据
  const { data } = await getReportWorkById(reportWorkId);
  detail.value = data;
};

onMounted(async () => {
  try {
    loading.value = true;
    await requestWorkDetail(props.reportWorkId);
    await generateExperimentList(detail.value.deviceId);

    if (!detail.value.dataPrepared) {
      loading.value = false;
      chartList.value = experimentList.value;
      return;
    }

    await combinationHistoryList({
      deviceCode: detail.value.deviceCode,
      processId: detail.value.processId,
      timeFrom: `${detail.value.workStartTime}`,
      timeEnd: `${detail.value.workEndTime}` || new Date().toISOString()
    });
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped lang="scss">
.label {
  color: var(--el-text-color-secondary);
}

.value:empty:after {
  content: var(--default-value);
}
</style>
