<template>
  <div>
    <form-title-bar :title="isReachStandardRate ? '达标率得分' : '数值范围'">
      <template #right>
        <el-button type="primary" :icon="Plus" @click="handleAddRawRecord"> 新增 </el-button>
      </template>
    </form-title-bar>
    <PureTable row-key="id" :data="list" :columns="columnsConfig" max-height="300">
      <template #range="{ row }">
        <input-formual v-model="row.range" :is-percentage="isReachStandardRate" />
      </template>
      <template #score="{ row, $index }">
        <el-input type="number" clearable v-model="row.score" @input="handleInput($event, $index)" />
      </template>
      <template #opertion="{ $index }">
        <el-button type="danger" link @click="handleDelete($index)"> 删除 </el-button>
      </template>
    </PureTable>
    <div>
      <div v-if="showEmptyTip && list.length === 0" class="text-danger">请添加检测标准</div>
    </div>
    <div v-if="emptyFormualList.length > 0">
      <div class="text-danger">标准{{ emptyFormualList.map(index => index + 1).join("、") }}数值范围存在空缺</div>
    </div>
    <div v-if="errorFormulaComparisionList.length > 0">
      <div class="text-danger">
        标准{{ errorFormulaComparisionList.map(index => index + 1).join("、") }}存在数值范围比较关系错误
      </div>
    </div>
    <div v-if="errorScoreList.length > 0">
      <div class="text-danger">
        标准{{ errorScoreList.map(index => index + 1).join("、") }}存在分值空缺或大于基础信息分值
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRaw, watch } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import FormTitleBar from "../form-title-bar.vue";
import { genQualitySpecificationTableColumnsConfig } from "./column-config";
import InputFormual from "@/components/input-formual/index.vue";
import { ComparisonSymbolEnum, InputFormualModelVal } from "@/components/input-formual/types";
import { JudgeTypeEnum } from "@/enums/quality-specification";

/**
 * 新增/编辑检测标准 —— 范围列表表单
 */

const props = withDefaults(
  defineProps<{
    /** 判定方式 */
    judgmentType?: JudgeTypeEnum;
    /** 分值上限 */
    limitScore?: string;
  }>(),
  {
    limitScore: "100"
  }
);

const { columnsConfig } = genQualitySpecificationTableColumnsConfig();

const list = ref<
  Array<{
    /** 判断公式 */
    range: InputFormualModelVal;
    /** 分值 */
    score: string;
  }>
>([]);

const showEmptyTip = ref(false);
/** 空缺公式列表 */
const emptyFormualList = ref<Array<number>>([]);
/** 错误分数列表 */
const errorScoreList = ref<Array<number>>([]);
/** 公式比较关系错误列表 */
const errorFormulaComparisionList = ref<Array<number>>([]);

const midValue = computed(() => {
  switch (props.judgmentType) {
    case JudgeTypeEnum.ResultValue:
      return "x";
    case JudgeTypeEnum.ReachStandardRate:
      return "q";
    case JudgeTypeEnum.MeanSquareError:
      return "D(x)";
    default:
      return "";
  }
});

/**
 * 当前是否是达标率
 */
const isReachStandardRate = computed(() => {
  return props.judgmentType === JudgeTypeEnum.ReachStandardRate;
});

const maxLength = 20;

/**
 * @description: 输入时矫正长度
 */
function handleInput(s: string, index: number) {
  if (s.length > maxLength) {
    list.value[index]["score"] = s.slice(0, maxLength);
  }
}

/**
 * @description: 添加一条记录
 */
function handleAddRawRecord() {
  list.value.push({
    range: {
      leftValue: "",
      leftSymbol: ComparisonSymbolEnum.LessThanValue as number,
      midValue: midValue.value,
      rightSymbol: ComparisonSymbolEnum.LessThanValue as number,
      rightValue: ""
    },
    score: ""
  });
}

/**
 * @description: 删除记录
 */
function handleDelete(index: number) {
  list.value.splice(index, 1);
}

/**
 * @description: 检查公式比较关系
 */
function handleCheckFormulaComparision() {
  errorFormulaComparisionList.value = [];
  list.value.forEach((item, index) => {
    if (item.range.leftValue && item.range.rightValue) {
      if (
        (item.range.leftSymbol === ComparisonSymbolEnum.LessThanValue ||
          item.range.rightSymbol === ComparisonSymbolEnum.LessThanValue) &&
        +item.range.leftValue >= +item.range.rightValue
      ) {
        errorFormulaComparisionList.value.push(index);
      }
    }
  });
}

/**
 * @description: 校验分数
 */
function handleCheckScore() {
  errorScoreList.value = [];
  list.value.forEach((item, index) => {
    if (!item.score) {
      errorScoreList.value.push(index);
    } else if (+item.score > +props.limitScore) {
      errorScoreList.value.push(index);
    }
  });
}

/**
 * @description: 校验空缺公式
 */
function handleCheckFormula() {
  emptyFormualList.value = [];
  list.value.forEach((item, index) => {
    const result = (item.range.leftValue || item.range.rightValue) && item.range.midValue;
    if (!result) {
      emptyFormualList.value.push(index);
    }
  });
}

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (list.value.length === 0) {
    showEmptyTip.value = true;
    return false;
  } else {
    showEmptyTip.value = false;
  }
  // 校验空缺公式
  handleCheckFormula();
  handleCheckScore();
  handleCheckFormulaComparision();
  return (
    emptyFormualList.value.length === 0 &&
    errorScoreList.value.length === 0 &&
    errorFormulaComparisionList.value.length === 0
  );
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: any) {
  list.value = v;
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return toRaw(list.value);
}

watch(midValue, (v, oldV) => {
  // 如果之前没有选定判定方式，则将所有记录的midValue设置为当前的midValue
  if (!oldV && v) {
    list.value.forEach(item => {
      item.range.midValue = v;
    });
  }
  // 如果之前选定了判定方式，为判定方式发生了变化，则清空所有判定标准
  if (oldV) {
    list.value = [];
  }
});

watch(
  () => props.limitScore,
  s => {
    if (s && errorScoreList.value.length > 0) {
      handleCheckScore();
    }
  }
);

watch(isReachStandardRate, v => {
  if (v) {
    columnsConfig.value[1].label = "达标率范围";
  } else {
    columnsConfig.value[1].label = "数值范围";
  }
});

watch(
  list,
  () => {
    // 如果当前处于提交校验阶段，针对每一次的改变，进行校验
    if (
      emptyFormualList.value.length > 0 ||
      errorScoreList.value.length > 0 ||
      errorFormulaComparisionList.value.length > 0
    ) {
      handleCheckFormula();
      handleCheckScore();
      handleCheckFormulaComparision();
    }
  },
  {
    deep: true
  }
);

defineExpose({
  validateForm,
  initFormValue,
  getFormValue,
  resetForm: () => {
    list.value = [];
    showEmptyTip.value = false;
    emptyFormualList.value = [];
    errorScoreList.value = [];
    errorFormulaComparisionList.value = [];
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-input__inner) {
  text-align: center;
}

:deep(input::-webkit-inner-spin-button, input::-webkit-outer-spin-button) {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
