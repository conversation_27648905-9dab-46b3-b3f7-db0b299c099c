import { defineStore } from "pinia";
import {
  ISchedulingPlan,
  ICreateSchedulingPlan,
  ISchedulingPlanDetail,
  ISchedulingPlanStatistics,
  ISchedulingPlanQuery
} from "@/models";
import * as api from "@/api/sales-order-detail/sales-scheduling-plan";
import { IResponse } from "@/models";
import { CreateSchedulingPlanStepEnum } from "@/enums";
import { useRowspan } from "@/utils/useRowspan";

export const useSalesSchedulingPlanStore = defineStore({
  id: "cx-sales-scheduling-plan-store",
  state: () => ({
    schedulingPlanData: [] as Array<ISchedulingPlan>,
    schedulingPlanDetail: {} as ISchedulingPlanDetail,
    schedulingPlanForm: {} as ICreateSchedulingPlan,
    addSchedulingPlanModalVisible: false as boolean,
    schedulingPlanDetailVisible: false as boolean,
    schedulingPlanStatistics: {} as ISchedulingPlanStatistics,
    isQuickCreateSchedulingPlan: false as boolean,
    activeCreateSchedulingPlanStep: CreateSchedulingPlanStepEnum.selectSaleOrderLine as CreateSchedulingPlanStepEnum,
    loading: false as boolean,
    /** 排产计划编辑 新增模式  默认为新增模式  */
    schedulingPlanFormAddMode: false as boolean,
    total: 0,
    deleteSchedulingPlanStatus: false
  }),
  actions: {
    async querySchedulingPlanPaging(params: ISchedulingPlanQuery) {
      this.loading = true;
      const res = await api.querySchedulingPlanPaging(params).finally(() => {
        this.loading = false;
      });
      this.schedulingPlanData = res.data?.list || [];
      this.total = res.data?.total;
    },

    async getSchedulingPlanStatistics(purchaseId: string) {
      const res = await api.querySchedulingPlanStatistic(purchaseId);
      const schedulingPlanStatistics = res.data;
      if (Array.isArray(res.data?.unSchduled) && res.data.unSchduled.length) {
        schedulingPlanStatistics.unSchduled = useRowspan(schedulingPlanStatistics.unSchduled, "soNo");
      }
      this.schedulingPlanStatistics = schedulingPlanStatistics;
    },

    async deleteSchedulingPlan(id: string): Promise<IResponse<boolean>> {
      return await api.deleteSchedulingPlan(id);
    },

    async editProductionPlan(params: ICreateSchedulingPlan): Promise<IResponse<boolean>> {
      return await api.editProductionPlan(params);
    },

    async getSchedulingPlanDetailById(id: string) {
      return api.getSchedulingPlanDetailById(id);
    },
    async createSchedulingPlan(params: ICreateSchedulingPlan): Promise<IResponse<boolean>> {
      return await api.createProductionPlan(params);
    },

    setSchedulingPlanFormValue(schedulingPlanValue?: ICreateSchedulingPlan) {
      this.schedulingPlanForm = schedulingPlanValue;
    },

    setSchedulingPlanDetail(schedulingPlanDetail?: ISchedulingPlanDetail) {
      this.schedulingPlanDetail = schedulingPlanDetail;
    },

    setActiveCreateSchedulingPlanStep(step: CreateSchedulingPlanStepEnum) {
      this.activeCreateSchedulingPlanStep = step;
    },
    setAddSchedulingPlanModalVisible(visible: boolean) {
      this.addSchedulingPlanModalVisible = visible;
    },
    setSchedulingPlanDetailVisible(visible: boolean) {
      this.schedulingPlanDetailVisible = visible;
    },
    setIsQuickCreateSchedulingPlan(isQuickCreateSchedulingPlan: boolean) {
      this.isQuickCreateSchedulingPlan = isQuickCreateSchedulingPlan;
    },

    setSchedulingPlanFormAddMode(addMode: boolean) {
      this.schedulingPlanFormAddMode = addMode;
    },

    setDeleteSchedulingPlanStatus(status: boolean) {
      this.deleteSchedulingPlanStatus = status;
    }
  }
});
