<template>
  <el-dialog title="同步明细" v-model="visible" class="small sync-history-dialog" destroy-on-close>
    <el-timeline v-if="histories?.length" class="sync-timeline">
      <el-timeline-item
        hollow
        v-for="history in histories"
        :key="history.id"
        :type="history.isFault ? 'danger' : 'success'"
      >
        <p class="text-middle text-primaryText">{{ history.logStepName }}</p>
        <p class="mt-2 text-secondary text-sm">
          <span v-if="history.reason" class="mr-2.5">{{ history.reason }}</span>
        </p>
        <div class="mt-2 text-secondary text-sm">
          <!-- 广州供电局无查看报文明细功能 -->
          <span>{{ formatDate(history.logTime, fullDateFormat) }}</span>
          <el-button
            link
            size="small"
            type="primary"
            class="ml-2.5"
            v-if="history.taskItemId && syncTabType !== SyncOrderTabEnum.SYNC_CSG_GUANGZHOU"
            @click="showMessage(history.taskItemId)"
          >
            查看报文
          </el-button>

          <template v-if="history.dataDetailItem && history.dataDetailItem.length">
            <el-button
              v-for="item in history.dataDetailItem"
              :key="item.taskItemId"
              link
              size="small"
              type="primary"
              class="ml-2.5"
              @click="showMessage(item.taskItemId)"
            >
              {{ item.categoryName }}
            </el-button>
          </template>
        </div>
      </el-timeline-item>
    </el-timeline>
    <CxEmpty v-else />
  </el-dialog>

  <el-dialog
    title="报文明细"
    v-model="messageVisible"
    class="middle"
    align-center
    destroy-on-close
    @closed="downloadId = null"
  >
    <SyncTimeLineMessageInfo
      :syncTabType="syncTabType"
      :task-id="activeTaskId"
      :stepKey="stepKey"
      @update:message-id="downloadIdChange"
    />
    <template #footer>
      <el-button type="primary" v-if="downloadId" :loading="downloadLoading" @click="downloadMessage(downloadId)"
        >下载报文</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SyncTimeLineMessageInfo from "./sync-timeline-message-info.vue";
import { computed, ref } from "vue";
import { useStateGridOrderSyncTimelineStore } from "@/store/modules";
import { formatDate } from "@/utils/format";
import { fullDateFormat } from "@/consts";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SyncOrderTabEnum } from "@/enums/state-grid-order/sync-order-tab";
import { getEipWulianMessageDownloadInfo, getEipBussinessDownloadInfoByTaskId } from "@/api/state-grid-order-sync";
import { downloadByUrl } from "@pureadmin/utils";
import { isWulianStep } from "@/enums/state-grid-order/state-grid-order-sync-type.enum";

const props = defineProps<{
  syncTabType?: string;
  stepKey: number;
}>();

const store = useStateGridOrderSyncTimelineStore();
const messageVisible = ref(false);
const activeTaskId = ref<string>();
const downloadId = ref<string>();
const downloadLoading = ref(false);
const handleDownloadEipBusinessMessage = useLoadingFn(async (taskItemId: string) => {
  const { data } = await getEipBussinessDownloadInfoByTaskId(taskItemId);
  const { fileName = "", fileUrl = "" } = data;
  if (fileName && fileUrl) {
    downloadByUrl(fileUrl, fileName);
  }
}, downloadLoading);
const handleDownloadEIPWulianMessage = useLoadingFn(async (messageId: string) => {
  const { data } = await getEipWulianMessageDownloadInfo(messageId);
  const { fileName = "", fileUrl = "" } = data;
  if (fileName && fileUrl) {
    downloadByUrl(fileUrl, fileName);
  }
}, downloadLoading);
const handleDownLoadIOTMessage = useLoadingFn(store.downloadIOTMessage, downloadLoading);

const histories = computed(() => store.histories);
const visible = computed({
  get() {
    return store.visible;
  },
  set(visible: boolean) {
    store.$patch({ visible });
  }
});
function showMessage(taskId: string) {
  activeTaskId.value = taskId;
  messageVisible.value = true;
}
/**
 * 下载ID赋值
 * 注意：区分平台
 * EIP平台：业务数据使用的是taskId下载报文列表；物联数据时使用报文Id（messageId）下载单条报文
 * 其他平台：使用报文Id（messageId）下载单条报文
 */
function downloadIdChange(id: string) {
  downloadId.value = id;
}

async function downloadMessage(id: string) {
  if (props.syncTabType === SyncOrderTabEnum.SYNC_SHANGHAI_IOT) {
    handleDownLoadIOTMessage(id);
  } else {
    if (isWulianStep(props.stepKey)) {
      handleDownloadEIPWulianMessage(id);
    } else {
      handleDownloadEipBusinessMessage(id);
    }
  }
}
</script>

<style lang="scss">
.sync-history-dialog {
  .el-dialog__body {
    max-height: 300px !important;

    .sync-timeline.el-timeline {
      padding-left: 2px;

      .el-timeline-item:last-child {
        padding-bottom: 0;
      }
    }
  }
}
</style>
