<template>
  <PureTable
    ref="tableInstance"
    border
    show-overflow-tooltip
    :height="465"
    :data="data"
    :loading="loading"
    :columns="columns"
    :pagination="ctx.pagination"
    :span-method="spanMethod"
    row-key="id"
    class="flex-1 overflow-hidden"
    :row-class-name="rowClassName"
    @row-click="handleRowClick"
    @select="handleSelect"
    @selectAll="handleSelectAll"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </PureTable>
  <!-- 已经选中面板 -->
  <div class="mt-2 border border-zinc-200 rounded-md">
    <div class="text-md ml-1 mt-1">已选销售订单行：</div>
    <el-scrollbar :height="80">
      <el-tag class="m-1" v-for="line in ctx.selectedLines" closable :key="line.id" @close="handleCloseByTag(line.id)">
        {{ line.soItemNo }}
      </el-tag>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { computed, inject, onMounted, ref, watch } from "vue";
import { salesOrderLineSelectKey } from "../tokens";
import { useSelectTable } from "./select-table";
import { ISalesOrderLineExt, SpanMethodProps } from "@/models";

const ctx = inject(salesOrderLineSelectKey);
const { columns, data, loading, refreshLines } = useSelectTable();
ctx.refreshLines = refreshLines;
const tableInstance = ref<PureTableInstance>();
const isMultipleMode = computed(() => ctx.multiple);

function rowClassName({ row }: { row: ISalesOrderLineExt }) {
  let className = "cursor-pointer";
  if (row.orderField) {
    className += " self-order";
  }
  return className;
}

function handleSelect(selections: Array<ISalesOrderLineExt>) {
  selectionChangeByMultiple(selections);
}

function handleSelectAll(selections: Array<ISalesOrderLineExt>) {
  ctx.selectedLines.length = 0;
  ctx.selectedLines.push(...selections);
}

function selectionChangeByMultiple(list: Array<ISalesOrderLineExt>): void {
  ctx.selectedLines = list;
}

function spanMethod(data: SpanMethodProps) {
  if (data.columnIndex === 0) {
    return {
      rowspan: data.row.rowSpan,
      colspan: data.row.colSpan
    };
  }
}

/**
 * @description: 标签点击关闭事件
 * @param {*} id 订单行id
 */
function handleCloseByTag(id: string) {
  ctx.selectedLines = ctx.selectedLines.filter(l => l.id !== id);
  const triggerLine = data.value.find(l => l.id === id);
  if (triggerLine) {
    tableInstance.value?.getTableRef().toggleRowSelection(triggerLine, false);
  }
}

function handleRowClick(row: ISalesOrderLineExt) {
  const isInclude = ctx.selectedLines.some(l => l.id === row.id);
  if (isMultipleMode.value) {
    if (isInclude) {
      ctx.selectedLines = ctx.selectedLines.filter(l => l.id !== row.id);
      tableInstance.value?.getTableRef().toggleRowSelection(row, false);
    } else {
      ctx.selectedLines.push(row);
      tableInstance.value?.getTableRef().toggleRowSelection(row, true);
    }
  } else {
    ctx.selectedLines = [row];
    tableInstance.value?.getTableRef().toggleRowSelection(row, true);
  }
}

onMounted(() => {
  tableInstance.value?.getTableRef().clearSelection();
  ctx.selectedLines.forEach(line => tableInstance.value?.getTableRef().toggleRowSelection(line, true));
});

watch(
  () => ctx.multiple,
  () => {
    ctx.selectedLines = [];
    tableInstance.value?.getTableRef().clearSelection();
  }
);
</script>

<style lang="scss" scoped>
::v-deep(.el-table-column--selection.is-center) {
  .cell {
    justify-content: center;
  }
}

.error-only-other-order {
  ::v-deep(.self-order) {
    .el-checkbox__inner {
      border-color: var(--el-color-danger);
    }
  }
}
</style>
