<template>
  <div class="inline-block">
    <el-button
      v-if="isAddMode"
      v-auth="PermissionKey.form.formPurchaseWorkOrderCreate"
      v-track="TrackPointKey.FORM_PURCHASE_PWO_CREATE"
      type="primary"
      :icon="Plus"
      @click="openDialog()"
    >
      新增工单
    </el-button>
    <el-button
      v-if="isEditMode"
      v-auth="PermissionKey.form.formPurchaseWorkOrderEdit"
      v-track="TrackPointKey.FORM_PURCHASE_PWO_EDIT"
      link
      type="primary"
      @click="openDialog"
    >
      编辑
    </el-button>
    <el-dialog
      v-model="dialogVisible"
      :title="isAddMode ? '新增工单' : '编辑工单'"
      align-center
      draggable
      :append-to-body="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 内容 -->
      <work-order-form
        v-loading="loading"
        ref="formRef"
        :mode="props.mode"
        :sub-class-code="props.subClassCode"
        :production-no="props.productionNo"
      />
      <template #footer>
        <span>
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleClickSaveBtn" :loading="loading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { PermissionKey, TrackPointKey } from "@/consts";
import WorkOrderForm from "./work-order-form/index.vue";
import { createWorkOrder, editWorkOrder, getWorkOrderById } from "@/api/work-order";
import { getProductOrderDetailById } from "@/api/production-order";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ICreateWorkOrder } from "@/models";
import { emitter as eventBus } from "@/utils/mitt";

const loading = ref(false);

/**
 * 新增/编辑 工单
 */
const props = defineProps<{
  /** 模式 */
  mode: "edit" | "add";
  /** 生产订单ID */
  productionId: string;
  /** 物资种类code */
  subClassCode: string;
  /** 生产订单编号 */
  productionNo: string;
  /** 工单ID，edit模式必传 */
  workId?: string;
}>();

const emits = defineEmits(["postSaveSuccess"]);

const dialogVisible = ref(false);

const formRef = ref<InstanceType<typeof WorkOrderForm>>();

/** 是否是编辑模式 */
const isEditMode = computed(() => props.mode === "edit");

/** 是否是新增模式 */
const isAddMode = computed(() => props.mode === "add");

/**
 * @description: 请求生产订单详情,填充表单物资名称、描述、单位等
 */
const requestProductionOrderDetail = useLoadingFn(async () => {
  const { data } = await getProductOrderDetailById(props.productionId);
  // 新增模式仅需要外界 透传如物资名称、描述、单位、物资编码
  const {
    materialsCode,
    materialsName: materialName,
    materialsDesc: materialDesc,
    materialsUnit: materialUnit,
    amount,
    unit,
    specificationModel: specificationType,
    voltageClasses: voltageLevel,
    voltageType,
    planFinishDate,
    planStartDate,
    actualFinishDate,
    actualStartDate
  } = data;
  formRef.value.initFormValue({
    materialsCode,
    materialName,
    materialDesc,
    materialUnit,
    amount,
    unit,
    specificationType,
    voltageLevel,
    voltageType,
    planDateArray: planFinishDate && planStartDate ? [planStartDate, planFinishDate] : [],
    actualFinishDate,
    actualStartDate
  });
}, loading);

/**
 * @description: 请求工单详情进行表单赋值
 */
const requestWorkOrderDetail = useLoadingFn(async () => {
  const { data } = await getWorkOrderById(props.workId);
  const { planFinishDate, planStartDate, processIds } = data;
  const formValue = {
    ...data,
    planDateArray: planFinishDate && planStartDate ? [planStartDate, planFinishDate] : [],
    processIds: processIds.split("-")
  };
  formRef.value.initFormValue(formValue);
}, loading);

/**
 * @description: 请求创建工单
 */
const requestCreateWorkOrderById = useLoadingFn(async () => {
  const formValue = formRef.value.getFormValue();
  const params: ICreateWorkOrder = {
    ...formValue,
    productionId: props.productionId,
    planStartDate: formValue.planDateArray[0],
    planFinishDate: formValue.planDateArray[1],
    processIds: formValue.processIds.join("-")
  };
  const workId = await createWorkOrder(params);
  return workId;
}, loading);

/**
 * @description: 请求编辑工单
 */
const requestEditWorkOrder = useLoadingFn(async () => {
  const formValue = formRef.value.getFormValue();
  const params: ICreateWorkOrder = {
    ...formValue,
    id: props.workId,
    productionId: props.productionId,
    planStartDate: formValue.planDateArray[0],
    planFinishDate: formValue.planDateArray[1],
    processIds: formValue.processIds.join("-")
  };
  const workId = await editWorkOrder(params);
  return workId;
}, loading);

async function requestAdd() {
  const { data: workId, msg } = await requestCreateWorkOrderById();
  if (!workId) {
    ElMessage({ type: "warning", message: msg || "网络异常, 请稍后再试" });
    return;
  }
  closeDialog();
  emits("postSaveSuccess");
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
  ElMessage({
    message: "新增成功",
    type: "success"
  });
}

async function requestEdit() {
  const { data: result, msg } = await requestEditWorkOrder();
  if (!result) {
    ElMessage({ type: "warning", message: msg || "网络异常, 请稍后再试" });
    return;
  }
  closeDialog();
  emits("postSaveSuccess");
  eventBus.emit("refreshWorkOrderAndReportWorkTip");
  ElMessage({
    message: "编辑成功",
    type: "success"
  });
}

/**
 * @description: 保存按钮点击事件
 */
const handleClickSaveBtn = async () => {
  const verified = await formRef.value.validateForm();
  if (!verified) {
    return;
  }

  // 新增
  if (isAddMode.value) {
    requestAdd();
  } else if (isEditMode.value) {
    // 编辑
    requestEdit();
  }
};

/**
 * @description: 开启dialog
 */
function openDialog() {
  dialogVisible.value = true;
}

/**
 * @description: 关闭dialog
 */
function closeDialog() {
  dialogVisible.value = false;
}

// 订阅弹窗开启状态，请求数据
watch(dialogVisible, visible => {
  if (visible) {
    if (isEditMode.value) {
      requestWorkOrderDetail();
    } else {
      requestProductionOrderDetail();
    }
  }
});
</script>

<style scoped></style>
