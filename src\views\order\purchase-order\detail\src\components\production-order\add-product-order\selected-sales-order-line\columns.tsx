import { TableWidth } from "@/enums";
import { MEASURE_UNIT } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns() {
  const { withUnitFormatter } = useTableCellFormatter();
  const columns: TableColumnList = [
    {
      label: "销售订单行项目号",
      prop: "soItemNo",
      width: TableWidth.order
    },
    {
      label: "物资种类",
      prop: "subClassName",
      width: TableWidth.type
    },
    {
      label: "物料编码",
      prop: "materialCode",
      minWidth: TableWidth.type
    },
    {
      label: "物料名称",
      prop: "materialName",
      minWidth: TableWidth.name
    },
    {
      label: "物料数量",
      prop: "materialNumber",
      width: TableWidth.number,
      align: "right",
      formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
    },
    {
      label: "电压等级",
      prop: "voltageLevel",
      width: TableWidth.number
    },
    {
      label: "操作",
      prop: "op",
      slot: "operate",
      fixed: "right",
      width: TableWidth.operation
    }
  ];

  return { columns };
}
