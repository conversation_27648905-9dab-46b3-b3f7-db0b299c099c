<template>
  <div class="select-product-out-factory">
    <PureTable
      ref="tableInstance"
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="outFactoryTestData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      @row-click="rowClick"
      @selection-change="selectionChange"
      @page-current-change="pageNoChange"
      @page-size-change="pageSizeChange"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import { IProductOrder } from "@/models/product-order";
import { IOrdersForChooseReq, IOutFactoryFromOrderRes } from "@/models/production-test-sensing";
import { useTableConfig } from "@/utils/useTableConfig";
import { inject, onMounted, ref } from "vue";
import { useColumns } from "./columns";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useCopyProductOrderStore } from "@/store/modules/production-test-sensing";
import { COPY_FROM_ORDER_TOKEN } from "../../tokens";
import { ProductOrWorkOrderEnum } from "@/enums/purchase-order";

const props = defineProps<{
  productInfo?: IProductOrder;
  modelValue?: IOutFactoryFromOrderRes[];
}>();

const emit = defineEmits<{
  (e: "update:modelValue", data: IOutFactoryFromOrderRes[]);
}>();
const copyCtx = inject(COPY_FROM_ORDER_TOKEN);
const copyProductOrderStore = useCopyProductOrderStore();
const { pagination } = useTableConfig();
const queryParams = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize,
  orderIds: [props.productInfo?.id]
};
const { columns } = useColumns();
const tableInstance = ref<PureTableInstance>();
const outFactoryTestData = ref<Array<IOutFactoryFromOrderRes>>([]);
const loading = ref<boolean>(false);
const initOutFactoryExperimentList = useLoadingFn(getOutFactoryTestList, loading);

onMounted(() => {
  initOutFactoryExperimentList(queryParams);
});

/** 获取原材料检测数据 */
async function getOutFactoryTestList(params: IOrdersForChooseReq) {
  const orderType = copyCtx.isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
  const res = await copyProductOrderStore.getOutFactoryExperimentFromOrder({ ...params, orderType });
  outFactoryTestData.value = res.list || [];
  pagination.total = res.total || 0;
}

/** 点击行 */
function rowClick(row: IOutFactoryFromOrderRes) {
  tableInstance.value?.getTableRef()?.toggleRowSelection(row, undefined);
}

/** 页码 */
function pageNoChange(pageNo: number) {
  Object.assign(queryParams, { pageNo });
  initOutFactoryExperimentList(queryParams);
}

/** 页码数量 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  Object.assign(queryParams, { pageNo: pagination.currentPage, pageSize });
  initOutFactoryExperimentList(queryParams);
}

/** 选择切换  */
function selectionChange(val: IOutFactoryFromOrderRes[]) {
  emit("update:modelValue", val);
}
</script>

<style scoped lang="scss"></style>
