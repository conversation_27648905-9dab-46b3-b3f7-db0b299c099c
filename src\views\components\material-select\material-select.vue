<template>
  <el-button @click="visible = true" v-bind="$attrs" :icon="Plus" :disabled="disabled">选择物料</el-button>
  <el-dialog
    title="选择物料"
    v-model="visible"
    class="middle"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :validate-event="false"
    @open="dialogOpen"
  >
    <MaterialHeader :subclass-code="props.subClassCode" @after-create="save" />
    <MaterialSelectTable />
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { provide, reactive, ref } from "vue";
import MaterialHeader from "./material-header.vue";
import MaterialSelectTable from "./material-select-table.vue";
import { IMaterialSelectCtx, materialSelectKey } from "./token";
import { queryMaterials } from "./tools";
import { useTableConfig } from "@/utils/useTableConfig";
import { IMaterial, IMaterialQueryParams } from "@/models";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

const props = defineProps<{
  subClassCode?: string;
  selectedId?: string;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "materialChange", material: IMaterial): void;
}>();

const { pagination } = useTableConfig();
const ctx = reactive<IMaterialSelectCtx>({
  data: [],
  loading: false,
  pagination,
  refresh
});

provide(materialSelectKey, ctx);

const visible = ref(false);

function dialogOpen() {
  initializeCtx();
  refresh();
}

function refresh(): void {
  ctx.loading = true;
  const { keyword, pagination } = ctx;
  const { currentPage, pageSize } = pagination;
  const params: IMaterialQueryParams = {
    keyWords: keyword,
    subClassCode: props.subClassCode,
    pageNo: currentPage,
    pageSize
  };
  queryMaterials(params)
    .then(res => {
      const { list, total } = res.data;
      ctx.data = list;
      ctx.pagination.total = total;
    })
    .finally(() => (ctx.loading = false));
}

function initializeCtx() {
  ctx.keyword = null;
  ctx.data = [];
  ctx.loading = false;
  ctx.selectedId = props.selectedId;
  ctx.selectedMaterial = null;
  ctx.pagination = useTableConfig().pagination;
}

function save() {
  if (!ctx.selectedId) {
    ElMessage.warning("请选择物料");
    return;
  }
  if (ctx.selectedMaterial) {
    emit("materialChange", ctx.selectedMaterial);
  }
  visible.value = false;
}
</script>

<style scoped></style>
