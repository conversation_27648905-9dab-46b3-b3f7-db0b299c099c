import { TableColumnRenderer } from "@pureadmin/table";
import { ColumnWidth, KeywordAliasEnum, KeywordAliasEnumMapDesc, ProductionStateEnum, TableWidth } from "@/enums";
import { ElButton, ElDropdown, ElDropdownItem, ElIcon, ElMessage, ElMessageBox, dayjs } from "element-plus";
import { computed, ref } from "vue";
import {
  useSalesFillInDataStore,
  useSalesOrderDetailStore,
  useSalesProductOrderStore,
  useSystemAuthStore
} from "@/store/modules";
import { IProductOrder } from "@/models";
import { EnumCell } from "@/components/TableCells";
import { PermissionKey, dateFormat, TrackPointKey, MEASURE_UNIT } from "@/consts";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { infoHeader } from "@/components/TableHeader";
import { RouterLink } from "vue-router";
import { MoreFilled } from "@element-plus/icons-vue";
import { useConfirm } from "@/utils/useConfirm";
import { duplicateProductionOrder } from "@/api/production-order";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

export function useColumns() {
  const productOrderStore = useSalesProductOrderStore();
  const fillInDataStore = useSalesFillInDataStore();
  const salesOrderDetailStore = useSalesOrderDetailStore();
  const { withUnitFormatter } = useTableCellFormatter();
  const systemAuthStore = useSystemAuthStore();

  const authDataCheckCMP = computed(() => systemAuthStore.businessLicenseAuth?.authorization?.dataCheckFlag);

  const columns: TableColumnList = [
    {
      label: "生产订单号",
      prop: "ipoNo",
      headerRenderer: () => (
        <KeywordAliasHeader
          code={KeywordAliasEnum.IPO_NO}
          defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
        />
      ),
      minWidth: ColumnWidth.Char14,
      fixed: "left",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div onClick={() => onViewProductOrderDetail(data.row)} class="text-primary cursor-pointer">
            {data.row.ipoNo}
          </div>
        );
      }
    },
    {
      label: "销售订单号",
      prop: "soNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "销售订单行项目",
      prop: "soItemNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "采购订单号",
      prop: "poNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "采购订单行项目",
      prop: "poItemNo",
      minWidth: ColumnWidth.Char14
    },
    {
      label: "物料名称",
      prop: "materialsName",
      minWidth: TableWidth.name
    },
    {
      label: "电压等级",
      prop: "voltageClasses",
      width: TableWidth.number
    },
    {
      label: "需求数量",
      prop: "amount",
      align: "right",
      width: TableWidth.number,
      formatter: withUnitFormatter("unit", "subclassCode", MEASURE_UNIT)
    },
    {
      label: "计划日期",
      prop: "planStartDate",
      width: TableWidth.dateRanger,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {data.row.planStartDate ? dayjs(data.row?.planStartDate).format(dateFormat) : ""} <span> ～ </span>{" "}
            {data.row.planFinishDate ? dayjs(data.row?.planFinishDate).format(dateFormat) : ""}
          </div>
        );
      }
    },
    {
      label: "生产状态",
      prop: "ipoStatus",
      width: TableWidth.largeType,
      cellRenderer: (data: TableColumnRenderer) => EnumCell(data, ProductionStateEnum, "productionStateEnum")
    },
    {
      label: "发货数量",
      prop: "quantityShipped",
      width: TableWidth.number
    },
    {
      label: "入库数量",
      prop: "quantityWarehousing",
      width: TableWidth.largeType
    },
    {
      label: "备注",
      prop: "remark",
      width: TableWidth.largeType
    },
    {
      label: "实际开始日期",
      prop: "actualStartDate",
      fixed: "right",
      width: TableWidth.date,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {data.row.actualStartDate ? (
              dayjs(data.row?.actualStartDate).format(dateFormat)
            ) : (
              <span class="text-secondary">{"待补全"}</span>
            )}
          </div>
        );
      }
    },
    {
      label: "实际结束日期",
      prop: "actualFinishDate",
      fixed: "right",
      width: TableWidth.date,
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            {data.row.actualFinishDate ? (
              dayjs(data.row?.actualFinishDate).format(dateFormat)
            ) : (
              <span class="text-secondary">{"待补全"}</span>
            )}
          </div>
        );
      }
    },
    {
      label: "数据检查",
      prop: "ipoStatus",
      width: TableWidth.type,
      fixed: "right",
      hide: () => !salesOrderDetailStore.isCable,
      headerRenderer: infoHeader("未显示有缺失时，建议人工检查相关数据，请在系统中维护所有生产数据数据"),
      cellRenderer: data => {
        return data.row.missingData ? (
          <a
            style={{ color: "var(--el-color-danger)" }}
            class={authDataCheckCMP.value ? "cursor-pointer" : null}
            onClick={authDataCheckCMP.value ? () => onShowDataMissModal(data.row.id) : null}
          >
            有缺失
          </a>
        ) : null;
      }
    },
    {
      label: "操作",
      prop: "op",
      width: ColumnWidth.Char12,
      fixed: "right",
      cellRenderer: (data: TableColumnRenderer) => {
        return (
          <div>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseProductionOrderEdit}
              v-track={TrackPointKey.FORM_PURCHASE_PO_EDIT}
              link
              type="primary"
              onClick={() => editProductOrder(data.row.id)}
            >
              编辑
            </ElButton>
            <ElButton
              v-if={salesOrderDetailStore.isCable}
              v-auth={PermissionKey.form.formPurchaseProductionOrderCopy}
              link
              type="primary"
              onClick={() => {
                copyProductionOrder(data.row.id, data.row.salesLineIds);
              }}
            >
              复制
            </ElButton>
            <ElButton
              v-auth={PermissionKey.form.formPurchaseProductionOrderDelete}
              v-track={TrackPointKey.FORM_PURCHASE_PO_DELETE}
              link
              type="danger"
              onClick={() => onDeleteProductOrder(data.row.id)}
            >
              删除
            </ElButton>
            {data.row.qualityTraceabilityRecordId && (
              <ElDropdown
                class="ml-3 inline-block p-1 mt-0.5"
                v-auth={PermissionKey.qualityTracing.qualityTracingRecordRedirectionFromProdOrder}
              >
                {{
                  default: () => (
                    <ElIcon class="focus-visible:outline-none">
                      <MoreFilled />
                    </ElIcon>
                  ),
                  dropdown: () => (
                    <ElDropdownItem>
                      <RouterLink
                        class="text-primary"
                        to={`/quality-tracing-record/${data.row.qualityTraceabilityRecordId}`}
                      >
                        查看质量追溯评分
                      </RouterLink>
                    </ElDropdownItem>
                  )
                }}
              </ElDropdown>
            )}
          </div>
        );
      }
    }
  ];

  const editProductOrderModalVisibleRef = ref<boolean>();

  const onViewProductOrderDetail = async (data: IProductOrder) => {
    if (salesOrderDetailStore.isCable) {
      productOrderStore.setFillInDataModalVisible(true);
      await fillInDataStore.refreshData(data.id);
    } else {
      productOrderStore.setProductOrderDetailVisible(true);
      await productOrderStore.getProductOrderDetailById(data.id);
    }
  };

  const editProductOrder = async (id: string) => {
    productOrderStore.setProductOrderFormAddMode(false);
    editProductOrderModalVisibleRef.value = true;
    const productOrderRes = await productOrderStore.getProductOrderDetailById(id);
    productOrderStore.setCreateProductOrder(productOrderRes);
  };

  const onDeleteProductOrder = (id: string) => {
    ElMessageBox.confirm("确认删除后，数据将无法恢复", "确认删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(async () => {
      await productOrderStore.deleteProductOrder(id);
      const salesId: string = salesOrderDetailStore.salesOrder.id;
      ElMessage({ type: "success", message: "删除成功" });
      productOrderStore.setDeleteProductOrderStatus(true);
      productOrderStore.queryProductOrderStatistics(salesId);
      salesOrderDetailStore.refreshStepStatus();
    });
  };

  const copyProductionOrder = async (productionId: string, salesLineIds: string[]) => {
    const confirm = await useConfirm(
      "复制数据范围有:生产订单、工单、报工、原材料及组部件检测、生产工艺及过程检测、出厂试验、成品信息",
      "确认复制",
      {
        type: "warning",
        confirmButtonText: "确认复制",
        cancelButtonText: "取消"
      }
    );
    if (confirm) {
      const duplicateResult = await duplicateProductionOrder(productionId, salesLineIds);
      if (duplicateResult.data) {
        ElMessage({ type: "success", message: "复制成功" });
        productOrderStore.queryProductOrders({
          saleId: salesOrderDetailStore.saleOrderId
        });
      }
    }
  };

  /** 展示数据缺失 */
  const onShowDataMissModal = (id: string) => {
    productOrderStore.setDataMissingModalVisible(true);
    productOrderStore.getProductOrderDataMissingById(id);
  };

  return { columns, editProductOrderModalVisibleRef };
}
