<template>
  <div class="upload-report">
    <div class="upload-report-content">
      <el-upload
        ref="uploadRef"
        drag
        :auto-upload="false"
        :on-change="reportChange"
        :limit="1"
        :on-exceed="handleExceed"
      >
        <el-icon size="24"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          <div class="upload-text"><span>将文件拖到此处，或</span><em>点击上传</em></div>
          <div class="upload-tips">
            <span>支持20M以内{{ currentFileType.join("，") }}格式文件</span>
          </div>
        </div>
      </el-upload>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UploadFilled } from "@element-plus/icons-vue";
import { ElMessage, UploadFile, UploadProps, UploadRawFile, genFileId } from "element-plus";
import { ref } from "vue";
import { validUploadFileType } from "@/utils/uploadFiles";
import { EFileType } from "@/views/config/collection/types";

const emits = defineEmits<{
  (event: "uploadReportChange", report?: UploadFile): void;
}>();

// 上传文件的数据
const uploadRef = ref();
const currentFileType = [EFileType.PDF, EFileType.PNG, EFileType.JPG];
// 控制覆盖文件
const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};
// 上传文件时
const reportChange = (report: UploadFile) => {
  const validFileType = validUploadFileType(report.raw?.type, currentFileType);
  let reportSource = report;
  if (validFileType) {
    const errorMsg = `选择的文件类型不正确，支持文件类型(${currentFileType.join("，")})`;
    ElMessage.error(errorMsg);
    uploadRef.value?.handleRemove(report);
    reportSource = null;
  }

  // 校验上传文件的大小
  const maxSize = 20 * 1024 * 1024;
  const fileSize = report.size;
  if (fileSize >= maxSize) {
    const errorMsg = `上传文件过大,不能超过(20M)`;
    ElMessage.error(errorMsg);
    uploadRef.value?.handleRemove(report);
    reportSource = null;
  }

  emits("uploadReportChange", reportSource);
};
</script>

<style scoped lang="scss"></style>
