import { IDeviceGroup, IDeviceGroupReq, IDeviceGroupForm, IResponse } from "@/models";
import { http } from "@/utils/http";
import { withApiGateway } from "../util";

/** 查询监控设备分组数据 */
export const queryDeviceGroup = (data?: IDeviceGroupReq) => {
  const url: string = withApiGateway("admin-api/business/monitorDevice/getGroupList");
  return http.post<IDeviceGroupReq, IResponse<Array<IDeviceGroup>>>(url, { data });
};

/** 创建监控设备分组 */
export const createDeviceGroup = (data: IDeviceGroupForm) => {
  const url: string = withApiGateway("admin-api/business/monitorDevice/createGroup");
  return http.post<IDeviceGroupForm, IResponse<string>>(url, { data });
};

/** 编辑设备分组 **/
export const editDeviceGroup = (data: IDeviceGroupForm) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/editGroup/${data.id}`);
  return http.post<IDeviceGroupForm, IResponse<boolean>>(url, { data });
};

/** 删除设备分组 **/
export const deleteDeviceGroup = (deviceGroupId: string) => {
  const url: string = withApiGateway(`admin-api/business/monitorDevice/deleteGroup/${deviceGroupId}`);
  return http.delete<void, IResponse<boolean>>(url);
};
