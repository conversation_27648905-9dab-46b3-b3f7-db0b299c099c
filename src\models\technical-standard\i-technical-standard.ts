import { IPagingReq } from "../i-paging-req";
import { ICollectionItem, ITechnicalStandardLibrary } from "../technical-standard-library";

export interface ITechnicalStandardInfo {
  id: string;
  /** 标准库名称 */
  standardName?: string;
  standardId: string;
  /** 生成订单id */
  productionOrderId: string;
  /** 物料编号  */
  materialCode: string;
  /** 规格型号  */
  specificationModel: string;
  /** 是否通用 */
  isStandard?: boolean;
}

export interface ITechnicalStandardStockReq extends IPagingReq {
  standardName?: string;
  keyWord?: string;
}
/** 技术标准库 */
export type ITechnicalStandardStock = ITechnicalStandardLibrary;

/** 生成订单中选用 */
export interface ITechnicalStandardFromOrder {
  id?: string;
  /** 订单号 */
  productionNo: string;
  /** 标准名称 */
  standardName: string;
  /** 标准id */
  standardId?: string;
  /** 订单号 */
  orderStandardId?: string;
}

/** 编辑 */
export interface ISaveStandardReq {
  specificationModel?: string;
  materialCode?: string;
  standardName?: string;
  isStandard?: boolean;
}

/** 技术标准库详情信息 */
export type IDetailCollectItem = ICollectionItem;

export interface IDetailBatchCollectItem {
  id: string;
  /** 是否包含上限 */
  includeMaxValue: boolean;
  /** 上限 */
  maxValue: string;
  /** 是否包含下限 */
  includeMinValue: boolean;
  /** 下限 */
  minValue: string;
}
