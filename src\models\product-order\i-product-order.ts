import {
  DeviceTerminalEnum,
  JointCategoriesEnum,
  ProductionStateEnum,
  VoltageClassesEnum,
  VoltageTypeEnum
} from "@/enums";
import { IDictionaryOption } from "../platform";
import { ICreateProductOrder } from "./i-create-product-order";
import { ISalesOrderLine } from "@/models/sales-order-line";

export interface IProductOrderRef {
  getFormValue: () => ICreateProductOrder | boolean;
  resetFormValue: () => {};
  patchFormValue: () => {};
}

export interface ICreateProductOrderExe extends ICreateProductOrder {
  planDateArray?: [Date, Date];
  subClassCode?: string;
  materialCode?: string;
}

/** 生产订单 */
export interface IProductOrder {
  id?: string;

  /** / 销售订单行Id */
  salesLineId?: string;

  /** 供应商编码 */
  supplierCode: string;

  /** 供应商名称 */
  supplierName: string;

  /** 生产订单号 */
  ipoNo?: string;

  /** 品类编码 */
  categoryCode: string;
  /** 种类编码 */
  subclassCode: string;

  /** 采购订单号 */
  poNo: string;

  /**采购订单行项目号  */
  poItemNo: string;

  /** 销售订单行项目号 */
  soItemNo: string;

  materialId?: string;

  /** 厂家物料编码 */
  materialsCode: string;
  /**  厂家物料名称 */
  materialsName: string;
  /** 厂家物资单位 */
  materialsUnit: string;
  /**  厂家物料描述 */
  materialsDesc: string;
  /** 生产数量 */
  amount?: number;
  /**  计量单位 */
  unit?: string;
  /** 计划开始日期  */
  planStartDate?: Date;
  /**  计划完成日期 */
  planFinishDate?: Date;
  /** 实际开始日期 */
  actualStartDate?: Date;
  /** 实际完成日期 */
  actualFinishDate?: Date;

  /*** 生产订单状态 */
  ipoStatus?: ProductionStateEnum;
  /**应用类型  */
  applicationType?: DeviceTerminalEnum;
  /** 电压类型 */
  voltageType?: VoltageTypeEnum;
  /** 电压等级 */
  voltageClasses?: VoltageClassesEnum;

  /** 备注 */
  remark?: string;

  /** 接头类型 */
  jointCategories?: JointCategoriesEnum;

  /** 规格型号 */
  specificationModel?: string;

  /**采购订单Id */
  purchaseId?: string;

  /** 排产计划Id */
  productionPlanId: string;

  /** 生产订单id */
  productionId?: string;

  /**销售订单号 */
  soNo: string;
  /**  */
  isWork: boolean;

  /** 品类名称 */
  categoryName: string;

  /** 种类名称 */
  subclassName: string;

  /** 数据缺失 */
  missingData: boolean;

  /** 数据缺失 true 展示数据缺失 */
  dataCheck: boolean;

  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;

  /** 发货数量 */
  quantityShipped?: number;

  /** 入库数量 */
  quantityWarehousing?: number;
  /** 种类编码 */
  subClassCode?: string;

  /** 销售订单id */
  saleId: string;

  /** 当前已绑定的销售订单行数据 */
  salesLineDetails?: Array<ISalesOrderLine>;

  /** 规格型号 */
  specificationType: string;

  /** 质量追溯记录id */
  qualityTraceabilityRecordId: string;
}
