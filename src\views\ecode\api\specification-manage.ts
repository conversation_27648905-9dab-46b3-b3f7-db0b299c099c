/**
 * @description: 喷码机管理
 */

import { http } from "@/utils/http";
import { IListResponse, IResponse } from "@/models";
import { withApiGateway } from "@/api/util";
import {
  AddEcodeModel,
  AddSpecification,
  AddStandard,
  EditEcodeModel,
  EditSpecification,
  EditStandard,
  InputAutoCompeleteItem,
  IEcodeModel,
  IEcodeSpecification,
  IEcodeStandard,
  StandardTreeNode,
  RequestListBaseParams,
  RequestListModelsParams,
  RequestListSpecificationsParams
} from "../models/i-specification-manage";

/**
 * @description: 获取标准树
 */
export const getStandardTree = () => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/listTree`);
  return http.get<void, IResponse<Array<StandardTreeNode>>>(url);
};

/**
 * @description: 获取标准列表
 */
export const getStandardList = (data: RequestListBaseParams) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/list`);
  return http.get<RequestListBaseParams, IListResponse<IEcodeStandard>>(url, { params: data });
};

/**
 * @description: 获取标准详情
 */
export const getStandardDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/detail`);
  return http.get<void, IResponse<IEcodeStandard>>(url, { params: { id } });
};

/**
 * @description: 新增标准
 */
export const addStandard = (data: AddStandard) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/add`);
  return http.post<AddStandard, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑标准
 */
export const editStandard = (data: EditStandard) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/updateStandard`);
  return http.put<EditStandard, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除标准
 */
export const deleteStandard = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/standard/delete`);
  return http.delete<void, IResponse<boolean>>(url, { params: { id } });
};

// -----------------------------------------------------------------------------------------

/**
 * @description: 获取所有型号
 */
export const getAllModel = () => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/listAll`);
  return http.get<void, IResponse<Array<IEcodeModel>>>(url);
};

/**
 * @description: 获取型号列表
 */
export const getModelList = (data: RequestListModelsParams) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/list`);
  return http.get<RequestListModelsParams, IListResponse<IEcodeModel>>(url, { params: data });
};

/**
 * @description: 获取型号详情
 */
export const getModelDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/detail`);
  return http.get<void, IResponse<IEcodeModel>>(url, { params: { id } });
};

/**
 * @description: 新增型号
 */
export const addModel = (data: AddEcodeModel) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/add`);
  return http.post<AddEcodeModel, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑型号
 */
export const editModel = (data: EditEcodeModel) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/updateStandard`);
  return http.put<EditEcodeModel, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除型号
 */
export const deleteModel = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/type/delete`);
  return http.delete<void, IResponse<boolean>>(url, { params: { id } });
};

// -----------------------------------------------------------------------------------------

/**
 * @description: 获取所有规格
 */
export const getAllSpecification = () => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/listAll`);
  return http.get<void, IResponse<Array<IEcodeSpecification>>>(url);
};

/**
 * @description: 获取规格列表
 */
export const getSpecificationList = (params: RequestListSpecificationsParams) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/list`);
  return http.get<RequestListSpecificationsParams, IListResponse<IEcodeSpecification>>(url, { params });
};

/**
 * @description: 获取规格详情
 */
export const getSpecificationDetail = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/detail`);
  return http.get<string, IResponse<IEcodeSpecification>>(url, { params: { id } });
};

/**
 * @description: 新增规格
 */
export const addSpecification = (data: AddSpecification) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/add`);
  return http.post<AddSpecification, IResponse<boolean>>(url, { data });
};

/**
 * @description: 编辑规格
 */
export const editSpecification = (data: EditSpecification) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/updateStandard`);
  return http.put<EditSpecification, IResponse<boolean>>(url, { data });
};

/**
 * @description: 删除规格
 */
export const deleteSpecification = (id: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/delete`);
  return http.delete<void, IResponse<boolean>>(url, { params: { id } });
};

/**
 * @description: 获取输入框自动补全列表
 */
export const getInputAutoCompeleteList = (field: string, value: string) => {
  const url: string = withApiGateway(`admin-api/ecode/specification/data/query`);
  return http.get<void, IResponse<Array<InputAutoCompeleteItem>>>(url, {
    params: { field, value }
  });
};
