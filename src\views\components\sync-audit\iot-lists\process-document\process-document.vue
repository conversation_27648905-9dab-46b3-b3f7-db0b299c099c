<template>
  <pure-table
    show-overflow-tooltip
    size="large"
    class="flex-1 overflow-hidden"
    row-key="id"
    v-bind="$attrs"
    :data="data"
    :columns="columns"
    :loading="loading"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
  <EditProcessDocumentDialog ref="editDialog" :subclass-code="subclassCode" />
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import EditProcessDocumentDialog from "@/views/components/state-grid-order-sync/dialogs/edit-process-document.vue";
import { PureTable } from "@pureadmin/table";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { StateGridOrderSyncResult, TableWidth } from "@/enums";
import { computed, onMounted, provide, ref, watchEffect } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IFinishedProductionSync, IPagingReq } from "@/models";
import { OperatorCell } from "@/components/TableCells";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";

const store = useStateGridSyncAuditStore();
const { enumFormatter } = useTableCellFormatter();

const loading = ref(false);
const allData = ref<Array<IFinishedProductionSync>>();
const data = ref<Array<IFinishedProductionSync>>([]);
const editDialog = ref<InstanceType<typeof EditProcessDocumentDialog>>();
const handleRefresh = useLoadingFn(store.getFinishedProductionSyncList, loading);

const subclassCode = computed(() => store.subClassCode);

provide(stateGridOrderSyncEditKey, {
  refreshFn: refresh
});

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName"
  },
  {
    label: "附件名称",
    prop: "materialCode"
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    prop: "operator",
    width: TableWidth.operation,
    fixed: "right",
    className: "no-default",
    cellRenderer: data => {
      if (!data.row.editable) {
        return null;
      }
      return OperatorCell([
        {
          name: "编辑",
          action: () => editDialog.value.openEditDialog(data.row),
          props: { type: "primary" }
        }
      ]);
    }
  }
];

watchEffect(() => {
  const activeNo = store.activeNo;
  data.value = activeNo ? allData.value?.filter(datum => datum.orderNo === activeNo) : allData.value;
});

onMounted(() => {
  refresh();
});

async function refresh(pageInfo?: IPagingReq) {
  const res = await handleRefresh(pageInfo);
  allData.value = res.list || [];
}
</script>

<style scoped></style>
