export function useSpan<T>() {
  const setSpan = (data: Array<T>, field: string, spanField: string, separator = "_") => {
    const rowSpanMap: Record<string, number> = {};
    const fields = field.split(separator);
    data.forEach(datum => {
      const value = getValue(datum, fields);
      rowSpanMap[value] = (rowSpanMap[value] || 0) + 1;
    });
    data.forEach(datum => {
      const value = getValue(datum, fields);
      if (!value) {
        datum[spanField] = [1, 1];
        return;
      }
      const row: number = rowSpanMap[value];
      rowSpanMap[value] = 0;
      datum[spanField] = [row, 1];
    });
  };

  function getValue(datum, fields: Array<string>) {
    return fields.reduce((prev, cur) => {
      prev += `${datum[cur]}`;
      return prev;
    }, "");
  }

  return {
    setSpan
  };
}
