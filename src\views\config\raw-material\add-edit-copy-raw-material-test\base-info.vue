<template>
  <!-- 原材料信息 -->
  <div class="py-2 px-3 mb-4 border rounded-[10px] border-gray-200">
    <el-descriptions>
      <el-descriptions-item label="原材料名称">
        {{ rawMaterialDetail.name }}
      </el-descriptions-item>
      <el-descriptions-item label="原材料类型">
        {{ rawMaterialDetail.processName }}
      </el-descriptions-item>
      <el-descriptions-item label="原材料编号">
        {{ rawMaterialDetail.code }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from "vue";

defineProps<{
  rawMaterialDetail: {
    name: string;
    processName: string;
    code: string;
  };
}>();
</script>

<style scoped lang="scss"></style>
