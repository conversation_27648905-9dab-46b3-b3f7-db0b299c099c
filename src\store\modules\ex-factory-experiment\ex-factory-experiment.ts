import { IProductionMaterialProcess, IResponse } from "@/models";
import { defineStore } from "pinia";
import { outGoingFactoryApi } from "@/api/production-test-sensing/out-going-factory";
import * as rawMaterialGroupUnitService from "@/api/production-test-sensing/raw-material-group-unit-check";
import { ICopyFromOrder, IRawMaterialCheckCollectionItem } from "@/models/raw-material/i-raw-material-res";
import {
  useFillInDataStore,
  usePurchaseOrderDetailStore,
  useSalesFillInDataStore,
  useSalesOrderDetailStore
} from "@/store/modules";
import { outGoingFactoryQMXExperimentApi } from "@/api/production-test-sensing/out-going-factory";
import { ProductOrWorkOrderEnum } from "@/enums";

/** 出厂试验 */
export const useEXFactoryExperimentStore = defineStore({
  id: "ex-factory-experiment-store",
  state: () => ({
    /** 出厂工序集合 */
    processes: [] as Array<IProductionMaterialProcess>,
    formMetadataModelInfos: [] as Array<IRawMaterialCheckCollectionItem>,
    activeProcess: {} as IProductionMaterialProcess
  }),
  getters: {
    /** 根据工序判断是否可以 新增/编辑 */
    getIsCanOperateAddAndEdit() {
      return !this.activeProcess?.autoCollect;
    }
  },
  actions: {
    /** 根据配置是否显示模拟局放耐压按钮操作 */
    async getSimulateJFNYBtn(): Promise<boolean> {
      return (await outGoingFactoryApi.getOutGoingFactoryJFNYButton())?.data;
    },

    /*** 查询工序列表 */
    async getOutGoingFactoryProcess(isOnlySale = false) {
      const { dataId } = isOnlySale ? useSalesFillInDataStore() : useFillInDataStore();
      const { isCable } = isOnlySale ? useSalesOrderDetailStore() : usePurchaseOrderDetailStore();
      this.processes = isCable
        ? await outGoingFactoryApi.getOutGoingFactoryProcess(dataId).then(res => res.data)
        : await outGoingFactoryApi.getNonCableOutGoingFactoryProcess(dataId).then(res => res.data);
    },

    async getMetadataModelInfo(processId: string, specificationModel: string) {
      const metadataModelRes: IResponse<Array<IRawMaterialCheckCollectionItem>> =
        await rawMaterialGroupUnitService.getAddRawMaterialCheckInfoByProcessId(processId, specificationModel);
      this.formMetadataModelInfos = metadataModelRes.data;
    },

    async setMetadataModelInfo(metadataModels?: Array<IRawMaterialCheckCollectionItem>) {
      this.formMetadataModelInfos = metadataModels;
    },

    /** 设置 */
    async setActiveProcess(process: IProductionMaterialProcess) {
      this.activeProcess = process;
    },

    /** 从订单中复制出厂试验 */
    async copyOutFactoryExperimentFromOrder(paramData: ICopyFromOrder, isOnlySale = false) {
      const isCable = isOnlySale ? useSalesOrderDetailStore().isCable : usePurchaseOrderDetailStore().isCable;
      const orderType = isCable ? ProductOrWorkOrderEnum.PRODUCT_ORDER : ProductOrWorkOrderEnum.WORK_ORDER;
      return await outGoingFactoryQMXExperimentApi.copyOutFactoryExperimentFromOrder({ ...paramData, orderType });
    },

    /** 初始化试验详情信息 */
    initFormMetadataModelInfos() {
      this.formMetadataModelInfos = [];
    }
  }
});
