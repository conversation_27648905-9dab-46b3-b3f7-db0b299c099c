<template>
  <el-form ref="formRef" :model="formValue" :rules="rules" label-width="60px">
    <el-row :gutter="40">
      <el-col>
        <el-form-item label="标识" prop="flagName" class="!w-[260px]">
          <el-input placeholder="请输入标识" v-model="formValue.flagName" clearable />
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="平台" prop="syncIotFlag">
          <el-checkbox v-model="formValue.syncIotFlag">对接上海IOT</el-checkbox>
          <el-checkbox v-model="formValue.syncGzFlag">对接广州供电局</el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formValue.status">
            <el-radio :label="true" border>启用</el-radio>
            <el-radio :label="false" border>禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            placeholder="请输入备注"
            show-word-limit
            rows="2"
            clearable
            v-model="formValue.remark"
            :maxlength="100"
            resize="none"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { ISynchronizationFlagForm } from "@/models";
import { useSynchronizationFlagStore } from "@/store/modules";

const formRef = ref<FormInstance>();
const formValue = reactive<ISynchronizationFlagForm>({
  id: undefined,
  flagName: undefined,
  remark: undefined,
  status: true,
  syncIotFlag: false,
  syncGzFlag: false
});
const rules = reactive<FormRules>({
  flagName: [{ required: true, message: requiredMessage("标识"), trigger: "change" }]
});

const synchronizationFlagStore = useSynchronizationFlagStore();

watchEffect(() => {
  if (synchronizationFlagStore.synchronizationFlagDetail) {
    Object.assign(formValue, synchronizationFlagStore.synchronizationFlagDetail);
  }
});

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<ISynchronizationFlagForm> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return formValue;
}

defineExpose({
  getValidValue
});
</script>

<style scoped></style>
