<template>
  <el-dialog
    title="编辑原材料检"
    v-model="editVisible"
    class="middle"
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <AddRawMaterial ref="editFormRef" notRefreshList :subclassCode="subclassCode" @stateChange="step = $event" />
    <template #footer>
      <el-button @click="editVisible = false">取消</el-button>
      <el-button type="primary" @click="editFormRef?.editRawMaterialRef?.onNextStep" v-if="firstStep">下一步</el-button>
      <template v-if="secondStep">
        <el-button @click="editFormRef?.editRawMaterialRef?.onPreStep">上一步</el-button>
        <el-button type="warning" @click="handleEditAndSync" :loading="editAndSyncLoading" v-if="hasSync"
          >保存，并重新同步</el-button
        >
        <el-button type="primary" @click="handleEdit" :loading="editLoading">保存</el-button>
      </template>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import AddRawMaterial from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/edit-raw-material-inspect/index.vue";
import { computed, provide, reactive, ref, watch } from "vue";
import { useEdit } from "@/views/components/state-grid-order-sync/hooks";
import { IRawMaterialSync } from "@/models";
import { EDiagType } from "@/models/raw-material/i-raw-material-res";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "../tokens";

const props = defineProps<{
  subclassCode: string;
}>();

const rawMaterialGroupUnitStore = useRawMaterialGroupUnitStore();

const {
  hasSync,
  editVisible,
  editFormRef,
  editLoading,
  editAndSyncLoading,
  handleEdit,
  handleEditAndSync,
  handleMaterialCategory,
  stateGridOrderSyncEditCtx
} = useEdit<InstanceType<typeof AddRawMaterial>>(editProcess);

const step = ref(0);

const firstStep = computed(() => step.value === 0);
const secondStep = computed(() => step.value === 1);
const provideValue = reactive({
  detailMaterialCategory: handleMaterialCategory(props.subclassCode)
});

watch(
  () => props.subclassCode,
  (subclassCode: string) => {
    if (!subclassCode) return;
    provideValue.detailMaterialCategory = handleMaterialCategory(subclassCode);
  }
);

provide(PROVIDE_PROCESS_INSPECT_TOKEN, provideValue);

async function openEditDialog(data: IRawMaterialSync) {
  step.value = 0;
  editVisible.value = true;
  stateGridOrderSyncEditCtx.editData = {
    id: data.dataId,
    no: data.code
  };
  await rawMaterialGroupUnitStore.getDetailOfRawMaterialGroupUnit(data.dataId, EDiagType.Edit);
  rawMaterialGroupUnitStore.setEditRawMaterialVisible(true, EDiagType.Edit);
}

async function editProcess() {
  await editFormRef.value.editRawMaterialRef.saveFormData(false);
  return true;
}

defineExpose({
  openEditDialog
});
</script>

<style scoped></style>
