<template>
  <el-descriptions class="px-6 bg-white" v-loading="loading">
    <template #title>
      <div class="flex items-center justify-between">
        <span class="text-lg font-bold">{{ specificationDetail.qualityName }}</span>
      </div>
    </template>
    <el-descriptions-item label="物资种类">{{ specificationDetail.subClassName }}</el-descriptions-item>
    <el-descriptions-item label="启用状态">
      <el-tag :type="specificationDetail.enabled ? '' : 'danger'">
        {{ specificationDetail.enabled ? "启用" : "停用" }}
      </el-tag>
    </el-descriptions-item>
    <el-descriptions-item label="备注">
      <el-tooltip :content="specificationDetail.remark" placement="bottom">
        <div class="line-clamp-1">{{ specificationDetail.remark }}</div>
      </el-tooltip>
    </el-descriptions-item>
    <el-descriptions-item label="原材料检权重">
      {{ formatWeight(specificationDetail.rawMaterialWeight) }}
    </el-descriptions-item>
    <el-descriptions-item label="生产过程权重">
      {{ formatWeight(specificationDetail.processWeight) }}
    </el-descriptions-item>
    <el-descriptions-item label="出厂试验权重">
      {{ formatWeight(specificationDetail.experimentWeight) }}
    </el-descriptions-item>
    <el-descriptions-item label="工艺稳定性权重">
      {{ formatWeight(specificationDetail.processStabilityWeight) }}
    </el-descriptions-item>
    <el-descriptions-item label="更新时间">
      {{ formatDate(specificationDetail.updateTime, fullDateFormat) }}
    </el-descriptions-item>
  </el-descriptions>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { formatDate } from "@/utils/format";
import { emptyDefaultValue, fullDateFormat } from "@/consts";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { queryQualitySpecificationById } from "@/api/quality-tracing";
import { QualitySpecificationItem } from "@/models/quality-tracing";

/**
 * 质量规范详情- 基础信息条
 */

const props = defineProps<{
  /** 质量规范id */
  id: string;
}>();

const specificationDetail = ref<QualitySpecificationItem>({
  enabled: false
} as QualitySpecificationItem);

const loading = ref(false);

/**
 * @description: 格式化权重
 */
const formatWeight = (weight?: number | null) => {
  if (typeof weight !== "number") {
    return emptyDefaultValue;
  }
  return weight + "%";
};

/**
 * @description: 请求质量规范详情
 */
const requestSpecificationDetail = useLoadingFn(async () => {
  const result = await queryQualitySpecificationById(props.id);
  specificationDetail.value = result.data;
}, loading);

onMounted(requestSpecificationDetail);

defineExpose({
  specificationDetail
});
</script>
<style lang="scss" scoped></style>
