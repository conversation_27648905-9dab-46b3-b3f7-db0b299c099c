import { http } from "@/utils/http";
import { withApiGateway } from "../util";
import {
  IListNoPageResponse,
  AcquisitionMaintain,
  IResponse,
  IAcquisitionMaintainForm,
  IPointReq,
  IPointsRes,
  INormalDistributionParams,
  INormalDistributionData
} from "@/models";
// 获取采集点工序信息列表
export const queryAutomaticCollection = (deviceId: string, processId: string) => {
  return http.get<{ deviceId: string; processId: string }, IListNoPageResponse<AcquisitionMaintain>>(
    withApiGateway(`admin-api/business/device/getAutomaticCollectionItem/${deviceId}/${processId}`)
  );
};
// 根据设备id查询该设备下未绑定过的采集点信息
export const deviceIdAcquisitionlist = (deviceId: string, processId: string) => {
  return http.get<{ deviceId: string; processId: string }, IListNoPageResponse<AcquisitionMaintain>>(
    withApiGateway(
      `admin-api/business/deviceCollectionPoint/getNotBoundPointByDeviceProcessId/${deviceId}/${processId}`
    )
  );
};
// 维护标准采集点绑定
export const bindAcquisitionPoint = (data: IAcquisitionMaintainForm) => {
  return http.post<IAcquisitionMaintainForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/deviceCollectionPoint/bindStandardPoint"),
    { data }
  );
};
// 维护标准采集点解绑
export const unbindAcquisitionPoint = (data: IAcquisitionMaintainForm) => {
  return http.post<IAcquisitionMaintainForm, IResponse<boolean>>(
    withApiGateway("admin-api/business/deviceCollectionPoint/unbindStandardPoint"),
    { data }
  );
};

// 根据设备id和工序id获取采集项信息
export const getPointByDeviceIdProcessId = (params: IPointReq) => {
  return http.get<void, IResponse<Array<IPointsRes>>>(
    withApiGateway("admin-api/business/deviceCollectionPoint/point-model-info"),
    { params }
  );
};

/**
 * @description: 获取正太分布数据
 */
export const getNormalDistributionData = (params: INormalDistributionParams) => {
  return http.post<INormalDistributionParams, IResponse<INormalDistributionData>>(
    withApiGateway("admin-api/rds-data/agg/singlePointAgg"),
    {
      data: params
    }
  );
};
