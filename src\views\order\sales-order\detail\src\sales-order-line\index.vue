<!-- 销售订单-详情-维护销售订单行 -->
<template>
  <div class="bg-bg_color p-5 flex-1 overflow-hidden w-full flex flex-col">
    <div class="w-full pb-4 px-2 flex justify-between">
      <div class="flex flex-row">
        <el-checkbox v-model="state.check" size="large" @change="queryFaultCount()">
          采购订单行号缺失
          <span class="color-warning">（{{ salesOrderLineManagementStore.salesOrderLineFaultCount }}条）</span>
        </el-checkbox>
      </div>
      <ElButton
        v-auth="PermissionKey.form.formPurchaseSalesCreate"
        size="large"
        type="primary"
        :icon="Plus"
        @click="onCreate()"
        >新增销售订单行明细
      </ElButton>
    </div>
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.salesOrderLineTableData"
      :columns="columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
    >
      <template #subClassCode="data">
        <span> {{ getSubClassNameByCode(data.row.subClassCode) }}</span>
      </template>
      <template #operation="data">
        <div>
          <ElButton v-auth="PermissionKey.form.formPurchaseSalesEdit" type="primary" link @click="onEdit(data.row.id)">
            编辑
          </ElButton>
          <ElButton
            v-auth="PermissionKey.form.formPurchaseSalesDelete"
            link
            type="danger"
            @click="onDelete(data.row.id)"
          >
            删除
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>
  </div>

  <el-dialog
    :overflow-hidden="true"
    :title="getDetailFormModalTitle()"
    align-center
    class="middle"
    destroy-on-close
    v-model="state.detailFormModalVis"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onCancelDetailFormModal"
  >
    <SalesOrderLineDetail
      ref="salesOrderLineDetailRef"
      :so-no="salesOrderDetailStore.salesOrder.soNo"
      :category-code="salesOrderDetailStore.salesOrder.categoryCode"
      @unbindPurchaseItemId="unbindPurchaseItemId"
    />
    <!-- <el-divider class="!mt-1" /> -->
    <div class="w-full flex justify-between my-3">
      <TitleBar title="关联采购订单行号" />
      <div>
        <el-button type="default" @click="select()"
          ><FontIcon class="mr-1.5" icon="icon-a-search2" />选择采购订单行号
        </el-button>
        <el-button type="primary" @click="add()" :icon="Plus">手动添加</el-button>
      </div>
    </div>
    <PureTable
      class="flex-1 pagination h-[225px]"
      row-key="id"
      :columns="columnsLink"
      size="default"
      :data="orderList"
      showOverflowTooltip
    >
      <template #operation="data">
        <ElButton type="danger" link @click="onRemove(data)">移除</ElButton>
      </template>
      <template #poItemNo="data">
        <span v-if="data.row?.purchaseLineId">{{ data.row.poItemNo }}</span>
        <div v-else>
          <el-input
            v-model="data.row.poItemNo"
            placeholder="请输入采购订单行项目号"
            v-if="!data.row?.isExist"
            @input="input(data)"
          />
          <div class="flex flex-col" v-else>
            <el-input v-model="data.row.poItemNo" class="danger-input" @input="input(data)" />
            <span class="danger-info">采购订单行项目号重复</span>
          </div>
        </div>
      </template>
      <template #poNo="data">
        <span v-if="data.row?.purchaseLineId">{{ data.row.poNo }}</span>
        <div v-else>
          <el-input
            v-if="!data.row?.isExist"
            v-model="data.row.poNo"
            placeholder="请输入采购订单号"
            @input="input(data)"
          />
          <div class="flex flex-col" v-else>
            <el-input v-model="data.row.poNo" class="danger-input" @input="input(data)" />
            <span class="danger-info">采购订单行项目号重复</span>
          </div>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image> <EmptyData /> </template>
        </el-empty>
      </template>
    </PureTable>
    <el-dialog
      v-model="state.selectPurchaseOrderDialogVisible"
      title="选择采购订单行项目号"
      class="middle"
      align-center
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <LinkPurchaseOrderLine ref="purchaseOrderRef" :purchaseItem="purchaseLines" :subClassCode="state.subClassCode" />
      <template #footer>
        <span>
          <el-button @click="onCancelSelectPurchaseOrder()">取消</el-button>
          <el-button type="primary" @click="onConfirmSelectPurchaseOrderLine()">选中</el-button>
        </span>
      </template>
    </el-dialog>
    <template #footer>
      <el-button @click="onCancelDetailFormModal()">取消</el-button>
      <el-button type="warning" :loading="continueLoading" @click="handleSaveAndContinue()" v-if="!state.selectId"
        >保存，并继续新增</el-button
      >
      <el-button type="primary" :loading="saveLoading" @click="onSaveDetailFormModal()">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import LinkPurchaseOrderLine from "../../../components/link-purchase-order-line-detail/index.vue";
import TitleBar from "@/components/TitleBar";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import SalesOrderLineDetail from "./sales-order-line-detail/index.vue";
import { Plus } from "@element-plus/icons-vue";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { useColumns } from "./columns";
import { IPurchaseLines, ISalesOrderLineLinkPurchase, ISalesOrderLineDetail, ISalesOrderLineParams } from "@/models";
import { useTableConfig } from "@/utils/useTableConfig";
import { useSalesOrderLineHook } from "../../../hooks/sales-order-line-hook";
import { useCategoryStore, useSalesOrderDetailStore, useSalesOrderLineManagementStore } from "@/store/modules";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { omit } from "lodash-unified";
import { useConfirm } from "@/utils/useConfirm";
import { PermissionKey } from "@/consts";

const route = useRoute();
const { columns, columnsLink } = useColumns();
const { pagination } = useTableConfig();
const saveLoading = ref<boolean>(false);
const continueLoading = ref<boolean>(false);
const loading = ref<boolean>(false);
const purchaseOrderRef = ref<InstanceType<typeof LinkPurchaseOrderLine>>();
const salesOrderLineDetailRef = ref<InstanceType<typeof SalesOrderLineDetail>>();
const orderList = ref<Array<IPurchaseLines & { isExist?: boolean }>>([]);
const purchaseLines = computed(() => orderList.value.filter(order => !!order.purchaseLineId) || []);
const salesOrderLineManagementStore = useSalesOrderLineManagementStore();
const salesOrderDetailStore = useSalesOrderDetailStore();
const categoryStore = useCategoryStore();
const {
  querySalesOrderLine,
  clearSalesOrderLineDetail,
  createSalesOrderLineDetail,
  deleteSaleOrderLineById,
  getSalesOrderLineDetailById,
  setSalesOrderLineDetailStorage,
  editSalesOrderLineDetail
} = useSalesOrderLineHook();

const state = reactive<{
  num: number;
  salesOrderLineTableData: Array<ISalesOrderLineLinkPurchase>;
  selectId: string;
  check: boolean;
  detailFormModalVis: boolean;
  params: ISalesOrderLineParams;
  selectPurchaseOrderDialogVisible: boolean;
  subClassCode: string;
}>({
  num: 0,
  salesOrderLineTableData: [],
  selectId: "",
  detailFormModalVis: false,
  params: {
    salesId: `${route.params?.id}`
  },
  selectPurchaseOrderDialogVisible: false,
  subClassCode: "",
  check: false
});

onMounted(async () => {
  await categoryStore.getAllSubclasses();
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
});

const queryFaultCount = async () => {
  state.params.isFaultPoItem = state.check;
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
};
const getDetailFormModalTitle = () => (!state.selectId ? "新增销售订单行明细" : "编辑销售订单行明细");

const onCreate = () => {
  state.detailFormModalVis = true;
  state.selectId = "";
};

const onEdit = async (id: string) => {
  state.selectId = id;
  const saleOrderLineDetail: ISalesOrderLineDetail = await getSalesOrderLineDetailById(id);
  if (!saleOrderLineDetail) {
    return;
  }
  orderList.value = saleOrderLineDetail.purchaseLines;
  setSalesOrderLineDetailStorage(saleOrderLineDetail);
  state.detailFormModalVis = true;
};

const onDelete = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await deleteSaleOrderLineById(id);
  ElMessage.success("删除成功");
  pagination.currentPage = 1;
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
  salesOrderDetailStore.refreshStepStatus();
};

const onCancelDetailFormModal = () => {
  clearForm();
};

const onSave = async () => {
  const formValue: ISalesOrderLineDetail | false = await salesOrderLineDetailRef.value
    .getValidValue()
    .catch(() => false);
  if (!formValue) {
    return;
  }
  expandForm(formValue);
  if (!state.selectId) {
    await createSalesOrderLineDetail(formValue);
  } else {
    await editSalesOrderLineDetail(formValue);
  }
  clearForm();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
  salesOrderDetailStore.refreshStepStatus();
};

const onSaveAndContinue = async () => {
  const formValue: ISalesOrderLineDetail | false = await salesOrderLineDetailRef.value
    .getValidValue()
    .catch(() => false);
  if (!formValue) {
    return;
  }
  expandForm(formValue);
  await createSalesOrderLineDetail(formValue);
  orderList.value.length = 0;
  await salesOrderLineDetailRef.value.resetFields();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
  salesOrderDetailStore.refreshStepStatus();
};

watch(
  () => salesOrderLineManagementStore.total,
  () => {
    pagination.total = salesOrderLineManagementStore.total;
  },
  {
    immediate: true
  }
);

const queryParams = () => {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  if (!state.params.isFaultPoItem) {
    state.params = omit(state.params, "isFaultPoItem");
  }
  return state.params;
};
function expandForm(formValue: ISalesOrderLineDetail) {
  formValue.purchaseLines = orderList.value
    .map(salesOrderLine => {
      return {
        poNo: salesOrderLine.poNo,
        poItemNo: salesOrderLine.poItemNo
      };
    })
    .filter(order => !!order.poItemNo && !!order.poNo);
}
function clearForm() {
  orderList.value.length = 0;
  clearSalesOrderLineDetail();
  state.detailFormModalVis = false;
}

async function select() {
  const subClassCode: string | false = await salesOrderLineDetailRef.value.getValidSubClassCode().catch(() => false);
  if (subClassCode) {
    state.subClassCode = subClassCode;
    state.selectPurchaseOrderDialogVisible = true;
  }
}

function onRemove(data) {
  orderList.value.splice(data.index, 1);
  orderList.value.forEach(data => {
    if (!data.purchaseId) {
      data.isExist = orderList.value.some(
        order => order.poNo == data.poNo && order.poItemNo == data.poItemNo && !!order.purchaseLineId
      );
    }
  });
}

function add() {
  orderList.value.unshift({
    purchaseLineId: "",
    purchaseId: "",
    poItemNo: "",
    poNo: "",
    conName: ""
  });
}
const onCancelSelectPurchaseOrder = () => {
  state.selectPurchaseOrderDialogVisible = false;
};

const onConfirmSelectPurchaseOrderLine = () => {
  state.selectPurchaseOrderDialogVisible = false;
  const purchaseLines: Array<IPurchaseLines> = purchaseOrderRef.value.getsalesLineLinkPurchaseItemIds();

  // 删减元素后的订单列表
  const cutedOrderList = orderList.value.filter(o => {
    if (!o.purchaseLineId) {
      return true;
    }
    const matching = purchaseLines.find(p => p.poNo === o.poNo && p.poItemNo === o.poItemNo);
    return matching;
  });

  // 需要新增的订单
  const addedOrderList = purchaseLines.filter(p => {
    const matching = cutedOrderList.find(o => {
      return p.poNo === o.poNo && p.poItemNo === o.poItemNo;
    });
    return !matching;
  });

  // 如果手动输入的已存在，就进行替换
  orderList.value = cutedOrderList.concat(addedOrderList).map(o => {
    const matching = purchaseLines.find(p => {
      if (!o.purchaseLineId && o.poNo === p.poNo && o.poItemNo === p.poItemNo) {
        return true;
      }
    });
    return matching || o;
  });
};

const getSubClassNameByCode = (subClassCode: string) => {
  if (!subClassCode) {
    return "--";
  }
  const categories = categoryStore.subclasses.find(categories => categories.categoryCode == subClassCode);
  return categories ? categories.categoryName : "--";
};
const getSalesOrderLine = useLoadingFn(querySalesOrderLine, loading);
const onSaveDetailFormModal = useLoadingFn(onSave, saveLoading);
const handleSaveAndContinue = useLoadingFn(onSaveAndContinue, continueLoading);

function unbindPurchaseItemId() {
  orderList.value.length = 0;
}

function input(data) {
  data.row.isExist = orderList.value.some(
    (order, index) => order.poItemNo == data.row.poItemNo && order.poNo == data.row.poNo && data.index != index
  );
}

async function onPageSizeChange() {
  pagination.currentPage = 1;
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
}

async function onCurrentPageChange() {
  state.salesOrderLineTableData = await getSalesOrderLine(queryParams());
}
</script>
<style scoped lang="scss">
.danger-info {
  color: var(--el-color-danger);
  font-size: 0.75rem;
}

.color-warning {
  color: var(--el-color-warning);
}

:deep(.danger-input .el-input__wrapper) {
  box-shadow: 0 0 0 0.0625rem var(--el-color-danger) inset;
}
</style>
