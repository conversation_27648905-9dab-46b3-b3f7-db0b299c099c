import { IStateGridOrderSyncDetailList, IPagingReq } from "@/models";
import { StateGridOrderSyncStep } from "@/enums";

export interface IStateGridOrderSync {
  id: string;
  poNo?: string;
  poItemNo?: string;
  purchaseId?: string;
  purchaseOrderId?: string;
  materialCode?: string;
  materialDesc?: string;
  amount?: number;
  matMinName?: string;
  syncResult?: boolean;
  subClassCode?: string;
  subClassName?: string;
  detail?: IStateGridOrderSyncDetailList[];
}
/** 上海IOT 同步订单数据模型*/
export interface IIOTStateGridOrderSync {
  /** ID */
  id: string;
  /**销售订单行ID */
  salesId: string;
  /** 销售订单编号 */
  soNo: string;
  /** 销售订单行号 */
  soItemNo?: string;
  /**合同编号 */
  conCode: string;
  /**项目名称 */
  prjName: string;
  /** 品类编码 */
  categoryCode?: string;
  /**品类名称 */
  categoryName?: string;
  /** 同步标识id(多个) */
  matSyncFlagId?: string;
  /**采购方公司名称 */
  buyerName: string;
  /**数据同步结果 */
  synType?: boolean;
  /**同步详细 */
  detail?: Array<IStateGridOrderSyncDetailList>;
  subClassCode: string;
}

export interface ISalesSyncListItemQueryPrams extends IPagingReq {
  saleId?: string;
  saleLineId: string;
  saleNo?: string;
  syncProcess: StateGridOrderSyncStep;
  channel: number;
}
