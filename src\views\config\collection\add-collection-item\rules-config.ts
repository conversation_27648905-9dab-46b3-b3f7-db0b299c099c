import { reactive, watch } from "vue";
import { FormRules } from "element-plus";
import { ISaveCollectionItemsReq } from "@/models/collection-items";
import { EControlType } from "@/enums";

/**
 * @description: 生成表单规则
 */
export function genRules(collectionForm: ISaveCollectionItemsReq) {
  const rules = reactive<FormRules>({
    targetName: [{ required: true, message: "请输入数据项", trigger: "change" }],
    targetNameAlias: [{ required: false, message: "请输入别名", trigger: "change" }],
    targetCode: [{ required: true, message: "请输入数据项编码", trigger: "change" }],
    collectionType: [{ required: false, message: "请选择数据采集方式", trigger: "change" }],
    required: [{ required: true, message: "请选择", trigger: "change" }],
    unit: [{ required: false, message: "请输入单位", trigger: "change" }],
    datumOrganization: [{ required: false, message: "请输入数据格式要求", trigger: "change" }],
    identityCode: [{ required: true, message: "请选择表单控件类型", trigger: "change" }],
    identityDetailList: [{ required: true, message: "请输入选项列表", trigger: "change" }],
    sort: [{ required: false, message: "请输入排序数字", trigger: "change" }],
    // 校验信息
    // 文本
    maxStrLength: [{ required: false, message: "请输入", trigger: "change" }],
    useRegular: [{ required: false, message: "请输入", trigger: "change" }],
    regular: [{ required: false, message: "请输入", trigger: "change" }],

    // 数字
    validMaxValue: [{ required: false, message: "请输入", trigger: "change" }],
    maxValue: [{ required: false, message: "请输入", trigger: "change" }],
    includeMaxValue: [{ required: false, message: "请输入", trigger: "change" }],
    validMinValue: [{ required: false, message: "请输入", trigger: "change" }],
    minValue: [{ required: false, message: "请输入", trigger: "change" }],
    includeMinValue: [{ required: false, message: "请输入", trigger: "change" }],
    decimalDigits: [{ required: false, message: "请输入小数位", trigger: "change" }],

    // 日期格式化
    format: [{ required: true, message: "请选择", trigger: "change" }],

    // 文件
    fileType: [{ required: false, message: "请选择", trigger: "change" }],
    fileSize: [{ required: false, message: "请输入", trigger: "change" }],

    dateTypeCode: [{ required: true, message: "请输入日期时间格式化", trigger: "change" }]
  });

  // 监听表单值变化
  watch(
    [() => collectionForm.useRegular, () => collectionForm.validMaxValue, () => collectionForm.validMinValue],
    ([useRegular, validMaxValue, validMinValue]) => {
      // 文本正则必填设置
      rules.regular = [{ required: useRegular, message: "请输入", trigger: "change" }];
      // 数字最大值，最小值 必填设置
      rules.maxValue = [{ required: validMaxValue, message: "请输入", trigger: "change" }];
      rules.minValue = [{ required: validMinValue, message: "请输入", trigger: "change" }];
      if (!validMaxValue) collectionForm.maxValue = null;
      if (!validMinValue) collectionForm.minValue = null;
    }
  );

  watch(
    () => collectionForm.identityCode,
    code => {
      if (code === EControlType.WaveRoseControl) {
        rules.waveFormConfig = [{ required: true, message: "请输入波形图配置", trigger: "change" }];
        rules.identityDetailList = [{ required: false, message: "请输入选项列表", trigger: "change" }];
      } else {
        rules.identityDetailList = [{ required: true, message: "请输入选项列表", trigger: "change" }];
        rules.waveFormConfig = [{ required: false, message: "请输入波形图配置", trigger: "change" }];
      }
    }
  );
  return rules;
}
