import { TableWidth } from "@/enums";

export function useColumns() {
  const columns: TableColumnList = [
    {
      type: "selection",
      minWidth: TableWidth.check
    },
    {
      label: "原材料编号",
      prop: "code",
      minWidth: TableWidth.order
    },
    {
      label: "原材料批次",
      prop: "materialBatchNo",
      minWidth: TableWidth.order
    },
    {
      label: "原材料名称",
      prop: "name",
      minWidth: TableWidth.name
    },
    {
      label: "原材料类型",
      prop: "processName",
      minWidth: TableWidth.type
    },
    {
      label: "品牌",
      prop: "borMaterials",
      minWidth: TableWidth.type
    },
    {
      label: "计量单位",
      prop: "partUnit",
      minWidth: TableWidth.unit
    },
    {
      label: "规格型号",
      prop: "modelCode",
      minWidth: TableWidth.type
    }
  ];

  return {
    columns
  };
}
