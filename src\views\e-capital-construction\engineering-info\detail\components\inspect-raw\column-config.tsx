import { TableWidth, BatchResultEnumMapDesc, MainRegulatEnumMapDesc, MaterialResultEnumMapDesc } from "@/enums";
import { TableColumnRenderer } from "@pureadmin/table";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

/**
 * @description: 原材料组部件检验
 */
export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columnsConfig: TableColumnList = [];
  const commonColumns: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: TableWidth.status,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: TableWidth.dateTime,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      minWidth: TableWidth.operation
    }
  ];
  if (isCombiner) {
    columnsConfig = [
      {
        label: "采集类型",
        prop: "collectionCode",
        minWidth: TableWidth.name,
        slot: "collectionCode"
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        width: TableWidth.name
      },
      {
        label: "子实物ID(单元)",
        prop: "subPhysicalItemId",
        width: TableWidth.name
      },
      {
        label: "批次检验结果",
        prop: "batchResult",
        width: TableWidth.largeNumber,
        cellRenderer: (data: TableColumnRenderer) => {
          return BatchResultEnumMapDesc[data.row.batchResult];
        }
      },
      {
        label: "原材料组件检验结果",
        prop: "materialComponentResult",
        width: TableWidth.name,
        cellRenderer: (data: TableColumnRenderer) => {
          return MaterialResultEnumMapDesc[data.row.materialComponentResult];
        }
      },
      {
        label: "钢印号/字头号",
        prop: "stampingNumber",
        width: TableWidth.name
      }
    ];
  } else {
    columnsConfig = [
      {
        label: "采集类型",
        prop: "collectionCode",
        width: TableWidth.name,
        slot: "collectionCode"
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        width: TableWidth.name
      },
      {
        label: "主体变/调补变类型",
        prop: "mainRegulatingTransformer",
        width: TableWidth.name,
        cellRenderer: (data: TableColumnRenderer) => {
          return MainRegulatEnumMapDesc[data.row.mainRegulatingTransformer];
        }
      }
    ];
  }

  return [...columnsConfig, ...commonColumns];
}
