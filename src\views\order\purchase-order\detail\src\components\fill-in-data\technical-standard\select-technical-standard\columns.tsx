import { TableWidth } from "@/enums";
import CxTag from "@/components/CxTag/index.vue";

export function useColumns() {
  const columns: TableColumnList = [
    {
      type: "index",
      width: TableWidth.check,
      label: "选择",
      slot: "radioGroup"
    },
    {
      label: "技术标准名称",
      prop: "standardName"
    },
    {
      label: "物料编号",
      prop: "materialCode"
    },
    {
      label: "规格型号",
      prop: "specificationModel"
    },
    {
      label: "是否通用标准",
      prop: "isStandard",
      width: TableWidth.dateTime,
      formatter: row => {
        return <CxTag type={row.isStandard ? "primary" : "info"}>{row.isStandard ? "是" : "否"}</CxTag>;
      }
    }
  ];

  return { columns };
}
