<template>
  <BaseList :columns="columns" type="raw-material" v-bind="$attrs" ref="baseList" />
  <EditRawMaterialDialog ref="editDialog" :subclass-code="subclassCode" />
  <DetailRawMaterialDialog ref="detailDialog" />
  <FileSyncListDialog :files="files" hide-operation v-model="fileSyncListVisible" />
</template>

<script setup lang="ts">
import { KeywordAliasEnum, StateGridOrderSyncResult, TableWidth } from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import BaseList from "../base-list.vue";
import { useFileFormatter } from "../hooks/useFileFormatter";
import { computed, h, provide, ref } from "vue";
import { syncAuditCellSpanKey } from "../../tokens";
import { OperatorCell } from "@/components/TableCells";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditRawMaterialDialog from "@/views/components/state-grid-order-sync/dialogs/edit-raw-material-dialog.vue";
import { useEditDetailDialog } from "@/views/components/state-grid-order-sync/hooks/useEditDetailDialog";
import { IRawMaterialSync } from "@/models";
import DetailRawMaterialDialog from "@/views/components/state-grid-order-sync/dialogs/detail-raw-material-dialog.vue";
import { linkFormatter } from "@/views/components/state-grid-order-sync/formatters/link-formatter";
import { fileSyncFormatter } from "@/views/components/state-grid-order-sync/formatters/file-sync-formatter";
import FileSyncListDialog from "@/views/components/state-grid-order-sync/dialogs/file-sync-list-dialog.vue";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

const store = useStateGridSyncAuditStore();

const { enumFormatter } = useTableCellFormatter();
const fileFormatter = useFileFormatter();
const { editDialog, detailDialog, openEditDialog, openDetailDialog } = useEditDetailDialog<
  InstanceType<typeof EditRawMaterialDialog>,
  InstanceType<typeof DetailRawMaterialDialog>,
  IRawMaterialSync
>();

const activeFileSyncId = ref<string>();
const fileSyncListVisible = ref(false);
const baseList = ref<InstanceType<typeof BaseList>>();

const files = computed(() => {
  const data = baseList.value?.getData().value as Array<IRawMaterialSync> | undefined;
  const sync = data?.find(datum => datum.id === activeFileSyncId.value) as IRawMaterialSync | undefined;
  return sync?.fileList || [];
});
const subclassCode = computed(() => store.subClassCode);

const columns: TableColumnList = [
  {
    label: "工序",
    prop: "processName",
    minWidth: TableWidth.name
  },
  {
    label: store.isCable ? "生产订单号" : "生产工单号",
    prop: "orderNo",
    headerRenderer: () =>
      h(KeywordAliasHeader, {
        code: store.isCable ? KeywordAliasEnum.IPO_NO : "product_work_order_no",
        defaultText: store.isCable ? "生产订单号" : "生产工单号"
      }),
    width: TableWidth.order
  },
  {
    label: "原材料编码",
    prop: "code",
    minWidth: TableWidth.suborder,
    formatter: linkFormatter(openDetailDialog)
  },
  {
    label: "原材料名称",
    prop: "name",
    minWidth: TableWidth.name
  },
  {
    label: "检验批次号",
    prop: "inspectBatchNo",
    minWidth: TableWidth.name
  },
  {
    label: "文件同步状态",
    prop: "fileList",
    minWidth: TableWidth.type,
    formatter: fileSyncFormatter(id => {
      activeFileSyncId.value = id;
      fileSyncListVisible.value = true;
    })
  },
  {
    label: "生产厂检测报告",
    prop: "inspectionReport",
    minWidth: TableWidth.largeName,
    formatter: fileFormatter
  },
  {
    label: "来料检测报告",
    prop: "spotCheckReport",
    minWidth: TableWidth.largeName,
    formatter: fileFormatter
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "检测项",
    prop: "inspectionName",
    minWidth: TableWidth.name,
    fixed: "right"
  },
  {
    label: "检测值",
    prop: "inspectionValue",
    minWidth: TableWidth.order,
    fixed: "right",
    className: "error-mark"
  },
  {
    label: "数据格式要求",
    prop: "inspectionRule",
    minWidth: TableWidth.name,
    fixed: "right",
    className: "error-mark"
  },
  {
    label: "操作",
    prop: "operator",
    width: TableWidth.operation,
    fixed: "right",
    className: "no-default",
    cellRenderer: data => {
      if (!data.row.editable) {
        return null;
      }
      return OperatorCell([
        {
          name: "编辑",
          action: () => openEditDialog(data.row),
          props: { type: "primary" }
        }
      ]);
    }
  }
];

provide(stateGridOrderSyncEditKey, {
  refreshFn: () => baseList.value?.refresh()
});
provide(syncAuditCellSpanKey, {
  uniqKeys: ["processCode", "processCode_orderNo", "id"],
  spanTypes: {
    processName: "processCode",
    orderNo: "processCode_orderNo",
    syncResult: "id",
    spotCheckReport: "id",
    inspectionReport: "id",
    inspectBatchNo: "id",
    code: "id",
    name: "id",
    operator: "id"
  }
});
</script>

<style scoped></style>
