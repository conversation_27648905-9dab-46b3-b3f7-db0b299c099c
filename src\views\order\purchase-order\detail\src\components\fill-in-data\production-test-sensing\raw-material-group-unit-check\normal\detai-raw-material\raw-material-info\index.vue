<template>
  <div class="mb-4 cx-form">
    <TitleBar title="基础信息" class="mb-2" />
    <el-descriptions class="mx-4">
      <el-descriptions-item label="原材料编号">
        {{ baseInfoDetail.code }}
      </el-descriptions-item>
      <el-descriptions-item label="原材料名称">
        {{ baseInfoDetail.name }}
      </el-descriptions-item>
      <el-descriptions-item label="原材料类型">
        {{ baseInfoDetail.processName }}
      </el-descriptions-item>
      <el-descriptions-item label="原材料批次号">
        {{ baseInfoDetail.materialBatchNo }}
      </el-descriptions-item>
      <el-descriptions-item label="品牌">
        {{ baseInfoDetail.borMaterials }}
      </el-descriptions-item>
      <el-descriptions-item label="计量单位">
        {{ baseInfoDetail.partUnit }}
      </el-descriptions-item>
      <el-descriptions-item label="制造商">
        {{ baseInfoDetail.rawmManufacturer }}
      </el-descriptions-item>
      <el-descriptions-item label="产地">
        {{ baseInfoDetail.oorMaterials }}
      </el-descriptions-item>
      <el-descriptions-item label="生产日期">
        {{ baseInfoDetail.productionDate }}
      </el-descriptions-item>
      <el-descriptions-item label="用料量">
        {{ baseInfoDetail.quantity }}
      </el-descriptions-item>
      <el-descriptions-item label="检验批次号">
        {{ baseInfoDetail.inspectBatchNo }}
      </el-descriptions-item>
      <el-descriptions-item label="检验日期">
        {{ baseInfoDetail.inspectDate }}
      </el-descriptions-item>
      <el-descriptions-item label="电压等级">
        {{ baseInfoDetail.voltageGrade }}
      </el-descriptions-item>
      <el-descriptions-item :span="2" label="规格型号">
        {{ baseInfoDetail.modelCode }}
      </el-descriptions-item>
      <el-descriptions-item :span="3" label="备注">
        {{ baseInfoDetail.inspectRemark }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar";

defineProps({
  baseInfoDetail: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style scoped lang="scss"></style>
