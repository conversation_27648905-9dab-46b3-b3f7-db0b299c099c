import { defineStore } from "pinia";
import * as api from "@/api/category";
import { ICategory, ICategoryInfoTree, IOrderSubclassParams } from "@/models";

type CategoryType = {
  categories: Array<ICategory>;
  subclasses: Array<ICategory>;
  categorySubclassTree: Array<ICategoryInfoTree>;
  tenantCategoryCodeTree: Array<ICategoryInfoTree>;
};
/** 物资种类 */
export const useCategoryStore = defineStore({
  id: "cx-category-store",
  state: (): CategoryType => ({
    categories: [],
    subclasses: [],
    /** 新增编辑租户时 选择的物资种类 */
    categorySubclassTree: [] as Array<ICategoryInfoTree>,
    /** 工艺路线时 查询当前租户下物资种类 */
    tenantCategoryCodeTree: [] as Array<ICategoryInfoTree>
  }),
  getters: {
    allSubclasses(): Promise<Array<ICategory>> {
      // todo: add retry if failure
      return api.getAllSubclasses().then(res => res.data);
    }
  },
  actions: {
    async getCategories() {
      if (!this.categories.length) {
        this.categories = (await api.getCategories()).data;
      }
      return this.categories;
    },
    async getAllSubclasses() {
      if (!this.subclasses.length) {
        this.subclasses = (await api.getAllSubclasses()).data;
      }
      return this.subclasses;
    },
    async querySubclassesByCategory(categoryCode: string) {
      return api.querySubclassesByCategory(categoryCode).then(res => res.data);
    },
    async querySubclassesByOrderId(params: IOrderSubclassParams) {
      return api.querySubclassesByOrderId(params).then(res => res.data);
    },
    async queryCategoryContainProcessTree() {
      return api.queryCategoryContainProcessTree().then(res => res.data);
    },
    async queryCategoryInfoTenantCategoryList() {
      return api.queryCategoryInfoTenantCategoryList().then(res => res.data);
    },

    async querySubMinClassCodeList(subClassCode: string) {
      return api.querySubclassMinClassCodeList(subClassCode).then(res => res.data);
    },
    async queryCategorySubclassTreeByTenantId(tenantId: string) {
      const res = await api.queryCategorySubclassTreeByTenantId(tenantId);
      this.categorySubclassTree = res.data;
    },

    /** 查询当前租户的物资种类及其下属品类（树形） */
    async queryTenantCategoryCodeTree() {
      const res = await api.queryTenantCategoryCodeTree();
      this.tenantCategoryCodeTree = res.data;
    }
  }
});
