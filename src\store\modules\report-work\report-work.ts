import { ICreateReportWork, IReportWork, IReportWorkReq, IResponse } from "@/models";
import { defineStore } from "pinia";
import * as ReportWorkService from "@/api/report-work";

export const useReportWorkStore = defineStore({
  id: "cx-report-work-store",
  state: () => ({
    reportWorks: [] as Array<IReportWork>,
    reportWorkFormModalVis: false as boolean,
    reportWorkTableModalVis: false as boolean,
    isAddReportWork: false as boolean,
    reportWorkDetail: {} as IReportWork,
    latestReportWork: {} as IReportWork,
    reportWorkParams: undefined as IReportWorkReq,
    loading: false,
    reportWorkFormProps: { subclassCode: "", processIds: "", minClassCode: "" },
    total: 0,
    freshReportWorkTag: false
  }),
  actions: {
    setReportWorkParams(params: IReportWorkReq) {
      this.reportWorkParams = params;
    },
    async refreshReportWorks(pageNo = 1) {
      this.reportWorkParams.pageNo = pageNo;
      this.queryReportWorkPaging();
    },

    async queryReportWorkPaging() {
      this.loading = true;
      const res = await ReportWorkService.queryReportWorkPaging(this.reportWorkParams);
      this.reportWorks = res.data.list;
      this.total = res.data.total;
      this.loading = false;
    },

    async createReportWork(data: ICreateReportWork): Promise<IResponse<boolean>> {
      return ReportWorkService.createReportWork(data);
    },

    async editorReportWork(data: ICreateReportWork): Promise<IResponse<boolean>> {
      return ReportWorkService.editReportWork(data);
    },

    async getReportWorkById(id: string) {
      const reportWorkRes = await ReportWorkService.getReportWorkById(id);
      this.reportWorkDetail = reportWorkRes.data;
      return reportWorkRes.data;
    },

    async deleteReportWork(id: string): Promise<IResponse<boolean>> {
      return ReportWorkService.deleteReportWork(id);
    },

    async getLatestReportWorkById(id: string) {
      const res = await ReportWorkService.getLatestReportWorkById(id);
      this.latestReportWork = res.data;
      return res.data;
    },

    setReportWorkDetail(reportWorkDetail?: Partial<IReportWork>) {
      this.reportWorkDetail = reportWorkDetail;
    },

    setReportWorkFormModalVis(visible: boolean) {
      this.reportWorkFormModalVis = visible;
    },

    clearReportWorkDetailAndShowReportWorkFormModal() {
      this.setReportWorkDetail();
      this.setReportWorkFormModalVis(true);
    },

    setReportWorkTableModalVis(visible: boolean) {
      this.reportWorkTableModalVis = visible;
    },

    setIsAddReportWork(isAddReportWork: boolean) {
      this.isAddReportWork = isAddReportWork;
    },

    setReportWorkProps(subclassCode: string, processIds: string, minClassCode?: string) {
      this.reportWorkFormProps = { subclassCode, processIds, minClassCode };
    },
    setFreshReportWorkTag(freshReportWorkTag: boolean) {
      this.freshReportWorkTag = freshReportWorkTag;
    }
  }
});
