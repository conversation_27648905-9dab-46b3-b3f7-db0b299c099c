<template>
  <el-radio-group v-model="state.saleOrderLineState" class="pb-4" @change="onChangeSaleOrderState()">
    <el-radio-button v-for="item of saleOrderLineStateOptions" :label="item.value" :key="item.value">{{
      item.label
    }}</el-radio-button>
  </el-radio-group>

  <pure-table
    row-key="id"
    border
    show-overflow-tooltip
    :data="salesOrderLineStore.salesOrderLines"
    :columns="columns"
    :height="273"
    :loading="state.loading"
    :span-method="onMergeGridRow"
    @row-click="onRowClick"
    :row-style="{ cursor: 'pointer' }"
  >
    <template #empty>
      <el-empty :image-size="120">
        <template #image> <EmptyData /> </template>
      </el-empty>
    </template>
  </pure-table>
</template>

<script setup lang="ts">
import { CreateSchedulingPlanStepEnum, FlowTypeEnum, SaleOrderLineStateEnum } from "@/enums";
import { IOption, ISalesOrderLineExt, SpanMethodProps } from "@/models";
import { useSalesOrderDetailStore, useSalesOrderLineStore, useSalesSchedulingPlanStore } from "@/store/modules";
import { reactive, watch } from "vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { useColumns } from "./columns";

const { columns } = useColumns();
const schedulingPlanStore = useSalesSchedulingPlanStore();
const salesOrderLineStore = useSalesOrderLineStore();
const salesOrderDetailStore = useSalesOrderDetailStore();

let saleOrderId: string;

const state = reactive<{
  saleOrderLineState: SaleOrderLineStateEnum;
  loading: boolean;
}>({
  saleOrderLineState: SaleOrderLineStateEnum.all,
  loading: true
});

const saleOrderLineStateOptions: Array<IOption> = [
  { label: "全部", value: SaleOrderLineStateEnum.all },
  { label: "有排产", value: SaleOrderLineStateEnum.ProductOrWorkOrderOrScheduling },
  { label: "无排产", value: SaleOrderLineStateEnum.NoProductOrWorkOrderOrScheduling }
];

const queryParams = (saleOrderLineState: SaleOrderLineStateEnum) => {
  return { saleId: saleOrderId, type: saleOrderLineState, flowType: FlowTypeEnum.PlanningScheduling };
};

const querySaleOrderLines = async () => {
  saleOrderId = salesOrderDetailStore.salesOrder?.id;
  await salesOrderLineStore.querySaleOrderLinesBySaleId(queryParams(state.saleOrderLineState));
  state.loading = false;
};

watch(
  () => schedulingPlanStore.activeCreateSchedulingPlanStep,
  (newVal: CreateSchedulingPlanStepEnum, oldVal: CreateSchedulingPlanStepEnum) => {
    if (
      newVal === CreateSchedulingPlanStepEnum.selectSaleOrderLine &&
      oldVal === CreateSchedulingPlanStepEnum.createSchedulingPlan
    ) {
      querySaleOrderLines();
    }
  }
);

watch(
  () => salesOrderDetailStore.salesOrder?.id,
  async () => {
    if (schedulingPlanStore.activeCreateSchedulingPlanStep === CreateSchedulingPlanStepEnum.createSchedulingPlan) {
      return;
    }
    querySaleOrderLines();
  },
  {
    immediate: true
  }
);

const onChangeSaleOrderState = () => {
  state.loading = true;
  schedulingPlanStore.setSchedulingPlanFormValue();
  querySaleOrderLines();
};

const onMergeGridRow = (data: SpanMethodProps) => {
  if (data.columnIndex === 0) {
    return {
      rowspan: data.row.rowSpan,
      colspan: data.row.colSpan
    };
  }
};

const onRowClick = (row: ISalesOrderLineExt) => {
  schedulingPlanStore.setSchedulingPlanFormValue({
    id: undefined,
    salesLineId: row.id,
    measUnit: row.materialUnit,
    purchaseId: undefined,
    ppNo: undefined,
    amount: row.materialNumber,
    planDate: undefined,
    planWorkDuration: undefined,
    deliveryDate: undefined,
    actStartDate: undefined,
    actEndDate: undefined,
    subClassCode: row.subClassCode
  });
};
</script>

<style scoped lang="scss"></style>
