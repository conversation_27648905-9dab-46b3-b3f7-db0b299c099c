import { useMaterial } from "@/utils/material";

/**
 * 原材料检 -- 电压等级是否必填（电缆终端和电缆中间接头必填）
 * @param subClassCode
 * <AUTHOR>
 */
export function voltageGardeRequireRawMaterial(subClassCode: string) {
  const useMaterialTool = useMaterial();
  return useMaterialTool.isCableTerminalAdapter(subClassCode);
}

/**
 * 工单 -- 电压等级是否必填（变压器&组合电器）
 * @param subClassCode
 * <AUTHOR>
 */
export function voltageGardeRequireWorkOrder(subClassCode: string) {
  const useMaterialTool = useMaterial();
  return useMaterialTool.isTransformerCompositeApparatus(subClassCode);
}
