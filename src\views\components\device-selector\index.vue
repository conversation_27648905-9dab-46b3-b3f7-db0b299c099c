<template>
  <el-select
    v-if="loaded"
    v-model="modelValue"
    :loading="loading"
    :placeholder="placeholder"
    :loading-text="SELECTOR_LOADING_TEXT"
    class="w-full"
    filterable
    clearable
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
  <el-select
    v-else
    class="w-full"
    :loading="loading"
    :placeholder="placeholder"
    :loading-text="SELECTOR_LOADING_TEXT"
    filterable
    clearable
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, withDefaults, computed } from "vue";
import { useVModels } from "@vueuse/core";
import { queryDeviceList } from "@/api/device/device";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { SELECTOR_LOADING_TEXT } from "@/consts/default-value";

interface IOption {
  label: string;
  value: string;
}

const props = withDefaults(
  defineProps<{
    /** 已选中id列表 */
    modelValue: string;
    /** 是否根据工序获取 */
    limitedByProcess?: boolean;
    /** 工序id filter模式时必传 */
    processId?: string;
  }>(),
  {
    limitedByProcess: false,
    processId: ""
  }
);

const emits = defineEmits<{
  (event: "update:modelValue", code: string): void;
}>();

const { modelValue } = useVModels(props, emits);
const loading = ref(false);
const options = ref<Array<IOption>>([]);
const loaded = ref(false);

const placeholder = computed(() => {
  if (props.limitedByProcess && !props.processId) {
    return "请先选择工序";
  }
  return "请选择设备";
});

/**
 * @description: 查询设备列表
 */
const requestDeviceList = useLoadingFn(async () => {
  // 若processIds为空，后端默认返回全部设备
  const { data } = await queryDeviceList({ processIds: [props.processId] });
  if (!data) {
    return;
  }
  options.value = data.map(({ id, deviceName, deviceCode }) => ({
    label: `${deviceName}(${deviceCode})`,
    value: id
  }));
  const isExist = options.value.find(({ value }) => value === modelValue.value);
  if (!isExist) {
    modelValue.value = "";
  }
  loaded.value = true;
}, loading);

watch(
  () => props.processId,
  async id => {
    if (id && props.limitedByProcess) {
      // 根据工序查询设备列表
      await requestDeviceList();
    }
  },
  {
    immediate: true
  }
);

onMounted(() => {
  if (!props.limitedByProcess) {
    requestDeviceList();
  }
});
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  height: var(--el-input-inner-height) !important;
}
</style>
