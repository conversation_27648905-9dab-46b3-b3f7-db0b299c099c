import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IListResponse, IResponse, I{{properCase name}},I{{properCase name }}Form, I{{properCase name}}Req } from "@/models";

/** 查询{{desc}}分页  */
export const query{{properCase name}} = (params: I{{properCase name}}Req) => {
  const url: string = withApiGateway(`admin-api/business/{{dashCase name}}/{{dashCase name}}`);
  return http.get<I{{properCase name}}Req, IListResponse<I{{properCase name}}>> (url, {
    params
  });
};

/** 根据{{desc}}id 查询详情 */
export const get{{properCase name}}ById = (id: string) => {
  const url: string = withApiGateway(`admin-api/business/{{dashCase name}}/{{dashCase name}}-detail-by-id/${id}`);
  return http.get<string, IResponse<I{{properCase name }}>> (url);
};

/** 新增{{desc}} */
export const create{{properCase name}} = (data: I{{properCase name }}Form) => {
  return http.post<I{{properCase name }}Form, IResponse<boolean>>(
    withApiGateway("admin-api/business/{{dashCase name}}/{{dashCase name}}"),
    { data },
    { showErrorInDialog: true }
  );
};

/** 编辑{{desc}} */
export const update{{properCase name}} = (data: I{{properCase name }}Form) => {
  return http.put<I{{properCase name }}Form, IResponse<boolean>>(
    withApiGateway('admin-api/business/{{dashCase name}}/{{dashCase name}}'),
    { data },
    { showErrorInDialog: true })
};

/** 删除{{desc}}根据Id */
export const delete{{properCase name}}ById = (id: string) => {
  return http.delete<string, IResponse<boolean>>(withApiGateway(`admin-api/business/{{dashCase name}}/{{dashCase name}}-by-id/${id}`));
};
