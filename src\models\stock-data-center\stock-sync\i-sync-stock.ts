/** 模块库存同步的状态 */
export enum ESyncStockStatus {
  NO_SYNC = "NO_SYNC", // 未同步
  TRANSFERRING = "TRANSFERRING", // 传输中
  SUCCEED_SYNC = "SUCCEED_SYNC", // 同步成功
  FAIL_SYNC = "FAIL_SYNC" // 同步失败
}

/** 库存同步数据 */
export interface IStockSync {
  dataType: number;
  dataId?: string;
}

export interface ISyncStockStatus {
  amount?: number;
  errorCount?: number;
  noSync?: number;
  result?: string;
  succeedCount?: number;
  syncInventoryEnum?: string;
}

export interface ISyncStockTagType extends ISyncStockStatus {
  processCode: string;
  processName: string;
  processId: string;
  syncStatus: string;
  permission: string;
}
