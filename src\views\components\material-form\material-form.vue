<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    class="cx-form"
    label-position="top"
    require-asterisk-position="right"
  >
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物资种类" prop="subClassCode">
          <SubclassSelect
            v-model="form.subClassCode"
            :disabled="disabledMap.subClassCode"
            @update:model-value="onSubClassCodeChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料编号" prop="materialCode">
          <el-input placeholder="请输入" v-model="form.materialCode" :disabled="disabledMap.materialCode" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="物料名称" prop="materialName">
          <el-input placeholder="请输入" v-model="form.materialName" :disabled="disabledMap.materialName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物料单位" prop="materialUnit">
          <Dictionary
            :parentCode="MEASURE_UNIT"
            :sub-class-code="form.subClassCode"
            class="w-full"
            v-model="form.materialUnit"
            placeholder="请选择"
            :disabled="disabledMap.materialUnit"
            clearable
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-form-item label="规格型号" prop="specificationModel">
          <el-input placeholder="请输入" v-model="form.specificationModel" :disabled="disabledMap.specificationModel" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="电压等级" prop="voltageClass">
          <EnumSelect
            class="w-full"
            allow-create
            v-model="form.voltageClass"
            placeholder="请选择/输入电压等级"
            :enum="VoltageClassesEnum"
            enumName="voltageClassesEnum"
            :disabled="disabledMap.voltageClass"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="40">
      <el-col>
        <el-form-item label="物料描述" prop="materialDescribe">
          <el-input
            type="textarea"
            placeholder="请输入"
            :rows="2"
            resize="none"
            v-model="form.materialDescribe"
            :disabled="disabledMap.materialDescribe"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElForm, FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IMaterialDto } from "@/models";
import { VoltageClassesEnum } from "@/enums";
import EnumSelect from "@/components/EnumSelect";
import Dictionary from "@/components/Dictionary";
import { MEASURE_UNIT } from "@/consts";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";

defineExpose({
  validate,
  getValidValue,
  initializeForm
});

const formRef = ref<FormInstance>();

const form = reactive<IMaterialDto>({
  id: undefined,
  materialCode: undefined,
  materialDescribe: undefined,
  specificationModel: undefined,
  materialName: undefined,
  materialUnit: undefined,
  voltageClass: undefined,
  subClassCode: undefined
});
const rules: FormRules = {
  materialCode: [{ required: true, trigger: "change", message: requiredMessage("物料编号") }],
  materialName: [{ required: true, message: requiredMessage("物料名称"), trigger: "change" }],
  materialDescribe: [{ required: true, message: requiredMessage("物料描述"), trigger: "change" }],
  materialUnit: [{ required: true, message: requiredMessage("物料单位"), trigger: "change" }],
  specificationModel: [{ required: true, message: requiredMessage("规格型号"), trigger: "change" }],
  voltageClass: [{ required: false, message: requiredMessage("电压等级"), trigger: "change" }]
};
const disabledMap = ref<{
  [key in keyof IMaterialDto]?: true;
}>({});

function initializeForm(value: Partial<IMaterialDto>, disableKeys?: Array<keyof IMaterialDto>) {
  if (value) {
    Object.assign(form, value);
  }

  if (Array.isArray(disableKeys)) {
    disabledMap.value = {};
    disableKeys.forEach(key => (disabledMap.value[key] = true));
  }
}

const onSubClassCodeChange = () => {
  form.materialUnit = undefined;
};

async function validate(): Promise<boolean> {
  return formRef.value.validate(() => {});
}

async function getValidValue(): Promise<IMaterialDto> {
  if (!(await validate())) {
    return Promise.reject("invalid");
  }
  return form;
}
</script>

<style scoped></style>
