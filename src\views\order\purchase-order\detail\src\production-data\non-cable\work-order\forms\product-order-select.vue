<template>
  <div class="flex flex-col h-[560px]">
    <el-radio-group class="pb-4" v-model="status">
      <el-radio-button v-for="option of options" :label="option.value" :key="String(option.value)">
        {{ option.label }}
      </el-radio-button>
    </el-radio-group>
    <pure-table
      border
      row-key="id"
      size="large"
      class="flex-1 overflow-hidden"
      show-overflow-tooltip
      :loading="loading"
      :columns="columns"
      :data="data"
      :span-method="spanMethod"
      @row-click="handleRowClick"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
  </div>
</template>

<script setup lang="ts">
import { IProductOrder } from "@/models";
import { TableWidth, VoltageClassesEnumMapDesc } from "@/enums";
import { computed, h, ref, watchEffect } from "vue";
import { PureTable, TableColumns } from "@pureadmin/table";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useNonCableWorkOrderStore } from "@/store/modules";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import CxTag from "@/components/CxTag/index.vue";
import { useRowspan } from "@/utils/useRowspan";
import { MEASURE_UNIT } from "@/consts";

const props = defineProps<{
  modelValue: IProductOrder;
}>();
const emit = defineEmits<{
  (e: "update:modelValue", productOrder: IProductOrder): void;
}>();

const store = useNonCableWorkOrderStore();
const { mapFormatter, singleSelectFormatter, withUnitFormatter } = useTableCellFormatter();

const status = ref<boolean | undefined>(undefined);
const data = ref<Array<IProductOrder>>([]);
const selectedId = ref<string>();
const loading = ref(false);
const handleQueryProductOrder = useLoadingFn(queryProductOrdersAndSetRowspan, loading);

const selectedProductOrder = computed({
  get() {
    return props.modelValue;
  },
  set(order: IProductOrder) {
    emit("update:modelValue", order);
  }
});

const options: Array<{ label: string; value: undefined | boolean }> = [
  { label: "全部", value: undefined },
  { label: "有工单", value: true },
  { label: "无工单", value: false }
];
const columns: Array<TableColumns> = [
  {
    label: "",
    prop: "id",
    width: TableWidth.radio,
    fixed: "left",
    formatter: singleSelectFormatter(selectedId)
  },
  {
    label: "生产订单",
    prop: "ipoNo",
    width: TableWidth.order
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "物料名称",
    prop: "materialsName",
    width: TableWidth.name
  },
  {
    label: "物料数量",
    prop: "amount",
    width: TableWidth.number,
    formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
  },
  {
    label: "电压等级",
    prop: "voltageClasses",
    width: TableWidth.largeType,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "有无工单",
    prop: "isWork",
    width: TableWidth.date,
    fixed: "right",
    formatter: (row, column, cellValue) => {
      if (cellValue) {
        return h(CxTag, { type: "success" }, () => "有");
      }
      return h(CxTag, { type: "warning" }, () => "无");
    }
  }
];

watchEffect(async () => {
  const isWork = typeof status.value === "boolean" ? status.value : undefined;
  data.value = await handleQueryProductOrder(isWork);
});

watchEffect(() => (selectedId.value = props.modelValue?.id));

function queryProductOrdersAndSetRowspan(isWork: boolean) {
  return store.queryProductOrdersByPurchase(isWork).then(orders => useRowspan(orders, "soNo"));
}

function spanMethod(data) {
  if (data.columnIndex === 0) {
    return {
      rowspan: data.row.rowSpan,
      colspan: data.row.colSpan
    };
  }
}

function handleRowClick(productOrder: IProductOrder) {
  selectedId.value = productOrder.id;
  selectedProductOrder.value = productOrder;
}
</script>

<style scoped></style>
