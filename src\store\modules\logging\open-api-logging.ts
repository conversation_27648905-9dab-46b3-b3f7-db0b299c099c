import { IOpenApiLog, IOpenApiLogReq } from "@/models";
import { defineStore } from "pinia";
import * as api from "@/api/logging/open-api";

export const useOpenApiStore = defineStore({
  id: "open-api-log-store",
  state: () => ({
    total: 0 as number,
    loading: false as boolean,
    list: [] as Array<IOpenApiLog>
  }),
  actions: {
    async queryOpenApiLog(params?: IOpenApiLogReq) {
      this.loading = true;
      const res = await api.queryOpenApiLog(params);
      this.total = res.data.total;
      this.list = res.data.list;
      this.loading = false;
    },

    async getDownloadFileUrl(id: string) {
      return (await api.getDownloadFileUrl(id)).data;
    }
  }
});
