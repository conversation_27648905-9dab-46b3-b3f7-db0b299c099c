<template>
  <Card margin="6px 0 12px" :fontWeight="700" title="销售订单行项目">
    <template #action>
      <el-button
        type="primary"
        v-auth="PermissionKey.form.formPurchaseSalesCreate"
        v-track="TrackPointKey.FORM_PURCHASE_SALES_LINE_CREATE"
        @click="store.openCreateDialog"
      >
        <FontIcon icon="icon-plus" class="mr-2" />
        新增销售订单行
      </el-button>
    </template>
    <div v-show="cardVisible" class="card-list flex-1 overflow-hidden">
      <el-scrollbar>
        <div
          v-for="item in store.salesOrderLines"
          :key="item.id"
          class="card-item"
          :class="getClassName(item)"
          :style="getBackground(item)"
        >
          <div class="w-full flex-bc flex-wrap">
            <div class="item-left flex-1">
              <div class="w-1/3 item-text">
                <span class="icon">售</span>
                <ShowTooltip className="text-sm max-w-[120px]" :content="item.soItemNo" />
              </div>
              <div class="w-1/3 item-text">
                <span class="icon">料</span>
                <ShowTooltip className="text-sm max-w-[120px]" :content="item.materialCode" />
              </div>
              <div class="w-1/3 item-text">
                <span class="icon">
                  <IconifyIconOffline :icon="BarChat" />
                </span>
                <span class="text"
                  >× {{ formatDecimal(item.materialNumber) }} {{ `${item.unitDictionary?.name || ""}` }}</span
                >
              </div>
            </div>
            <div class="item-right w-[100px]">
              <el-button
                v-if="item.poItemNos"
                v-auth="PermissionKey.form.formPurchaseSalesEdit"
                v-track="TrackPointKey.FORM_PURCHASE_SALES_LINE_DELETE"
                link
                @click="unlinkConfirm(item.id)"
                class="unlink-btn"
              >
                <IconifyIconOffline :icon="Unlink" />
              </el-button>
              <el-button
                v-auth="PermissionKey.form.formPurchaseSalesDelete"
                v-track="TrackPointKey.FORM_PURCHASE_SALES_LINE_DELETE"
                link
                @click="deleteConfirm(item.id)"
                class="unlink-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
              <el-button
                v-auth="PermissionKey.form.formPurchaseSalesEdit"
                v-track="TrackPointKey.FORM_PURCHASE_SALES_LINE_EDIT"
                link
                @click="salesOrderLineEditStore.openEditDialog(item.id)"
                class="edit-btn"
              >
                <el-icon><EditPen /></el-icon>
              </el-button>
            </div>
          </div>
          <div class="my-1">
            <span class="item-title font-medium" @click="salesOrderLineDetailStore.showDetail(item.id)">{{
              item.materialDesc
            }}</span>
          </div>
          <div class="w-full item-text">
            <span class="icon">购</span>
            <span class="text">{{ item.poItemNos }}</span>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <pure-table
      v-show="!cardVisible"
      show-overflow-tooltip
      row-key="id"
      :loading="fetchLoading || loading"
      :columns="columns"
      :data="store.salesOrderLines"
      class="flex-1 overflow-hidden"
      :row-class-name="getRowClassName"
    >
      <template #empty>
        <CxEmpty />
      </template>
    </pure-table>
    <SalesOrderLineDetailDialog />
    <CreateSalesOrderLineDialog />
    <UpdateSalesOrderLineDialog @updateSuccess="updateSuccess" />
  </Card>
</template>

<script setup lang="ts">
import Card from "../card.vue";
import { Delete, EditPen } from "@element-plus/icons-vue";
import BarChat from "@iconify-icons/ri/bar-chart-fill";
import { PureTable, TableColumns } from "@pureadmin/table";
import { computed, h, onMounted, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { OperatorCell } from "@/components/TableCells";
import ShowTooltip from "@/components/ShowTooltip";
import { useLoadingFn } from "@/utils/useLoadingFn";
import SalesOrderLineDetailDialog from "./sales-order-line-detail-dialog.vue";
import CreateSalesOrderLineDialog from "./create-sales-order-line-dialog.vue";
import UpdateSalesOrderLineDialog from "./update-sales-order-line-dialog.vue";
import {
  usePurchaseOrderDetailPurchaseOrderLineStore,
  usePurchaseOrderDetailSalesOrderLineDetailStore,
  usePurchaseOrderDetailSalesOrderLineEditStore,
  usePurchaseOrderDetailSalesOrderLineStore,
  usePurchaseOrderDetailSalesOrderStore
} from "@/store/modules/purchase-order-detail";
import { ColumnWidth, TableWidth, ToggleStyleEnum } from "@/enums";
import { usePurchaseOrderLink } from "@/views/order/purchase-order/detail/src/hooks/usePurchaseOrderLink";
import { useConfirm } from "@/utils/useConfirm";
import { MEASURE_UNIT, TrackPointKey } from "@/consts";
import { ISalesOrderLine } from "@/models";
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { PermissionKey } from "@/consts";
import { formatDecimal } from "@/utils/format";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import Unlink from "@iconify-icons/ri/link-unlink";

const store = usePurchaseOrderDetailSalesOrderLineStore();
const salesOrderStore = usePurchaseOrderDetailSalesOrderStore();
const salesOrderLineEditStore = usePurchaseOrderDetailSalesOrderLineEditStore();
const salesOrderLineDetailStore = usePurchaseOrderDetailSalesOrderLineDetailStore();
const purchaseOrderLineStore = usePurchaseOrderDetailPurchaseOrderLineStore();
const { refreshLink } = usePurchaseOrderLink();
const { withUnitFormatter } = useTableCellFormatter();
const loading = ref(false);

const handleUnlinkLine = useLoadingFn(unlinkLine, loading);
const handleDeleteLine = useLoadingFn(deleteLine, loading);
const columns: Array<TableColumns> = [
  {
    label: "销售订单行项目号",
    prop: "soItemNo",
    minWidth: TableWidth.order,
    formatter: row => {
      const { id, soItemNo } = row;
      return h("a", { class: "text-primary", onClick: () => salesOrderLineDetailStore.showDetail(id) }, soItemNo);
    }
  },
  {
    label: "采购订单行项目号",
    prop: "poItemNos",
    minWidth: TableWidth.order
  },
  {
    label: "物资种类",
    prop: "subClassName",
    width: TableWidth.type
  },
  {
    label: "物料名称",
    prop: "materialName",
    minWidth: TableWidth.name
  },
  {
    label: "物料数量",
    prop: "materialNumber",
    width: TableWidth.number,
    align: "right",
    formatter: withUnitFormatter("materialUnit", "subClassCode", MEASURE_UNIT)
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    width: TableWidth.number
  },
  {
    label: "操作",
    width: ColumnWidth.Char12,
    fixed: "right",
    cellRenderer: data =>
      OperatorCell([
        {
          name: "编辑",
          action: () => salesOrderLineEditStore.openEditDialog(data.row.id),
          props: { type: "primary" },
          trackKey: TrackPointKey.FORM_PURCHASE_SALES_LINE_EDIT,
          permissionKey: PermissionKey.form.formPurchaseSalesEdit
        },
        {
          name: "解绑",
          action: () => unlinkConfirm(data.row.id),
          props: { type: "danger" },
          trackKey: TrackPointKey.FORM_PURCHASE_SALES_LINE_DELETE,
          permissionKey: PermissionKey.form.formPurchaseSalesDelete
        },
        {
          name: "删除",
          action: () => deleteConfirm(data.row.id),
          props: { type: "danger" },
          trackKey: TrackPointKey.FORM_PURCHASE_SALES_LINE_DELETE,
          permissionKey: PermissionKey.form.formPurchaseSalesDelete
        }
      ])
  }
];
const fetchLoading = computed(() => store.loadingList);
const cardVisible = computed(() => purchaseOrderLineStore.toggleStyle === ToggleStyleEnum.MENU);

onMounted(() => {
  watch(() => salesOrderStore.activeOrderId, store.refreshSalesOrderLines, { immediate: true });
});

async function unlinkLine(id: string) {
  await store.unlinkSalesOrderLine(id);
  ElMessage.success("解绑成功");
  refreshLink();
}

async function deleteLine(id: string) {
  await store.deleteSalesOrderLine(id);
  ElMessage.success("删除成功");
  refreshLink();
}

function getRowClassName({ row }): string {
  return isHighlight(row) ? "highlight" : null;
}

function getClassName(item: ISalesOrderLine): string {
  return isHighlight(item) ? "highlight" : null;
}

function isHighlight(line: ISalesOrderLine): boolean {
  const activePurchaseOrderLineNo: string = purchaseOrderLineStore.activePurchaseOrderLineNo;
  if (!activePurchaseOrderLineNo) {
    return false;
  }
  return line.poItemNoArray?.includes(activePurchaseOrderLineNo);
}

function getBackground(item: ISalesOrderLine) {
  return {
    background: `linear-gradient(var(--bg-color, white), var(--bg-color, white)) padding-box, ${item.linearGradient} border-box`
  };
}

async function unlinkConfirm(id: string) {
  if (!(await useConfirm("确认取消与该销售订单行项目的关联关系吗？", "确认解绑"))) {
    return;
  }
  await handleUnlinkLine(id);
}

async function deleteConfirm(id: string) {
  if (!(await useConfirm("确认删除该销售订单行项目吗？", "确认删除"))) {
    return;
  }
  await handleDeleteLine(id);
}

function updateSuccess() {
  ElMessage.success("编辑成功");
  void store.refreshSalesOrderLines();
}
</script>

<style scoped lang="scss">
:deep(.highlight) {
  --el-bg-color: var(--el-color-warning-light-9);
  --bg-color: var(--el-color-warning-light-9);
  background-color: var(--el-color-warning-light-9);
}

.card-list {
  @apply h-full overflow-y-auto;
}

.card-item {
  padding: 12px;
  border-radius: 3px;
  border-width: 0 0 0 4px;
  border-color: transparent;
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 2px 2px 12px 2px;

  .item-left {
    @apply flex gap-4;
  }

  .item-right {
    @apply text-right;
  }

  .item-title {
    @apply inline-block leading-5 text-base cursor-pointer;

    &:hover {
      color: var(--el-color-primary);
    }
  }

  .item-text {
    @apply flex items-center text-secondary;

    .icon {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      width: 16px;
      height: 16px;
      border-radius: 1px;
      margin-right: 4px;
      color: var(--el-color-primary-light-3);
      background: var(--el-color-primary-light-9);
      border: 0.5px solid var(--el-color-primary-light-7);
    }

    .text {
      @apply text-sm;
    }
  }

  .unlink-btn {
    @apply text-secondary;

    &:hover {
      color: var(--el-color-danger);
    }
  }

  .edit-btn {
    @apply text-secondary;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}

@media screen and (max-width: 1439px) {
  .card-item {
    flex-direction: column;

    .item-right {
      @apply text-left flex-row items-center gap-1 w-auto;
    }
  }
}
</style>
