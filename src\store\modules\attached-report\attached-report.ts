import { defineStore } from "pinia";
import * as api from "@/api/attached-report";
import { IAttachDocumentRes, ISaveDocumentReq } from "@/models/attached-report";
import { IResponse } from "@/models";
import { uploadFile } from "@/api/upload-file";
import { getProductionProcessListByCategoryCodeAndProductionStage } from "@/api";

export const useAttachReportStore = defineStore({
  id: "attach-report-store",
  state: () => ({}),
  actions: {
    /** 查询工序文档列表 */
    async getProcessDocumentList(id: string) {
      const processDocumentList: IResponse<Array<IAttachDocumentRes>> = await api.getProcessDocumentList(id);
      return processDocumentList.data;
    },

    /** 新增工序文档 */
    async saveProcessDocument(params: ISaveDocumentReq) {
      return await api.saveProcessDocument(params);
    },

    /** 删除工序文档 */
    async delProcessDocument(id: string) {
      return (await api.delProcessDocument(id)).data;
    },

    /** 上传工序文档 */
    async upload(file: File) {
      const formData = new FormData();
      formData.append("file", file);
      return (await uploadFile(formData)).data.id;
    },

    /**根据物资种类查询工序 */
    async queryProcessBysubClassCode(subClassCode: string) {
      return (await getProductionProcessListByCategoryCodeAndProductionStage(subClassCode)).data;
    }
  }
});
