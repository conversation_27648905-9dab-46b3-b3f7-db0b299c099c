<template>
  <div class="pre-delivery-test">
    <NormalDeliveryTest ref="editDeliveryTestInstance" v-if="isNormal" />
    <ACDeliveryTest ref="editDeliveryTestInstance" v-else />
  </div>
</template>

<script setup lang="ts">
import NormalDeliveryTest from "../../predelivery-test/normal/not-jfny/add-air-tightness/index.vue";
import ACDeliveryTest from "../../predelivery-test/armour-clamp/test-info/add-air-tightness/index.vue";
import { inject, onMounted, ref } from "vue";
import { EMaterialCategory } from "@/enums/purchase-order/index";
import { useEXFactoryExperimentStore } from "@/store/modules";
import { PROVIDE_PROCESS_INSPECT_TOKEN } from "@/views/components/state-grid-order-sync/tokens";

const editDeliveryTestInstance = ref<InstanceType<typeof NormalDeliveryTest> | InstanceType<typeof ACDeliveryTest>>();
const prodCtx = inject(PROVIDE_PROCESS_INSPECT_TOKEN);
const isNormal = ref<boolean>(true);

const exFactoryExperimentStore = useEXFactoryExperimentStore();

onMounted(() => {
  isNormal.value = prodCtx?.detailMaterialCategory === EMaterialCategory.Normal;
  // 重置数据
  exFactoryExperimentStore.initFormMetadataModelInfos();
});

/**
 * 获取试验数据
 */
const getDeliveryTestFormValue = async () => {
  const qmxExperimentBaseValue = await editDeliveryTestInstance.value.getQMXExperimentBaseFormValue();
  const qmxAirTightnessExpersValue = await editDeliveryTestInstance.value.getQMXAirTightnessExpersFormValue();

  /*** 表单数据验证不过 */
  if (
    (typeof qmxExperimentBaseValue === "boolean" && !qmxExperimentBaseValue) ||
    !Array.isArray(qmxAirTightnessExpersValue?.submitData) ||
    qmxAirTightnessExpersValue?.submitData.length === 0
  ) {
    return;
  }

  return {
    qmxExperimentBaseValue,
    qmxAirTightnessExpersValue
  };
};

defineExpose({
  getDeliveryTestFormValue
});
</script>

<style scoped lang="scss"></style>
