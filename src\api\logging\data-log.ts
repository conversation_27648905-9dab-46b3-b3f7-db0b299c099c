import { IDataBaseList, IDataLogList, ISearchDataLog } from "@/models/logging";
import { withApiGateway } from "../util";
import { http } from "@/utils/http";
import { IListResponse, IResponse } from "@/models/response";

/** 获取数据日志列表 */
export function getDataLogList(params: ISearchDataLog) {
  const url = withApiGateway(`admin-api/infra/tableData/page`);
  return http.post<ISearchDataLog, IListResponse<IDataLogList>>(url, { data: params });
}

/** 获取对象表的数据 */
export function getTableOptions() {
  const url = withApiGateway(`admin-api/infra/tableData/getTableConfigList`);
  return http.get<void, IResponse<Array<IDataBaseList>>>(url);
}
