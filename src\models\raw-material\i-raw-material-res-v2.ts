import { IPagingReq } from "../i-paging-req";

export enum EDiagType {
  Add = "add",
  Edit = "edit",
  Detail = "detail"
}

/**
 * 原材料检测
 */
export enum ERawMaterialCheckType {
  All = -1, // 全部
  Has = 1, // 有
  Not = 0 // 无
}

/**
 * 获取工序接口
 */
export interface IProductionStageProcessCode {
  processCode: string;
  processName: string;
  /** 物资种类 */
  subclassCode?: string;
}

export interface ISearchRawMaterialListReq extends IPagingReq {
  /** 关键字 */
  keyWords?: string;
  /** 原材料类型编码 */
  processCode?: string;
}

/**
 * 保存新增原材料数据
 */
export interface ISaveAddRawMaterial {
  id?: string;
  /** 原材料编号  */
  code: string;
  /** 原材料名称  */
  name: string;
  /** 原材料类型信息  */
  processCode: string;
  processName: string;
  /** 规格型号  */
  modelCode: string;
  /** 计量单位  */
  partUnit?: string;
  /** 品牌  */
  borMaterials: string;
  /** 原材料制造商  */
  rawmManufacturer: string;
  /** 产地  */
  oorMaterials: string;
  /** 电压等级  */
  voltageGrade: string;
  /** 原材料批次号  */
  materialBatchNo: string;
  /** 生产日期  */
  productionDate: string;
  /** 材料标准 */
  materialStandard?: string;
  /** 备注  */
  remark: string;
}

/**
 * 原材料列表接口
 */
export interface IRawMaterialList {
  /** 唯一标识 */
  id?: string;
  /** 原材料编码 */
  code: string;
  /** 原材料名称 */
  name: string;
  /**  */
  materialBatchNo: string;
  /** */
  modelCode: string;
  /** */
  partUnit: string;
  /** 原材料类型信息  */
  processCode: string;
  processName: string;
  /** 生产日期  */
  productionDate: string;
  /** 材料标准 */
  materialStandard?: string;
}

/** 原材料检分页列表 */
export interface ISearchRawMaterialInspecReq extends IPagingReq {
  /** 原材料ID */
  rawMaterialId?: string;
  /** 检验批次号 */
  inspectNo?: string;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 工序编号 */
  processCode?: string;
  /** 工序整合编号（同名合并） */
  processUnionCode?: string;
}

export interface IReport {
  id: string;
  name: string;
  url: string;
  bucket: string;
}

/** 原材料检列表 */
export interface IRawmaterialInspecList {
  id?: string;
  /** 检测批号  */
  inspectBatchNo: string;
  /** 检测日期  */
  inspectDate: string;
  /** 生产厂检测报告  */
  inspectionReport: IReport;
  /** 抽检报告  */
  spotCheckReport: IReport;
  /** 备注  */
  remark: string;
}

export interface IRawMaterialExperimentValue {
  dataCode: string;
  identityCode: string;
  //{[key:string]:any} => 针对文件 后面需要调整
  dataValue: string | number | Array<string> | Array<number> | Array<{ key: string }> | { [key: string]: any };
}

/** 新增原材料检信息 */
export interface IAddRawMaterialInspection {
  id?: string;
  /** 原材料Id */
  materialId: string;
  /** 原材料Code */
  materialCode?: string;
  /** 检验批次号 */
  inspectBatchNo: string;
  /** 检验日期 */
  inspectDate: string;
  /** 检测标准 */
  inspectStandard?: string;
  /** 炉/批号  */
  furnaceNo?: string;
  /** 检测人员  */
  inspectOperate?: string;
  /** 检测单位  */
  inspectEnterprise?: string;
  /** 审核员  */
  auditor?: string;
  /** 物资种类 */
  subClassCode?: string;
  subClassName?: string;
  /** 物资种类在该原材料下的工序Id */
  processId?: string;
  /** 备注 */
  remark: string;
  rawMetadataValue: Array<IRawMaterialExperimentValue>;
}

/** 原材料检详情信息 */
export type IRawMaterialInspectDetail = IAddRawMaterialInspection;

/** 控件值信息 */
export interface IIdentityDetailList {
  id?: number;
  metadataModelDetailId: number;
  identityId: number;
  identityValue: string;
  maxLength?: number;
  minLength?: number;
  step?: number;
}

/** 控件信息 */
export interface IDataTypeIdentityDetail {
  id?: number;
  identityCode: string;
  identityName: string;
  identityDetailList?: IIdentityDetailList;
}

/**
 * 原材料检测采集项
 */
export interface IRawMaterialInspectCollectionItem {
  collectionType?: number;
  collectionTypeName?: string;
  groupCode?: string;
  groupName?: string;
  id?: string;
  processId?: string;
  identityId?: string;
  isShow?: boolean;
  itemCode?: string;
  remarks?: string;
  required?: boolean;
  requiredPre?: string;
  sort?: number;
  status?: boolean;
  targetCode?: string;
  targetCodeAlias?: string;
  targetName?: string;
  targetValue?: string;
  targetNameAlias?: string;
  targetValueType?: string;
  unit?: string;
  unitLength?: string;
  initValue?: string;
  initType?: string;

  /** 日期时间格式 */
  dateTypeCode?: string;
  dateTypeName?: string;
  /** 数据格式要求 */
  datumOrganization?: string;
  /** 校验信息 */

  // 文本
  /** 文本最大长度 */
  maxStrLength?: number;
  /** 是否使用正则 */
  useRegular?: boolean;
  /** 正则表达式 */
  regular?: string;

  // 数字输入框
  /** 是否限制最大值 */
  validMaxValue?: boolean;
  /** 最大值 */
  maxValue?: number;
  /** 是否包含最大值 */
  includeMaxValue?: boolean;
  /** 是否限制最小值 */
  validMinValue?: boolean;
  /** 最小值 */
  minValue?: number;
  /** 是否包含最小值 */
  includeMinValue?: boolean;
  /** 小数精度 */
  decimalDigits?: number;

  // 日期格式化
  format?: string;

  // 文件大小
  /** 文件类型，多选 */
  fileType?: string[];
  /** 文件大小 */
  fileSize?: number;

  dataTypeIdentityDetail?: IDataTypeIdentityDetail;
}
