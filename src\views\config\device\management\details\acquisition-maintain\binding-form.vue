<template>
  <div class="px-5">
    <el-form ref="formRef" :model="formValue" :rules="rules" label-width="100px">
      <el-row>
        <el-col>
          <el-form-item label="标准采集点" prop="standardPointId">
            <span>{{ props.standardPointName }}</span>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="设备采集点" prop="devicePointId">
            <el-select
              placeholder="请选择设备采集点"
              class="w-full"
              v-model="formValue.devicePointId"
              clearable
              filterable
            >
              <el-option
                v-for="item in acquisitionMaintainStore.acquisitionlist || []"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { requiredMessage } from "@/utils/form";
import { IAcquisitionMaintainForm } from "@/models";
import { useRoute } from "vue-router";
import { useAcquisitionMaintainStore } from "@/store/modules/device";

const route = useRoute();
const acquisitionMaintainStore = useAcquisitionMaintainStore();
const deviceId: string = route.params.id as string;

const formRef = ref<FormInstance>();
const formValue = reactive<{
  standardPointId: string;
  devicePointId: string;
  deviceId: string;
}>({
  standardPointId: undefined,
  devicePointId: undefined,
  deviceId: undefined
});

const rules = reactive<FormRules>({
  devicePointId: [{ required: true, message: requiredMessage("设备采集点"), trigger: "change" }]
});

const props = defineProps({
  standardPointId: {
    type: String,
    default: ""
  },
  standardPointName: {
    type: String,
    default: ""
  },
  processId: {
    type: String,
    required: true
  }
});

acquisitionMaintainStore.deviceIdAcquisitionlist(deviceId, props.processId);

watchEffect(async () => {
  const { standardPointId } = props;
  formValue.standardPointId = standardPointId;
});

const getFormValue = async (): Promise<boolean | IAcquisitionMaintainForm> => {
  if (!formRef.value) {
    return false;
  }

  const valid = await formRef.value.validate(() => {});
  if (!valid) {
    return valid;
  }
  return formValue;
};

defineExpose({
  getFormValue
});
</script>

<style scoped lang="scss"></style>
