<template>
  <Teleport to="body" :disabled="!fullScreen">
    <div class="full-screen-container" :class="{ 'el-overlay': fullScreen }">
      <div class="screen-container">
        <div class="flex">
          <div class="card-container">
            <div class="leading-8 text-lg font-semibold">采购订单</div>
            <PurchaseOrder :collapsed="collapsed" />
          </div>

          <div class="card-container mb-3">
            <SalesOrderHeader />
            <SalesOrder :collapsed="collapsed" v-if="hasActiveSalesOrder" />
          </div>
        </div>
        <HorizontalDivider v-model="collapsed" />
        <div class="flex flex-1 overflow-hidden">
          <PurchaseOrderLine class="card-container" />
          <SalesOrderLine class="card-container" v-if="hasActiveSalesOrder" />
        </div>
        <VerticalDivider v-model="fullScreen" />
        <EmptySalesOrder v-if="!hasActiveSalesOrder" />
      </div>
      <SalesOrderDialog />
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, provide, reactive, ref } from "vue";
import HorizontalDivider from "./src/horizontal-divider.vue";
import VerticalDivider from "./src/vertical-divider.vue";
import PurchaseOrder from "./src/purchase-order/purchase-order.vue";
import PurchaseOrderLine from "./src/purchase-order-line/purchase-order-line.vue";
import SalesOrder from "./src/sales-order/sales-order.vue";
import SalesOrderLine from "./src/sales-order-line/sales-order-line.vue";
import EmptySalesOrder from "./src/sales-order/empty-sales-order.vue";
import SalesOrderDialog from "./src/sales-order/sales-order-dialog.vue";
import SalesOrderHeader from "./src/sales-order/sales-order-header.vue";
import { salesOrderDialogKey } from "./token";
import { usePurchaseOrderDetailSalesOrderStore } from "@/store/modules/purchase-order-detail";

const fullScreen = ref(false);
const collapsed = ref(true);
const salesOrderStore = usePurchaseOrderDetailSalesOrderStore();
const hasActiveSalesOrder = computed(() => !!salesOrderStore.activeOrder);

provide(
  salesOrderDialogKey,
  reactive({
    title: "",
    visible: false
  })
);
</script>

<style scoped lang="scss">
.full-screen-container {
  @apply flex flex-1 overflow-hidden;
  padding-top: 20px;
  margin-top: -20px;

  .screen-container {
    @apply relative flex-1 flex flex-col w-full pt-3;
  }

  &.el-overlay {
    margin-top: 0;
    background: linear-gradient(180deg, var(--el-color-primary-light-9) 0%, #ffffff 4%, #ffffff 100%);
  }

  &:not(.el-overlay) {
    .screen-container {
      background: linear-gradient(180deg, var(--el-color-primary-light-9) 0%, #ffffff 4%, #ffffff 100%);
    }
  }
}

.card-container {
  @apply mb-3 px-5 basis-1/2 overflow-hidden;
}
</style>
