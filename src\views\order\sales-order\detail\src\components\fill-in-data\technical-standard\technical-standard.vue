<template>
  <div class="technical-standard flex flex-col">
    <!-- 搜索数据 -->
    <SearchTechnicalStandard class="mb-5">
      <template #right>
        <div class="operate-button">
          <el-button type="primary" @click="selectTechnicalStandard()">
            <FontIcon class="mr-2" icon="icon-plus" />
            技术标准库选用
          </el-button>
          <el-button type="primary" @click="selectFromProduction()">
            <FontIcon class="mr-2" icon="icon-plus" />
            从订单中选用
          </el-button>
        </div>
      </template>
    </SearchTechnicalStandard>
    <!-- 技术标准数据 -->
    <PureTable
      class="flex-1 overflow-hidden pagination"
      row-key="id"
      :data="technicalStandardList"
      :columns="columns"
      :loading="loading"
      showOverflowTooltip
    >
      <template #operate="{ row }">
        <div class="operate">
          <el-button link type="primary" @click="detailDialog(row)"> 详情 </el-button>
          <el-button link type="primary" @click="editDialog(row)"> 编辑 </el-button>
          <el-button link type="danger" @click="deleteTechnicalStandard(row.id)"> 删除 </el-button>
        </div>
      </template>
      <template #empty>
        <CxEmpty />
      </template>
    </PureTable>
  </div>
  <!-- 技术标准库选用 -->
  <SelectTechnicalStandard v-model="selectVisible" />
  <!-- 从订单中选用 -->
  <CopyStandardFromOrder v-model="copyVisible" />
  <!-- 详情 -->
  <DetailStandard v-model="detailVisible" :standardInfo="standardInfo" />
  <!-- 编辑 -->
  <EditTechnicalInfo v-model="editVisible" :standardInfo="standardInfo" />
</template>

<script setup lang="ts">
import SearchTechnicalStandard from "./technical-standard-search.vue";
import CxEmpty from "@/components/CxEmpty";
import SelectTechnicalStandard from "@/views/order/sales-order/detail/src/components/fill-in-data/technical-standard/select-technical-standard/select-technical-standard.vue";
import DetailStandard from "@/views/order/sales-order/detail/src/components/fill-in-data/technical-standard/detail-technical-standard/detail-technical-standard.vue";
import EditTechnicalInfo from "@/views/order/sales-order/detail/src/components/fill-in-data/technical-standard/edit-technical-standard/edit-technical-info.vue";
import CopyStandardFromOrder from "@/views/order/sales-order/detail/src/components/fill-in-data/technical-standard/copy-technical-standard/copy-technical-standard.vue";
import { useColumns } from "./columns";
import { onMounted, onUnmounted, provide, ref } from "vue";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useSalesFillInDataStore, useTechnicalStandardStore } from "@/store/modules";
import { ElMessage } from "element-plus";
import { technicalStandardKey } from "./tokens";
import { ITechnicalStandardInfo } from "@/models/technical-standard";

const fillInDataStore = useSalesFillInDataStore();
const store = useTechnicalStandardStore();
const { columns } = useColumns();
const loading = ref<boolean>(false);
const technicalStandardList = ref([]);
const initTechnicalStandard = useLoadingFn(getTechnicalStandardList, loading);
const selectVisible = ref<boolean>(false);
const detailVisible = ref<boolean>(false);
const editVisible = ref<boolean>(false);
const copyVisible = ref<boolean>(false);
const standardInfo = ref<ITechnicalStandardInfo>();
provide(technicalStandardKey, {
  refresh: refresh
});

onMounted(() => {
  initTechnicalStandard();
});

/** 获取技术标准数据 */
async function getTechnicalStandardList() {
  const res = await store.getTechnicalStandardInfo(fillInDataStore.dataId);
  if (res?.id) {
    technicalStandardList.value.push(res);
  } else {
    technicalStandardList.value = [];
  }
}

/** 从技术标准库中选用 */
function selectTechnicalStandard() {
  selectVisible.value = true;
}

/** 从订单中选用 */
function selectFromProduction() {
  copyVisible.value = true;
}

/** 详情 */
function detailDialog(row) {
  standardInfo.value = row;
  detailVisible.value = true;
}
/** 编辑 */
function editDialog(row) {
  standardInfo.value = row;
  editVisible.value = true;
}

/** 删除技术标准 */
async function deleteTechnicalStandard(id: string) {
  if (!(await useConfirm("正在执行删除技术标准，是否继续？", "确认删除"))) {
    return;
  }

  await store.delTechnicalStandard(id);
  ElMessage.success("删除成功");
  refresh();
}

function refresh() {
  technicalStandardList.value = [];
  initTechnicalStandard();
}

onUnmounted(() => {
  standardInfo.value = null;
});
</script>

<style scoped lang="scss"></style>
