<template>
  <el-dialog
    title="编辑销售订单行"
    align-center
    class="default"
    v-model="store.visible"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <SalesOrderLineForm ref="form" type="update" :category-code="categoryCode" v-loading="store.loading" />
    <template #footer>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SalesOrderLineForm from "./sales-order-line-form.vue";
import { usePurchaseOrderDetailSalesOrderLineEditStore } from "@/store/modules/purchase-order-detail";
import { computed, onMounted, ref, watch } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { ISalesOrderLine } from "@/models";

const emit = defineEmits<{
  (e: "updateSuccess", value: ISalesOrderLine): void;
}>();

const store = usePurchaseOrderDetailSalesOrderLineEditStore();
const form = ref<InstanceType<typeof SalesOrderLineForm>>();
const loading = ref(false);
const handleSave = useLoadingFn(save, loading);

const categoryCode = computed(() => store.salesOrderLine?.categoryCode || "");

onMounted(() => {
  watch([() => store.visible, () => store.loading], ([visible, loading]) => {
    if (!visible || loading) {
      return;
    }
    form.value.initializeForm(store.salesOrderLine);
  });
});

function cancel() {
  store.$patch({ visible: false });
}

async function save() {
  const data = await form.value.getValue();
  const line = await store.updateSalesOrderLine(data).then(res => res.data);
  emit("updateSuccess", line);
  cancel();
}
</script>

<style scoped></style>
