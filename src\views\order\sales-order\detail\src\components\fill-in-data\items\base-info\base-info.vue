<template>
  <el-collapse-item :name="name" class="relative">
    <template #title>
      <CollapseItemTitle title="基础信息" :name="name" v-track="TrackPointKey.FORM_PURCHASE_PD_UPDOWN_1">
        <Status />
      </CollapseItemTitle>
    </template>
    <el-descriptions class="pt-5 pb-3 px-6 mr-8">
      <el-descriptions-item v-for="item in items" :key="item.key" :label="item.label">
        <template #label>
          <span v-if="item.key != 'ipoNo'">{{ item.label }}</span>
          <span
            v-else
            v-alias="{
              code: KeywordAliasEnum.IPO_NO,
              default: KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]
            }"
          />
        </template>

        {{ data?.[item.key] }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="absolute top-16 right-4">
      <add-edit-production-order
        v-if="fillInDataStore.data && salesStore.isCable"
        mode="edit"
        :production-id="fillInDataStore.data.id"
        @post-save-success="fillInDataStore.refreshData(fillInDataStore.data.id)"
      >
        <template #trigger="{ auth, track, handleOpen }">
          <el-button type="primary" v-auth="auth.editAuth" v-track="track.editTrack" @click="handleOpen">
            编辑
          </el-button>
        </template>
      </add-edit-production-order>
      <add-edit-work-order
        v-if="fillInDataStore.data && !salesStore.isCable"
        mode="edit"
        :production-id="fillInDataStore.productionId"
        :production-no="fillInDataStore.data.ipoNo"
        :work-order-id="fillInDataStore.data.id"
        :sub-class-code="fillInDataStore.data.subclassCode || fillInDataStore.data.subClassCode"
        @post-save-success="fillInDataStore.refreshData(fillInDataStore.data.id, fillInDataStore.productionId)"
      >
        <template #trigger="{ auth, track, handleOpen }">
          <el-button type="primary" v-auth="auth.editAuth" v-track="track.editTrack" @click="handleOpen">
            编辑
          </el-button>
        </template>
      </add-edit-work-order>
    </div>
  </el-collapse-item>
</template>

<script setup lang="ts">
import { DeviceTerminalEnum, JointCategoriesEnum, VoltageClassesEnum, VoltageTypeEnum } from "@/enums";
import { formatDate, formatDecimal, formatEnum } from "@/utils/format";
import CollapseItemTitle from "@/views/order/sales-order/detail/src/components/fill-in-data/collapse-item-title/collapse-item-title.vue";
import { useSalesFillInDataStore, useSalesOrderDetailStore, useUserDictionaryStore } from "@/store/modules";
import { computed, ref, watchEffect } from "vue";
import Status from "@/views/order/sales-order/detail/src/components/fill-in-data/items/base-info/status";
import { CABLE_TERMINAL_ADAPTER, MEASURE_UNIT, TrackPointKey } from "@/consts";
import { IProductOrder, IWorkOrder } from "@/models";
import { FillInDataOfCollapseNameEnum } from "../../fill-in-data/types";
import { useMaterial } from "@/utils/material";
import AddEditProductionOrder from "@/views/order/production-order/add-edit-production-order/index.vue";
import AddEditWorkOrder from "@/views/order/production-order/add-edit-work-order/index.vue";
import { KeywordAliasEnum, KeywordAliasEnumMapDesc } from "@/enums";

const name = FillInDataOfCollapseNameEnum.PRODUCT_ORDER_DETAIL;

const salesStore = useSalesOrderDetailStore();
const userDictionaryStore = useUserDictionaryStore();
const fillInDataStore = useSalesFillInDataStore();
const materialTool = useMaterial();

const data = ref<Record<string, any>>();

const items = computed(() => {
  if (salesStore.isCable) {
    let items = CABLE_TERMINAL_ADAPTER.includes(fillInDataStore.data?.subClassCode)
      ? [
          { label: "生产订单号", key: "ipoNo" },
          { label: "销售订单号", key: "soNo" },
          { label: "销售订单行项目号", key: "soItemNo" },
          { label: "物资品类", key: "categoryName" },
          { label: "物资种类", key: "subclassName" },
          { label: "物料编号", key: "materialsCode" },
          { label: "物料名称", key: "materialsName" },
          { label: "物料描述", key: "materialsDesc" },
          { label: "物料单位", key: "materialUnitName" },
          { label: "应用类型", key: "applicationType" },
          { label: "接头类型", key: "jointCategories" },
          { label: "电压类型", key: "voltageType" },
          { label: "电压等级", key: "voltageClasses" },
          { label: "规格型号", key: "specificationModel" },
          { label: "生产数量", key: "amount" },
          { label: "计划日期", key: "planDate" },
          { label: "实际开始日期", key: "actualStartDate" },
          { label: "实际完成日期", key: "actualFinishDate" },
          { label: "发货数量", key: "quantityShipped" },
          { label: "入库数量", key: "quantityWarehousing" },
          { label: "备注", key: "remark" }
        ]
      : [
          { label: "生产订单号", key: "ipoNo" },
          { label: "销售订单号", key: "soNo" },
          { label: "销售订单行项目号", key: "soItemNo" },
          { label: "物资品类", key: "categoryName" },
          { label: "物资种类", key: "subclassName" },
          { label: "物料编号", key: "materialsCode" },
          { label: "物料名称", key: "materialsName" },
          { label: "物料描述", key: "materialsDesc" },
          { label: "物料单位", key: "materialUnitName" },
          { label: "电压等级", key: "voltageClasses" },
          { label: "规格型号", key: "specificationModel" },
          { label: "生产数量", key: "amount" },
          { label: "计划日期", key: "planDate" },
          { label: "实际开始日期", key: "actualStartDate" },
          { label: "实际完成日期", key: "actualFinishDate" },
          { label: "发货数量", key: "quantityShipped" },
          { label: "入库数量", key: "quantityWarehousing" },
          { label: "备注", key: "remark" }
        ];
    if (materialTool.isCableJoint(fillInDataStore.data?.subClassCode)) {
      items = items.filter(({ key }) => key !== "applicationType");
    }
    return items;
  }

  // 金具
  if (fillInDataStore.isArmourClamp) {
    return [
      { label: "工单编号", key: "woNo" },
      { label: "生产订单号", key: "ipoNo" },
      { label: "生产数量", key: "amount" },
      { label: "物资品类", key: "categoryName" },
      { label: "物资种类", key: "subclassName" },
      { label: "物资子种类", key: "minClassName" },
      { label: "产品名称", key: "productName" },
      { label: "物料编号", key: "materialsCode" },
      { label: "物料名称", key: "materialName" },
      { label: "物料描述", key: "materialDesc" },
      { label: "物料单位", key: "materialUnit" },
      { label: "电压等级", key: "voltageLevel" },
      { label: "规格型号", key: "specificationType" },
      { label: "计划日期", key: "planDate" },
      { label: "实际开始日期", key: "actualStartDate" },
      { label: "实际完成日期", key: "actualFinishDate" },
      { label: "质量追溯码", key: "qualityTraceCode" }
    ];
  }

  return [
    { label: "工单编号", key: "woNo" },
    { label: "生产订单号", key: "ipoNo" },
    { label: "产品编号", key: "productCode" },
    { label: "生产数量", key: "amount" },
    { label: "物资品类", key: "categoryName" },
    { label: "物资种类", key: "subclassName" },
    { label: "物料编号", key: "materialsCode" },
    { label: "物料名称", key: "materialName" },
    { label: "物料描述", key: "materialDesc" },
    { label: "物料单位", key: "materialUnit" },
    { label: "电压等级", key: "voltageLevel" },
    { label: "规格型号", key: "specificationType" },
    { label: "计划日期", key: "planDate" },
    { label: "实际开始日期", key: "actualStartDate" },
    { label: "实际完成日期", key: "actualFinishDate" }
  ];
});

watchEffect(async () => {
  if (salesStore.isCable) {
    const productOrder = fillInDataStore.data as IProductOrder;
    if (!productOrder) {
      data.value = {};
    } else {
      const subClassCode = productOrder.subClassCode || productOrder.subclassCode;
      const unitName = await userDictionaryStore.getName(MEASURE_UNIT, subClassCode, productOrder.unit);
      const materialUnitName = await userDictionaryStore.getName(
        MEASURE_UNIT,
        subClassCode,
        productOrder.materialsUnit
      );

      data.value = {
        ...productOrder,
        materialUnitName,
        applicationType: formatEnum(productOrder.applicationType, DeviceTerminalEnum, "deviceTerminalEnum"),
        jointCategories: formatEnum(productOrder.jointCategories, JointCategoriesEnum, "jointCategoriesEnum"),
        voltageType: formatEnum(productOrder.voltageType, VoltageTypeEnum, "voltageTypeEnum"),
        voltageClasses: formatEnum(productOrder.voltageClasses, VoltageClassesEnum, "voltageClassesEnum"),
        amount: productOrder.amount ? `${formatDecimal(productOrder.amount)} ${unitName}` : "",
        planDate: `${formatDate(productOrder.planStartDate)} ～ ${formatDate(productOrder.planFinishDate)}`,
        actualStartDate: formatDate(productOrder.actualStartDate),
        actualFinishDate: formatDate(productOrder.actualFinishDate)
      };
    }
    return;
  }
  const workOrder = fillInDataStore.data as IWorkOrder;
  if (!workOrder) {
    data.value = {};
    return;
  }
  const workOrderUnit = workOrder.unitDictionary?.name || "";
  const materialUnitName = await userDictionaryStore.getName(
    MEASURE_UNIT,
    workOrder.subclassCode,
    workOrder.materialUnit
  );
  data.value = {
    ...workOrder,
    amount: workOrder.amount ? `${formatDecimal(workOrder.amount)} ${workOrderUnit}` : "",
    materialUnit: materialUnitName,
    voltageLevel: formatEnum(workOrder.voltageLevel, VoltageClassesEnum, "voltageClassesEnum"),
    planDate: `${formatDate(workOrder.planStartDate)} ～ ${formatDate(workOrder.planFinishDate)}`,
    actualStartDate: formatDate(workOrder.actualStartDate),
    actualFinishDate: formatDate(workOrder.actualFinishDate)
  };
});
</script>

<style scoped></style>
