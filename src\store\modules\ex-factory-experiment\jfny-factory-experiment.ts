import {
  delExFactoryExperiment,
  getDetailExperimentJfny,
  getDetailExperimentJfnyCharts,
  getExFactoryJfnyTableData,
  getExperimentLinkList,
  linkProductionAndExperiment,
  upLoadReportOfJfny
} from "@/api/production-test-sensing/out-going-factory/out-going-factory-jfny";
import { fullDateFormat } from "@/consts";
import {
  IExperimentJfnyDetail,
  IExperimentJfnyEChartDetail,
  IExperimentUploadFile,
  IJfnyExperimentList,
  IJfnyExperimentListReq,
  IResponse,
  ITimeDatas,
  IWaitExperimentJfnyListReq
} from "@/models";
import { IUpLoadFile } from "@/models/production-test-sensing/i-common";
import { formatDate } from "@/utils/format";
import { isArray } from "@pureadmin/utils";
import { defineStore } from "pinia";

export const useExFactoryExperimentJFNYStore = defineStore({
  id: "ex-factory-experiment-jfny",
  state: () => ({
    exFactoryExperimentJfnyTableData: [] as Array<IJfnyExperimentList>,
    jfnyTableTotal: 0,
    uploadFileRef: false,
    detailExperimentInfo: {
      detailExperimentBaseInfo: {} as IExperimentJfnyDetail,
      detailExperimentChartInfo: [] as Array<IExperimentJfnyEChartDetail>
    },
    waitLinkJfnyTotal: 0,
    waitLinkJfnyQuery: {
      productionId: null
    },
    waitLinkJfnyExperimentData: [] as Array<IJfnyExperimentList>,
    linkCheckJfnyExperimentData: [] as Array<IJfnyExperimentList>
  }),
  actions: {
    /**
     * 获取局放耐压的列表数据
     */
    async getExFactoryExperimentJfnyListAction(params: IJfnyExperimentListReq) {
      const res = await getExFactoryJfnyTableData(params);
      if (isArray(res.data?.list) && res.data?.list.length) {
        this.exFactoryExperimentJfnyTableData = res.data.list.map(item => {
          item.startTime = formatDate(item.startTime, fullDateFormat);
          item.endTime = formatDate(item.endTime, fullDateFormat);
          return item;
        });
        this.jfnyTableTotal = res.data.total;
      } else {
        this.exFactoryExperimentJfnyTableData = [];
        this.jfnyTableTotal = 0;
      }
    },

    /**
     * 获取详情数据
     */
    async getDetailExFactoryJfny(id: string) {
      const res = await getDetailExperimentJfny(id);
      if (res.data?.id) {
        this.detailExperimentInfo.detailExperimentBaseInfo = res.data;
        const { startTime, endTime, processVOS } = res.data;
        this.detailExperimentInfo.detailExperimentBaseInfo.startTime = formatDate(startTime, fullDateFormat);
        this.detailExperimentInfo.detailExperimentBaseInfo.endTime = formatDate(endTime, fullDateFormat);
        this.detailExperimentInfo.detailExperimentBaseInfo.processVOS = processVOS.map(process => {
          process.timestamp = formatDate(process.timestamp, fullDateFormat);
          return process;
        });
      }
    },

    /**
     * 获取详情的图表数据
     */
    async getDetailExFactoryJfnyChart(id: string) {
      const res = await getDetailExperimentJfnyCharts(id);
      if (isArray(res.data) && res.data.length) {
        this.detailExperimentInfo.detailExperimentChartInfo = res.data.map(item => {
          item.values = this.handleValuesTimesTamp(item.values);
          return item;
        });
      }
    },
    handleValuesTimesTamp(values: ITimeDatas[]) {
      return (values || []).map(item => {
        // 无法确定后端返回的是13位时间戳还是10位时间戳，无法确定返回的是number还是string
        if (`${item.timestamp}`.length === 10) {
          const timestampStr: number = +item.timestamp * 1000;
          item.timestamp = formatDate(timestampStr, fullDateFormat);
        } else {
          const timestampStr: number = +item.timestamp;
          item.timestamp = formatDate(timestampStr, fullDateFormat);
        }
        return item;
      });
    },

    /**
     * 删除局放耐压的详情数据
     */
    async delExFactoryJfnyData(id: string, productionId: string): Promise<IResponse<number>> {
      return await delExFactoryExperiment(id, productionId);
    },

    /**
     * 查询局方耐压待关联的列表
     */
    queryWaitLinkJfny(searchParam?: IWaitExperimentJfnyListReq) {
      this.waitLinkJfnyQuery = { ...this.waitLinkJfnyQuery, ...searchParam };
      this.getExperimentLinkList();
    },

    /** 获取局放耐压的待关联列表 */
    async getExperimentLinkList() {
      const res = await getExperimentLinkList(this.waitLinkJfnyQuery);
      if (Array.isArray(res.data?.list) && res.data.list?.length) {
        this.waitLinkJfnyExperimentData = res.data.list;
        this.waitLinkJfnyTotal = res.data.total;
      } else {
        this.waitLinkJfnyExperimentData = [];
        this.waitLinkJfnyTotal = 0;
      }
    },

    /**
     * 关联局放耐压试验列表
     */
    async linkProductionAndExperiment(productionId: string, experimentIds: string[]): Promise<IResponse<boolean>> {
      return await linkProductionAndExperiment(productionId, experimentIds);
    },

    /**
     * 上传文件
     */
    async uploadReportOfExperiment(params: IExperimentUploadFile): Promise<IResponse<IUpLoadFile>> {
      return await upLoadReportOfJfny(params);
    },

    /** 重置详情数据 */
    initDetailExperimentInfo() {
      this.detailExperimentInfo.detailExperimentBaseInfo = {};
      this.detailExperimentInfo.detailExperimentChartInfo = [];
    }
  }
});
