<template>
  <div class="select-standard-from-order h-full flex flex-col">
    <section class="select-top">
      <div class="search flex items-center w-1/2 mb-3">
        <span class="text-right" mr-2>订单号：</span>
        <el-input class="mr-4 flex-1" v-model="keywords" clearable placeholder="请输入订单号" />
        <el-button type="primary" @click="searchRawMaterial">搜索</el-button>
      </div>
      <div class="tip-text mb-3">
        <span>注: 生产订单暂只支持选择一个技术标准，已选择标准后再次选择则直接替换现有标准</span>
      </div>
    </section>
    <section class="select-center flex-1 overflow-hidden">
      <PureTable
        ref="tableRef"
        class="flex-1 overflow-hidden pagination"
        row-key="productionOrderNo"
        :data="productOrder"
        :height="460"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        showOverflowTooltip
        @row-click="rowClick"
        @current-change="currentChange"
        @page-current-change="pageNoChange"
        @page-size-change="pageSizeChange"
      >
        <template #radioGroup="{ row }">
          <el-radio-group class="text-center" v-model="currentCheckId">
            <el-radio class="text-center" :label="row.productionOrderNo">{{ "" }}</el-radio>
          </el-radio-group>
        </template>
        <template #operate="{ row }">
          <el-button type="primary" link @click.stop.prevent="previewStandard(row)">预览</el-button>
        </template>
        <template #empty>
          <CxEmpty />
        </template>
      </PureTable>
    </section>
  </div>
  <!-- 预览数据 -->
  <DetailStandard v-model="detailVisible" :standardInfo="standardInfo" :isEditBtn="false" />
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty";
import DetailStandard from "@/views/order/purchase-order/detail/src/components/fill-in-data/technical-standard/detail-technical-standard/detail-technical-standard.vue";
import { TableWidth } from "@/enums";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { useTableConfig } from "@/utils/useTableConfig";
import { onMounted, onUnmounted, ref } from "vue";
import { useFillInDataStore, useTechnicalStandardStore } from "@/store/modules";
import { ITechnicalStandardStockReq } from "@/models";

const emit = defineEmits<{
  (e: "currentChange", data): void;
}>();

const store = useTechnicalStandardStore();
const fillInDataStore = useFillInDataStore();
const keywords = ref();
const columns: TableColumnList = [
  {
    type: "index",
    width: TableWidth.check,
    slot: "radioGroup"
  },
  {
    label: "订单号",
    prop: "productionOrderNo"
  },
  {
    label: "标准名称",
    prop: "standardName"
  },
  {
    label: "操作",
    slot: "operate",
    width: TableWidth.operation
  }
];
const { pagination } = useTableConfig();
const tableRef = ref<PureTableInstance>();
const productOrder = ref([]);
const loading = ref<boolean>(false);
const initProductOrderList = useLoadingFn(getProductOrderList, loading);
const currentCheckId = ref();
const detailVisible = ref<boolean>(false);
const standardInfo = ref();
const defaultPageInfo = {
  pageNo: pagination.currentPage,
  pageSize: pagination.pageSize
};
const queryParams = {};

onMounted(() => {
  initProductOrderList(defaultPageInfo);
});

/** 获取订单数据 */
async function getProductOrderList(params: ITechnicalStandardStockReq) {
  Object.assign(queryParams, params);
  const res = (await store.getTechnicalStandardFromOrder(fillInDataStore.dataId, queryParams)).data;
  productOrder.value = res.list || [];
  pagination.total = res.total || 0;
}

/** 搜索数据 */
function searchRawMaterial() {
  initProductOrderList({
    keyWord: keywords.value,
    ...defaultPageInfo
  });
}

/** 页码改变 */
function pageNoChange(pageNo: number) {
  initProductOrderList({
    keyWord: keywords.value,
    pageNo,
    pageSize: pagination.pageSize
  });
}

/** 页码数量改变 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  initProductOrderList({
    keyWord: keywords.value,
    pageNo: pagination.currentPage,
    pageSize
  });
}

/** 点击行 */
function rowClick(row) {
  currentCheckId.value = row.productionOrderNo;
}

/** 选择的数据 */
function currentChange(data) {
  emit("currentChange", data);
}

/** 预览 */
function previewStandard(row) {
  standardInfo.value = { ...row, id: row.orderStandardId };
  detailVisible.value = true;
}

onUnmounted(() => {
  productOrder.value = [];
  currentCheckId.value = null;
  standardInfo.value = null;
});
</script>

<style scoped lang="scss">
.tip-text {
  color: var(--el-color-warning);
}
</style>
