import { ColumnWidth, BodyPressureEnumMapDesc, RecorderStatusEnumMapDesc, TableWidth } from "@/enums";
import { FileInfoModel } from "@/models";
import { TableColumnRenderer } from "@pureadmin/table";
import { downLoadFile } from "@/api/upload-file";
import { downloadByData } from "@pureadmin/utils";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";

export function useColumns(isCombiner: boolean) {
  const { dateFormatter } = useTableCellFormatter();
  let columns: TableColumnList = [];
  const commonColumns: TableColumnList = [
    {
      label: "同步状态",
      prop: "pullStatus",
      minWidth: ColumnWidth.Char8,
      slot: "pullStatus"
    },
    {
      label: "创建时间",
      prop: "createTime",
      minWidth: ColumnWidth.Char10,
      formatter: dateFormatter()
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      slot: "operation",
      width: ColumnWidth.Char10
    }
  ];
  if (isCombiner) {
    columns = [
      {
        label: "运输车牌号",
        prop: "transportationVehicleLicensePlate",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "子实物ID(单元)",
        prop: "subPhysicalItemId",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "子实物编码",
        prop: "subPhysicalItemCode",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "发运日期",
        prop: "dispatchDate",
        minWidth: ColumnWidth.Char10,
        formatter: dateFormatter()
      },
      {
        label: "记录仪编号",
        prop: "collisionRecorderSerial",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "安装状态",
        prop: "recorderStatus",
        minWidth: ColumnWidth.Char6,
        cellRenderer: (data: TableColumnRenderer) => {
          return RecorderStatusEnumMapDesc[data.row.recorderStatus];
        }
      },
      {
        label: "安装照片",
        prop: "fileId",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.fileIdInfo)}>
              {data.row.fileIdInfo?.name}
            </div>
          );
        }
      }
    ];
  } else {
    columns = [
      {
        label: "车牌号",
        prop: "licensePlateNumber",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "本体压力表压力值(kPa)",
        prop: "bodyPressureValue",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "本体压力表删除状态",
        prop: "bodyPressureDeleteFlag",
        minWidth: ColumnWidth.Char10,
        cellRenderer: (data: TableColumnRenderer) => {
          return BodyPressureEnumMapDesc[data.row.bodyPressureDeleteFlag];
        }
      },
      {
        label: "压力表照片",
        prop: "bodyPressureFileId",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.bodyPressureFileIdInfo)}>
              {data.row.bodyPressureFileIdInfo?.name}
            </div>
          );
        }
      },
      {
        label: "绝缘平均含水量(%)",
        prop: "isolationWaterContent",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "露点温度(℃ )",
        prop: "dewPointTemperature",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "露点值照片",
        prop: "dewPointFileId",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.dewPointFileIdInfo)}>
              {data.row.dewPointFileIdInfo?.name}
            </div>
          );
        }
      },
      {
        label: "冲撞记录仪编号",
        prop: "number",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "冲撞记录仪型号",
        prop: "model",
        minWidth: ColumnWidth.Char10
      },
      {
        label: "冲撞记录照片",
        prop: "collisionFileId",
        minWidth: TableWidth.file,
        cellRenderer: (data: TableColumnRenderer) => {
          return (
            <div class={"cursor-pointer text-primary"} onClick={() => downLoad(data.row.collisionFileIdInfo)}>
              {data.row.collisionFileIdInfo?.name}
            </div>
          );
        }
      }
    ];
  }
  async function downLoad(fileInfo: FileInfoModel) {
    const blob = await downLoadFile(fileInfo.id);
    downloadByData(blob, fileInfo.name, blob.type);
  }
  return { columns: [...columns, ...commonColumns] };
}
