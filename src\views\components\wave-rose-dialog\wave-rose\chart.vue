<template>
  <div>
    <v-chart ref="chartRef" v-if="loaded" :option="option" autoresize class="min-w-[280px] w-full h-full" />
  </div>
</template>

<script setup lang="ts">
import { computed, withDefaults, ref, onMounted } from "vue";
import VChart from "vue-echarts";

const props = withDefaults(
  defineProps<{
    xAxisData?: Array<any>;
    yAxisData?: Array<any>;
  }>(),
  {
    xAxisData: () => [],
    yAxisData: () => []
  }
);

const loaded = ref(false);

const option = computed(() => {
  return {
    grid: {
      top: 30,
      bottom: 70
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: props.xAxisData,
      axisLabel: {
        show: true,
        color: "#909399"
      }
    },
    yAxis: {
      nameGap: 15,
      nameLocation: "end",
      type: "value",
      axisLine: {
        show: true
      },
      axisLabel: {
        show: true,
        color: "#909399"
      },
      axisTick: {
        show: true
      },
      splitLine: {
        show: false
      }
    },
    tooltip: {
      trigger: "axis"
    },
    dataZoom: [
      {
        type: "inside"
      },
      {
        type: "slider"
      }
    ],
    series: [
      {
        type: "line",
        smooth: true,
        symbol: "none",
        sampling: "lttb",
        lineStyle: {
          width: 1
        },
        data: props.yAxisData
      }
    ],
    graphic: {
      type: "text",
      left: "center",
      top: "middle",
      silent: true,
      invisible: !!props.xAxisData.length,
      style: {
        text: "暂无采集点数据",
        fontSize: "16px",
        fill: "#909399"
      }
    }
  };
});

onMounted(() => {
  loaded.value = true;
});
</script>

<style scoped></style>
