import {
  PurchaseChannel,
  StateGridOrderSyncDataFormatEnum,
  OrderSyncPriorityEnum,
  StateGridOrderSyncType
} from "@/enums";

export interface IStateGridOrderSyncParams {
  orderId: string;
  orderItemId: string;
  dataType: StateGridOrderSyncType;
  dataId?: string;
  dataFormat?: StateGridOrderSyncDataFormatEnum;
  productionId?: string;
  workId?: string;
  channel?: PurchaseChannel;
  priority?: OrderSyncPriorityEnum;
}

export interface SyncOrderBySearchParams {
  /** 订单id */
  orderId: string;
  /** 订单行id */
  orderItemId: string;
  /** 步骤 */
  dataType: StateGridOrderSyncType;
  /** 渠道 */
  channel: PurchaseChannel;
  /** 搜索关键词 */
  searchNo?: string;
  /** 同步状态 */
  syncResult?: string;
  /** 工序id */
  processId?: string;
  /** 上海/广州 当前同步步骤 （注意区分 国网） */
  syncProcess?: number;
}

export interface IStateGridOrderRateParams {
  purchaseId: string;
  purchaseLineId: string;
  dataType: StateGridOrderSyncType;
  dataId?: string;
  dataFormat?: StateGridOrderSyncDataFormatEnum;
  productionId?: string;
  workId?: string;
}
