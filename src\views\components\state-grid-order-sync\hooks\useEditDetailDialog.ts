import { ref } from "vue";
import { ISync<PERSON>ommon } from "@/models";

type OpenEditDialog = (data: ISyncCommon) => void;
type OpenDetailDialog = (id: string) => void;

export function useEditDetailDialog<
  E extends { openEditDialog: OpenEditDialog },
  D extends { openDetailDialog: OpenDetailDialog },
  T extends ISyncCommon
>() {
  const editDialog = ref<E>();
  const detailDialog = ref<D>();

  function openEditDialog(data: T) {
    editDialog.value.openEditDialog(data);
  }

  function openDetailDialog(id: string) {
    detailDialog.value.openDetailDialog(id);
  }

  return {
    editDialog,
    detailDialog,
    openEditDialog,
    openDetailDialog
  };
}
