<template>
  <div class="flex flex-col flex-1 overflow-hidden">
    <!-- 页头 -->
    <div class="bg-bg_color flex justify-between pt-2">
      <tab-switch :operate-modules="tabConfig" v-model:currentTabIndex="activeIndex" />
      <div class="flex gap-3 pr-6 -mt-2" v-if="isActiveExperimentDataTab">
        <import-data-dialog ref="experimentDataRef" @post-import-success="reloadExperimentList" />
        <download-template-dialog />
      </div>
    </div>

    <!-- 主体 -->
    <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
      <business-data v-if="isActiveBusinessDataTab" />
      <experiment-data
        v-auth="PermissionKey.meta.metaImportExperiment"
        v-if="isActiveExperimentDataTab"
        ref="experimentDataRef"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { usePageStoreHook } from "@/store/modules/page";
import TabSwitch from "@/components/cx-tab-switch";
import BusinessData from "./business-data/index.vue";
import ExperimentData from "./experiment-data/index.vue";
import DownloadTemplateDialog from "./experiment-data/download-template-dialog/index.vue";
import ImportDataDialog from "./experiment-data/import-data-dialog/index.vue";
import { PermissionKey } from "@/consts/permission-key";
import { TabEnum, tabConfig } from "./module-config";

usePageStoreHook().setTitle("数据导入");

const experimentDataRef = ref<InstanceType<typeof ExperimentData>>();

const activeIndex = ref(0);
/** 是否激活业务数据tab */
const isActiveBusinessDataTab = computed(() => tabConfig.value[activeIndex.value].moduleCode === TabEnum.BusinessData);
/** 是否激活试验数据tab */
const isActiveExperimentDataTab = computed(
  () => tabConfig.value[activeIndex.value].moduleCode === TabEnum.ExperimentData
);

/**
 * @description: 导入成功后刷新试验数据列表
 */
function reloadExperimentList() {
  if (!experimentDataRef.value) {
    return;
  }
  experimentDataRef.value.reloadExperimentList();
}
</script>

<style scoped lang="scss"></style>
