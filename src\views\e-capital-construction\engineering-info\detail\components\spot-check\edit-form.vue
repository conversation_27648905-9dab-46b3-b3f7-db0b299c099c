<template>
  <!-- 原材料组部件抽检 -->
  <el-form
    ref="formRef"
    :model="formData"
    :validate-on-rule-change="false"
    class="cx-form"
    label-position="top"
    label-width="6rem"
  >
    <el-row :gutter="40">
      <el-col :span="24">
        <TitleBar class="mb-2" title="原材料信息" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="采集类型ID"
          prop="gisComponentType"
          :rules="{ required: true, message: '请选择采集类型ID', trigger: 'change' }"
        >
          <el-select
            v-model="formData.gisComponentType"
            class="w-full"
            placeholder="请选择采集类型ID"
            clearable
            filterable
          >
            <el-option v-for="(val, key) in GisComponentEnum" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="总抽检批次"
          prop="totalBatchesSampled"
          :rules="{ required: true, message: '请输入总抽检批次', trigger: 'change' }"
        >
          <el-input-number
            class="w-full"
            :min="0"
            v-model="formData.totalBatchesSampled"
            placeholder="请输入总抽检批次"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="批次编号"
          prop="batchNumber"
          :rules="{ required: true, message: '请输入批次编号', trigger: 'change' }"
        >
          <el-input v-model="formData.batchNumber" placeholder="请输入批次编号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="批次数量"
          prop="batchQuantity"
          :rules="{ required: true, message: '请输入批次数量', trigger: 'change' }"
        >
          <el-input-number
            :min="0"
            class="w-full"
            v-model="formData.batchQuantity"
            placeholder="请输入批次数量"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="钢印号/字头号"
          prop="stampingNumber"
          :rules="{ required: true, message: '请输入钢印号/字头号', trigger: 'change' }"
        >
          <el-input v-model="formData.stampingNumber" placeholder="请输入钢印号/字头号" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="试验时间"
          prop="testTime"
          :rules="{ required: true, message: '请选择试验时间', trigger: 'change' }"
        >
          <el-date-picker
            v-model="formData.testTime"
            placeholder="请选择试验时间"
            class="flex-1"
            clearable
            type="datetime"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="抽检结果"
          prop="samplingResult"
          :rules="{ required: true, message: '请选择抽检结果', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.samplingResult">
            <el-radio v-for="item in SamplingResultEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <TitleBar class="mb-2" title="抽检项目" />
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="液压失效次数"
          prop="hydraulicFailureCount"
          :rules="{ required: true, message: '请选择液压失效次数', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.hydraulicFailureCount">
            <el-radio v-for="item in HydraulicFailureCountEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="抽检失效值"
          prop="failureValueSampled"
          :rules="{
            required:
              formData.gisComponentType == 1 ||
              formData.gisComponentType == 2 ||
              formData.gisComponentType == 5 ||
              formData.gisComponentType == 8,
            message: '请输入破坏值（抽检）',
            trigger: 'change'
          }"
        >
          <el-input-number
            class="w-full"
            v-model="formData.failureValueSampled"
            placeholder="请输入破坏值（抽检）"
            :precision="2"
            controls-position="right"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="抽检失效位置"
          prop="failureLocationSampled"
          :rules="{
            required:
              formData.gisComponentType == 1 ||
              formData.gisComponentType == 2 ||
              formData.gisComponentType == 5 ||
              formData.gisComponentType == 8,
            message: '请输入抽检失效位置',
            trigger: 'change'
          }"
        >
          <el-input v-model="formData.failureLocationSampled" placeholder="请输入抽检失效位置" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="操动机构类型"
          prop="operatingMechanismType"
          :rules="{
            required: formData.gisComponentType == 4,
            message: '请选择操动机构类型',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.operatingMechanismType">
            <el-radio v-for="item in OperatingMechanismTypeEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="机械操作"
          prop="mechanicalOperation"
          :rules="{
            required: formData.gisComponentType == 4,
            message: '请选择机械操作',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.mechanicalOperation">
            <el-radio v-for="item in MechanicalOperationEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item
          label="操作次数 "
          prop="operationCount"
          :rules="{
            required: false,
            message: '请输入操作次数 ',
            trigger: 'change'
          }"
        >
          <el-radio-group v-model="formData.operationCount">
            <el-radio v-for="item in MechanicalOperationEnumOptions" :key="item.value" :label="Number(item.value)">{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watchEffect } from "vue";
import { FormInstance } from "element-plus";
import {
  GisComponentEnum,
  HydraulicFailureCountEnumOptions,
  OperatingMechanismTypeEnumOptions,
  MechanicalOperationEnumOptions,
  SamplingResultEnumOptions
} from "@/enums";
import { SpotCheckModel } from "@/models";
import TitleBar from "@/components/TitleBar/index";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    detail: SpotCheckModel; // 表格表单数据
    isEdit: boolean;
  }>(),
  {
    detail: () => {
      return {} as SpotCheckModel;
    },
    isEdit: false
  }
);
const formData = reactive({} as SpotCheckModel);
// const editMode = ref(false);
const route = useRoute();

watchEffect(() => {
  Object.assign(formData, props.detail);
  if (!props.isEdit) {
    formData.equipmentType = Number(route.query.type);
  }
});

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as SpotCheckModel);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
