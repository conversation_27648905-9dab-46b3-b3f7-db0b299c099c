<template>
  <pure-table
    show-overflow-tooltip
    size="large"
    class="flex-1 overflow-hidden"
    :class="{ pagination: addClassPagination }"
    row-key="id"
    v-bind="$attrs"
    :data="data"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :max-height="600"
    @page-current-change="pageChange"
    @page-size-change="pageSizeChange"
  >
    <template #empty>
      <CxEmpty />
    </template>
  </pure-table>
  <EditFinishingWarehousingDialog ref="editDialog" :subclass-code="subclassCode" />
</template>

<script setup lang="ts">
import CxEmpty from "@/components/CxEmpty/CxEmpty.vue";
import { PureTable } from "@pureadmin/table";
import { useStateGridSyncAuditStore } from "@/store/modules/state-grid-order-sync/state-grid-sync-audit";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import {
  QualifiedEnum,
  ShipmentStatusEnum,
  StateGridOrderSyncResult,
  TableWidth,
  VoltageClassesEnumMapDesc
} from "@/enums";
import { computed, inject, onMounted, provide, ref, watchEffect } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IFinishedProductionSync, IPagingReq } from "@/models";
import { dateFormat } from "@/consts";
import { OperatorCell } from "@/components/TableCells";
import { stateGridOrderSyncEditKey } from "@/views/components/state-grid-order-sync/tokens";
import EditFinishingWarehousingDialog from "@/views/components/state-grid-order-sync/dialogs/edit-finishing-warehousing-dialog.vue";
import { useTableConfig } from "@/utils/useTableConfig";
import { syncAuditStateKey } from "../../tokens";

const store = useStateGridSyncAuditStore();
const { enumFormatter, dateFormatter, mapFormatter } = useTableCellFormatter();

const loading = ref(false);
const allData = ref<Array<IFinishedProductionSync>>();
const data = ref<Array<IFinishedProductionSync>>();
const editDialog = ref<InstanceType<typeof EditFinishingWarehousingDialog>>();
const handleRefresh = useLoadingFn(store.getFinishedProductionSyncList, loading);
const ctx = inject(syncAuditStateKey);
const subclassCode = computed(() => store.subClassCode || ctx.subClassCode);

provide(stateGridOrderSyncEditKey, {
  refreshFn: refresh
});

const normalColumn = [
  {
    label: "成品编号",
    prop: "productNo",
    minWidth: TableWidth.largeOrder
  },
  {
    label: "生产批次号",
    prop: "productBatchNo",
    minWidth: TableWidth.largeOrder
  }
];

const armourClampColumn = [
  {
    label: "产品名称",
    prop: "productName",
    minWidth: TableWidth.largeOrder
  }
];

const columns: TableColumnList = [
  ...(store.isArmourClamp ? armourClampColumn : normalColumn),
  {
    label: "规格型号",
    prop: "specificationModel",
    minWidth: TableWidth.largeName
  },
  {
    label: "电压等级",
    prop: "voltageLevel",
    minWidth: TableWidth.number,
    formatter: mapFormatter(VoltageClassesEnumMapDesc)
  },
  {
    label: "数量",
    prop: "amount",
    minWidth: TableWidth.number,
    formatter: row => {
      const { amount, unitName } = row;
      return amount ? `${amount} ${unitName}` : null;
    }
  },
  {
    label: "入库日期",
    prop: "storageTime",
    minWidth: TableWidth.date,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "检验结果",
    prop: "isQualified",
    minWidth: TableWidth.type,
    formatter: enumFormatter(QualifiedEnum, "QualifiedEnum")
  },
  {
    label: "发货状态",
    prop: "shipmentStatus",
    minWidth: TableWidth.type,
    formatter: enumFormatter(ShipmentStatusEnum, "ShipmentStatusEnum")
  },
  {
    label: "发货时间",
    prop: "shipmentTime",
    minWidth: TableWidth.dateTime,
    formatter: dateFormatter(dateFormat)
  },
  {
    label: "同步状态",
    prop: "syncResult",
    width: TableWidth.type,
    fixed: "right",
    formatter: enumFormatter(StateGridOrderSyncResult, "StateGridOrderSyncResult")
  },
  {
    label: "操作",
    prop: "operator",
    width: TableWidth.operation,
    fixed: "right",
    className: "no-default",
    cellRenderer: data => {
      if (!data.row.editable) {
        return null;
      }
      return OperatorCell([
        {
          name: "编辑",
          action: () => editDialog.value.openEditDialog(data.row),
          props: { type: "primary" }
        }
      ]);
    }
  }
];

// 分页信息
const { pagination } = useTableConfig();
// 分页信息
const pageInfo = {
  pageNo: 1,
  pageSize: pagination.pageSize
};
pagination.hideOnSinglePage = true;
// 是否添加分页类名
const addClassPagination = computed(() => {
  return pagination?.total > 20;
});

watchEffect(() => {
  const activeNo = store.activeNo;
  data.value = activeNo ? allData.value?.filter(datum => datum.orderNo === activeNo) : allData.value;
});

onMounted(() => {
  refresh(pageInfo);
});

async function refresh(pageInfo?: IPagingReq) {
  const res = await handleRefresh(pageInfo);
  allData.value = res.list || [];
  pagination.total = res.total || 0;
}

/**
 * 切换页码
 */
function pageChange(pageNo: number) {
  refresh({ pageSize: pagination.pageSize, pageNo });
}
/**
 * 切换页码数量
 */
function pageSizeChange(pageSize: number) {
  pagination.currentPage = 1;
  refresh({ pageNo: 1, pageSize });
}
</script>

<style scoped></style>
