import { http } from "@/utils/http";
import { IDeviceDataAcquisitionReq, IResponse, IAcquisitionPointData } from "@/models";
import { withApiGateway } from "../util";

/**
 * @description: 实时数据查询（最近1分钟）
 */
export const deviceRealTimeDataAcquisition = (params: IDeviceDataAcquisitionReq, signal: AbortSignal) => {
  const url = withApiGateway("admin-api/business/device/deviceRealTimeDataAcquisition");
  return http.post<IDeviceDataAcquisitionReq, IResponse<IAcquisitionPointData>>(
    url,
    { data: params },
    {
      signal,
      timeout: 0
    }
  );
};

/**
 * @description: 历史数据查询
 */
export const deviceHistoryDataAcquisition = (params: IDeviceDataAcquisitionReq, signal: AbortSignal) => {
  const url = withApiGateway("admin-api/business/device/deviceHistoryDataAcquisition");
  return http.post<IDeviceDataAcquisitionReq, IResponse<IAcquisitionPointData>>(
    url,
    { data: params },
    {
      signal,
      timeout: 0
    }
  );
};
