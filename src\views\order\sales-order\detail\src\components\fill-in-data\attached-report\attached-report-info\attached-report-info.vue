<template>
  <el-dialog
    v-model="visible"
    :title="title"
    align-center
    draggable
    class="default"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="patch-form">
      <AttachedReportForm ref="patchFormRef" :processDocInfo="props.processDocInfo" />
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :saveLoading="saveLoading" @click="saveAttachedReportInfo">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import AttachedReportForm from "./attached-report-from.vue";
import { computed, ref } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { IAttachDocumentRes, ISaveDocumentReq } from "@/models/attached-report";
import { useAttachReportStore, useSalesFillInDataStore } from "@/store/modules";
import { omit } from "lodash-unified";
import { ElMessage } from "element-plus";

const props = defineProps<{
  modelValue?: boolean;
  selectedId?: string;
  processDocInfo: IAttachDocumentRes;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", val: boolean): void;
}>();
const fillInDataStore = useSalesFillInDataStore();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const title = computed(() => (!props.selectedId ? "新增工序文档" : "编辑工序文档"));
const patchFormRef = ref<InstanceType<typeof AttachedReportForm>>();
const saveLoading = ref<boolean>(false);
const saveAttachedReportInfo = useLoadingFn(postAttachedReport, saveLoading);
const attachReportStore = useAttachReportStore();

/** 保存编辑工序文档数据 */
async function postAttachedReport() {
  let reportInfo: ISaveDocumentReq | false = await patchFormRef.value?.getFormValue().catch(() => false);
  if (!reportInfo) {
    return;
  }
  reportInfo = omit(reportInfo, ["uploadFile", "processCode"]);
  reportInfo.productionId = fillInDataStore.data.id;
  await attachReportStore.saveProcessDocument(reportInfo);
  ElMessage.success(!props.selectedId ? `新增工序文档成功` : `编辑工序文档成功`);
  visible.value = false;
}
</script>

<style scoped lang="scss"></style>
