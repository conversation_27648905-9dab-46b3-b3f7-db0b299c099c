<template>
  <div>
    <span class="font-semibold ml-2 text-[14.5px] miss-items">缺失项（{{ state.sum }}）</span>
    <DataIntegrityCheckPopoverContentInfo
      :text="'过程检'"
      :production-test-key="ETestSensingType.ProductionProcessInspection"
      :production-test-index="1"
      :pointList="props.processPointList"
      :requiredPointList="props.requiredPointList"
      :productionId="props.productionId"
      :purchaseOrderId="props.purchaseOrderId"
      :saleOrderId="props.saleOrderId"
    />
    <DataIntegrityCheckPopoverContentInfo
      :text="'自动采集项'"
      :production-test-key="ETestSensingType.ProductionProcessInspection"
      :production-test-index="1"
      :pointList="props.timePointList"
      :requiredPointList="props.requiredPointList"
      :productionId="props.productionId"
      :purchaseOrderId="props.purchaseOrderId"
      :saleOrderId="props.saleOrderId"
    />
    <DataIntegrityCheckPopoverContentInfo
      :text="'试验数据'"
      :production-test-key="ETestSensingType.ExFactoryExperiment"
      :production-test-index="2"
      :pointList="props.experPointList"
      :requiredPointList="props.requiredPointList"
      :productionId="props.productionId"
      :purchaseOrderId="props.purchaseOrderId"
      :saleOrderId="props.saleOrderId"
    />
  </div>
</template>
<script setup lang="ts">
import { reactive, watchEffect } from "vue";
import DataIntegrityCheckPopoverContentInfo from "../data-integrity-check-popover-content-info/index.vue";
import { ETestSensingType } from "@/enums/purchase-order/production-test-sensing";

const props = defineProps<{
  /* 生产订单id **/
  productionId: string;
  /* 采购订单id **/
  purchaseOrderId: string;
  /* 销售订单Id */
  saleOrderId: string;
  /* 过程检 **/
  processPointList: Array<string>;
  /* 过程检 自动采集列表 **/
  timePointList: Array<string>;
  /* 过程检 必采列表 **/
  requiredPointList: Array<string>;
  /* 试验列表 **/
  experPointList: Array<string>;
}>();

const state = reactive<{
  sum: number;
}>({
  sum: 0
});

watchEffect(() => {
  state.sum = props.processPointList.length + props.experPointList.length + props.timePointList.length;
});
</script>
<style scoped lang="scss">
.miss-items {
  color: rgb(178, 178, 200);
}
</style>
