import { VoltageClassesEnum } from "@/enums";
import { IDictionaryOption } from "../platform";

export interface ISalesOrderLine {
  id: string;
  /**	销售订单ID */
  salesId: string;
  /** 销售订单号  */
  soNo: string;
  /** 采购方公司名称 **/
  buyerName: string;

  /** 销售订单行项目号 */
  soItemNo: string;

  /** 	采购订单行项目号 */
  poItemNos: string;

  /** 物资品类 */
  categoryCode: string;

  /** 物资种类  */
  subClassCode: string;
  /** 物资种类名称  */
  subClassName: string;

  /** 物料Id */
  materialId?: string;
  /** 物料名称 */
  materialName: string;

  /** 物料单位 */
  materialUnit: string;

  /**物料数量 */
  materialNumber: number;

  /** 物料编码 */
  materialCode: string;
  /** 物料描述 */
  materialDesc: string;

  /** 	电压等级  */
  voltageLevel: VoltageClassesEnum;

  /** 是否生产 */
  isProduction: boolean;

  /** 	生产订单号  */
  ipoNo: string;

  /** 	有无工单 */
  isWork: string;

  /** 规格型号 **/
  specificationType: string;

  /** 是否排产 */
  schduled: boolean;

  /** 前端分割 poItemNos */
  poItemNoArray?: Array<string>;
  /** 前端关联采购订单行，线性变化颜色 */
  linearGradient?: string;
  /** 单位字典对象 */
  unitDictionary?: IDictionaryOption;
  /** 项目号 */
  prjNo?: string;
  /** 项目名称 */
  prjName?: string;

  /** 销售订单行信息 */
  salesLineDetails?: ISalesOrderLine[];
  /** 项目号 */
  prjCode?: string;
  /** 是否本订单 true：本订单 false其他订单 */
  orderField: boolean;
  /** 生产工号 **/
  productionWorkNo?: string;
  /** 生产工号 **/
  entityId?: string;
}
