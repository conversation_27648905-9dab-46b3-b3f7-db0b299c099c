<template>
  <el-radio-group v-model="active">
    <el-radio-button v-for="item in list" :key="item.value" :label="item.value">
      <el-tooltip placement="top" :key="item.value" :label="item.value" :content="item.tooltip">
        {{ item.label }}({{ item.count }})
      </el-tooltip>
    </el-radio-button>
  </el-radio-group>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useLoadingFn } from "@/utils/useLoadingFn";
import { getQualityTracingRecordLevelList } from "@/api/quality-tracing";
import { QualityTracingRecordLevelListParams } from "@/models/quality-tracing";

const props = defineProps<{
  modelValue?: string;
}>();

const emits = defineEmits(["update:modelValue"]);

const active = ref("");
const loading = ref(false);
const list = ref<
  Array<{
    label: string;
    value: string;
    count: number;
    tooltip: string;
  }>
>([]);

/**
 * @description: 获取质量追溯记录等级列表
 */
const requestList = useLoadingFn(async (params: QualityTracingRecordLevelListParams) => {
  const { data } = await getQualityTracingRecordLevelList(params);

  list.value = data.map(item => {
    return {
      label: item.levelName,
      value: item.qualityLevelId,
      count: item.countVal,
      tooltip: item.levelDesc
    };
  });

  active.value = list.value[0].value;
}, loading);

watch(
  () => props.modelValue,
  v => {
    if (!v) {
      active.value = "";
      return;
    }
    active.value = v;
  }
);

watch(active, levelId => {
  emits("update:modelValue", levelId);
});

defineExpose({
  requestLevelList: requestList
});
</script>

<style scoped lang="scss"></style>
