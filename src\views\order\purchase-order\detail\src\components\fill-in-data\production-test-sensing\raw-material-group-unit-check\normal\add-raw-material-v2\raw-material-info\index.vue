<template>
  <el-form
    ref="inspectFormIns"
    label-width="7rem"
    class="cx-form"
    label-position="right"
    :scroll-to-error="true"
    :model="inspectForm"
    :rules="rules"
  >
    <div class="raw-material-form mb-2">
      <div class="title flex-bc mb-4">
        <TitleBar title="原材料信息" />
        <el-button type="primary" class="ml-4" @click="selectRawMaterial">选择原材料</el-button>
      </div>
      <div class="raw-material-form">
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="原材料类型" prop="processName">
              <el-input v-model="inspectForm.processName" placeholder="请输入原材料类型" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原材料编号" prop="code">
              <el-input v-model="inspectForm.code" placeholder="请输入原材料编号" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="原材料批次号" prop="materialBatchNo">
              <el-input v-model="inspectForm.materialBatchNo" placeholder="请输入原材料批次号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="modelCode">
              <el-input v-model="inspectForm.modelCode" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="原材料名称" prop="name">
              <el-input v-model="inspectForm.name" placeholder="请输入原材料名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用料量" prop="quantity">
              <el-input-number
                v-model="inspectForm.quantity"
                :min="0"
                class="!w-full"
                controls-position="right"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="raw-material-inspec-form mb-2">
      <div class="title mb-4">
        <TitleBar title="原材料检信息" />
      </div>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="检验批次号" prop="inspectBatchNo">
            <el-input v-model="inspectForm.inspectBatchNo" placeholder="请输入检验批次号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检验日期" prop="inspectDate">
            <el-date-picker
              class="!w-full"
              v-model="inspectForm.inspectDate"
              type="date"
              placeholder="请选择日期"
              :disabled-date="disabledNowAfterDate"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="inspectForm.remark" :rows="2" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>

  <!-- 选择原材料数据 -->
  <el-dialog
    v-model="selectRawMaterialInfoRef"
    title="选择原材料"
    class="middle"
    align-center
    :destroy-on-close="true"
    :close-on-click-modal="false"
    @close="closeSelectRawMaterial()"
  >
    <div class="add-raw-material-inspec">
      <SelectRawMaterial ref="selectRawMaterialInstance" />
    </div>
    <template #footer>
      <el-button @click="closeSelectRawMaterial()">取消</el-button>
      <el-button type="primary" @click="saveSelectRawMaterial()" :disabled="!selectRawMaterialLists?.code"
        >保存</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitleBar from "@/components/TitleBar/index";
import SelectRawMaterial from "../select-raw-matrial/index.vue";
import { useRawMaterialGroupUnitStore } from "@/store/modules/raw-material-group-unit-check";
import { FormInstance, FormRules } from "element-plus";
import { reactive, ref, inject, computed } from "vue";
import { disabledNowAfterDate } from "@/utils/disabledDate";
import { IProductionMaterialProcess } from "@/models/production-test-sensing/i-raw-material-group-unit-check";

const ctx = inject<{
  processInfo: IProductionMaterialProcess;
}>("addRawMaterialToken");

// 表单信息
const inspectFormIns = ref<FormInstance>();
const inspectForm = reactive<any>({
  processName: ctx.processInfo?.processName,
  processCode: ctx.processInfo?.processCode,
  code: null,
  name: null,
  materialBatchNo: null,
  modelCode: null,
  quantity: null,
  inspectBatchNo: null,
  inspectDate: null,
  inspectOperate: null,
  inspectEnterprise: null,
  auditor: null,
  remark: null
});
const rules = reactive<FormRules>({
  processName: [{ required: true, message: "请输入原材料类型", trigger: "change" }],
  code: [{ required: true, message: "请输入原材料编号", trigger: "change" }],
  materialBatchNo: [{ required: true, message: "请输入原材料批次号", trigger: "change" }],
  name: [{ required: true, message: "请输入原材料名称", trigger: "change" }],
  modelCode: [{ required: true, message: "请输入规格型号", trigger: "change" }],
  quantity: [{ required: false, message: "请输入用料量", trigger: "change" }],
  inspectBatchNo: [{ required: true, message: "请输入检验批次号", trigger: "change" }],
  inspectDate: [{ required: true, message: "请选择检验日期", trigger: "change" }]
});
const rawMaterialStore = useRawMaterialGroupUnitStore();
rawMaterialStore.getRawMaterialKindOptions();
const selectRawMaterialInfoRef = ref<boolean>(false);
const selectRawMaterialLists = computed(() => rawMaterialStore.selectRawMaterialList);

/**
 * 选择原材料
 */
const selectRawMaterial = () => {
  selectRawMaterialInfoRef.value = true;
};
/**
 * 关闭选择原材料的弹框
 */
const closeSelectRawMaterial = () => {
  selectRawMaterialInfoRef.value = false;
};
/**
 * 保存选择的原材料
 */
const saveSelectRawMaterial = () => {
  const selectInspectInfo = rawMaterialStore.selectRawMaterialList;
  const processCode = inspectForm.processCode;
  Object.assign(inspectForm, { ...selectInspectInfo, materialId: selectInspectInfo.id });
  inspectForm.processCode = processCode;
  closeSelectRawMaterial();
};

/**
 * 校验数据 成功返回表单数据，失败直接返回
 */
const validateAndSendFormData = async () => {
  const formEl = inspectFormIns.value;
  if (!formEl) return;
  const validate = await formEl.validate(() => {});
  if (validate) {
    return Promise.resolve(inspectForm);
  } else {
    return Promise.reject();
  }
};

defineExpose({
  validateAndSendFormData
});
</script>

<style scoped lang="scss"></style>
