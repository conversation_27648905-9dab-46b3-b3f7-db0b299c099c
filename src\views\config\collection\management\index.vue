<template>
  <div class="collection-config flex-1 overflow-hidden">
    <section class="collection-section h-full flex-1 flex flex-col overflow-hidden">
      <!-- 品类Tab切换 -->
      <div class="pt-1 px-6 bg-bg_color">
        <template v-for="(tag, index) in collectionsItems" :key="tag.collecProcessId">
          <div class="tab-view" :class="{ active: index === currentTagIndex }" @click="switchTag(tag, index)">
            {{ tag.categoryName }}
          </div>
        </template>
      </div>
      <!-- 对应品类的配置项 -->
      <div class="collection-container p-5 flex-1 flex overflow-hidden">
        <div class="collection-left w-[260px] bg-bg_color mr-4">
          <CollectionTree :collectionItems="currentCollection" @handleNodeChange="handleNodeChange($event)" />
        </div>
        <div class="collection-right bg-bg_color p-4 h-full flex-1 flex flex-col overflow-hidden">
          <div class="flex">
            <div class="collection-items mb-4 flex-1 flex items-center min-h-[32px]">
              <div class="label w-[85px]">生产阶段：</div>
              <el-radio-group
                v-model="currentCollecCategoryCode"
                v-if="productProcessList.length"
                @change="productProcessChange"
              >
                <template v-for="category in productProcessList" :key="category.id">
                  <el-radio-button :label="category.productionStageCode">{{
                    category.productionStageName
                  }}</el-radio-button>
                </template>
              </el-radio-group>
              <div v-else class="empty text-secondary text-base">暂无数据</div>
            </div>
            <div class="add-collection">
              <el-button
                type="primary"
                @click="addCollectionInfo"
                :disabled="!productProcessList.length || !processInfoList.length"
              >
                <template #icon>
                  <FontIcon icon="icon-plus" />
                </template>
                新增采集项类别
              </el-button>
            </div>
          </div>
          <!-- 工序数据 -->
          <div class="process-info flex mb-2.5 min-h-[42px]">
            <div class="label w-[85px] mb-2.5">生产工序：</div>
            <SwitchTab
              class="flex-1"
              v-if="processInfoList.length"
              :switchTags="processInfoList"
              :hasIcon="false"
              @switch-change="switchProcess($event)"
            />
            <div v-else class="empty text-secondary text-base mb-2.5">暂无数据</div>
          </div>
          <div class="flex-1 h-full overflow-hidden">
            <PureTable
              class="flex-1 overflow-hidden"
              row-key="id"
              border
              :data="collectionItemsStore.collectionItemsTableList"
              :columns="columns"
              showOverflowTooltip
              :pagination="[]"
              :loading="collectionItemsStore.collectionLoading"
              :span-method="mergeSpanMethod"
            >
              <template #operates="data">
                <div class="operate-btn">
                  <ElButton link type="primary" @click="editCollection(data.row?.id)">编辑采集项</ElButton>
                  <ElButton link type="danger" @click="deleteCollection(data.row?.id)">删除</ElButton>
                </div>
              </template>

              <template #empty>
                <el-empty :image-size="120">
                  <template #image> <EmptyData /> </template>
                </el-empty>
              </template>
            </PureTable>
          </div>
        </div>
      </div>
    </section>
    <!-- 新增采集项数据 -->
    <el-dialog
      v-model="addCollectionCategoryRef"
      :title="collectionCategoryTitle"
      class="middle"
      align-center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="collectionCategoryClose()"
    >
      <!-- 采集项配置 -->
      <AddCollectionItems ref="collectionItemIns" />
      <template #footer>
        <el-button @click="collectionCategoryClose()">取消</el-button>
        <el-button type="primary" @click="addCollectionCategorySave()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import CollectionTree from "../component/tree-collection-catory.vue";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import AddCollectionItems from "../add-collection-item/index.vue";
import SwitchTab from "@/views/order/purchase-order/detail/src/components/fill-in-data/production-test-sensing/component/switch-tab/index.vue";
import { usePageStoreHook } from "@/store/modules/page";
import { ref, onMounted } from "vue";
import { useCategoryStore } from "@/store/modules/category";
import { ICategoryInfoFindTenantList } from "@/models/category/i-category-info-list";
import { useCollectionItemsStore } from "@/store/modules/collection-items";
import { SpanMethodProps } from "@/models";
import { IProcessInfoList, IProductProcessList } from "@/models/collection-items/i-collection-items";
import { EControlType } from "@/enums";
import { ElMessage } from "element-plus";
import { TreeNodeData } from "element-plus/es/components/tree/src/tree.type";
import { useConfirm } from "@/utils/useConfirm";
import { useColumns } from "./columns";
// 设置标题
usePageStoreHook().setTitle("采集项配置");

// 品类及物资种类信息
const categoryStore = useCategoryStore();
const collectionItemsStore = useCollectionItemsStore();
const collectionsItems = ref<Array<ICategoryInfoFindTenantList>>();
const currentCollection = ref([]);
const currentTagIndex = ref(0);

const productProcessList = ref<Array<IProductProcessList>>([]);
const currentCollecCategoryCode = ref();
const processInfoList = ref<Array<IProcessInfoList>>([]);
const currentProcessInfo = ref();

const collectionItemIns = ref();
const collectionCategoryTitle = ref("新增采集项信息");
const isAddCollection = ref(true);
const addCollectionCategoryRef = ref();

onMounted(async () => {
  collectionsItems.value = await categoryStore.queryCategoryInfoTenantCategoryList();
  currentCollection.value = collectionsItems.value[0].children;
});

// 表格属性获取
const { columns } = useColumns();

// 切换品类数据信息
const switchTag = (collectionsItem: any, index: number) => {
  currentTagIndex.value = index;
  currentCollection.value = collectionsItem.children;
};

// 点击左侧树 获取生产过程
const handleNodeChange = async (nodeData: TreeNodeData) => {
  if (!nodeData) {
    productProcessList.value = [];
    processInfoList.value = [];
    collectionItemsStore.collectionItemsTableList = [];
  }
  if (!nodeData?.isLeaf) return;
  await collectionItemsStore.getProductProcessByCategory(nodeData.data?.categoryCode);
  productProcessList.value = collectionItemsStore.productProcessList;
  productProcessChange(productProcessList.value[0]?.productionStageCode);
};

// 切换不同的生产工艺
const productProcessChange = async (productProcessCode: string) => {
  processInfoList.value = [];
  if (!productProcessCode) {
    collectionItemsStore.collectionItemsTableList = [];
    return;
  }
  currentCollecCategoryCode.value = productProcessCode;
  const currentProductProcess = productProcessList.value.find(item => item.productionStageCode === productProcessCode);
  await collectionItemsStore.getProcessInfoByProductProcessId(currentProductProcess.id);
  processInfoList.value = collectionItemsStore.processInfoList;
  switchProcess(processInfoList.value[0]);
};

// 获取生产过程的工序数据
const switchProcess = async process => {
  if (process) {
    currentProcessInfo.value = process?.processCode ? process : process.data;
    collectionItemsStore.getCollectionItemsDataActions(currentProcessInfo.value);
  } else {
    collectionItemsStore.collectionItemsTableList = [];
  }
};

/** 合并采集项数据 */
const mergeSpanMethod = ({ rowIndex, columnIndex }: SpanMethodProps) => {
  if (columnIndex === 0 || columnIndex == 1) {
    if (rowIndex === 0) {
      return {
        rowspan: collectionItemsStore.collectionItemsTableList.length,
        colspan: 1
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0
      };
    }
  }
};

// 新增采集项信息
const addCollectionInfo = () => {
  collectionCategoryTitle.value = "新增采集项信息";
  isAddCollection.value = true;
  addCollectionCategoryRef.value = true;
};

// 关闭采集项弹框
const collectionCategoryClose = () => {
  addCollectionCategoryRef.value = false;
  collectionItemsStore.initDetialInfo();
};

// 编辑采集项信息
const editCollection = (id: string) => {
  collectionCategoryTitle.value = "编辑采集项信息";
  addCollectionCategoryRef.value = true;
  isAddCollection.value = false;
  collectionItemsStore.getDetailOfCollectionItemsById(id);
};

//保存采集项信息
const addCollectionCategorySave = () => {
  collectionItemIns.value?.validForm(async res => {
    if (res.identityCode) {
      const { decimalDigits, identityCode, identityDetailList } = res;
      if (identityCode !== EControlType.SelectControl && identityCode !== EControlType.RadioControl) {
        if (!identityDetailList.length) {
          const identityDetail = {
            id: null,
            identityLabel: "",
            identityValue: "",
            metadataModelDetailId: "",
            maxLength: "",
            minLength: "",
            step: decimalDigits
          };
          res.identityDetailList.push(identityDetail);
        }
      }
      // 保存采集项数据
      delete res["dataTypeIdentityDetail"];
      const { id } = currentProcessInfo.value;
      const formData = { ...res, ...{ processId: id } };
      await collectionItemsStore.saveCollectionItemsActions(formData);
      ElMessage.success(isAddCollection.value ? "新增成功" : "编辑成功");
      collectionCategoryClose();
      refresh();
    }
  });
};
// 删除采集项
const deleteCollection = async (id: string) => {
  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  await collectionItemsStore.delCollectionItemsById(id);
  ElMessage.success("删除成功");
  refresh();
};

// 刷新页面
const refresh = () => {
  collectionItemsStore.getCollectionItemsDataActions(currentProcessInfo.value);
};
</script>

<style scoped lang="scss">
.collection-item {
  .tag {
    @apply inline-flex flex-ac py-1 px-2.5 text-base;
    cursor: pointer;
    height: 32px;
    margin-right: 10px;
    border: 1px solid var(--el-color-info-light-8);
    border-radius: 3px;
    color: var(--el-text-color-regular);
    box-shadow: 2px 2px 5px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    background-color: #fff;

    &.active {
      color: var(--el-color-primary);
      border-color: var(--el-color-primary-light-5);
      background: var(--el-color-primary-light-9);
      box-shadow: 2px 2px 5px 0 var(--el-color-primary-light-9);
    }
  }
}

.tab-view {
  @apply inline-flex flex-col items-center pr-6 cursor-pointer;

  &:after {
    display: inline-block;
    content: "";
    width: 40px;
    height: 3px;
    margin-top: 10px;
  }

  &.active {
    color: var(--el-color-primary);

    &:after {
      background: var(--el-color-primary);
    }
  }
}
</style>
