import { MEASURE_UNIT, SPARE_PART, dateFormat, fullDateFormat } from "@/consts";
import {
  FunctionTypeEnum,
  ImportDataTypeEnum,
  KeywordAliasEnum,
  KeywordAliasEnumMapDesc,
  TableWidth,
  VoltageClassesEnum,
  VoltageClassesEnumMapDesc
} from "@/enums";
import { useTableCellFormatter } from "@/utils/useTableCellFormatter";
import { IDevice, IMaterial } from "@/models";
import { formatEnum } from "@/utils/format";
import { IEmphasisRawmaterialList } from "@/models/stock-data-center/i-emphasis-raw-material";
import { KeywordAliasHeader } from "@/views/components/table-header-renderer";

export const useColumnHook = () => {
  const { dictionaryFormatter, dateFormatter, withUnitFormatter, mapFormatter } = useTableCellFormatter();

  const baseColumns: TableColumnList = [
    {
      label: "导入时间",
      prop: "createTime",
      fixed: "right",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    },
    {
      label: "更新时间",
      prop: "updateTime",
      fixed: "right",
      width: TableWidth.dateTime,
      formatter: dateFormatter(fullDateFormat)
    }
  ];

  const dataColumns = (dataType: ImportDataTypeEnum): TableColumnList => {
    switch (dataType) {
      case ImportDataTypeEnum.emphasisRawMaterial:
        return [
          {
            label: "物资品类",
            prop: "categoryName",
            width: TableWidth.type
          },
          {
            label: "物资种类",
            prop: "categorySubClassName",
            width: TableWidth.type
          },
          {
            label: "原材料名称",
            prop: "materialName",
            width: TableWidth.name
          },
          {
            label: "原材料类型",
            prop: "processName",
            width: TableWidth.type
          },
          {
            label: "库存数量",
            prop: "inventory",
            align: "right",
            width: TableWidth.number,
            formatter: (row: IEmphasisRawmaterialList) => {
              return `${row.inventory} ${row.partUnit}`;
            }
          },
          {
            label: "规格型号",
            prop: "modelCode",
            width: TableWidth.type
          },
          {
            label: "电压等级",
            prop: "voltageGrade",
            width: TableWidth.number,
            formatter: mapFormatter(VoltageClassesEnumMapDesc)
          },
          {
            label: "原材料产地",
            prop: "oorMaterials",
            width: TableWidth.type
          },
          {
            label: "原材料供应商",
            prop: "supplierName",
            width: TableWidth.name
          },
          {
            label: "入库时间",
            prop: "storageTime",
            width: TableWidth.dateTime,
            formatter: dateFormatter(dateFormat)
          },
          {
            label: "存放地点所在市",
            prop: "locationCity",
            width: TableWidth.type
          },
          {
            label: "备注",
            prop: "remarks",
            width: TableWidth.largeName
          },
          ...baseColumns
        ];

      case ImportDataTypeEnum.finishedProduct:
        return [
          {
            label: "物资品类",
            prop: "categoryName",
            width: TableWidth.type
          },
          {
            label: "物资种类",
            prop: "subclassName",
            minWidth: TableWidth.largeType
          },
          {
            label: "库存数量",
            prop: "productAmount",
            width: TableWidth.number,
            formatter: withUnitFormatter("productUnit", "subclassCode", MEASURE_UNIT)
          },
          {
            label: "物料编码",
            prop: "eipMatCode",
            width: TableWidth.order
          },
          {
            label: "物料描述",
            prop: "eipMatDes",
            width: TableWidth.name
          },
          {
            label: "规格型号",
            prop: "speModel",
            width: TableWidth.type
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.number,
            formatter: mapFormatter(VoltageClassesEnumMapDesc)
          },
          {
            label: "入库时间",
            prop: "putStorageTime",
            width: TableWidth.dateTime,
            formatter: dateFormatter(dateFormat)
          },
          {
            label: "存放地点所在市",
            prop: "storeCity",
            width: TableWidth.type
          },
          {
            label: "采购订单号",
            prop: "poNo",
            width: TableWidth.order
          },
          {
            label: "采购订单行项目号",
            prop: "poItemNo",
            width: TableWidth.largeOrder
          },
          ...baseColumns
        ];

      case ImportDataTypeEnum.reservePartStock:
        return [
          {
            label: "物资品类",
            prop: "categoryName",
            width: TableWidth.type
          },
          {
            label: "物资种类",
            prop: "subclassName",
            width: TableWidth.name
          },
          {
            label: "备品备件名称",
            prop: "productName",
            minWidth: TableWidth.name
          },
          {
            label: "备品备件类型",
            prop: "spareProductType",
            width: TableWidth.type,
            formatter: dictionaryFormatter(SPARE_PART, "subclassCode")
          },
          {
            label: "库存数量",
            prop: "productAmount",
            align: "right",
            width: TableWidth.number,
            formatter: withUnitFormatter("productUnit", "subclassCode", MEASURE_UNIT)
          },
          {
            label: "规格型号",
            prop: "speModels",
            width: TableWidth.largeType
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.type,
            formatter: mapFormatter(VoltageClassesEnumMapDesc)
          },
          {
            label: "入库时间",
            prop: "putStorageTime",
            width: TableWidth.dateTime,
            formatter: dateFormatter(dateFormat)
          },
          {
            label: "存放地点所在市",
            prop: "storeCity",
            width: TableWidth.type
          },
          ...baseColumns
        ];

      case ImportDataTypeEnum.device:
        return [
          {
            label: "设备编号",
            prop: "deviceCode"
          },
          {
            label: "设备名称",
            prop: "deviceName"
          },
          {
            label: "功能类型",
            prop: "functionType",
            formatter: (row: IDevice) => formatEnum(row.functionType, FunctionTypeEnum, "FunctionTypeEnum")
          },
          {
            label: "设备产地",
            prop: "deviceUnit"
          },
          {
            label: "购入年月",
            prop: "purchaseTime",
            formatter: dateFormatter(dateFormat)
          },
          {
            label: "规格型号",
            prop: "specificationModel"
          },
          ...baseColumns
        ];

      case ImportDataTypeEnum.material:
        return [
          {
            label: "物资种类",
            prop: "subClassName",
            width: TableWidth.name
          },
          {
            label: "物料编号",
            prop: "materialCode",
            width: TableWidth.order
          },
          {
            label: "物料名称",
            prop: "materialName",
            minWidth: TableWidth.name
          },
          {
            label: "物料描述",
            prop: "materialDescribe",
            minWidth: TableWidth.largeName
          },
          {
            label: "物料单位",
            prop: "materialUnit",
            width: TableWidth.unit,
            formatter: (row: IMaterial) => row?.unitDictionary?.name
          },
          {
            label: "规格型号",
            prop: "specificationModel",
            width: TableWidth.type
          },
          {
            label: "电压等级",
            prop: "voltageClass",
            width: TableWidth.type,
            formatter: (row: IMaterial) => formatEnum(row.voltageClass, VoltageClassesEnum, "voltageClassesEnum")
          },
          ...baseColumns
        ];

      case ImportDataTypeEnum.salesOrder:
        return [
          {
            label: "销售订单号",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "采购订单号",
            prop: "customerOrderNo",
            width: TableWidth.order
          },
          {
            label: "物资品类",
            prop: "categoryName",
            width: TableWidth.type
          },
          {
            label: "订单进度",
            prop: "shipmentStatusStr",
            width: TableWidth.type
          },
          {
            label: "交货日期",
            prop: "deliveryDate",
            width: TableWidth.dateTime,
            formatter: dateFormatter(dateFormat)
          },
          {
            label: "采购方公司名称",
            prop: "customerName",
            width: TableWidth.name
          },
          {
            label: "标识",
            prop: "tag",
            width: TableWidth.type
          },
          {
            label: "备注",
            prop: "remark",
            width: TableWidth.largeName
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.salesOrderDetail:
        return [
          {
            label: "销售订单号",
            prop: "salesOrderNo",
            width: TableWidth.order
          },
          {
            label: "采购订单号",
            prop: "soItemNo",
            width: TableWidth.order
          },
          {
            label: "物资品类",
            prop: "categoryName",
            width: TableWidth.type
          },
          {
            label: "物资种类",
            prop: "lowestMaterialClassName",
            width: TableWidth.type
          },
          {
            label: "物料编号",
            prop: "productCode",
            width: TableWidth.type
          },
          {
            label: "物料名称",
            prop: "productName",
            width: TableWidth.type
          },
          {
            label: "物料描述",
            prop: "materialDesc",
            width: TableWidth.type
          },
          {
            label: "物料单位",
            prop: "productUnit",
            width: TableWidth.type
          },
          {
            label: "物料数量",
            prop: "productAmount",
            width: TableWidth.type
          },
          {
            label: "规格型号",
            prop: "specificationType",
            width: TableWidth.type
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.type
          },
          {
            label: "项目号",
            prop: "projectNo",
            width: TableWidth.type
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.productionPlan:
        return [
          {
            label: "排产计划编码",
            prop: "scheduleCode",
            width: TableWidth.order
          },
          {
            label: "销售订单号",
            prop: "salesOrderNo",
            width: TableWidth.order
          },
          {
            label: "销售订单行号",
            prop: "soItemNo",
            width: TableWidth.order
          },
          {
            label: "排产数量",
            prop: "scheduleAmount",
            width: TableWidth.order
          },
          {
            label: "排产进度",
            prop: "schedule",
            width: TableWidth.order
          },
          {
            label: "计划开始日期",
            prop: "planStartDateStr",
            width: TableWidth.order
          },
          {
            label: "计划结束日期",
            prop: "planFinishDateStr",
            width: TableWidth.order
          },
          {
            label: "计划工期(天)",
            prop: "planPeriod",
            width: TableWidth.order
          },
          {
            label: "最终交付日期",
            prop: "dueDateStr",
            width: TableWidth.order
          },
          {
            label: "实际开始日期",
            prop: "actualStartDateStr",
            width: TableWidth.order
          },
          {
            label: "实际结束日期",
            prop: "actualFinishDateStr",
            width: TableWidth.order
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.productionOrder:
        return [
          {
            label: "生产订单号",
            prop: "no",
            headerRenderer: () => (
              <KeywordAliasHeader
                code={KeywordAliasEnum.IPO_NO}
                defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
              />
            ),
            width: TableWidth.order
          },
          {
            label: "销售订单号",
            prop: "salesOrderNo",
            width: TableWidth.order
          },
          {
            label: "销售订单行号",
            prop: "salesOrderRowNo",
            width: TableWidth.order
          },
          {
            label: "生产状态",
            prop: "productionStatusName",
            width: TableWidth.order
          },
          {
            label: "规格型号",
            prop: "specificationType",
            width: TableWidth.order
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.order
          },
          {
            label: "计量单位",
            prop: "unit",
            width: TableWidth.order
          },
          {
            label: "生产数量",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "计量单位",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "计划日期",
            prop: "planTimeStr",
            width: TableWidth.order
          },
          {
            label: "实际开始日期",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "实际结束日期",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "备注",
            prop: "remark",
            width: TableWidth.order
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.workOrder:
        return [
          {
            label: "工单编号",
            prop: "woNo",
            width: TableWidth.order
          },
          {
            label: "生产订单号",
            prop: "ipoNo",
            headerRenderer: () => (
              <KeywordAliasHeader
                code={KeywordAliasEnum.IPO_NO}
                defaultText={KeywordAliasEnumMapDesc[KeywordAliasEnum.IPO_NO]}
              />
            ),
            width: TableWidth.order
          },

          {
            label: "产品编号",
            prop: "productCode",
            width: TableWidth.order
          },
          {
            label: "工艺路线",
            prop: "processRouteNo",
            width: TableWidth.order
          },
          {
            label: "工序",
            prop: "procedureNo",
            width: TableWidth.order
          },
          {
            label: "生产数量",
            prop: "amount",
            width: TableWidth.order
          },
          {
            label: "计量单位",
            prop: "unit",
            width: TableWidth.order
          },
          {
            label: "物料编号",
            prop: "materialsCode",
            width: TableWidth.order
          },
          {
            label: "物料名称",
            prop: "materialsName",
            width: TableWidth.order
          },
          {
            label: "物料描述",
            prop: "materialsDescription",
            width: TableWidth.order
          },
          {
            label: "物料单位",
            prop: "materialsUnit",
            width: TableWidth.order
          },
          {
            label: "规格型号",
            prop: "modelSpec",
            width: TableWidth.order
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.order
          },
          {
            label: "工单状态",
            prop: "woStatusName",
            width: TableWidth.order
          },
          {
            label: "计划日期",
            prop: "planDateStr",
            width: TableWidth.order
          },
          {
            label: "实际开始日期",
            prop: "actualStartDateStr",
            width: TableWidth.order
          },
          {
            label: "实际结束日期",
            prop: "actualFinishDateStr",
            width: TableWidth.order
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.rawMaterialInfo:
        return [
          {
            label: "原材料编号",
            prop: "code",
            minWidth: TableWidth.order
          },
          {
            label: "原材料名称",
            prop: "name",
            minWidth: TableWidth.order
          },
          {
            label: "原材料类型",
            prop: "processName",
            minWidth: TableWidth.order
          },
          {
            label: "原材料批次号",
            prop: "materialBatchNo",
            minWidth: TableWidth.order
          },
          {
            label: "规格型号",
            prop: "modelCode",
            minWidth: TableWidth.order
          },
          {
            label: "计量单位",
            prop: "partUnit",
            minWidth: TableWidth.order
          },
          {
            label: "电压等级",
            prop: "voltageGrade",
            minWidth: TableWidth.order
          },
          {
            label: "品牌",
            prop: "borMaterials",
            minWidth: TableWidth.order
          },
          {
            label: "产地",
            prop: "oorMaterials",
            minWidth: TableWidth.order
          },
          {
            label: "制造商",
            prop: "rawmManufacturer",
            minWidth: TableWidth.order
          },
          {
            label: "生产日期",
            prop: "productionDateStr",
            minWidth: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            minWidth: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            minWidth: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];

      case ImportDataTypeEnum.finishedProductInfo:
        return [
          {
            label: "成品编号",
            prop: "no",
            width: TableWidth.order
          },
          {
            label: "生产订单",
            prop: "productionOrderNo",
            width: TableWidth.order
          },
          {
            label: "生产工单",
            prop: "workOrderNo",
            width: TableWidth.order
          },
          {
            label: "生产批次号",
            prop: "productBatchNo",
            width: TableWidth.order
          },
          {
            label: "规格型号",
            prop: "specificationType",
            width: TableWidth.order
          },
          {
            label: "电压等级",
            prop: "voltageLevel",
            width: TableWidth.order
          },
          {
            label: "数量",
            prop: "number",
            width: TableWidth.order
          },
          {
            label: "导入时间",
            prop: "importTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          },
          {
            label: "更新时间",
            prop: "updateTime",
            fixed: "right",
            width: TableWidth.dateTime,
            formatter: dateFormatter(fullDateFormat)
          }
        ];
      default:
        return [];
    }
  };
  return { dataColumns };
};
