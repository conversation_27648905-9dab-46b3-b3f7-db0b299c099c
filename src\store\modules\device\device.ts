import { IOption } from "@/models";
import { defineStore } from "pinia";
import { ICreateDevice, IDevice, IDeviceReq } from "@/models/device";
import * as deviceService from "@/api/device";

export const useDeviceStore = defineStore({
  id: "cx-device-store",
  state: () => ({
    devices: [] as Array<IDevice>,
    options: [] as Array<IOption & { deviceCode?: string }>,
    deviceDetail: {} as IDevice,
    total: 0 as number,
    loading: false as boolean,
    /** 是否允许新增设备 */
    allowAdd: false
  }),
  actions: {
    /** 查询设备不分类 */
    async queryDevice(params?: IDeviceReq) {
      this.loading = true;
      const deviceRes = await deviceService.queryDevice(params);
      this.total = deviceRes.data.total;
      this.devices = deviceRes.data.list;
      this.loading = false;
    },

    async queryDeviceList(params?: IDeviceReq) {
      const deviceRes = await deviceService.queryDeviceList(params);
      if (!Array.isArray(deviceRes.data) || deviceRes.data.length === 0) {
        this.devices = [];
        this.options = [];
        return;
      }
      this.devices = deviceRes.data;
      this.options = deviceRes.data.map(item => ({
        label: item.deviceName,
        value: item.id,
        deviceCode: item.deviceCode
      }));
    },

    /** 一键同步 */
    async onKeySynchronization() {
      return deviceService.syncAllDevice();
    },

    /** 一键同步 */
    async syncDeviceById(id: string) {
      return deviceService.syncDeviceById(id);
    },

    async createDevice(data: ICreateDevice) {
      return deviceService.createDevice(data);
    },

    async editDevice(data: ICreateDevice) {
      return deviceService.editDevice(data);
    },

    async getDeviceDetailById(id: string) {
      const deviceRes = await deviceService.getDeviceDetailById(id);
      this.deviceDetail = deviceRes.data;
    },

    setDevice(data?: ICreateDevice) {
      this.deviceDetail = data;
    },

    async deleteDevice(id: string) {
      return deviceService.deleteDevice(id);
    },

    /** 查询设备是否可以新增 */
    async queryDeviceAllowAdd() {
      const { data } = await deviceService.checkDeviceAllowAdd();
      this.allowAdd = data;
    }
  }
});
