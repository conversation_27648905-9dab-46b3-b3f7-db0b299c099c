<template>
  <div class="w-full h-full overflow-hidden">
    <!-- 销售订单 -->
    <el-scrollbar v-if="syncList?.length || loading" class="w-full h-full">
      <div class="order-list flex flex-col gap-5 py-1">
        <loading-skeleton :loading="loading" :count="skeletonCount">
          <sync-list-item v-for="sync in syncList" :key="sync.id" :sync="sync" />
        </loading-skeleton>
      </div>
    </el-scrollbar>
    <template v-else>
      <EmptyDataDisplay />
    </template>
    <!-- 同步详情弹窗 -->
    <el-dialog
      :title="detailStore.title"
      v-model="dialogVisible"
      fullscreen
      destroy-on-close
      :close-on-press-escape="false"
      class="state-grid-order-sync-detail-dialog"
      :show-close="false"
    >
      <template #header="{ close, titleId, titleClass }">
        <div :id="titleId" class="flex justify-between">
          <div class="flex gap-3 items-center">
            <el-button link @click="close">
              <el-icon size="20"><Back /></el-icon>
            </el-button>
            <div :class="titleClass" v-if="detailStore.title">{{ detailStore.title }}</div>
          </div>
          <el-button type="danger" @click="close">
            <el-icon size="20"><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <sync-detail />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import LoadingSkeleton from "@/views/order/sales-order/detail/src/sg-order/list/loading-skeleton.vue";
import SyncListItem from "@/views/order/sales-order/detail/src/sg-order/list/item.vue";
import SyncDetail from "@/views/order/sales-order/detail/src/sg-order/detail/detail.vue";
import {
  useSalesOrderDetailStore,
  useSalesOrderSyncInfo,
  useSalesStateGridOrderSyncIotListStore
} from "@/store/modules";
import { computed, onMounted, ref } from "vue";
import { StateGridOrderSyncStep } from "@/enums/state-grid-order";
import { useThrottleFn } from "@vueuse/core";
import { REFRESH_SYNC_DATA_PERIOD } from "@/consts";
import { useSocket } from "@/utils/useSocket";
import { PurchaseChannel, RefreshSceneEnum, SocketEventEnum } from "@/enums";
import EmptyDataDisplay from "@/views/order/components/empty-data-display/index.vue";
import { isOrderLineDataType } from "../sync-step-tool";
import { Close, Back } from "@element-plus/icons-vue";

const store = useSalesOrderSyncInfo();
store.toggleActiveStep(StateGridOrderSyncStep.SALES_LINE);
const salesDetailStore = useSalesOrderDetailStore();
const detailStore = useSalesOrderSyncInfo();
const listStore = useSalesStateGridOrderSyncIotListStore();

const socket = useSocket();
const loading = ref<boolean>(false);
/** 骨架数量 */
const skeletonCount = computed(() => salesDetailStore.salesOrder?.saleLineCount);
const syncList = computed(() => listStore.iotSyncs);
const dialogVisible = computed({
  get() {
    return detailStore.dialogVisible;
  },
  set(value) {
    detailStore.$patch({ dialogVisible: value });
  }
});

const handleRefresh = useThrottleFn(requestSyncList, REFRESH_SYNC_DATA_PERIOD, true);

onMounted(() => {
  loading.value = true;
  listStore.setSalesOrderId(salesDetailStore.saleOrderId);
  listStore.setChannel(PurchaseChannel.CSG_GuangZhou);
  requestSyncList();
  addRefreshEventListener();
});

/**
 * @description: 获取同步列表数据
 */
async function requestSyncList() {
  await listStore.refreshSalesOrderSyncIotList().finally(() => {
    loading.value = false;
  });
}

/**
 * @description: 开启socket 监听刷新消息
 */
function addRefreshEventListener() {
  // 接受刷新消息，请求最新同步列表
  socket.on(SocketEventEnum.REFRESH, event => {
    const { type, purchaseOrderItemId, dataType } = event;
    // 如果当前不是刷新消息，或者消息不属于订单行数据，直接返回
    if (type !== RefreshSceneEnum.CSG_GUANGZHOU_DATA || !isOrderLineDataType(dataType)) {
      return;
    }
    // 当前同步的列表里面是否有当前消息的订单行
    const matching = listStore.iotSyncs.find(item => item.id === purchaseOrderItemId);
    if (!matching) {
      return;
    }
    handleRefresh();
  });
}
</script>
