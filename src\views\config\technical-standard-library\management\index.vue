<template>
  <div class="bg-bg_color pt-[8px] px-6 flex justify-between">
    <ElForm :inline="true" :model="state.params" size="large" class="ml-6 flex-1">
      <ElFormItem label="物资品类：">
        <CategorySelect v-model="state.params.categoryCode" placeholder="请选择物资品类" clearable />
      </ElFormItem>
      <ElFormItem label="物资种类：">
        <SubclassSelect
          v-model="state.params.subClassCode"
          :category-code="state.params.categoryCode"
          placeholder="请选择物资种类"
          clearable
        />
      </ElFormItem>
      <ElFormItem label="技术标准：">
        <ElInput
          class="!w-[224px]"
          clearable
          v-model="state.params.standardName"
          placeholder="请选择技术标准名称"
          @clear="onConfirmQuery"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton size="large" type="primary" @click="onConfirmQuery()">搜索</ElButton>
        <ElButton size="large" @click="onResetQuery()">重置</ElButton>
      </ElFormItem>
    </ElForm>
    <ElButton
      v-if="state.licenseAuthIncludeIOT"
      v-auth="PermissionKey.meta.metaTechnicalStandardSyncIot"
      size="large"
      type="primary"
      :disabled="isSync"
      @click="onSync()"
      :loading="syncLoading"
    >
      <template #icon>
        <FontIcon icon="icon-sync" />
      </template>
      同步到IOT
    </ElButton>
    <ElButton
      v-auth="PermissionKey.meta.metaTechnicalStandardCreate"
      size="large"
      type="primary"
      :icon="Plus"
      @click="onAddTechnicalStandard()"
      >创建标准
    </ElButton>
  </div>
  <div class="bg-bg_color p-5 my-5 mx-6 flex flex-col flex-1 overflow-hidden">
    <PureTable
      class="flex-1 overflow-hidden pagination tooltip-max-w"
      row-key="id"
      size="large"
      :data="state.technicalStandardLibraryTableData"
      :columns="state.columns"
      :pagination="pagination"
      showOverflowTooltip
      :loading="loading"
      @page-size-change="onPageSizeChange()"
      @page-current-change="onCurrentPageChange()"
    >
      <template #categoryCode="data">
        <span> {{ getCategoryNameByCode(data.row.categoryCode) }}</span>
      </template>

      <template #subClassCode="data">
        <span> {{ getSubClassNameByCode(data.row.subClassCode) }}</span>
      </template>
      <template #operation="data">
        <div>
          <ElButton type="primary" link @click="onQuery(data.row.id)"> 详情 </ElButton>
          <ElButton
            v-auth="PermissionKey.meta.metaTechnicalStandardEdit"
            type="primary"
            link
            @click="onEdit(data.row.id)"
          >
            编辑
          </ElButton>
        </div>
      </template>
      <template #empty>
        <el-empty :image-size="120">
          <template #image>
            <EmptyData />
          </template>
        </el-empty>
      </template>
    </PureTable>

    <el-dialog
      :title="getLibraryFormModalTitle()"
      align-center
      class="default"
      destroy-on-close
      v-model="state.technicalStandardLibraryFormModalVis"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="onCancelLibraryFormModal"
    >
      <LibraryForm ref="libraryFormRef" />
      <template #footer>
        <el-button @click="onCancelLibraryFormModal()">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="onSaveLibraryDetailFormModal()">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/utils/useTableConfig";
import { Plus } from "@element-plus/icons-vue";
import { useColumns } from "./columns";
import { reactive, ref, watch, onMounted, computed, watchEffect } from "vue";
import {
  ITechnicalStandardLibrary,
  ITechnicalStandardLibraryDetail,
  ITechnicalStandardLibraryReqParams
} from "@/models";
import { ElMessage, ElForm, ElFormItem, ElInput, ElButton } from "element-plus";
import { useLoadingFn } from "@/utils/useLoadingFn";
import EmptyData from "@/assets/svg/empty_data.svg?component";
import { usePageStoreHook } from "@/store/modules/page";
import { useCategoryStore, useSystemAuthStore, useTechnicalStandardLibraryStore } from "@/store/modules";
import { useTechnicalStandardLibraryHook } from "../hooks/technical-standard-library-hook";
import LibraryForm from "./library-form/index.vue";
import SubclassSelect from "@/views/components/subclass-select/subclass-select.vue";
import CategorySelect from "@/views/components/category-select/category-select.vue";
import { useRouter } from "vue-router";
import { pickBy } from "lodash-unified";
import { PermissionKey } from "@/consts";

usePageStoreHook().setTitle("技术标准库");

const { columns } = useColumns();
const { pagination } = useTableConfig();
const technicalStandardLibraryStore = useTechnicalStandardLibraryStore();
const categoryStore = useCategoryStore();
const systemAuthStore = useSystemAuthStore();
const saveLoading = ref<boolean>(false);
const loading = ref<boolean>(false);
const syncLoading = ref<boolean>(false);

const libraryFormRef = ref<InstanceType<typeof LibraryForm>>();
const isSync = computed(() => !state.technicalStandardLibraryTableData.length);
const router = useRouter();

const {
  queryTechnicalStandardLibrary,
  getLibraryDetailById,
  createTechnicalStandardLibrary,
  editTechnicalStandardLibrary,
  setTechnicalStandardLibraryStorage,
  clearTechnicalStandardLibraryStorage,
  standardLibrarySync
} = useTechnicalStandardLibraryHook();

const state = reactive<{
  params: ITechnicalStandardLibraryReqParams;
  technicalStandardLibraryTableData: Array<ITechnicalStandardLibrary>;
  technicalStandardLibraryFormModalVis: boolean;
  selectId: string;
  columns: TableColumnList;
  licenseAuthIncludeIOT: boolean;
}>({
  params: {} as ITechnicalStandardLibraryReqParams,
  technicalStandardLibraryTableData: [],
  technicalStandardLibraryFormModalVis: false,
  selectId: "",
  columns: [],
  licenseAuthIncludeIOT: false
});

watch(
  () => technicalStandardLibraryStore.total,
  () => {
    pagination.total = technicalStandardLibraryStore.total;
  },
  {
    immediate: true
  }
);

watchEffect(async () => {
  if (!(await systemAuthStore.checkLicenseAuthIncludeIOT)) {
    state.licenseAuthIncludeIOT = false;
    state.columns = columns.filter(col => !["syncResult", "lastSyncTime", "syncNote"].includes(col.prop as string));
  } else {
    state.licenseAuthIncludeIOT = true;
    state.columns = columns;
  }
});

onMounted(async () => {
  await categoryStore.getAllSubclasses();
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
});

const onPageSizeChange = async () => {
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
};
const onCurrentPageChange = async () => {
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
};

const getLibraryFormModalTitle = () => (!state.selectId ? "新增技术标准" : "编辑技术标准");

async function onAddTechnicalStandard() {
  state.selectId = "";
  state.technicalStandardLibraryFormModalVis = true;
}
const onQuery = async (id: string) => {
  router.push(`/technical-standard-library/${id}`);
};

const onSave = async () => {
  const formValue: ITechnicalStandardLibraryDetail | false = await libraryFormRef.value
    .getValidValue()
    .catch(() => false);

  if (!formValue) {
    return;
  }

  if (!formValue.id) {
    await createTechnicalStandardLibrary(formValue);
  } else {
    await editTechnicalStandardLibrary(formValue);
  }
  clearForm();
  ElMessage.success(formValue.id ? "编辑成功" : "新增成功");
  pagination.currentPage = 1;
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
};

const onEdit = async (id: string) => {
  state.technicalStandardLibraryFormModalVis = true;
  state.selectId = id;
  const technicalStandardLibraryDetail: ITechnicalStandardLibraryDetail = await getLibraryDetailById(id);
  if (!technicalStandardLibraryDetail) {
    return;
  }
  setTechnicalStandardLibraryStorage(technicalStandardLibraryDetail);
};

const onCancelLibraryFormModal = () => {
  clearForm();
};
watch(
  () => state.params.categoryCode,
  () => {
    state.params.subClassCode = null;
  }
);

function queryParams() {
  state.params.pageSize = pagination.pageSize;
  state.params.pageNo = pagination.currentPage;
  const params: ITechnicalStandardLibraryReqParams = pickBy(state.params, value => !!value);
  return params;
}

const getCategoryNameByCode = (categoryCode: string) => {
  if (!categoryCode) {
    return "--";
  }
  const categories = categoryStore.categories.find(categories => categories.categoryCode == categoryCode);
  return categories ? categories.categoryName : "--";
};

const getSubClassNameByCode = (subClassCode: string) => {
  if (!subClassCode) {
    return "--";
  }
  const categories = categoryStore.subclasses.find(categories => categories.categoryCode == subClassCode);
  return categories ? categories.categoryName : "--";
};

const onConfirmQuery = async () => {
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
};

const onResetQuery = async () => {
  state.params = {};
  pagination.currentPage = 1;
  state.technicalStandardLibraryTableData = await getTechnicalStandardLibrary(queryParams());
};
const technicalStandardLibrarySync = async () => {
  await standardLibrarySync();
  await onResetQuery();
  ElMessage.success("一键同步成功");
};

const getTechnicalStandardLibrary = useLoadingFn(queryTechnicalStandardLibrary, loading);
const onSaveLibraryDetailFormModal = useLoadingFn(onSave, saveLoading);
const onSync = useLoadingFn(technicalStandardLibrarySync, saveLoading);

function clearForm() {
  clearTechnicalStandardLibraryStorage();
  state.technicalStandardLibraryFormModalVis = false;
}
</script>

<style scoped lang="scss">
.el-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
