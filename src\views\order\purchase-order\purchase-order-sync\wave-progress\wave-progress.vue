<template>
  <div class="relative wave-progress" :class="status">
    <canvas id="canvas" :width="width" :height="height" />
    <div class="flex-c w-full h-full font-semibold absolute z-10 text top-0 left-0">
      <span class="text-4xl font-barlow" :class="{ '!text-white': percentage > 80 }">
        {{ percentage || 0 }}
        <span class="text-base translate-y-1/4" :class="{ '!text-white': percentage > 80 }">%</span>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, watchEffect } from "vue";
import { WaveGroup } from "@/views/order/purchase-order/purchase-order-sync/wave-progress/wave-group";
import { hexToRgb } from "@pureadmin/utils";

const props = withDefaults(
  defineProps<{
    percentage: number;
    status?: "success" | "exception" | "";
  }>(),
  {
    percentage: 0
  }
);

let ctx: CanvasRenderingContext2D;
const width = 100;
const height = 100;
const drawStep = 0.3;
let offsetY: number = height;
let primaryColor: string;
let dangerColor: string;
const waveGroup = new WaveGroup();
let requestAnimationFrameHandle: number;

const total = computed(() => (height * (100 - props.percentage)) / 100);

onMounted(async () => {
  const canvas = document.getElementById("canvas") as HTMLCanvasElement;
  const style: CSSStyleDeclaration = getComputedStyle(canvas);
  primaryColor = hexToRgb(style.getPropertyValue("--el-color-primary")).toString();
  dangerColor = hexToRgb(style.getPropertyValue("--el-color-danger")).toString();
  ctx = canvas.getContext("2d");
  waveGroup.resize(width, height);

  watchEffect(() => draw());
  listenStatusChange();
});

onUnmounted(() => {
  cancelAnimationFrame(requestAnimationFrameHandle);
});

function draw() {
  cancelAnimationFrame(requestAnimationFrameHandle);
  ctx.clearRect(0, 0, width, height);
  if (total.value === height) {
    return;
  }
  offsetY = Math.max(offsetY - drawStep, total.value);
  waveGroup.setOffsetY(offsetY);
  waveGroup.draw(ctx, offsetY === 0);
  if (offsetY === total.value && offsetY === 0) {
    return;
  }
  requestAnimationFrameHandle = requestAnimationFrame(draw);
}

function listenStatusChange() {
  watchEffect(() => {
    const baseColor =
      {
        success: primaryColor,
        exception: dangerColor
      }[props.status] || primaryColor;
    const color = `rgba(${baseColor}, .4)`;
    waveGroup.setColor(color);
    draw();
  });
}
</script>

<style scoped>
#canvas {
  border-radius: 50%;
  border: 3px solid var(--el-color-info-light-8);
}

.wave-progress {
  --color: var(--el-color-primary);
}

.exception {
  --color: var(--el-color-danger);
}

.text {
  color: var(--color);
}
</style>
