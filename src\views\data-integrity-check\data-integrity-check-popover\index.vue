<template>
  <!-- 显示弹出层 -->
  <el-popover placement="bottom" :show-after="200" popper-class="el-popover-self" v-if="props.popoverData.dropdown">
    <template #reference>
      <span class="text-orange-400 font-semibold">{{ props.popoverData?.value }}</span>
    </template>
    <el-scrollbar max-height="400">
      <DataIntegrityCheckPopoverContent
        :processPointList="props.popoverData?.processPointList || []"
        :timePointList="props.popoverData?.timePointList || []"
        :experPointList="props.popoverData?.experPointList || []"
        :requiredPointList="props.popoverData?.requiredPointList || []"
        :productionId="props.productionId"
        :purchaseOrderId="props.purchaseOrderId"
        :saleOrderId="props.saleOrderId"
      />
    </el-scrollbar>
  </el-popover>
  <!-- 已上传 -->
  <span v-if="!props.popoverData.dropdown && props.popoverData?.lackStatus === IntegrityCheckProcessEnum.Uploaded">
    {{ props.popoverData?.value || "--" }}
  </span>
  <!-- 未上传 -->
  <span
    class="text-orange-400 font-semibold"
    v-if="!props.popoverData.dropdown && props.popoverData?.lackStatus === IntegrityCheckProcessEnum.UnUploaded"
  >
    {{ props.popoverData?.value }}
  </span>
</template>

<script setup lang="ts">
import { ICheckItem } from "@/models";
import DataIntegrityCheckPopoverContent from "../data-integrity-check-popover-content/index.vue";
import { IntegrityCheckProcessEnum } from "@/enums";
const props = defineProps<{
  popoverData: ICheckItem;
  /* 生产订单id **/
  productionId: string;
  /* 采购订单id **/
  purchaseOrderId: string;
  /* 销售订单Id **/
  saleOrderId: string;
}>();
</script>

<style lang="scss">
.el-popover-self {
  min-width: 180px !important;
  line-height: 2 !important;
}
</style>
