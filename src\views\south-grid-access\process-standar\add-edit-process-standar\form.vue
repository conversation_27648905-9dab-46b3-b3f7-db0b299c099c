<template>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="规格型号" prop="model">
          <el-input v-model="form.model" clearable placeholder="请输入规格型号" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="设备规格型号名称" prop="modelName">
          <el-input v-model="form.modelName" clearable placeholder="请输入设备规格型号名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="物资分类" prop="categoryCode">
          <el-radio-group v-model="form.categoryCode">
            <el-radio v-for="item in CategoryCodeEnumOptions" :label="`${item.value}`" :key="item.value" border>{{
              item.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance, FormRules } from "element-plus";
import { IProcessStandarModelForm } from "@/models/south-grid-access";
import { CategoryCodeEnumOptions } from "@/enums/south-grid-access";

defineExpose({
  validateForm,
  initFormValue,
  getFormValue
});

const form = reactive<IProcessStandarModelForm>({});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  model: [{ required: true, trigger: "change", message: "规格型号不能为空" }],
  categoryCode: [{ required: true, trigger: "change", message: "物资分类不能为空" }]
};

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate();
}

/**
 * @description: 初始化表单
 */
function initFormValue(v: IProcessStandarModelForm) {
  Object.assign(form, v);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, form);
}
</script>

<style scoped></style>
