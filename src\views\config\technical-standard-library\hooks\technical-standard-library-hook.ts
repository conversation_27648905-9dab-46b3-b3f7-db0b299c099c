import {
  ICollectionItem,
  ICollectionParams,
  IProcessData,
  ITechnicalStandardLibrary,
  ITechnicalStandardLibraryDetail,
  ITechnicalStandardLibraryReqParams
} from "@/models";
import { useTechnicalStandardLibraryStore } from "@/store/modules";
import { pickBy, isBoolean } from "lodash-unified";

/**
 * 技术标准库的hook
 */

export const useTechnicalStandardLibraryHook = () => {
  const technicalStandardLibraryStore = useTechnicalStandardLibraryStore();

  // 获取技术标准库的列表数据
  async function queryTechnicalStandardLibrary(params: ITechnicalStandardLibraryReqParams) {
    const tableData: Array<ITechnicalStandardLibrary> =
      await technicalStandardLibraryStore.queryTechnicalStandardLibrary(params);
    return tableData;
  }

  // 新增技术标准库
  async function createTechnicalStandardLibrary(params: ITechnicalStandardLibraryDetail) {
    await technicalStandardLibraryStore.createTechnicalStandardLibrary(params);
  }

  // 编辑技术标准库
  async function editTechnicalStandardLibrary(params: ITechnicalStandardLibraryDetail) {
    await technicalStandardLibraryStore.editTechnicalStandardLibrary(params);
  }

  // 根据Id获取技术标准库详情
  async function getLibraryDetailById(id: string) {
    const libraryDetail: ITechnicalStandardLibraryDetail =
      await technicalStandardLibraryStore.getTechnicalStandardLibraryById(id);
    return libraryDetail;
  }

  // 给选中的技术标准库明细详情赋值
  function setTechnicalStandardLibraryStorage(technicalStandardLibrary: ITechnicalStandardLibraryDetail) {
    technicalStandardLibraryStore.setTechnicalStandardLibraryStorage(technicalStandardLibrary);
  }

  // 清空选中的技术标准库明细详情
  function clearTechnicalStandardLibraryStorage() {
    technicalStandardLibraryStore.clearTechnicalStandardLibraryStorage();
  }

  // 根据物资品类获取生产工序
  async function getProductProcessByStandardId(subClassCode: string) {
    const productProcesData: Array<IProcessData> = await technicalStandardLibraryStore.getProductProcessByStandardId(
      subClassCode
    );
    return productProcesData;
  }

  /** 根据工序，采集点 查询技术标准库参数列表 */
  async function queryStandardLibraryCollectionPoint(params: ICollectionParams) {
    return await technicalStandardLibraryStore.queryStandardLibraryCollectionPoint(
      pickBy(params, item => item || isBoolean(item))
    );
  }

  //根据物资品类获取技术标准详情列表
  function getCollectionItems() {
    return technicalStandardLibraryStore.getCollectionItems();
  }

  // 对物资品类获取技术标准详情列表赋值
  async function batchUpdateCollectionItems(collectionItems: Array<ICollectionItem>) {
    return technicalStandardLibraryStore.batchUpdateCollectionItems(collectionItems);
  }

  // 对技术标准 进行同步
  async function standardLibrarySync() {
    return technicalStandardLibraryStore.standardLibrarySync();
  }
  return {
    queryTechnicalStandardLibrary,
    getLibraryDetailById,
    createTechnicalStandardLibrary,
    editTechnicalStandardLibrary,
    setTechnicalStandardLibraryStorage,
    clearTechnicalStandardLibraryStorage,
    getProductProcessByStandardId,
    queryStandardLibraryCollectionPoint,
    getCollectionItems,
    batchUpdateCollectionItems,
    standardLibrarySync
  };
};
