import {
  deleteExperimentById,
  deleteReport,
  getExperimentCheckData,
  getExperimentListStatus,
  getJfnyExperimentList,
  getLinkProductionOrder,
  getSingleJfnyInfo,
  saveSelectLinkProductionOrder,
  editJfnyExperimentById
} from "@/api/leave-factory/jfny-experiment";
import { upLoadReportOfJfny } from "@/api/production-test-sensing/out-going-factory";
import { fullDateFormat } from "@/consts";
import { ToggleStyleEnum } from "@/enums";
import { IExperimentUploadFile, IResponse } from "@/models";
import {
  IEditJfnyExperimentForm,
  IJfnyExperimentList,
  ILinkStatusCount,
  IProductionOrderList,
  ISearchJfnyListReq,
  ISearchProductionOrderReq
} from "@/models/leave-factory";
import { IUpLoadFile } from "@/models/production-test-sensing/i-common";
import { formatDate } from "@/utils/format";
import { cloneDeep } from "@pureadmin/utils";
import { defineStore } from "pinia";
import { orderBy } from "lodash-unified";

/** 出厂试验--局放耐压 */
export const useJfnyExperimentStore = defineStore({
  id: "jfny-experiment-store",
  state: () => ({
    checkDate: [] as any,
    linkStatus: {} as ILinkStatusCount,
    totalPage: 1 as number,
    defaultPageInfo: { pageNo: 1, pageSize: 20 },
    queryParams: {} as ISearchJfnyListReq,
    jfnyExperimentCards: [] as Array<IJfnyExperimentList>,
    jfnyExperimentList: [] as Array<IJfnyExperimentList>,
    tableTotal: 0 as number,
    productionOrderList: [] as Array<IProductionOrderList>,
    productionOrderTotal: 0 as number,
    selectProductionOrder: [] as Array<IProductionOrderList>,
    queryProductionParams: {} as ISearchProductionOrderReq
  }),
  actions: {
    /**
     * 获取日历的关联数据状态
     */
    async getExperimentCheckData(params: { startDate: string; endDate: string }): Promise<IResponse<Array<string>>> {
      return await getExperimentCheckData(params);
    },

    /**
     * 获取关联状态
     */
    async getExperimentLinkStatus() {
      const query = cloneDeep(this.queryParams);
      delete query["pageSize"];
      delete query["pageNo"];
      const res = await getExperimentListStatus(query);
      if (res.data) {
        this.linkStatus = res.data;
      }
    },

    /**
     * 查询局放耐压试验列表
     */
    queryJfnyExperimentList(params: ISearchJfnyListReq) {
      this.queryParams = { ...this.defaultPageInfo, ...this.queryParams, ...params };
      this.getJfnyExperimentListAction();
    },

    /**
     * 获取局放耐压试验列表
     */
    async getJfnyExperimentListAction(panelType: string) {
      this.queryParams = { ...this.defaultPageInfo, ...this.queryParams };
      const res = await getJfnyExperimentList({ ...this.queryParams });
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        if (panelType === ToggleStyleEnum.MENU) {
          this.jfnyExperimentCards = [...this.jfnyExperimentCards, ...this.formateData(res.data.list)];
          this.tableTotal = res.data.total;
        } else {
          this.jfnyExperimentList = this.formateData(res.data?.list);
          this.tableTotal = res.data.total;
        }
        this.totalPage = Math.ceil(res.data.total / (this.queryParams.pageSize || this.defaultPageInfo.pageSize));
      } else {
        this.jfnyExperimentCards = [];
        this.jfnyExperimentList = [];
        this.tableTotal = 0;
      }
    },

    /**
     * 获取单个试验信息
     */
    async getSingleJfnyInfo(id: string): Promise<IJfnyExperimentList> {
      return (await getSingleJfnyInfo(id)).data || null;
    },

    /**
     * 删除试验
     */
    async deleteExperimentById(id: string): Promise<IResponse<boolean>> {
      return await deleteExperimentById(id);
    },

    /**
     * 删除报告
     */
    async deleteExperimentReport(id: string): Promise<IResponse<boolean>> {
      return await deleteReport(id);
    },

    /**
     * 上传报告
     */
    async uploadReportOfExperiment(params: IExperimentUploadFile): Promise<IResponse<IUpLoadFile>> {
      return await upLoadReportOfJfny(params);
    },

    /**
     * 查询生产订单
     */
    queryProductionOrderList(keyword: string) {
      this.getProductionOrderList(keyword);
    },

    /**
     * 查询生产订单
     */
    editJfnyExperimentById(id: string, data: IEditJfnyExperimentForm) {
      return editJfnyExperimentById(id, data);
    },

    /**
     * 获取生产订单列表
     */
    async getProductionOrderList(pramsData: ISearchProductionOrderReq) {
      this.queryProductionParams = { ...this.queryProductionParams, ...pramsData };
      const res = await getLinkProductionOrder(this.queryProductionParams);
      if (Array.isArray(res.data?.list) && res.data?.list?.length) {
        this.productionOrderList = this.mergeSoNoSoItemNoSpan(res.data?.list);
        this.productionOrderTotal = res.data.total;
      } else {
        this.productionOrderList = [];
        this.productionOrderTotal = 0;
      }
    },

    /** 保存关联的生产订单 */
    async saveLinkProductionOrder(params: {
      experimentId: string;
      productionIds: string[];
    }): Promise<IResponse<boolean>> {
      return await saveSelectLinkProductionOrder(params);
    },

    /** 重置查询试验的参数  */
    initExperimentQuery() {
      this.queryParams = {};
    },

    /** 重置试验数据 */
    initExperimentData() {
      this.jfnyExperimentCards = [];
    },

    /** 重置查询生产订单的参数 */
    initQueryProductionParams() {
      this.queryProductionParams = {};
    },

    /** 格式化数据 */
    formateData(list: IJfnyExperimentList[]) {
      return list.map((item: IJfnyExperimentList) => {
        item.startTime = formatDate(item.startTime, fullDateFormat);
        item.endTime = formatDate(item.endTime, fullDateFormat);
        return item;
      });
    },

    /** 合并生产订单数据 */
    mergeSoNoSoItemNoSpan(productionOrder: IProductionOrderList[]) {
      if (!Array.isArray(productionOrder)) {
        return [];
      }
      // 根据soNo排序
      const sortOrder = orderBy(productionOrder, ["soNo"], ["asc"]);
      const rowSpanMap: Record<string, number> = {};
      sortOrder.forEach(line => (rowSpanMap[line.soNo] = (rowSpanMap[line.soNo] || 0) + 1));
      return sortOrder.map(line => {
        if (!line.soNo) {
          return { ...line, rowSpan: 1, colSpan: 1 };
        }
        const rowSpan: number = rowSpanMap[line.soNo];
        rowSpanMap[line.soNo] = 0;
        return { ...line, rowSpan, colSpan: 1 };
      });
    }
  }
});
