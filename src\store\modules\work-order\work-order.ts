import { ICreateWorkOrder, IResponse, IWorkOrder, IWorkOrderReq } from "@/models";
import { defineStore } from "pinia";
import * as workOrderService from "@/api/work-order";
import { isAllEmpty } from "@pureadmin/utils";
import { omitBy } from "lodash-unified";

export const useWorkOrderStore = defineStore({
  id: "cx-work-order-store",
  state: () => ({
    workOrders: [] as Array<IWorkOrder>,
    workOrderFormModalVis: false as boolean,
    isCreateWorkOrderForm: false as boolean,
    createWorkOrderDetail: {} as ICreateWorkOrder,
    workOrderDetail: {} as IWorkOrder,
    workOrderDetailModalVis: false as boolean,
    workOrderParams: undefined as IWorkOrderReq,
    loading: false,
    total: 0,
    refreshWorkOrdersTag: false
  }),
  actions: {
    setWorkOrderParams(params: IWorkOrderReq) {
      this.workOrderParams = params;
    },
    async refreshWorkOrders(pageNo = 1, refreshWorkOrders = false) {
      this.loading = true;
      this.workOrderParams.pageNo = pageNo;
      this.refreshWorkOrdersTag = refreshWorkOrders;
      this.queryWorkOrderPaging();
    },

    async queryWorkOrderPaging() {
      this.loading = true;
      const queryParams = omitBy(this.workOrderParams, val => isAllEmpty(val));
      const res = await workOrderService.queryWorkOrderPaging(queryParams);
      this.workOrders = res.data.list;
      this.total = res.data.total;
    },

    async createWorkOrder(data: ICreateWorkOrder) {
      return workOrderService.createWorkOrder(data);
    },

    async editWorkOrder(data: ICreateWorkOrder) {
      return workOrderService.editWorkOrder(data);
    },

    async deleteWorkOrder(id: string) {
      return workOrderService.deleteWorkOrder(id);
    },

    async getWorkOrderDetailById(id: string) {
      const workOrderRes: IResponse<IWorkOrder> = await workOrderService.getWorkOrderById(id);
      this.workOrderDetail = workOrderRes.data;
    },

    /**
     * @description: 查询线缆工单以及报工是否缺失
     */
    async queryCableReportWorkMissingStatus(productionId: string) {
      const { data } = await workOrderService.queryProductionOrderReportWorkIntegrity(productionId);
      return !data;
    },

    setCreateWorkOrderDetail(createWorkOrderDetail: Partial<ICreateWorkOrder> & { subclassCode: string }) {
      this.createWorkOrderDetail = createWorkOrderDetail;
    },

    setWorkOrderFormModalVis(visible: boolean, isCreate = false) {
      this.workOrderFormModalVis = visible;
      this.isCreateWorkOrderForm = isCreate;
    },
    setWorkOrderDetailModalVis(visible: boolean) {
      this.workOrderDetailModalVis = visible;
    },
    setWorkOrderDetail(workOrder: IWorkOrder) {
      this.workOrderDetail = workOrder;
    }
  }
});
