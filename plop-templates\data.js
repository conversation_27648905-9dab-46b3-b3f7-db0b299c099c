module.exports.data = {
  fields: {
    inspectBatchNo: {
      type: "string",
      desc: "检验批次号",
      column: true,
      form: true,
      search: false,
      formFieldConfig: {
        type: "string",
        label: "检验批次号",
        placeholder: "请输入检验批次号",
        rule: { required: true, trigger: "change", message: "检验批次号不能为空" }
      },
      searchFieldConfig: {
        label: "检验批次号",
        placeholder: "请输入检验批次号",
        type: "string"
      }
    },
    productionOrderNo: {
      type: "string",
      desc: "生产单据编码",
      column: true,
      form: true,
      search: false,
      formFieldConfig: {
        type: "string",
        label: "生产单据编码",
        placeholder: "请输入生产单据编码",
        rule: { required: true, trigger: "change", message: "生产单据编码不能为空" }
      },
      searchFieldConfig: {
        label: "生产单据编码",
        placeholder: "请输入生产单据编码",
        type: "string"
      }
    },

    inspectDate: {
      type: "string",
      desc: "检验日期",
      column: false,
      form: true,
      search: true,
      formFieldConfig: {
        type: "date",
        label: "检验日期",
        placeholder: "请选择检验日期",
        rule: { required: true, trigger: "change", message: "检验日期不能为空" }
      },
      searchFieldConfig: {
        label: "检验日期",
        placeholder: "请选择检验日期",
        type: "date"
      }
    },
    procedureCode: {
      type: "string",
      desc: "工序编码",
      column: true,
      form: true,
      search: false,
      formFieldConfig: {
        type: "string",
        label: "工序编码",
        placeholder: "请输入工序编码",
        rule: { required: true, trigger: "change", message: "工序编码不能为空" }
      },
      searchFieldConfig: {
        label: "工序编码",
        placeholder: "请输入工序编码",
        type: "string"
      }
    }
  },
  formIsDialog: true,
  // 表单域标签的位置， 当设置为 left 或 right
  labelPosition: "top",
  needSearch: true,
  needCreateOrEdit: true,
  needDelete: true,
  formLineNumber: 1,
  viewPathPrefix: "src/views/south-grid-access",
  modelPathPrefix: "src/models/south-grid-access",
  apiPathPrefix: "src/api/south-grid-access"
};
