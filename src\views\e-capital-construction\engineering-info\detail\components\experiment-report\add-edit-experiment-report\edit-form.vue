<template>
  <!-- 出厂实验报告 -->
  <el-form ref="formRef" :model="formData" label-position="top">
    <el-form-item
      v-if="isCombiner"
      label="实物ID（间隔）"
      prop="utcNum"
      :rules="{ required: true, message: '请输入实物ID（间隔）', trigger: 'change' }"
    >
      <el-input v-model="formData.utcNum" placeholder="请输入实物ID（间隔）" />
    </el-form-item>
    <TitleBar class="mb-2" title="试验报告" />
    <el-form-item label="" prop="fileId" :rules="{ required: true, message: '请选择文件', trigger: 'change' }">
      <!-- 附件上传 -->
      <UploadFileCom ref="uploadRefTest" v-model="formData.fileInfo" @change="uploadChange" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { FormInstance } from "element-plus";
import { IExperimentReport, FileInfoModel } from "@/models";
import { EquipmentTypeEnumExt } from "@/enums";
import TitleBar from "@/components/TitleBar/index";
import UploadFileCom from "../../upload-file/index.vue";
import { useRoute } from "vue-router";

const props = withDefaults(
  defineProps<{
    row: IExperimentReport;
  }>(),
  {
    row: () => {
      return {} as IExperimentReport;
    }
  }
);

const route = useRoute();
const formData = reactive({} as IExperimentReport);
/** 是否是组合电器 */
const isCombiner = route.query.type == EquipmentTypeEnumExt.Combiner.toString();
const fileList = ref([]);
const uploadRefTest = ref();

watch(
  () => props.row,
  value => {
    Object.assign(formData, value);
    fileList.value = formData.fileInfo ? [formData.fileInfo] : [];
  },
  { immediate: true }
);

const uploadChange = (info: FileInfoModel) => {
  formData.fileInfo = info;
  formData.fileId = info?.id || "";
  fileList.value = info?.id ? [info] : [];
};

const formRef = ref<FormInstance>();

/**
 * @description: 校验表单
 */
async function validateForm() {
  if (!formRef.value) {
    return false;
  }
  return await formRef.value.validate(valid => valid);
}

/**
 * @description: 获取表单值
 */
function getFormValue() {
  return Object.assign({}, formData as IExperimentReport);
}

/**
 * @description: 重置
 */
function resetFormValue() {
  uploadRefTest.value?.resetFormValue();
  formRef.value.resetFields();
}

defineExpose({
  validateForm,
  getFormValue,
  resetFormValue
});
</script>

<style scoped lang="scss">
.el-input-number {
  width: 100%;
}
</style>
