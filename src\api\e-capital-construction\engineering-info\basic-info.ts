/**
 * @description: 试验参数标准接口
 */

import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, BasicInfoModel } from "@/models";
/**
 * @description: 原材料基本信息列表
 */
export const BasicInfoApi = (id: String) => {
  return http.get<string, IResponse<Array<BasicInfoModel>>>(
    withApiGateway(`admin-api/ejj/project/raw-material/info/list/${id}`)
  );
};
/**
 * @description: 创建原材料基本信息
 */
export const BasicInfoCreateApi = (params: BasicInfoModel) => {
  return http.post<BasicInfoModel, IResponse<boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/info`), {
    data: params
  });
};

/**
 * @description: 编辑原材料基本信息
 */
export const BasicInfoEditApi = (params: BasicInfoModel) => {
  return http.put<BasicInfoModel, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/info`), {
    data: params
  });
};

/**
 * @description: 删除原材料基本信息
 */
export const BasicInfoDeleteApi = (id: string) => {
  return http.delete<string, IResponse<Boolean>>(withApiGateway(`admin-api/ejj/project/raw-material/info/${id}`));
};
