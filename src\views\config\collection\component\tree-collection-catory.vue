<template>
  <div class="category-tree p-4">
    <el-tree
      ref="treeInstance"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      highlight-current
      default-expand-all
      :indent="0"
      :current-node-key="currentNodeKey"
      @node-click="handleNodeClick"
      @current-change="currentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ICategoryInfoFindTenantList } from "@/models/category/i-category-info-list";
import { TreeOptionProps, TreeNodeData } from "element-plus/es/components/tree/src/tree.type";
import { computed, ref, watch } from "vue";

const props = withDefaults(
  defineProps<{
    collectionItems: ICategoryInfoFindTenantList[];
  }>(),
  {}
);

const emits = defineEmits<{
  (event: "handleNodeChange", nodeData: TreeNodeData): void;
}>();

const treeInstance = ref();
const treeData = computed(() => props.collectionItems);
const currentNodeKey = ref();

/** 点击节点 */
const handleNodeClick = (data?: ICategoryInfoFindTenantList) => {
  // 设置当前选中的高亮
  setTimeout(() => {
    currentNodeKey.value = data?.id;
    treeInstance.value.setCurrentKey(data?.id);
  });
};

/** 节点变化时触发  */
const currentChange = (data: ICategoryInfoFindTenantList, node: TreeNodeData) => {
  emits("handleNodeChange", node);
};

watch(
  treeData,
  newVal => {
    if (newVal?.length) {
      if (newVal[0]?.children?.length) {
        handleNodeClick(newVal[0]?.children[0]);
      } else {
        handleNodeClick(newVal[0]);
      }
    } else {
      handleNodeClick();
    }
  },
  {
    immediate: true
  }
);

const defaultProps: TreeOptionProps = {
  children: "children",
  label: data => {
    return data.categoryName;
  },
  isLeaf(data) {
    return !data.children?.length;
  },
  class: () => {
    return "custome-collection-tree";
  }
};
</script>

<style scoped lang="scss">
:deep(.el-tree--highlight-current .el-tree-node.is-current.custome-collection-tree > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary);
}
</style>
